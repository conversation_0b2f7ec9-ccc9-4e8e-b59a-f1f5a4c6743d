// This file is generated automatically by Next.js
// Do not edit this file manually
// This file validates that all pages and layouts export the correct types

import type { AppRoutes, LayoutRoutes, ParamMap, AppRouteHandlerRoutes } from "./routes.js"
import type { ResolvingMetadata, ResolvingViewport } from "next/types.js"
import type { NextRequest } from 'next/server.js'

type AppPageConfig<Route extends AppRoutes = AppRoutes> = {
  default: React.ComponentType<{ params: Promise<ParamMap[Route]> } & any> | ((props: { params: Promise<ParamMap[Route]> } & any) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type LayoutConfig<Route extends LayoutRoutes = LayoutRoutes> = {
  default: React.ComponentType<LayoutProps<Route>> | ((props: LayoutProps<Route>) => React.ReactNode | Promise<React.ReactNode> | never | void | Promise<void>)
  generateStaticParams?: (props: { params: ParamMap[Route] }) => Promise<any[]> | any[]
  generateMetadata?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingMetadata
  ) => Promise<any> | any
  generateViewport?: (
    props: { params: Promise<ParamMap[Route]> } & any,
    parent: ResolvingViewport
  ) => Promise<any> | any
  metadata?: any
  viewport?: any
}

type RouteHandlerConfig<Route extends AppRouteHandlerRoutes = AppRouteHandlerRoutes> = {
  GET?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  POST?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PUT?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  PATCH?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  DELETE?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  HEAD?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
  OPTIONS?: (request: NextRequest, context: { params: Promise<ParamMap[Route]> }) => Promise<Response | void> | Response | void
}


// Validate ../../src/app/[locale]/[...rest]/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/[locale]/[...rest]">> = Specific
  const handler = {} as typeof import("../../src/app/[locale]/[...rest]/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/[locale]/dashboard/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/[locale]/dashboard">> = Specific
  const handler = {} as typeof import("../../src/app/[locale]/dashboard/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/[locale]/dashboard/projects/[projectId]/edit/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/[locale]/dashboard/projects/[projectId]/edit">> = Specific
  const handler = {} as typeof import("../../src/app/[locale]/dashboard/projects/[projectId]/edit/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/[locale]/dashboard/projects/[projectId]/modules/[moduleId]/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/[locale]/dashboard/projects/[projectId]/modules/[moduleId]">> = Specific
  const handler = {} as typeof import("../../src/app/[locale]/dashboard/projects/[projectId]/modules/[moduleId]/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/[locale]/dashboard/projects/[projectId]/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/[locale]/dashboard/projects/[projectId]">> = Specific
  const handler = {} as typeof import("../../src/app/[locale]/dashboard/projects/[projectId]/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/[locale]/dashboard/projects/create/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/[locale]/dashboard/projects/create">> = Specific
  const handler = {} as typeof import("../../src/app/[locale]/dashboard/projects/create/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/[locale]/dashboard/projects/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/[locale]/dashboard/projects">> = Specific
  const handler = {} as typeof import("../../src/app/[locale]/dashboard/projects/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/[locale]/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/[locale]">> = Specific
  const handler = {} as typeof import("../../src/app/[locale]/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/[locale]/sign-up/page.tsx
{
  type __IsExpected<Specific extends AppPageConfig<"/[locale]/sign-up">> = Specific
  const handler = {} as typeof import("../../src/app/[locale]/sign-up/page.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/auth/[...nextauth]/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/auth/[...nextauth]">> = Specific
  const handler = {} as typeof import("../../src/app/api/auth/[...nextauth]/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/api/register/route.ts
{
  type __IsExpected<Specific extends RouteHandlerConfig<"/api/register">> = Specific
  const handler = {} as typeof import("../../src/app/api/register/route.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}





// Validate ../../src/app/[locale]/dashboard/layout.tsx
{
  type __IsExpected<Specific extends LayoutConfig<"/[locale]/dashboard">> = Specific
  const handler = {} as typeof import("../../src/app/[locale]/dashboard/layout.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}

// Validate ../../src/app/[locale]/layout.tsx
{
  type __IsExpected<Specific extends LayoutConfig<"/[locale]">> = Specific
  const handler = {} as typeof import("../../src/app/[locale]/layout.js")
  type __Check = __IsExpected<typeof handler>
  // @ts-ignore
  type __Unused = __Check
}
