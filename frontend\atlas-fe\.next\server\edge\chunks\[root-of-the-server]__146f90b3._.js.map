{"version": 3, "sources": [], "sections": [{"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/api/auth/doFinalSignOutHandshake.ts"], "sourcesContent": ["import axios, { type AxiosError } from 'axios'\r\nimport type { JWT } from 'next-auth/jwt'\r\n\r\n// this performs the final handshake for the keycloak\r\n// provider, the way it's written could also potentially\r\n// perform the action for other providers as well\r\nexport const doFinalSignOutHandshake = async (jwt: JWT) => {\r\n  const { idToken } = jwt\r\n\r\n  try {\r\n    // Add the id_token_hint to the query string\r\n    const params = new URLSearchParams()\r\n    params.append('id_token_hint', idToken)\r\n    const { status, statusText } = await axios.get(\r\n      `${process.env.AUTH_KEYCLOAK_ISSUER}/protocol/openid-connect/logout?${params.toString()}`,\r\n    )\r\n\r\n    // The response body should contain a confirmation that the user has been logged out\r\n    console.debug('Completed post-logout handshake', status, statusText)\r\n  } catch (e: any) {\r\n    console.error(\r\n      'Unable to perform post-logout handshake',\r\n      (e as AxiosError)?.code || e,\r\n    )\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;AAMO,MAAM,0BAA0B,OAAO;IAC5C,MAAM,EAAE,OAAO,EAAE,GAAG;IAEpB,IAAI;QACF,4CAA4C;QAC5C,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,CAAC,iBAAiB;QAC/B,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,wJAAK,CAAC,GAAG,CAC5C,GAAG,QAAQ,GAAG,CAAC,oBAAoB,CAAC,gCAAgC,EAAE,OAAO,QAAQ,IAAI;QAG3F,oFAAoF;QACpF,QAAQ,KAAK,CAAC,mCAAmC,QAAQ;IAC3D,EAAE,OAAO,GAAQ;QACf,QAAQ,KAAK,CACX,2CACA,AAAC,GAAkB,QAAQ;IAE/B;AACF"}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/lib/api/auth/refreshAccessToken.ts"], "sourcesContent": ["import type { JWT } from 'next-auth/jwt'\r\n\r\nexport const refreshAccessToken = async (token: JWT): Promise<JWT> => {\r\n  try {\r\n    if (\r\n      token.refreshTokenExpires &&\r\n      Date.now() >= token.refreshTokenExpires * 1000\r\n    ) {\r\n      return {\r\n        ...token,\r\n        error: 'RefreshTokenExpired',\r\n        errorDetails: 'The refresh token itself has expired.',\r\n      }\r\n    }\r\n\r\n    if (!token.refreshToken) {\r\n      throw new Error('MissingRefreshToken')\r\n    }\r\n\r\n    const clientId = process.env.AUTH_KEYCLOAK_ID\r\n    const clientSecret = process.env.AUTH_KEYCLOAK_SECRET\r\n    const tokenUrl = `${process.env.AUTH_KEYCLOAK_ISSUER}/protocol/openid-connect/token`\r\n\r\n    if (!(clientId && tokenUrl)) {\r\n      throw new Error('MissingKeycloakConfig')\r\n    }\r\n\r\n    const params = new URLSearchParams()\r\n    params.append('client_id', clientId)\r\n    if (clientSecret) {\r\n      params.append('client_secret', clientSecret)\r\n    }\r\n    params.append('grant_type', 'refresh_token')\r\n    params.append('refresh_token', token.refreshToken)\r\n\r\n    console.log('Try to refresh access token...')\r\n    const response = await fetch(tokenUrl, {\r\n      method: 'POST',\r\n      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },\r\n      body: params.toString(),\r\n    })\r\n\r\n    const refreshedTokens = await response.json()\r\n\r\n    if (!response.ok) {\r\n      console.error('Error during refresh token:', refreshedTokens)\r\n      if (refreshedTokens.error === 'invalid_grant') {\r\n        return {\r\n          ...token,\r\n          error: 'RefreshTokenInvalidated',\r\n          errorDetails:\r\n            refreshedTokens.error_description ||\r\n            'Refresh token validation failed on server.',\r\n        }\r\n      }\r\n      throw new Error(refreshedTokens.error || 'RefreshFailed')\r\n    }\r\n\r\n    const nowInSeconds = Math.floor(Date.now() / 1000)\r\n    const newAccessTokenExpiresAt = nowInSeconds + refreshedTokens.expires_in\r\n    let newRefreshTokenExpiresAt = token.refreshTokenExpires\r\n    if (typeof refreshedTokens.refreshTokenExpires === 'number') {\r\n      newRefreshTokenExpiresAt =\r\n        nowInSeconds + refreshedTokens.refreshTokenExpires\r\n    }\r\n\r\n    return {\r\n      ...token,\r\n      accessToken: refreshedTokens.access_token,\r\n      idToken: refreshedTokens.id_token,\r\n      accessTokenExpires: newAccessTokenExpiresAt,\r\n      refreshToken: refreshedTokens.refresh_token ?? token.refreshToken,\r\n      refreshTokenExpires: newRefreshTokenExpiresAt,\r\n      error: undefined,\r\n      errorDetails: undefined,\r\n    }\r\n  } catch (error: any) {\r\n    console.error('Exception during refresh token:', error)\r\n    return {\r\n      ...token,\r\n      error: 'RefreshAccessTokenError',\r\n      errorDetails: error.message || 'UnknownError',\r\n    }\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAEO,MAAM,qBAAqB,OAAO;IACvC,IAAI;QACF,IACE,MAAM,mBAAmB,IACzB,KAAK,GAAG,MAAM,MAAM,mBAAmB,GAAG,MAC1C;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,OAAO;gBACP,cAAc;YAChB;QACF;QAEA,IAAI,CAAC,MAAM,YAAY,EAAE;YACvB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,QAAQ,GAAG,CAAC,gBAAgB;QAC7C,MAAM,eAAe,QAAQ,GAAG,CAAC,oBAAoB;QACrD,MAAM,WAAW,GAAG,QAAQ,GAAG,CAAC,oBAAoB,CAAC,8BAA8B,CAAC;QAEpF,IAAI,CAAC,CAAC,YAAY,QAAQ,GAAG;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,CAAC,aAAa;QAC3B,IAAI,cAAc;YAChB,OAAO,MAAM,CAAC,iBAAiB;QACjC;QACA,OAAO,MAAM,CAAC,cAAc;QAC5B,OAAO,MAAM,CAAC,iBAAiB,MAAM,YAAY;QAEjD,QAAQ,GAAG,CAAC;QACZ,MAAM,WAAW,MAAM,MAAM,UAAU;YACrC,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAoC;YAC/D,MAAM,OAAO,QAAQ;QACvB;QAEA,MAAM,kBAAkB,MAAM,SAAS,IAAI;QAE3C,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,IAAI,gBAAgB,KAAK,KAAK,iBAAiB;gBAC7C,OAAO;oBACL,GAAG,KAAK;oBACR,OAAO;oBACP,cACE,gBAAgB,iBAAiB,IACjC;gBACJ;YACF;YACA,MAAM,IAAI,MAAM,gBAAgB,KAAK,IAAI;QAC3C;QAEA,MAAM,eAAe,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;QAC7C,MAAM,0BAA0B,eAAe,gBAAgB,UAAU;QACzE,IAAI,2BAA2B,MAAM,mBAAmB;QACxD,IAAI,OAAO,gBAAgB,mBAAmB,KAAK,UAAU;YAC3D,2BACE,eAAe,gBAAgB,mBAAmB;QACtD;QAEA,OAAO;YACL,GAAG,KAAK;YACR,aAAa,gBAAgB,YAAY;YACzC,SAAS,gBAAgB,QAAQ;YACjC,oBAAoB;YACpB,cAAc,gBAAgB,aAAa,IAAI,MAAM,YAAY;YACjE,qBAAqB;YACrB,OAAO;YACP,cAAc;QAChB;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;YACL,GAAG,KAAK;YACR,OAAO;YACP,cAAc,MAAM,OAAO,IAAI;QACjC;IACF;AACF"}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/auth.ts"], "sourcesContent": ["import { doFinalSignOutHandshake } from '@atlas/lib/api/auth/doFinalSignOutHandshake'\r\nimport { refreshAccessToken } from '@atlas/lib/api/auth/refreshAccessToken'\r\nimport type { AdapterSession } from '@auth/core/adapters'\r\nimport NextAuth, { type Session } from 'next-auth'\r\nimport type { JWT } from 'next-auth/jwt'\r\nimport Keycloak from 'next-auth/providers/keycloak'\r\n\r\nconst ACCESS_TOKEN_BUFFER_SECONDS = 60\r\n\r\nexport const { handlers, signIn, signOut, auth } = NextAuth({\r\n  providers: [Keycloak],\r\n\r\n  callbacks: {\r\n    /**\r\n     * Controlla se l'utente è autorizzato ad accedere.\r\n     * Utile per middleware o protezione server-side.\r\n     *\r\n     */\r\n    authorized({ auth }) {\r\n      return !!auth\r\n    },\r\n\r\n    /**\r\n     * Callback JWT: Gestisce la creazione e l'aggiornamento del token JWT.\r\n     * Eseguito al login iniziale e ad ogni richiesta successiva che usa il JWT.\r\n     */\r\n    async jwt({ token, account, user }) {\r\n      const now = Math.floor(Date.now() / 1000)\r\n\r\n      if (account?.access_token && account?.id_token && user) {\r\n        console.debug('[AUTH] jwt - Initial sign-in.')\r\n\r\n        const accessTokenExpires =\r\n          account.expires_at ?? now + (account.expires_in ?? 300)\r\n\r\n        let refreshTokenExpires: number | undefined\r\n        if (typeof account.refresh_expires_in === 'number') {\r\n          refreshTokenExpires = now + account.refresh_expires_in\r\n          console.debug(\r\n            `[AUTH] jwt - Refresh token initial expiry: ${new Date(refreshTokenExpires * 1000).toISOString()}`,\r\n          )\r\n        }\r\n\r\n        return {\r\n          ...token,\r\n          accessToken: account.access_token,\r\n          idToken: account.id_token,\r\n          refreshToken: account.refresh_token,\r\n          accessTokenExpires: accessTokenExpires,\r\n          refreshTokenExpires: refreshTokenExpires,\r\n          user: user,\r\n          error: undefined,\r\n        }\r\n      }\r\n\r\n      if (token.refreshTokenExpires && now >= token.refreshTokenExpires) {\r\n        console.debug(\r\n          '[AUTH] jwt - Refresh token expired. Invalidating session.',\r\n        )\r\n        return null\r\n      }\r\n\r\n      const isAccessTokenValid =\r\n        token.accessTokenExpires &&\r\n        now < token.accessTokenExpires - ACCESS_TOKEN_BUFFER_SECONDS\r\n\r\n      if (isAccessTokenValid) {\r\n        return token\r\n      }\r\n\r\n      console.debug(\r\n        '[AUTH] jwt - Access token expired or requires refresh. Attempting refresh...',\r\n      )\r\n\r\n      if (!token.refreshToken) {\r\n        console.debug('[AUTH] jwt - Cannot refresh: Missing refresh token.')\r\n        return null\r\n      }\r\n\r\n      try {\r\n        const refreshedToken = await refreshAccessToken(token)\r\n        if (\r\n          refreshedToken.error === 'RefreshTokenExpired' ||\r\n          refreshedToken.error === 'RefreshTokenInvalidated'\r\n        ) {\r\n          console.debug(\r\n            `[AUTH] jwt - Refresh failed (${refreshedToken.error}). Invalidating session.`,\r\n          )\r\n          return null\r\n        }\r\n\r\n        return refreshedToken\r\n      } catch (error) {\r\n        console.error('[AUTH] jwt - Exception during token refresh:', error)\r\n        return null\r\n      }\r\n    },\r\n\r\n    /**\r\n     * Callback Session: Gestisce l'oggetto sessione accessibile lato client.\r\n     * Riceve il token JWT aggiornato dalla callback `jwt`.\r\n     */\r\n    session({ session, token }: { session: Session; token: JWT }): Session {\r\n      session.accessToken = token.accessToken\r\n      session.idToken = token.idToken\r\n      session.user = token.user\r\n      session.error = token.error\r\n      session.accessTokenExpires = token.accessTokenExpires\r\n\r\n      if (session.error) {\r\n        console.warn(\r\n          `[AUTH] session - Propagating token error to client session: ${session.error}`,\r\n        )\r\n      }\r\n\r\n      return session\r\n    },\r\n  },\r\n  session: {\r\n    strategy: 'jwt',\r\n  },\r\n  events: {\r\n    signOut: (\r\n      message:\r\n        | { token: JWT | null }\r\n        // biome-ignore lint/suspicious/noConfusingVoidType: necessary\r\n        | { session: void | AdapterSession | null },\r\n    ) => {\r\n      if ('token' in message && message.token) {\r\n        return doFinalSignOutHandshake(message.token)\r\n      }\r\n      return Promise.resolve()\r\n    },\r\n  },\r\n})\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AAEA;AAEA;AAAA;;;;;AAEA,MAAM,8BAA8B;AAE7B,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,IAAA,wKAAQ,EAAC;IAC1D,WAAW;QAAC,0KAAQ;KAAC;IAErB,WAAW;QACT;;;;KAIC,GACD,YAAW,EAAE,IAAI,EAAE;YACjB,OAAO,CAAC,CAAC;QACX;QAEA;;;KAGC,GACD,MAAM,KAAI,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE;YAChC,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;YAEpC,IAAI,SAAS,gBAAgB,SAAS,YAAY,MAAM;gBACtD,QAAQ,KAAK,CAAC;gBAEd,MAAM,qBACJ,QAAQ,UAAU,IAAI,MAAM,CAAC,QAAQ,UAAU,IAAI,GAAG;gBAExD,IAAI;gBACJ,IAAI,OAAO,QAAQ,kBAAkB,KAAK,UAAU;oBAClD,sBAAsB,MAAM,QAAQ,kBAAkB;oBACtD,QAAQ,KAAK,CACX,CAAC,2CAA2C,EAAE,IAAI,KAAK,sBAAsB,MAAM,WAAW,IAAI;gBAEtG;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,aAAa,QAAQ,YAAY;oBACjC,SAAS,QAAQ,QAAQ;oBACzB,cAAc,QAAQ,aAAa;oBACnC,oBAAoB;oBACpB,qBAAqB;oBACrB,MAAM;oBACN,OAAO;gBACT;YACF;YAEA,IAAI,MAAM,mBAAmB,IAAI,OAAO,MAAM,mBAAmB,EAAE;gBACjE,QAAQ,KAAK,CACX;gBAEF,OAAO;YACT;YAEA,MAAM,qBACJ,MAAM,kBAAkB,IACxB,MAAM,MAAM,kBAAkB,GAAG;YAEnC,IAAI,oBAAoB;gBACtB,OAAO;YACT;YAEA,QAAQ,KAAK,CACX;YAGF,IAAI,CAAC,MAAM,YAAY,EAAE;gBACvB,QAAQ,KAAK,CAAC;gBACd,OAAO;YACT;YAEA,IAAI;gBACF,MAAM,iBAAiB,MAAM,IAAA,6KAAkB,EAAC;gBAChD,IACE,eAAe,KAAK,KAAK,yBACzB,eAAe,KAAK,KAAK,2BACzB;oBACA,QAAQ,KAAK,CACX,CAAC,6BAA6B,EAAE,eAAe,KAAK,CAAC,wBAAwB,CAAC;oBAEhF,OAAO;gBACT;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gDAAgD;gBAC9D,OAAO;YACT;QACF;QAEA;;;KAGC,GACD,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAoC;YAC1D,QAAQ,WAAW,GAAG,MAAM,WAAW;YACvC,QAAQ,OAAO,GAAG,MAAM,OAAO;YAC/B,QAAQ,IAAI,GAAG,MAAM,IAAI;YACzB,QAAQ,KAAK,GAAG,MAAM,KAAK;YAC3B,QAAQ,kBAAkB,GAAG,MAAM,kBAAkB;YAErD,IAAI,QAAQ,KAAK,EAAE;gBACjB,QAAQ,IAAI,CACV,CAAC,4DAA4D,EAAE,QAAQ,KAAK,EAAE;YAElF;YAEA,OAAO;QACT;IACF;IACA,SAAS;QACP,UAAU;IACZ;IACA,QAAQ;QACN,SAAS,CACP;YAKA,IAAI,WAAW,WAAW,QAAQ,KAAK,EAAE;gBACvC,OAAO,IAAA,uLAAuB,EAAC,QAAQ,KAAK;YAC9C;YACA,OAAO,QAAQ,OAAO;QACxB;IACF;AACF"}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/i18n/request.ts"], "sourcesContent": ["import { getRequestConfig } from 'next-intl/server'\r\nimport { routing } from './routing'\r\n\r\nexport default getRequestConfig(async ({ requestLocale }) => {\r\n  // This typically corresponds to the `[locale]` segment\r\n  let locale = await requestLocale\r\n\r\n  // Ensure that a valid locale is used\r\n  if (!(locale && routing.locales.includes(locale as any))) {\r\n    locale = routing.defaultLocale\r\n  }\r\n\r\n  return {\r\n    locale,\r\n    messages: (await import(`../../messages/${locale}.json`)).default,\r\n  }\r\n})\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;uCAEe,IAAA,sRAAgB,EAAC,OAAO,EAAE,aAAa,EAAE;IACtD,uDAAuD;IACvD,IAAI,SAAS,MAAM;IAEnB,qCAAqC;IACrC,IAAI,CAAC,CAAC,UAAU,yIAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAc,GAAG;QACxD,SAAS,yIAAO,CAAC,aAAa;IAChC;IAEA,OAAO;QACL;QACA,UAAU,CAAC;;;;;;;;;kBAAa,CAAC,eAAe,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IACnE;AACF"}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/i18n/routing.ts"], "sourcesContent": ["import { createNavigation } from 'next-intl/navigation'\r\nimport { defineRouting } from 'next-intl/routing'\r\n\r\nexport const locales = ['en', 'it'] as const\r\n\r\nexport type Locales = (typeof locales)[number]\r\n\r\nexport const routing = defineRouting({\r\n  locales,\r\n\r\n  defaultLocale: 'it',\r\n})\r\n\r\nexport const { Link, redirect, usePathname, useRouter, getPathname } =\r\n  createNavigation(routing)\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,MAAM,UAAU;IAAC;IAAM;CAAK;AAI5B,MAAM,UAAU,IAAA,2PAAa,EAAC;IACnC;IAEA,eAAe;AACjB;AAEO,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,GAClE,IAAA,0RAAgB,EAAC"}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["// middleware.ts\r\nimport { auth } from '@atlas/auth'\r\nimport type { NextRequest } from 'next/server'\r\nimport { NextResponse } from 'next/server'\r\nimport createMiddleware from 'next-intl/middleware'\r\nimport { routing } from './i18n/routing'\r\n\r\nconst i18n = createMiddleware(routing)\r\n\r\nexport const config = {\r\n  // intercetta solo pagine; esclude API, _next e file statici\r\n  matcher: ['/((?!api|_next|.*\\\\..*).*)'],\r\n}\r\n\r\n// piccolo alias per il tipo di contesto che vuole next-auth\r\ntype Ctx = { params: Record<string, string | string[]> }\r\n\r\nexport default function middleware(req: NextRequest) {\r\n  // 1) lascia lavorare next-intl (redirect/rewrite lingua)\r\n  const res = i18n(req)\r\n  if (res) {\r\n    return res\r\n  }\r\n\r\n  const withAuth = auth(() => NextResponse.next())\r\n  return withAuth(req as any, { params: {} } as Ctx)\r\n}\r\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;;;;AAChB;AAEA;AAAA;AACA;AACA;;;;;AAEA,MAAM,OAAO,IAAA,yMAAgB,EAAC,yIAAO;AAE9B,MAAM,SAAS;IACpB,4DAA4D;IAC5D,SAAS;QAAC;KAA6B;AACzC;AAKe,SAAS,WAAW,GAAgB;IACjD,yDAAyD;IACzD,MAAM,MAAM,KAAK;IACjB,IAAI,KAAK;QACP,OAAO;IACT;IAEA,MAAM,WAAW,IAAA,2HAAI,EAAC,IAAM,gMAAY,CAAC,IAAI;IAC7C,OAAO,SAAS,KAAY;QAAE,QAAQ,CAAC;IAAE;AAC3C"}}]}