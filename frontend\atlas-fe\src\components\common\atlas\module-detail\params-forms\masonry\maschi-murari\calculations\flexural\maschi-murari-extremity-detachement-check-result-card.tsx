import { Badge } from '@atlas/components/ui/badge'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@atlas/components/ui/card'
import { cn } from '@atlas/lib/utils'
import type { ExtremityDetachementCheckSchema } from '@atlas/types/schemas/masonry/maschi-murari-form'
import { useLocale, useTranslations } from 'next-intl'

type Props = {
  extremityDetachementCheck?: ExtremityDetachementCheckSchema
}

export function ExtremityDetachementCheck({
  extremityDetachementCheck,
}: Props) {
  const {
    appliedSpecificBendingMoment30FromEdge,
    reinforcementDesignStrainForEndDebonding,
    neutralAxisCompressedEdgeDistance,
    masonryStrain,
    resultantCompressiveForceMasonry,
    resultantTensileForceFrcm,
    designMomentCapacityReinforcedSection,
    check,
  } = extremityDetachementCheck || {}

  const t = useTranslations(
    'components.calculations.maschi-murari.flexuralReinforcementCalculationResult.extremityDetachementCheck',
  )

  const locale = useLocale()

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('title')}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <span className="font-medium">
            {t('appliedSpecificBendingMoment30FromEdge.label')}:
          </span>{' '}
          <span>
            {appliedSpecificBendingMoment30FromEdge?.toLocaleString(locale, {
              maximumFractionDigits: 5,
            })}{' '}
          </span>
        </div>
        <div>
          <span className="font-medium">
            {t('reinforcementDesignStrainForEndDebonding.label')}:
          </span>{' '}
          <span>
            {reinforcementDesignStrainForEndDebonding?.toLocaleString(locale, {
              maximumFractionDigits: 5,
            })}{' '}
          </span>
        </div>
        <div>
          <span className="font-medium">
            {t('neutralAxisCompressedEdgeDistance.label')}:
          </span>{' '}
          <span>
            {neutralAxisCompressedEdgeDistance?.toLocaleString(locale, {
              maximumFractionDigits: 5,
            })}{' '}
          </span>
        </div>
        <div>
          <span className="font-medium">{t('masonryStrain.label')}:</span>{' '}
          <span>
            {masonryStrain?.toLocaleString(locale, {
              maximumFractionDigits: 5,
            })}{' '}
          </span>
        </div>
        <div>
          <span className="font-medium">
            {t('resultantCompressiveForceMasonry.label')}:
          </span>{' '}
          <span>
            {resultantCompressiveForceMasonry?.toLocaleString(locale, {
              maximumFractionDigits: 5,
            })}{' '}
          </span>
        </div>
        <div>
          <span className="font-medium">
            {t('resultantTensileForceFrcm.label')}:
          </span>{' '}
          <span>
            {resultantTensileForceFrcm?.toLocaleString(locale, {
              maximumFractionDigits: 5,
            })}{' '}
          </span>
        </div>
        <div>
          <span className="font-medium">
            {t('designMomentCapacityReinforcedSection.label')}:
          </span>{' '}
          <span>
            {designMomentCapacityReinforcedSection?.toLocaleString(locale, {
              maximumFractionDigits: 5,
            })}{' '}
          </span>
        </div>
        <span className="font-medium">{t('check.label')}:</span>{' '}
        <Badge
          className={cn(
            'text-base px-3 py-1',
            check ? 'bg-green-600' : 'bg-red-600',
          )}
        >
          {check ? t('check.satisfied') : t('check.notSatisfied')}
        </Badge>
        {check ? null : (
          <div>
            <span className="font-medium text-red-600">
              {' '}
              {t('checkResultingText.no')}
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
