import { NumberFormInput } from '@atlas/components/common/form/number-form-input'
import { SelectFormInput } from '@atlas/components/common/form/select-form-input'
import { Button } from '@atlas/components/ui/button'
import { Form } from '@atlas/components/ui/form'
import type { Module } from '@atlas/lib/api/modules/schemas/modules'
import { useProductsQuery } from '@atlas/lib/api/products/use-products-query'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'
import { zodResolver } from '@hookform/resolvers/zod'
import { Loader2Icon } from 'lucide-react'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useCallback, useEffect, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

const formSchema = z.object({
  productId: z.string().min(1, 'Product is required'),
  stripWidth: z.number().min(0.1, 'Strip width must be greater than 0'),
  equivalentThickness: z
    .number()
    .min(0.001, 'Equivalent thickness must be greater than 0'),
  layersNumber: z.number().min(1, 'Layers number must be at least 1'),
  expositionType: z.enum(['INTERNAL', 'EXTERNAL', 'AGGRESSIVE']),
  environmentalConversionFactor: z
    .number()
    .min(0, 'Environmental conversion factor must be positive'),
})

type FormSchema = z.infer<typeof formSchema>

type Props = {
  session: Session
  projectId: Project['id']
  moduleId: Module['id']
  defaultValues?: Partial<FormSchema>
  preInterventionData?: any
  materialProperties?: any
  geometryProperties?: any
  onSave?: () => void
  onGeometryChange?: (geometry: Partial<FormSchema>) => void
  initialDeformation?: any
}

const EXPOSITION_TYPE_OPTIONS = [
  { value: 'INTERNAL', label: 'Internal' },
  { value: 'EXTERNAL', label: 'External' },
  { value: 'AGGRESSIVE', label: 'Aggressive' },
]

export const WoodCompositeGeometryForm = ({
  session,
  projectId,
  moduleId,
  defaultValues,
  preInterventionData: _preInterventionData,
  materialProperties,
  geometryProperties,
  onSave,
  onGeometryChange,
  initialDeformation,
}: Props) => {
  const t = useTranslations('forms.project-params.wood.compositeGeometry')
  const tCommon = useTranslations('actions.common')

  const { data: productsData, isLoading: isLoadingProducts } = useProductsQuery(
    'wood',
    session.accessToken,
  )

  const productOptions = useMemo(() => {
    if (!productsData?.content) {
      return []
    }
    return productsData.content
      .filter(product => product.categories.includes('wood'))
      .map(product => ({
        value: product.id,
        label: product.name,
      }))
  }, [productsData])

  const form = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      productId: defaultValues?.productId || '',
      stripWidth: defaultValues?.stripWidth || 0,
      equivalentThickness: defaultValues?.equivalentThickness || 0,
      layersNumber: defaultValues?.layersNumber || 2.25,
      expositionType: defaultValues?.expositionType || 'INTERNAL',
      environmentalConversionFactor:
        defaultValues?.environmentalConversionFactor || 0.95,
    },
  })

  // Reset form when defaultValues change (for saved data after refresh)
  useEffect(() => {
    if (defaultValues) {
      form.reset({
        productId: defaultValues.productId ?? '',
        stripWidth: defaultValues.stripWidth ?? 0,
        equivalentThickness: defaultValues.equivalentThickness ?? 0,
        layersNumber: defaultValues.layersNumber ?? 2.25,
        expositionType: defaultValues.expositionType ?? 'INTERNAL',
        environmentalConversionFactor:
          defaultValues.environmentalConversionFactor ?? 0.95,
      })
    }
  }, [defaultValues, form])

  const { mutate, isPending } = useSaveModuleParamsMutation(session.accessToken)

  const handleFormSubmit = useCallback(
    (data: FormSchema) => {
      // Save as part of postIntervationCheck structure with all inherited data
      mutate({
        projectId,
        moduleId,
        body: {
          postIntervationCheck: {
            // Include inherited data from pre-intervention
            initialDeformation: initialDeformation || 0,
            materialProperties: materialProperties || null,
            geometry: geometryProperties || null,
            // Include the current form data
            compositeGeometry: data,
          },
        } as any,
      })
      onSave?.()
    },
    [
      mutate,
      projectId,
      moduleId,
      onSave,
      materialProperties,
      geometryProperties,
      initialDeformation,
    ],
  )

  // Watch for exposition type changes to update environmental conversion factor
  const expositionType = form.watch('expositionType')
  const selectedProductId = form.watch('productId')

  useEffect(() => {
    let factor = 0.85 // default for external and aggressive
    if (expositionType === 'INTERNAL') {
      factor = 0.95
    }
    form.setValue('environmentalConversionFactor', factor)
  }, [expositionType, form])

  // Update equivalent thickness when product changes
  useEffect(() => {
    if (selectedProductId && productsData?.content) {
      const selectedProduct = productsData.content.find(
        p => p.id === selectedProductId,
      )
      if (selectedProduct) {
        form.setValue('equivalentThickness', selectedProduct.thickness)
      }
    }
  }, [selectedProductId, productsData, form])

  // Watch specific form values and notify parent component of changes
  const stripWidth = form.watch('stripWidth')
  const layersNumber = form.watch('layersNumber')
  const equivalentThickness = form.watch('equivalentThickness')
  const environmentalConversionFactor = form.watch(
    'environmentalConversionFactor',
  )

  useEffect(() => {
    // Only notify parent if we have a valid product selection and the callback exists
    if (onGeometryChange && selectedProductId && stripWidth > 0) {
      const geometryData = {
        productId: selectedProductId,
        stripWidth,
        layersNumber,
        equivalentThickness,
        expositionType,
        environmentalConversionFactor,
      }
      onGeometryChange(geometryData)
    }
  }, [
    selectedProductId,
    stripWidth,
    layersNumber,
    equivalentThickness,
    expositionType,
    environmentalConversionFactor,
    onGeometryChange,
  ])

  return (
    <div className="flex flex-col 2xl:flex-row justify-center gap-2">
      <Form {...form}>
        <form
          className="space-y-4 rounded-md border p-4 grow"
          onSubmit={form.handleSubmit(handleFormSubmit)}
        >
          <SelectFormInput
            control={form.control}
            name="productId"
            options={productOptions}
            t={t}
            loading={isLoadingProducts}
            required={true}
          />

          <NumberFormInput
            control={form.control}
            name="stripWidth"
            t={t}
            required={true}
          />

          <NumberFormInput
            control={form.control}
            name="equivalentThickness"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />

          <NumberFormInput
            control={form.control}
            name="layersNumber"
            t={t}
            required={true}
          />

          <SelectFormInput
            control={form.control}
            name="expositionType"
            options={EXPOSITION_TYPE_OPTIONS}
            t={t}
            required={true}
          />

          <NumberFormInput
            control={form.control}
            name="environmentalConversionFactor"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />

          <Button
            type="submit"
            className="w-full sm:w-auto"
            disabled={isPending}
          >
            {isPending && <Loader2Icon className="animate-spin" />}
            {tCommon('save')}
          </Button>
        </form>
      </Form>
    </div>
  )
}
