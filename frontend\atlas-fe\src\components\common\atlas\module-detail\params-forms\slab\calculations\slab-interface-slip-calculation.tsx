'use client'

import { NumberFormInput } from '@atlas/components/common/form/number-form-input'
import { SelectFormInput } from '@atlas/components/common/form/select-form-input'
import { Button } from '@atlas/components/ui/button'
import { Card, CardContent } from '@atlas/components/ui/card'
import { Form } from '@atlas/components/ui/form'
import type { ModuleWithParamsSlab } from '@atlas/lib/api/modules/schemas/modules'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import { useModuleCalculation } from '@atlas/lib/mutation/modules/use-module-calculation'
import { useProductsByType } from '@atlas/lib/query/products/use-products-by-type'
import {
  type SlabInterfaceSlipCalculationInput,
  slabInterfaceSlipCalculationSchema,
} from '@atlas/types/schemas/slab-form'
import { zodResolver } from '@hookform/resolvers/zod'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useEffect, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { SlabInterfaceSlipResultCard } from './slab-interface-slip-result-card'

type Props = {
  module: ModuleWithParamsSlab
  session: Session
  projectId: Project['id']
}

export const SlabInterfaceSlipCalculation = ({
  session,
  module,
  projectId,
}: Props) => {
  const t = useTranslations('forms.calculations.slab.interface-slip')
  const tAction = useTranslations('actions.calculations.slab.interface-slip')
  const tCommon = useTranslations('actions.common')

  const { mutate, isPending } = useModuleCalculation(session.accessToken)

  // Get results from module
  const { interfaceSlipVerifyExecutionInput, interfaceSlipCalculationResult } =
    module

  // Debug logging
  console.log('🔍 Slab Interface Slip Debug:', {
    interfaceSlipVerifyExecutionInput: interfaceSlipVerifyExecutionInput,
    interfaceSlipCalculationResult: interfaceSlipCalculationResult,
    'module keys': Object.keys(module),
  })

  // Get FRC product name from module params
  const frcProductName =
    module.params?.slabFrcReinforcement?.frcReinforcementType

  const {
    data: products,
    isError: errorGettingProducts,
    isLoading: isLoadingProducts,
  } = useProductsByType(session, 'SLAB', 0, 100)

  // Find product by name from FRC reinforcement
  const frcProduct = useMemo(
    () => products?.content.find(p => p.name === frcProductName),
    [frcProductName, products],
  )

  const form = useForm<SlabInterfaceSlipCalculationInput>({
    resolver: zodResolver(slabInterfaceSlipCalculationSchema),
    defaultValues: {
      calculationType: 'INTERFACE_SLIP_VERIFY',
      input: {
        shearForce: interfaceSlipVerifyExecutionInput?.shearForce ?? 30,
        productInput: {
          id:
            interfaceSlipVerifyExecutionInput?.product?.id ??
            frcProduct?.id ??
            '',
          name:
            interfaceSlipVerifyExecutionInput?.product?.name ??
            frcProduct?.name ??
            '',
          sourceType:
            interfaceSlipVerifyExecutionInput?.product?.sourceType ??
            (frcProduct ? 'DATABASE' : 'CUSTOM'),
          frcSlabThickness:
            interfaceSlipVerifyExecutionInput?.product?.thickness ??
            frcProduct?.thickness,
          elasticModulus:
            interfaceSlipVerifyExecutionInput?.product?.elasticModulus ??
            frcProduct?.elasticModulus,
          cylindricCompressiveStrength:
            interfaceSlipVerifyExecutionInput?.product
              ?.cylindricCompressiveStrength ??
            frcProduct?.cylindricCompressiveStrength,
          characteristicTensileStrength:
            interfaceSlipVerifyExecutionInput?.product
              ?.characteristicTensileStrength ??
            frcProduct?.characteristicTensileStrength,
          specificWeight:
            interfaceSlipVerifyExecutionInput?.product?.specificWeight ??
            frcProduct?.specificWeight,
          adhesionToConcrete:
            interfaceSlipVerifyExecutionInput?.product?.adhesionToConcrete ??
            frcProduct?.adhesionToConcrete,
          fiberType:
            interfaceSlipVerifyExecutionInput?.product?.fiberType ??
            frcProduct?.fiberType ??
            'CARBON',
        },
      },
    },
  })

  const productsOptions = [
    ...(products?.content.map(m => ({
      value: m.id,
      label: m.name ?? t('product.unnamed'),
    })) ?? []),
    { value: 'custom', label: t('product.custom') },
  ]

  const [productId] = form.watch(['input.productInput.id'])
  const selectedProduct = useMemo(
    () => products?.content.find(p => p.id === productId),
    [productId, products],
  )

  useEffect(() => {
    if (!productId || productId === 'custom') {
      return
    }

    if (selectedProduct) {
      form.setValue('input.productInput.id', selectedProduct.id)
      form.setValue('input.productInput.name', selectedProduct.name || '')
      form.setValue('input.productInput.sourceType', 'DATABASE')

      // Auto-populate product properties - use correct property names from Product schema
      if (selectedProduct.thickness) {
        form.setValue(
          'input.productInput.frcSlabThickness',
          selectedProduct.thickness,
        )
      }
      if (selectedProduct.elasticModulus) {
        form.setValue(
          'input.productInput.elasticModulus',
          selectedProduct.elasticModulus,
        )
      }
      if (
        selectedProduct.characteristicCylindricalCompressiveStrengthFrcMatrix
      ) {
        form.setValue(
          'input.productInput.cylindricCompressiveStrength',
          selectedProduct.characteristicCylindricalCompressiveStrengthFrcMatrix,
        )
      }
      if (selectedProduct.characteristicTensileStrength) {
        form.setValue(
          'input.productInput.characteristicTensileStrength',
          selectedProduct.characteristicTensileStrength,
        )
      }
      if (selectedProduct.specificWeight) {
        form.setValue(
          'input.productInput.specificWeight',
          selectedProduct.specificWeight,
        )
      }
      if (selectedProduct.adhesionToConcrete) {
        form.setValue(
          'input.productInput.adhesionToConcrete',
          selectedProduct.adhesionToConcrete,
        )
      }
      if (selectedProduct.fiberType) {
        form.setValue('input.productInput.fiberType', selectedProduct.fiberType)
      }
    }
  }, [form, productId, selectedProduct])

  const onSubmit = (data: SlabInterfaceSlipCalculationInput) => {
    mutate(
      {
        projectId,
        moduleId: module.id,
        body: data,
      },
      {
        onSuccess: () => {
          toast.success(tAction('calculate.success'))
        },
        onError: (error: any) => {
          toast.error(tAction('calculate.failure', { error: error.message }))
        },
      },
    )
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="pt-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <h1 className="text-3xl font-bold">{t('heading')}</h1>

              <SelectFormInput
                control={form.control}
                name="input.productInput.id"
                options={productsOptions}
                t={t}
                loading={isLoadingProducts}
                requestError={errorGettingProducts}
              />

              <NumberFormInput
                control={form.control}
                name="input.shearForce"
                t={t}
              />

              <Button type="submit" disabled={isPending}>
                {isPending ? tAction('calculating') : tCommon('calculate')}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>

      {interfaceSlipCalculationResult && (
        <SlabInterfaceSlipResultCard result={interfaceSlipCalculationResult} />
      )}
    </div>
  )
}
