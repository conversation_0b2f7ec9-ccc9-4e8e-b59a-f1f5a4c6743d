/**
 * Calculate slab total thickness (D13 in SOLAIO.md)
 * H = D10 + D11 (joistWebHeight + existingSlabHeight)
 */
export const calculateSlabTotalHeight = (
  joistWebHeight: number,
  existingSlabHeight: number,
): number => {
  return joistWebHeight + existingSlabHeight
}

/**
 * Calculate slab effective depth (D16 in SOLAIO.md)
 * d = H - c1 (totalSlabThickness - bottomRebarCover)
 */
export const calculateSlabEffectiveDepth = (
  totalSlabThickness: number,
  bottomRebarCover: number,
): number => {
  return totalSlabThickness - bottomRebarCover
}

/**
 * Calculate reinforcement area for slab rebars
 * Area = quantity * PI() * (diameter/2)^2
 */
export const calculateSlabRebarArea = (
  diameter: number,
  quantity: number,
): number => {
  if (!diameter || !quantity || diameter <= 0 || quantity <= 0) {
    return 0
  }

  const radius = diameter / 2
  return quantity * Math.PI * radius ** 2
}

/**
 * Calculate FRC design cylindrical compressive strength (D68 in SOLAIO.md)
 * fFcd = 0.85 * fFck / 1.5
 */
export const calculateFrcDesignCylindricalCompressiveStrength = (
  characteristicStrength: number,
): number => {
  return (0.85 * characteristicStrength) / 1.5
}

/**
 * Calculate FRC design tensile strength (D72 in SOLAIO.md)
 * fFtu,d = fFtu,k / 1.5
 */
export const calculateFrcDesignTensileStrength = (
  characteristicTensileStrength: number,
): number => {
  return characteristicTensileStrength / 1.5
}

/**
 * Calculate first correction factor for FRC (D75 in SOLAIO.md)
 * η = 1 - (fFck - 50) / 200
 */
export const calculateFrcFirstCorrectionFactor = (
  characteristicStrength: number,
): number => {
  return 1 - (characteristicStrength - 50) / 200
}

/**
 * Calculate second correction factor for FRC (D76 in SOLAIO.md)
 * λ = 0.8 - (fFck - 50) / 400
 */
export const calculateFrcSecondCorrectionFactor = (
  characteristicStrength: number,
): number => {
  return 0.8 - (characteristicStrength - 50) / 400
}

/**
 * Calculate extrados rebar cover distance (D84 in SOLAIO.md)
 * c3 = D65 - (D78/2) (frcSlabThickness - diameter/2)
 */
export const calculateExtradosRebarCoverDistance = (
  frcSlabThickness: number,
  rebarDiameter: number,
): number => {
  return frcSlabThickness - rebarDiameter / 2
}

/**
 * Calculate B450C steel design yield strength (D82 in SOLAIO.md)
 * fyd,new = 450 / 1.15 = 391.30 MPa
 * Note: Assumes γs = 1.15 (brittle mechanisms) and FC=1 (new reinforcement)
 */
export const calculateB450CSteelDesignYieldStrength = (): number => {
  return 450 / 1.15
}

/**
 * Calculate steel yield strain (D83 in SOLAIO.md)
 * εyd = fyd / Es
 */
export const calculateSteelYieldStrain = (
  designYieldStrength: number,
  elasticModulus: number,
): number => {
  if (!elasticModulus || elasticModulus <= 0) {
    return 0
  }
  return designYieldStrength / elasticModulus
}

// Design strength calculations for concrete
export const calculateSlabDesignStrengthForBrittleMechanisms = (
  averageCompressiveStrength: number,
  confidenceFactor: number,
): number => {
  const gammaC = 1.5 // Safety factor for concrete
  return averageCompressiveStrength / confidenceFactor / gammaC
}

export const calculateSlabDesignStrengthForDuctileMechanisms = (
  averageCompressiveStrength: number,
  confidenceFactor: number,
): number => {
  const gammaC = 1.5 // Safety factor for concrete
  return averageCompressiveStrength / confidenceFactor / gammaC
}

// Design tensile strength calculation for concrete
export const calculateSlabDesignTensileStrengthForBrittleMechanisms = (
  averageTensileStrength: number,
  confidenceFactor: number,
): number => {
  const gammaC = 1.5 // Safety factor for concrete
  return averageTensileStrength / confidenceFactor / gammaC
}

// Design yield strength calculations for steel
export const calculateSlabDesignYieldStrengthForBrittleMechanisms = (
  yieldStrength: number,
  confidenceFactor: number,
): number => {
  const gammaS = 1.15 // Safety factor for steel
  return yieldStrength / confidenceFactor / gammaS
}

export const calculateSlabDesignYieldStrengthForDuctileMechanisms = (
  yieldStrength: number,
  confidenceFactor: number,
): number => {
  const gammaS = 1.15 // Safety factor for steel
  return yieldStrength / confidenceFactor / gammaS
}
