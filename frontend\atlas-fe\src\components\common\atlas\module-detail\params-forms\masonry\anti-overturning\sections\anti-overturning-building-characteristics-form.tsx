import { NumberFormInput } from '@atlas/components/common/form/number-form-input'
import { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'
import { Button } from '@atlas/components/ui/button'
import { Form } from '@atlas/components/ui/form'
import { BUILDING_TYPE, MODULE_BUILDING_TYPE } from '@atlas/constants/module'
import type { Module } from '@atlas/lib/api/modules/schemas/modules'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'
import {
  type MasonryAntiOverturningBuildingCharacteristicsSchemaInputs,
  type MasonryAntiOverturningParamsFormSchemaInputs,
  masonryAntiOverturningBuildingCharacteristicsSchema,
} from '@atlas/types/schemas/masonry/antioverturning-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useQueryClient } from '@tanstack/react-query'
import { Loader2Icon } from 'lucide-react'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useCallback, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'

type Props = {
  session: Session
  projectId: Project['id']
  moduleId: Module['id']
  defaultValues?: Partial<MasonryAntiOverturningBuildingCharacteristicsSchemaInputs>
  params: MasonryAntiOverturningParamsFormSchemaInputs
  setParams: (newParams: any) => void
  onSave: () => void
}

export const MasonryAntiOverturningBuildingCharacteristicsForm = ({
  session,
  projectId,
  moduleId,
  defaultValues,
  params,
  setParams,
  onSave,
}: Props) => {
  const t = useTranslations(
    'forms.project-params.anti-overturning.building-characteristics',
  )
  const tAction = useTranslations('actions.save-project-params.messages')
  const tCommon = useTranslations('actions.common')
  const queryClient = useQueryClient()

  const buildingFundamentalPeriodCalculation = (
    buildingType: BUILDING_TYPE,
    totalBuildingHeight: number,
  ) => {
    return buildingType === BUILDING_TYPE.REINFORCED_CONCRETE
      ? 0.075 * totalBuildingHeight ** 0.75
      : 0.05 * totalBuildingHeight ** 0.75
  }

  const defaultBuildingFundamentalPeriod = buildingFundamentalPeriodCalculation(
    defaultValues?.buildingType ?? BUILDING_TYPE.MASONRY,
    defaultValues?.totalBuildingHeight ?? 1,
  )

  const form =
    useForm<MasonryAntiOverturningBuildingCharacteristicsSchemaInputs>({
      resolver: zodResolver(
        masonryAntiOverturningBuildingCharacteristicsSchema,
      ),
      defaultValues: {
        buildingType:
          defaultValues?.buildingType ?? BUILDING_TYPE.REINFORCED_CONCRETE,
        totalBuildingHeight: defaultValues?.totalBuildingHeight ?? 1,
        buildingFundamentalPeriod: defaultBuildingFundamentalPeriod,
        parameterA: defaultBuildingFundamentalPeriod < 0.5 ? 0.8 : 0.3,
        parameterB:
          defaultBuildingFundamentalPeriod < 0.5
            ? 1.4
            : defaultBuildingFundamentalPeriod > 1
              ? 1
              : 1.2,
        parameterAp:
          defaultBuildingFundamentalPeriod < 0.5
            ? 5
            : defaultBuildingFundamentalPeriod > 1
              ? 2.5
              : 4,
      },
    })

  const { mutate, isPending } = useSaveModuleParamsMutation(
    session.accessToken,
    {
      onSuccess: async () => {
        toast.success(tAction('edit.success'))
        await queryClient.invalidateQueries({
          queryKey: ['modules', { projectId, moduleId }],
        })
        onSave()
      },
      onError: error => {
        toast.error(tAction('edit.failure', { error: error.message }))
      },
    },
  )

  const handleFormSubmit = useCallback(
    (body: MasonryAntiOverturningBuildingCharacteristicsSchemaInputs) => {
      // construct body based on global schema:
      const masonryAntiOverturningParams: MasonryAntiOverturningParamsFormSchemaInputs =
        {
          ...params,
          buildingCharacteristics: body,
        }
      mutate({ projectId, moduleId, body: masonryAntiOverturningParams })
    },
    [mutate, projectId, moduleId, params],
  )

  const buildingType = form.watch('buildingType')
  const totalBuildingHeight = form.watch('totalBuildingHeight')

  useEffect(() => {
    const buildingFundamentalPeriod =
      buildingType === BUILDING_TYPE.REINFORCED_CONCRETE
        ? 0.075 * totalBuildingHeight ** 0.75
        : 0.05 * totalBuildingHeight ** 0.75

    form.setValue('parameterA', buildingFundamentalPeriod < 0.5 ? 0.8 : 0.3)
    form.setValue(
      'parameterB',
      buildingFundamentalPeriod < 0.5
        ? 1.4
        : buildingFundamentalPeriod > 1
          ? 1
          : 1.2,
    )
    form.setValue(
      'parameterAp',
      buildingFundamentalPeriod < 0.5
        ? 5
        : buildingFundamentalPeriod > 1
          ? 2.5
          : 4,
    )
    form.setValue('buildingFundamentalPeriod', buildingFundamentalPeriod)
  }, [buildingType, totalBuildingHeight, form])

  useEffect(() => {
    const subscription = form.watch(values => {
      setParams((prev: any) => ({
        ...prev,
        buildingCharacteristics: values,
      }))
    })
    return () => subscription.unsubscribe()
  }, [form, setParams])

  return (
    <Form {...form}>
      <form
        className="space-y-4 rounded-md border p-4"
        onSubmit={form.handleSubmit(handleFormSubmit)}
      >
        <SelectFormFixedInput
          control={form.control}
          name="buildingType"
          options={MODULE_BUILDING_TYPE}
          optionLabelFn={p => t(`building.${p}`)}
          t={t}
          disabled
        />
        <NumberFormInput
          control={form.control}
          name="totalBuildingHeight"
          t={t}
        />
        <NumberFormInput
          control={form.control}
          name="buildingFundamentalPeriod"
          t={t}
          disabled={true}
        />
        <NumberFormInput
          control={form.control}
          name="parameterA"
          t={t}
          disabled={true}
        />
        <NumberFormInput
          control={form.control}
          name="parameterB"
          t={t}
          disabled={true}
        />
        <NumberFormInput
          control={form.control}
          name="parameterAp"
          t={t}
          disabled={true}
        />
        <Button
          type="submit"
          className="w-full sm:w-auto"
          disabled={isPending}
          onClick={form.handleSubmit(handleFormSubmit)}
        >
          {isPending && <Loader2Icon className="animate-spin" />}
          {tCommon('save')}
        </Button>
      </form>
    </Form>
  )
}
