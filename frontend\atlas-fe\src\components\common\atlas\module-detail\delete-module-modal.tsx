'use client'
import { moduleToDeleteAtom } from '@atlas/components/common/atlas/module-detail/module-detail-content'
import {
  AlertDialog,
  AlertDialogAction, // <-- The component we are modifying
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@atlas/components/ui/alert-dialog'
import { buttonVariants } from '@atlas/components/ui/button'
import { useRouter } from '@atlas/i18n/routing'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import { useDeleteModuleMutation } from '@atlas/lib/mutation/modules/use-delete-module-mutation'
import { useQueryClient } from '@tanstack/react-query'
import { useAtom } from 'jotai/index'
import { Loader2Icon } from 'lucide-react'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { toast } from 'sonner'

type Props = {
  session: Session
  project: Project
}

export const DeleteModuleModal = ({ session, project }: Props) => {
  const t = useTranslations('components.modules.delete-modal')
  const tAction = useTranslations('actions.delete-module.messages')
  const tCommon = useTranslations('actions.common')
  const [module, setModuleToDelete] = useAtom(moduleToDeleteAtom)
  const router = useRouter()
  const queryClient = useQueryClient()

  const { mutate, isPending } = useDeleteModuleMutation(session.accessToken, {
    onSuccess: async () => {
      toast.success(tAction('delete.success'))
      setModuleToDelete(undefined)
      await queryClient.invalidateQueries({
        queryKey: ['projects', project.id],
      })
      router.push(`/dashboard/projects/${project.id}`)
    },
    onError: error => {
      toast.error(tAction('delete.failure', { error: error.message }))
    },
  })

  const handleConfirm = () => {
    mutate({ projectId: project.id, moduleId: module!.id })
  }

  const handleCancel = () => {
    setModuleToDelete(undefined)
  }

  return (
    <AlertDialog
      open={!!module}
      onOpenChange={() => {
        setModuleToDelete(undefined)
      }}
    >
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{t('title')}</AlertDialogTitle>
          <AlertDialogDescription>
            {t('description', {
              projectName: project.constructionSiteName ?? '',
              moduleName: module!.name ?? '',
            })}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleCancel}>
            {tCommon('cancel')}
          </AlertDialogCancel>
          <AlertDialogAction
            className={buttonVariants({ variant: 'destructive' })}
            onClick={handleConfirm}
          >
            {isPending && <Loader2Icon className="animate-spin" />}
            {tCommon('delete')}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  )
}
