{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/alert.tsx"], "sourcesContent": ["import { cn } from '@atlas/lib/utils'\r\nimport { cva, type VariantProps } from 'class-variance-authority'\r\nimport type * as React from 'react'\r\n\r\nconst alertVariants = cva(\r\n  'relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current',\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: 'bg-card text-card-foreground',\r\n        destructive:\r\n          'text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n    },\r\n  },\r\n)\r\n\r\nfunction Alert({\r\n  className,\r\n  variant,\r\n  ...props\r\n}: React.ComponentProps<'div'> & VariantProps<typeof alertVariants>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert\"\r\n      role=\"alert\"\r\n      className={cn(alertVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-title\"\r\n      className={cn(\r\n        'col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-description\"\r\n      className={cn(\r\n        'text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAGA,MAAM,gBAAgB,IAAA,uKAAG,EACvB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,IAAA,yHAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/module-detail-error-component.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { Alert, AlertDescription, AlertTitle } from '@atlas/components/ui/alert'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Link } from '@atlas/i18n/routing'\r\nimport type { ApiError, ValidationError } from '@atlas/types'\r\nimport { AlertTriangle } from 'lucide-react'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ninterface DetailErrorProps {\r\n  reset?: () => void\r\n  error?: ApiError | ValidationError\r\n}\r\n\r\nexport const ModuleDetailErrorComponent = ({ reset }: DetailErrorProps) => {\r\n  const t = useTranslations('pages.module-detail.error')\r\n\r\n  return (\r\n    <div className=\"flex items-center justify-center h-full p-4 md:p-8\">\r\n      <Alert variant=\"destructive\" className=\"max-w-md w-full\">\r\n        <AlertTriangle className=\"h-5 w-5\" />\r\n        <AlertTitle className=\"font-bold text-lg mb-1\">{t('title')}</AlertTitle>\r\n        <AlertDescription>\r\n          <p className=\"mb-4\">{t('description')}</p>\r\n          <div className=\"flex justify-end gap-3\">\r\n            {reset && (\r\n              <Button variant=\"outline\" size=\"sm\" onClick={() => reset()}>\r\n                {t('actions.retry.label')}\r\n              </Button>\r\n            )}\r\n            <Link href=\"/dashboard\">\r\n              <Button variant=\"outline\" size=\"sm\">\r\n                {t('actions.go-to-home.label')}\r\n              </Button>\r\n            </Link>\r\n          </div>\r\n        </AlertDescription>\r\n      </Alert>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AACA;AAPA;;;;;;;AAcO,MAAM,6BAA6B,CAAC,EAAE,KAAK,EAAoB;IACpE,MAAM,IAAI,IAAA,yNAAe,EAAC;IAE1B,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,0IAAK;YAAC,SAAQ;YAAc,WAAU;;8BACrC,8OAAC,yOAAa;oBAAC,WAAU;;;;;;8BACzB,8OAAC,+IAAU;oBAAC,WAAU;8BAA0B,EAAE;;;;;;8BAClD,8OAAC,qJAAgB;;sCACf,8OAAC;4BAAE,WAAU;sCAAQ,EAAE;;;;;;sCACvB,8OAAC;4BAAI,WAAU;;gCACZ,uBACC,8OAAC,4IAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,SAAS,IAAM;8CAChD,EAAE;;;;;;8CAGP,8OAAC,8HAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,4IAAM;wCAAC,SAAQ;wCAAU,MAAK;kDAC5B,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB", "debugId": null}}, {"offset": {"line": 177, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/app/%5Blocale%5D/dashboard/projects/%5BprojectId%5D/modules/%5BmoduleId%5D/error.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { ModuleDetailErrorComponent } from '@atlas/components/common/atlas/module-detail/module-detail-error-component'\r\n\r\ninterface DetailErrorProps {\r\n  reset?: () => void\r\n}\r\nconst ProjectDetailError = ({ reset }: DetailErrorProps) => (\r\n  <ModuleDetailErrorComponent reset={reset} />\r\n)\r\n\r\nexport default ProjectDetailError\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAOA,MAAM,qBAAqB,CAAC,EAAE,KAAK,EAAoB,iBACrD,8OAAC,iOAA0B;QAAC,OAAO;;;;;;uCAGtB", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/triangle-alert.js", "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/lucide-react/src/icons/triangle-alert.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'm21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3',\n      key: 'wmoenq',\n    },\n  ],\n  ['path', { d: 'M12 9v4', key: 'juzpu7' }],\n  ['path', { d: 'M12 17h.01', key: 'p32p05' }],\n];\n\n/**\n * @component @name TriangleAlert\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEuNzMgMTgtOC0xNGEyIDIgMCAwIDAtMy40OCAwbC04IDE0QTIgMiAwIDAgMCA0IDIxaDE2YTIgMiAwIDAgMCAxLjczLTMiIC8+CiAgPHBhdGggZD0iTTEyIDl2NCIgLz4KICA8cGF0aCBkPSJNMTIgMTdoLjAxIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/triangle-alert\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TriangleAlert = createLucideIcon('triangle-alert', __iconNode);\n\nexport default TriangleAlert;\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAA,CAAA,CAAA,CAAuB;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAW,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAA;YAAE,GAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAc,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CAC7C;AAaA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,aAAA,CAAA,CAAA,KAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,8JAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAkB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAA", "debugId": null}}]}