(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "ExtremityDetachementCheck",
    ()=>ExtremityDetachementCheck
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/use-intl/dist/esm/development/react.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
function ExtremityDetachementCheck(param) {
    let { extremityDetachementCheck } = param;
    _s();
    const { appliedSpecificBendingMoment30FromEdge, reinforcementDesignStrainForEndDebonding, neutralAxisCompressedEdgeDistance, masonryStrain, resultantCompressiveForceMasonry, resultantTensileForceFrcm, designMomentCapacityReinforcedSection, check } = extremityDetachementCheck || {};
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('components.calculations.maschi-murari.flexuralReinforcementCalculationResult.extremityDetachementCheck');
    const locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                    children: t('title')
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                    lineNumber: 39,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                lineNumber: 38,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                className: "space-y-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('appliedSpecificBendingMoment30FromEdge.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                                lineNumber: 43,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    appliedSpecificBendingMoment30FromEdge === null || appliedSpecificBendingMoment30FromEdge === void 0 ? void 0 : appliedSpecificBendingMoment30FromEdge.toLocaleString(locale, {
                                        maximumFractionDigits: 5
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                                lineNumber: 46,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                        lineNumber: 42,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('reinforcementDesignStrainForEndDebonding.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                                lineNumber: 53,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    reinforcementDesignStrainForEndDebonding === null || reinforcementDesignStrainForEndDebonding === void 0 ? void 0 : reinforcementDesignStrainForEndDebonding.toLocaleString(locale, {
                                        maximumFractionDigits: 5
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                                lineNumber: 56,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                        lineNumber: 52,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('neutralAxisCompressedEdgeDistance.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                                lineNumber: 63,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    neutralAxisCompressedEdgeDistance === null || neutralAxisCompressedEdgeDistance === void 0 ? void 0 : neutralAxisCompressedEdgeDistance.toLocaleString(locale, {
                                        maximumFractionDigits: 5
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                                lineNumber: 66,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                        lineNumber: 62,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('masonryStrain.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                                lineNumber: 73,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    masonryStrain === null || masonryStrain === void 0 ? void 0 : masonryStrain.toLocaleString(locale, {
                                        maximumFractionDigits: 5
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                                lineNumber: 74,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                        lineNumber: 72,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('resultantCompressiveForceMasonry.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                                lineNumber: 81,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    resultantCompressiveForceMasonry === null || resultantCompressiveForceMasonry === void 0 ? void 0 : resultantCompressiveForceMasonry.toLocaleString(locale, {
                                        maximumFractionDigits: 5
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                                lineNumber: 84,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                        lineNumber: 80,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('resultantTensileForceFrcm.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                                lineNumber: 91,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    resultantTensileForceFrcm === null || resultantTensileForceFrcm === void 0 ? void 0 : resultantTensileForceFrcm.toLocaleString(locale, {
                                        maximumFractionDigits: 5
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                                lineNumber: 94,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                        lineNumber: 90,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('designMomentCapacityReinforcedSection.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                                lineNumber: 101,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    designMomentCapacityReinforcedSection === null || designMomentCapacityReinforcedSection === void 0 ? void 0 : designMomentCapacityReinforcedSection.toLocaleString(locale, {
                                        maximumFractionDigits: 5
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                                lineNumber: 104,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                        lineNumber: 100,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "font-medium",
                        children: [
                            t('check.label'),
                            ":"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                        lineNumber: 110,
                        columnNumber: 9
                    }, this),
                    ' ',
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-base px-3 py-1', check ? 'bg-green-600' : 'bg-red-600'),
                        children: check ? t('check.satisfied') : t('check.notSatisfied')
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                        lineNumber: 111,
                        columnNumber: 9
                    }, this),
                    check ? null : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium text-red-600",
                            children: [
                                ' ',
                                t('checkResultingText.no')
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                            lineNumber: 121,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                        lineNumber: 120,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
                lineNumber: 41,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx",
        lineNumber: 37,
        columnNumber: 5
    }, this);
}
_s(ExtremityDetachementCheck, "ibHvjDxkLpek5QWjIfNYywjof5I=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"]
    ];
});
_c = ExtremityDetachementCheck;
var _c;
__turbopack_context__.k.register(_c, "ExtremityDetachementCheck");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "InPlaneFlexuralCheckResultCard",
    ()=>InPlaneFlexuralCheckResultCard
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/tabs.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$separator$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-separator/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/use-intl/dist/esm/development/react.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
function InPlaneFlexuralCheckResultCard(param) {
    let { inPlaneFlexuralCheckSchema } = param;
    _s();
    const { nonReinforcedSection, reinforcedSection } = inPlaneFlexuralCheckSchema || {};
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('components.calculations.maschi-murari.flexuralReinforcementCalculationResult.inPlaneFlexuralCheck');
    const locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                    children: t('title')
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                    lineNumber: 38,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                lineNumber: 37,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                className: "space-y-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        children: t('nonReinforcedSection.title')
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                        lineNumber: 41,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('nonReinforcedSection.inPlaneBendingMoment.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                                lineNumber: 43,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    nonReinforcedSection === null || nonReinforcedSection === void 0 ? void 0 : nonReinforcedSection.inPlaneBendingMoment.toLocaleString(locale, {
                                        maximumFractionDigits: 5
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                                lineNumber: 46,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                        lineNumber: 42,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('nonReinforcedSection.neutralAxisCompressedEdgeDistance.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                                lineNumber: 53,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    nonReinforcedSection === null || nonReinforcedSection === void 0 ? void 0 : nonReinforcedSection.neutralAxisCompressedEdgeDistance.toLocaleString(locale, {
                                        maximumFractionDigits: 5
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                                lineNumber: 56,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                        lineNumber: 52,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('nonReinforcedSection.inPlaneFlexuralCapacity.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                                lineNumber: 66,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    nonReinforcedSection === null || nonReinforcedSection === void 0 ? void 0 : nonReinforcedSection.inPlaneFlexuralCapacity.toLocaleString(locale, {
                                        maximumFractionDigits: 5
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                                lineNumber: 69,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                        lineNumber: 65,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "font-medium",
                        children: [
                            t('nonReinforcedSection.check.label'),
                            ":"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                        lineNumber: 78,
                        columnNumber: 9
                    }, this),
                    ' ',
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-base px-3 py-1', (nonReinforcedSection === null || nonReinforcedSection === void 0 ? void 0 : nonReinforcedSection.check) ? 'bg-green-600' : 'bg-red-600'),
                        children: (nonReinforcedSection === null || nonReinforcedSection === void 0 ? void 0 : nonReinforcedSection.check) ? t('nonReinforcedSection.check.satisfied') : t('nonReinforcedSection.check.notSatisfied')
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                        lineNumber: 81,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$separator$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                        lineNumber: 91,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        children: t('reinforcedSection.title')
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                        lineNumber: 92,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tabs"], {
                        defaultValue: "hypothesisOne",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsList"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                        value: "hypothesisOne",
                                        children: t('reinforcedSection.hypothesisOne.label')
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                                        lineNumber: 95,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                        value: "hypothesisTwo",
                                        children: t('reinforcedSection.hypothesisTwo.label')
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                                        lineNumber: 98,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                        value: "hypothesisThree",
                                        children: t('reinforcedSection.hypothesisThree.label')
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                                        lineNumber: 101,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                                lineNumber: 94,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                                value: "hypothesisOne",
                                children: renderHypothesis(reinforcedSection === null || reinforcedSection === void 0 ? void 0 : reinforcedSection.hypothesisOne, 'hypothesisOne')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                                lineNumber: 105,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                                value: "hypothesisTwo",
                                children: renderHypothesis(reinforcedSection === null || reinforcedSection === void 0 ? void 0 : reinforcedSection.hypothesisTwo, 'hypothesisTwo')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                                lineNumber: 111,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                                value: "hypothesisThree",
                                children: renderHypothesis(reinforcedSection === null || reinforcedSection === void 0 ? void 0 : reinforcedSection.hypothesisThree, 'hypothesisThree')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                                lineNumber: 117,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                        lineNumber: 93,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('reinforcedSection.momentCapacity.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                                lineNumber: 125,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    reinforcedSection === null || reinforcedSection === void 0 ? void 0 : reinforcedSection.momentCapacity.toLocaleString(locale, {
                                        maximumFractionDigits: 5
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                                lineNumber: 128,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                        lineNumber: 124,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "font-medium",
                        children: [
                            t('reinforcedSection.check.label'),
                            ":"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                        lineNumber: 134,
                        columnNumber: 9
                    }, this),
                    ' ',
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-base px-3 py-1', (reinforcedSection === null || reinforcedSection === void 0 ? void 0 : reinforcedSection.check) ? 'bg-green-600' : 'bg-red-600'),
                        children: (reinforcedSection === null || reinforcedSection === void 0 ? void 0 : reinforcedSection.check) ? t('reinforcedSection.check.satisfied') : t('reinforcedSection.check.notSatisfied')
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                        lineNumber: 137,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                lineNumber: 40,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
        lineNumber: 36,
        columnNumber: 5
    }, this);
    //TURBOPACK unreachable
    ;
    function renderHypothesis(regionHypothesis, labelKey) {
        var _regionHypothesis_neutralAxisCompressedEdgeDistance, _regionHypothesis_reinforcementOrMasonryStrain;
        if (!regionHypothesis) {
            return null;
        }
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                    children: t("reinforcedSection.".concat(labelKey, ".label"))
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                    lineNumber: 157,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: [
                                t('reinforcedSection.hypothesis.neutralAxisCompressedEdgeDistance.label'),
                                ":"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                            lineNumber: 159,
                            columnNumber: 11
                        }, this),
                        ' ',
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            children: [
                                (_regionHypothesis_neutralAxisCompressedEdgeDistance = regionHypothesis.neutralAxisCompressedEdgeDistance) === null || _regionHypothesis_neutralAxisCompressedEdgeDistance === void 0 ? void 0 : _regionHypothesis_neutralAxisCompressedEdgeDistance.toLocaleString(locale, {
                                    maximumFractionDigits: 5
                                }),
                                ' '
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                            lineNumber: 165,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                    lineNumber: 158,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: [
                                t("reinforcedSection.".concat(labelKey, ".reinforcementOrMasonryStrain.label")),
                                ":"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                            lineNumber: 175,
                            columnNumber: 11
                        }, this),
                        ' ',
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            children: [
                                (_regionHypothesis_reinforcementOrMasonryStrain = regionHypothesis.reinforcementOrMasonryStrain) === null || _regionHypothesis_reinforcementOrMasonryStrain === void 0 ? void 0 : _regionHypothesis_reinforcementOrMasonryStrain.toLocaleString(locale, {
                                    maximumFractionDigits: 5
                                }),
                                ' '
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                            lineNumber: 181,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                    lineNumber: 174,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: "font-medium",
                    children: [
                        t("reinforcedSection.".concat(labelKey, ".check.label")),
                        ":"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                    lineNumber: 190,
                    columnNumber: 9
                }, this),
                ' ',
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-base px-3 py-1'),
                    children: regionHypothesis.check ? t("reinforcedSection.".concat(labelKey, ".check.satisfied")) : t("reinforcedSection.".concat(labelKey, ".check.notSatisfied"))
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx",
                    lineNumber: 193,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true);
    }
}
_s(InPlaneFlexuralCheckResultCard, "ibHvjDxkLpek5QWjIfNYywjof5I=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"]
    ];
});
_c = InPlaneFlexuralCheckResultCard;
var _c;
__turbopack_context__.k.register(_c, "InPlaneFlexuralCheckResultCard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "OutOfPlaneFlexuralCheckResultCard",
    ()=>OutOfPlaneFlexuralCheckResultCard
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/tabs.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$separator$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-separator/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/use-intl/dist/esm/development/react.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
function OutOfPlaneFlexuralCheckResultCard(param) {
    let { outOfPlaneFlexuralCheckSchema } = param;
    _s();
    const { nonReinforcedSection, reinforcedSection } = outOfPlaneFlexuralCheckSchema || {};
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('components.calculations.maschi-murari.flexuralReinforcementCalculationResult.outOfPlaneFlexuralCheck');
    const locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                    children: t('title')
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                    lineNumber: 38,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                lineNumber: 37,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                className: "space-y-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        children: t('nonReinforcedSection.title')
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                        lineNumber: 41,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('nonReinforcedSection.appliedDesignBendingMoment.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                                lineNumber: 43,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    nonReinforcedSection === null || nonReinforcedSection === void 0 ? void 0 : nonReinforcedSection.appliedDesignBendingMoment.toLocaleString(locale, {
                                        maximumFractionDigits: 5
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                                lineNumber: 46,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                        lineNumber: 42,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('nonReinforcedSection.neutralAxisCompressedEdgeDistance.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                                lineNumber: 56,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    nonReinforcedSection === null || nonReinforcedSection === void 0 ? void 0 : nonReinforcedSection.neutralAxisCompressedEdgeDistance.toLocaleString(locale, {
                                        maximumFractionDigits: 5
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                                lineNumber: 59,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                        lineNumber: 55,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('nonReinforcedSection.resultantCompressiveForceMasonry.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                                lineNumber: 69,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    nonReinforcedSection === null || nonReinforcedSection === void 0 ? void 0 : nonReinforcedSection.resultantCompressiveForceMasonry.toLocaleString(locale, {
                                        maximumFractionDigits: 5
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                                lineNumber: 72,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                        lineNumber: 68,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('nonReinforcedSection.designBendingMoment.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                                lineNumber: 82,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    nonReinforcedSection === null || nonReinforcedSection === void 0 ? void 0 : nonReinforcedSection.designBendingMoment.toLocaleString(locale, {
                                        maximumFractionDigits: 5
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                                lineNumber: 85,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                        lineNumber: 81,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "font-medium",
                        children: [
                            t('nonReinforcedSection.check.label'),
                            ":"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                        lineNumber: 91,
                        columnNumber: 9
                    }, this),
                    ' ',
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-base px-3 py-1', (nonReinforcedSection === null || nonReinforcedSection === void 0 ? void 0 : nonReinforcedSection.check) ? 'bg-green-600' : 'bg-red-600'),
                        children: (nonReinforcedSection === null || nonReinforcedSection === void 0 ? void 0 : nonReinforcedSection.check) ? t('nonReinforcedSection.check.satisfied') : t('nonReinforcedSection.check.notSatisfied')
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                        lineNumber: 94,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$separator$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                        lineNumber: 104,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        children: t('reinforcedSection.title')
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                        lineNumber: 105,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tabs"], {
                        defaultValue: "regionHypothesisOne",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsList"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                        value: "regionHypothesisOne",
                                        children: t('reinforcedSection.regionHypothesisOne.label')
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                                        lineNumber: 108,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                        value: "regionHypothesisTwo",
                                        children: t('reinforcedSection.regionHypothesisTwo.label')
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                                        lineNumber: 111,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                                lineNumber: 107,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                                value: "regionHypothesisOne",
                                children: renderRegionHypothese(reinforcedSection === null || reinforcedSection === void 0 ? void 0 : reinforcedSection.regionHypothesisOne, 'regionHypothesisOne')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                                lineNumber: 115,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                                value: "regionHypothesisTwo",
                                children: renderRegionHypothese(reinforcedSection === null || reinforcedSection === void 0 ? void 0 : reinforcedSection.regionHypothesisTwo, 'regionHypothesisTwo')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                                lineNumber: 121,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                        lineNumber: 106,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('reinforcedSection.momentCapacity.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                                lineNumber: 129,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    reinforcedSection === null || reinforcedSection === void 0 ? void 0 : reinforcedSection.momentCapacity.toLocaleString(locale, {
                                        maximumFractionDigits: 5
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                                lineNumber: 132,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                        lineNumber: 128,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "font-medium",
                        children: [
                            t('reinforcedSection.check.label'),
                            ":"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                        lineNumber: 138,
                        columnNumber: 9
                    }, this),
                    ' ',
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-base px-3 py-1', (reinforcedSection === null || reinforcedSection === void 0 ? void 0 : reinforcedSection.check) ? 'bg-green-600' : 'bg-red-600'),
                        children: (reinforcedSection === null || reinforcedSection === void 0 ? void 0 : reinforcedSection.check) ? t('reinforcedSection.check.satisfied') : t('reinforcedSection.check.notSatisfied')
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                        lineNumber: 141,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                lineNumber: 40,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
        lineNumber: 36,
        columnNumber: 5
    }, this);
    //TURBOPACK unreachable
    ;
    function renderRegionHypothese(regionHypothesis, labelKey) {
        var _regionHypothesis_neutralAxisCompressedEdgeDistance, _regionHypothesis_reinforcementOrMasonryStrain, _regionHypothesis_resultantCompressiveForceMasonry, _regionHypothesis_resultantTensileForceFrcm, _regionHypothesis_designBendingMomentReinforcedSection;
        if (!regionHypothesis) {
            return null;
        }
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                    children: t("reinforcedSection.".concat(labelKey, ".label"))
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                    lineNumber: 161,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: [
                                t('reinforcedSection.regionHypothesis.neutralAxisCompressedEdgeDistance.label'),
                                ":"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                            lineNumber: 163,
                            columnNumber: 11
                        }, this),
                        ' ',
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            children: [
                                (_regionHypothesis_neutralAxisCompressedEdgeDistance = regionHypothesis.neutralAxisCompressedEdgeDistance) === null || _regionHypothesis_neutralAxisCompressedEdgeDistance === void 0 ? void 0 : _regionHypothesis_neutralAxisCompressedEdgeDistance.toLocaleString(locale, {
                                    maximumFractionDigits: 5
                                }),
                                ' '
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                            lineNumber: 169,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                    lineNumber: 162,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: [
                                t("reinforcedSection.".concat(labelKey, ".reinforcementOrMasonryStrain.label")),
                                ":"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                            lineNumber: 179,
                            columnNumber: 11
                        }, this),
                        ' ',
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            children: [
                                (_regionHypothesis_reinforcementOrMasonryStrain = regionHypothesis.reinforcementOrMasonryStrain) === null || _regionHypothesis_reinforcementOrMasonryStrain === void 0 ? void 0 : _regionHypothesis_reinforcementOrMasonryStrain.toLocaleString(locale, {
                                    maximumFractionDigits: 5
                                }),
                                ' '
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                            lineNumber: 185,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                    lineNumber: 178,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: [
                                t('reinforcedSection.regionHypothesis.resultantCompressiveForceMasonry.label'),
                                ":"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                            lineNumber: 195,
                            columnNumber: 11
                        }, this),
                        ' ',
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            children: [
                                (_regionHypothesis_resultantCompressiveForceMasonry = regionHypothesis.resultantCompressiveForceMasonry) === null || _regionHypothesis_resultantCompressiveForceMasonry === void 0 ? void 0 : _regionHypothesis_resultantCompressiveForceMasonry.toLocaleString(locale, {
                                    maximumFractionDigits: 5
                                }),
                                ' '
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                            lineNumber: 201,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                    lineNumber: 194,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: [
                                t('reinforcedSection.regionHypothesis.resultantTensileForceFrcm.label'),
                                ":"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                            lineNumber: 211,
                            columnNumber: 11
                        }, this),
                        ' ',
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            children: [
                                (_regionHypothesis_resultantTensileForceFrcm = regionHypothesis.resultantTensileForceFrcm) === null || _regionHypothesis_resultantTensileForceFrcm === void 0 ? void 0 : _regionHypothesis_resultantTensileForceFrcm.toLocaleString(locale, {
                                    maximumFractionDigits: 5
                                }),
                                ' '
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                            lineNumber: 217,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                    lineNumber: 210,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: [
                                t('reinforcedSection.regionHypothesis.designBendingMomentReinforcedSection.label'),
                                ":"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                            lineNumber: 227,
                            columnNumber: 11
                        }, this),
                        ' ',
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            children: [
                                (_regionHypothesis_designBendingMomentReinforcedSection = regionHypothesis.designBendingMomentReinforcedSection) === null || _regionHypothesis_designBendingMomentReinforcedSection === void 0 ? void 0 : _regionHypothesis_designBendingMomentReinforcedSection.toLocaleString(locale, {
                                    maximumFractionDigits: 5
                                }),
                                ' '
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                            lineNumber: 233,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                    lineNumber: 226,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: "font-medium",
                    children: [
                        t("reinforcedSection.".concat(labelKey, ".check.label")),
                        ":"
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                    lineNumber: 242,
                    columnNumber: 9
                }, this),
                ' ',
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-base px-3 py-1'),
                    children: regionHypothesis.check ? t("reinforcedSection.".concat(labelKey, ".check.satisfied")) : t("reinforcedSection.".concat(labelKey, ".check.notSatisfied"))
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx",
                    lineNumber: 245,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true);
    }
}
_s(OutOfPlaneFlexuralCheckResultCard, "ibHvjDxkLpek5QWjIfNYywjof5I=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"]
    ];
});
_c = OutOfPlaneFlexuralCheckResultCard;
var _c;
__turbopack_context__.k.register(_c, "OutOfPlaneFlexuralCheckResultCard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-shear-check-result-card.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "ShearCheckResultCard",
    ()=>ShearCheckResultCard
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/use-intl/dist/esm/development/react.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
function ShearCheckResultCard(param) {
    let { shearCheckResults } = param;
    _s();
    const { appliedDesignSpecificShearForce, averageNormalStress, resultantCompressiveForceMasonry, resultantTensileForceFrcm, check } = shearCheckResults || {};
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('components.calculations.maschi-murari.flexuralReinforcementCalculationResult.shearCheck');
    const locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                    children: t('title')
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-shear-check-result-card.tsx",
                    lineNumber: 34,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-shear-check-result-card.tsx",
                lineNumber: 33,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                className: "space-y-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('appliedDesignSpecificShearForce.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-shear-check-result-card.tsx",
                                lineNumber: 38,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    appliedDesignSpecificShearForce === null || appliedDesignSpecificShearForce === void 0 ? void 0 : appliedDesignSpecificShearForce.toLocaleString(locale, {
                                        maximumFractionDigits: 5
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-shear-check-result-card.tsx",
                                lineNumber: 41,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-shear-check-result-card.tsx",
                        lineNumber: 37,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('averageNormalStress.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-shear-check-result-card.tsx",
                                lineNumber: 48,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    averageNormalStress === null || averageNormalStress === void 0 ? void 0 : averageNormalStress.toLocaleString(locale, {
                                        maximumFractionDigits: 5
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-shear-check-result-card.tsx",
                                lineNumber: 49,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-shear-check-result-card.tsx",
                        lineNumber: 47,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('resultantCompressiveForceMasonry.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-shear-check-result-card.tsx",
                                lineNumber: 56,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    resultantCompressiveForceMasonry === null || resultantCompressiveForceMasonry === void 0 ? void 0 : resultantCompressiveForceMasonry.toLocaleString(locale, {
                                        maximumFractionDigits: 5
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-shear-check-result-card.tsx",
                                lineNumber: 59,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-shear-check-result-card.tsx",
                        lineNumber: 55,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('resultantTensileForceFrcm.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-shear-check-result-card.tsx",
                                lineNumber: 66,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    resultantTensileForceFrcm === null || resultantTensileForceFrcm === void 0 ? void 0 : resultantTensileForceFrcm.toLocaleString(locale, {
                                        maximumFractionDigits: 5
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-shear-check-result-card.tsx",
                                lineNumber: 69,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-shear-check-result-card.tsx",
                        lineNumber: 65,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "font-medium",
                        children: [
                            t('check.label'),
                            ":"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-shear-check-result-card.tsx",
                        lineNumber: 75,
                        columnNumber: 9
                    }, this),
                    ' ',
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-base px-3 py-1', check ? 'bg-green-600' : 'bg-red-600'),
                        children: check ? t('check.satisfied') : t('check.notSatisfied')
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-shear-check-result-card.tsx",
                        lineNumber: 76,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-shear-check-result-card.tsx",
                lineNumber: 36,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-shear-check-result-card.tsx",
        lineNumber: 32,
        columnNumber: 5
    }, this);
}
_s(ShearCheckResultCard, "ibHvjDxkLpek5QWjIfNYywjof5I=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"]
    ];
});
_c = ShearCheckResultCard;
var _c;
__turbopack_context__.k.register(_c, "ShearCheckResultCard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation-result.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "MaschiMurariReinforcementFlexuralCalculationResult",
    ()=>MaschiMurariReinforcementFlexuralCalculationResult
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/tabs.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/use-intl/dist/esm/development/react.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$maschi$2d$murari$2f$calculations$2f$flexural$2f$maschi$2d$murari$2d$extremity$2d$detachement$2d$check$2d$result$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$maschi$2d$murari$2f$calculations$2f$flexural$2f$maschi$2d$murari$2d$flexural$2d$in$2d$plane$2d$result$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$maschi$2d$murari$2f$calculations$2f$flexural$2f$maschi$2d$murari$2d$flexural$2d$out$2d$of$2d$plane$2d$result$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$maschi$2d$murari$2f$calculations$2f$flexural$2f$maschi$2d$murari$2d$shear$2d$check$2d$result$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-shear-check-result-card.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
function MaschiMurariReinforcementFlexuralCalculationResult(param) {
    let { flexuralReinforcementCalculationResult } = param;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('components.calculations.maschi-murari.flexuralReinforcementCalculationResult');
    const _locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                className: "text-lg font-medium py-4",
                children: t('title')
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation-result.tsx",
                lineNumber: 34,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                            children: t('title')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation-result.tsx",
                            lineNumber: 37,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation-result.tsx",
                        lineNumber: 36,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                        className: "space-y-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tabs"], {
                            defaultValue: "inPlaneFlexuralCheck",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsList"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                            value: "inPlaneFlexuralCheck",
                                            children: t('inPlaneFlexuralCheck.label')
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation-result.tsx",
                                            lineNumber: 42,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                            value: "outOfPlaneFlexuralCheck",
                                            children: t('outOfPlaneFlexuralCheck.label')
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation-result.tsx",
                                            lineNumber: 45,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                            value: "shearCheck",
                                            children: t('shearCheck.label')
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation-result.tsx",
                                            lineNumber: 48,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                            value: "extremityDetachementCheck",
                                            children: t('extremityDetachementCheck.label')
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation-result.tsx",
                                            lineNumber: 51,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation-result.tsx",
                                    lineNumber: 41,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                                    value: "inPlaneFlexuralCheck",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$maschi$2d$murari$2f$calculations$2f$flexural$2f$maschi$2d$murari$2d$flexural$2d$in$2d$plane$2d$result$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["InPlaneFlexuralCheckResultCard"], {
                                        inPlaneFlexuralCheckSchema: flexuralReinforcementCalculationResult === null || flexuralReinforcementCalculationResult === void 0 ? void 0 : flexuralReinforcementCalculationResult.inPlaneFlexuralCheck
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation-result.tsx",
                                        lineNumber: 56,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation-result.tsx",
                                    lineNumber: 55,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                                    value: "outOfPlaneFlexuralCheck",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$maschi$2d$murari$2f$calculations$2f$flexural$2f$maschi$2d$murari$2d$flexural$2d$out$2d$of$2d$plane$2d$result$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["OutOfPlaneFlexuralCheckResultCard"], {
                                        outOfPlaneFlexuralCheckSchema: flexuralReinforcementCalculationResult === null || flexuralReinforcementCalculationResult === void 0 ? void 0 : flexuralReinforcementCalculationResult.outOfPlaneFlexuralCheck
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation-result.tsx",
                                        lineNumber: 63,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation-result.tsx",
                                    lineNumber: 62,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                                    value: "shearCheck",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$maschi$2d$murari$2f$calculations$2f$flexural$2f$maschi$2d$murari$2d$shear$2d$check$2d$result$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ShearCheckResultCard"], {
                                        shearCheckResults: flexuralReinforcementCalculationResult === null || flexuralReinforcementCalculationResult === void 0 ? void 0 : flexuralReinforcementCalculationResult.shearCheck
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation-result.tsx",
                                        lineNumber: 70,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation-result.tsx",
                                    lineNumber: 69,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                                    value: "extremityDetachementCheck",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$maschi$2d$murari$2f$calculations$2f$flexural$2f$maschi$2d$murari$2d$extremity$2d$detachement$2d$check$2d$result$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ExtremityDetachementCheck"], {
                                        extremityDetachementCheck: flexuralReinforcementCalculationResult === null || flexuralReinforcementCalculationResult === void 0 ? void 0 : flexuralReinforcementCalculationResult.extremityDetachementCheck
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation-result.tsx",
                                        lineNumber: 77,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation-result.tsx",
                                    lineNumber: 76,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation-result.tsx",
                            lineNumber: 40,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation-result.tsx",
                        lineNumber: 39,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation-result.tsx",
                lineNumber: 35,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
_s(MaschiMurariReinforcementFlexuralCalculationResult, "YWbB9+16OW5GSQS5KLw4ThFOoro=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"]
    ];
});
_c = MaschiMurariReinforcementFlexuralCalculationResult;
var _c;
__turbopack_context__.k.register(_c, "MaschiMurariReinforcementFlexuralCalculationResult");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "MaschiMurariFlexuralCalculation",
    ()=>MaschiMurariFlexuralCalculation
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$product$2f$custom$2d$product$2d$section$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/product/custom-product-section.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$product$2f$product$2d$summary$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/product/product-summary-card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/select-form-fixed-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/select-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/separator.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$module$2d$calculation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-module-calculation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$products$2f$use$2d$products$2d$by$2d$category$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/query/products/use-products-by-category.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$maschi$2d$murari$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/masonry/maschi-murari-form.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$maschi$2d$murari$2f$calculations$2f$flexural$2f$maschi$2d$murari$2d$reinforcement$2d$flexural$2d$calculation$2d$result$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation-result.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
// N.B: this function is temporary, until we remove the quotes in future seed
function getCleanedUpValue(recordToClean, nature) {
    // nature should be 'BRICK', 'TUFF', or 'STONE'
    if (!recordToClean) {
        return undefined;
    }
    const key = '"'.concat(nature, '"') // matches the API key format
    ;
    return recordToClean[key];
}
const MaschiMurariFlexuralCalculation = (param)=>{
    let { session, module, projectId } = param;
    var _params_materialProperties, _params_materialProperties1, _params_panelGeometry, _params_panelGeometry1, _params_materialProperties2, _params_materialProperties3, _params_materialProperties4, _params_actions;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.calculations.maschi-murari.flexural');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.calculations.maschi-murari');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const { params, flexuralReinforcementVerifyExecutionInput, flexuralReinforcementCalculationResult } = module;
    var _flexuralReinforcementVerifyExecutionInput_sectionFailureMode, _flexuralReinforcementVerifyExecutionInput_designReinforcementStress, _flexuralReinforcementVerifyExecutionInput_designReinforcementStrain, _flexuralReinforcementVerifyExecutionInput_reinforcedArrangement, _flexuralReinforcementVerifyExecutionInput_singleStripWidth, _flexuralReinforcementVerifyExecutionInput_stripSpacing, _flexuralReinforcementVerifyExecutionInput_reinforcementTotalWidthAlongLength, _flexuralReinforcementVerifyExecutionInput_compressedEdgeReinforcementFiberDistance, _flexuralReinforcementVerifyExecutionInput_layersNumber, _flexuralReinforcementVerifyExecutionInput_reinforcedSidesNumber, _flexuralReinforcementVerifyExecutionInput_totalEquivalentThickness, _flexuralReinforcementVerifyExecutionInput_firstCoefficient, _flexuralReinforcementVerifyExecutionInput_secondCoefficient, _flexuralReinforcementVerifyExecutionInput_outOfPlanUnitWidthPanel, _flexuralReinforcementVerifyExecutionInput_outOfPlanAppliedDesignAxialStress, _flexuralReinforcementVerifyExecutionInput_outOfPlaneReinforcementFailureMode, _flexuralReinforcementVerifyExecutionInput_outOfPlaneDesignReinforcementStrain, _flexuralReinforcementVerifyExecutionInput_outOfPlaneDesignReinforcementStress, _flexuralReinforcementVerifyExecutionInput_outOfPlaneReinforcementTotalWidthAlongLength, _flexuralReinforcementVerifyExecutionInput_outOfPlaneResistingArea;
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$maschi$2d$murari$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["maschiMurariFlexuralReinforcementExecutionSchema"]),
        defaultValues: {
            calculationType: 'FLEXURAL_VERIFY',
            input: {
                product: {
                    id: flexuralReinforcementVerifyExecutionInput === null || flexuralReinforcementVerifyExecutionInput === void 0 ? void 0 : flexuralReinforcementVerifyExecutionInput.product.id,
                    name: flexuralReinforcementVerifyExecutionInput === null || flexuralReinforcementVerifyExecutionInput === void 0 ? void 0 : flexuralReinforcementVerifyExecutionInput.product.name,
                    sourceType: (flexuralReinforcementVerifyExecutionInput === null || flexuralReinforcementVerifyExecutionInput === void 0 ? void 0 : flexuralReinforcementVerifyExecutionInput.product.id) === 'custom' ? 'CUSTOM' : 'DATABASE'
                },
                sectionFailureMode: (_flexuralReinforcementVerifyExecutionInput_sectionFailureMode = flexuralReinforcementVerifyExecutionInput === null || flexuralReinforcementVerifyExecutionInput === void 0 ? void 0 : flexuralReinforcementVerifyExecutionInput.sectionFailureMode) !== null && _flexuralReinforcementVerifyExecutionInput_sectionFailureMode !== void 0 ? _flexuralReinforcementVerifyExecutionInput_sectionFailureMode : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REINFORCEMENT_FAILURE_MODE"].DISTACCO_DI_ESTREMITA,
                designReinforcementStress: (_flexuralReinforcementVerifyExecutionInput_designReinforcementStress = flexuralReinforcementVerifyExecutionInput === null || flexuralReinforcementVerifyExecutionInput === void 0 ? void 0 : flexuralReinforcementVerifyExecutionInput.designReinforcementStress) !== null && _flexuralReinforcementVerifyExecutionInput_designReinforcementStress !== void 0 ? _flexuralReinforcementVerifyExecutionInput_designReinforcementStress : 0,
                designReinforcementStrain: (_flexuralReinforcementVerifyExecutionInput_designReinforcementStrain = flexuralReinforcementVerifyExecutionInput === null || flexuralReinforcementVerifyExecutionInput === void 0 ? void 0 : flexuralReinforcementVerifyExecutionInput.designReinforcementStrain) !== null && _flexuralReinforcementVerifyExecutionInput_designReinforcementStrain !== void 0 ? _flexuralReinforcementVerifyExecutionInput_designReinforcementStrain : 0,
                reinforcedArrangement: (_flexuralReinforcementVerifyExecutionInput_reinforcedArrangement = flexuralReinforcementVerifyExecutionInput === null || flexuralReinforcementVerifyExecutionInput === void 0 ? void 0 : flexuralReinforcementVerifyExecutionInput.reinforcedArrangement) !== null && _flexuralReinforcementVerifyExecutionInput_reinforcedArrangement !== void 0 ? _flexuralReinforcementVerifyExecutionInput_reinforcedArrangement : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REINFORCEMENT_ARRANGEMENT"].CONTINUE,
                singleStripWidth: (_flexuralReinforcementVerifyExecutionInput_singleStripWidth = flexuralReinforcementVerifyExecutionInput === null || flexuralReinforcementVerifyExecutionInput === void 0 ? void 0 : flexuralReinforcementVerifyExecutionInput.singleStripWidth) !== null && _flexuralReinforcementVerifyExecutionInput_singleStripWidth !== void 0 ? _flexuralReinforcementVerifyExecutionInput_singleStripWidth : 0,
                stripSpacing: (_flexuralReinforcementVerifyExecutionInput_stripSpacing = flexuralReinforcementVerifyExecutionInput === null || flexuralReinforcementVerifyExecutionInput === void 0 ? void 0 : flexuralReinforcementVerifyExecutionInput.stripSpacing) !== null && _flexuralReinforcementVerifyExecutionInput_stripSpacing !== void 0 ? _flexuralReinforcementVerifyExecutionInput_stripSpacing : 0,
                reinforcementTotalWidthAlongLength: (_flexuralReinforcementVerifyExecutionInput_reinforcementTotalWidthAlongLength = flexuralReinforcementVerifyExecutionInput === null || flexuralReinforcementVerifyExecutionInput === void 0 ? void 0 : flexuralReinforcementVerifyExecutionInput.reinforcementTotalWidthAlongLength) !== null && _flexuralReinforcementVerifyExecutionInput_reinforcementTotalWidthAlongLength !== void 0 ? _flexuralReinforcementVerifyExecutionInput_reinforcementTotalWidthAlongLength : 0,
                compressedEdgeReinforcementFiberDistance: (_flexuralReinforcementVerifyExecutionInput_compressedEdgeReinforcementFiberDistance = flexuralReinforcementVerifyExecutionInput === null || flexuralReinforcementVerifyExecutionInput === void 0 ? void 0 : flexuralReinforcementVerifyExecutionInput.compressedEdgeReinforcementFiberDistance) !== null && _flexuralReinforcementVerifyExecutionInput_compressedEdgeReinforcementFiberDistance !== void 0 ? _flexuralReinforcementVerifyExecutionInput_compressedEdgeReinforcementFiberDistance : 0,
                layersNumber: (_flexuralReinforcementVerifyExecutionInput_layersNumber = flexuralReinforcementVerifyExecutionInput === null || flexuralReinforcementVerifyExecutionInput === void 0 ? void 0 : flexuralReinforcementVerifyExecutionInput.layersNumber) !== null && _flexuralReinforcementVerifyExecutionInput_layersNumber !== void 0 ? _flexuralReinforcementVerifyExecutionInput_layersNumber : 1,
                reinforcedSidesNumber: (_flexuralReinforcementVerifyExecutionInput_reinforcedSidesNumber = flexuralReinforcementVerifyExecutionInput === null || flexuralReinforcementVerifyExecutionInput === void 0 ? void 0 : flexuralReinforcementVerifyExecutionInput.reinforcedSidesNumber) !== null && _flexuralReinforcementVerifyExecutionInput_reinforcedSidesNumber !== void 0 ? _flexuralReinforcementVerifyExecutionInput_reinforcedSidesNumber : 0,
                totalEquivalentThickness: (_flexuralReinforcementVerifyExecutionInput_totalEquivalentThickness = flexuralReinforcementVerifyExecutionInput === null || flexuralReinforcementVerifyExecutionInput === void 0 ? void 0 : flexuralReinforcementVerifyExecutionInput.totalEquivalentThickness) !== null && _flexuralReinforcementVerifyExecutionInput_totalEquivalentThickness !== void 0 ? _flexuralReinforcementVerifyExecutionInput_totalEquivalentThickness : 0,
                firstCoefficient: (_flexuralReinforcementVerifyExecutionInput_firstCoefficient = flexuralReinforcementVerifyExecutionInput === null || flexuralReinforcementVerifyExecutionInput === void 0 ? void 0 : flexuralReinforcementVerifyExecutionInput.firstCoefficient) !== null && _flexuralReinforcementVerifyExecutionInput_firstCoefficient !== void 0 ? _flexuralReinforcementVerifyExecutionInput_firstCoefficient : 0,
                secondCoefficient: (_flexuralReinforcementVerifyExecutionInput_secondCoefficient = flexuralReinforcementVerifyExecutionInput === null || flexuralReinforcementVerifyExecutionInput === void 0 ? void 0 : flexuralReinforcementVerifyExecutionInput.secondCoefficient) !== null && _flexuralReinforcementVerifyExecutionInput_secondCoefficient !== void 0 ? _flexuralReinforcementVerifyExecutionInput_secondCoefficient : 0,
                // Only for out-of-plane bending
                outOfPlanUnitWidthPanel: (_flexuralReinforcementVerifyExecutionInput_outOfPlanUnitWidthPanel = flexuralReinforcementVerifyExecutionInput === null || flexuralReinforcementVerifyExecutionInput === void 0 ? void 0 : flexuralReinforcementVerifyExecutionInput.outOfPlanUnitWidthPanel) !== null && _flexuralReinforcementVerifyExecutionInput_outOfPlanUnitWidthPanel !== void 0 ? _flexuralReinforcementVerifyExecutionInput_outOfPlanUnitWidthPanel : 1000,
                outOfPlanAppliedDesignAxialStress: (_flexuralReinforcementVerifyExecutionInput_outOfPlanAppliedDesignAxialStress = flexuralReinforcementVerifyExecutionInput === null || flexuralReinforcementVerifyExecutionInput === void 0 ? void 0 : flexuralReinforcementVerifyExecutionInput.outOfPlanAppliedDesignAxialStress) !== null && _flexuralReinforcementVerifyExecutionInput_outOfPlanAppliedDesignAxialStress !== void 0 ? _flexuralReinforcementVerifyExecutionInput_outOfPlanAppliedDesignAxialStress : 0,
                outOfPlaneReinforcementFailureMode: (_flexuralReinforcementVerifyExecutionInput_outOfPlaneReinforcementFailureMode = flexuralReinforcementVerifyExecutionInput === null || flexuralReinforcementVerifyExecutionInput === void 0 ? void 0 : flexuralReinforcementVerifyExecutionInput.outOfPlaneReinforcementFailureMode) !== null && _flexuralReinforcementVerifyExecutionInput_outOfPlaneReinforcementFailureMode !== void 0 ? _flexuralReinforcementVerifyExecutionInput_outOfPlaneReinforcementFailureMode : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REINFORCEMENT_FAILURE_MODE"].DISTACCO_DI_ESTREMITA,
                outOfPlaneDesignReinforcementStrain: (_flexuralReinforcementVerifyExecutionInput_outOfPlaneDesignReinforcementStrain = flexuralReinforcementVerifyExecutionInput === null || flexuralReinforcementVerifyExecutionInput === void 0 ? void 0 : flexuralReinforcementVerifyExecutionInput.outOfPlaneDesignReinforcementStrain) !== null && _flexuralReinforcementVerifyExecutionInput_outOfPlaneDesignReinforcementStrain !== void 0 ? _flexuralReinforcementVerifyExecutionInput_outOfPlaneDesignReinforcementStrain : 0,
                outOfPlaneDesignReinforcementStress: (_flexuralReinforcementVerifyExecutionInput_outOfPlaneDesignReinforcementStress = flexuralReinforcementVerifyExecutionInput === null || flexuralReinforcementVerifyExecutionInput === void 0 ? void 0 : flexuralReinforcementVerifyExecutionInput.outOfPlaneDesignReinforcementStress) !== null && _flexuralReinforcementVerifyExecutionInput_outOfPlaneDesignReinforcementStress !== void 0 ? _flexuralReinforcementVerifyExecutionInput_outOfPlaneDesignReinforcementStress : 0,
                outOfPlaneReinforcementTotalWidthAlongLength: (_flexuralReinforcementVerifyExecutionInput_outOfPlaneReinforcementTotalWidthAlongLength = flexuralReinforcementVerifyExecutionInput === null || flexuralReinforcementVerifyExecutionInput === void 0 ? void 0 : flexuralReinforcementVerifyExecutionInput.outOfPlaneReinforcementTotalWidthAlongLength) !== null && _flexuralReinforcementVerifyExecutionInput_outOfPlaneReinforcementTotalWidthAlongLength !== void 0 ? _flexuralReinforcementVerifyExecutionInput_outOfPlaneReinforcementTotalWidthAlongLength : 0,
                outOfPlaneResistingArea: (_flexuralReinforcementVerifyExecutionInput_outOfPlaneResistingArea = flexuralReinforcementVerifyExecutionInput === null || flexuralReinforcementVerifyExecutionInput === void 0 ? void 0 : flexuralReinforcementVerifyExecutionInput.outOfPlaneResistingArea) !== null && _flexuralReinforcementVerifyExecutionInput_outOfPlaneResistingArea !== void 0 ? _flexuralReinforcementVerifyExecutionInput_outOfPlaneResistingArea : 0
            }
        }
    });
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$module$2d$calculation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useModuleCalculation"])(session.accessToken, {
        onSuccess: {
            "MaschiMurariFlexuralCalculation.useModuleCalculation": ()=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(tAction('calculate.success'));
            }
        }["MaschiMurariFlexuralCalculation.useModuleCalculation"],
        onError: {
            "MaschiMurariFlexuralCalculation.useModuleCalculation": (error)=>{
                console.log('ERROR  ', error);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(tAction('calculate.failure', {
                    error: error.message
                }));
            }
        }["MaschiMurariFlexuralCalculation.useModuleCalculation"]
    });
    const handleFormSubmit = (body)=>{
        mutate({
            projectId,
            moduleId: module.id,
            body
        });
    };
    const { data: products, isError: errorGettingProducts, isLoading: isLoadingProducts } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$products$2f$use$2d$products$2d$by$2d$category$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useProductsByCategory"])(session, 'MASCHI_MURARI', 0, 100);
    var _products_content_map;
    const productsOptions = [
        ...(_products_content_map = products === null || products === void 0 ? void 0 : products.content.map((m)=>{
            var _m_name;
            return {
                value: m.id,
                label: (_m_name = m.name) !== null && _m_name !== void 0 ? _m_name : t('product.unnamed')
            };
        })) !== null && _products_content_map !== void 0 ? _products_content_map : []
    ];
    const [productId] = form.watch([
        'input.product.id'
    ]);
    const selectedProduct = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "MaschiMurariFlexuralCalculation.useMemo[selectedProduct]": ()=>products === null || products === void 0 ? void 0 : products.content.find({
                "MaschiMurariFlexuralCalculation.useMemo[selectedProduct]": (p)=>p.id === productId
            }["MaschiMurariFlexuralCalculation.useMemo[selectedProduct]"])
    }["MaschiMurariFlexuralCalculation.useMemo[selectedProduct]"], [
        productId,
        products
    ]);
    const conventionalStrainLimit = selectedProduct === null || selectedProduct === void 0 ? void 0 : selectedProduct.conventionalStrainLimit;
    var _params_materialProperties_conversionFactor;
    const conversionFactor = (_params_materialProperties_conversionFactor = params === null || params === void 0 ? void 0 : (_params_materialProperties = params.materialProperties) === null || _params_materialProperties === void 0 ? void 0 : _params_materialProperties.conversionFactor) !== null && _params_materialProperties_conversionFactor !== void 0 ? _params_materialProperties_conversionFactor : 0;
    var _form_watch;
    const sectionFailureModeValue = (_form_watch = form.watch('input.sectionFailureMode')) !== null && _form_watch !== void 0 ? _form_watch : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REINFORCEMENT_FAILURE_MODE"].DISTACCO_DI_ESTREMITA;
    var _params_materialProperties_structuralElementsNature;
    const structuralElementsNatureValue = (_params_materialProperties_structuralElementsNature = params === null || params === void 0 ? void 0 : (_params_materialProperties1 = params.materialProperties) === null || _params_materialProperties1 === void 0 ? void 0 : _params_materialProperties1.structuralElementsNature) !== null && _params_materialProperties_structuralElementsNature !== void 0 ? _params_materialProperties_structuralElementsNature : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FACING_MATERIAL"].BRICK;
    var _params_panelGeometry_height;
    const _panelGeometryHeight = (_params_panelGeometry_height = params === null || params === void 0 ? void 0 : (_params_panelGeometry = params.panelGeometry) === null || _params_panelGeometry === void 0 ? void 0 : _params_panelGeometry.height) !== null && _params_panelGeometry_height !== void 0 ? _params_panelGeometry_height : 0;
    var _params_panelGeometry_width;
    const panelGeometryWidth = (_params_panelGeometry_width = params === null || params === void 0 ? void 0 : (_params_panelGeometry1 = params.panelGeometry) === null || _params_panelGeometry1 === void 0 ? void 0 : _params_panelGeometry1.width) !== null && _params_panelGeometry_width !== void 0 ? _params_panelGeometry_width : 0;
    var _form_watch1;
    const singleStripWidthValue = (_form_watch1 = form.watch('input.singleStripWidth')) !== null && _form_watch1 !== void 0 ? _form_watch1 : 0;
    var _form_watch2;
    const stripSpacingValue = (_form_watch2 = form.watch('input.stripSpacing')) !== null && _form_watch2 !== void 0 ? _form_watch2 : 0;
    var _params_materialProperties_conversionFactor1;
    const conversionFactorValue = (_params_materialProperties_conversionFactor1 = params === null || params === void 0 ? void 0 : (_params_materialProperties2 = params.materialProperties) === null || _params_materialProperties2 === void 0 ? void 0 : _params_materialProperties2.conversionFactor) !== null && _params_materialProperties_conversionFactor1 !== void 0 ? _params_materialProperties_conversionFactor1 : 0;
    var _params_materialProperties_ultimateCompressiveStrainLinearBehaviour;
    const ultimateCompressiveStrainLinearBehaviourValue = (_params_materialProperties_ultimateCompressiveStrainLinearBehaviour = params === null || params === void 0 ? void 0 : (_params_materialProperties3 = params.materialProperties) === null || _params_materialProperties3 === void 0 ? void 0 : _params_materialProperties3.ultimateCompressiveStrainLinearBehaviour) !== null && _params_materialProperties_ultimateCompressiveStrainLinearBehaviour !== void 0 ? _params_materialProperties_ultimateCompressiveStrainLinearBehaviour : 0;
    var _params_materialProperties_ultimateCompressiveStrain;
    const ultimateCompressiveStrainValue = (_params_materialProperties_ultimateCompressiveStrain = params === null || params === void 0 ? void 0 : (_params_materialProperties4 = params.materialProperties) === null || _params_materialProperties4 === void 0 ? void 0 : _params_materialProperties4.ultimateCompressiveStrain) !== null && _params_materialProperties_ultimateCompressiveStrain !== void 0 ? _params_materialProperties_ultimateCompressiveStrain : 0;
    var _params_actions_appliedNormalStress;
    const appliedNormalStressValue = (_params_actions_appliedNormalStress = params === null || params === void 0 ? void 0 : (_params_actions = params.actions) === null || _params_actions === void 0 ? void 0 : _params_actions.appliedNormalStress) !== null && _params_actions_appliedNormalStress !== void 0 ? _params_actions_appliedNormalStress : 0;
    var _form_watch3;
    const reinforcedArrangementValue = (_form_watch3 = form.watch('input.reinforcedArrangement')) !== null && _form_watch3 !== void 0 ? _form_watch3 : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REINFORCEMENT_ARRANGEMENT"].CONTINUE;
    var _form_watch4;
    const reinforcedSidesNumberValue = (_form_watch4 = form.watch('input.reinforcedSidesNumber')) !== null && _form_watch4 !== void 0 ? _form_watch4 : 0;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MaschiMurariFlexuralCalculation.useEffect": ()=>{
            form.setValue('input.layersNumber', 1);
            form.setValue('input.outOfPlanUnitWidthPanel', 1000);
            form.setValue('input.outOfPlaneReinforcementFailureMode', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REINFORCEMENT_FAILURE_MODE"].DISTACCO_INTERMEDIO);
            var _getCleanedUpValue;
            // D61 = designReinforcementStrain =
            // =IF(D42="Distacco intermedio",(1.5*D41*D26/1.5),(D41*D26/1.5))
            // D42 = input.reinforcementFailureMode
            // D41 = product.conventionalStrainLimit
            // D26 = materialProperties.conversionFactor
            const conventionalStrainLimitValue = (_getCleanedUpValue = getCleanedUpValue(conventionalStrainLimit, structuralElementsNatureValue)) !== null && _getCleanedUpValue !== void 0 ? _getCleanedUpValue : 0;
            const designReinforcementStrain = sectionFailureModeValue === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REINFORCEMENT_FAILURE_MODE"].DISTACCO_INTERMEDIO ? 1.5 * conventionalStrainLimitValue * conversionFactor / 1.5 : conventionalStrainLimitValue * conversionFactor / 1.5;
            form.setValue('input.designReinforcementStrain', designReinforcementStrain);
            var _selectedProduct_elasticModulus;
            // D62 = designReinforcementStress = =D43*D38
            // D43 = designReinforcementStrain
            // D38 = product.elasticModulus
            const elasticModulus = (_selectedProduct_elasticModulus = selectedProduct === null || selectedProduct === void 0 ? void 0 : selectedProduct.elasticModulus) !== null && _selectedProduct_elasticModulus !== void 0 ? _selectedProduct_elasticModulus : 0;
            const designReinforcementStress = designReinforcementStrain * elasticModulus;
            form.setValue('input.designReinforcementStress', designReinforcementStress);
            // D66 reinforcementTotalWidthAlongLength =
            //  =IF(D63="Continuo",D9,D64*(D9/D65))
            // D63 = input.reinforcedArrangement
            // D64 = input.singleStripWidth
            // D65 = input.stripSpacing
            // D9 = width
            const reinforcementTotalWidthAlongLength = reinforcedArrangementValue === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REINFORCEMENT_ARRANGEMENT"].CONTINUE ? panelGeometryWidth : singleStripWidthValue * (panelGeometryWidth / stripSpacingValue);
            form.setValue('input.reinforcementTotalWidthAlongLength', reinforcementTotalWidthAlongLength);
            var _selectedProduct_thickness;
            // totalEquivalentThickness
            // =IF(D63="Continuo",D69*D57,D69*D57*D64/D65)
            // D69 = reinforcedSidesNumber
            // D64 = input.singleStripWidth
            // D65 = input.stripSpacing
            // D57 = thickness of the product
            const thicknessOfTheProduct = (_selectedProduct_thickness = selectedProduct === null || selectedProduct === void 0 ? void 0 : selectedProduct.thickness) !== null && _selectedProduct_thickness !== void 0 ? _selectedProduct_thickness : 0;
            const totalEquivalentThickness = reinforcedArrangementValue === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REINFORCEMENT_ARRANGEMENT"].CONTINUE ? reinforcedSidesNumberValue * singleStripWidthValue : reinforcedSidesNumberValue * thicknessOfTheProduct * singleStripWidthValue / stripSpacingValue;
            form.setValue('input.totalEquivalentThickness', totalEquivalentThickness);
            // first Coefficient = D24/D25
            // D24: materialProperties.ultimateCompressiveStrainLinearBehaviour
            // D25: materialProperties.ultimateCompressiveStrain
            const firstCoefficient = ultimateCompressiveStrainLinearBehaviourValue / ultimateCompressiveStrainValue;
            form.setValue('input.firstCoefficient', firstCoefficient);
            // second Coefficient = D24/D61
            // D61 = designReinforcementStrain
            const secondCoefficient = ultimateCompressiveStrainLinearBehaviourValue / designReinforcementStrain;
            form.setValue('input.secondCoefficient', secondCoefficient);
            // OUT OF PLANE BENDING
            // D79 = outOfPlanAppliedDesignAxialStress=D29
            // D29 = actions.appliedNormalStress
            form.setValue('input.outOfPlanAppliedDesignAxialStress', appliedNormalStressValue);
            // D81 = outOfPlaneDesignReinforcementStrain
            // =  =(1.5*D59*D26/1.5)
            // D59 = product.conventionalStrainLimit
            // D26 = materialProperties.conversionFactor
            const outOfPlaneDesignReinforcementStrain = 1.5 * conventionalStrainLimitValue * conversionFactorValue / 1.5;
            form.setValue('input.outOfPlaneDesignReinforcementStrain', outOfPlaneDesignReinforcementStrain);
            // D82= outOfPlaneDesignReinforcementStrain=D81*D56
            // D56 = product.elasticModulus
            form.setValue('input.outOfPlaneDesignReinforcementStress', outOfPlaneDesignReinforcementStrain * elasticModulus);
            // D83 = outOfPlaneReinforcementTotalWidthAlongLength
            // =IF(D63="Continuo",D78,D64*(D78/D65))
            // D78 = width = outOfPlanUnitWidthPanel = 1000
            // D63 = input.reinforcedArrangement
            // D64 = input.singleStripWidth
            // D65 = input.stripSpacing
            const outOfPlaneReinforcementTotalWidthAlongLength = reinforcedArrangementValue === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REINFORCEMENT_ARRANGEMENT"].CONTINUE ? 1000 : singleStripWidthValue * (1000 / stripSpacingValue);
            form.setValue('input.outOfPlaneReinforcementTotalWidthAlongLength', outOfPlaneReinforcementTotalWidthAlongLength);
            // D85 = outOfPlaneResistingArea
            // =D57*D68*D83
            // D57 = thickness of the product
            // D68 = reinforcedLayersNumber = 1
            form.setValue('input.outOfPlaneResistingArea', thicknessOfTheProduct * 1 * outOfPlaneReinforcementTotalWidthAlongLength);
            if (!productId) {
                return;
            }
            if (productId === 'custom') {
                form.setValue('input.product.sourceType', 'CUSTOM');
            }
            if (selectedProduct) {
                form.setValue('input.product', {
                    ...selectedProduct,
                    sourceType: 'DATABASE'
                });
            }
        }
    }["MaschiMurariFlexuralCalculation.useEffect"], [
        form,
        productId,
        selectedProduct,
        conversionFactor,
        sectionFailureModeValue,
        conventionalStrainLimit,
        structuralElementsNatureValue,
        panelGeometryWidth,
        singleStripWidthValue,
        stripSpacingValue,
        reinforcedArrangementValue,
        reinforcedSidesNumberValue,
        ultimateCompressiveStrainLinearBehaviourValue,
        ultimateCompressiveStrainValue,
        appliedNormalStressValue,
        conversionFactorValue
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col justify-center gap-4",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Form"], {
                ...form,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                    className: "space-y-4 rounded-md border p-4",
                    onSubmit: form.handleSubmit(handleFormSubmit),
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-3xl font-bold",
                            children: t('heading')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 376,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            src: "/assets/masonry_frcm/column/FRCM_COLONNE MURATURA.jpg",
                            alt: "flexural verify",
                            height: 250,
                            width: 500,
                            className: "mx-auto rounded-md object-contain",
                            priority: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 377,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-xl font-bold",
                            children: t('inPlane.title')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 385,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormInput"], {
                            control: form.control,
                            name: "input.product.id",
                            options: productsOptions,
                            t: t,
                            loading: isLoadingProducts,
                            requestError: errorGettingProducts,
                            errorMessage: t('products.error')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 386,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        productId === 'custom' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$product$2f$custom$2d$product$2d$section$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CustomProductSection"], {}, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 395,
                            columnNumber: 38
                        }, ("TURBOPACK compile-time value", void 0)),
                        selectedProduct && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$product$2f$product$2d$summary$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ProductSummaryCard"], {
                            product: selectedProduct
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 396,
                            columnNumber: 31
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 397,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                            control: form.control,
                            name: "input.sectionFailureMode",
                            options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_REINFORCEMENT_FAILURE_MODE"],
                            optionLabelFn: (p)=>t("input.sectionFailureMode.".concat(p)),
                            t: t
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 398,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.designReinforcementStrain",
                            t: t,
                            disabled: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 405,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.designReinforcementStress",
                            t: t,
                            disabled: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 411,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                            control: form.control,
                            name: "input.reinforcedArrangement",
                            options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_REINFORCEMENT_ARRANGEMENT"],
                            optionLabelFn: (p)=>t("input.reinforcedArrangement.".concat(p)),
                            t: t
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 417,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.singleStripWidth",
                            t: t
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 424,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.stripSpacing",
                            t: t
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 429,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.reinforcementTotalWidthAlongLength",
                            t: t,
                            disabled: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 434,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.compressedEdgeReinforcementFiberDistance",
                            t: t
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 440,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.layersNumber",
                            t: t,
                            disabled: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 445,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.reinforcedSidesNumber",
                            t: t
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 451,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.totalEquivalentThickness",
                            t: t,
                            disabled: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 457,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.firstCoefficient",
                            t: t,
                            disabled: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 463,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.secondCoefficient",
                            t: t,
                            disabled: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 469,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 475,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-xl font-bold",
                            children: t('outOfPlane.title')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 476,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.outOfPlanUnitWidthPanel",
                            t: t,
                            disabled: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 477,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.outOfPlanAppliedDesignAxialStress",
                            t: t,
                            disabled: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 483,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                            control: form.control,
                            name: "input.outOfPlaneReinforcementFailureMode",
                            options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_REINFORCEMENT_FAILURE_MODE"],
                            optionLabelFn: (p)=>t("input.outOfPlaneReinforcementFailureMode.".concat(p)),
                            t: t,
                            disabled: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 489,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.outOfPlaneDesignReinforcementStrain",
                            t: t,
                            disabled: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 499,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.outOfPlaneDesignReinforcementStress",
                            t: t,
                            disabled: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 505,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.outOfPlaneReinforcementTotalWidthAlongLength",
                            t: t,
                            disabled: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 512,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.outOfPlaneResistingArea",
                            t: t,
                            disabled: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 518,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                            type: "submit",
                            className: "w-full sm:w-auto",
                            disabled: isPending,
                            children: [
                                isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                                    className: "animate-spin"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                                    lineNumber: 529,
                                    columnNumber: 27
                                }, ("TURBOPACK compile-time value", void 0)),
                                tCommon('calculate')
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                            lineNumber: 524,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                    lineNumber: 372,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                lineNumber: 371,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            flexuralReinforcementCalculationResult && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$maschi$2d$murari$2f$calculations$2f$flexural$2f$maschi$2d$murari$2d$reinforcement$2d$flexural$2d$calculation$2d$result$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MaschiMurariReinforcementFlexuralCalculationResult"], {
                flexuralReinforcementCalculationResult: flexuralReinforcementCalculationResult
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
                lineNumber: 535,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx",
        lineNumber: 370,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(MaschiMurariFlexuralCalculation, "PASt7Pj3XMYeJU6OPHzcN/Djfww=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$module$2d$calculation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useModuleCalculation"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$products$2f$use$2d$products$2d$by$2d$category$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useProductsByCategory"]
    ];
});
_c = MaschiMurariFlexuralCalculation;
var _c;
__turbopack_context__.k.register(_c, "MaschiMurariFlexuralCalculation");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-nonreinforced-section-result-card.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "ShearNonReinforcedSectionResultCard",
    ()=>ShearNonReinforcedSectionResultCard
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/use-intl/dist/esm/development/react.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
function ShearNonReinforcedSectionResultCard(param) {
    let { nonReinforcedSectionShearResults } = param;
    _s();
    const { inPlaneAppliedShear, verticalStress, correctionFactorBasedOnWallSlenderness, shearResistanceNotReinforcedMasonry, check } = nonReinforcedSectionShearResults || {};
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('components.calculations.maschi-murari.shearReinforcementCalculationResult.nonReinforcedSectionResult');
    const locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                    children: t('title')
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-nonreinforced-section-result-card.tsx",
                    lineNumber: 36,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-nonreinforced-section-result-card.tsx",
                lineNumber: 35,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                className: "space-y-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('inPlaneAppliedShear.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-nonreinforced-section-result-card.tsx",
                                lineNumber: 40,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    inPlaneAppliedShear === null || inPlaneAppliedShear === void 0 ? void 0 : inPlaneAppliedShear.toLocaleString(locale, {
                                        maximumFractionDigits: 3
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-nonreinforced-section-result-card.tsx",
                                lineNumber: 41,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-nonreinforced-section-result-card.tsx",
                        lineNumber: 39,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('verticalStress.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-nonreinforced-section-result-card.tsx",
                                lineNumber: 48,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    verticalStress === null || verticalStress === void 0 ? void 0 : verticalStress.toLocaleString(locale, {
                                        maximumFractionDigits: 3
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-nonreinforced-section-result-card.tsx",
                                lineNumber: 49,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-nonreinforced-section-result-card.tsx",
                        lineNumber: 47,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('correctionFactorBasedOnWallSlenderness.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-nonreinforced-section-result-card.tsx",
                                lineNumber: 56,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    correctionFactorBasedOnWallSlenderness === null || correctionFactorBasedOnWallSlenderness === void 0 ? void 0 : correctionFactorBasedOnWallSlenderness.toLocaleString(locale, {
                                        maximumFractionDigits: 3
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-nonreinforced-section-result-card.tsx",
                                lineNumber: 59,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-nonreinforced-section-result-card.tsx",
                        lineNumber: 55,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('shearResistanceNotReinforcedMasonry.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-nonreinforced-section-result-card.tsx",
                                lineNumber: 66,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    shearResistanceNotReinforcedMasonry === null || shearResistanceNotReinforcedMasonry === void 0 ? void 0 : shearResistanceNotReinforcedMasonry.toLocaleString(locale, {
                                        maximumFractionDigits: 3
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-nonreinforced-section-result-card.tsx",
                                lineNumber: 69,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-nonreinforced-section-result-card.tsx",
                        lineNumber: 65,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "font-medium",
                        children: [
                            t('check.label'),
                            ":"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-nonreinforced-section-result-card.tsx",
                        lineNumber: 75,
                        columnNumber: 9
                    }, this),
                    ' ',
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-base px-3 py-1', check ? 'bg-green-600' : 'bg-red-600'),
                        children: check ? t('check.satisfied') : t('check.notSatisfied')
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-nonreinforced-section-result-card.tsx",
                        lineNumber: 76,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-nonreinforced-section-result-card.tsx",
                lineNumber: 38,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-nonreinforced-section-result-card.tsx",
        lineNumber: 34,
        columnNumber: 5
    }, this);
}
_s(ShearNonReinforcedSectionResultCard, "ibHvjDxkLpek5QWjIfNYywjof5I=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"]
    ];
});
_c = ShearNonReinforcedSectionResultCard;
var _c;
__turbopack_context__.k.register(_c, "ShearNonReinforcedSectionResultCard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-reinforced-section-result-card.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "ShearReinforcedSectionResultCard",
    ()=>ShearReinforcedSectionResultCard
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/use-intl/dist/esm/development/react.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
function ShearReinforcedSectionResultCard(param) {
    let { reinforcedSectionShearResults } = param;
    _s();
    const { shearResistanceReinforcementContribution, totalShearResistance, firstCheck, shearResistanceFromMasonryDiagonalCompression, secondCheck } = reinforcedSectionShearResults || {};
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('components.calculations.maschi-murari.shearReinforcementCalculationResult.reinforcedSectionResult');
    const locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                    children: t('title')
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-reinforced-section-result-card.tsx",
                    lineNumber: 36,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-reinforced-section-result-card.tsx",
                lineNumber: 35,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                className: "space-y-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('shearResistanceReinforcementContribution.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-reinforced-section-result-card.tsx",
                                lineNumber: 40,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    shearResistanceReinforcementContribution === null || shearResistanceReinforcementContribution === void 0 ? void 0 : shearResistanceReinforcementContribution.toLocaleString(locale, {
                                        maximumFractionDigits: 3
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-reinforced-section-result-card.tsx",
                                lineNumber: 43,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-reinforced-section-result-card.tsx",
                        lineNumber: 39,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('totalShearResistance.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-reinforced-section-result-card.tsx",
                                lineNumber: 50,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    totalShearResistance === null || totalShearResistance === void 0 ? void 0 : totalShearResistance.toLocaleString(locale, {
                                        maximumFractionDigits: 3
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-reinforced-section-result-card.tsx",
                                lineNumber: 53,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-reinforced-section-result-card.tsx",
                        lineNumber: 49,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "font-medium",
                        children: [
                            t('firstCheck.label'),
                            ":"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-reinforced-section-result-card.tsx",
                        lineNumber: 59,
                        columnNumber: 9
                    }, this),
                    ' ',
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-base px-3 py-1', firstCheck ? 'bg-green-600' : 'bg-red-600'),
                        children: firstCheck ? t('firstCheck.satisfied') : t('firstCheck.notSatisfied')
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-reinforced-section-result-card.tsx",
                        lineNumber: 60,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('shearResistanceFromMasonryDiagonalCompression.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-reinforced-section-result-card.tsx",
                                lineNumber: 71,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    shearResistanceFromMasonryDiagonalCompression === null || shearResistanceFromMasonryDiagonalCompression === void 0 ? void 0 : shearResistanceFromMasonryDiagonalCompression.toLocaleString(locale, {
                                        maximumFractionDigits: 3
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-reinforced-section-result-card.tsx",
                                lineNumber: 74,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-reinforced-section-result-card.tsx",
                        lineNumber: 70,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "font-medium",
                        children: [
                            t('secondCheck.label'),
                            ":"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-reinforced-section-result-card.tsx",
                        lineNumber: 83,
                        columnNumber: 9
                    }, this),
                    ' ',
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-base px-3 py-1', secondCheck ? 'bg-green-600' : 'bg-red-600'),
                        children: secondCheck ? t('secondCheck.satisfied') : t('secondCheck.notSatisfied')
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-reinforced-section-result-card.tsx",
                        lineNumber: 84,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-reinforced-section-result-card.tsx",
                lineNumber: 38,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-reinforced-section-result-card.tsx",
        lineNumber: 34,
        columnNumber: 5
    }, this);
}
_s(ShearReinforcedSectionResultCard, "ibHvjDxkLpek5QWjIfNYywjof5I=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"]
    ];
});
_c = ShearReinforcedSectionResultCard;
var _c;
__turbopack_context__.k.register(_c, "ShearReinforcedSectionResultCard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation-result.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "MaschiMurariReinforcementShearCalculationResult",
    ()=>MaschiMurariReinforcementShearCalculationResult
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/tabs.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/use-intl/dist/esm/development/react.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$maschi$2d$murari$2f$calculations$2f$shear$2f$maschi$2d$murari$2d$shear$2d$nonreinforced$2d$section$2d$result$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-nonreinforced-section-result-card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$maschi$2d$murari$2f$calculations$2f$shear$2f$maschi$2d$murari$2d$shear$2d$reinforced$2d$section$2d$result$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-reinforced-section-result-card.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
function MaschiMurariReinforcementShearCalculationResult(param) {
    let { shearReinforcementCalculationResult } = param;
    var _shearReinforcementCalculationResult_inPlaneShearCheck, _shearReinforcementCalculationResult_inPlaneShearCheck1;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('components.calculations.maschi-murari.shearReinforcementCalculationResult');
    const _locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                className: "text-lg font-medium py-4",
                children: t('title')
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation-result.tsx",
                lineNumber: 32,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                            children: t('title')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation-result.tsx",
                            lineNumber: 35,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation-result.tsx",
                        lineNumber: 34,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                        className: "space-y-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tabs"], {
                            defaultValue: "nonReinforcedSection",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsList"], {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                            value: "nonReinforcedSection",
                                            children: t('shearReinforcedSectionResult.label')
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation-result.tsx",
                                            lineNumber: 40,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                            value: "reinforcedSection",
                                            children: t('shearReinforcedSectionResult.label')
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation-result.tsx",
                                            lineNumber: 43,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation-result.tsx",
                                    lineNumber: 39,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                                    value: "nonReinforcedSection",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$maschi$2d$murari$2f$calculations$2f$shear$2f$maschi$2d$murari$2d$shear$2d$nonreinforced$2d$section$2d$result$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ShearNonReinforcedSectionResultCard"], {
                                        nonReinforcedSectionShearResults: shearReinforcementCalculationResult === null || shearReinforcementCalculationResult === void 0 ? void 0 : (_shearReinforcementCalculationResult_inPlaneShearCheck = shearReinforcementCalculationResult.inPlaneShearCheck) === null || _shearReinforcementCalculationResult_inPlaneShearCheck === void 0 ? void 0 : _shearReinforcementCalculationResult_inPlaneShearCheck.nonReinforcedSection
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation-result.tsx",
                                        lineNumber: 48,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation-result.tsx",
                                    lineNumber: 47,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                                    value: "reinforcedSection",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$maschi$2d$murari$2f$calculations$2f$shear$2f$maschi$2d$murari$2d$shear$2d$reinforced$2d$section$2d$result$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ShearReinforcedSectionResultCard"], {
                                        reinforcedSectionShearResults: shearReinforcementCalculationResult === null || shearReinforcementCalculationResult === void 0 ? void 0 : (_shearReinforcementCalculationResult_inPlaneShearCheck1 = shearReinforcementCalculationResult.inPlaneShearCheck) === null || _shearReinforcementCalculationResult_inPlaneShearCheck1 === void 0 ? void 0 : _shearReinforcementCalculationResult_inPlaneShearCheck1.reinforcedSection
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation-result.tsx",
                                        lineNumber: 56,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation-result.tsx",
                                    lineNumber: 55,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation-result.tsx",
                            lineNumber: 38,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation-result.tsx",
                        lineNumber: 37,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation-result.tsx",
                lineNumber: 33,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
_s(MaschiMurariReinforcementShearCalculationResult, "YWbB9+16OW5GSQS5KLw4ThFOoro=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"]
    ];
});
_c = MaschiMurariReinforcementShearCalculationResult;
var _c;
__turbopack_context__.k.register(_c, "MaschiMurariReinforcementShearCalculationResult");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "MaschiMurariShearCalculation",
    ()=>MaschiMurariShearCalculation
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$product$2f$custom$2d$product$2d$section$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/product/custom-product-section.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$product$2f$product$2d$summary$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/product/product-summary-card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/select-form-fixed-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/select-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/separator.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$module$2d$calculation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-module-calculation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$products$2f$use$2d$products$2d$by$2d$category$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/query/products/use-products-by-category.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$maschi$2d$murari$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/masonry/maschi-murari-form.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$maschi$2d$murari$2f$calculations$2f$shear$2f$maschi$2d$murari$2d$reinforcement$2d$shear$2d$calculation$2d$result$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation-result.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
// N.B: this function is temporary, until we remove the quotes in future seed
function getCleanedUpValue(recordToClean, nature) {
    // nature should be 'BRICK', 'TUFF', or 'STONE'
    if (!recordToClean) {
        return undefined;
    }
    const key = '"'.concat(nature, '"') // matches the API key format
    ;
    return recordToClean[key];
}
const MaschiMurariShearCalculation = (param)=>{
    let { session, module, projectId, onNext } = param;
    var _params_materialProperties, _params_materialProperties1, _params_panelGeometry, _params_panelGeometry1;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.calculations.maschi-murari.shear');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.calculations.maschi-murari');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const { params, shearReinforcementVerifyExecutionInput, shearReinforcementCalculationResult } = module;
    var _shearReinforcementVerifyExecutionInput_reinforcementFailureMode, _shearReinforcementVerifyExecutionInput_designReinforcementStress, _shearReinforcementVerifyExecutionInput_designReinforcementStrain, _shearReinforcementVerifyExecutionInput_reinforcedArrangement, _shearReinforcementVerifyExecutionInput_singleStripWidth, _shearReinforcementVerifyExecutionInput_stripSpacing, _shearReinforcementVerifyExecutionInput_totalReinforcementWidthPerpendicularShearDirection, _shearReinforcementVerifyExecutionInput_compressedEdgeReinforcementFiberDistance, _shearReinforcementVerifyExecutionInput_layersNumber, _shearReinforcementVerifyExecutionInput_reinforcedSidesNumber;
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$maschi$2d$murari$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["maschiMurariShearReinforcementExecutionSchema"]),
        defaultValues: {
            calculationType: 'SHEAR_VERIFY',
            input: {
                product: {
                    id: shearReinforcementVerifyExecutionInput === null || shearReinforcementVerifyExecutionInput === void 0 ? void 0 : shearReinforcementVerifyExecutionInput.product.id,
                    name: shearReinforcementVerifyExecutionInput === null || shearReinforcementVerifyExecutionInput === void 0 ? void 0 : shearReinforcementVerifyExecutionInput.product.name,
                    sourceType: (shearReinforcementVerifyExecutionInput === null || shearReinforcementVerifyExecutionInput === void 0 ? void 0 : shearReinforcementVerifyExecutionInput.product.id) === 'custom' ? 'CUSTOM' : 'DATABASE'
                },
                reinforcementFailureMode: (_shearReinforcementVerifyExecutionInput_reinforcementFailureMode = shearReinforcementVerifyExecutionInput === null || shearReinforcementVerifyExecutionInput === void 0 ? void 0 : shearReinforcementVerifyExecutionInput.reinforcementFailureMode) !== null && _shearReinforcementVerifyExecutionInput_reinforcementFailureMode !== void 0 ? _shearReinforcementVerifyExecutionInput_reinforcementFailureMode : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REINFORCEMENT_FAILURE_MODE"].DISTACCO_DI_ESTREMITA,
                designReinforcementStress: (_shearReinforcementVerifyExecutionInput_designReinforcementStress = shearReinforcementVerifyExecutionInput === null || shearReinforcementVerifyExecutionInput === void 0 ? void 0 : shearReinforcementVerifyExecutionInput.designReinforcementStress) !== null && _shearReinforcementVerifyExecutionInput_designReinforcementStress !== void 0 ? _shearReinforcementVerifyExecutionInput_designReinforcementStress : 0,
                designReinforcementStrain: (_shearReinforcementVerifyExecutionInput_designReinforcementStrain = shearReinforcementVerifyExecutionInput === null || shearReinforcementVerifyExecutionInput === void 0 ? void 0 : shearReinforcementVerifyExecutionInput.designReinforcementStrain) !== null && _shearReinforcementVerifyExecutionInput_designReinforcementStrain !== void 0 ? _shearReinforcementVerifyExecutionInput_designReinforcementStrain : 0,
                reinforcedArrangement: (_shearReinforcementVerifyExecutionInput_reinforcedArrangement = shearReinforcementVerifyExecutionInput === null || shearReinforcementVerifyExecutionInput === void 0 ? void 0 : shearReinforcementVerifyExecutionInput.reinforcedArrangement) !== null && _shearReinforcementVerifyExecutionInput_reinforcedArrangement !== void 0 ? _shearReinforcementVerifyExecutionInput_reinforcedArrangement : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REINFORCEMENT_ARRANGEMENT"].CONTINUE,
                singleStripWidth: (_shearReinforcementVerifyExecutionInput_singleStripWidth = shearReinforcementVerifyExecutionInput === null || shearReinforcementVerifyExecutionInput === void 0 ? void 0 : shearReinforcementVerifyExecutionInput.singleStripWidth) !== null && _shearReinforcementVerifyExecutionInput_singleStripWidth !== void 0 ? _shearReinforcementVerifyExecutionInput_singleStripWidth : 0,
                stripSpacing: (_shearReinforcementVerifyExecutionInput_stripSpacing = shearReinforcementVerifyExecutionInput === null || shearReinforcementVerifyExecutionInput === void 0 ? void 0 : shearReinforcementVerifyExecutionInput.stripSpacing) !== null && _shearReinforcementVerifyExecutionInput_stripSpacing !== void 0 ? _shearReinforcementVerifyExecutionInput_stripSpacing : 0,
                totalReinforcementWidthPerpendicularShearDirection: (_shearReinforcementVerifyExecutionInput_totalReinforcementWidthPerpendicularShearDirection = shearReinforcementVerifyExecutionInput === null || shearReinforcementVerifyExecutionInput === void 0 ? void 0 : shearReinforcementVerifyExecutionInput.totalReinforcementWidthPerpendicularShearDirection) !== null && _shearReinforcementVerifyExecutionInput_totalReinforcementWidthPerpendicularShearDirection !== void 0 ? _shearReinforcementVerifyExecutionInput_totalReinforcementWidthPerpendicularShearDirection : 0,
                compressedEdgeReinforcementFiberDistance: (_shearReinforcementVerifyExecutionInput_compressedEdgeReinforcementFiberDistance = shearReinforcementVerifyExecutionInput === null || shearReinforcementVerifyExecutionInput === void 0 ? void 0 : shearReinforcementVerifyExecutionInput.compressedEdgeReinforcementFiberDistance) !== null && _shearReinforcementVerifyExecutionInput_compressedEdgeReinforcementFiberDistance !== void 0 ? _shearReinforcementVerifyExecutionInput_compressedEdgeReinforcementFiberDistance : 0,
                layersNumber: (_shearReinforcementVerifyExecutionInput_layersNumber = shearReinforcementVerifyExecutionInput === null || shearReinforcementVerifyExecutionInput === void 0 ? void 0 : shearReinforcementVerifyExecutionInput.layersNumber) !== null && _shearReinforcementVerifyExecutionInput_layersNumber !== void 0 ? _shearReinforcementVerifyExecutionInput_layersNumber : 1,
                reinforcedSidesNumber: (_shearReinforcementVerifyExecutionInput_reinforcedSidesNumber = shearReinforcementVerifyExecutionInput === null || shearReinforcementVerifyExecutionInput === void 0 ? void 0 : shearReinforcementVerifyExecutionInput.reinforcedSidesNumber) !== null && _shearReinforcementVerifyExecutionInput_reinforcedSidesNumber !== void 0 ? _shearReinforcementVerifyExecutionInput_reinforcedSidesNumber : 0
            }
        }
    });
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$module$2d$calculation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useModuleCalculation"])(session.accessToken, {
        onSuccess: {
            "MaschiMurariShearCalculation.useModuleCalculation": ()=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(tAction('calculate.success'));
            }
        }["MaschiMurariShearCalculation.useModuleCalculation"],
        onError: {
            "MaschiMurariShearCalculation.useModuleCalculation": (error)=>{
                console.log('ERROR  ', error);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(tAction('calculate.failure', {
                    error: error.message
                }));
            }
        }["MaschiMurariShearCalculation.useModuleCalculation"]
    });
    const handleFormSubmit = (body)=>{
        mutate({
            projectId,
            moduleId: module.id,
            body
        });
    };
    const { data: products, isError: errorGettingProducts, isLoading: isLoadingProducts } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$products$2f$use$2d$products$2d$by$2d$category$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useProductsByCategory"])(session, 'MASCHI_MURARI', 0, 100);
    var _products_content_map;
    const productsOptions = [
        ...(_products_content_map = products === null || products === void 0 ? void 0 : products.content.map((m)=>{
            var _m_name;
            return {
                value: m.id,
                label: (_m_name = m.name) !== null && _m_name !== void 0 ? _m_name : t('product.unnamed')
            };
        })) !== null && _products_content_map !== void 0 ? _products_content_map : []
    ];
    const [productId] = form.watch([
        'input.product.id'
    ]);
    const selectedProduct = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "MaschiMurariShearCalculation.useMemo[selectedProduct]": ()=>products === null || products === void 0 ? void 0 : products.content.find({
                "MaschiMurariShearCalculation.useMemo[selectedProduct]": (p)=>p.id === productId
            }["MaschiMurariShearCalculation.useMemo[selectedProduct]"])
    }["MaschiMurariShearCalculation.useMemo[selectedProduct]"], [
        productId,
        products
    ]);
    const conventionalStrainLimit = selectedProduct === null || selectedProduct === void 0 ? void 0 : selectedProduct.conventionalStrainLimit;
    var _params_materialProperties_conversionFactor;
    const conversionFactor = (_params_materialProperties_conversionFactor = params === null || params === void 0 ? void 0 : (_params_materialProperties = params.materialProperties) === null || _params_materialProperties === void 0 ? void 0 : _params_materialProperties.conversionFactor) !== null && _params_materialProperties_conversionFactor !== void 0 ? _params_materialProperties_conversionFactor : 0;
    const reinforcementFailureModeValue = form.watch('input.reinforcementFailureMode');
    var _params_materialProperties_structuralElementsNature;
    const structuralElementsNatureValue = (_params_materialProperties_structuralElementsNature = params === null || params === void 0 ? void 0 : (_params_materialProperties1 = params.materialProperties) === null || _params_materialProperties1 === void 0 ? void 0 : _params_materialProperties1.structuralElementsNature) !== null && _params_materialProperties_structuralElementsNature !== void 0 ? _params_materialProperties_structuralElementsNature : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FACING_MATERIAL"].BRICK;
    var _params_panelGeometry_height;
    const panelGeometryHeight = (_params_panelGeometry_height = params === null || params === void 0 ? void 0 : (_params_panelGeometry = params.panelGeometry) === null || _params_panelGeometry === void 0 ? void 0 : _params_panelGeometry.height) !== null && _params_panelGeometry_height !== void 0 ? _params_panelGeometry_height : 0;
    var _params_panelGeometry_width;
    const panelGeometryWidth = (_params_panelGeometry_width = params === null || params === void 0 ? void 0 : (_params_panelGeometry1 = params.panelGeometry) === null || _params_panelGeometry1 === void 0 ? void 0 : _params_panelGeometry1.width) !== null && _params_panelGeometry_width !== void 0 ? _params_panelGeometry_width : 0;
    var _form_watch;
    const singleStripWidthValue = (_form_watch = form.watch('input.singleStripWidth')) !== null && _form_watch !== void 0 ? _form_watch : 0;
    var _form_watch1;
    const stripSpacingValue = (_form_watch1 = form.watch('input.stripSpacing')) !== null && _form_watch1 !== void 0 ? _form_watch1 : 0;
    const reinforcedArrangementValue = form.watch('input.reinforcedArrangement');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MaschiMurariShearCalculation.useEffect": ()=>{
            var _getCleanedUpValue;
            // D43 = designReinforcementStrain =
            // =IF(D42="Distacco intermedio",(1.5*D41*D26/1.5),(D41*D26/1.5))
            // D42 = input.reinforcementFailureMode
            // D41 = product.conventionalStrainLimit
            // D26 = materialProperties.conversionFactor
            const conventionalStrainLimitValue = (_getCleanedUpValue = getCleanedUpValue(conventionalStrainLimit, structuralElementsNatureValue)) !== null && _getCleanedUpValue !== void 0 ? _getCleanedUpValue : 0;
            const designReinforcementStrain = reinforcementFailureModeValue === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REINFORCEMENT_FAILURE_MODE"].DISTACCO_INTERMEDIO ? 1.5 * conventionalStrainLimitValue * conversionFactor / 1.5 : conventionalStrainLimitValue * conversionFactor / 1.5;
            form.setValue('input.designReinforcementStrain', designReinforcementStrain);
            var _selectedProduct_elasticModulus;
            // D62 = designReinforcementStress = =D43*D38
            // D43 = designReinforcementStrain
            // D38 = product.elasticModulus
            const elasticModulus = (_selectedProduct_elasticModulus = selectedProduct === null || selectedProduct === void 0 ? void 0 : selectedProduct.elasticModulus) !== null && _selectedProduct_elasticModulus !== void 0 ? _selectedProduct_elasticModulus : 0;
            const designReinforcementStress = designReinforcementStrain * elasticModulus;
            form.setValue('input.designReinforcementStress', designReinforcementStress);
            // D48 totalReinforcementWidthPerpendicularShearDirection =
            //  =IF(D45="Continuo",MIN(D8,D9),MIN(D46*(D8/D47),D8,D9))
            // D45 = input.reinforcedArrangement
            // D8 = panelGeometry.height
            // D9 = panelGeometry.width
            // D46 = input.singleStripWidth
            // D47 = input.stripSpacing
            const totalReinforcementWidthPerpendicularShearDirection = reinforcedArrangementValue === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REINFORCEMENT_ARRANGEMENT"].CONTINUE ? Math.min(panelGeometryHeight, panelGeometryWidth) : Math.min(singleStripWidthValue * (panelGeometryHeight / stripSpacingValue), panelGeometryHeight, panelGeometryWidth);
            form.setValue('input.totalReinforcementWidthPerpendicularShearDirection', totalReinforcementWidthPerpendicularShearDirection);
            form.setValue('input.layersNumber', 1);
            if (!productId) {
                return;
            }
            if (productId === 'custom') {
                form.setValue('input.product.sourceType', 'CUSTOM');
            }
            if (selectedProduct) {
                form.setValue('input.product', {
                    ...selectedProduct,
                    sourceType: 'DATABASE'
                });
            }
        }
    }["MaschiMurariShearCalculation.useEffect"], [
        form,
        productId,
        selectedProduct,
        conversionFactor,
        reinforcementFailureModeValue,
        conventionalStrainLimit,
        structuralElementsNatureValue,
        panelGeometryHeight,
        panelGeometryWidth,
        singleStripWidthValue,
        stripSpacingValue,
        reinforcedArrangementValue
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col justify-center gap-4",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Form"], {
                ...form,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                    className: "space-y-4 rounded-md border p-4",
                    onSubmit: form.handleSubmit(handleFormSubmit),
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-3xl font-bold",
                            children: t('heading')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx",
                            lineNumber: 248,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            src: "/assets/masonry_frcm/maschi-murari/FRCM_PANNELLI MURARI_Diffuso.jpg",
                            alt: "shear verify",
                            height: 250,
                            width: 500,
                            className: "mx-auto rounded-md object-contain",
                            priority: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx",
                            lineNumber: 249,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-xl font-bold",
                            children: t('sub-heading')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx",
                            lineNumber: 257,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormInput"], {
                            control: form.control,
                            name: "input.product.id",
                            options: productsOptions,
                            t: t,
                            loading: isLoadingProducts,
                            requestError: errorGettingProducts,
                            errorMessage: t('products.error')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx",
                            lineNumber: 258,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        productId === 'custom' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$product$2f$custom$2d$product$2d$section$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CustomProductSection"], {}, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx",
                            lineNumber: 267,
                            columnNumber: 38
                        }, ("TURBOPACK compile-time value", void 0)),
                        selectedProduct && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$product$2f$product$2d$summary$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ProductSummaryCard"], {
                            product: selectedProduct
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx",
                            lineNumber: 268,
                            columnNumber: 31
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx",
                            lineNumber: 269,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                            control: form.control,
                            name: "input.reinforcementFailureMode",
                            options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_REINFORCEMENT_FAILURE_MODE"],
                            optionLabelFn: (p)=>t("input.reinforcementFailureMode.".concat(p)),
                            t: t
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx",
                            lineNumber: 270,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.designReinforcementStrain",
                            t: t,
                            disabled: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx",
                            lineNumber: 277,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.designReinforcementStress",
                            t: t,
                            disabled: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx",
                            lineNumber: 283,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                            control: form.control,
                            name: "input.reinforcedArrangement",
                            options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_REINFORCEMENT_ARRANGEMENT"],
                            optionLabelFn: (p)=>t("input.reinforcedArrangement.".concat(p)),
                            t: t
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx",
                            lineNumber: 289,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.singleStripWidth",
                            t: t
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx",
                            lineNumber: 296,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.stripSpacing",
                            t: t
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx",
                            lineNumber: 301,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.totalReinforcementWidthPerpendicularShearDirection",
                            t: t,
                            disabled: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx",
                            lineNumber: 306,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.layersNumber",
                            t: t,
                            disabled: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx",
                            lineNumber: 312,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.reinforcedSidesNumber",
                            t: t
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx",
                            lineNumber: 318,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.compressedEdgeReinforcementFiberDistance",
                            t: t
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx",
                            lineNumber: 323,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                            type: "submit",
                            className: "w-full sm:w-auto",
                            disabled: isPending,
                            children: [
                                isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                                    className: "animate-spin"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx",
                                    lineNumber: 333,
                                    columnNumber: 27
                                }, ("TURBOPACK compile-time value", void 0)),
                                tCommon('calculate')
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx",
                            lineNumber: 328,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx",
                    lineNumber: 244,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx",
                lineNumber: 243,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            shearReinforcementCalculationResult && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$maschi$2d$murari$2f$calculations$2f$shear$2f$maschi$2d$murari$2d$reinforcement$2d$shear$2d$calculation$2d$result$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MaschiMurariReinforcementShearCalculationResult"], {
                        shearReinforcementCalculationResult: shearReinforcementCalculationResult
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx",
                        lineNumber: 340,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                        type: "submit",
                        className: "w-full sm:w-auto",
                        onClick: onNext,
                        children: tCommon('next')
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx",
                        lineNumber: 345,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx",
        lineNumber: 242,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(MaschiMurariShearCalculation, "PASt7Pj3XMYeJU6OPHzcN/Djfww=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$module$2d$calculation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useModuleCalculation"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$products$2f$use$2d$products$2d$by$2d$category$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useProductsByCategory"]
    ];
});
_c = MaschiMurariShearCalculation;
var _c;
__turbopack_context__.k.register(_c, "MaschiMurariShearCalculation");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-actions-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "MaschiMurariActionsForm",
    ()=>MaschiMurariActionsForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-save-module-params-mutation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$maschi$2d$murari$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/masonry/maschi-murari-form.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
const MaschiMurariActionsForm = (param)=>{
    let { session, projectId, moduleId, defaultValues, params, setParams, onSave } = param;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.maschi-murari.actions');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.save-project-params.messages');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    var _defaultValues_appliedNormalStress, _defaultValues_inPlaneBendingMoment, _defaultValues_outOfPlaneBendingMoment, _defaultValues_inPlaneAppliedShear, _defaultValues_outOfPlaneAppliedShear;
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$maschi$2d$murari$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["maschiMurariActionsSchema"]),
        defaultValues: {
            appliedNormalStress: (_defaultValues_appliedNormalStress = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.appliedNormalStress) !== null && _defaultValues_appliedNormalStress !== void 0 ? _defaultValues_appliedNormalStress : 0,
            inPlaneBendingMoment: (_defaultValues_inPlaneBendingMoment = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.inPlaneBendingMoment) !== null && _defaultValues_inPlaneBendingMoment !== void 0 ? _defaultValues_inPlaneBendingMoment : 0,
            outOfPlaneBendingMoment: (_defaultValues_outOfPlaneBendingMoment = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.outOfPlaneBendingMoment) !== null && _defaultValues_outOfPlaneBendingMoment !== void 0 ? _defaultValues_outOfPlaneBendingMoment : 0,
            inPlaneAppliedShear: (_defaultValues_inPlaneAppliedShear = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.inPlaneAppliedShear) !== null && _defaultValues_inPlaneAppliedShear !== void 0 ? _defaultValues_inPlaneAppliedShear : 0,
            outOfPlaneAppliedShear: (_defaultValues_outOfPlaneAppliedShear = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.outOfPlaneAppliedShear) !== null && _defaultValues_outOfPlaneAppliedShear !== void 0 ? _defaultValues_outOfPlaneAppliedShear : 0
        }
    });
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"])(session.accessToken, {
        onSuccess: {
            "MaschiMurariActionsForm.useSaveModuleParamsMutation": async ()=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(tAction('edit.success'));
                await queryClient.invalidateQueries({
                    queryKey: [
                        'modules',
                        {
                            projectId,
                            moduleId
                        }
                    ]
                });
                onSave();
            }
        }["MaschiMurariActionsForm.useSaveModuleParamsMutation"],
        onError: {
            "MaschiMurariActionsForm.useSaveModuleParamsMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(tAction('edit.failure', {
                    error: error.message
                }));
            }
        }["MaschiMurariActionsForm.useSaveModuleParamsMutation"]
    });
    const handleFormSubmit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "MaschiMurariActionsForm.useCallback[handleFormSubmit]": (body)=>{
            // construct body based on global schema:
            const maschiMurariParams = {
                ...params,
                actions: body
            };
            mutate({
                projectId,
                moduleId,
                body: maschiMurariParams
            });
        }
    }["MaschiMurariActionsForm.useCallback[handleFormSubmit]"], [
        mutate,
        projectId,
        moduleId,
        params
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MaschiMurariActionsForm.useEffect": ()=>{
            const subscription = form.watch({
                "MaschiMurariActionsForm.useEffect.subscription": (values)=>{
                    setParams({
                        "MaschiMurariActionsForm.useEffect.subscription": (prev)=>({
                                ...prev,
                                buildingCharacteristics: values
                            })
                    }["MaschiMurariActionsForm.useEffect.subscription"]);
                }
            }["MaschiMurariActionsForm.useEffect.subscription"]);
            return ({
                "MaschiMurariActionsForm.useEffect": ()=>subscription.unsubscribe()
            })["MaschiMurariActionsForm.useEffect"];
        }
    }["MaschiMurariActionsForm.useEffect"], [
        form,
        setParams
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Form"], {
        ...form,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            className: "space-y-4 rounded-md border p-4",
            onSubmit: form.handleSubmit(handleFormSubmit),
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "appliedNormalStress",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-actions-form.tsx",
                    lineNumber: 100,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "inPlaneBendingMoment",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-actions-form.tsx",
                    lineNumber: 105,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "outOfPlaneBendingMoment",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-actions-form.tsx",
                    lineNumber: 110,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "inPlaneAppliedShear",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-actions-form.tsx",
                    lineNumber: 115,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "outOfPlaneAppliedShear",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-actions-form.tsx",
                    lineNumber: 120,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    type: "submit",
                    className: "w-full sm:w-auto",
                    disabled: isPending,
                    onClick: form.handleSubmit(handleFormSubmit),
                    children: [
                        isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                            className: "animate-spin"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-actions-form.tsx",
                            lineNumber: 131,
                            columnNumber: 25
                        }, ("TURBOPACK compile-time value", void 0)),
                        tCommon('save')
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-actions-form.tsx",
                    lineNumber: 125,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-actions-form.tsx",
            lineNumber: 96,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-actions-form.tsx",
        lineNumber: 95,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(MaschiMurariActionsForm, "0GvcWzWtWEWdQUcgL4k4yYfwxkU=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"]
    ];
});
_c = MaschiMurariActionsForm;
var _c;
__turbopack_context__.k.register(_c, "MaschiMurariActionsForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-material-properties-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "MaschiMurariMaterialPropertiesForm",
    ()=>MaschiMurariMaterialPropertiesForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/select-form-fixed-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-save-module-params-mutation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$maschi$2d$murari$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/masonry/maschi-murari-form.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const MaschiMurariMaterialPropertiesForm = (param)=>{
    let { session, projectId, moduleId, defaultValues, params, setParams, onSave } = param;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.maschi-murari.material-properties');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.save-project-params.messages');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    var _defaultValues_structuralElementsNature, _defaultValues_masonryType, _defaultValues_knowledgeLevel, _defaultValues_confidenceFactor, _defaultValues_executionClass, _defaultValues_loadResistantCategory, _defaultValues_masonrySafetyFactor, _defaultValues_characteristicCompressiveStrength, _defaultValues_designCompressiveStrength, _defaultValues_characteristicShearStrength, _defaultValues_designShearStrength, _defaultValues_elasticModulus, _defaultValues_shearModulus, _defaultValues_ultimateCompressiveStrainLinearBehaviour, _defaultValues_ultimateCompressiveStrain, _defaultValues_exposure, _defaultValues_conversionFactor;
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$maschi$2d$murari$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["maschiMurariMaterialPropertiesSchema"]),
        defaultValues: {
            structuralElementsNature: (_defaultValues_structuralElementsNature = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.structuralElementsNature) !== null && _defaultValues_structuralElementsNature !== void 0 ? _defaultValues_structuralElementsNature : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FACING_MATERIAL"].BRICK,
            masonryType: (_defaultValues_masonryType = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.masonryType) !== null && _defaultValues_masonryType !== void 0 ? _defaultValues_masonryType : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CRM_MASONRY_TYPE"].MURATURA_IN_PIETRAME_DISORDINATA,
            knowledgeLevel: (_defaultValues_knowledgeLevel = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.knowledgeLevel) !== null && _defaultValues_knowledgeLevel !== void 0 ? _defaultValues_knowledgeLevel : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleMaterialKnowledgeLevel"].LC1,
            confidenceFactor: (_defaultValues_confidenceFactor = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.confidenceFactor) !== null && _defaultValues_confidenceFactor !== void 0 ? _defaultValues_confidenceFactor : 0,
            executionClass: (_defaultValues_executionClass = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.executionClass) !== null && _defaultValues_executionClass !== void 0 ? _defaultValues_executionClass : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["executionClass"].ONE,
            loadResistantCategory: (_defaultValues_loadResistantCategory = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.loadResistantCategory) !== null && _defaultValues_loadResistantCategory !== void 0 ? _defaultValues_loadResistantCategory : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["loadResistingCategory"].MASONRY_CAT_I_AND_MORTAR_WITH_GUARANTEED_PERFORMANCE,
            masonrySafetyFactor: (_defaultValues_masonrySafetyFactor = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.masonrySafetyFactor) !== null && _defaultValues_masonrySafetyFactor !== void 0 ? _defaultValues_masonrySafetyFactor : 0,
            characteristicCompressiveStrength: (_defaultValues_characteristicCompressiveStrength = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.characteristicCompressiveStrength) !== null && _defaultValues_characteristicCompressiveStrength !== void 0 ? _defaultValues_characteristicCompressiveStrength : 0,
            designCompressiveStrength: (_defaultValues_designCompressiveStrength = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.designCompressiveStrength) !== null && _defaultValues_designCompressiveStrength !== void 0 ? _defaultValues_designCompressiveStrength : 0,
            characteristicShearStrength: (_defaultValues_characteristicShearStrength = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.characteristicShearStrength) !== null && _defaultValues_characteristicShearStrength !== void 0 ? _defaultValues_characteristicShearStrength : 0,
            designShearStrength: (_defaultValues_designShearStrength = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.designShearStrength) !== null && _defaultValues_designShearStrength !== void 0 ? _defaultValues_designShearStrength : 0,
            elasticModulus: (_defaultValues_elasticModulus = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.elasticModulus) !== null && _defaultValues_elasticModulus !== void 0 ? _defaultValues_elasticModulus : 0,
            shearModulus: (_defaultValues_shearModulus = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.shearModulus) !== null && _defaultValues_shearModulus !== void 0 ? _defaultValues_shearModulus : 0,
            ultimateCompressiveStrainLinearBehaviour: (_defaultValues_ultimateCompressiveStrainLinearBehaviour = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.ultimateCompressiveStrainLinearBehaviour) !== null && _defaultValues_ultimateCompressiveStrainLinearBehaviour !== void 0 ? _defaultValues_ultimateCompressiveStrainLinearBehaviour : 0,
            ultimateCompressiveStrain: (_defaultValues_ultimateCompressiveStrain = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.ultimateCompressiveStrain) !== null && _defaultValues_ultimateCompressiveStrain !== void 0 ? _defaultValues_ultimateCompressiveStrain : 0.0035,
            exposure: (_defaultValues_exposure = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.exposure) !== null && _defaultValues_exposure !== void 0 ? _defaultValues_exposure : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleGeometryExposure"].INTERNAL,
            conversionFactor: (_defaultValues_conversionFactor = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.conversionFactor) !== null && _defaultValues_conversionFactor !== void 0 ? _defaultValues_conversionFactor : 0
        }
    });
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"])(session.accessToken, {
        onSuccess: {
            "MaschiMurariMaterialPropertiesForm.useSaveModuleParamsMutation": async ()=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(tAction('edit.success'));
                await queryClient.invalidateQueries({
                    queryKey: [
                        'modules',
                        {
                            projectId,
                            moduleId
                        }
                    ]
                });
                onSave();
            }
        }["MaschiMurariMaterialPropertiesForm.useSaveModuleParamsMutation"],
        onError: {
            "MaschiMurariMaterialPropertiesForm.useSaveModuleParamsMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(tAction('edit.failure', {
                    error: error.message
                }));
            }
        }["MaschiMurariMaterialPropertiesForm.useSaveModuleParamsMutation"]
    });
    const handleFormSubmit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "MaschiMurariMaterialPropertiesForm.useCallback[handleFormSubmit]": (body)=>{
            // construct body based on global schema:
            const maschiMurariParams = {
                ...params,
                materialProperties: body
            };
            mutate({
                projectId,
                moduleId,
                body: maschiMurariParams
            });
        }
    }["MaschiMurariMaterialPropertiesForm.useCallback[handleFormSubmit]"], [
        mutate,
        projectId,
        moduleId,
        params
    ]);
    var _form_watch;
    const knowledgeMaterialLevel = (_form_watch = form.watch('knowledgeLevel')) !== null && _form_watch !== void 0 ? _form_watch : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleMaterialKnowledgeLevel"].LC1;
    var _form_watch1;
    const executionClassFormValue = (_form_watch1 = form.watch('executionClass')) !== null && _form_watch1 !== void 0 ? _form_watch1 : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["executionClass"].ONE;
    var _form_watch2;
    const loadResistingCategoryFormValue = (_form_watch2 = form.watch('loadResistantCategory')) !== null && _form_watch2 !== void 0 ? _form_watch2 : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["loadResistingCategory"].MASONRY_CAT_I_AND_MORTAR_WITH_GUARANTEED_PERFORMANCE;
    var _form_watch3;
    const masonryTypeValue = (_form_watch3 = form.watch('masonryType')) !== null && _form_watch3 !== void 0 ? _form_watch3 : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CRM_MASONRY_TYPE"].MURATURA_IN_PIETRAME_DISORDINATA;
    var _form_watch4;
    const exposureValue = (_form_watch4 = form.watch('exposure')) !== null && _form_watch4 !== void 0 ? _form_watch4 : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleGeometryExposure"].INTERNAL;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MaschiMurariMaterialPropertiesForm.useEffect": ()=>{
            const calculateGivenStrength = {
                "MaschiMurariMaterialPropertiesForm.useEffect.calculateGivenStrength": (strengthMinMax)=>{
                    return knowledgeMaterialLevel === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleMaterialKnowledgeLevel"].LC1 ? strengthMinMax.min : knowledgeMaterialLevel === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleMaterialKnowledgeLevel"].LC3 ? strengthMinMax.max : (strengthMinMax.min + strengthMinMax.max) / 2;
                }
            }["MaschiMurariMaterialPropertiesForm.useEffect.calculateGivenStrength"];
            const confidenceFactor = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleMaterialKnowledgeLevelValues"][knowledgeMaterialLevel];
            form.setValue('confidenceFactor', confidenceFactor);
            const masonryStrengthSafetyFactor = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["masonryStrengthSafetyFactorMapping"][loadResistingCategoryFormValue][executionClassFormValue];
            form.setValue('masonrySafetyFactor', masonryStrengthSafetyFactor);
            const characteristicCompressiveStrengthMinMax = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["characteristicCompressiveStrengthValues"][masonryTypeValue];
            const characteristicCompressiveStrength = calculateGivenStrength(characteristicCompressiveStrengthMinMax);
            form.setValue('characteristicCompressiveStrength', characteristicCompressiveStrength);
            const designCompressiveStrength = characteristicCompressiveStrength / masonryStrengthSafetyFactor / confidenceFactor;
            form.setValue('designCompressiveStrength', designCompressiveStrength);
            const characteristicsShearStrengthMinMax = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["characteristicShearStrengthValues"][masonryTypeValue];
            const characteristicShearStrength = calculateGivenStrength(characteristicsShearStrengthMinMax);
            form.setValue('characteristicShearStrength', characteristicShearStrength);
            const designShearStrength = characteristicShearStrength / confidenceFactor / masonryStrengthSafetyFactor;
            form.setValue('designShearStrength', designShearStrength);
            const normalElasticityModulusMinMax = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["characteristicNormalElasticityModulusValues"][masonryTypeValue];
            const normalElasticityModulus = (normalElasticityModulusMinMax.min + normalElasticityModulusMinMax.max) / 2;
            form.setValue('elasticModulus', normalElasticityModulus);
            const shearElasticityModulusMinMax = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["characteristicShearElasticityModulusValues"][masonryTypeValue];
            const shearElasticityModulus = (shearElasticityModulusMinMax.min + shearElasticityModulusMinMax.max) / 2;
            form.setValue('shearModulus', shearElasticityModulus);
            // =D19/D22
            const ultimateCompressiveStrainLinearBehaviour = designCompressiveStrength / normalElasticityModulus;
            form.setValue('ultimateCompressiveStrainLinearBehaviour', ultimateCompressiveStrainLinearBehaviour);
            const ultimateCompressiveStrain = 0.0035;
            form.setValue('ultimateCompressiveStrain', ultimateCompressiveStrain);
            const conversionFactor = exposureValue === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleGeometryExposure"].INTERNAL ? 0.9 : exposureValue === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleGeometryExposure"].EXTERNAL ? 0.8 : 0.7;
            form.setValue('conversionFactor', conversionFactor);
        }
    }["MaschiMurariMaterialPropertiesForm.useEffect"], [
        form,
        knowledgeMaterialLevel,
        executionClassFormValue,
        loadResistingCategoryFormValue,
        masonryTypeValue,
        exposureValue
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MaschiMurariMaterialPropertiesForm.useEffect": ()=>{
            const subscription = form.watch({
                "MaschiMurariMaterialPropertiesForm.useEffect.subscription": (values)=>{
                    setParams({
                        "MaschiMurariMaterialPropertiesForm.useEffect.subscription": (prev)=>({
                                ...prev,
                                buildingCharacteristics: values
                            })
                    }["MaschiMurariMaterialPropertiesForm.useEffect.subscription"]);
                }
            }["MaschiMurariMaterialPropertiesForm.useEffect.subscription"]);
            return ({
                "MaschiMurariMaterialPropertiesForm.useEffect": ()=>subscription.unsubscribe()
            })["MaschiMurariMaterialPropertiesForm.useEffect"];
        }
    }["MaschiMurariMaterialPropertiesForm.useEffect"], [
        form,
        setParams
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Form"], {
        ...form,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            className: "space-y-4 rounded-md border p-4",
            onSubmit: form.handleSubmit(handleFormSubmit),
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "structuralElementsNature",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_FACING_MATERIAL"],
                    optionLabelFn: (p)=>t("structuralElementsNature.".concat(p)),
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-material-properties-form.tsx",
                    lineNumber: 250,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "masonryType",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_CRM_MASONRY_TYPE"],
                    optionLabelFn: (p)=>t("masonryType.".concat(p)),
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-material-properties-form.tsx",
                    lineNumber: 257,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "knowledgeLevel",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_MATERIAL_KNOWLEDGE_LEVEL"],
                    optionLabelFn: (p)=>t("knowledgeLevel.".concat(p)),
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-material-properties-form.tsx",
                    lineNumber: 264,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "confidenceFactor",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-material-properties-form.tsx",
                    lineNumber: 271,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "executionClass",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_EXECUTION_CLASS"],
                    optionLabelFn: (p)=>t("executionClass.".concat(p)),
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-material-properties-form.tsx",
                    lineNumber: 277,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "loadResistantCategory",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_LOAD_RESISTING_CATEGORY"],
                    optionLabelFn: (p)=>t("loadResistantCategory.".concat(p)),
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-material-properties-form.tsx",
                    lineNumber: 284,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "masonrySafetyFactor",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-material-properties-form.tsx",
                    lineNumber: 291,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "characteristicCompressiveStrength",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-material-properties-form.tsx",
                    lineNumber: 297,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "designCompressiveStrength",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-material-properties-form.tsx",
                    lineNumber: 303,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "characteristicShearStrength",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-material-properties-form.tsx",
                    lineNumber: 309,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "designShearStrength",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-material-properties-form.tsx",
                    lineNumber: 315,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "elasticModulus",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-material-properties-form.tsx",
                    lineNumber: 321,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "shearModulus",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-material-properties-form.tsx",
                    lineNumber: 327,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "ultimateCompressiveStrainLinearBehaviour",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-material-properties-form.tsx",
                    lineNumber: 333,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "ultimateCompressiveStrain",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-material-properties-form.tsx",
                    lineNumber: 339,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "exposure",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_GEOMETRY_EXPOSURE"],
                    optionLabelFn: (p)=>t("exposure.".concat(p)),
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-material-properties-form.tsx",
                    lineNumber: 345,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "conversionFactor",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-material-properties-form.tsx",
                    lineNumber: 352,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    type: "submit",
                    className: "w-full sm:w-auto",
                    disabled: isPending,
                    onClick: form.handleSubmit(handleFormSubmit),
                    children: [
                        isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                            className: "animate-spin"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-material-properties-form.tsx",
                            lineNumber: 364,
                            columnNumber: 25
                        }, ("TURBOPACK compile-time value", void 0)),
                        tCommon('save')
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-material-properties-form.tsx",
                    lineNumber: 358,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-material-properties-form.tsx",
            lineNumber: 246,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-material-properties-form.tsx",
        lineNumber: 245,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(MaschiMurariMaterialPropertiesForm, "q8qy4WSiQgq59By9bYU1Gd3awn8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"]
    ];
});
_c = MaschiMurariMaterialPropertiesForm;
var _c;
__turbopack_context__.k.register(_c, "MaschiMurariMaterialPropertiesForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-panel-geometry-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "MaschiMurariPanelGeometryForm",
    ()=>MaschiMurariPanelGeometryForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-save-module-params-mutation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$maschi$2d$murari$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/masonry/maschi-murari-form.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
const MaschiMurariPanelGeometryForm = (param)=>{
    let { session, projectId, moduleId, defaultValues, params, setParams, onSave } = param;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.maschi-murari.panel-geometry');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.save-project-params.messages');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    var _defaultValues_height, _defaultValues_width, _defaultValues_thickness;
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$maschi$2d$murari$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["maschiMurariPanelGeometrySchema"]),
        defaultValues: {
            height: (_defaultValues_height = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.height) !== null && _defaultValues_height !== void 0 ? _defaultValues_height : 0,
            width: (_defaultValues_width = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.width) !== null && _defaultValues_width !== void 0 ? _defaultValues_width : 0,
            thickness: (_defaultValues_thickness = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.thickness) !== null && _defaultValues_thickness !== void 0 ? _defaultValues_thickness : 0
        }
    });
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"])(session.accessToken, {
        onSuccess: {
            "MaschiMurariPanelGeometryForm.useSaveModuleParamsMutation": async ()=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(tAction('edit.success'));
                await queryClient.invalidateQueries({
                    queryKey: [
                        'modules',
                        {
                            projectId,
                            moduleId
                        }
                    ]
                });
                onSave();
            }
        }["MaschiMurariPanelGeometryForm.useSaveModuleParamsMutation"],
        onError: {
            "MaschiMurariPanelGeometryForm.useSaveModuleParamsMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(tAction('edit.failure', {
                    error: error.message
                }));
            }
        }["MaschiMurariPanelGeometryForm.useSaveModuleParamsMutation"]
    });
    const handleFormSubmit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "MaschiMurariPanelGeometryForm.useCallback[handleFormSubmit]": (body)=>{
            // construct body based on global schema:
            const maschiMurariParams = {
                ...params,
                panelGeometry: body
            };
            mutate({
                projectId,
                moduleId,
                body: maschiMurariParams
            });
        }
    }["MaschiMurariPanelGeometryForm.useCallback[handleFormSubmit]"], [
        mutate,
        projectId,
        moduleId,
        params
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MaschiMurariPanelGeometryForm.useEffect": ()=>{
            const subscription = form.watch({
                "MaschiMurariPanelGeometryForm.useEffect.subscription": (values)=>{
                    setParams({
                        "MaschiMurariPanelGeometryForm.useEffect.subscription": (prev)=>({
                                ...prev,
                                buildingCharacteristics: values
                            })
                    }["MaschiMurariPanelGeometryForm.useEffect.subscription"]);
                }
            }["MaschiMurariPanelGeometryForm.useEffect.subscription"]);
            return ({
                "MaschiMurariPanelGeometryForm.useEffect": ()=>subscription.unsubscribe()
            })["MaschiMurariPanelGeometryForm.useEffect"];
        }
    }["MaschiMurariPanelGeometryForm.useEffect"], [
        form,
        setParams
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Form"], {
        ...form,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            className: "space-y-4 rounded-md border p-4",
            onSubmit: form.handleSubmit(handleFormSubmit),
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    src: "/assets/masonry_frcm/FRCM_Muratura.jpg",
                    alt: "site characteristics",
                    height: 250,
                    width: 500,
                    className: "mx-auto rounded-md object-contain",
                    priority: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-panel-geometry-form.tsx",
                    lineNumber: 99,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "height",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-panel-geometry-form.tsx",
                    lineNumber: 107,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "width",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-panel-geometry-form.tsx",
                    lineNumber: 108,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "thickness",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-panel-geometry-form.tsx",
                    lineNumber: 109,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    type: "submit",
                    className: "w-full sm:w-auto",
                    disabled: isPending,
                    onClick: form.handleSubmit(handleFormSubmit),
                    children: [
                        isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                            className: "animate-spin"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-panel-geometry-form.tsx",
                            lineNumber: 116,
                            columnNumber: 25
                        }, ("TURBOPACK compile-time value", void 0)),
                        tCommon('save')
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-panel-geometry-form.tsx",
                    lineNumber: 110,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-panel-geometry-form.tsx",
            lineNumber: 95,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-panel-geometry-form.tsx",
        lineNumber: 94,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(MaschiMurariPanelGeometryForm, "0GvcWzWtWEWdQUcgL4k4yYfwxkU=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"]
    ];
});
_c = MaschiMurariPanelGeometryForm;
var _c;
__turbopack_context__.k.register(_c, "MaschiMurariPanelGeometryForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "MaschiMurariParamsForm",
    ()=>MaschiMurariParamsForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/accordion.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$maschi$2d$murari$2f$calculations$2f$flexural$2f$maschi$2d$murari$2d$reinforcement$2d$flexural$2d$calculation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$maschi$2d$murari$2f$calculations$2f$shear$2f$maschi$2d$murari$2d$reinforcement$2d$shear$2d$calculation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$maschi$2d$murari$2f$sections$2f$maschi$2d$murari$2d$actions$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-actions-form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$maschi$2d$murari$2f$sections$2f$maschi$2d$murari$2d$material$2d$properties$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-material-properties-form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$maschi$2d$murari$2f$sections$2f$maschi$2d$murari$2d$panel$2d$geometry$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-panel-geometry-form.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
const MaschiMurariParamsForm = (param)=>{
    let { session, projectId, moduleId, module } = param;
    var _module_params, _module_params1, _module_params2, _module_params3, _module_params4, _module_params5;
    _s();
    var _module_params6;
    const [params, setParams] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])((_module_params6 = module === null || module === void 0 ? void 0 : module.params) !== null && _module_params6 !== void 0 ? _module_params6 : {});
    const [openItems, setOpenItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([
        '0'
    ]);
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.maschi-murari');
    const handleItemSaved = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "MaschiMurariParamsForm.useCallback[handleItemSaved]": (id)=>{
            const nextId = String(Number(id) + 1);
            setOpenItems({
                "MaschiMurariParamsForm.useCallback[handleItemSaved]": (old)=>{
                    const temp = old.filter({
                        "MaschiMurariParamsForm.useCallback[handleItemSaved].temp": (v)=>v !== id
                    }["MaschiMurariParamsForm.useCallback[handleItemSaved].temp"]);
                    return old.includes(nextId) ? temp : [
                        ...temp,
                        nextId
                    ];
                }
            }["MaschiMurariParamsForm.useCallback[handleItemSaved]"]);
        }
    }["MaschiMurariParamsForm.useCallback[handleItemSaved]"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Accordion"], {
            type: "multiple",
            value: openItems,
            onValueChange: setOpenItems,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionItem"], {
                    value: "0",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-medium",
                                children: t('panel-geometry.title')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
                                lineNumber: 59,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
                            lineNumber: 58,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionContent"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$maschi$2d$murari$2f$sections$2f$maschi$2d$murari$2d$panel$2d$geometry$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MaschiMurariPanelGeometryForm"], {
                                session: session,
                                projectId: projectId,
                                moduleId: moduleId,
                                defaultValues: params === null || params === void 0 ? void 0 : params.panelGeometry,
                                setParams: setParams,
                                params: params,
                                onSave: ()=>handleItemSaved('0')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
                                lineNumber: 62,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
                            lineNumber: 61,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
                    lineNumber: 57,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionItem"], {
                    value: "1",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-medium",
                                children: t('material-properties.title')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
                                lineNumber: 75,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
                            lineNumber: 74,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionContent"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$maschi$2d$murari$2f$sections$2f$maschi$2d$murari$2d$material$2d$properties$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MaschiMurariMaterialPropertiesForm"], {
                                session: session,
                                projectId: projectId,
                                moduleId: moduleId,
                                defaultValues: params === null || params === void 0 ? void 0 : params.materialProperties,
                                setParams: setParams,
                                params: params,
                                onSave: ()=>handleItemSaved('1')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
                                lineNumber: 80,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
                            lineNumber: 79,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
                    lineNumber: 73,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionItem"], {
                    value: "2",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-medium",
                                children: t('actions.title')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
                                lineNumber: 93,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
                            lineNumber: 92,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionContent"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$maschi$2d$murari$2f$sections$2f$maschi$2d$murari$2d$actions$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MaschiMurariActionsForm"], {
                                session: session,
                                projectId: projectId,
                                moduleId: moduleId,
                                defaultValues: params.actions,
                                params: params,
                                setParams: setParams,
                                onSave: ()=>handleItemSaved('2')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
                                lineNumber: 96,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
                            lineNumber: 95,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
                    lineNumber: 91,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionItem"], {
                    value: "3",
                    disabled: !(module === null || module === void 0 ? void 0 : (_module_params = module.params) === null || _module_params === void 0 ? void 0 : _module_params.panelGeometry) || !(module === null || module === void 0 ? void 0 : (_module_params1 = module.params) === null || _module_params1 === void 0 ? void 0 : _module_params1.materialProperties) || !(module === null || module === void 0 ? void 0 : (_module_params2 = module.params) === null || _module_params2 === void 0 ? void 0 : _module_params2.actions),
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-medium",
                                children: t('shear.title')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
                                lineNumber: 116,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
                            lineNumber: 115,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionContent"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$maschi$2d$murari$2f$calculations$2f$shear$2f$maschi$2d$murari$2d$reinforcement$2d$shear$2d$calculation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MaschiMurariShearCalculation"], {
                                session: session,
                                projectId: projectId,
                                module: module,
                                onNext: ()=>handleItemSaved('3')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
                                lineNumber: 119,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
                            lineNumber: 118,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
                    lineNumber: 107,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionItem"], {
                    value: "4",
                    disabled: !(module === null || module === void 0 ? void 0 : (_module_params3 = module.params) === null || _module_params3 === void 0 ? void 0 : _module_params3.panelGeometry) || !(module === null || module === void 0 ? void 0 : (_module_params4 = module.params) === null || _module_params4 === void 0 ? void 0 : _module_params4.materialProperties) || !(module === null || module === void 0 ? void 0 : (_module_params5 = module.params) === null || _module_params5 === void 0 ? void 0 : _module_params5.actions),
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-medium",
                                children: t('flexural.title')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
                                lineNumber: 136,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
                            lineNumber: 135,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionContent"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$maschi$2d$murari$2f$calculations$2f$flexural$2f$maschi$2d$murari$2d$reinforcement$2d$flexural$2d$calculation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MaschiMurariFlexuralCalculation"], {
                                session: session,
                                projectId: projectId,
                                module: module
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
                                lineNumber: 139,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
                            lineNumber: 138,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
                    lineNumber: 127,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
            lineNumber: 52,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx",
        lineNumber: 51,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(MaschiMurariParamsForm, "Lx+MGo0Ovjr3dHYDfBsYSN9ms6s=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"]
    ];
});
_c = MaschiMurariParamsForm;
var _c;
__turbopack_context__.k.register(_c, "MaschiMurariParamsForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
]);

//# sourceMappingURL=eb54d_common_atlas_module-detail_params-forms_masonry_maschi-murari_984902cd._.js.map