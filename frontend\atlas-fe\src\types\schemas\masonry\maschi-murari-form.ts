import {
  MODULE_CRM_MASONRY_TYPE,
  MODULE_EXECUTION_CLASS,
  MODULE_FACING_MATERIAL,
  MODULE_GEOMETRY_EXPOSURE,
  MODULE_LOAD_RESISTING_CATEGORY,
  MODULE_MATERIAL_KNOWLEDGE_LEVEL,
  REI<PERSON>ORCEMENT_ARRANGEMENT,
  REINFORCEMENT_FAILURE_MODE,
} from '@atlas/constants/module'
import { nullToUndefinedTransform } from '@atlas/functions/zod/null-to-undefined-transform'
import { productSchema } from '@atlas/lib/api/products/schemas/product'
import z from 'zod'
import { productFormSchema } from '../product-form-schema'

export const maschiMurariPanelGeometrySchema = z.object({
  height: z.number().positive().optional().nullable(),
  width: z.number().positive().optional().nullable(),
  thickness: z.number().positive().optional().nullable(),
})

export const maschiMurariMaterialPropertiesSchema = z.object({
  structuralElementsNature: z.enum(MODULE_FACING_MATERIAL),
  masonryType: z.enum(MODULE_CRM_MASONRY_TYPE),
  knowledgeLevel: z.enum(MODULE_MATERIAL_KNOWLEDGE_LEVEL),
  confidenceFactor: z.number().optional().nullable(),
  executionClass: z.enum(MODULE_EXECUTION_CLASS),
  loadResistantCategory: z.enum(MODULE_LOAD_RESISTING_CATEGORY),
  masonrySafetyFactor: z.number().optional().nullable(),
  characteristicCompressiveStrength: z.number().optional().nullable(),
  designCompressiveStrength: z.number().optional().nullable(),
  characteristicShearStrength: z.number().optional().nullable(),
  designShearStrength: z.number().optional().nullable(),
  elasticModulus: z.number().optional().nullable(),
  shearModulus: z.number().optional().nullable(),
  ultimateCompressiveStrainLinearBehaviour: z.number().optional().nullable(),
  ultimateCompressiveStrain: z.number().optional().nullable(),
  exposure: z.enum(MODULE_GEOMETRY_EXPOSURE),
  conversionFactor: z.number().optional().nullable(),
})

export const maschiMurariActionsSchema = z.object({
  appliedNormalStress: z.number().optional().nullable(),
  inPlaneBendingMoment: z.number().optional().nullable(),
  outOfPlaneBendingMoment: z.number().optional().nullable(),
  inPlaneAppliedShear: z.number().optional().nullable(),
  outOfPlaneAppliedShear: z.number().optional().nullable(),
})

export const maschiMurariParamsSchema = z.object({
  panelGeometry: maschiMurariPanelGeometrySchema
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  materialProperties: maschiMurariMaterialPropertiesSchema
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  actions: maschiMurariActionsSchema
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
})

export const maschiMurariShearReinforcementVerifyExecutionSchema = z.object({
  product: productSchema,
  reinforcementFailureMode: z.enum(REINFORCEMENT_FAILURE_MODE),
  designReinforcementStress: z.number(),
  designReinforcementStrain: z.number(),
  reinforcedArrangement: z.enum(REINFORCEMENT_ARRANGEMENT),
  singleStripWidth: z.number(),
  stripSpacing: z.number(),
  totalReinforcementWidthPerpendicularShearDirection: z.number(),
  compressedEdgeReinforcementFiberDistance: z.number(),
  layersNumber: z.number(),
  reinforcedSidesNumber: z.number(),
})

export const maschiMurariFlexuralReinforcementVerifyExecutionSchema = z.object({
  // For in-plane bending
  product: productSchema,
  sectionFailureMode: z.enum(REINFORCEMENT_FAILURE_MODE),
  designReinforcementStress: z.number(),
  designReinforcementStrain: z.number(),
  reinforcedArrangement: z.enum(REINFORCEMENT_ARRANGEMENT),
  singleStripWidth: z.number(),
  stripSpacing: z.number(),
  reinforcementTotalWidthAlongLength: z.number(),
  compressedEdgeReinforcementFiberDistance: z.number(),
  layersNumber: z.number(),
  reinforcedSidesNumber: z.number(),
  totalEquivalentThickness: z.number(),
  firstCoefficient: z.number(),
  secondCoefficient: z.number(),

  // Only for out-of-plane bending
  outOfPlanUnitWidthPanel: z.number(),
  outOfPlanAppliedDesignAxialStress: z.number(),
  outOfPlaneReinforcementFailureMode: z.enum(REINFORCEMENT_FAILURE_MODE),
  outOfPlaneDesignReinforcementStress: z.number(),
  outOfPlaneDesignReinforcementStrain: z.number(),
  outOfPlaneReinforcementTotalWidthAlongLength: z.number(),
  outOfPlaneResistingArea: z.number(),
})

export const flexuralReinforcementVerifyExecutionSchema = z.object({
  // For in-plane bending
  product: productFormSchema,
  sectionFailureMode: z.enum(REINFORCEMENT_FAILURE_MODE),
  designReinforcementStress: z.number(),
  designReinforcementStrain: z.number(),
  reinforcedArrangement: z.enum(REINFORCEMENT_ARRANGEMENT),
  singleStripWidth: z.number(),
  stripSpacing: z.number(),
  reinforcementTotalWidthAlongLength: z.number(),
  compressedEdgeReinforcementFiberDistance: z.number(),
  layersNumber: z.number(),
  reinforcedSidesNumber: z.number(),
  totalEquivalentThickness: z.number(),
  firstCoefficient: z.number(),
  secondCoefficient: z.number(),

  // Only for out-of-plane bending
  outOfPlanUnitWidthPanel: z.number(),
  outOfPlanAppliedDesignAxialStress: z.number(),
  outOfPlaneReinforcementFailureMode: z.enum(REINFORCEMENT_FAILURE_MODE),
  outOfPlaneDesignReinforcementStress: z.number(),
  outOfPlaneDesignReinforcementStrain: z.number(),
  outOfPlaneReinforcementTotalWidthAlongLength: z.number(),
  outOfPlaneResistingArea: z.number(),
})

export const shearReinforcementExecutionSchema = z.object({
  product: productFormSchema,
  reinforcementFailureMode: z.enum(REINFORCEMENT_FAILURE_MODE),
  designReinforcementStress: z.number(),
  designReinforcementStrain: z.number(),
  reinforcedArrangement: z.enum(REINFORCEMENT_ARRANGEMENT),
  singleStripWidth: z.number(),
  stripSpacing: z.number(),
  totalReinforcementWidthPerpendicularShearDirection: z.number(),
  compressedEdgeReinforcementFiberDistance: z.number(),
  layersNumber: z.number(),
  reinforcedSidesNumber: z.number(),
})

export const maschiMurariFlexuralReinforcementExecutionSchema = z.object({
  calculationType: z.literal('FLEXURAL_VERIFY'),
  input: flexuralReinforcementVerifyExecutionSchema,
})

export const maschiMurariShearReinforcementExecutionSchema = z.object({
  calculationType: z.literal('SHEAR_VERIFY'),
  input: shearReinforcementExecutionSchema,
})

export const nonReinforcedSectionShearSchema = z.object({
  inPlaneAppliedShear: z.number(),
  verticalStress: z.number(),
  correctionFactorBasedOnWallSlenderness: z.number(),
  shearResistanceNotReinforcedMasonry: z.number(),
  check: z.boolean(),
})

export const reinforcedSectionShearSchema = z.object({
  shearResistanceReinforcementContribution: z.number(),
  totalShearResistance: z.number(),
  firstCheck: z.boolean(),
  shearResistanceFromMasonryDiagonalCompression: z.number(),
  secondCheck: z.boolean(),
})

export const inPlaneShearCheckSchema = z.object({
  nonReinforcedSection: nonReinforcedSectionShearSchema
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  reinforcedSection: reinforcedSectionShearSchema
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
})

export const maschiMurariShearReinforcementCalculationResultSchema = z.object({
  inPlaneShearCheck: inPlaneShearCheckSchema
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
})

export const inPlaneFlexuralHypothesis = z.object({
  neutralAxisCompressedEdgeDistance: z.number(),
  reinforcementOrMasonryStrain: z.number(),
  check: z.boolean(),
})

export const inPlaneFlexuralReinforcedSection = z.object({
  hypothesisOne: inPlaneFlexuralHypothesis.optional().nullable(),
  hypothesisTwo: inPlaneFlexuralHypothesis.optional().nullable(),
  hypothesisThree: inPlaneFlexuralHypothesis.optional().nullable(),
  momentCapacity: z.number(),
  check: z.boolean(),
})

export const inPlaneFlexuralNonReinforcedSection = z.object({
  inPlaneBendingMoment: z.number(),
  neutralAxisCompressedEdgeDistance: z.number(),
  inPlaneFlexuralCapacity: z.number(),
  check: z.boolean(),
})

export const inPlaneFlexuralCheckSchema = z.object({
  nonReinforcedSection: inPlaneFlexuralNonReinforcedSection
    .optional()
    .nullable(),
  reinforcedSection: inPlaneFlexuralReinforcedSection.optional().nullable(),
})

export const outOfPlaneFlexuralRegionHypothesis = z.object({
  neutralAxisCompressedEdgeDistance: z.number(),
  reinforcementOrMasonryStrain: z.number(),
  resultantCompressiveForceMasonry: z.number(),
  resultantTensileForceFrcm: z.number(),
  designBendingMomentReinforcedSection: z.number(),
  check: z.boolean(),
})

export const outOfPlaneReinforcedSection = z.object({
  regionHypothesisTwo: outOfPlaneFlexuralRegionHypothesis.optional().nullable(),
  regionHypothesisOne: outOfPlaneFlexuralRegionHypothesis.optional().nullable(),
  momentCapacity: z.number(),
  check: z.boolean(),
})

export const outOfPlaneNonReinforcedSection = z.object({
  appliedDesignBendingMoment: z.number(),
  neutralAxisCompressedEdgeDistance: z.number(),
  resultantCompressiveForceMasonry: z.number(),
  designBendingMoment: z.number(),
  check: z.boolean(),
})

export const outOfPlaneFlexuralCheckSchema = z.object({
  nonReinforcedSection: outOfPlaneNonReinforcedSection.optional().nullable(),
  reinforcedSection: outOfPlaneReinforcedSection.optional().nullable(),
})

export const shearCheckSchema = z.object({
  appliedDesignSpecificShearForce: z.number(),
  averageNormalStress: z.number(),
  resultantCompressiveForceMasonry: z.number(),
  resultantTensileForceFrcm: z.number(),
  check: z.boolean(),
})

export const extremityDetachementCheckSchema = z.object({
  appliedSpecificBendingMoment30FromEdge: z.number().optional().nullable(),
  reinforcementDesignStrainForEndDebonding: z.number(),
  neutralAxisCompressedEdgeDistance: z.number(),
  masonryStrain: z.number(),
  resultantCompressiveForceMasonry: z.number(),
  resultantTensileForceFrcm: z.number(),
  designMomentCapacityReinforcedSection: z.number(),
  check: z.boolean(),
})

export const maschiMurariFlexuralReinforcementCalculationResultSchema =
  z.object({
    inPlaneFlexuralCheck: inPlaneFlexuralCheckSchema
      .nullable()
      .optional()
      .transform(nullToUndefinedTransform),
    outOfPlaneFlexuralCheck: outOfPlaneFlexuralCheckSchema
      .nullable()
      .optional()
      .transform(nullToUndefinedTransform),
    shearCheck: shearCheckSchema
      .nullable()
      .optional()
      .transform(nullToUndefinedTransform),
    extremityDetachementCheck: extremityDetachementCheckSchema
      .nullable()
      .optional()
      .transform(nullToUndefinedTransform),
  })

export type MaschiMurariFlexuralReinforcementCalculationResultSchema = z.infer<
  typeof maschiMurariFlexuralReinforcementCalculationResultSchema
>

export type InPlaneFlexuralCheckSchema = z.infer<
  typeof inPlaneFlexuralCheckSchema
>
export type OutOfPlaneFlexuralCheckSchema = z.infer<
  typeof outOfPlaneFlexuralCheckSchema
>
export type ShearCheckSchema = z.infer<typeof shearCheckSchema>
export type ExtremityDetachementCheckSchema = z.infer<
  typeof extremityDetachementCheckSchema
>
export type OutOfPlaneNonReinforcedSectionSchema = z.infer<
  typeof outOfPlaneNonReinforcedSection
>

export type MaschiMurariShearReinforcementCalculationResultSchema = z.infer<
  typeof maschiMurariShearReinforcementCalculationResultSchema
>

export type NonReinforcedSectionShearSchema = z.infer<
  typeof nonReinforcedSectionShearSchema
>
export type ReinforcedSectionShearSchema = z.infer<
  typeof reinforcedSectionShearSchema
>
export type InPlaneShearCheckSchema = z.infer<typeof inPlaneShearCheckSchema>
export type OutOfPlaneFlexuralRegionHypothesis = z.infer<
  typeof outOfPlaneFlexuralRegionHypothesis
>

export type MaschiMurariShearReinforcementVerifyExecutionSchemaInput = z.infer<
  typeof maschiMurariShearReinforcementVerifyExecutionSchema
>
export type MaschiMurariShearReinforcementExecutionSchemaInput = z.infer<
  typeof maschiMurariShearReinforcementExecutionSchema
>

export type MaschiMurariFlexuralReinforcementVerifyExecutionSchemaInput =
  z.infer<typeof maschiMurariFlexuralReinforcementVerifyExecutionSchema>

export type MaschiMurariFlexuralReinforcementExecutionSchemaInput = z.infer<
  typeof maschiMurariFlexuralReinforcementExecutionSchema
>

export type MaschiMurariParamsSchemaInputs = z.infer<
  typeof maschiMurariParamsSchema
>
export type MaschiMurariPanelGeometrySchemaInputs = z.infer<
  typeof maschiMurariPanelGeometrySchema
>
export type MaschiMurariMaterialPropertiesSchemaInputs = z.infer<
  typeof maschiMurariMaterialPropertiesSchema
>
export type MaschiMurariActionsSchemaInputs = z.infer<
  typeof maschiMurariActionsSchema
>
