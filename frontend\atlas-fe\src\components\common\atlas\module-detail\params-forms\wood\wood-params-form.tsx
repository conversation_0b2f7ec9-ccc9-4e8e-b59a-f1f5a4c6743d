import { WoodBeamGeometryForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form'
import { WoodGeneralForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-general-form'
import { WoodGeometryForm as WoodMaterialPropertiesForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-geometry-form'
import { WoodPreInterventionCheckForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form'
import { WoodPostInterventionForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form'

import {
  Accordion as AccordionComponent,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@atlas/components/ui/accordion'
import { Separator } from '@atlas/components/ui/separator'
import type {
  Module,
  ModuleWithParamsWood,
} from '@atlas/lib/api/modules/schemas/modules'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import { woodParamsCheckSchema, woodCalculationCheck } from '@atlas/types/schemas/wood-form'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useCallback, useMemo, useState } from 'react'
import { ModuleReportGenerationSection } from '../../module-report-generation-section'

type Props = {
  session: Session
  projectId: Project['id']
  moduleId: Module['id']
  module: ModuleWithParamsWood
}

export const WoodParamsForm = ({
  session,
  projectId,
  moduleId,
  module,
}: Props) => {
  const { params } = module
  const [openItems, setOpenItems] = useState(['0'])
  const t = useTranslations('forms.project-params.wood')

  const handleItemSaved = useCallback((id: string) => {
    const nextId = String(Number(id) + 1)
    setOpenItems(old => {
      const temp = old.filter(v => v !== id)
      return old.includes(nextId) ? temp : [...temp, nextId]
    })
  }, [])

  const { success } = useMemo(
    () => woodParamsCheckSchema.safeParse(params),
    [params],
  )

  const { success: enableReport } = useMemo(
      () => woodCalculationCheck.safeParse(module),
      [module],
    )

  return (
    <div>
      <AccordionComponent
        type="multiple"
        value={openItems}
        onValueChange={setOpenItems}
      >
        <AccordionItem value="0">
          <AccordionTrigger>
            <h3 className="text-lg font-medium">{t('general.title')}</h3>
          </AccordionTrigger>
          <AccordionContent>
            <WoodGeneralForm
              session={session}
              projectId={projectId}
              moduleId={moduleId}
              defaultValues={{
                initialDeformation: params?.initialDeformation,
              }}
              onSave={() => handleItemSaved('0')}
            />
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="1">
          <AccordionTrigger>
            <h3 className="text-lg font-medium">
              {t('materialProperties.title')}
            </h3>
          </AccordionTrigger>
          <AccordionContent>
            <WoodMaterialPropertiesForm
              session={session}
              projectId={projectId}
              moduleId={moduleId}
              defaultValues={
                // Wood material properties are stored in materialProperties field
                // Check if params.materialProperties has wood material properties structure
                params?.materialProperties &&
                typeof params.materialProperties === 'object' &&
                'category' in params.materialProperties
                  ? (params.materialProperties as any) // It's wood material properties
                  : undefined // No wood material properties saved yet, start with empty form
              }
              onSave={() => handleItemSaved('1')}
            />
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="2">
          <AccordionTrigger>
            <h3 className="text-lg font-medium">{t('geometry.title')}</h3>
          </AccordionTrigger>
          <AccordionContent>
            <WoodBeamGeometryForm
              session={session}
              projectId={projectId}
              moduleId={moduleId}
              defaultValues={
                // Wood geometry properties are stored in geometry field
                params?.geometry &&
                typeof params.geometry === 'object' &&
                'beamSectionWidth' in params.geometry
                  ? (params.geometry as any) // It's wood geometry properties
                  : undefined // No wood geometry properties saved yet, start with empty form
              }
              materialProperties={
                // Pass material properties for calculations
                params?.materialProperties &&
                typeof params.materialProperties === 'object' &&
                'category' in params.materialProperties
                  ? (params.materialProperties as any) // Wood material properties
                  : undefined // No material properties available yet
              }
              onSave={() => handleItemSaved('2')}
            />
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="3">
          <AccordionTrigger>
            <h3 className="text-lg font-medium">
              {t('preIntervationCheck.title')}
            </h3>
          </AccordionTrigger>
          <AccordionContent>
            <WoodPreInterventionCheckForm
              session={session}
              projectId={projectId}
              moduleId={moduleId}
              initialDeformation={params?.initialDeformation}
              defaultValues={
                // Wood pre-intervention check properties are stored in preIntervationCheck field
                params?.preIntervationCheck &&
                typeof params.preIntervationCheck === 'object'
                  ? (params.preIntervationCheck as any) // It's wood pre-intervention check properties
                  : undefined // No pre-intervention check properties saved yet, start with empty form
              }
              geometryProperties={
                // Pass geometry properties for calculations
                params?.geometry &&
                typeof params.geometry === 'object' &&
                'beamSectionWidth' in params.geometry
                  ? (params.geometry as any) // Wood geometry properties
                  : undefined // No geometry properties available yet
              }
              onSave={() => handleItemSaved('3')}
            />
          </AccordionContent>
        </AccordionItem>
      </AccordionComponent>
      {success && (
        <>
          <Separator />
          <WoodPostInterventionForm
            module={module}
            session={session}
            projectId={projectId}
            moduleId={moduleId}
          />
          <div className="pt-4">
                      <ModuleReportGenerationSection
                        moduleId={moduleId}
                        enabled={enableReport}
                        projectId={projectId}
                        session={session}
                      />
                    </div>
        </>
      )}
    </div>
  )
}
