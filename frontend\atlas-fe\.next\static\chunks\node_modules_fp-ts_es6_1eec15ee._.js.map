{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/fp-ts/es6/function.js"], "sourcesContent": ["var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\n// -------------------------------------------------------------------------------------\n// instances\n// -------------------------------------------------------------------------------------\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var getBooleanAlgebra = function (B) {\n    return function () { return ({\n        meet: function (x, y) { return function (a) { return B.meet(x(a), y(a)); }; },\n        join: function (x, y) { return function (a) { return B.join(x(a), y(a)); }; },\n        zero: function () { return B.zero; },\n        one: function () { return B.one; },\n        implies: function (x, y) { return function (a) { return B.implies(x(a), y(a)); }; },\n        not: function (x) { return function (a) { return B.not(x(a)); }; }\n    }); };\n};\n/**\n * Unary functions form a semigroup as long as you can provide a semigroup for the codomain.\n *\n * @example\n * import { Predicate, getSemigroup } from 'fp-ts/function'\n * import * as B from 'fp-ts/boolean'\n *\n * const f: Predicate<number> = (n) => n <= 2\n * const g: Predicate<number> = (n) => n >= 0\n *\n * const S1 = getSemigroup(B.SemigroupAll)<number>()\n *\n * assert.deepStrictEqual(S1.concat(f, g)(1), true)\n * assert.deepStrictEqual(S1.concat(f, g)(3), false)\n *\n * const S2 = getSemigroup(B.SemigroupAny)<number>()\n *\n * assert.deepStrictEqual(S2.concat(f, g)(1), true)\n * assert.deepStrictEqual(S2.concat(f, g)(3), true)\n *\n * @category instances\n * @since 2.10.0\n */\nexport var getSemigroup = function (S) {\n    return function () { return ({\n        concat: function (f, g) { return function (a) { return S.concat(f(a), g(a)); }; }\n    }); };\n};\n/**\n * Unary functions form a monoid as long as you can provide a monoid for the codomain.\n *\n * @example\n * import { Predicate } from 'fp-ts/Predicate'\n * import { getMonoid } from 'fp-ts/function'\n * import * as B from 'fp-ts/boolean'\n *\n * const f: Predicate<number> = (n) => n <= 2\n * const g: Predicate<number> = (n) => n >= 0\n *\n * const M1 = getMonoid(B.MonoidAll)<number>()\n *\n * assert.deepStrictEqual(M1.concat(f, g)(1), true)\n * assert.deepStrictEqual(M1.concat(f, g)(3), false)\n *\n * const M2 = getMonoid(B.MonoidAny)<number>()\n *\n * assert.deepStrictEqual(M2.concat(f, g)(1), true)\n * assert.deepStrictEqual(M2.concat(f, g)(3), true)\n *\n * @category instances\n * @since 2.10.0\n */\nexport var getMonoid = function (M) {\n    var getSemigroupM = getSemigroup(M);\n    return function () { return ({\n        concat: getSemigroupM().concat,\n        empty: function () { return M.empty; }\n    }); };\n};\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var getSemiring = function (S) { return ({\n    add: function (f, g) { return function (x) { return S.add(f(x), g(x)); }; },\n    zero: function () { return S.zero; },\n    mul: function (f, g) { return function (x) { return S.mul(f(x), g(x)); }; },\n    one: function () { return S.one; }\n}); };\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var getRing = function (R) {\n    var S = getSemiring(R);\n    return {\n        add: S.add,\n        mul: S.mul,\n        one: S.one,\n        zero: S.zero,\n        sub: function (f, g) { return function (x) { return R.sub(f(x), g(x)); }; }\n    };\n};\n// -------------------------------------------------------------------------------------\n// utils\n// -------------------------------------------------------------------------------------\n/**\n * @since 2.11.0\n */\nexport var apply = function (a) {\n    return function (f) {\n        return f(a);\n    };\n};\n/**\n * @since 2.0.0\n */\nexport function identity(a) {\n    return a;\n}\n/**\n * @since 2.0.0\n */\nexport var unsafeCoerce = identity;\n/**\n * @since 2.0.0\n */\nexport function constant(a) {\n    return function () { return a; };\n}\n/**\n * A thunk that returns always `true`.\n *\n * @since 2.0.0\n */\nexport var constTrue = /*#__PURE__*/ constant(true);\n/**\n * A thunk that returns always `false`.\n *\n * @since 2.0.0\n */\nexport var constFalse = /*#__PURE__*/ constant(false);\n/**\n * A thunk that returns always `null`.\n *\n * @since 2.0.0\n */\nexport var constNull = /*#__PURE__*/ constant(null);\n/**\n * A thunk that returns always `undefined`.\n *\n * @since 2.0.0\n */\nexport var constUndefined = /*#__PURE__*/ constant(undefined);\n/**\n * A thunk that returns always `void`.\n *\n * @since 2.0.0\n */\nexport var constVoid = constUndefined;\nexport function flip(f) {\n    return function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        if (args.length > 1) {\n            return f(args[1], args[0]);\n        }\n        return function (a) { return f(a)(args[0]); };\n    };\n}\nexport function flow(ab, bc, cd, de, ef, fg, gh, hi, ij) {\n    switch (arguments.length) {\n        case 1:\n            return ab;\n        case 2:\n            return function () {\n                return bc(ab.apply(this, arguments));\n            };\n        case 3:\n            return function () {\n                return cd(bc(ab.apply(this, arguments)));\n            };\n        case 4:\n            return function () {\n                return de(cd(bc(ab.apply(this, arguments))));\n            };\n        case 5:\n            return function () {\n                return ef(de(cd(bc(ab.apply(this, arguments)))));\n            };\n        case 6:\n            return function () {\n                return fg(ef(de(cd(bc(ab.apply(this, arguments))))));\n            };\n        case 7:\n            return function () {\n                return gh(fg(ef(de(cd(bc(ab.apply(this, arguments)))))));\n            };\n        case 8:\n            return function () {\n                return hi(gh(fg(ef(de(cd(bc(ab.apply(this, arguments))))))));\n            };\n        case 9:\n            return function () {\n                return ij(hi(gh(fg(ef(de(cd(bc(ab.apply(this, arguments)))))))));\n            };\n    }\n    return;\n}\n/**\n * @since 2.0.0\n */\nexport function tuple() {\n    var t = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        t[_i] = arguments[_i];\n    }\n    return t;\n}\n/**\n * @since 2.0.0\n */\nexport function increment(n) {\n    return n + 1;\n}\n/**\n * @since 2.0.0\n */\nexport function decrement(n) {\n    return n - 1;\n}\n/**\n * @since 2.0.0\n */\nexport function absurd(_) {\n    throw new Error('Called `absurd` function which should be uncallable');\n}\n/**\n * Creates a tupled version of this function: instead of `n` arguments, it accepts a single tuple argument.\n *\n * @example\n * import { tupled } from 'fp-ts/function'\n *\n * const add = tupled((x: number, y: number): number => x + y)\n *\n * assert.strictEqual(add([1, 2]), 3)\n *\n * @since 2.4.0\n */\nexport function tupled(f) {\n    return function (a) { return f.apply(void 0, a); };\n}\n/**\n * Inverse function of `tupled`\n *\n * @since 2.4.0\n */\nexport function untupled(f) {\n    return function () {\n        var a = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            a[_i] = arguments[_i];\n        }\n        return f(a);\n    };\n}\nexport function pipe(a, ab, bc, cd, de, ef, fg, gh, hi) {\n    switch (arguments.length) {\n        case 1:\n            return a;\n        case 2:\n            return ab(a);\n        case 3:\n            return bc(ab(a));\n        case 4:\n            return cd(bc(ab(a)));\n        case 5:\n            return de(cd(bc(ab(a))));\n        case 6:\n            return ef(de(cd(bc(ab(a)))));\n        case 7:\n            return fg(ef(de(cd(bc(ab(a))))));\n        case 8:\n            return gh(fg(ef(de(cd(bc(ab(a)))))));\n        case 9:\n            return hi(gh(fg(ef(de(cd(bc(ab(a))))))));\n        default: {\n            var ret = arguments[0];\n            for (var i = 1; i < arguments.length; i++) {\n                ret = arguments[i](ret);\n            }\n            return ret;\n        }\n    }\n}\n/**\n * Type hole simulation\n *\n * @since 2.7.0\n */\nexport var hole = absurd;\n/**\n * @since 2.11.0\n */\nexport var SK = function (_, b) { return b; };\n/**\n * Use `Predicate` module instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport function not(predicate) {\n    return function (a) { return !predicate(a); };\n}\n/**\n * Use `Endomorphism` module instead.\n *\n * @category zone of death\n * @since 2.10.0\n * @deprecated\n */\nexport var getEndomorphismMonoid = function () { return ({\n    concat: function (first, second) { return flow(first, second); },\n    empty: identity\n}); };\n/** @internal */\nexport var dual = function (arity, body) {\n    var isDataFirst = typeof arity === 'number' ? function (args) { return args.length >= arity; } : arity;\n    return function () {\n        var args = Array.from(arguments);\n        if (isDataFirst(arguments)) {\n            return body.apply(this, args);\n        }\n        return function (self) { return body.apply(void 0, __spreadArray([self], args, false)); };\n    };\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAI,gBAAgB,4CAAS,yCAAK,aAAa,IAAK,SAAU,EAAE,EAAE,IAAI,EAAE,IAAI;IACxE,IAAI,QAAQ,UAAU,MAAM,KAAK,GAAG,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,GAAG,IAAK;QACjF,IAAI,MAAM,CAAC,CAAC,KAAK,IAAI,GAAG;YACpB,IAAI,CAAC,IAAI,KAAK,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG;YAClD,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;QACnB;IACJ;IACA,OAAO,GAAG,MAAM,CAAC,MAAM,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;AACtD;AAQO,IAAI,oBAAoB,SAAU,CAAC;IACtC,OAAO;QAAc,OAAQ;YACzB,MAAM,SAAU,CAAC,EAAE,CAAC;gBAAI,OAAO,SAAU,CAAC;oBAAI,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE;gBAAK;YAAG;YAC5E,MAAM,SAAU,CAAC,EAAE,CAAC;gBAAI,OAAO,SAAU,CAAC;oBAAI,OAAO,EAAE,IAAI,CAAC,EAAE,IAAI,EAAE;gBAAK;YAAG;YAC5E,MAAM;gBAAc,OAAO,EAAE,IAAI;YAAE;YACnC,KAAK;gBAAc,OAAO,EAAE,GAAG;YAAE;YACjC,SAAS,SAAU,CAAC,EAAE,CAAC;gBAAI,OAAO,SAAU,CAAC;oBAAI,OAAO,EAAE,OAAO,CAAC,EAAE,IAAI,EAAE;gBAAK;YAAG;YAClF,KAAK,SAAU,CAAC;gBAAI,OAAO,SAAU,CAAC;oBAAI,OAAO,EAAE,GAAG,CAAC,EAAE;gBAAK;YAAG;QACrE;IAAI;AACR;AAwBO,IAAI,eAAe,SAAU,CAAC;IACjC,OAAO;QAAc,OAAQ;YACzB,QAAQ,SAAU,CAAC,EAAE,CAAC;gBAAI,OAAO,SAAU,CAAC;oBAAI,OAAO,EAAE,MAAM,CAAC,EAAE,IAAI,EAAE;gBAAK;YAAG;QACpF;IAAI;AACR;AAyBO,IAAI,YAAY,SAAU,CAAC;IAC9B,IAAI,gBAAgB,aAAa;IACjC,OAAO;QAAc,OAAQ;YACzB,QAAQ,gBAAgB,MAAM;YAC9B,OAAO;gBAAc,OAAO,EAAE,KAAK;YAAE;QACzC;IAAI;AACR;AAKO,IAAI,cAAc,SAAU,CAAC;IAAI,OAAQ;QAC5C,KAAK,SAAU,CAAC,EAAE,CAAC;YAAI,OAAO,SAAU,CAAC;gBAAI,OAAO,EAAE,GAAG,CAAC,EAAE,IAAI,EAAE;YAAK;QAAG;QAC1E,MAAM;YAAc,OAAO,EAAE,IAAI;QAAE;QACnC,KAAK,SAAU,CAAC,EAAE,CAAC;YAAI,OAAO,SAAU,CAAC;gBAAI,OAAO,EAAE,GAAG,CAAC,EAAE,IAAI,EAAE;YAAK;QAAG;QAC1E,KAAK;YAAc,OAAO,EAAE,GAAG;QAAE;IACrC;AAAI;AAKG,IAAI,UAAU,SAAU,CAAC;IAC5B,IAAI,IAAI,YAAY;IACpB,OAAO;QACH,KAAK,EAAE,GAAG;QACV,KAAK,EAAE,GAAG;QACV,KAAK,EAAE,GAAG;QACV,MAAM,EAAE,IAAI;QACZ,KAAK,SAAU,CAAC,EAAE,CAAC;YAAI,OAAO,SAAU,CAAC;gBAAI,OAAO,EAAE,GAAG,CAAC,EAAE,IAAI,EAAE;YAAK;QAAG;IAC9E;AACJ;AAOO,IAAI,QAAQ,SAAU,CAAC;IAC1B,OAAO,SAAU,CAAC;QACd,OAAO,EAAE;IACb;AACJ;AAIO,SAAS,SAAS,CAAC;IACtB,OAAO;AACX;AAIO,IAAI,eAAe;AAInB,SAAS,SAAS,CAAC;IACtB,OAAO;QAAc,OAAO;IAAG;AACnC;AAMO,IAAI,YAAY,WAAW,GAAG,SAAS;AAMvC,IAAI,aAAa,WAAW,GAAG,SAAS;AAMxC,IAAI,YAAY,WAAW,GAAG,SAAS;AAMvC,IAAI,iBAAiB,WAAW,GAAG,SAAS;AAM5C,IAAI,YAAY;AAChB,SAAS,KAAK,CAAC;IAClB,OAAO;QACH,IAAI,OAAO,EAAE;QACb,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;YAC1C,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;QAC5B;QACA,IAAI,KAAK,MAAM,GAAG,GAAG;YACjB,OAAO,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;QAC7B;QACA,OAAO,SAAU,CAAC;YAAI,OAAO,EAAE,GAAG,IAAI,CAAC,EAAE;QAAG;IAChD;AACJ;AACO,SAAS,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;IACnD,OAAQ,UAAU,MAAM;QACpB,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;gBACH,OAAO,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE;YAC7B;QACJ,KAAK;YACD,OAAO;gBACH,OAAO,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE;YAChC;QACJ,KAAK;YACD,OAAO;gBACH,OAAO,GAAG,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE;YACnC;QACJ,KAAK;YACD,OAAO;gBACH,OAAO,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE;YACtC;QACJ,KAAK;YACD,OAAO;gBACH,OAAO,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE;YACzC;QACJ,KAAK;YACD,OAAO;gBACH,OAAO,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE;YAC5C;QACJ,KAAK;YACD,OAAO;gBACH,OAAO,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE;YAC/C;QACJ,KAAK;YACD,OAAO;gBACH,OAAO,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,KAAK,CAAC,IAAI,EAAE;YAClD;IACR;IACA;AACJ;AAIO,SAAS;IACZ,IAAI,IAAI,EAAE;IACV,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;QAC1C,CAAC,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;IACzB;IACA,OAAO;AACX;AAIO,SAAS,UAAU,CAAC;IACvB,OAAO,IAAI;AACf;AAIO,SAAS,UAAU,CAAC;IACvB,OAAO,IAAI;AACf;AAIO,SAAS,OAAO,CAAC;IACpB,MAAM,IAAI,MAAM;AACpB;AAaO,SAAS,OAAO,CAAC;IACpB,OAAO,SAAU,CAAC;QAAI,OAAO,EAAE,KAAK,CAAC,KAAK,GAAG;IAAI;AACrD;AAMO,SAAS,SAAS,CAAC;IACtB,OAAO;QACH,IAAI,IAAI,EAAE;QACV,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;YAC1C,CAAC,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;QACzB;QACA,OAAO,EAAE;IACb;AACJ;AACO,SAAS,KAAK,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;IAClD,OAAQ,UAAU,MAAM;QACpB,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO,GAAG;QACd,KAAK;YACD,OAAO,GAAG,GAAG;QACjB,KAAK;YACD,OAAO,GAAG,GAAG,GAAG;QACpB,KAAK;YACD,OAAO,GAAG,GAAG,GAAG,GAAG;QACvB,KAAK;YACD,OAAO,GAAG,GAAG,GAAG,GAAG,GAAG;QAC1B,KAAK;YACD,OAAO,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;QAC7B,KAAK;YACD,OAAO,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;QAChC,KAAK;YACD,OAAO,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;QACnC;YAAS;gBACL,IAAI,MAAM,SAAS,CAAC,EAAE;gBACtB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;oBACvC,MAAM,SAAS,CAAC,EAAE,CAAC;gBACvB;gBACA,OAAO;YACX;IACJ;AACJ;AAMO,IAAI,OAAO;AAIX,IAAI,KAAK,SAAU,CAAC,EAAE,CAAC;IAAI,OAAO;AAAG;AAQrC,SAAS,IAAI,SAAS;IACzB,OAAO,SAAU,CAAC;QAAI,OAAO,CAAC,UAAU;IAAI;AAChD;AAQO,IAAI,wBAAwB;IAAc,OAAQ;QACrD,QAAQ,SAAU,KAAK,EAAE,MAAM;YAAI,OAAO,KAAK,OAAO;QAAS;QAC/D,OAAO;IACX;AAAI;AAEG,IAAI,OAAO,SAAU,KAAK,EAAE,IAAI;IACnC,IAAI,cAAc,OAAO,UAAU,WAAW,SAAU,IAAI;QAAI,OAAO,KAAK,MAAM,IAAI;IAAO,IAAI;IACjG,OAAO;QACH,IAAI,OAAO,MAAM,IAAI,CAAC;QACtB,IAAI,YAAY,YAAY;YACxB,OAAO,KAAK,KAAK,CAAC,IAAI,EAAE;QAC5B;QACA,OAAO,SAAU,IAAI;YAAI,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,cAAc;gBAAC;aAAK,EAAE,MAAM;QAAS;IAC5F;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/fp-ts/es6/internal.js"], "sourcesContent": ["var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {\n    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n        if (ar || !(i in from)) {\n            if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n            ar[i] = from[i];\n        }\n    }\n    return to.concat(ar || Array.prototype.slice.call(from));\n};\nimport { dual } from './function';\n// -------------------------------------------------------------------------------------\n// Option\n// -------------------------------------------------------------------------------------\n/** @internal */\nexport var isNone = function (fa) { return fa._tag === 'None'; };\n/** @internal */\nexport var isSome = function (fa) { return fa._tag === 'Some'; };\n/** @internal */\nexport var none = { _tag: 'None' };\n/** @internal */\nexport var some = function (a) { return ({ _tag: 'Some', value: a }); };\n// -------------------------------------------------------------------------------------\n// Either\n// -------------------------------------------------------------------------------------\n/** @internal */\nexport var isLeft = function (ma) { return ma._tag === 'Left'; };\n/** @internal */\nexport var isRight = function (ma) { return ma._tag === 'Right'; };\n/** @internal */\nexport var left = function (e) { return ({ _tag: 'Left', left: e }); };\n/** @internal */\nexport var right = function (a) { return ({ _tag: 'Right', right: a }); };\n// -------------------------------------------------------------------------------------\n// ReadonlyNonEmptyArray\n// -------------------------------------------------------------------------------------\n/** @internal */\nexport var singleton = function (a) { return [a]; };\n/** @internal */\nexport var isNonEmpty = function (as) { return as.length > 0; };\n/** @internal */\nexport var head = function (as) { return as[0]; };\n/** @internal */\nexport var tail = function (as) { return as.slice(1); };\n// -------------------------------------------------------------------------------------\n// empty\n// -------------------------------------------------------------------------------------\n/** @internal */\nexport var emptyReadonlyArray = [];\n/** @internal */\nexport var emptyRecord = {};\n// -------------------------------------------------------------------------------------\n// Record\n// -------------------------------------------------------------------------------------\n/** @internal */\nexport var has = Object.prototype.hasOwnProperty;\n// -------------------------------------------------------------------------------------\n// NonEmptyArray\n// -------------------------------------------------------------------------------------\n/** @internal */\nexport var fromReadonlyNonEmptyArray = function (as) { return __spreadArray([as[0]], as.slice(1), true); };\n/** @internal */\nexport var liftNullable = function (F) {\n    return function (f, onNullable) {\n        return function () {\n            var a = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                a[_i] = arguments[_i];\n            }\n            var o = f.apply(void 0, a);\n            return F.fromEither(o == null ? left(onNullable.apply(void 0, a)) : right(o));\n        };\n    };\n};\n/** @internal */\nexport var liftOption = function (F) {\n    return function (f, onNone) {\n        return function () {\n            var a = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                a[_i] = arguments[_i];\n            }\n            var o = f.apply(void 0, a);\n            return F.fromEither(isNone(o) ? left(onNone.apply(void 0, a)) : right(o.value));\n        };\n    };\n};\n/** @internal */\nexport var flatMapNullable = function (F, M) {\n    return /*#__PURE__*/ dual(3, function (self, f, onNullable) {\n        return M.flatMap(self, liftNullable(F)(f, onNullable));\n    });\n};\n/** @internal */\nexport var flatMapOption = function (F, M) {\n    return /*#__PURE__*/ dual(3, function (self, f, onNone) { return M.flatMap(self, liftOption(F)(f, onNone)); });\n};\n/** @internal */\nexport var flatMapEither = function (F, M) {\n    return /*#__PURE__*/ dual(2, function (self, f) {\n        return M.flatMap(self, function (a) { return F.fromEither(f(a)); });\n    });\n};\n/** @internal */\nexport var flatMapIO = function (F, M) {\n    return /*#__PURE__*/ dual(2, function (self, f) {\n        return M.flatMap(self, function (a) { return F.fromIO(f(a)); });\n    });\n};\n/** @internal */\nexport var flatMapTask = function (F, M) {\n    return /*#__PURE__*/ dual(2, function (self, f) {\n        return M.flatMap(self, function (a) { return F.fromTask(f(a)); });\n    });\n};\n/** @internal */\nexport var flatMapReader = function (F, M) {\n    return /*#__PURE__*/ dual(2, function (self, f) {\n        return M.flatMap(self, function (a) { return F.fromReader(f(a)); });\n    });\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA;AATA,IAAI,gBAAgB,4CAAS,yCAAK,aAAa,IAAK,SAAU,EAAE,EAAE,IAAI,EAAE,IAAI;IACxE,IAAI,QAAQ,UAAU,MAAM,KAAK,GAAG,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,GAAG,IAAK;QACjF,IAAI,MAAM,CAAC,CAAC,KAAK,IAAI,GAAG;YACpB,IAAI,CAAC,IAAI,KAAK,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG;YAClD,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;QACnB;IACJ;IACA,OAAO,GAAG,MAAM,CAAC,MAAM,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;AACtD;;AAMO,IAAI,SAAS,SAAU,EAAE;IAAI,OAAO,GAAG,IAAI,KAAK;AAAQ;AAExD,IAAI,SAAS,SAAU,EAAE;IAAI,OAAO,GAAG,IAAI,KAAK;AAAQ;AAExD,IAAI,OAAO;IAAE,MAAM;AAAO;AAE1B,IAAI,OAAO,SAAU,CAAC;IAAI,OAAQ;QAAE,MAAM;QAAQ,OAAO;IAAE;AAAI;AAK/D,IAAI,SAAS,SAAU,EAAE;IAAI,OAAO,GAAG,IAAI,KAAK;AAAQ;AAExD,IAAI,UAAU,SAAU,EAAE;IAAI,OAAO,GAAG,IAAI,KAAK;AAAS;AAE1D,IAAI,OAAO,SAAU,CAAC;IAAI,OAAQ;QAAE,MAAM;QAAQ,MAAM;IAAE;AAAI;AAE9D,IAAI,QAAQ,SAAU,CAAC;IAAI,OAAQ;QAAE,MAAM;QAAS,OAAO;IAAE;AAAI;AAKjE,IAAI,YAAY,SAAU,CAAC;IAAI,OAAO;QAAC;KAAE;AAAE;AAE3C,IAAI,aAAa,SAAU,EAAE;IAAI,OAAO,GAAG,MAAM,GAAG;AAAG;AAEvD,IAAI,OAAO,SAAU,EAAE;IAAI,OAAO,EAAE,CAAC,EAAE;AAAE;AAEzC,IAAI,OAAO,SAAU,EAAE;IAAI,OAAO,GAAG,KAAK,CAAC;AAAI;AAK/C,IAAI,qBAAqB,EAAE;AAE3B,IAAI,cAAc,CAAC;AAKnB,IAAI,MAAM,OAAO,SAAS,CAAC,cAAc;AAKzC,IAAI,4BAA4B,SAAU,EAAE;IAAI,OAAO,cAAc;QAAC,EAAE,CAAC,EAAE;KAAC,EAAE,GAAG,KAAK,CAAC,IAAI;AAAO;AAElG,IAAI,eAAe,SAAU,CAAC;IACjC,OAAO,SAAU,CAAC,EAAE,UAAU;QAC1B,OAAO;YACH,IAAI,IAAI,EAAE;YACV,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;gBAC1C,CAAC,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;YACzB;YACA,IAAI,IAAI,EAAE,KAAK,CAAC,KAAK,GAAG;YACxB,OAAO,EAAE,UAAU,CAAC,KAAK,OAAO,KAAK,WAAW,KAAK,CAAC,KAAK,GAAG,MAAM,MAAM;QAC9E;IACJ;AACJ;AAEO,IAAI,aAAa,SAAU,CAAC;IAC/B,OAAO,SAAU,CAAC,EAAE,MAAM;QACtB,OAAO;YACH,IAAI,IAAI,EAAE;YACV,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;gBAC1C,CAAC,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;YACzB;YACA,IAAI,IAAI,EAAE,KAAK,CAAC,KAAK,GAAG;YACxB,OAAO,EAAE,UAAU,CAAC,OAAO,KAAK,KAAK,OAAO,KAAK,CAAC,KAAK,GAAG,MAAM,MAAM,EAAE,KAAK;QACjF;IACJ;AACJ;AAEO,IAAI,kBAAkB,SAAU,CAAC,EAAE,CAAC;IACvC,OAAO,WAAW,GAAG,IAAA,sJAAI,EAAC,GAAG,SAAU,IAAI,EAAE,CAAC,EAAE,UAAU;QACtD,OAAO,EAAE,OAAO,CAAC,MAAM,aAAa,GAAG,GAAG;IAC9C;AACJ;AAEO,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;IACrC,OAAO,WAAW,GAAG,IAAA,sJAAI,EAAC,GAAG,SAAU,IAAI,EAAE,CAAC,EAAE,MAAM;QAAI,OAAO,EAAE,OAAO,CAAC,MAAM,WAAW,GAAG,GAAG;IAAU;AAChH;AAEO,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;IACrC,OAAO,WAAW,GAAG,IAAA,sJAAI,EAAC,GAAG,SAAU,IAAI,EAAE,CAAC;QAC1C,OAAO,EAAE,OAAO,CAAC,MAAM,SAAU,CAAC;YAAI,OAAO,EAAE,UAAU,CAAC,EAAE;QAAK;IACrE;AACJ;AAEO,IAAI,YAAY,SAAU,CAAC,EAAE,CAAC;IACjC,OAAO,WAAW,GAAG,IAAA,sJAAI,EAAC,GAAG,SAAU,IAAI,EAAE,CAAC;QAC1C,OAAO,EAAE,OAAO,CAAC,MAAM,SAAU,CAAC;YAAI,OAAO,EAAE,MAAM,CAAC,EAAE;QAAK;IACjE;AACJ;AAEO,IAAI,cAAc,SAAU,CAAC,EAAE,CAAC;IACnC,OAAO,WAAW,GAAG,IAAA,sJAAI,EAAC,GAAG,SAAU,IAAI,EAAE,CAAC;QAC1C,OAAO,EAAE,OAAO,CAAC,MAAM,SAAU,CAAC;YAAI,OAAO,EAAE,QAAQ,CAAC,EAAE;QAAK;IACnE;AACJ;AAEO,IAAI,gBAAgB,SAAU,CAAC,EAAE,CAAC;IACrC,OAAO,WAAW,GAAG,IAAA,sJAAI,EAAC,GAAG,SAAU,IAAI,EAAE,CAAC;QAC1C,OAAO,EAAE,OAAO,CAAC,MAAM,SAAU,CAAC;YAAI,OAAO,EAAE,UAAU,CAAC,EAAE;QAAK;IACrE;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 510, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/fp-ts/es6/Apply.js"], "sourcesContent": ["/**\n * The `Apply` class provides the `ap` which is used to apply a function to an argument under a type constructor.\n *\n * `Apply` can be used to lift functions of two or more arguments to work on values wrapped with the type constructor\n * `f`.\n *\n * Instances must satisfy the following law in addition to the `Functor` laws:\n *\n * 1. Associative composition: `F.ap(F.ap(F.map(fbc, bc => ab => a => bc(ab(a))), fab), fa) <-> F.ap(fbc, F.ap(fab, fa))`\n *\n * Formally, `Apply` represents a strong lax semi-monoidal endofunctor.\n *\n * @example\n * import * as O from 'fp-ts/Option'\n * import { pipe } from 'fp-ts/function'\n *\n * const f = (a: string) => (b: number) => (c: boolean) => a + String(b) + String(c)\n * const fa: O.Option<string> = O.some('s')\n * const fb: O.Option<number> = O.some(1)\n * const fc: O.Option<boolean> = O.some(true)\n *\n * assert.deepStrictEqual(\n *   pipe(\n *     // lift a function\n *     O.some(f),\n *     // apply the first argument\n *     O.ap(fa),\n *     // apply the second argument\n *     O.ap(fb),\n *     // apply the third argument\n *     O.ap(fc)\n *   ),\n *   O.some('s1true')\n * )\n *\n * @since 2.0.0\n */\nimport { tuple } from './function';\nimport * as _ from './internal';\nexport function ap(F, G) {\n    return function (fa) {\n        return function (fab) {\n            return F.ap(F.map(fab, function (gab) { return function (ga) { return G.ap(gab, ga); }; }), fa);\n        };\n    };\n}\nexport function apFirst(A) {\n    return function (second) { return function (first) {\n        return A.ap(A.map(first, function (a) { return function () { return a; }; }), second);\n    }; };\n}\nexport function apSecond(A) {\n    return function (second) {\n        return function (first) {\n            return A.ap(A.map(first, function () { return function (b) { return b; }; }), second);\n        };\n    };\n}\nexport function apS(F) {\n    return function (name, fb) {\n        return function (fa) {\n            return F.ap(F.map(fa, function (a) { return function (b) {\n                var _a;\n                return Object.assign({}, a, (_a = {}, _a[name] = b, _a));\n            }; }), fb);\n        };\n    };\n}\nexport function getApplySemigroup(F) {\n    return function (S) { return ({\n        concat: function (first, second) {\n            return F.ap(F.map(first, function (x) { return function (y) { return S.concat(x, y); }; }), second);\n        }\n    }); };\n}\nfunction curried(f, n, acc) {\n    return function (x) {\n        var combined = Array(acc.length + 1);\n        for (var i = 0; i < acc.length; i++) {\n            combined[i] = acc[i];\n        }\n        combined[acc.length] = x;\n        return n === 0 ? f.apply(null, combined) : curried(f, n - 1, combined);\n    };\n}\nvar tupleConstructors = {\n    1: function (a) { return [a]; },\n    2: function (a) { return function (b) { return [a, b]; }; },\n    3: function (a) { return function (b) { return function (c) { return [a, b, c]; }; }; },\n    4: function (a) { return function (b) { return function (c) { return function (d) { return [a, b, c, d]; }; }; }; },\n    5: function (a) { return function (b) { return function (c) { return function (d) { return function (e) { return [a, b, c, d, e]; }; }; }; }; }\n};\nfunction getTupleConstructor(len) {\n    if (!_.has.call(tupleConstructors, len)) {\n        tupleConstructors[len] = curried(tuple, len - 1, []);\n    }\n    return tupleConstructors[len];\n}\nexport function sequenceT(F) {\n    return function () {\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        var len = args.length;\n        var f = getTupleConstructor(len);\n        var fas = F.map(args[0], f);\n        for (var i = 1; i < len; i++) {\n            fas = F.ap(fas, args[i]);\n        }\n        return fas;\n    };\n}\nfunction getRecordConstructor(keys) {\n    var len = keys.length;\n    switch (len) {\n        case 1:\n            return function (a) {\n                var _a;\n                return (_a = {}, _a[keys[0]] = a, _a);\n            };\n        case 2:\n            return function (a) { return function (b) {\n                var _a;\n                return (_a = {}, _a[keys[0]] = a, _a[keys[1]] = b, _a);\n            }; };\n        case 3:\n            return function (a) { return function (b) { return function (c) {\n                var _a;\n                return (_a = {}, _a[keys[0]] = a, _a[keys[1]] = b, _a[keys[2]] = c, _a);\n            }; }; };\n        case 4:\n            return function (a) { return function (b) { return function (c) { return function (d) {\n                var _a;\n                return (_a = {},\n                    _a[keys[0]] = a,\n                    _a[keys[1]] = b,\n                    _a[keys[2]] = c,\n                    _a[keys[3]] = d,\n                    _a);\n            }; }; }; };\n        case 5:\n            return function (a) { return function (b) { return function (c) { return function (d) { return function (e) {\n                var _a;\n                return (_a = {},\n                    _a[keys[0]] = a,\n                    _a[keys[1]] = b,\n                    _a[keys[2]] = c,\n                    _a[keys[3]] = d,\n                    _a[keys[4]] = e,\n                    _a);\n            }; }; }; }; };\n        default:\n            return curried(function () {\n                var args = [];\n                for (var _i = 0; _i < arguments.length; _i++) {\n                    args[_i] = arguments[_i];\n                }\n                var r = {};\n                for (var i = 0; i < len; i++) {\n                    r[keys[i]] = args[i];\n                }\n                return r;\n            }, len - 1, []);\n    }\n}\nexport function sequenceS(F) {\n    return function (r) {\n        var keys = Object.keys(r);\n        var len = keys.length;\n        var f = getRecordConstructor(keys);\n        var fr = F.map(r[keys[0]], f);\n        for (var i = 1; i < len; i++) {\n            fr = F.ap(fr, r[keys[i]]);\n        }\n        return fr;\n    };\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAoCC;;;;;;;;;;;;;;;;AACD;AACA;;;AACO,SAAS,GAAG,CAAC,EAAE,CAAC;IACnB,OAAO,SAAU,EAAE;QACf,OAAO,SAAU,GAAG;YAChB,OAAO,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,KAAK,SAAU,GAAG;gBAAI,OAAO,SAAU,EAAE;oBAAI,OAAO,EAAE,EAAE,CAAC,KAAK;gBAAK;YAAG,IAAI;QAChG;IACJ;AACJ;AACO,SAAS,QAAQ,CAAC;IACrB,OAAO,SAAU,MAAM;QAAI,OAAO,SAAU,KAAK;YAC7C,OAAO,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,OAAO,SAAU,CAAC;gBAAI,OAAO;oBAAc,OAAO;gBAAG;YAAG,IAAI;QAClF;IAAG;AACP;AACO,SAAS,SAAS,CAAC;IACtB,OAAO,SAAU,MAAM;QACnB,OAAO,SAAU,KAAK;YAClB,OAAO,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,OAAO;gBAAc,OAAO,SAAU,CAAC;oBAAI,OAAO;gBAAG;YAAG,IAAI;QAClF;IACJ;AACJ;AACO,SAAS,IAAI,CAAC;IACjB,OAAO,SAAU,IAAI,EAAE,EAAE;QACrB,OAAO,SAAU,EAAE;YACf,OAAO,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,IAAI,SAAU,CAAC;gBAAI,OAAO,SAAU,CAAC;oBACnD,IAAI;oBACJ,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,GAAG,GAAG,EAAE;gBAC1D;YAAG,IAAI;QACX;IACJ;AACJ;AACO,SAAS,kBAAkB,CAAC;IAC/B,OAAO,SAAU,CAAC;QAAI,OAAQ;YAC1B,QAAQ,SAAU,KAAK,EAAE,MAAM;gBAC3B,OAAO,EAAE,EAAE,CAAC,EAAE,GAAG,CAAC,OAAO,SAAU,CAAC;oBAAI,OAAO,SAAU,CAAC;wBAAI,OAAO,EAAE,MAAM,CAAC,GAAG;oBAAI;gBAAG,IAAI;YAChG;QACJ;IAAI;AACR;AACA,SAAS,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG;IACtB,OAAO,SAAU,CAAC;QACd,IAAI,WAAW,MAAM,IAAI,MAAM,GAAG;QAClC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;YACjC,QAAQ,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE;QACxB;QACA,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QACvB,OAAO,MAAM,IAAI,EAAE,KAAK,CAAC,MAAM,YAAY,QAAQ,GAAG,IAAI,GAAG;IACjE;AACJ;AACA,IAAI,oBAAoB;IACpB,GAAG,SAAU,CAAC;QAAI,OAAO;YAAC;SAAE;IAAE;IAC9B,GAAG,SAAU,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO;gBAAC;gBAAG;aAAE;QAAE;IAAG;IAC1D,GAAG,SAAU,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,SAAU,CAAC;gBAAI,OAAO;oBAAC;oBAAG;oBAAG;iBAAE;YAAE;QAAG;IAAG;IACtF,GAAG,SAAU,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,SAAU,CAAC;gBAAI,OAAO,SAAU,CAAC;oBAAI,OAAO;wBAAC;wBAAG;wBAAG;wBAAG;qBAAE;gBAAE;YAAG;QAAG;IAAG;IAClH,GAAG,SAAU,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,SAAU,CAAC;gBAAI,OAAO,SAAU,CAAC;oBAAI,OAAO,SAAU,CAAC;wBAAI,OAAO;4BAAC;4BAAG;4BAAG;4BAAG;4BAAG;yBAAE;oBAAE;gBAAG;YAAG;QAAG;IAAG;AAClJ;AACA,SAAS,oBAAoB,GAAG;IAC5B,IAAI,CAAC,qJAAK,CAAC,IAAI,CAAC,mBAAmB,MAAM;QACrC,iBAAiB,CAAC,IAAI,GAAG,QAAQ,uJAAK,EAAE,MAAM,GAAG,EAAE;IACvD;IACA,OAAO,iBAAiB,CAAC,IAAI;AACjC;AACO,SAAS,UAAU,CAAC;IACvB,OAAO;QACH,IAAI,OAAO,EAAE;QACb,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;YAC1C,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;QAC5B;QACA,IAAI,MAAM,KAAK,MAAM;QACrB,IAAI,IAAI,oBAAoB;QAC5B,IAAI,MAAM,EAAE,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE;QACzB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC1B,MAAM,EAAE,EAAE,CAAC,KAAK,IAAI,CAAC,EAAE;QAC3B;QACA,OAAO;IACX;AACJ;AACA,SAAS,qBAAqB,IAAI;IAC9B,IAAI,MAAM,KAAK,MAAM;IACrB,OAAQ;QACJ,KAAK;YACD,OAAO,SAAU,CAAC;gBACd,IAAI;gBACJ,OAAQ,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;YACtC;QACJ,KAAK;YACD,OAAO,SAAU,CAAC;gBAAI,OAAO,SAAU,CAAC;oBACpC,IAAI;oBACJ,OAAQ,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;gBACvD;YAAG;QACP,KAAK;YACD,OAAO,SAAU,CAAC;gBAAI,OAAO,SAAU,CAAC;oBAAI,OAAO,SAAU,CAAC;wBAC1D,IAAI;wBACJ,OAAQ,KAAK,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GAAG;oBACxE;gBAAG;YAAG;QACV,KAAK;YACD,OAAO,SAAU,CAAC;gBAAI,OAAO,SAAU,CAAC;oBAAI,OAAO,SAAU,CAAC;wBAAI,OAAO,SAAU,CAAC;4BAChF,IAAI;4BACJ,OAAQ,KAAK,CAAC,GACV,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GACd,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GACd,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GACd,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GACd;wBACR;oBAAG;gBAAG;YAAG;QACb,KAAK;YACD,OAAO,SAAU,CAAC;gBAAI,OAAO,SAAU,CAAC;oBAAI,OAAO,SAAU,CAAC;wBAAI,OAAO,SAAU,CAAC;4BAAI,OAAO,SAAU,CAAC;gCACtG,IAAI;gCACJ,OAAQ,KAAK,CAAC,GACV,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GACd,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GACd,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GACd,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GACd,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,GACd;4BACR;wBAAG;oBAAG;gBAAG;YAAG;QAChB;YACI,OAAO,QAAQ;gBACX,IAAI,OAAO,EAAE;gBACb,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;oBAC1C,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;gBAC5B;gBACA,IAAI,IAAI,CAAC;gBACT,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;oBAC1B,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE;gBACxB;gBACA,OAAO;YACX,GAAG,MAAM,GAAG,EAAE;IACtB;AACJ;AACO,SAAS,UAAU,CAAC;IACvB,OAAO,SAAU,CAAC;QACd,IAAI,OAAO,OAAO,IAAI,CAAC;QACvB,IAAI,MAAM,KAAK,MAAM;QACrB,IAAI,IAAI,qBAAqB;QAC7B,IAAI,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;QAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;YAC1B,KAAK,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;QAC5B;QACA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 790, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/fp-ts/es6/Functor.js"], "sourcesContent": ["/**\n * A `Functor` is a type constructor which supports a mapping operation `map`.\n *\n * `map` can be used to turn functions `a -> b` into functions `f a -> f b` whose argument and return types use the type\n * constructor `f` to represent some computational context.\n *\n * Instances must satisfy the following laws:\n *\n * 1. Identity: `F.map(fa, a => a) <-> fa`\n * 2. Composition: `F.map(fa, a => bc(ab(a))) <-> F.map(F.map(fa, ab), bc)`\n *\n * @since 2.0.0\n */\nimport { pipe } from './function';\nexport function map(F, G) {\n    return function (f) { return function (fa) { return F.map(fa, function (ga) { return G.map(ga, f); }); }; };\n}\nexport function flap(F) {\n    return function (a) { return function (fab) { return F.map(fab, function (f) { return f(a); }); }; };\n}\nexport function bindTo(F) {\n    return function (name) { return function (fa) { return F.map(fa, function (a) {\n        var _a;\n        return (_a = {}, _a[name] = a, _a);\n    }); }; };\n}\nfunction let_(F) {\n    return function (name, f) { return function (fa) { return F.map(fa, function (a) {\n        var _a;\n        return Object.assign({}, a, (_a = {}, _a[name] = f(a), _a));\n    }); }; };\n}\nexport { \n/**\n * @since 2.13.0\n */\nlet_ as let };\n/** @deprecated */\nexport function getFunctorComposition(F, G) {\n    var _map = map(F, G);\n    return {\n        map: function (fga, f) { return pipe(fga, _map(f)); }\n    };\n}\n/** @internal */\nexport function as(F) {\n    return function (self, b) { return F.map(self, function () { return b; }); };\n}\n/** @internal */\nexport function asUnit(F) {\n    var asM = as(F);\n    return function (self) { return asM(self, undefined); };\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;CAYC;;;;;;;;;;;;;;;;AACD;;AACO,SAAS,IAAI,CAAC,EAAE,CAAC;IACpB,OAAO,SAAU,CAAC;QAAI,OAAO,SAAU,EAAE;YAAI,OAAO,EAAE,GAAG,CAAC,IAAI,SAAU,EAAE;gBAAI,OAAO,EAAE,GAAG,CAAC,IAAI;YAAI;QAAI;IAAG;AAC9G;AACO,SAAS,KAAK,CAAC;IAClB,OAAO,SAAU,CAAC;QAAI,OAAO,SAAU,GAAG;YAAI,OAAO,EAAE,GAAG,CAAC,KAAK,SAAU,CAAC;gBAAI,OAAO,EAAE;YAAI;QAAI;IAAG;AACvG;AACO,SAAS,OAAO,CAAC;IACpB,OAAO,SAAU,IAAI;QAAI,OAAO,SAAU,EAAE;YAAI,OAAO,EAAE,GAAG,CAAC,IAAI,SAAU,CAAC;gBACxE,IAAI;gBACJ,OAAQ,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,GAAG,GAAG;YACnC;QAAI;IAAG;AACX;AACA,SAAS,KAAK,CAAC;IACX,OAAO,SAAU,IAAI,EAAE,CAAC;QAAI,OAAO,SAAU,EAAE;YAAI,OAAO,EAAE,GAAG,CAAC,IAAI,SAAU,CAAC;gBAC3E,IAAI;gBACJ,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,GAAG,EAAE,IAAI,EAAE;YAC7D;QAAI;IAAG;AACX;;AAOO,SAAS,sBAAsB,CAAC,EAAE,CAAC;IACtC,IAAI,OAAO,IAAI,GAAG;IAClB,OAAO;QACH,KAAK,SAAU,GAAG,EAAE,CAAC;YAAI,OAAO,IAAA,sJAAI,EAAC,KAAK,KAAK;QAAK;IACxD;AACJ;AAEO,SAAS,GAAG,CAAC;IAChB,OAAO,SAAU,IAAI,EAAE,CAAC;QAAI,OAAO,EAAE,GAAG,CAAC,MAAM;YAAc,OAAO;QAAG;IAAI;AAC/E;AAEO,SAAS,OAAO,CAAC;IACpB,IAAI,MAAM,GAAG;IACb,OAAO,SAAU,IAAI;QAAI,OAAO,IAAI,MAAM;IAAY;AAC1D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 884, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/fp-ts/es6/Applicative.js"], "sourcesContent": ["/**\n * The `Applicative` type class extends the `Apply` type class with a `of` function, which can be used to create values\n * of type `f a` from values of type `a`.\n *\n * Where `Apply` provides the ability to lift functions of two or more arguments to functions whose arguments are\n * wrapped using `f`, and `Functor` provides the ability to lift functions of one argument, `pure` can be seen as the\n * function which lifts functions of _zero_ arguments. That is, `Applicative` functors support a lifting operation for\n * any number of function arguments.\n *\n * Instances must satisfy the following laws in addition to the `Apply` laws:\n *\n * 1. Identity: `A.ap(A.of(a => a), fa) <-> fa`\n * 2. Homomorphism: `A.ap(A.of(ab), A.of(a)) <-> A.of(ab(a))`\n * 3. Interchange: `A.ap(fab, A.of(a)) <-> A.ap(A.of(ab => ab(a)), fab)`\n *\n * Note. `Functor`'s `map` can be derived: `A.map(x, f) = A.ap(A.of(f), x)`\n *\n * @since 2.0.0\n */\nimport { ap, getApplySemigroup } from './Apply';\nimport { pipe } from './function';\nimport { getFunctorComposition } from './Functor';\nexport function getApplicativeMonoid(F) {\n    var f = getApplySemigroup(F);\n    return function (M) { return ({\n        concat: f(M).concat,\n        empty: F.of(M.empty)\n    }); };\n}\n/** @deprecated */\nexport function getApplicativeComposition(F, G) {\n    var map = getFunctorComposition(F, G).map;\n    var _ap = ap(F, G);\n    return {\n        map: map,\n        of: function (a) { return F.of(G.of(a)); },\n        ap: function (fgab, fga) { return pipe(fgab, _ap(fga)); }\n    };\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;CAkBC;;;;;;AACD;AACA;AACA;;;;AACO,SAAS,qBAAqB,CAAC;IAClC,IAAI,IAAI,IAAA,gKAAiB,EAAC;IAC1B,OAAO,SAAU,CAAC;QAAI,OAAQ;YAC1B,QAAQ,EAAE,GAAG,MAAM;YACnB,OAAO,EAAE,EAAE,CAAC,EAAE,KAAK;QACvB;IAAI;AACR;AAEO,SAAS,0BAA0B,CAAC,EAAE,CAAC;IAC1C,IAAI,MAAM,IAAA,sKAAqB,EAAC,GAAG,GAAG,GAAG;IACzC,IAAI,MAAM,IAAA,iJAAE,EAAC,GAAG;IAChB,OAAO;QACH,KAAK;QACL,IAAI,SAAU,CAAC;YAAI,OAAO,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;QAAK;QACzC,IAAI,SAAU,IAAI,EAAE,GAAG;YAAI,OAAO,IAAA,sJAAI,EAAC,MAAM,IAAI;QAAO;IAC5D;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 940, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/fp-ts/es6/Chain.js"], "sourcesContent": ["export function chainFirst(M) {\n    var tapM = tap(M);\n    return function (f) { return function (first) { return tapM(first, f); }; };\n}\n/** @internal */\nexport function tap(M) {\n    return function (first, f) { return M.chain(first, function (a) { return M.map(f(a), function () { return a; }); }); };\n}\nexport function bind(M) {\n    return function (name, f) { return function (ma) { return M.chain(ma, function (a) { return M.map(f(a), function (b) {\n        var _a;\n        return Object.assign({}, a, (_a = {}, _a[name] = b, _a));\n    }); }); }; };\n}\n"], "names": [], "mappings": ";;;;;;;;AAAO,SAAS,WAAW,CAAC;IACxB,IAAI,OAAO,IAAI;IACf,OAAO,SAAU,CAAC;QAAI,OAAO,SAAU,KAAK;YAAI,OAAO,KAAK,OAAO;QAAI;IAAG;AAC9E;AAEO,SAAS,IAAI,CAAC;IACjB,OAAO,SAAU,KAAK,EAAE,CAAC;QAAI,OAAO,EAAE,KAAK,CAAC,OAAO,SAAU,CAAC;YAAI,OAAO,EAAE,GAAG,CAAC,EAAE,IAAI;gBAAc,OAAO;YAAG;QAAI;IAAI;AACzH;AACO,SAAS,KAAK,CAAC;IAClB,OAAO,SAAU,IAAI,EAAE,CAAC;QAAI,OAAO,SAAU,EAAE;YAAI,OAAO,EAAE,KAAK,CAAC,IAAI,SAAU,CAAC;gBAAI,OAAO,EAAE,GAAG,CAAC,EAAE,IAAI,SAAU,CAAC;oBAC/G,IAAI;oBACJ,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,KAAK,GAAG,GAAG,EAAE;gBAC1D;YAAI;QAAI;IAAG;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 981, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/fp-ts/es6/FromEither.js"], "sourcesContent": ["/**\n * The `FromEither` type class represents those data types which support errors.\n *\n * @since 2.10.0\n */\nimport { tap } from './Chain';\nimport { flow } from './function';\nimport * as _ from './internal';\nexport function fromOption(F) {\n    return function (onNone) { return function (ma) { return F.fromEither(_.isNone(ma) ? _.left(onNone()) : _.right(ma.value)); }; };\n}\nexport function fromPredicate(F) {\n    return function (predicate, onFalse) {\n        return function (a) {\n            return F.fromEither(predicate(a) ? _.right(a) : _.left(onFalse(a)));\n        };\n    };\n}\nexport function fromOptionK(F) {\n    var fromOptionF = fromOption(F);\n    return function (onNone) {\n        var from = fromOptionF(onNone);\n        return function (f) { return flow(f, from); };\n    };\n}\nexport function chainOptionK(F, M) {\n    var fromOptionKF = fromOptionK(F);\n    return function (onNone) {\n        var from = fromOptionKF(onNone);\n        return function (f) { return function (ma) { return M.chain(ma, from(f)); }; };\n    };\n}\nexport function fromEitherK(F) {\n    return function (f) { return flow(f, F.fromEither); };\n}\nexport function chainEitherK(F, M) {\n    var fromEitherKF = fromEitherK(F);\n    return function (f) { return function (ma) { return M.chain(ma, fromEitherKF(f)); }; };\n}\nexport function chainFirstEitherK(F, M) {\n    var tapEitherM = tapEither(F, M);\n    return function (f) { return function (ma) { return tapEitherM(ma, f); }; };\n}\nexport function filterOrElse(F, M) {\n    return function (predicate, onFalse) {\n        return function (ma) {\n            return M.chain(ma, function (a) { return F.fromEither(predicate(a) ? _.right(a) : _.left(onFalse(a))); });\n        };\n    };\n}\n/** @internal */\nexport function tapEither(F, M) {\n    var fromEither = fromEitherK(F);\n    var tapM = tap(M);\n    return function (self, f) { return tapM(self, fromEither(f)); };\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;;;;;;;;;;;;;;AACD;AACA;AACA;;;;AACO,SAAS,WAAW,CAAC;IACxB,OAAO,SAAU,MAAM;QAAI,OAAO,SAAU,EAAE;YAAI,OAAO,EAAE,UAAU,CAAC,wJAAQ,CAAC,MAAM,sJAAM,CAAC,YAAY,uJAAO,CAAC,GAAG,KAAK;QAAI;IAAG;AACnI;AACO,SAAS,cAAc,CAAC;IAC3B,OAAO,SAAU,SAAS,EAAE,OAAO;QAC/B,OAAO,SAAU,CAAC;YACd,OAAO,EAAE,UAAU,CAAC,UAAU,KAAK,uJAAO,CAAC,KAAK,sJAAM,CAAC,QAAQ;QACnE;IACJ;AACJ;AACO,SAAS,YAAY,CAAC;IACzB,IAAI,cAAc,WAAW;IAC7B,OAAO,SAAU,MAAM;QACnB,IAAI,OAAO,YAAY;QACvB,OAAO,SAAU,CAAC;YAAI,OAAO,IAAA,sJAAI,EAAC,GAAG;QAAO;IAChD;AACJ;AACO,SAAS,aAAa,CAAC,EAAE,CAAC;IAC7B,IAAI,eAAe,YAAY;IAC/B,OAAO,SAAU,MAAM;QACnB,IAAI,OAAO,aAAa;QACxB,OAAO,SAAU,CAAC;YAAI,OAAO,SAAU,EAAE;gBAAI,OAAO,EAAE,KAAK,CAAC,IAAI,KAAK;YAAK;QAAG;IACjF;AACJ;AACO,SAAS,YAAY,CAAC;IACzB,OAAO,SAAU,CAAC;QAAI,OAAO,IAAA,sJAAI,EAAC,GAAG,EAAE,UAAU;IAAG;AACxD;AACO,SAAS,aAAa,CAAC,EAAE,CAAC;IAC7B,IAAI,eAAe,YAAY;IAC/B,OAAO,SAAU,CAAC;QAAI,OAAO,SAAU,EAAE;YAAI,OAAO,EAAE,KAAK,CAAC,IAAI,aAAa;QAAK;IAAG;AACzF;AACO,SAAS,kBAAkB,CAAC,EAAE,CAAC;IAClC,IAAI,aAAa,UAAU,GAAG;IAC9B,OAAO,SAAU,CAAC;QAAI,OAAO,SAAU,EAAE;YAAI,OAAO,WAAW,IAAI;QAAI;IAAG;AAC9E;AACO,SAAS,aAAa,CAAC,EAAE,CAAC;IAC7B,OAAO,SAAU,SAAS,EAAE,OAAO;QAC/B,OAAO,SAAU,EAAE;YACf,OAAO,EAAE,KAAK,CAAC,IAAI,SAAU,CAAC;gBAAI,OAAO,EAAE,UAAU,CAAC,UAAU,KAAK,uJAAO,CAAC,KAAK,sJAAM,CAAC,QAAQ;YAAM;QAC3G;IACJ;AACJ;AAEO,SAAS,UAAU,CAAC,EAAE,CAAC;IAC1B,IAAI,aAAa,YAAY;IAC7B,IAAI,OAAO,IAAA,kJAAG,EAAC;IACf,OAAO,SAAU,IAAI,EAAE,CAAC;QAAI,OAAO,KAAK,MAAM,WAAW;IAAK;AAClE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1086, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/fp-ts/es6/Predicate.js"], "sourcesContent": ["import { constFalse, constTrue, flow, pipe } from './function';\nvar contramap_ = function (predicate, f) { return pipe(predicate, contramap(f)); };\n/**\n * @since 2.11.0\n */\nexport var contramap = function (f) {\n    return function (predicate) {\n        return flow(f, predicate);\n    };\n};\n/**\n * @category type lambdas\n * @since 2.11.0\n */\nexport var URI = 'Predicate';\n/**\n * @category instances\n * @since 2.11.0\n */\nexport var getSemigroupAny = function () { return ({\n    concat: function (first, second) { return pipe(first, or(second)); }\n}); };\n/**\n * @category instances\n * @since 2.11.0\n */\nexport var getMonoidAny = function () { return ({\n    concat: getSemigroupAny().concat,\n    empty: constFalse\n}); };\n/**\n * @category instances\n * @since 2.11.0\n */\nexport var getSemigroupAll = function () { return ({\n    concat: function (first, second) { return pipe(first, and(second)); }\n}); };\n/**\n * @category instances\n * @since 2.11.0\n */\nexport var getMonoidAll = function () { return ({\n    concat: getSemigroupAll().concat,\n    empty: constTrue\n}); };\n/**\n * @category instances\n * @since 2.11.0\n */\nexport var Contravariant = {\n    URI: URI,\n    contramap: contramap_\n};\n// -------------------------------------------------------------------------------------\n// utils\n// -------------------------------------------------------------------------------------\n/**\n * @since 2.11.0\n */\nexport var not = function (predicate) {\n    return function (a) {\n        return !predicate(a);\n    };\n};\n/**\n * @since 2.11.0\n */\nexport var or = function (second) {\n    return function (first) {\n        return function (a) {\n            return first(a) || second(a);\n        };\n    };\n};\n/**\n * @since 2.11.0\n */\nexport var and = function (second) {\n    return function (first) {\n        return function (a) {\n            return first(a) && second(a);\n        };\n    };\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAAA;;AACA,IAAI,aAAa,SAAU,SAAS,EAAE,CAAC;IAAI,OAAO,IAAA,sJAAI,EAAC,WAAW,UAAU;AAAK;AAI1E,IAAI,YAAY,SAAU,CAAC;IAC9B,OAAO,SAAU,SAAS;QACtB,OAAO,IAAA,sJAAI,EAAC,GAAG;IACnB;AACJ;AAKO,IAAI,MAAM;AAKV,IAAI,kBAAkB;IAAc,OAAQ;QAC/C,QAAQ,SAAU,KAAK,EAAE,MAAM;YAAI,OAAO,IAAA,sJAAI,EAAC,OAAO,GAAG;QAAU;IACvE;AAAI;AAKG,IAAI,eAAe;IAAc,OAAQ;QAC5C,QAAQ,kBAAkB,MAAM;QAChC,OAAO,4JAAU;IACrB;AAAI;AAKG,IAAI,kBAAkB;IAAc,OAAQ;QAC/C,QAAQ,SAAU,KAAK,EAAE,MAAM;YAAI,OAAO,IAAA,sJAAI,EAAC,OAAO,IAAI;QAAU;IACxE;AAAI;AAKG,IAAI,eAAe;IAAc,OAAQ;QAC5C,QAAQ,kBAAkB,MAAM;QAChC,OAAO,2JAAS;IACpB;AAAI;AAKG,IAAI,gBAAgB;IACvB,KAAK;IACL,WAAW;AACf;AAOO,IAAI,MAAM,SAAU,SAAS;IAChC,OAAO,SAAU,CAAC;QACd,OAAO,CAAC,UAAU;IACtB;AACJ;AAIO,IAAI,KAAK,SAAU,MAAM;IAC5B,OAAO,SAAU,KAAK;QAClB,OAAO,SAAU,CAAC;YACd,OAAO,MAAM,MAAM,OAAO;QAC9B;IACJ;AACJ;AAIO,IAAI,MAAM,SAAU,MAAM;IAC7B,OAAO,SAAU,KAAK;QAClB,OAAO,SAAU,CAAC;YACd,OAAO,MAAM,MAAM,OAAO;QAC9B;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1172, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/fp-ts/es6/Magma.js"], "sourcesContent": ["/**\n * A `Magma` is a pair `(A, concat)` in which `A` is a non-empty set and `concat` is a binary operation on `A`\n *\n * See [Semigroup](https://gcanti.github.io/fp-ts/modules/Semigroup.ts.html) for some instances.\n *\n * @since 2.0.0\n */\n// -------------------------------------------------------------------------------------\n// combinators\n// -------------------------------------------------------------------------------------\n/**\n * The dual of a `Magma`, obtained by swapping the arguments of `concat`.\n *\n * @example\n * import { reverse, concatAll } from 'fp-ts/Magma'\n * import * as N from 'fp-ts/number'\n *\n * const subAll = concatAll(reverse(N.MagmaSub))(0)\n *\n * assert.deepStrictEqual(subAll([1, 2, 3]), 2)\n *\n * @since 2.11.0\n */\nexport var reverse = function (M) { return ({\n    concat: function (first, second) { return M.concat(second, first); }\n}); };\n/**\n * @since 2.11.0\n */\nexport var filterFirst = function (predicate) {\n    return function (M) { return ({\n        concat: function (first, second) { return (predicate(first) ? M.concat(first, second) : second); }\n    }); };\n};\n/**\n * @since 2.11.0\n */\nexport var filterSecond = function (predicate) {\n    return function (M) { return ({\n        concat: function (first, second) { return (predicate(second) ? M.concat(first, second) : first); }\n    }); };\n};\n/**\n * @since 2.11.0\n */\nexport var endo = function (f) {\n    return function (M) { return ({\n        concat: function (first, second) { return M.concat(f(first), f(second)); }\n    }); };\n};\n// -------------------------------------------------------------------------------------\n// utils\n// -------------------------------------------------------------------------------------\n/**\n * Given a sequence of `as`, concat them and return the total.\n *\n * If `as` is empty, return the provided `startWith` value.\n *\n * @example\n * import { concatAll } from 'fp-ts/Magma'\n * import * as N from 'fp-ts/number'\n *\n * const subAll = concatAll(N.MagmaSub)(0)\n *\n * assert.deepStrictEqual(subAll([1, 2, 3]), -6)\n *\n * @since 2.11.0\n */\nexport var concatAll = function (M) {\n    return function (startWith) {\n        return function (as) {\n            return as.reduce(function (a, acc) { return M.concat(a, acc); }, startWith);\n        };\n    };\n};\n"], "names": [], "mappings": "AAAA;;;;;;CAMC,GACD,wFAAwF;AACxF,cAAc;AACd,wFAAwF;AACxF;;;;;;;;;;;;CAYC;;;;;;;;;;;;AACM,IAAI,UAAU,SAAU,CAAC;IAAI,OAAQ;QACxC,QAAQ,SAAU,KAAK,EAAE,MAAM;YAAI,OAAO,EAAE,MAAM,CAAC,QAAQ;QAAQ;IACvE;AAAI;AAIG,IAAI,cAAc,SAAU,SAAS;IACxC,OAAO,SAAU,CAAC;QAAI,OAAQ;YAC1B,QAAQ,SAAU,KAAK,EAAE,MAAM;gBAAI,OAAQ,UAAU,SAAS,EAAE,MAAM,CAAC,OAAO,UAAU;YAAS;QACrG;IAAI;AACR;AAIO,IAAI,eAAe,SAAU,SAAS;IACzC,OAAO,SAAU,CAAC;QAAI,OAAQ;YAC1B,QAAQ,SAAU,KAAK,EAAE,MAAM;gBAAI,OAAQ,UAAU,UAAU,EAAE,MAAM,CAAC,OAAO,UAAU;YAAQ;QACrG;IAAI;AACR;AAIO,IAAI,OAAO,SAAU,CAAC;IACzB,OAAO,SAAU,CAAC;QAAI,OAAQ;YAC1B,QAAQ,SAAU,KAAK,EAAE,MAAM;gBAAI,OAAO,EAAE,MAAM,CAAC,EAAE,QAAQ,EAAE;YAAU;QAC7E;IAAI;AACR;AAmBO,IAAI,YAAY,SAAU,CAAC;IAC9B,OAAO,SAAU,SAAS;QACtB,OAAO,SAAU,EAAE;YACf,OAAO,GAAG,MAAM,CAAC,SAAU,CAAC,EAAE,GAAG;gBAAI,OAAO,EAAE,MAAM,CAAC,GAAG;YAAM,GAAG;QACrE;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1252, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/fp-ts/es6/Eq.js"], "sourcesContent": ["import { pipe } from './function';\n// -------------------------------------------------------------------------------------\n// constructors\n// -------------------------------------------------------------------------------------\n/**\n * @category constructors\n * @since 2.0.0\n */\nexport var fromEquals = function (equals) { return ({\n    equals: function (x, y) { return x === y || equals(x, y); }\n}); };\n// -------------------------------------------------------------------------------------\n// combinators\n// -------------------------------------------------------------------------------------\n/**\n * @since 2.10.0\n */\nexport var struct = function (eqs) {\n    return fromEquals(function (first, second) {\n        for (var key in eqs) {\n            if (!eqs[key].equals(first[key], second[key])) {\n                return false;\n            }\n        }\n        return true;\n    });\n};\n/**\n * Given a tuple of `Eq`s returns a `Eq` for the tuple\n *\n * @example\n * import { tuple } from 'fp-ts/Eq'\n * import * as S from 'fp-ts/string'\n * import * as N from 'fp-ts/number'\n * import * as B from 'fp-ts/boolean'\n *\n * const E = tuple(S.Eq, N.Eq, B.Eq)\n * assert.strictEqual(E.equals(['a', 1, true], ['a', 1, true]), true)\n * assert.strictEqual(E.equals(['a', 1, true], ['b', 1, true]), false)\n * assert.strictEqual(E.equals(['a', 1, true], ['a', 2, true]), false)\n * assert.strictEqual(E.equals(['a', 1, true], ['a', 1, false]), false)\n *\n * @since 2.10.0\n */\nexport var tuple = function () {\n    var eqs = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        eqs[_i] = arguments[_i];\n    }\n    return fromEquals(function (first, second) { return eqs.every(function (E, i) { return E.equals(first[i], second[i]); }); });\n};\n/* istanbul ignore next */\nvar contramap_ = function (fa, f) { return pipe(fa, contramap(f)); };\n/**\n * A typical use case for `contramap` would be like, given some `User` type, to construct an `Eq<User>`.\n *\n * We can do so with a function from `User -> X` where `X` is some value that we know how to compare\n * for equality (meaning we have an `Eq<X>`)\n *\n * For example, given the following `User` type, we want to construct an `Eq<User>` that just looks at the `key` field\n * for each user (since it's known to be unique).\n *\n * If we have a way of comparing `UUID`s for equality (`eqUUID: Eq<UUID>`) and we know how to go from `User -> UUID`,\n * using `contramap` we can do this\n *\n * @example\n * import { contramap, Eq } from 'fp-ts/Eq'\n * import { pipe } from 'fp-ts/function'\n * import * as S from 'fp-ts/string'\n *\n * type UUID = string\n *\n * interface User {\n *   readonly key: UUID\n *   readonly firstName: string\n *   readonly lastName: string\n * }\n *\n * const eqUUID: Eq<UUID> = S.Eq\n *\n * const eqUserByKey: Eq<User> = pipe(\n *   eqUUID,\n *   contramap((user) => user.key)\n * )\n *\n * assert.deepStrictEqual(\n *   eqUserByKey.equals(\n *     { key: 'k1', firstName: 'a1', lastName: 'b1' },\n *     { key: 'k2', firstName: 'a1', lastName: 'b1' }\n *   ),\n *   false\n * )\n * assert.deepStrictEqual(\n *   eqUserByKey.equals(\n *     { key: 'k1', firstName: 'a1', lastName: 'b1' },\n *     { key: 'k1', firstName: 'a2', lastName: 'b1' }\n *   ),\n *   true\n * )\n *\n * @since 2.0.0\n */\nexport var contramap = function (f) { return function (fa) {\n    return fromEquals(function (x, y) { return fa.equals(f(x), f(y)); });\n}; };\n/**\n * @category type lambdas\n * @since 2.0.0\n */\nexport var URI = 'Eq';\n/**\n * @category instances\n * @since 2.5.0\n */\nexport var eqStrict = {\n    equals: function (a, b) { return a === b; }\n};\nvar empty = {\n    equals: function () { return true; }\n};\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var getSemigroup = function () { return ({\n    concat: function (x, y) { return fromEquals(function (a, b) { return x.equals(a, b) && y.equals(a, b); }); }\n}); };\n/**\n * @category instances\n * @since 2.6.0\n */\nexport var getMonoid = function () { return ({\n    concat: getSemigroup().concat,\n    empty: empty\n}); };\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var Contravariant = {\n    URI: URI,\n    contramap: contramap_\n};\n// -------------------------------------------------------------------------------------\n// deprecated\n// -------------------------------------------------------------------------------------\n/**\n * Use [`tuple`](#tuple) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var getTupleEq = tuple;\n/**\n * Use [`struct`](#struct) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var getStructEq = struct;\n/**\n * Use [`eqStrict`](#eqstrict) instead\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var strictEqual = eqStrict.equals;\n/**\n * This instance is deprecated, use small, specific instances instead.\n * For example if a function needs a `Contravariant` instance, pass `E.Contravariant` instead of `E.eq`\n * (where `E` is from `import E from 'fp-ts/Eq'`)\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var eq = Contravariant;\n/**\n * Use [`Eq`](./boolean.ts.html#eq) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var eqBoolean = eqStrict;\n/**\n * Use [`Eq`](./string.ts.html#eq) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var eqString = eqStrict;\n/**\n * Use [`Eq`](./number.ts.html#eq) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var eqNumber = eqStrict;\n/**\n * Use [`Eq`](./Date.ts.html#eq) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var eqDate = {\n    equals: function (first, second) { return first.valueOf() === second.valueOf(); }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAQO,IAAI,aAAa,SAAU,MAAM;IAAI,OAAQ;QAChD,QAAQ,SAAU,CAAC,EAAE,CAAC;YAAI,OAAO,MAAM,KAAK,OAAO,GAAG;QAAI;IAC9D;AAAI;AAOG,IAAI,SAAS,SAAU,GAAG;IAC7B,OAAO,WAAW,SAAU,KAAK,EAAE,MAAM;QACrC,IAAK,IAAI,OAAO,IAAK;YACjB,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,IAAI,GAAG;gBAC3C,OAAO;YACX;QACJ;QACA,OAAO;IACX;AACJ;AAkBO,IAAI,QAAQ;IACf,IAAI,MAAM,EAAE;IACZ,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;QAC1C,GAAG,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;IAC3B;IACA,OAAO,WAAW,SAAU,KAAK,EAAE,MAAM;QAAI,OAAO,IAAI,KAAK,CAAC,SAAU,CAAC,EAAE,CAAC;YAAI,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE;QAAG;IAAI;AAC9H;AACA,wBAAwB,GACxB,IAAI,aAAa,SAAU,EAAE,EAAE,CAAC;IAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,UAAU;AAAK;AAkD5D,IAAI,YAAY,SAAU,CAAC;IAAI,OAAO,SAAU,EAAE;QACrD,OAAO,WAAW,SAAU,CAAC,EAAE,CAAC;YAAI,OAAO,GAAG,MAAM,CAAC,EAAE,IAAI,EAAE;QAAK;IACtE;AAAG;AAKI,IAAI,MAAM;AAKV,IAAI,WAAW;IAClB,QAAQ,SAAU,CAAC,EAAE,CAAC;QAAI,OAAO,MAAM;IAAG;AAC9C;AACA,IAAI,QAAQ;IACR,QAAQ;QAAc,OAAO;IAAM;AACvC;AAKO,IAAI,eAAe;IAAc,OAAQ;QAC5C,QAAQ,SAAU,CAAC,EAAE,CAAC;YAAI,OAAO,WAAW,SAAU,CAAC,EAAE,CAAC;gBAAI,OAAO,EAAE,MAAM,CAAC,GAAG,MAAM,EAAE,MAAM,CAAC,GAAG;YAAI;QAAI;IAC/G;AAAI;AAKG,IAAI,YAAY;IAAc,OAAQ;QACzC,QAAQ,eAAe,MAAM;QAC7B,OAAO;IACX;AAAI;AAKG,IAAI,gBAAgB;IACvB,KAAK;IACL,WAAW;AACf;AAWO,IAAI,aAAa;AAQjB,IAAI,cAAc;AAQlB,IAAI,cAAc,SAAS,MAAM;AAUjC,IAAI,KAAK;AAQT,IAAI,YAAY;AAQhB,IAAI,WAAW;AAQf,IAAI,WAAW;AAQf,IAAI,SAAS;IAChB,QAAQ,SAAU,KAAK,EAAE,MAAM;QAAI,OAAO,MAAM,OAAO,OAAO,OAAO,OAAO;IAAI;AACpF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1374, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/fp-ts/es6/Ord.js"], "sourcesContent": ["import { eqStrict } from './Eq';\nimport { constant, constTrue, pipe } from './function';\n// -------------------------------------------------------------------------------------\n// defaults\n// -------------------------------------------------------------------------------------\n/**\n * @category defaults\n * @since 2.10.0\n */\nexport var equalsDefault = function (compare) {\n    return function (first, second) {\n        return first === second || compare(first, second) === 0;\n    };\n};\n// -------------------------------------------------------------------------------------\n// constructors\n// -------------------------------------------------------------------------------------\n/**\n * @category constructors\n * @since 2.0.0\n */\nexport var fromCompare = function (compare) { return ({\n    equals: equalsDefault(compare),\n    compare: function (first, second) { return (first === second ? 0 : compare(first, second)); }\n}); };\n// -------------------------------------------------------------------------------------\n// combinators\n// -------------------------------------------------------------------------------------\n/**\n * Given a tuple of `Ord`s returns an `Ord` for the tuple.\n *\n * @example\n * import { tuple } from 'fp-ts/Ord'\n * import * as B from 'fp-ts/boolean'\n * import * as S from 'fp-ts/string'\n * import * as N from 'fp-ts/number'\n *\n * const O = tuple(S.Ord, N.Ord, B.Ord)\n * assert.strictEqual(O.compare(['a', 1, true], ['b', 2, true]), -1)\n * assert.strictEqual(O.compare(['a', 1, true], ['a', 2, true]), -1)\n * assert.strictEqual(O.compare(['a', 1, true], ['a', 1, false]), 1)\n *\n * @since 2.10.0\n */\nexport var tuple = function () {\n    var ords = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        ords[_i] = arguments[_i];\n    }\n    return fromCompare(function (first, second) {\n        var i = 0;\n        for (; i < ords.length - 1; i++) {\n            var r = ords[i].compare(first[i], second[i]);\n            if (r !== 0) {\n                return r;\n            }\n        }\n        return ords[i].compare(first[i], second[i]);\n    });\n};\n/**\n * @since 2.10.0\n */\nexport var reverse = function (O) { return fromCompare(function (first, second) { return O.compare(second, first); }); };\n/* istanbul ignore next */\nvar contramap_ = function (fa, f) { return pipe(fa, contramap(f)); };\n/**\n * A typical use case for `contramap` would be like, given some `User` type, to construct an `Ord<User>`.\n *\n * We can do so with a function from `User -> X` where `X` is some value that we know how to compare\n * for ordering (meaning we have an `Ord<X>`)\n *\n * For example, given the following `User` type, there are lots of possible choices for `X`,\n * but let's say we want to sort a list of users by `lastName`.\n *\n * If we have a way of comparing `lastName`s for ordering (`ordLastName: Ord<string>`) and we know how to go from `User -> string`,\n * using `contramap` we can do this\n *\n * @example\n * import { pipe } from 'fp-ts/function'\n * import { contramap, Ord } from 'fp-ts/Ord'\n * import * as RA from 'fp-ts/ReadonlyArray'\n * import * as S from 'fp-ts/string'\n *\n * interface User {\n *   readonly firstName: string\n *   readonly lastName: string\n * }\n *\n * const ordLastName: Ord<string> = S.Ord\n *\n * const ordByLastName: Ord<User> = pipe(\n *   ordLastName,\n *   contramap((user) => user.lastName)\n * )\n *\n * assert.deepStrictEqual(\n *   RA.sort(ordByLastName)([\n *     { firstName: 'a', lastName: 'd' },\n *     { firstName: 'c', lastName: 'b' }\n *   ]),\n *   [\n *     { firstName: 'c', lastName: 'b' },\n *     { firstName: 'a', lastName: 'd' }\n *   ]\n * )\n *\n * @since 2.0.0\n */\nexport var contramap = function (f) { return function (fa) {\n    return fromCompare(function (first, second) { return fa.compare(f(first), f(second)); });\n}; };\n/**\n * @category type lambdas\n * @since 2.0.0\n */\nexport var URI = 'Ord';\n/**\n * A typical use case for the `Semigroup` instance of `Ord` is merging two or more orderings.\n *\n * For example the following snippet builds an `Ord` for a type `User` which\n * sorts by `created` date descending, and **then** `lastName`\n *\n * @example\n * import * as D from 'fp-ts/Date'\n * import { pipe } from 'fp-ts/function'\n * import { contramap, getSemigroup, Ord, reverse } from 'fp-ts/Ord'\n * import * as RA from 'fp-ts/ReadonlyArray'\n * import * as S from 'fp-ts/string'\n *\n * interface User {\n *   readonly id: string\n *   readonly lastName: string\n *   readonly created: Date\n * }\n *\n * const ordByLastName: Ord<User> = pipe(\n *   S.Ord,\n *   contramap((user) => user.lastName)\n * )\n *\n * const ordByCreated: Ord<User> = pipe(\n *   D.Ord,\n *   contramap((user) => user.created)\n * )\n *\n * const ordUserByCreatedDescThenLastName = getSemigroup<User>().concat(\n *   reverse(ordByCreated),\n *   ordByLastName\n * )\n *\n * assert.deepStrictEqual(\n *   RA.sort(ordUserByCreatedDescThenLastName)([\n *     { id: 'c', lastName: 'd', created: new Date(1973, 10, 30) },\n *     { id: 'a', lastName: 'b', created: new Date(1973, 10, 30) },\n *     { id: 'e', lastName: 'f', created: new Date(1980, 10, 30) }\n *   ]),\n *   [\n *     { id: 'e', lastName: 'f', created: new Date(1980, 10, 30) },\n *     { id: 'a', lastName: 'b', created: new Date(1973, 10, 30) },\n *     { id: 'c', lastName: 'd', created: new Date(1973, 10, 30) }\n *   ]\n * )\n *\n * @category instances\n * @since 2.0.0\n */\nexport var getSemigroup = function () { return ({\n    concat: function (first, second) {\n        return fromCompare(function (a, b) {\n            var ox = first.compare(a, b);\n            return ox !== 0 ? ox : second.compare(a, b);\n        });\n    }\n}); };\n/**\n * Returns a `Monoid` such that:\n *\n * - its `concat(ord1, ord2)` operation will order first by `ord1`, and then by `ord2`\n * - its `empty` value is an `Ord` that always considers compared elements equal\n *\n * @example\n * import { sort } from 'fp-ts/Array'\n * import { contramap, reverse, getMonoid } from 'fp-ts/Ord'\n * import * as S from 'fp-ts/string'\n * import * as B from 'fp-ts/boolean'\n * import { pipe } from 'fp-ts/function'\n * import { concatAll } from 'fp-ts/Monoid'\n * import * as N from 'fp-ts/number'\n *\n * interface User {\n *   readonly id: number\n *   readonly name: string\n *   readonly age: number\n *   readonly rememberMe: boolean\n * }\n *\n * const byName = pipe(\n *   S.Ord,\n *   contramap((p: User) => p.name)\n * )\n *\n * const byAge = pipe(\n *   N.Ord,\n *   contramap((p: User) => p.age)\n * )\n *\n * const byRememberMe = pipe(\n *   B.Ord,\n *   contramap((p: User) => p.rememberMe)\n * )\n *\n * const M = getMonoid<User>()\n *\n * const users: Array<User> = [\n *   { id: 1, name: 'Guido', age: 47, rememberMe: false },\n *   { id: 2, name: 'Guido', age: 46, rememberMe: true },\n *   { id: 3, name: 'Giulio', age: 44, rememberMe: false },\n *   { id: 4, name: 'Giulio', age: 44, rememberMe: true }\n * ]\n *\n * // sort by name, then by age, then by `rememberMe`\n * const O1 = concatAll(M)([byName, byAge, byRememberMe])\n * assert.deepStrictEqual(sort(O1)(users), [\n *   { id: 3, name: 'Giulio', age: 44, rememberMe: false },\n *   { id: 4, name: 'Giulio', age: 44, rememberMe: true },\n *   { id: 2, name: 'Guido', age: 46, rememberMe: true },\n *   { id: 1, name: 'Guido', age: 47, rememberMe: false }\n * ])\n *\n * // now `rememberMe = true` first, then by name, then by age\n * const O2 = concatAll(M)([reverse(byRememberMe), byName, byAge])\n * assert.deepStrictEqual(sort(O2)(users), [\n *   { id: 4, name: 'Giulio', age: 44, rememberMe: true },\n *   { id: 2, name: 'Guido', age: 46, rememberMe: true },\n *   { id: 3, name: 'Giulio', age: 44, rememberMe: false },\n *   { id: 1, name: 'Guido', age: 47, rememberMe: false }\n * ])\n *\n * @category instances\n * @since 2.4.0\n */\nexport var getMonoid = function () { return ({\n    concat: getSemigroup().concat,\n    empty: fromCompare(function () { return 0; })\n}); };\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var Contravariant = {\n    URI: URI,\n    contramap: contramap_\n};\n// -------------------------------------------------------------------------------------\n// utils\n// -------------------------------------------------------------------------------------\n/**\n * @since 2.11.0\n */\nexport var trivial = {\n    equals: constTrue,\n    compare: /*#__PURE__*/ constant(0)\n};\n/**\n * @since 2.11.0\n */\nexport var equals = function (O) {\n    return function (second) {\n        return function (first) {\n            return first === second || O.compare(first, second) === 0;\n        };\n    };\n};\n// TODO: curry in v3\n/**\n * Test whether one value is _strictly less than_ another\n *\n * @since 2.0.0\n */\nexport var lt = function (O) {\n    return function (first, second) {\n        return O.compare(first, second) === -1;\n    };\n};\n// TODO: curry in v3\n/**\n * Test whether one value is _strictly greater than_ another\n *\n * @since 2.0.0\n */\nexport var gt = function (O) {\n    return function (first, second) {\n        return O.compare(first, second) === 1;\n    };\n};\n// TODO: curry in v3\n/**\n * Test whether one value is _non-strictly less than_ another\n *\n * @since 2.0.0\n */\nexport var leq = function (O) {\n    return function (first, second) {\n        return O.compare(first, second) !== 1;\n    };\n};\n// TODO: curry in v3\n/**\n * Test whether one value is _non-strictly greater than_ another\n *\n * @since 2.0.0\n */\nexport var geq = function (O) {\n    return function (first, second) {\n        return O.compare(first, second) !== -1;\n    };\n};\n// TODO: curry in v3\n/**\n * Take the minimum of two values. If they are considered equal, the first argument is chosen\n *\n * @since 2.0.0\n */\nexport var min = function (O) {\n    return function (first, second) {\n        return first === second || O.compare(first, second) < 1 ? first : second;\n    };\n};\n// TODO: curry in v3\n/**\n * Take the maximum of two values. If they are considered equal, the first argument is chosen\n *\n * @since 2.0.0\n */\nexport var max = function (O) {\n    return function (first, second) {\n        return first === second || O.compare(first, second) > -1 ? first : second;\n    };\n};\n/**\n * Clamp a value between a minimum and a maximum\n *\n * @since 2.0.0\n */\nexport var clamp = function (O) {\n    var minO = min(O);\n    var maxO = max(O);\n    return function (low, hi) { return function (a) { return maxO(minO(a, hi), low); }; };\n};\n/**\n * Test whether a value is between a minimum and a maximum (inclusive)\n *\n * @since 2.0.0\n */\nexport var between = function (O) {\n    var ltO = lt(O);\n    var gtO = gt(O);\n    return function (low, hi) { return function (a) { return ltO(a, low) || gtO(a, hi) ? false : true; }; };\n};\n// -------------------------------------------------------------------------------------\n// deprecated\n// -------------------------------------------------------------------------------------\n/**\n * Use [`tuple`](#tuple) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var getTupleOrd = tuple;\n/**\n * Use [`reverse`](#reverse) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var getDualOrd = reverse;\n/**\n * Use [`Contravariant`](#contravariant) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var ord = Contravariant;\n// default compare for primitive types\nfunction compare(first, second) {\n    return first < second ? -1 : first > second ? 1 : 0;\n}\nvar strictOrd = {\n    equals: eqStrict.equals,\n    compare: compare\n};\n/**\n * Use [`Ord`](./boolean.ts.html#ord) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var ordBoolean = strictOrd;\n/**\n * Use [`Ord`](./string.ts.html#ord) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var ordString = strictOrd;\n/**\n * Use [`Ord`](./number.ts.html#ord) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var ordNumber = strictOrd;\n/**\n * Use [`Ord`](./Date.ts.html#ord) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var ordDate = /*#__PURE__*/ pipe(ordNumber, \n/*#__PURE__*/\ncontramap(function (date) { return date.valueOf(); }));\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAQO,IAAI,gBAAgB,SAAU,OAAO;IACxC,OAAO,SAAU,KAAK,EAAE,MAAM;QAC1B,OAAO,UAAU,UAAU,QAAQ,OAAO,YAAY;IAC1D;AACJ;AAQO,IAAI,cAAc,SAAU,OAAO;IAAI,OAAQ;QAClD,QAAQ,cAAc;QACtB,SAAS,SAAU,KAAK,EAAE,MAAM;YAAI,OAAQ,UAAU,SAAS,IAAI,QAAQ,OAAO;QAAU;IAChG;AAAI;AAoBG,IAAI,QAAQ;IACf,IAAI,OAAO,EAAE;IACb,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;QAC1C,IAAI,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;IAC5B;IACA,OAAO,YAAY,SAAU,KAAK,EAAE,MAAM;QACtC,IAAI,IAAI;QACR,MAAO,IAAI,KAAK,MAAM,GAAG,GAAG,IAAK;YAC7B,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE;YAC3C,IAAI,MAAM,GAAG;gBACT,OAAO;YACX;QACJ;QACA,OAAO,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE;IAC9C;AACJ;AAIO,IAAI,UAAU,SAAU,CAAC;IAAI,OAAO,YAAY,SAAU,KAAK,EAAE,MAAM;QAAI,OAAO,EAAE,OAAO,CAAC,QAAQ;IAAQ;AAAI;AACvH,wBAAwB,GACxB,IAAI,aAAa,SAAU,EAAE,EAAE,CAAC;IAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,UAAU;AAAK;AA4C5D,IAAI,YAAY,SAAU,CAAC;IAAI,OAAO,SAAU,EAAE;QACrD,OAAO,YAAY,SAAU,KAAK,EAAE,MAAM;YAAI,OAAO,GAAG,OAAO,CAAC,EAAE,QAAQ,EAAE;QAAU;IAC1F;AAAG;AAKI,IAAI,MAAM;AAmDV,IAAI,eAAe;IAAc,OAAQ;QAC5C,QAAQ,SAAU,KAAK,EAAE,MAAM;YAC3B,OAAO,YAAY,SAAU,CAAC,EAAE,CAAC;gBAC7B,IAAI,KAAK,MAAM,OAAO,CAAC,GAAG;gBAC1B,OAAO,OAAO,IAAI,KAAK,OAAO,OAAO,CAAC,GAAG;YAC7C;QACJ;IACJ;AAAI;AAoEG,IAAI,YAAY;IAAc,OAAQ;QACzC,QAAQ,eAAe,MAAM;QAC7B,OAAO,YAAY;YAAc,OAAO;QAAG;IAC/C;AAAI;AAKG,IAAI,gBAAgB;IACvB,KAAK;IACL,WAAW;AACf;AAOO,IAAI,UAAU;IACjB,QAAQ,2JAAS;IACjB,SAAS,WAAW,GAAG,IAAA,0JAAQ,EAAC;AACpC;AAIO,IAAI,SAAS,SAAU,CAAC;IAC3B,OAAO,SAAU,MAAM;QACnB,OAAO,SAAU,KAAK;YAClB,OAAO,UAAU,UAAU,EAAE,OAAO,CAAC,OAAO,YAAY;QAC5D;IACJ;AACJ;AAOO,IAAI,KAAK,SAAU,CAAC;IACvB,OAAO,SAAU,KAAK,EAAE,MAAM;QAC1B,OAAO,EAAE,OAAO,CAAC,OAAO,YAAY,CAAC;IACzC;AACJ;AAOO,IAAI,KAAK,SAAU,CAAC;IACvB,OAAO,SAAU,KAAK,EAAE,MAAM;QAC1B,OAAO,EAAE,OAAO,CAAC,OAAO,YAAY;IACxC;AACJ;AAOO,IAAI,MAAM,SAAU,CAAC;IACxB,OAAO,SAAU,KAAK,EAAE,MAAM;QAC1B,OAAO,EAAE,OAAO,CAAC,OAAO,YAAY;IACxC;AACJ;AAOO,IAAI,MAAM,SAAU,CAAC;IACxB,OAAO,SAAU,KAAK,EAAE,MAAM;QAC1B,OAAO,EAAE,OAAO,CAAC,OAAO,YAAY,CAAC;IACzC;AACJ;AAOO,IAAI,MAAM,SAAU,CAAC;IACxB,OAAO,SAAU,KAAK,EAAE,MAAM;QAC1B,OAAO,UAAU,UAAU,EAAE,OAAO,CAAC,OAAO,UAAU,IAAI,QAAQ;IACtE;AACJ;AAOO,IAAI,MAAM,SAAU,CAAC;IACxB,OAAO,SAAU,KAAK,EAAE,MAAM;QAC1B,OAAO,UAAU,UAAU,EAAE,OAAO,CAAC,OAAO,UAAU,CAAC,IAAI,QAAQ;IACvE;AACJ;AAMO,IAAI,QAAQ,SAAU,CAAC;IAC1B,IAAI,OAAO,IAAI;IACf,IAAI,OAAO,IAAI;IACf,OAAO,SAAU,GAAG,EAAE,EAAE;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,KAAK,KAAK,GAAG,KAAK;QAAM;IAAG;AACxF;AAMO,IAAI,UAAU,SAAU,CAAC;IAC5B,IAAI,MAAM,GAAG;IACb,IAAI,MAAM,GAAG;IACb,OAAO,SAAU,GAAG,EAAE,EAAE;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,IAAI,GAAG,QAAQ,IAAI,GAAG,MAAM,QAAQ;QAAM;IAAG;AAC1G;AAWO,IAAI,cAAc;AAQlB,IAAI,aAAa;AAQjB,IAAI,MAAM;AACjB,sCAAsC;AACtC,SAAS,QAAQ,KAAK,EAAE,MAAM;IAC1B,OAAO,QAAQ,SAAS,CAAC,IAAI,QAAQ,SAAS,IAAI;AACtD;AACA,IAAI,YAAY;IACZ,QAAQ,oJAAQ,CAAC,MAAM;IACvB,SAAS;AACb;AAQO,IAAI,aAAa;AAQjB,IAAI,YAAY;AAQhB,IAAI,YAAY;AAQhB,IAAI,UAAU,WAAW,GAAG,IAAA,sJAAI,EAAC,WACxC,WAAW,GACX,UAAU,SAAU,IAAI;IAAI,OAAO,KAAK,OAAO;AAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1579, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/fp-ts/es6/Semigroup.js"], "sourcesContent": ["/**\n * If a type `A` can form a `Semigroup` it has an **associative** binary operation.\n *\n * ```ts\n * interface Semigroup<A> {\n *   readonly concat: (x: A, y: A) => A\n * }\n * ```\n *\n * Associativity means the following equality must hold for any choice of `x`, `y`, and `z`.\n *\n * ```ts\n * concat(x, concat(y, z)) = concat(concat(x, y), z)\n * ```\n *\n * A common example of a semigroup is the type `string` with the operation `+`.\n *\n * ```ts\n * import { Semigroup } from 'fp-ts/Semigroup'\n *\n * const semigroupString: Semigroup<string> = {\n *   concat: (x, y) => x + y\n * }\n *\n * const x = 'x'\n * const y = 'y'\n * const z = 'z'\n *\n * semigroupString.concat(x, y) // 'xy'\n *\n * semigroupString.concat(x, semigroupString.concat(y, z)) // 'xyz'\n *\n * semigroupString.concat(semigroupString.concat(x, y), z) // 'xyz'\n * ```\n *\n * *Adapted from https://typelevel.org/cats*\n *\n * @since 2.0.0\n */\nimport { getSemigroup, identity } from './function';\nimport * as _ from './internal';\nimport * as M from './Magma';\nimport * as Or from './Ord';\n// -------------------------------------------------------------------------------------\n// constructors\n// -------------------------------------------------------------------------------------\n/**\n * Get a semigroup where `concat` will return the minimum, based on the provided order.\n *\n * @example\n * import * as N from 'fp-ts/number'\n * import * as S from 'fp-ts/Semigroup'\n *\n * const S1 = S.min(N.Ord)\n *\n * assert.deepStrictEqual(S1.concat(1, 2), 1)\n *\n * @category constructors\n * @since 2.10.0\n */\nexport var min = function (O) { return ({\n    concat: Or.min(O)\n}); };\n/**\n * Get a semigroup where `concat` will return the maximum, based on the provided order.\n *\n * @example\n * import * as N from 'fp-ts/number'\n * import * as S from 'fp-ts/Semigroup'\n *\n * const S1 = S.max(N.Ord)\n *\n * assert.deepStrictEqual(S1.concat(1, 2), 2)\n *\n * @category constructors\n * @since 2.10.0\n */\nexport var max = function (O) { return ({\n    concat: Or.max(O)\n}); };\n/**\n * @category constructors\n * @since 2.10.0\n */\nexport var constant = function (a) { return ({\n    concat: function () { return a; }\n}); };\n// -------------------------------------------------------------------------------------\n// combinators\n// -------------------------------------------------------------------------------------\n/**\n * The dual of a `Semigroup`, obtained by swapping the arguments of `concat`.\n *\n * @example\n * import { reverse } from 'fp-ts/Semigroup'\n * import * as S from 'fp-ts/string'\n *\n * assert.deepStrictEqual(reverse(S.Semigroup).concat('a', 'b'), 'ba')\n *\n * @since 2.10.0\n */\nexport var reverse = M.reverse;\n/**\n * Given a struct of semigroups returns a semigroup for the struct.\n *\n * @example\n * import { struct } from 'fp-ts/Semigroup'\n * import * as N from 'fp-ts/number'\n *\n * interface Point {\n *   readonly x: number\n *   readonly y: number\n * }\n *\n * const S = struct<Point>({\n *   x: N.SemigroupSum,\n *   y: N.SemigroupSum\n * })\n *\n * assert.deepStrictEqual(S.concat({ x: 1, y: 2 }, { x: 3, y: 4 }), { x: 4, y: 6 })\n *\n * @since 2.10.0\n */\nexport var struct = function (semigroups) { return ({\n    concat: function (first, second) {\n        var r = {};\n        for (var k in semigroups) {\n            if (_.has.call(semigroups, k)) {\n                r[k] = semigroups[k].concat(first[k], second[k]);\n            }\n        }\n        return r;\n    }\n}); };\n/**\n * Given a tuple of semigroups returns a semigroup for the tuple.\n *\n * @example\n * import { tuple } from 'fp-ts/Semigroup'\n * import * as B from 'fp-ts/boolean'\n * import * as N from 'fp-ts/number'\n * import * as S from 'fp-ts/string'\n *\n * const S1 = tuple(S.Semigroup, N.SemigroupSum)\n * assert.deepStrictEqual(S1.concat(['a', 1], ['b', 2]), ['ab', 3])\n *\n * const S2 = tuple(S.Semigroup, N.SemigroupSum, B.SemigroupAll)\n * assert.deepStrictEqual(S2.concat(['a', 1, true], ['b', 2, false]), ['ab', 3, false])\n *\n * @since 2.10.0\n */\nexport var tuple = function () {\n    var semigroups = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n        semigroups[_i] = arguments[_i];\n    }\n    return ({\n        concat: function (first, second) { return semigroups.map(function (s, i) { return s.concat(first[i], second[i]); }); }\n    });\n};\n/**\n * Between each pair of elements insert `middle`.\n *\n * @example\n * import { intercalate } from 'fp-ts/Semigroup'\n * import * as S from 'fp-ts/string'\n * import { pipe } from 'fp-ts/function'\n *\n * const S1 = pipe(S.Semigroup, intercalate(' + '))\n *\n * assert.strictEqual(S1.concat('a', 'b'), 'a + b')\n *\n * @since 2.10.0\n */\nexport var intercalate = function (middle) {\n    return function (S) { return ({\n        concat: function (x, y) { return S.concat(x, S.concat(middle, y)); }\n    }); };\n};\n// -------------------------------------------------------------------------------------\n// instances\n// -------------------------------------------------------------------------------------\n/**\n * Always return the first argument.\n *\n * @example\n * import * as S from 'fp-ts/Semigroup'\n *\n * assert.deepStrictEqual(S.first<number>().concat(1, 2), 1)\n *\n * @category instances\n * @since 2.10.0\n */\nexport var first = function () { return ({ concat: identity }); };\n/**\n * Always return the last argument.\n *\n * @example\n * import * as S from 'fp-ts/Semigroup'\n *\n * assert.deepStrictEqual(S.last<number>().concat(1, 2), 2)\n *\n * @category instances\n * @since 2.10.0\n */\nexport var last = function () { return ({ concat: function (_, y) { return y; } }); };\n// -------------------------------------------------------------------------------------\n// utils\n// -------------------------------------------------------------------------------------\n/**\n * Given a sequence of `as`, concat them and return the total.\n *\n * If `as` is empty, return the provided `startWith` value.\n *\n * @example\n * import { concatAll } from 'fp-ts/Semigroup'\n * import * as N from 'fp-ts/number'\n *\n * const sum = concatAll(N.SemigroupSum)(0)\n *\n * assert.deepStrictEqual(sum([1, 2, 3]), 6)\n * assert.deepStrictEqual(sum([]), 0)\n *\n * @since 2.10.0\n */\nexport var concatAll = M.concatAll;\n// -------------------------------------------------------------------------------------\n// deprecated\n// -------------------------------------------------------------------------------------\n/**\n * Use `void` module instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var semigroupVoid = constant(undefined);\n/**\n * Use [`getAssignSemigroup`](./struct.ts.html#getAssignSemigroup) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var getObjectSemigroup = function () { return ({\n    concat: function (first, second) { return Object.assign({}, first, second); }\n}); };\n/**\n * Use [`last`](#last) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var getLastSemigroup = last;\n/**\n * Use [`first`](#first) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var getFirstSemigroup = first;\n/**\n * Use [`tuple`](#tuple) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var getTupleSemigroup = tuple;\n/**\n * Use [`struct`](#struct) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var getStructSemigroup = struct;\n/**\n * Use [`reverse`](#reverse) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var getDualSemigroup = reverse;\n/**\n * Use [`max`](#max) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var getJoinSemigroup = max;\n/**\n * Use [`min`](#min) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var getMeetSemigroup = min;\n/**\n * Use [`intercalate`](#intercalate) instead.\n *\n * @category zone of death\n * @since 2.5.0\n * @deprecated\n */\nexport var getIntercalateSemigroup = intercalate;\nexport function fold(S) {\n    var concatAllS = concatAll(S);\n    return function (startWith, as) { return (as === undefined ? concatAllS(startWith) : concatAllS(startWith)(as)); };\n}\n/**\n * Use [`SemigroupAll`](./boolean.ts.html#SemigroupAll) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var semigroupAll = {\n    concat: function (x, y) { return x && y; }\n};\n/**\n * Use [`SemigroupAny`](./boolean.ts.html#SemigroupAny) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var semigroupAny = {\n    concat: function (x, y) { return x || y; }\n};\n/**\n * Use [`getSemigroup`](./function.ts.html#getSemigroup) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var getFunctionSemigroup = getSemigroup;\n/**\n * Use [`Semigroup`](./string.ts.html#Semigroup) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var semigroupString = {\n    concat: function (x, y) { return x + y; }\n};\n/**\n * Use [`SemigroupSum`](./number.ts.html#SemigroupSum) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var semigroupSum = {\n    concat: function (x, y) { return x + y; }\n};\n/**\n * Use [`SemigroupProduct`](./number.ts.html#SemigroupProduct) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var semigroupProduct = {\n    concat: function (x, y) { return x * y; }\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAsCC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACD;AACA;AACA;AACA;;;;;AAkBO,IAAI,MAAM,SAAU,CAAC;IAAI,OAAQ;QACpC,QAAQ,gJAAM,CAAC;IACnB;AAAI;AAeG,IAAI,MAAM,SAAU,CAAC;IAAI,OAAQ;QACpC,QAAQ,gJAAM,CAAC;IACnB;AAAI;AAKG,IAAI,WAAW,SAAU,CAAC;IAAI,OAAQ;QACzC,QAAQ;YAAc,OAAO;QAAG;IACpC;AAAI;AAeG,IAAI,UAAU,sJAAS;AAsBvB,IAAI,SAAS,SAAU,UAAU;IAAI,OAAQ;QAChD,QAAQ,SAAU,KAAK,EAAE,MAAM;YAC3B,IAAI,IAAI,CAAC;YACT,IAAK,IAAI,KAAK,WAAY;gBACtB,IAAI,qJAAK,CAAC,IAAI,CAAC,YAAY,IAAI;oBAC3B,CAAC,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE;gBACnD;YACJ;YACA,OAAO;QACX;IACJ;AAAI;AAkBG,IAAI,QAAQ;IACf,IAAI,aAAa,EAAE;IACnB,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;QAC1C,UAAU,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;IAClC;IACA,OAAQ;QACJ,QAAQ,SAAU,KAAK,EAAE,MAAM;YAAI,OAAO,WAAW,GAAG,CAAC,SAAU,CAAC,EAAE,CAAC;gBAAI,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE;YAAG;QAAI;IACzH;AACJ;AAeO,IAAI,cAAc,SAAU,MAAM;IACrC,OAAO,SAAU,CAAC;QAAI,OAAQ;YAC1B,QAAQ,SAAU,CAAC,EAAE,CAAC;gBAAI,OAAO,EAAE,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,QAAQ;YAAK;QACvE;IAAI;AACR;AAeO,IAAI,QAAQ;IAAc,OAAQ;QAAE,QAAQ,0JAAQ;IAAC;AAAI;AAYzD,IAAI,OAAO;IAAc,OAAQ;QAAE,QAAQ,SAAU,CAAC,EAAE,CAAC;YAAI,OAAO;QAAG;IAAE;AAAI;AAoB7E,IAAI,YAAY,wJAAW;AAW3B,IAAI,gBAAgB,SAAS;AAQ7B,IAAI,qBAAqB;IAAc,OAAQ;QAClD,QAAQ,SAAU,KAAK,EAAE,MAAM;YAAI,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;QAAS;IAChF;AAAI;AAQG,IAAI,mBAAmB;AAQvB,IAAI,oBAAoB;AAQxB,IAAI,oBAAoB;AAQxB,IAAI,qBAAqB;AAQzB,IAAI,mBAAmB;AAQvB,IAAI,mBAAmB;AAQvB,IAAI,mBAAmB;AAQvB,IAAI,0BAA0B;AAC9B,SAAS,KAAK,CAAC;IAClB,IAAI,aAAa,UAAU;IAC3B,OAAO,SAAU,SAAS,EAAE,EAAE;QAAI,OAAQ,OAAO,YAAY,WAAW,aAAa,WAAW,WAAW;IAAM;AACrH;AAQO,IAAI,eAAe;IACtB,QAAQ,SAAU,CAAC,EAAE,CAAC;QAAI,OAAO,KAAK;IAAG;AAC7C;AAQO,IAAI,eAAe;IACtB,QAAQ,SAAU,CAAC,EAAE,CAAC;QAAI,OAAO,KAAK;IAAG;AAC7C;AAQO,IAAI,uBAAuB,8JAAY;AAQvC,IAAI,kBAAkB;IACzB,QAAQ,SAAU,CAAC,EAAE,CAAC;QAAI,OAAO,IAAI;IAAG;AAC5C;AAQO,IAAI,eAAe;IACtB,QAAQ,SAAU,CAAC,EAAE,CAAC;QAAI,OAAO,IAAI;IAAG;AAC5C;AAQO,IAAI,mBAAmB;IAC1B,QAAQ,SAAU,CAAC,EAAE,CAAC;QAAI,OAAO,IAAI;IAAG;AAC5C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1799, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/fp-ts/es6/Separated.js"], "sourcesContent": ["/**\n * ```ts\n * interface Separated<E, A> {\n *    readonly left: E\n *    readonly right: A\n * }\n * ```\n *\n * Represents a result of separating a whole into two parts.\n *\n * @since 2.10.0\n */\nimport { pipe } from './function';\nimport { flap as flap_ } from './Functor';\n// -------------------------------------------------------------------------------------\n// constructors\n// -------------------------------------------------------------------------------------\n/**\n * @category constructors\n * @since 2.10.0\n */\nexport var separated = function (left, right) { return ({ left: left, right: right }); };\nvar _map = function (fa, f) { return pipe(fa, map(f)); };\nvar _mapLeft = function (fa, f) { return pipe(fa, mapLeft(f)); };\nvar _bimap = function (fa, g, f) { return pipe(fa, bimap(g, f)); };\n/**\n * `map` can be used to turn functions `(a: A) => B` into functions `(fa: F<A>) => F<B>` whose argument and return types\n * use the type constructor `F` to represent some computational context.\n *\n * @category mapping\n * @since 2.10.0\n */\nexport var map = function (f) {\n    return function (fa) {\n        return separated(left(fa), f(right(fa)));\n    };\n};\n/**\n * Map a function over the first type argument of a bifunctor.\n *\n * @category error handling\n * @since 2.10.0\n */\nexport var mapLeft = function (f) {\n    return function (fa) {\n        return separated(f(left(fa)), right(fa));\n    };\n};\n/**\n * Map a pair of functions over the two type arguments of the bifunctor.\n *\n * @category mapping\n * @since 2.10.0\n */\nexport var bimap = function (f, g) {\n    return function (fa) {\n        return separated(f(left(fa)), g(right(fa)));\n    };\n};\n/**\n * @category type lambdas\n * @since 2.10.0\n */\nexport var URI = 'Separated';\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var Bifunctor = {\n    URI: URI,\n    mapLeft: _mapLeft,\n    bimap: _bimap\n};\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var Functor = {\n    URI: URI,\n    map: _map\n};\n/**\n * @category mapping\n * @since 2.10.0\n */\nexport var flap = /*#__PURE__*/ flap_(Functor);\n// -------------------------------------------------------------------------------------\n// utils\n// -------------------------------------------------------------------------------------\n/**\n * @since 2.10.0\n */\nexport var left = function (s) { return s.left; };\n/**\n * @since 2.10.0\n */\nexport var right = function (s) { return s.right; };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;;;;;;;;;;;;;;;;;;;;AACD;AACA;;;AAQO,IAAI,YAAY,SAAU,IAAI,EAAE,KAAK;IAAI,OAAQ;QAAE,MAAM;QAAM,OAAO;IAAM;AAAI;AACvF,IAAI,OAAO,SAAU,EAAE,EAAE,CAAC;IAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,IAAI;AAAK;AACvD,IAAI,WAAW,SAAU,EAAE,EAAE,CAAC;IAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,QAAQ;AAAK;AAC/D,IAAI,SAAS,SAAU,EAAE,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,MAAM,GAAG;AAAK;AAQ1D,IAAI,MAAM,SAAU,CAAC;IACxB,OAAO,SAAU,EAAE;QACf,OAAO,UAAU,KAAK,KAAK,EAAE,MAAM;IACvC;AACJ;AAOO,IAAI,UAAU,SAAU,CAAC;IAC5B,OAAO,SAAU,EAAE;QACf,OAAO,UAAU,EAAE,KAAK,MAAM,MAAM;IACxC;AACJ;AAOO,IAAI,QAAQ,SAAU,CAAC,EAAE,CAAC;IAC7B,OAAO,SAAU,EAAE;QACf,OAAO,UAAU,EAAE,KAAK,MAAM,EAAE,MAAM;IAC1C;AACJ;AAKO,IAAI,MAAM;AAKV,IAAI,YAAY;IACnB,KAAK;IACL,SAAS;IACT,OAAO;AACX;AAKO,IAAI,UAAU;IACjB,KAAK;IACL,KAAK;AACT;AAKO,IAAI,OAAO,WAAW,GAAG,IAAA,qJAAK,EAAC;AAO/B,IAAI,OAAO,SAAU,CAAC;IAAI,OAAO,EAAE,IAAI;AAAE;AAIzC,IAAI,QAAQ,SAAU,CAAC;IAAI,OAAO,EAAE,KAAK;AAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1887, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/fp-ts/es6/Witherable.js"], "sourcesContent": ["import * as _ from './internal';\nexport function wiltDefault(T, C) {\n    return function (F) {\n        var traverseF = T.traverse(F);\n        return function (wa, f) { return F.map(traverseF(wa, f), C.separate); };\n    };\n}\nexport function witherDefault(T, C) {\n    return function (F) {\n        var traverseF = T.traverse(F);\n        return function (wa, f) { return F.map(traverseF(wa, f), C.compact); };\n    };\n}\nexport function filterE(W) {\n    return function (F) {\n        var witherF = W.wither(F);\n        return function (predicate) { return function (ga) { return witherF(ga, function (a) { return F.map(predicate(a), function (b) { return (b ? _.some(a) : _.none); }); }); }; };\n    };\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AACO,SAAS,YAAY,CAAC,EAAE,CAAC;IAC5B,OAAO,SAAU,CAAC;QACd,IAAI,YAAY,EAAE,QAAQ,CAAC;QAC3B,OAAO,SAAU,EAAE,EAAE,CAAC;YAAI,OAAO,EAAE,GAAG,CAAC,UAAU,IAAI,IAAI,EAAE,QAAQ;QAAG;IAC1E;AACJ;AACO,SAAS,cAAc,CAAC,EAAE,CAAC;IAC9B,OAAO,SAAU,CAAC;QACd,IAAI,YAAY,EAAE,QAAQ,CAAC;QAC3B,OAAO,SAAU,EAAE,EAAE,CAAC;YAAI,OAAO,EAAE,GAAG,CAAC,UAAU,IAAI,IAAI,EAAE,OAAO;QAAG;IACzE;AACJ;AACO,SAAS,QAAQ,CAAC;IACrB,OAAO,SAAU,CAAC;QACd,IAAI,UAAU,EAAE,MAAM,CAAC;QACvB,OAAO,SAAU,SAAS;YAAI,OAAO,SAAU,EAAE;gBAAI,OAAO,QAAQ,IAAI,SAAU,CAAC;oBAAI,OAAO,EAAE,GAAG,CAAC,UAAU,IAAI,SAAU,CAAC;wBAAI,OAAQ,IAAI,sJAAM,CAAC,KAAK,sJAAM;oBAAG;gBAAI;YAAI;QAAG;IACjL;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1931, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/fp-ts/es6/Zero.js"], "sourcesContent": ["export function guard(F, P) {\n    return function (b) { return (b ? P.of(undefined) : F.zero()); };\n}\n"], "names": [], "mappings": ";;;;AAAO,SAAS,MAAM,CAAC,EAAE,CAAC;IACtB,OAAO,SAAU,CAAC;QAAI,OAAQ,IAAI,EAAE,EAAE,CAAC,aAAa,EAAE,IAAI;IAAK;AACnE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1944, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/fp-ts/es6/Option.js"], "sourcesContent": ["import { getApplicativeMonoid } from './Applicative';\nimport { apFirst as apFirst_, apS as apS_, apSecond as apSecond_, getApplySemigroup as getApplySemigroup_ } from './Apply';\nimport * as chainable from './Chain';\nimport { chainEitherK as chainEitherK_, fromEitherK as fromEitherK_, tapEither as tapEither_ } from './FromEither';\nimport { constNull, constUndefined, dual, flow, identity, pipe } from './function';\nimport { as as as_, asUnit as asUnit_, bindTo as bindTo_, flap as flap_, let as let__ } from './Functor';\nimport * as _ from './internal';\nimport { not } from './Predicate';\nimport { first, last } from './Semigroup';\nimport { separated } from './Separated';\nimport { wiltDefault, witherDefault } from './Witherable';\nimport { guard as guard_ } from './Zero';\n// -------------------------------------------------------------------------------------\n// constructors\n// -------------------------------------------------------------------------------------\n/**\n * `None` doesn't have a constructor, instead you can use it directly as a value. Represents a missing value.\n *\n * @category constructors\n * @since 2.0.0\n */\nexport var none = _.none;\n/**\n * Constructs a `Some`. Represents an optional value that exists.\n *\n * @category constructors\n * @since 2.0.0\n */\nexport var some = _.some;\nexport function fromPredicate(predicate) {\n    return function (a) { return (predicate(a) ? some(a) : none); };\n}\n/**\n * Returns the `Left` value of an `Either` if possible.\n *\n * @example\n * import { getLeft, none, some } from 'fp-ts/Option'\n * import { right, left } from 'fp-ts/Either'\n *\n * assert.deepStrictEqual(getLeft(right(1)), none)\n * assert.deepStrictEqual(getLeft(left('a')), some('a'))\n *\n * @category constructors\n * @since 2.0.0\n */\nexport var getLeft = function (ma) { return (ma._tag === 'Right' ? none : some(ma.left)); };\n/**\n * Returns the `Right` value of an `Either` if possible.\n *\n * @example\n * import { getRight, none, some } from 'fp-ts/Option'\n * import { right, left } from 'fp-ts/Either'\n *\n * assert.deepStrictEqual(getRight(right(1)), some(1))\n * assert.deepStrictEqual(getRight(left('a')), none)\n *\n * @category constructors\n * @since 2.0.0\n */\nexport var getRight = function (ma) { return (ma._tag === 'Left' ? none : some(ma.right)); };\nvar _map = function (fa, f) { return pipe(fa, map(f)); };\nvar _ap = function (fab, fa) { return pipe(fab, ap(fa)); };\nvar _reduce = function (fa, b, f) { return pipe(fa, reduce(b, f)); };\nvar _foldMap = function (M) {\n    var foldMapM = foldMap(M);\n    return function (fa, f) { return pipe(fa, foldMapM(f)); };\n};\nvar _reduceRight = function (fa, b, f) { return pipe(fa, reduceRight(b, f)); };\nvar _traverse = function (F) {\n    var traverseF = traverse(F);\n    return function (ta, f) { return pipe(ta, traverseF(f)); };\n};\n/* istanbul ignore next */\nvar _alt = function (fa, that) { return pipe(fa, alt(that)); };\nvar _filter = function (fa, predicate) { return pipe(fa, filter(predicate)); };\n/* istanbul ignore next */\nvar _filterMap = function (fa, f) { return pipe(fa, filterMap(f)); };\n/* istanbul ignore next */\nvar _extend = function (wa, f) { return pipe(wa, extend(f)); };\n/* istanbul ignore next */\nvar _partition = function (fa, predicate) {\n    return pipe(fa, partition(predicate));\n};\n/* istanbul ignore next */\nvar _partitionMap = function (fa, f) { return pipe(fa, partitionMap(f)); };\n/**\n * @category type lambdas\n * @since 2.0.0\n */\nexport var URI = 'Option';\n/**\n * @category instances\n * @since 2.0.0\n */\nexport var getShow = function (S) { return ({\n    show: function (ma) { return (isNone(ma) ? 'none' : \"some(\".concat(S.show(ma.value), \")\")); }\n}); };\n/**\n * @example\n * import { none, some, getEq } from 'fp-ts/Option'\n * import * as N from 'fp-ts/number'\n *\n * const E = getEq(N.Eq)\n * assert.strictEqual(E.equals(none, none), true)\n * assert.strictEqual(E.equals(none, some(1)), false)\n * assert.strictEqual(E.equals(some(1), none), false)\n * assert.strictEqual(E.equals(some(1), some(2)), false)\n * assert.strictEqual(E.equals(some(1), some(1)), true)\n *\n * @category instances\n * @since 2.0.0\n */\nexport var getEq = function (E) { return ({\n    equals: function (x, y) { return x === y || (isNone(x) ? isNone(y) : isNone(y) ? false : E.equals(x.value, y.value)); }\n}); };\n/**\n * The `Ord` instance allows `Option` values to be compared with\n * `compare`, whenever there is an `Ord` instance for\n * the type the `Option` contains.\n *\n * `None` is considered to be less than any `Some` value.\n *\n *\n * @example\n * import { none, some, getOrd } from 'fp-ts/Option'\n * import * as N from 'fp-ts/number'\n *\n * const O = getOrd(N.Ord)\n * assert.strictEqual(O.compare(none, none), 0)\n * assert.strictEqual(O.compare(none, some(1)), -1)\n * assert.strictEqual(O.compare(some(1), none), 1)\n * assert.strictEqual(O.compare(some(1), some(2)), -1)\n * assert.strictEqual(O.compare(some(1), some(1)), 0)\n *\n * @category instances\n * @since 2.0.0\n */\nexport var getOrd = function (O) { return ({\n    equals: getEq(O).equals,\n    compare: function (x, y) { return (x === y ? 0 : isSome(x) ? (isSome(y) ? O.compare(x.value, y.value) : 1) : isSome(y) ? -1 : 0); }\n}); };\n/**\n * Monoid returning the left-most non-`None` value. If both operands are `Some`s then the inner values are\n * concatenated using the provided `Semigroup`\n *\n * | x       | y       | concat(x, y)       |\n * | ------- | ------- | ------------------ |\n * | none    | none    | none               |\n * | some(a) | none    | some(a)            |\n * | none    | some(b) | some(b)            |\n * | some(a) | some(b) | some(concat(a, b)) |\n *\n * @example\n * import { getMonoid, some, none } from 'fp-ts/Option'\n * import { SemigroupSum } from 'fp-ts/number'\n *\n * const M = getMonoid(SemigroupSum)\n * assert.deepStrictEqual(M.concat(none, none), none)\n * assert.deepStrictEqual(M.concat(some(1), none), some(1))\n * assert.deepStrictEqual(M.concat(none, some(1)), some(1))\n * assert.deepStrictEqual(M.concat(some(1), some(2)), some(3))\n *\n * @category instances\n * @since 2.0.0\n */\nexport var getMonoid = function (S) { return ({\n    concat: function (x, y) { return (isNone(x) ? y : isNone(y) ? x : some(S.concat(x.value, y.value))); },\n    empty: none\n}); };\n/**\n * @category mapping\n * @since 2.0.0\n */\nexport var map = function (f) { return function (fa) {\n    return isNone(fa) ? none : some(f(fa.value));\n}; };\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var Functor = {\n    URI: URI,\n    map: _map\n};\n/**\n * Maps the `Some` value of this `Option` to the specified constant value.\n *\n * @category mapping\n * @since 2.16.0\n */\nexport var as = dual(2, as_(Functor));\n/**\n * Maps the `Some` value of this `Option` to the void constant value.\n *\n * @category mapping\n * @since 2.16.0\n */\nexport var asUnit = asUnit_(Functor);\n/**\n * @category constructors\n * @since 2.7.0\n */\nexport var of = some;\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var Pointed = {\n    URI: URI,\n    of: of\n};\n/**\n * @since 2.0.0\n */\nexport var ap = function (fa) { return function (fab) {\n    return isNone(fab) ? none : isNone(fa) ? none : some(fab.value(fa.value));\n}; };\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var Apply = {\n    URI: URI,\n    map: _map,\n    ap: _ap\n};\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var Applicative = {\n    URI: URI,\n    map: _map,\n    ap: _ap,\n    of: of\n};\n/**\n * @category sequencing\n * @since 2.14.0\n */\nexport var flatMap = /*#__PURE__*/ dual(2, function (ma, f) { return (isNone(ma) ? none : f(ma.value)); });\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var Chain = {\n    URI: URI,\n    map: _map,\n    ap: _ap,\n    chain: flatMap\n};\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var Monad = {\n    URI: URI,\n    map: _map,\n    ap: _ap,\n    of: of,\n    chain: flatMap\n};\n/**\n * @category folding\n * @since 2.0.0\n */\nexport var reduce = function (b, f) { return function (fa) {\n    return isNone(fa) ? b : f(b, fa.value);\n}; };\n/**\n * @category folding\n * @since 2.0.0\n */\nexport var foldMap = function (M) { return function (f) { return function (fa) {\n    return isNone(fa) ? M.empty : f(fa.value);\n}; }; };\n/**\n * @category folding\n * @since 2.0.0\n */\nexport var reduceRight = function (b, f) { return function (fa) {\n    return isNone(fa) ? b : f(fa.value, b);\n}; };\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var Foldable = {\n    URI: URI,\n    reduce: _reduce,\n    foldMap: _foldMap,\n    reduceRight: _reduceRight\n};\n/**\n * Returns the provided `Option` `that` if `self` is `None`, otherwise returns `self`.\n *\n * @param self - The first `Option` to be checked.\n * @param that - The `Option` to return if `self` is `None`.\n *\n * @example\n * import * as O from \"fp-ts/Option\"\n *\n * assert.deepStrictEqual(O.orElse(O.none, () => O.none), O.none)\n * assert.deepStrictEqual(O.orElse(O.some(1), () => O.none), O.some(1))\n * assert.deepStrictEqual(O.orElse(O.none, () => O.some('b')), O.some('b'))\n * assert.deepStrictEqual(O.orElse(O.some(1), () => O.some('b')), O.some(1))\n *\n * @category error handling\n * @since 2.16.0\n */\nexport var orElse = dual(2, function (self, that) { return (isNone(self) ? that() : self); });\n/**\n * Alias of `orElse`.\n *\n * Less strict version of [`alt`](#alt).\n *\n * The `W` suffix (short for **W**idening) means that the return types will be merged.\n *\n * @category legacy\n * @since 2.9.0\n */\nexport var altW = orElse;\n/**\n * Alias of `orElse`.\n *\n * @category legacy\n * @since 2.0.0\n */\nexport var alt = orElse;\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var Alt = {\n    URI: URI,\n    map: _map,\n    alt: _alt\n};\n/**\n * @since 2.7.0\n */\nexport var zero = function () { return none; };\n/**\n * @category instances\n * @since 2.11.0\n */\nexport var Zero = {\n    URI: URI,\n    zero: zero\n};\n/**\n * @category do notation\n * @since 2.11.0\n */\nexport var guard = /*#__PURE__*/ guard_(Zero, Pointed);\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var Alternative = {\n    URI: URI,\n    map: _map,\n    ap: _ap,\n    of: of,\n    alt: _alt,\n    zero: zero\n};\n/**\n * @since 2.0.0\n */\nexport var extend = function (f) { return function (wa) {\n    return isNone(wa) ? none : some(f(wa));\n}; };\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var Extend = {\n    URI: URI,\n    map: _map,\n    extend: _extend\n};\n/**\n * @category filtering\n * @since 2.0.0\n */\nexport var compact = /*#__PURE__*/ flatMap(identity);\nvar defaultSeparated = /*#__PURE__*/ separated(none, none);\n/**\n * @category filtering\n * @since 2.0.0\n */\nexport var separate = function (ma) {\n    return isNone(ma) ? defaultSeparated : separated(getLeft(ma.value), getRight(ma.value));\n};\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var Compactable = {\n    URI: URI,\n    compact: compact,\n    separate: separate\n};\n/**\n * @category filtering\n * @since 2.0.0\n */\nexport var filter = function (predicate) {\n    return function (fa) {\n        return isNone(fa) ? none : predicate(fa.value) ? fa : none;\n    };\n};\n/**\n * @category filtering\n * @since 2.0.0\n */\nexport var filterMap = function (f) { return function (fa) {\n    return isNone(fa) ? none : f(fa.value);\n}; };\n/**\n * @category filtering\n * @since 2.0.0\n */\nexport var partition = function (predicate) {\n    return function (fa) {\n        return separated(_filter(fa, not(predicate)), _filter(fa, predicate));\n    };\n};\n/**\n * @category filtering\n * @since 2.0.0\n */\nexport var partitionMap = function (f) { return flow(map(f), separate); };\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var Filterable = {\n    URI: URI,\n    map: _map,\n    compact: compact,\n    separate: separate,\n    filter: _filter,\n    filterMap: _filterMap,\n    partition: _partition,\n    partitionMap: _partitionMap\n};\n/**\n * @category traversing\n * @since 2.6.3\n */\nexport var traverse = function (F) {\n    return function (f) {\n        return function (ta) {\n            return isNone(ta) ? F.of(none) : F.map(f(ta.value), some);\n        };\n    };\n};\n/**\n * @category traversing\n * @since 2.6.3\n */\nexport var sequence = function (F) {\n    return function (ta) {\n        return isNone(ta) ? F.of(none) : F.map(ta.value, some);\n    };\n};\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var Traversable = {\n    URI: URI,\n    map: _map,\n    reduce: _reduce,\n    foldMap: _foldMap,\n    reduceRight: _reduceRight,\n    traverse: _traverse,\n    sequence: sequence\n};\nvar _wither = /*#__PURE__*/ witherDefault(Traversable, Compactable);\nvar _wilt = /*#__PURE__*/ wiltDefault(Traversable, Compactable);\n/**\n * @category filtering\n * @since 2.6.5\n */\nexport var wither = function (F) {\n    var _witherF = _wither(F);\n    return function (f) { return function (fa) { return _witherF(fa, f); }; };\n};\n/**\n * @category filtering\n * @since 2.6.5\n */\nexport var wilt = function (F) {\n    var _wiltF = _wilt(F);\n    return function (f) { return function (fa) { return _wiltF(fa, f); }; };\n};\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var Witherable = {\n    URI: URI,\n    map: _map,\n    reduce: _reduce,\n    foldMap: _foldMap,\n    reduceRight: _reduceRight,\n    traverse: _traverse,\n    sequence: sequence,\n    compact: compact,\n    separate: separate,\n    filter: _filter,\n    filterMap: _filterMap,\n    partition: _partition,\n    partitionMap: _partitionMap,\n    wither: _wither,\n    wilt: _wilt\n};\n/**\n * @since 2.7.0\n */\nexport var throwError = function () { return none; };\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var MonadThrow = {\n    URI: URI,\n    map: _map,\n    ap: _ap,\n    of: of,\n    chain: flatMap,\n    throwError: throwError\n};\n/**\n * Transforms an `Either` to an `Option` discarding the error.\n *\n * Alias of [getRight](#getright)\n *\n * @category conversions\n * @since 2.0.0\n */\nexport var fromEither = getRight;\n/**\n * @category instances\n * @since 2.11.0\n */\nexport var FromEither = {\n    URI: URI,\n    fromEither: fromEither\n};\n// -------------------------------------------------------------------------------------\n// refinements\n// -------------------------------------------------------------------------------------\n/**\n * Returns `true` if the option is an instance of `Some`, `false` otherwise.\n *\n * @example\n * import { some, none, isSome } from 'fp-ts/Option'\n *\n * assert.strictEqual(isSome(some(1)), true)\n * assert.strictEqual(isSome(none), false)\n *\n * @category refinements\n * @since 2.0.0\n */\nexport var isSome = _.isSome;\n/**\n * Returns `true` if the option is `None`, `false` otherwise.\n *\n * @example\n * import { some, none, isNone } from 'fp-ts/Option'\n *\n * assert.strictEqual(isNone(some(1)), false)\n * assert.strictEqual(isNone(none), true)\n *\n * @category refinements\n * @since 2.0.0\n */\nexport var isNone = function (fa) { return fa._tag === 'None'; };\n/**\n * Less strict version of [`match`](#match).\n *\n * The `W` suffix (short for **W**idening) means that the handler return types will be merged.\n *\n * @category pattern matching\n * @since 2.10.0\n */\nexport var matchW = function (onNone, onSome) {\n    return function (ma) {\n        return isNone(ma) ? onNone() : onSome(ma.value);\n    };\n};\n/**\n * Alias of [`matchW`](#matchw).\n *\n * @category pattern matching\n * @since 2.10.0\n */\nexport var foldW = matchW;\n/**\n * Takes a (lazy) default value, a function, and an `Option` value, if the `Option` value is `None` the default value is\n * returned, otherwise the function is applied to the value inside the `Some` and the result is returned.\n *\n * @example\n * import { some, none, match } from 'fp-ts/Option'\n * import { pipe } from 'fp-ts/function'\n *\n * assert.strictEqual(\n *   pipe(\n *     some(1),\n *     match(() => 'a none', a => `a some containing ${a}`)\n *   ),\n *   'a some containing 1'\n * )\n *\n * assert.strictEqual(\n *   pipe(\n *     none,\n *     match(() => 'a none', a => `a some containing ${a}`)\n *   ),\n *   'a none'\n * )\n *\n * @category pattern matching\n * @since 2.10.0\n */\nexport var match = matchW;\n/**\n * Alias of [`match`](#match).\n *\n * @category pattern matching\n * @since 2.0.0\n */\nexport var fold = match;\n/**\n * Less strict version of [`getOrElse`](#getorelse).\n *\n * The `W` suffix (short for **W**idening) means that the handler return type will be merged.\n *\n * @category error handling\n * @since 2.6.0\n */\nexport var getOrElseW = function (onNone) {\n    return function (ma) {\n        return isNone(ma) ? onNone() : ma.value;\n    };\n};\n/**\n * Extracts the value out of the structure, if it exists. Otherwise returns the given default value\n *\n * @example\n * import { some, none, getOrElse } from 'fp-ts/Option'\n * import { pipe } from 'fp-ts/function'\n *\n * assert.strictEqual(\n *   pipe(\n *     some(1),\n *     getOrElse(() => 0)\n *   ),\n *   1\n * )\n * assert.strictEqual(\n *   pipe(\n *     none,\n *     getOrElse(() => 0)\n *   ),\n *   0\n * )\n *\n * @category error handling\n * @since 2.0.0\n */\nexport var getOrElse = getOrElseW;\n/**\n * @category mapping\n * @since 2.10.0\n */\nexport var flap = /*#__PURE__*/ flap_(Functor);\n/**\n * Combine two effectful actions, keeping only the result of the first.\n *\n * @since 2.0.0\n */\nexport var apFirst = /*#__PURE__*/ apFirst_(Apply);\n/**\n * Combine two effectful actions, keeping only the result of the second.\n *\n * @since 2.0.0\n */\nexport var apSecond = /*#__PURE__*/ apSecond_(Apply);\n/**\n * @category sequencing\n * @since 2.0.0\n */\nexport var flatten = compact;\n/**\n * Composes computations in sequence, using the return value of one computation to determine the next computation and\n * keeping only the result of the first.\n *\n * @category combinators\n * @since 2.15.0\n */\nexport var tap = /*#__PURE__*/ dual(2, chainable.tap(Chain));\n/**\n * Composes computations in sequence, using the return value of one computation to determine the next computation and\n * keeping only the result of the first.\n *\n * @example\n * import { pipe } from 'fp-ts/function'\n * import * as O from 'fp-ts/Option'\n * import * as E from 'fp-ts/Either'\n *\n * const compute = (value: number) => pipe(\n *   O.of(value),\n *   O.tapEither((value) => value > 0 ? E.right('ok') : E.left('error')),\n * )\n *\n * assert.deepStrictEqual(compute(1), O.of(1))\n * assert.deepStrictEqual(compute(-42), O.none)\n *\n * @category combinators\n * @since 2.16.0\n */\nexport var tapEither = /*#__PURE__*/ dual(2, tapEither_(FromEither, Chain));\n/**\n * @since 2.0.0\n */\nexport var duplicate = /*#__PURE__*/ extend(identity);\n/**\n * @category lifting\n * @since 2.11.0\n */\nexport var fromEitherK = /*#__PURE__*/ fromEitherK_(FromEither);\n/**\n * @category sequencing\n * @since 2.11.0\n */\nexport var chainEitherK = \n/*#__PURE__*/ chainEitherK_(FromEither, Chain);\n/**\n * Alias of `tapEither`.\n *\n * @category legacy\n * @since 2.12.0\n */\nexport var chainFirstEitherK = tapEither;\n/**\n * Constructs a new `Option` from a nullable type. If the value is `null` or `undefined`, returns `None`, otherwise\n * returns the value wrapped in a `Some`.\n *\n * @example\n * import { none, some, fromNullable } from 'fp-ts/Option'\n *\n * assert.deepStrictEqual(fromNullable(undefined), none)\n * assert.deepStrictEqual(fromNullable(null), none)\n * assert.deepStrictEqual(fromNullable(1), some(1))\n *\n * @category conversions\n * @since 2.0.0\n */\nexport var fromNullable = function (a) { return (a == null ? none : some(a)); };\n/**\n * Transforms an exception into an `Option`. If `f` throws, returns `None`, otherwise returns the output wrapped in a\n * `Some`.\n *\n * See also [`tryCatchK`](#trycatchk).\n *\n * @example\n * import { none, some, tryCatch } from 'fp-ts/Option'\n *\n * assert.deepStrictEqual(\n *   tryCatch(() => {\n *     throw new Error()\n *   }),\n *   none\n * )\n * assert.deepStrictEqual(tryCatch(() => 1), some(1))\n *\n * @category interop\n * @since 2.0.0\n */\nexport var tryCatch = function (f) {\n    try {\n        return some(f());\n    }\n    catch (e) {\n        return none;\n    }\n};\n/**\n * Converts a function that may throw to one returning a `Option`.\n *\n * @category interop\n * @since 2.10.0\n */\nexport var tryCatchK = function (f) {\n    return function () {\n        var a = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            a[_i] = arguments[_i];\n        }\n        return tryCatch(function () { return f.apply(void 0, a); });\n    };\n};\n/**\n * Returns a *smart constructor* from a function that returns a nullable value.\n *\n * @example\n * import { fromNullableK, none, some } from 'fp-ts/Option'\n *\n * const f = (s: string): number | undefined => {\n *   const n = parseFloat(s)\n *   return isNaN(n) ? undefined : n\n * }\n *\n * const g = fromNullableK(f)\n *\n * assert.deepStrictEqual(g('1'), some(1))\n * assert.deepStrictEqual(g('a'), none)\n *\n * @category lifting\n * @since 2.9.0\n */\nexport var fromNullableK = function (f) { return flow(f, fromNullable); };\n/**\n * This is `chain` + `fromNullable`, useful when working with optional values.\n *\n * @example\n * import { some, none, fromNullable, chainNullableK } from 'fp-ts/Option'\n * import { pipe } from 'fp-ts/function'\n *\n * interface Employee {\n *   readonly company?: {\n *     readonly address?: {\n *       readonly street?: {\n *         readonly name?: string\n *       }\n *     }\n *   }\n * }\n *\n * const employee1: Employee = { company: { address: { street: { name: 'high street' } } } }\n *\n * assert.deepStrictEqual(\n *   pipe(\n *     fromNullable(employee1.company),\n *     chainNullableK(company => company.address),\n *     chainNullableK(address => address.street),\n *     chainNullableK(street => street.name)\n *   ),\n *   some('high street')\n * )\n *\n * const employee2: Employee = { company: { address: { street: {} } } }\n *\n * assert.deepStrictEqual(\n *   pipe(\n *     fromNullable(employee2.company),\n *     chainNullableK(company => company.address),\n *     chainNullableK(address => address.street),\n *     chainNullableK(street => street.name)\n *   ),\n *   none\n * )\n *\n * @category sequencing\n * @since 2.9.0\n */\nexport var chainNullableK = function (f) {\n    return function (ma) {\n        return isNone(ma) ? none : fromNullable(f(ma.value));\n    };\n};\n/**\n * Extracts the value out of the structure, if it exists. Otherwise returns `null`.\n *\n * @example\n * import { some, none, toNullable } from 'fp-ts/Option'\n * import { pipe } from 'fp-ts/function'\n *\n * assert.strictEqual(\n *   pipe(\n *     some(1),\n *     toNullable\n *   ),\n *   1\n * )\n * assert.strictEqual(\n *   pipe(\n *     none,\n *     toNullable\n *   ),\n *   null\n * )\n *\n * @category conversions\n * @since 2.0.0\n */\nexport var toNullable = /*#__PURE__*/ match(constNull, identity);\n/**\n * Extracts the value out of the structure, if it exists. Otherwise returns `undefined`.\n *\n * @example\n * import { some, none, toUndefined } from 'fp-ts/Option'\n * import { pipe } from 'fp-ts/function'\n *\n * assert.strictEqual(\n *   pipe(\n *     some(1),\n *     toUndefined\n *   ),\n *   1\n * )\n * assert.strictEqual(\n *   pipe(\n *     none,\n *     toUndefined\n *   ),\n *   undefined\n * )\n *\n * @category conversions\n * @since 2.0.0\n */\nexport var toUndefined = /*#__PURE__*/ match(constUndefined, identity);\nexport function elem(E) {\n    return function (a, ma) {\n        if (ma === undefined) {\n            var elemE_1 = elem(E);\n            return function (ma) { return elemE_1(a, ma); };\n        }\n        return isNone(ma) ? false : E.equals(a, ma.value);\n    };\n}\n/**\n * Returns `true` if the predicate is satisfied by the wrapped value\n *\n * @example\n * import { some, none, exists } from 'fp-ts/Option'\n * import { pipe } from 'fp-ts/function'\n *\n * assert.strictEqual(\n *   pipe(\n *     some(1),\n *     exists(n => n > 0)\n *   ),\n *   true\n * )\n * assert.strictEqual(\n *   pipe(\n *     some(1),\n *     exists(n => n > 1)\n *   ),\n *   false\n * )\n * assert.strictEqual(\n *   pipe(\n *     none,\n *     exists(n => n > 0)\n *   ),\n *   false\n * )\n *\n * @since 2.0.0\n */\nexport var exists = function (predicate) {\n    return function (ma) {\n        return isNone(ma) ? false : predicate(ma.value);\n    };\n};\n// -------------------------------------------------------------------------------------\n// do notation\n// -------------------------------------------------------------------------------------\n/**\n * @category do notation\n * @since 2.9.0\n */\nexport var Do = /*#__PURE__*/ of(_.emptyRecord);\n/**\n * @category do notation\n * @since 2.8.0\n */\nexport var bindTo = /*#__PURE__*/ bindTo_(Functor);\nvar let_ = /*#__PURE__*/ let__(Functor);\nexport { \n/**\n * @category do notation\n * @since 2.13.0\n */\nlet_ as let };\n/**\n * @category do notation\n * @since 2.8.0\n */\nexport var bind = /*#__PURE__*/ chainable.bind(Chain);\n/**\n * @category do notation\n * @since 2.8.0\n */\nexport var apS = /*#__PURE__*/ apS_(Apply);\n/**\n * @since 2.11.0\n */\nexport var ApT = /*#__PURE__*/ of(_.emptyReadonlyArray);\n// -------------------------------------------------------------------------------------\n// array utils\n// -------------------------------------------------------------------------------------\n/**\n * Equivalent to `ReadonlyNonEmptyArray#traverseWithIndex(Applicative)`.\n *\n * @category traversing\n * @since 2.11.0\n */\nexport var traverseReadonlyNonEmptyArrayWithIndex = function (f) {\n    return function (as) {\n        var o = f(0, _.head(as));\n        if (isNone(o)) {\n            return none;\n        }\n        var out = [o.value];\n        for (var i = 1; i < as.length; i++) {\n            var o_1 = f(i, as[i]);\n            if (isNone(o_1)) {\n                return none;\n            }\n            out.push(o_1.value);\n        }\n        return some(out);\n    };\n};\n/**\n * Equivalent to `ReadonlyArray#traverseWithIndex(Applicative)`.\n *\n * @category traversing\n * @since 2.11.0\n */\nexport var traverseReadonlyArrayWithIndex = function (f) {\n    var g = traverseReadonlyNonEmptyArrayWithIndex(f);\n    return function (as) { return (_.isNonEmpty(as) ? g(as) : ApT); };\n};\n/**\n * Equivalent to `ReadonlyArray#traverseWithIndex(Applicative)`.\n *\n * @category traversing\n * @since 2.9.0\n */\nexport var traverseArrayWithIndex = traverseReadonlyArrayWithIndex;\n/**\n * Equivalent to `ReadonlyArray#traverse(Applicative)`.\n *\n * @category traversing\n * @since 2.9.0\n */\nexport var traverseArray = function (f) {\n    return traverseReadonlyArrayWithIndex(function (_, a) { return f(a); });\n};\n/**\n * Equivalent to `ReadonlyArray#sequence(Applicative)`.\n *\n * @category traversing\n * @since 2.9.0\n */\nexport var sequenceArray = \n/*#__PURE__*/ traverseArray(identity);\n// -------------------------------------------------------------------------------------\n// legacy\n// -------------------------------------------------------------------------------------\n/**\n * Alias of `flatMap`.\n *\n * @category legacy\n * @since 2.0.0\n */\nexport var chain = flatMap;\n/**\n * Alias of `tap`.\n *\n * @category legacy\n * @since 2.0.0\n */\nexport var chainFirst = tap;\n// -------------------------------------------------------------------------------------\n// deprecated\n// -------------------------------------------------------------------------------------\n/**\n * Use `Refinement` module instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport function getRefinement(getOption) {\n    return function (a) { return isSome(getOption(a)); };\n}\n/**\n * Use [`chainNullableK`](#chainnullablek) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var mapNullable = chainNullableK;\n/**\n * This instance is deprecated, use small, specific instances instead.\n * For example if a function needs a `Functor` instance, pass `O.Functor` instead of `O.option`\n * (where `O` is from `import O from 'fp-ts/Option'`)\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var option = {\n    URI: URI,\n    map: _map,\n    of: of,\n    ap: _ap,\n    chain: flatMap,\n    reduce: _reduce,\n    foldMap: _foldMap,\n    reduceRight: _reduceRight,\n    traverse: _traverse,\n    sequence: sequence,\n    zero: zero,\n    alt: _alt,\n    extend: _extend,\n    compact: compact,\n    separate: separate,\n    filter: _filter,\n    filterMap: _filterMap,\n    partition: _partition,\n    partitionMap: _partitionMap,\n    wither: _wither,\n    wilt: _wilt,\n    throwError: throwError\n};\n/**\n * Use [`getApplySemigroup`](./Apply.ts.html#getapplysemigroup) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var getApplySemigroup = /*#__PURE__*/ getApplySemigroup_(Apply);\n/**\n * Use [`getApplicativeMonoid`](./Applicative.ts.html#getapplicativemonoid) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var getApplyMonoid = /*#__PURE__*/ getApplicativeMonoid(Applicative);\n/**\n * Use\n *\n * ```ts\n * import { first } from 'fp-ts/Semigroup'\n * import { getMonoid } from 'fp-ts/Option'\n *\n * getMonoid(first())\n * ```\n *\n * instead.\n *\n * Monoid returning the left-most non-`None` value\n *\n * | x       | y       | concat(x, y) |\n * | ------- | ------- | ------------ |\n * | none    | none    | none         |\n * | some(a) | none    | some(a)      |\n * | none    | some(b) | some(b)      |\n * | some(a) | some(b) | some(a)      |\n *\n * @example\n * import { getFirstMonoid, some, none } from 'fp-ts/Option'\n *\n * const M = getFirstMonoid<number>()\n * assert.deepStrictEqual(M.concat(none, none), none)\n * assert.deepStrictEqual(M.concat(some(1), none), some(1))\n * assert.deepStrictEqual(M.concat(none, some(2)), some(2))\n * assert.deepStrictEqual(M.concat(some(1), some(2)), some(1))\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var getFirstMonoid = function () { return getMonoid(first()); };\n/**\n * Use\n *\n * ```ts\n * import { last } from 'fp-ts/Semigroup'\n * import { getMonoid } from 'fp-ts/Option'\n *\n * getMonoid(last())\n * ```\n *\n * instead.\n *\n * Monoid returning the right-most non-`None` value\n *\n * | x       | y       | concat(x, y) |\n * | ------- | ------- | ------------ |\n * | none    | none    | none         |\n * | some(a) | none    | some(a)      |\n * | none    | some(b) | some(b)      |\n * | some(a) | some(b) | some(b)      |\n *\n * @example\n * import { getLastMonoid, some, none } from 'fp-ts/Option'\n *\n * const M = getLastMonoid<number>()\n * assert.deepStrictEqual(M.concat(none, none), none)\n * assert.deepStrictEqual(M.concat(some(1), none), some(1))\n * assert.deepStrictEqual(M.concat(none, some(2)), some(2))\n * assert.deepStrictEqual(M.concat(some(1), some(2)), some(2))\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var getLastMonoid = function () { return getMonoid(last()); };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AAUO,IAAI,OAAO,sJAAM;AAOjB,IAAI,OAAO,sJAAM;AACjB,SAAS,cAAc,SAAS;IACnC,OAAO,SAAU,CAAC;QAAI,OAAQ,UAAU,KAAK,KAAK,KAAK;IAAO;AAClE;AAcO,IAAI,UAAU,SAAU,EAAE;IAAI,OAAQ,GAAG,IAAI,KAAK,UAAU,OAAO,KAAK,GAAG,IAAI;AAAI;AAcnF,IAAI,WAAW,SAAU,EAAE;IAAI,OAAQ,GAAG,IAAI,KAAK,SAAS,OAAO,KAAK,GAAG,KAAK;AAAI;AAC3F,IAAI,OAAO,SAAU,EAAE,EAAE,CAAC;IAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,IAAI;AAAK;AACvD,IAAI,MAAM,SAAU,GAAG,EAAE,EAAE;IAAI,OAAO,IAAA,sJAAI,EAAC,KAAK,GAAG;AAAM;AACzD,IAAI,UAAU,SAAU,EAAE,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,OAAO,GAAG;AAAK;AACnE,IAAI,WAAW,SAAU,CAAC;IACtB,IAAI,WAAW,QAAQ;IACvB,OAAO,SAAU,EAAE,EAAE,CAAC;QAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,SAAS;IAAK;AAC5D;AACA,IAAI,eAAe,SAAU,EAAE,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,YAAY,GAAG;AAAK;AAC7E,IAAI,YAAY,SAAU,CAAC;IACvB,IAAI,YAAY,SAAS;IACzB,OAAO,SAAU,EAAE,EAAE,CAAC;QAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,UAAU;IAAK;AAC7D;AACA,wBAAwB,GACxB,IAAI,OAAO,SAAU,EAAE,EAAE,IAAI;IAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,IAAI;AAAQ;AAC7D,IAAI,UAAU,SAAU,EAAE,EAAE,SAAS;IAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,OAAO;AAAa;AAC7E,wBAAwB,GACxB,IAAI,aAAa,SAAU,EAAE,EAAE,CAAC;IAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,UAAU;AAAK;AACnE,wBAAwB,GACxB,IAAI,UAAU,SAAU,EAAE,EAAE,CAAC;IAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,OAAO;AAAK;AAC7D,wBAAwB,GACxB,IAAI,aAAa,SAAU,EAAE,EAAE,SAAS;IACpC,OAAO,IAAA,sJAAI,EAAC,IAAI,UAAU;AAC9B;AACA,wBAAwB,GACxB,IAAI,gBAAgB,SAAU,EAAE,EAAE,CAAC;IAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,aAAa;AAAK;AAKlE,IAAI,MAAM;AAKV,IAAI,UAAU,SAAU,CAAC;IAAI,OAAQ;QACxC,MAAM,SAAU,EAAE;YAAI,OAAQ,OAAO,MAAM,SAAS,QAAQ,MAAM,CAAC,EAAE,IAAI,CAAC,GAAG,KAAK,GAAG;QAAO;IAChG;AAAI;AAgBG,IAAI,QAAQ,SAAU,CAAC;IAAI,OAAQ;QACtC,QAAQ,SAAU,CAAC,EAAE,CAAC;YAAI,OAAO,MAAM,KAAK,CAAC,OAAO,KAAK,OAAO,KAAK,OAAO,KAAK,QAAQ,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,EAAE,KAAK,CAAC;QAAG;IAC1H;AAAI;AAuBG,IAAI,SAAS,SAAU,CAAC;IAAI,OAAQ;QACvC,QAAQ,MAAM,GAAG,MAAM;QACvB,SAAS,SAAU,CAAC,EAAE,CAAC;YAAI,OAAQ,MAAM,IAAI,IAAI,OAAO,KAAM,OAAO,KAAK,EAAE,OAAO,CAAC,EAAE,KAAK,EAAE,EAAE,KAAK,IAAI,IAAK,OAAO,KAAK,CAAC,IAAI;QAAI;IACtI;AAAI;AAyBG,IAAI,YAAY,SAAU,CAAC;IAAI,OAAQ;QAC1C,QAAQ,SAAU,CAAC,EAAE,CAAC;YAAI,OAAQ,OAAO,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,EAAE,KAAK;QAAK;QACrG,OAAO;IACX;AAAI;AAKG,IAAI,MAAM,SAAU,CAAC;IAAI,OAAO,SAAU,EAAE;QAC/C,OAAO,OAAO,MAAM,OAAO,KAAK,EAAE,GAAG,KAAK;IAC9C;AAAG;AAKI,IAAI,UAAU;IACjB,KAAK;IACL,KAAK;AACT;AAOO,IAAI,KAAK,IAAA,sJAAI,EAAC,GAAG,IAAA,mJAAG,EAAC;AAOrB,IAAI,SAAS,IAAA,uJAAO,EAAC;AAKrB,IAAI,KAAK;AAKT,IAAI,UAAU;IACjB,KAAK;IACL,IAAI;AACR;AAIO,IAAI,KAAK,SAAU,EAAE;IAAI,OAAO,SAAU,GAAG;QAChD,OAAO,OAAO,OAAO,OAAO,OAAO,MAAM,OAAO,KAAK,IAAI,KAAK,CAAC,GAAG,KAAK;IAC3E;AAAG;AAKI,IAAI,QAAQ;IACf,KAAK;IACL,KAAK;IACL,IAAI;AACR;AAKO,IAAI,cAAc;IACrB,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;AACR;AAKO,IAAI,UAAU,WAAW,GAAG,IAAA,sJAAI,EAAC,GAAG,SAAU,EAAE,EAAE,CAAC;IAAI,OAAQ,OAAO,MAAM,OAAO,EAAE,GAAG,KAAK;AAAI;AAKjG,IAAI,QAAQ;IACf,KAAK;IACL,KAAK;IACL,IAAI;IACJ,OAAO;AACX;AAKO,IAAI,QAAQ;IACf,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,OAAO;AACX;AAKO,IAAI,SAAS,SAAU,CAAC,EAAE,CAAC;IAAI,OAAO,SAAU,EAAE;QACrD,OAAO,OAAO,MAAM,IAAI,EAAE,GAAG,GAAG,KAAK;IACzC;AAAG;AAKI,IAAI,UAAU,SAAU,CAAC;IAAI,OAAO,SAAU,CAAC;QAAI,OAAO,SAAU,EAAE;YACzE,OAAO,OAAO,MAAM,EAAE,KAAK,GAAG,EAAE,GAAG,KAAK;QAC5C;IAAG;AAAG;AAKC,IAAI,cAAc,SAAU,CAAC,EAAE,CAAC;IAAI,OAAO,SAAU,EAAE;QAC1D,OAAO,OAAO,MAAM,IAAI,EAAE,GAAG,KAAK,EAAE;IACxC;AAAG;AAKI,IAAI,WAAW;IAClB,KAAK;IACL,QAAQ;IACR,SAAS;IACT,aAAa;AACjB;AAkBO,IAAI,SAAS,IAAA,sJAAI,EAAC,GAAG,SAAU,IAAI,EAAE,IAAI;IAAI,OAAQ,OAAO,QAAQ,SAAS;AAAO;AAWpF,IAAI,OAAO;AAOX,IAAI,MAAM;AAKV,IAAI,MAAM;IACb,KAAK;IACL,KAAK;IACL,KAAK;AACT;AAIO,IAAI,OAAO;IAAc,OAAO;AAAM;AAKtC,IAAI,OAAO;IACd,KAAK;IACL,MAAM;AACV;AAKO,IAAI,QAAQ,WAAW,GAAG,IAAA,mJAAM,EAAC,MAAM;AAKvC,IAAI,cAAc;IACrB,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,MAAM;AACV;AAIO,IAAI,SAAS,SAAU,CAAC;IAAI,OAAO,SAAU,EAAE;QAClD,OAAO,OAAO,MAAM,OAAO,KAAK,EAAE;IACtC;AAAG;AAKI,IAAI,SAAS;IAChB,KAAK;IACL,KAAK;IACL,QAAQ;AACZ;AAKO,IAAI,UAAU,WAAW,GAAG,QAAQ,0JAAQ;AACnD,IAAI,mBAAmB,WAAW,GAAG,IAAA,4JAAS,EAAC,MAAM;AAK9C,IAAI,WAAW,SAAU,EAAE;IAC9B,OAAO,OAAO,MAAM,mBAAmB,IAAA,4JAAS,EAAC,QAAQ,GAAG,KAAK,GAAG,SAAS,GAAG,KAAK;AACzF;AAKO,IAAI,cAAc;IACrB,KAAK;IACL,SAAS;IACT,UAAU;AACd;AAKO,IAAI,SAAS,SAAU,SAAS;IACnC,OAAO,SAAU,EAAE;QACf,OAAO,OAAO,MAAM,OAAO,UAAU,GAAG,KAAK,IAAI,KAAK;IAC1D;AACJ;AAKO,IAAI,YAAY,SAAU,CAAC;IAAI,OAAO,SAAU,EAAE;QACrD,OAAO,OAAO,MAAM,OAAO,EAAE,GAAG,KAAK;IACzC;AAAG;AAKI,IAAI,YAAY,SAAU,SAAS;IACtC,OAAO,SAAU,EAAE;QACf,OAAO,IAAA,4JAAS,EAAC,QAAQ,IAAI,IAAA,sJAAG,EAAC,aAAa,QAAQ,IAAI;IAC9D;AACJ;AAKO,IAAI,eAAe,SAAU,CAAC;IAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,IAAI;AAAW;AAKjE,IAAI,aAAa;IACpB,KAAK;IACL,KAAK;IACL,SAAS;IACT,UAAU;IACV,QAAQ;IACR,WAAW;IACX,WAAW;IACX,cAAc;AAClB;AAKO,IAAI,WAAW,SAAU,CAAC;IAC7B,OAAO,SAAU,CAAC;QACd,OAAO,SAAU,EAAE;YACf,OAAO,OAAO,MAAM,EAAE,EAAE,CAAC,QAAQ,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,GAAG;QACxD;IACJ;AACJ;AAKO,IAAI,WAAW,SAAU,CAAC;IAC7B,OAAO,SAAU,EAAE;QACf,OAAO,OAAO,MAAM,EAAE,EAAE,CAAC,QAAQ,EAAE,GAAG,CAAC,GAAG,KAAK,EAAE;IACrD;AACJ;AAKO,IAAI,cAAc;IACrB,KAAK;IACL,KAAK;IACL,QAAQ;IACR,SAAS;IACT,aAAa;IACb,UAAU;IACV,UAAU;AACd;AACA,IAAI,UAAU,WAAW,GAAG,IAAA,iKAAa,EAAC,aAAa;AACvD,IAAI,QAAQ,WAAW,GAAG,IAAA,+JAAW,EAAC,aAAa;AAK5C,IAAI,SAAS,SAAU,CAAC;IAC3B,IAAI,WAAW,QAAQ;IACvB,OAAO,SAAU,CAAC;QAAI,OAAO,SAAU,EAAE;YAAI,OAAO,SAAS,IAAI;QAAI;IAAG;AAC5E;AAKO,IAAI,OAAO,SAAU,CAAC;IACzB,IAAI,SAAS,MAAM;IACnB,OAAO,SAAU,CAAC;QAAI,OAAO,SAAU,EAAE;YAAI,OAAO,OAAO,IAAI;QAAI;IAAG;AAC1E;AAKO,IAAI,aAAa;IACpB,KAAK;IACL,KAAK;IACL,QAAQ;IACR,SAAS;IACT,aAAa;IACb,UAAU;IACV,UAAU;IACV,SAAS;IACT,UAAU;IACV,QAAQ;IACR,WAAW;IACX,WAAW;IACX,cAAc;IACd,QAAQ;IACR,MAAM;AACV;AAIO,IAAI,aAAa;IAAc,OAAO;AAAM;AAK5C,IAAI,aAAa;IACpB,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,YAAY;AAChB;AASO,IAAI,aAAa;AAKjB,IAAI,aAAa;IACpB,KAAK;IACL,YAAY;AAChB;AAgBO,IAAI,SAAS,wJAAQ;AAarB,IAAI,SAAS,SAAU,EAAE;IAAI,OAAO,GAAG,IAAI,KAAK;AAAQ;AASxD,IAAI,SAAS,SAAU,MAAM,EAAE,MAAM;IACxC,OAAO,SAAU,EAAE;QACf,OAAO,OAAO,MAAM,WAAW,OAAO,GAAG,KAAK;IAClD;AACJ;AAOO,IAAI,QAAQ;AA4BZ,IAAI,QAAQ;AAOZ,IAAI,OAAO;AASX,IAAI,aAAa,SAAU,MAAM;IACpC,OAAO,SAAU,EAAE;QACf,OAAO,OAAO,MAAM,WAAW,GAAG,KAAK;IAC3C;AACJ;AA0BO,IAAI,YAAY;AAKhB,IAAI,OAAO,WAAW,GAAG,IAAA,qJAAK,EAAC;AAM/B,IAAI,UAAU,WAAW,GAAG,IAAA,sJAAQ,EAAC;AAMrC,IAAI,WAAW,WAAW,GAAG,IAAA,uJAAS,EAAC;AAKvC,IAAI,UAAU;AAQd,IAAI,MAAM,WAAW,GAAG,IAAA,sJAAI,EAAC,GAAG,kJAAa,CAAC;AAqB9C,IAAI,YAAY,WAAW,GAAG,IAAA,sJAAI,EAAC,GAAG,IAAA,6JAAU,EAAC,YAAY;AAI7D,IAAI,YAAY,WAAW,GAAG,OAAO,0JAAQ;AAK7C,IAAI,cAAc,WAAW,GAAG,IAAA,+JAAY,EAAC;AAK7C,IAAI,eACX,WAAW,GAAG,IAAA,gKAAa,EAAC,YAAY;AAOjC,IAAI,oBAAoB;AAexB,IAAI,eAAe,SAAU,CAAC;IAAI,OAAQ,KAAK,OAAO,OAAO,KAAK;AAAK;AAqBvE,IAAI,WAAW,SAAU,CAAC;IAC7B,IAAI;QACA,OAAO,KAAK;IAChB,EACA,OAAO,GAAG;QACN,OAAO;IACX;AACJ;AAOO,IAAI,YAAY,SAAU,CAAC;IAC9B,OAAO;QACH,IAAI,IAAI,EAAE;QACV,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;YAC1C,CAAC,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;QACzB;QACA,OAAO,SAAS;YAAc,OAAO,EAAE,KAAK,CAAC,KAAK,GAAG;QAAI;IAC7D;AACJ;AAoBO,IAAI,gBAAgB,SAAU,CAAC;IAAI,OAAO,IAAA,sJAAI,EAAC,GAAG;AAAe;AA6CjE,IAAI,iBAAiB,SAAU,CAAC;IACnC,OAAO,SAAU,EAAE;QACf,OAAO,OAAO,MAAM,OAAO,aAAa,EAAE,GAAG,KAAK;IACtD;AACJ;AA0BO,IAAI,aAAa,WAAW,GAAG,MAAM,2JAAS,EAAE,0JAAQ;AA0BxD,IAAI,cAAc,WAAW,GAAG,MAAM,gKAAc,EAAE,0JAAQ;AAC9D,SAAS,KAAK,CAAC;IAClB,OAAO,SAAU,CAAC,EAAE,EAAE;QAClB,IAAI,OAAO,WAAW;YAClB,IAAI,UAAU,KAAK;YACnB,OAAO,SAAU,EAAE;gBAAI,OAAO,QAAQ,GAAG;YAAK;QAClD;QACA,OAAO,OAAO,MAAM,QAAQ,EAAE,MAAM,CAAC,GAAG,GAAG,KAAK;IACpD;AACJ;AAgCO,IAAI,SAAS,SAAU,SAAS;IACnC,OAAO,SAAU,EAAE;QACf,OAAO,OAAO,MAAM,QAAQ,UAAU,GAAG,KAAK;IAClD;AACJ;AAQO,IAAI,KAAK,WAAW,GAAG,GAAG,6JAAa;AAKvC,IAAI,SAAS,WAAW,GAAG,IAAA,uJAAO,EAAC;AAC1C,IAAI,OAAO,WAAW,GAAG,IAAA,oJAAK,EAAC;;AAWxB,IAAI,OAAO,WAAW,GAAG,mJAAc,CAAC;AAKxC,IAAI,MAAM,WAAW,GAAG,IAAA,kJAAI,EAAC;AAI7B,IAAI,MAAM,WAAW,GAAG,GAAG,oKAAoB;AAU/C,IAAI,yCAAyC,SAAU,CAAC;IAC3D,OAAO,SAAU,EAAE;QACf,IAAI,IAAI,EAAE,GAAG,sJAAM,CAAC;QACpB,IAAI,OAAO,IAAI;YACX,OAAO;QACX;QACA,IAAI,MAAM;YAAC,EAAE,KAAK;SAAC;QACnB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM,EAAE,IAAK;YAChC,IAAI,MAAM,EAAE,GAAG,EAAE,CAAC,EAAE;YACpB,IAAI,OAAO,MAAM;gBACb,OAAO;YACX;YACA,IAAI,IAAI,CAAC,IAAI,KAAK;QACtB;QACA,OAAO,KAAK;IAChB;AACJ;AAOO,IAAI,iCAAiC,SAAU,CAAC;IACnD,IAAI,IAAI,uCAAuC;IAC/C,OAAO,SAAU,EAAE;QAAI,OAAQ,4JAAY,CAAC,MAAM,EAAE,MAAM;IAAM;AACpE;AAOO,IAAI,yBAAyB;AAO7B,IAAI,gBAAgB,SAAU,CAAC;IAClC,OAAO,+BAA+B,SAAU,CAAC,EAAE,CAAC;QAAI,OAAO,EAAE;IAAI;AACzE;AAOO,IAAI,gBACX,WAAW,GAAG,cAAc,0JAAQ;AAU7B,IAAI,QAAQ;AAOZ,IAAI,aAAa;AAWjB,SAAS,cAAc,SAAS;IACnC,OAAO,SAAU,CAAC;QAAI,OAAO,OAAO,UAAU;IAAK;AACvD;AAQO,IAAI,cAAc;AAUlB,IAAI,SAAS;IAChB,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,QAAQ;IACR,SAAS;IACT,aAAa;IACb,UAAU;IACV,UAAU;IACV,MAAM;IACN,KAAK;IACL,QAAQ;IACR,SAAS;IACT,UAAU;IACV,QAAQ;IACR,WAAW;IACX,WAAW;IACX,cAAc;IACd,QAAQ;IACR,MAAM;IACN,YAAY;AAChB;AAQO,IAAI,oBAAoB,WAAW,GAAG,IAAA,gKAAkB,EAAC;AAQzD,IAAI,iBAAiB,WAAW,GAAG,IAAA,yKAAoB,EAAC;AAmCxD,IAAI,iBAAiB;IAAc,OAAO,UAAU,IAAA,wJAAK;AAAK;AAmC9D,IAAI,gBAAgB;IAAc,OAAO,UAAU,IAAA,uJAAI;AAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2633, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/fp-ts/es6/Compactable.js"], "sourcesContent": ["import { pipe } from './function';\nimport { getFunctorComposition, map } from './Functor';\nimport { getLeft, getRight } from './Option';\nimport * as S from './Separated';\nexport function compact(F, G) {\n    return function (fga) { return F.map(fga, G.compact); };\n}\nexport function separate(F, C, G) {\n    var _compact = compact(F, C);\n    var _map = map(F, G);\n    return function (fge) { return S.separated(_compact(pipe(fge, _map(getLeft))), _compact(pipe(fge, _map(getRight)))); };\n}\n/** @deprecated */\nexport function getCompactableComposition(F, G) {\n    var map = getFunctorComposition(F, G).map;\n    return {\n        map: map,\n        compact: compact(F, G),\n        separate: separate(F, G, G)\n    };\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;;;;;AACO,SAAS,QAAQ,CAAC,EAAE,CAAC;IACxB,OAAO,SAAU,GAAG;QAAI,OAAO,EAAE,GAAG,CAAC,KAAK,EAAE,OAAO;IAAG;AAC1D;AACO,SAAS,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC;IAC5B,IAAI,WAAW,QAAQ,GAAG;IAC1B,IAAI,OAAO,IAAA,oJAAG,EAAC,GAAG;IAClB,OAAO,SAAU,GAAG;QAAI,OAAO,4JAAW,CAAC,SAAS,IAAA,sJAAI,EAAC,KAAK,KAAK,uJAAO,KAAK,SAAS,IAAA,sJAAI,EAAC,KAAK,KAAK,wJAAQ;IAAM;AACzH;AAEO,SAAS,0BAA0B,CAAC,EAAE,CAAC;IAC1C,IAAI,MAAM,IAAA,sKAAqB,EAAC,GAAG,GAAG,GAAG;IACzC,OAAO;QACH,KAAK;QACL,SAAS,QAAQ,GAAG;QACpB,UAAU,SAAS,GAAG,GAAG;IAC7B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2673, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/fp-ts/es6/ChainRec.js"], "sourcesContent": ["/**\n * @since 2.0.0\n */\nexport var tailRec = function (startWith, f) {\n    var ab = f(startWith);\n    while (ab._tag === 'Left') {\n        ab = f(ab.left);\n    }\n    return ab.right;\n};\n"], "names": [], "mappings": "AAAA;;CAEC;;;;AACM,IAAI,UAAU,SAAU,SAAS,EAAE,CAAC;IACvC,IAAI,KAAK,EAAE;IACX,MAAO,GAAG,IAAI,KAAK,OAAQ;QACvB,KAAK,EAAE,GAAG,IAAI;IAClB;IACA,OAAO,GAAG,KAAK;AACnB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2690, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/fp-ts/es6/Either.js"], "sourcesContent": ["import { getApplicativeMonoid } from './Applicative';\nimport { apFirst as apFirst_, apS as apS_, apSecond as apSecond_, getApplySemigroup as getApplySemigroup_ } from './Apply';\nimport * as chainable from './Chain';\nimport { tailRec } from './ChainRec';\nimport { chainOptionK as chainOptionK_, filterOrElse as filterOrElse_, fromOption as fromOption_, fromOptionK as fromOptionK_, fromPredicate as fromPredicate_ } from './FromEither';\nimport { dual, flow, identity, pipe } from './function';\nimport { as as as_, asUnit as asUnit_, bindTo as bindTo_, flap as flap_, let as let__ } from './Functor';\nimport * as _ from './internal';\nimport { separated } from './Separated';\nimport { wiltDefault, witherDefault } from './Witherable';\n// -------------------------------------------------------------------------------------\n// constructors\n// -------------------------------------------------------------------------------------\n/**\n * Constructs a new `Either` holding a `Left` value. This usually represents a failure, due to the right-bias of this\n * structure.\n *\n * @category constructors\n * @since 2.0.0\n */\nexport var left = _.left;\n/**\n * Constructs a new `Either` holding a `Right` value. This usually represents a successful value due to the right bias\n * of this structure.\n *\n * @category constructors\n * @since 2.0.0\n */\nexport var right = _.right;\n/**\n * @category sequencing\n * @since 2.14.0\n */\nexport var flatMap = /*#__PURE__*/ dual(2, function (ma, f) { return (isLeft(ma) ? ma : f(ma.right)); });\nvar _map = function (fa, f) { return pipe(fa, map(f)); };\nvar _ap = function (fab, fa) { return pipe(fab, ap(fa)); };\n/* istanbul ignore next */\nvar _reduce = function (fa, b, f) { return pipe(fa, reduce(b, f)); };\n/* istanbul ignore next */\nvar _foldMap = function (M) { return function (fa, f) {\n    var foldMapM = foldMap(M);\n    return pipe(fa, foldMapM(f));\n}; };\n/* istanbul ignore next */\nvar _reduceRight = function (fa, b, f) { return pipe(fa, reduceRight(b, f)); };\nvar _traverse = function (F) {\n    var traverseF = traverse(F);\n    return function (ta, f) { return pipe(ta, traverseF(f)); };\n};\nvar _bimap = function (fa, f, g) { return pipe(fa, bimap(f, g)); };\nvar _mapLeft = function (fa, f) { return pipe(fa, mapLeft(f)); };\n/* istanbul ignore next */\nvar _alt = function (fa, that) { return pipe(fa, alt(that)); };\n/* istanbul ignore next */\nvar _extend = function (wa, f) { return pipe(wa, extend(f)); };\nvar _chainRec = function (a, f) {\n    return tailRec(f(a), function (e) {\n        return isLeft(e) ? right(left(e.left)) : isLeft(e.right) ? left(f(e.right.left)) : right(right(e.right.right));\n    });\n};\n/**\n * @category type lambdas\n * @since 2.0.0\n */\nexport var URI = 'Either';\n/**\n * @category instances\n * @since 2.0.0\n */\nexport var getShow = function (SE, SA) { return ({\n    show: function (ma) { return (isLeft(ma) ? \"left(\".concat(SE.show(ma.left), \")\") : \"right(\".concat(SA.show(ma.right), \")\")); }\n}); };\n/**\n * @category instances\n * @since 2.0.0\n */\nexport var getEq = function (EL, EA) { return ({\n    equals: function (x, y) {\n        return x === y || (isLeft(x) ? isLeft(y) && EL.equals(x.left, y.left) : isRight(y) && EA.equals(x.right, y.right));\n    }\n}); };\n/**\n * Semigroup returning the left-most non-`Left` value. If both operands are `Right`s then the inner values are\n * concatenated using the provided `Semigroup`\n *\n * @example\n * import { getSemigroup, left, right } from 'fp-ts/Either'\n * import { SemigroupSum } from 'fp-ts/number'\n *\n * const S = getSemigroup<string, number>(SemigroupSum)\n * assert.deepStrictEqual(S.concat(left('a'), left('b')), left('a'))\n * assert.deepStrictEqual(S.concat(left('a'), right(2)), right(2))\n * assert.deepStrictEqual(S.concat(right(1), left('b')), right(1))\n * assert.deepStrictEqual(S.concat(right(1), right(2)), right(3))\n *\n * @category instances\n * @since 2.0.0\n */\nexport var getSemigroup = function (S) { return ({\n    concat: function (x, y) { return (isLeft(y) ? x : isLeft(x) ? y : right(S.concat(x.right, y.right))); }\n}); };\n/**\n * Builds a `Compactable` instance for `Either` given `Monoid` for the left side.\n *\n * @category filtering\n * @since 2.10.0\n */\nexport var getCompactable = function (M) {\n    var empty = left(M.empty);\n    return {\n        URI: URI,\n        _E: undefined,\n        compact: function (ma) { return (isLeft(ma) ? ma : ma.right._tag === 'None' ? empty : right(ma.right.value)); },\n        separate: function (ma) {\n            return isLeft(ma)\n                ? separated(ma, ma)\n                : isLeft(ma.right)\n                    ? separated(right(ma.right.left), empty)\n                    : separated(empty, right(ma.right.right));\n        }\n    };\n};\n/**\n * Builds a `Filterable` instance for `Either` given `Monoid` for the left side\n *\n * @category filtering\n * @since 2.10.0\n */\nexport var getFilterable = function (M) {\n    var empty = left(M.empty);\n    var _a = getCompactable(M), compact = _a.compact, separate = _a.separate;\n    var filter = function (ma, predicate) {\n        return isLeft(ma) ? ma : predicate(ma.right) ? ma : empty;\n    };\n    var partition = function (ma, p) {\n        return isLeft(ma)\n            ? separated(ma, ma)\n            : p(ma.right)\n                ? separated(empty, right(ma.right))\n                : separated(right(ma.right), empty);\n    };\n    return {\n        URI: URI,\n        _E: undefined,\n        map: _map,\n        compact: compact,\n        separate: separate,\n        filter: filter,\n        filterMap: function (ma, f) {\n            if (isLeft(ma)) {\n                return ma;\n            }\n            var ob = f(ma.right);\n            return ob._tag === 'None' ? empty : right(ob.value);\n        },\n        partition: partition,\n        partitionMap: function (ma, f) {\n            if (isLeft(ma)) {\n                return separated(ma, ma);\n            }\n            var e = f(ma.right);\n            return isLeft(e) ? separated(right(e.left), empty) : separated(empty, right(e.right));\n        }\n    };\n};\n/**\n * Builds `Witherable` instance for `Either` given `Monoid` for the left side\n *\n * @category filtering\n * @since 2.0.0\n */\nexport var getWitherable = function (M) {\n    var F_ = getFilterable(M);\n    var C = getCompactable(M);\n    return {\n        URI: URI,\n        _E: undefined,\n        map: _map,\n        compact: F_.compact,\n        separate: F_.separate,\n        filter: F_.filter,\n        filterMap: F_.filterMap,\n        partition: F_.partition,\n        partitionMap: F_.partitionMap,\n        traverse: _traverse,\n        sequence: sequence,\n        reduce: _reduce,\n        foldMap: _foldMap,\n        reduceRight: _reduceRight,\n        wither: witherDefault(Traversable, C),\n        wilt: wiltDefault(Traversable, C)\n    };\n};\n/**\n * The default [`Applicative`](#applicative) instance returns the first error, if you want to\n * get all errors you need to provide a way to concatenate them via a `Semigroup`.\n *\n * @example\n * import * as A from 'fp-ts/Apply'\n * import * as E from 'fp-ts/Either'\n * import { pipe } from 'fp-ts/function'\n * import * as S from 'fp-ts/Semigroup'\n * import * as string from 'fp-ts/string'\n *\n * const parseString = (u: unknown): E.Either<string, string> =>\n *   typeof u === 'string' ? E.right(u) : E.left('not a string')\n *\n * const parseNumber = (u: unknown): E.Either<string, number> =>\n *   typeof u === 'number' ? E.right(u) : E.left('not a number')\n *\n * interface Person {\n *   readonly name: string\n *   readonly age: number\n * }\n *\n * const parsePerson = (\n *   input: Record<string, unknown>\n * ): E.Either<string, Person> =>\n *   pipe(\n *     E.Do,\n *     E.apS('name', parseString(input.name)),\n *     E.apS('age', parseNumber(input.age))\n *   )\n *\n * assert.deepStrictEqual(parsePerson({}), E.left('not a string')) // <= first error\n *\n * const Applicative = E.getApplicativeValidation(\n *   pipe(string.Semigroup, S.intercalate(', '))\n * )\n *\n * const apS = A.apS(Applicative)\n *\n * const parsePersonAll = (\n *   input: Record<string, unknown>\n * ): E.Either<string, Person> =>\n *   pipe(\n *     E.Do,\n *     apS('name', parseString(input.name)),\n *     apS('age', parseNumber(input.age))\n *   )\n *\n * assert.deepStrictEqual(parsePersonAll({}), E.left('not a string, not a number')) // <= all errors\n *\n * @category error handling\n * @since 2.7.0\n */\nexport var getApplicativeValidation = function (SE) { return ({\n    URI: URI,\n    _E: undefined,\n    map: _map,\n    ap: function (fab, fa) {\n        return isLeft(fab)\n            ? isLeft(fa)\n                ? left(SE.concat(fab.left, fa.left))\n                : fab\n            : isLeft(fa)\n                ? fa\n                : right(fab.right(fa.right));\n    },\n    of: of\n}); };\n/**\n * The default [`Alt`](#alt) instance returns the last error, if you want to\n * get all errors you need to provide a way to concatenate them via a `Semigroup`.\n *\n * @example\n * import * as E from 'fp-ts/Either'\n * import { pipe } from 'fp-ts/function'\n * import * as S from 'fp-ts/Semigroup'\n * import * as string from 'fp-ts/string'\n *\n * const parseString = (u: unknown): E.Either<string, string> =>\n *   typeof u === 'string' ? E.right(u) : E.left('not a string')\n *\n * const parseNumber = (u: unknown): E.Either<string, number> =>\n *   typeof u === 'number' ? E.right(u) : E.left('not a number')\n *\n * const parse = (u: unknown): E.Either<string, string | number> =>\n *   pipe(\n *     parseString(u),\n *     E.alt<string, string | number>(() => parseNumber(u))\n *   )\n *\n * assert.deepStrictEqual(parse(true), E.left('not a number')) // <= last error\n *\n * const Alt = E.getAltValidation(pipe(string.Semigroup, S.intercalate(', ')))\n *\n * const parseAll = (u: unknown): E.Either<string, string | number> =>\n *   Alt.alt<string | number>(parseString(u), () => parseNumber(u))\n *\n * assert.deepStrictEqual(parseAll(true), E.left('not a string, not a number')) // <= all errors\n *\n * @category error handling\n * @since 2.7.0\n */\nexport var getAltValidation = function (SE) { return ({\n    URI: URI,\n    _E: undefined,\n    map: _map,\n    alt: function (me, that) {\n        if (isRight(me)) {\n            return me;\n        }\n        var ea = that();\n        return isLeft(ea) ? left(SE.concat(me.left, ea.left)) : ea;\n    }\n}); };\n/**\n * @category mapping\n * @since 2.0.0\n */\nexport var map = function (f) { return function (fa) {\n    return isLeft(fa) ? fa : right(f(fa.right));\n}; };\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var Functor = {\n    URI: URI,\n    map: _map\n};\n/**\n * Maps the `Right` value of this `Either` to the specified constant value.\n *\n * @category mapping\n * @since 2.16.0\n */\nexport var as = dual(2, as_(Functor));\n/**\n * Maps the `Right` value of this `Either` to the void constant value.\n *\n * @category mapping\n * @since 2.16.0\n */\nexport var asUnit = asUnit_(Functor);\n/**\n * @category constructors\n * @since 2.7.0\n */\nexport var of = right;\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var Pointed = {\n    URI: URI,\n    of: of\n};\n/**\n * Less strict version of [`ap`](#ap).\n *\n * The `W` suffix (short for **W**idening) means that the error types will be merged.\n *\n * @since 2.8.0\n */\nexport var apW = function (fa) { return function (fab) {\n    return isLeft(fab) ? fab : isLeft(fa) ? fa : right(fab.right(fa.right));\n}; };\n/**\n * @since 2.0.0\n */\nexport var ap = apW;\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var Apply = {\n    URI: URI,\n    map: _map,\n    ap: _ap\n};\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var Applicative = {\n    URI: URI,\n    map: _map,\n    ap: _ap,\n    of: of\n};\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var Chain = {\n    URI: URI,\n    map: _map,\n    ap: _ap,\n    chain: flatMap\n};\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var Monad = {\n    URI: URI,\n    map: _map,\n    ap: _ap,\n    of: of,\n    chain: flatMap\n};\n/**\n * Left-associative fold of a structure.\n *\n * @example\n * import { pipe } from 'fp-ts/function'\n * import * as E from 'fp-ts/Either'\n *\n * const startWith = 'prefix'\n * const concat = (a: string, b: string) => `${a}:${b}`\n *\n * assert.deepStrictEqual(\n *   pipe(E.right('a'), E.reduce(startWith, concat)),\n *   'prefix:a'\n * )\n *\n * assert.deepStrictEqual(\n *   pipe(E.left('e'), E.reduce(startWith, concat)),\n *   'prefix'\n * )\n *\n * @category folding\n * @since 2.0.0\n */\nexport var reduce = function (b, f) { return function (fa) {\n    return isLeft(fa) ? b : f(b, fa.right);\n}; };\n/**\n * Map each element of the structure to a monoid, and combine the results.\n *\n * @example\n * import { pipe } from 'fp-ts/function'\n * import * as E from 'fp-ts/Either'\n * import * as S from 'fp-ts/string'\n *\n * const yell = (a: string) => `${a}!`\n *\n * assert.deepStrictEqual(\n *   pipe(E.right('a'), E.foldMap(S.Monoid)(yell)),\n *   'a!'\n * )\n *\n * assert.deepStrictEqual(\n *   pipe(E.left('e'), E.foldMap(S.Monoid)(yell)),\n *   S.Monoid.empty\n * )\n *\n * @category folding\n * @since 2.0.0\n */\nexport var foldMap = function (M) { return function (f) { return function (fa) {\n    return isLeft(fa) ? M.empty : f(fa.right);\n}; }; };\n/**\n * Right-associative fold of a structure.\n *\n * @example\n * import { pipe } from 'fp-ts/function'\n * import * as E from 'fp-ts/Either'\n *\n * const startWith = 'postfix'\n * const concat = (a: string, b: string) => `${a}:${b}`\n *\n * assert.deepStrictEqual(\n *   pipe(E.right('a'), E.reduceRight(startWith, concat)),\n *   'a:postfix'\n * )\n *\n * assert.deepStrictEqual(\n *   pipe(E.left('e'), E.reduceRight(startWith, concat)),\n *   'postfix'\n * )\n *\n * @category folding\n * @since 2.0.0\n */\nexport var reduceRight = function (b, f) { return function (fa) {\n    return isLeft(fa) ? b : f(fa.right, b);\n}; };\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var Foldable = {\n    URI: URI,\n    reduce: _reduce,\n    foldMap: _foldMap,\n    reduceRight: _reduceRight\n};\n/**\n * Map each element of a structure to an action, evaluate these actions from left to right, and collect the results.\n *\n * @example\n * import { pipe } from 'fp-ts/function'\n * import * as RA from 'fp-ts/ReadonlyArray'\n * import * as E from 'fp-ts/Either'\n * import * as O from 'fp-ts/Option'\n *\n * assert.deepStrictEqual(\n *   pipe(E.right(['a']), E.traverse(O.Applicative)(RA.head)),\n *   O.some(E.right('a'))\n *  )\n *\n * assert.deepStrictEqual(\n *   pipe(E.right([]), E.traverse(O.Applicative)(RA.head)),\n *   O.none\n * )\n *\n * @category traversing\n * @since 2.6.3\n */\nexport var traverse = function (F) {\n    return function (f) {\n        return function (ta) {\n            return isLeft(ta) ? F.of(left(ta.left)) : F.map(f(ta.right), right);\n        };\n    };\n};\n/**\n * Evaluate each monadic action in the structure from left to right, and collect the results.\n *\n * @example\n * import { pipe } from 'fp-ts/function'\n * import * as E from 'fp-ts/Either'\n * import * as O from 'fp-ts/Option'\n *\n * assert.deepStrictEqual(\n *   pipe(E.right(O.some('a')), E.sequence(O.Applicative)),\n *   O.some(E.right('a'))\n *  )\n *\n * assert.deepStrictEqual(\n *   pipe(E.right(O.none), E.sequence(O.Applicative)),\n *   O.none\n * )\n *\n * @category traversing\n * @since 2.6.3\n */\nexport var sequence = function (F) {\n    return function (ma) {\n        return isLeft(ma) ? F.of(left(ma.left)) : F.map(ma.right, right);\n    };\n};\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var Traversable = {\n    URI: URI,\n    map: _map,\n    reduce: _reduce,\n    foldMap: _foldMap,\n    reduceRight: _reduceRight,\n    traverse: _traverse,\n    sequence: sequence\n};\n/**\n * Map a pair of functions over the two type arguments of the bifunctor.\n *\n * @category mapping\n * @since 2.0.0\n */\nexport var bimap = function (f, g) { return function (fa) {\n    return isLeft(fa) ? left(f(fa.left)) : right(g(fa.right));\n}; };\n/**\n * Map a function over the first type argument of a bifunctor.\n *\n * @category error handling\n * @since 2.0.0\n */\nexport var mapLeft = function (f) { return function (fa) {\n    return isLeft(fa) ? left(f(fa.left)) : fa;\n}; };\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var Bifunctor = {\n    URI: URI,\n    bimap: _bimap,\n    mapLeft: _mapLeft\n};\n/**\n * Less strict version of [`alt`](#alt).\n *\n * The `W` suffix (short for **W**idening) means that the error and the return types will be merged.\n *\n * @category error handling\n * @since 2.9.0\n */\nexport var altW = function (that) { return function (fa) {\n    return isLeft(fa) ? that() : fa;\n}; };\n/**\n * Identifies an associative operation on a type constructor. It is similar to `Semigroup`, except that it applies to\n * types of kind `* -> *`.\n *\n * In case of `Either` returns the left-most non-`Left` value (or the right-most `Left` value if both values are `Left`).\n *\n * | x        | y        | pipe(x, alt(() => y) |\n * | -------- | -------- | -------------------- |\n * | left(a)  | left(b)  | left(b)              |\n * | left(a)  | right(2) | right(2)             |\n * | right(1) | left(b)  | right(1)             |\n * | right(1) | right(2) | right(1)             |\n *\n * @example\n * import * as E from 'fp-ts/Either'\n * import { pipe } from 'fp-ts/function'\n *\n * assert.deepStrictEqual(\n *   pipe(\n *     E.left('a'),\n *     E.alt(() => E.left('b'))\n *   ),\n *   E.left('b')\n * )\n * assert.deepStrictEqual(\n *   pipe(\n *     E.left('a'),\n *     E.alt(() => E.right(2))\n *   ),\n *   E.right(2)\n * )\n * assert.deepStrictEqual(\n *   pipe(\n *     E.right(1),\n *     E.alt(() => E.left('b'))\n *   ),\n *   E.right(1)\n * )\n * assert.deepStrictEqual(\n *   pipe(\n *     E.right(1),\n *     E.alt(() => E.right(2))\n *   ),\n *   E.right(1)\n * )\n *\n * @category error handling\n * @since 2.0.0\n */\nexport var alt = altW;\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var Alt = {\n    URI: URI,\n    map: _map,\n    alt: _alt\n};\n/**\n * @since 2.0.0\n */\nexport var extend = function (f) { return function (wa) {\n    return isLeft(wa) ? wa : right(f(wa));\n}; };\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var Extend = {\n    URI: URI,\n    map: _map,\n    extend: _extend\n};\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var ChainRec = {\n    URI: URI,\n    map: _map,\n    ap: _ap,\n    chain: flatMap,\n    chainRec: _chainRec\n};\n/**\n * @since 2.6.3\n */\nexport var throwError = left;\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var MonadThrow = {\n    URI: URI,\n    map: _map,\n    ap: _ap,\n    of: of,\n    chain: flatMap,\n    throwError: throwError\n};\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var FromEither = {\n    URI: URI,\n    fromEither: identity\n};\n/**\n * @example\n * import { fromPredicate, left, right } from 'fp-ts/Either'\n * import { pipe } from 'fp-ts/function'\n *\n * assert.deepStrictEqual(\n *   pipe(\n *     1,\n *     fromPredicate(\n *       (n) => n > 0,\n *       () => 'error'\n *     )\n *   ),\n *   right(1)\n * )\n * assert.deepStrictEqual(\n *   pipe(\n *     -1,\n *     fromPredicate(\n *       (n) => n > 0,\n *       () => 'error'\n *     )\n *   ),\n *   left('error')\n * )\n *\n * @category lifting\n * @since 2.0.0\n */\nexport var fromPredicate = /*#__PURE__*/ fromPredicate_(FromEither);\n// -------------------------------------------------------------------------------------\n// conversions\n// -------------------------------------------------------------------------------------\n/**\n * @example\n * import * as E from 'fp-ts/Either'\n * import { pipe } from 'fp-ts/function'\n * import * as O from 'fp-ts/Option'\n *\n * assert.deepStrictEqual(\n *   pipe(\n *     O.some(1),\n *     E.fromOption(() => 'error')\n *   ),\n *   E.right(1)\n * )\n * assert.deepStrictEqual(\n *   pipe(\n *     O.none,\n *     E.fromOption(() => 'error')\n *   ),\n *   E.left('error')\n * )\n *\n * @category conversions\n * @since 2.0.0\n */\nexport var fromOption = \n/*#__PURE__*/ fromOption_(FromEither);\n// -------------------------------------------------------------------------------------\n// refinements\n// -------------------------------------------------------------------------------------\n/**\n * Returns `true` if the either is an instance of `Left`, `false` otherwise.\n *\n * @category refinements\n * @since 2.0.0\n */\nexport var isLeft = _.isLeft;\n/**\n * Returns `true` if the either is an instance of `Right`, `false` otherwise.\n *\n * @category refinements\n * @since 2.0.0\n */\nexport var isRight = _.isRight;\n/**\n * Less strict version of [`match`](#match).\n *\n * The `W` suffix (short for **W**idening) means that the handler return types will be merged.\n *\n * @category pattern matching\n * @since 2.10.0\n */\nexport var matchW = function (onLeft, onRight) {\n    return function (ma) {\n        return isLeft(ma) ? onLeft(ma.left) : onRight(ma.right);\n    };\n};\n/**\n * Alias of [`matchW`](#matchw).\n *\n * @category pattern matching\n * @since 2.10.0\n */\nexport var foldW = matchW;\n/**\n * Takes two functions and an `Either` value, if the value is a `Left` the inner value is applied to the first function,\n * if the value is a `Right` the inner value is applied to the second function.\n *\n * @example\n * import { match, left, right } from 'fp-ts/Either'\n * import { pipe } from 'fp-ts/function'\n *\n * function onLeft(errors: Array<string>): string {\n *   return `Errors: ${errors.join(', ')}`\n * }\n *\n * function onRight(value: number): string {\n *   return `Ok: ${value}`\n * }\n *\n * assert.strictEqual(\n *   pipe(\n *     right(1),\n *     match(onLeft, onRight)\n *   ),\n *   'Ok: 1'\n * )\n * assert.strictEqual(\n *   pipe(\n *     left(['error 1', 'error 2']),\n *     match(onLeft, onRight)\n *   ),\n *   'Errors: error 1, error 2'\n * )\n *\n * @category pattern matching\n * @since 2.10.0\n */\nexport var match = matchW;\n/**\n * Alias of [`match`](#match).\n *\n * @category pattern matching\n * @since 2.0.0\n */\nexport var fold = match;\n/**\n * Less strict version of [`getOrElse`](#getorelse).\n *\n * The `W` suffix (short for **W**idening) means that the handler return type will be merged.\n *\n * @category error handling\n * @since 2.6.0\n */\nexport var getOrElseW = function (onLeft) {\n    return function (ma) {\n        return isLeft(ma) ? onLeft(ma.left) : ma.right;\n    };\n};\n/**\n * Returns the wrapped value if it's a `Right` or a default value if is a `Left`.\n *\n * @example\n * import { getOrElse, left, right } from 'fp-ts/Either'\n * import { pipe } from 'fp-ts/function'\n *\n * assert.deepStrictEqual(\n *   pipe(\n *     right(1),\n *     getOrElse(() => 0)\n *   ),\n *   1\n * )\n * assert.deepStrictEqual(\n *   pipe(\n *     left('error'),\n *     getOrElse(() => 0)\n *   ),\n *   0\n * )\n *\n * @category error handling\n * @since 2.0.0\n */\nexport var getOrElse = getOrElseW;\n// -------------------------------------------------------------------------------------\n// combinators\n// -------------------------------------------------------------------------------------\n/**\n * @category mapping\n * @since 2.10.0\n */\nexport var flap = /*#__PURE__*/ flap_(Functor);\n/**\n * Combine two effectful actions, keeping only the result of the first.\n *\n * @since 2.0.0\n */\nexport var apFirst = /*#__PURE__*/ apFirst_(Apply);\n/**\n * Less strict version of [`apFirst`](#apfirst)\n *\n * The `W` suffix (short for **W**idening) means that the error types will be merged.\n *\n * @since 2.12.0\n */\nexport var apFirstW = apFirst;\n/**\n * Combine two effectful actions, keeping only the result of the second.\n *\n * @since 2.0.0\n */\nexport var apSecond = /*#__PURE__*/ apSecond_(Apply);\n/**\n * Less strict version of [`apSecond`](#apsecond)\n *\n * The `W` suffix (short for **W**idening) means that the error types will be merged.\n *\n * @since 2.12.0\n */\nexport var apSecondW = apSecond;\n/**\n * Composes computations in sequence, using the return value of one computation to determine the next computation and\n * keeping only the result of the first.\n *\n * @category combinators\n * @since 2.15.0\n */\nexport var tap = /*#__PURE__*/ dual(2, chainable.tap(Chain));\n/**\n * Less strict version of [`flatten`](#flatten).\n *\n * The `W` suffix (short for **W**idening) means that the error types will be merged.\n *\n * @category sequencing\n * @since 2.11.0\n */\nexport var flattenW = \n/*#__PURE__*/ flatMap(identity);\n/**\n * The `flatten` function is the conventional monad join operator. It is used to remove one level of monadic structure, projecting its bound argument into the outer level.\n *\n * @example\n * import * as E from 'fp-ts/Either'\n *\n * assert.deepStrictEqual(E.flatten(E.right(E.right('a'))), E.right('a'))\n * assert.deepStrictEqual(E.flatten(E.right(E.left('e'))), E.left('e'))\n * assert.deepStrictEqual(E.flatten(E.left('e')), E.left('e'))\n *\n * @category sequencing\n * @since 2.0.0\n */\nexport var flatten = flattenW;\n/**\n * @since 2.0.0\n */\nexport var duplicate = /*#__PURE__*/ extend(identity);\n/**\n * Use `liftOption`.\n *\n * @category legacy\n * @since 2.10.0\n */\nexport var fromOptionK = \n/*#__PURE__*/ fromOptionK_(FromEither);\n/**\n * Use `flatMapOption`.\n *\n * @category legacy\n * @since 2.11.0\n */\nexport var chainOptionK = /*#__PURE__*/ chainOptionK_(FromEither, Chain);\n/**\n * Use `flatMapOption`.\n *\n * @category legacy\n * @since 2.13.2\n */\nexport var chainOptionKW = chainOptionK;\n/** @internal */\nvar _FromEither = {\n    fromEither: FromEither.fromEither\n};\n/**\n * @category lifting\n * @since 2.15.0\n */\nexport var liftNullable = /*#__PURE__*/ _.liftNullable(_FromEither);\n/**\n * @category lifting\n * @since 2.15.0\n */\nexport var liftOption = /*#__PURE__*/ _.liftOption(_FromEither);\n/** @internal */\nvar _FlatMap = {\n    flatMap: flatMap\n};\n/**\n * @category sequencing\n * @since 2.15.0\n */\nexport var flatMapNullable = /*#__PURE__*/ _.flatMapNullable(_FromEither, _FlatMap);\n/**\n * @category sequencing\n * @since 2.15.0\n */\nexport var flatMapOption = /*#__PURE__*/ _.flatMapOption(_FromEither, _FlatMap);\n/**\n * @example\n * import * as E from 'fp-ts/Either'\n * import { pipe } from 'fp-ts/function'\n *\n * assert.deepStrictEqual(\n *   pipe(\n *     E.right(1),\n *     E.filterOrElse(\n *       (n) => n > 0,\n *       () => 'error'\n *     )\n *   ),\n *   E.right(1)\n * )\n * assert.deepStrictEqual(\n *   pipe(\n *     E.right(-1),\n *     E.filterOrElse(\n *       (n) => n > 0,\n *       () => 'error'\n *     )\n *   ),\n *   E.left('error')\n * )\n * assert.deepStrictEqual(\n *   pipe(\n *     E.left('a'),\n *     E.filterOrElse(\n *       (n) => n > 0,\n *       () => 'error'\n *     )\n *   ),\n *   E.left('a')\n * )\n *\n * @category filtering\n * @since 2.0.0\n */\nexport var filterOrElse = /*#__PURE__*/ filterOrElse_(FromEither, Chain);\n/**\n * Less strict version of [`filterOrElse`](#filterorelse).\n *\n * The `W` suffix (short for **W**idening) means that the error types will be merged.\n *\n * @category filtering\n * @since 2.9.0\n */\nexport var filterOrElseW = filterOrElse;\n/**\n * Returns a `Right` if is a `Left` (and vice versa).\n *\n * @since 2.0.0\n */\nexport var swap = function (ma) { return (isLeft(ma) ? right(ma.left) : left(ma.right)); };\n/**\n * Less strict version of [`orElse`](#orelse).\n *\n * The `W` suffix (short for **W**idening) means that the return types will be merged.\n *\n * @category error handling\n * @since 2.10.0\n */\nexport var orElseW = function (onLeft) {\n    return function (ma) {\n        return isLeft(ma) ? onLeft(ma.left) : ma;\n    };\n};\n/**\n * Useful for recovering from errors.\n *\n * @category error handling\n * @since 2.0.0\n */\nexport var orElse = orElseW;\n/**\n * Takes a default and a nullable value, if the value is not nully, turn it into a `Right`, if the value is nully use\n * the provided default as a `Left`.\n *\n * @example\n * import { fromNullable, left, right } from 'fp-ts/Either'\n *\n * const parse = fromNullable('nully')\n *\n * assert.deepStrictEqual(parse(1), right(1))\n * assert.deepStrictEqual(parse(null), left('nully'))\n *\n * @category conversions\n * @since 2.0.0\n */\nexport var fromNullable = function (e) {\n    return function (a) {\n        return a == null ? left(e) : right(a);\n    };\n};\n/**\n * Constructs a new `Either` from a function that might throw.\n *\n * See also [`tryCatchK`](#trycatchk).\n *\n * @example\n * import * as E from 'fp-ts/Either'\n *\n * const unsafeHead = <A>(as: ReadonlyArray<A>): A => {\n *   if (as.length > 0) {\n *     return as[0]\n *   } else {\n *     throw new Error('empty array')\n *   }\n * }\n *\n * const head = <A>(as: ReadonlyArray<A>): E.Either<Error, A> =>\n *   E.tryCatch(() => unsafeHead(as), e => (e instanceof Error ? e : new Error('unknown error')))\n *\n * assert.deepStrictEqual(head([]), E.left(new Error('empty array')))\n * assert.deepStrictEqual(head([1, 2, 3]), E.right(1))\n *\n * @category interop\n * @since 2.0.0\n */\nexport var tryCatch = function (f, onThrow) {\n    try {\n        return right(f());\n    }\n    catch (e) {\n        return left(onThrow(e));\n    }\n};\n/**\n * Converts a function that may throw to one returning a `Either`.\n *\n * @category interop\n * @since 2.10.0\n */\nexport var tryCatchK = function (f, onThrow) {\n    return function () {\n        var a = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            a[_i] = arguments[_i];\n        }\n        return tryCatch(function () { return f.apply(void 0, a); }, onThrow);\n    };\n};\n/**\n * Use `liftNullable`.\n *\n * @category legacy\n * @since 2.9.0\n */\nexport var fromNullableK = function (e) {\n    var from = fromNullable(e);\n    return function (f) { return flow(f, from); };\n};\n/**\n * Use `flatMapNullable`.\n *\n * @category legacy\n * @since 2.9.0\n */\nexport var chainNullableK = function (e) {\n    var from = fromNullableK(e);\n    return function (f) { return flatMap(from(f)); };\n};\n/**\n * @category conversions\n * @since 2.10.0\n */\nexport var toUnion = /*#__PURE__*/ foldW(identity, identity);\n// -------------------------------------------------------------------------------------\n// utils\n// -------------------------------------------------------------------------------------\n/**\n * Default value for the `onError` argument of `tryCatch`\n *\n * @since 2.0.0\n */\nexport function toError(e) {\n    try {\n        return e instanceof Error ? e : new Error(String(e));\n    }\n    catch (error) {\n        return new Error();\n    }\n}\nexport function elem(E) {\n    return function (a, ma) {\n        if (ma === undefined) {\n            var elemE_1 = elem(E);\n            return function (ma) { return elemE_1(a, ma); };\n        }\n        return isLeft(ma) ? false : E.equals(a, ma.right);\n    };\n}\n/**\n * Returns `false` if `Left` or returns the result of the application of the given predicate to the `Right` value.\n *\n * @example\n * import { exists, left, right } from 'fp-ts/Either'\n *\n * const gt2 = exists((n: number) => n > 2)\n *\n * assert.strictEqual(gt2(left('a')), false)\n * assert.strictEqual(gt2(right(1)), false)\n * assert.strictEqual(gt2(right(3)), true)\n *\n * @since 2.0.0\n */\nexport var exists = function (predicate) {\n    return function (ma) {\n        return isLeft(ma) ? false : predicate(ma.right);\n    };\n};\n// -------------------------------------------------------------------------------------\n// do notation\n// -------------------------------------------------------------------------------------\n/**\n * @category do notation\n * @since 2.9.0\n */\nexport var Do = /*#__PURE__*/ of(_.emptyRecord);\n/**\n * @category do notation\n * @since 2.8.0\n */\nexport var bindTo = /*#__PURE__*/ bindTo_(Functor);\nvar let_ = /*#__PURE__*/ let__(Functor);\nexport { \n/**\n * @category do notation\n * @since 2.13.0\n */\nlet_ as let };\n/**\n * @category do notation\n * @since 2.8.0\n */\nexport var bind = /*#__PURE__*/ chainable.bind(Chain);\n/**\n * The `W` suffix (short for **W**idening) means that the error types will be merged.\n *\n * @category do notation\n * @since 2.8.0\n */\nexport var bindW = bind;\n/**\n * @category do notation\n * @since 2.8.0\n */\nexport var apS = /*#__PURE__*/ apS_(Apply);\n/**\n * Less strict version of [`apS`](#aps).\n *\n * The `W` suffix (short for **W**idening) means that the error types will be merged.\n *\n * @category do notation\n * @since 2.8.0\n */\nexport var apSW = apS;\n/**\n * @since 2.11.0\n */\nexport var ApT = /*#__PURE__*/ of(_.emptyReadonlyArray);\n// -------------------------------------------------------------------------------------\n// array utils\n// -------------------------------------------------------------------------------------\n/**\n * Equivalent to `ReadonlyNonEmptyArray#traverseWithIndex(Applicative)`.\n *\n * @category traversing\n * @since 2.11.0\n */\nexport var traverseReadonlyNonEmptyArrayWithIndex = function (f) {\n    return function (as) {\n        var e = f(0, _.head(as));\n        if (isLeft(e)) {\n            return e;\n        }\n        var out = [e.right];\n        for (var i = 1; i < as.length; i++) {\n            var e_1 = f(i, as[i]);\n            if (isLeft(e_1)) {\n                return e_1;\n            }\n            out.push(e_1.right);\n        }\n        return right(out);\n    };\n};\n/**\n * Equivalent to `ReadonlyArray#traverseWithIndex(Applicative)`.\n *\n * @category traversing\n * @since 2.11.0\n */\nexport var traverseReadonlyArrayWithIndex = function (f) {\n    var g = traverseReadonlyNonEmptyArrayWithIndex(f);\n    return function (as) { return (_.isNonEmpty(as) ? g(as) : ApT); };\n};\n/**\n * Equivalent to `ReadonlyArray#traverseWithIndex(Applicative)`.\n *\n * @category traversing\n * @since 2.9.0\n */\nexport var traverseArrayWithIndex = traverseReadonlyArrayWithIndex;\n/**\n * Equivalent to `ReadonlyArray#traverse(Applicative)`.\n *\n * @category traversing\n * @since 2.9.0\n */\nexport var traverseArray = function (f) { return traverseReadonlyArrayWithIndex(function (_, a) { return f(a); }); };\n/**\n * Equivalent to `ReadonlyArray#sequence(Applicative)`.\n *\n * @category traversing\n * @since 2.9.0\n */\nexport var sequenceArray = \n/*#__PURE__*/ traverseArray(identity);\n// -------------------------------------------------------------------------------------\n// legacy\n// -------------------------------------------------------------------------------------\n/**\n * Alias of `flatMap`.\n *\n * @category legacy\n * @since 2.6.0\n */\nexport var chainW = flatMap;\n/**\n * Alias of `flatMap`.\n *\n * @category legacy\n * @since 2.0.0\n */\nexport var chain = flatMap;\n/**\n * Alias of `tap`.\n *\n * @category legacy\n * @since 2.0.0\n */\nexport var chainFirst = tap;\n/**\n * Alias of `tap`.\n *\n * @category legacy\n * @since 2.8.0\n */\nexport var chainFirstW = tap;\n/**\n * Use [`parse`](./Json.ts.html#parse) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport function parseJSON(s, onError) {\n    return tryCatch(function () { return JSON.parse(s); }, onError);\n}\n/**\n * Use [`stringify`](./Json.ts.html#stringify) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var stringifyJSON = function (u, onError) {\n    return tryCatch(function () {\n        var s = JSON.stringify(u);\n        if (typeof s !== 'string') {\n            throw new Error('Converting unsupported structure to JSON');\n        }\n        return s;\n    }, onError);\n};\n/**\n * This instance is deprecated, use small, specific instances instead.\n * For example if a function needs a `Functor` instance, pass `E.Functor` instead of `E.either`\n * (where `E` is from `import E from 'fp-ts/Either'`)\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var either = {\n    URI: URI,\n    map: _map,\n    of: of,\n    ap: _ap,\n    chain: flatMap,\n    reduce: _reduce,\n    foldMap: _foldMap,\n    reduceRight: _reduceRight,\n    traverse: _traverse,\n    sequence: sequence,\n    bimap: _bimap,\n    mapLeft: _mapLeft,\n    alt: _alt,\n    extend: _extend,\n    chainRec: _chainRec,\n    throwError: throwError\n};\n/**\n * Use [`getApplySemigroup`](./Apply.ts.html#getapplysemigroup) instead.\n *\n * Semigroup returning the left-most `Left` value. If both operands are `Right`s then the inner values\n * are concatenated using the provided `Semigroup`\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var getApplySemigroup = \n/*#__PURE__*/ getApplySemigroup_(Apply);\n/**\n * Use [`getApplicativeMonoid`](./Applicative.ts.html#getapplicativemonoid) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var getApplyMonoid = \n/*#__PURE__*/ getApplicativeMonoid(Applicative);\n/**\n * Use [`getApplySemigroup`](./Apply.ts.html#getapplysemigroup) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var getValidationSemigroup = function (SE, SA) {\n    return getApplySemigroup_(getApplicativeValidation(SE))(SA);\n};\n/**\n * Use [`getApplicativeMonoid`](./Applicative.ts.html#getapplicativemonoid) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var getValidationMonoid = function (SE, MA) {\n    return getApplicativeMonoid(getApplicativeValidation(SE))(MA);\n};\n/**\n * Use [`getApplicativeValidation`](#getapplicativevalidation) and [`getAltValidation`](#getaltvalidation) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport function getValidation(SE) {\n    var ap = getApplicativeValidation(SE).ap;\n    var alt = getAltValidation(SE).alt;\n    return {\n        URI: URI,\n        _E: undefined,\n        map: _map,\n        of: of,\n        chain: flatMap,\n        bimap: _bimap,\n        mapLeft: _mapLeft,\n        reduce: _reduce,\n        foldMap: _foldMap,\n        reduceRight: _reduceRight,\n        extend: _extend,\n        traverse: _traverse,\n        sequence: sequence,\n        chainRec: _chainRec,\n        throwError: throwError,\n        ap: ap,\n        alt: alt\n    };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAWO,IAAI,OAAO,sJAAM;AAQjB,IAAI,QAAQ,uJAAO;AAKnB,IAAI,UAAU,WAAW,GAAG,IAAA,sJAAI,EAAC,GAAG,SAAU,EAAE,EAAE,CAAC;IAAI,OAAQ,OAAO,MAAM,KAAK,EAAE,GAAG,KAAK;AAAI;AACtG,IAAI,OAAO,SAAU,EAAE,EAAE,CAAC;IAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,IAAI;AAAK;AACvD,IAAI,MAAM,SAAU,GAAG,EAAE,EAAE;IAAI,OAAO,IAAA,sJAAI,EAAC,KAAK,GAAG;AAAM;AACzD,wBAAwB,GACxB,IAAI,UAAU,SAAU,EAAE,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,OAAO,GAAG;AAAK;AACnE,wBAAwB,GACxB,IAAI,WAAW,SAAU,CAAC;IAAI,OAAO,SAAU,EAAE,EAAE,CAAC;QAChD,IAAI,WAAW,QAAQ;QACvB,OAAO,IAAA,sJAAI,EAAC,IAAI,SAAS;IAC7B;AAAG;AACH,wBAAwB,GACxB,IAAI,eAAe,SAAU,EAAE,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,YAAY,GAAG;AAAK;AAC7E,IAAI,YAAY,SAAU,CAAC;IACvB,IAAI,YAAY,SAAS;IACzB,OAAO,SAAU,EAAE,EAAE,CAAC;QAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,UAAU;IAAK;AAC7D;AACA,IAAI,SAAS,SAAU,EAAE,EAAE,CAAC,EAAE,CAAC;IAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,MAAM,GAAG;AAAK;AACjE,IAAI,WAAW,SAAU,EAAE,EAAE,CAAC;IAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,QAAQ;AAAK;AAC/D,wBAAwB,GACxB,IAAI,OAAO,SAAU,EAAE,EAAE,IAAI;IAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,IAAI;AAAQ;AAC7D,wBAAwB,GACxB,IAAI,UAAU,SAAU,EAAE,EAAE,CAAC;IAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,OAAO;AAAK;AAC7D,IAAI,YAAY,SAAU,CAAC,EAAE,CAAC;IAC1B,OAAO,IAAA,yJAAO,EAAC,EAAE,IAAI,SAAU,CAAC;QAC5B,OAAO,OAAO,KAAK,MAAM,KAAK,EAAE,IAAI,KAAK,OAAO,EAAE,KAAK,IAAI,KAAK,EAAE,EAAE,KAAK,CAAC,IAAI,KAAK,MAAM,MAAM,EAAE,KAAK,CAAC,KAAK;IAChH;AACJ;AAKO,IAAI,MAAM;AAKV,IAAI,UAAU,SAAU,EAAE,EAAE,EAAE;IAAI,OAAQ;QAC7C,MAAM,SAAU,EAAE;YAAI,OAAQ,OAAO,MAAM,QAAQ,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,GAAG,OAAO,SAAS,MAAM,CAAC,GAAG,IAAI,CAAC,GAAG,KAAK,GAAG;QAAO;IACjI;AAAI;AAKG,IAAI,QAAQ,SAAU,EAAE,EAAE,EAAE;IAAI,OAAQ;QAC3C,QAAQ,SAAU,CAAC,EAAE,CAAC;YAClB,OAAO,MAAM,KAAK,CAAC,OAAO,KAAK,OAAO,MAAM,GAAG,MAAM,CAAC,EAAE,IAAI,EAAE,EAAE,IAAI,IAAI,QAAQ,MAAM,GAAG,MAAM,CAAC,EAAE,KAAK,EAAE,EAAE,KAAK,CAAC;QACrH;IACJ;AAAI;AAkBG,IAAI,eAAe,SAAU,CAAC;IAAI,OAAQ;QAC7C,QAAQ,SAAU,CAAC,EAAE,CAAC;YAAI,OAAQ,OAAO,KAAK,IAAI,OAAO,KAAK,IAAI,MAAM,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,EAAE,KAAK;QAAK;IAC1G;AAAI;AAOG,IAAI,iBAAiB,SAAU,CAAC;IACnC,IAAI,QAAQ,KAAK,EAAE,KAAK;IACxB,OAAO;QACH,KAAK;QACL,IAAI;QACJ,SAAS,SAAU,EAAE;YAAI,OAAQ,OAAO,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,KAAK,SAAS,QAAQ,MAAM,GAAG,KAAK,CAAC,KAAK;QAAI;QAC9G,UAAU,SAAU,EAAE;YAClB,OAAO,OAAO,MACR,IAAA,4JAAS,EAAC,IAAI,MACd,OAAO,GAAG,KAAK,IACX,IAAA,4JAAS,EAAC,MAAM,GAAG,KAAK,CAAC,IAAI,GAAG,SAChC,IAAA,4JAAS,EAAC,OAAO,MAAM,GAAG,KAAK,CAAC,KAAK;QACnD;IACJ;AACJ;AAOO,IAAI,gBAAgB,SAAU,CAAC;IAClC,IAAI,QAAQ,KAAK,EAAE,KAAK;IACxB,IAAI,KAAK,eAAe,IAAI,UAAU,GAAG,OAAO,EAAE,WAAW,GAAG,QAAQ;IACxE,IAAI,SAAS,SAAU,EAAE,EAAE,SAAS;QAChC,OAAO,OAAO,MAAM,KAAK,UAAU,GAAG,KAAK,IAAI,KAAK;IACxD;IACA,IAAI,YAAY,SAAU,EAAE,EAAE,CAAC;QAC3B,OAAO,OAAO,MACR,IAAA,4JAAS,EAAC,IAAI,MACd,EAAE,GAAG,KAAK,IACN,IAAA,4JAAS,EAAC,OAAO,MAAM,GAAG,KAAK,KAC/B,IAAA,4JAAS,EAAC,MAAM,GAAG,KAAK,GAAG;IACzC;IACA,OAAO;QACH,KAAK;QACL,IAAI;QACJ,KAAK;QACL,SAAS;QACT,UAAU;QACV,QAAQ;QACR,WAAW,SAAU,EAAE,EAAE,CAAC;YACtB,IAAI,OAAO,KAAK;gBACZ,OAAO;YACX;YACA,IAAI,KAAK,EAAE,GAAG,KAAK;YACnB,OAAO,GAAG,IAAI,KAAK,SAAS,QAAQ,MAAM,GAAG,KAAK;QACtD;QACA,WAAW;QACX,cAAc,SAAU,EAAE,EAAE,CAAC;YACzB,IAAI,OAAO,KAAK;gBACZ,OAAO,IAAA,4JAAS,EAAC,IAAI;YACzB;YACA,IAAI,IAAI,EAAE,GAAG,KAAK;YAClB,OAAO,OAAO,KAAK,IAAA,4JAAS,EAAC,MAAM,EAAE,IAAI,GAAG,SAAS,IAAA,4JAAS,EAAC,OAAO,MAAM,EAAE,KAAK;QACvF;IACJ;AACJ;AAOO,IAAI,gBAAgB,SAAU,CAAC;IAClC,IAAI,KAAK,cAAc;IACvB,IAAI,IAAI,eAAe;IACvB,OAAO;QACH,KAAK;QACL,IAAI;QACJ,KAAK;QACL,SAAS,GAAG,OAAO;QACnB,UAAU,GAAG,QAAQ;QACrB,QAAQ,GAAG,MAAM;QACjB,WAAW,GAAG,SAAS;QACvB,WAAW,GAAG,SAAS;QACvB,cAAc,GAAG,YAAY;QAC7B,UAAU;QACV,UAAU;QACV,QAAQ;QACR,SAAS;QACT,aAAa;QACb,QAAQ,IAAA,iKAAa,EAAC,aAAa;QACnC,MAAM,IAAA,+JAAW,EAAC,aAAa;IACnC;AACJ;AAsDO,IAAI,2BAA2B,SAAU,EAAE;IAAI,OAAQ;QAC1D,KAAK;QACL,IAAI;QACJ,KAAK;QACL,IAAI,SAAU,GAAG,EAAE,EAAE;YACjB,OAAO,OAAO,OACR,OAAO,MACH,KAAK,GAAG,MAAM,CAAC,IAAI,IAAI,EAAE,GAAG,IAAI,KAChC,MACJ,OAAO,MACH,KACA,MAAM,IAAI,KAAK,CAAC,GAAG,KAAK;QACtC;QACA,IAAI;IACR;AAAI;AAmCG,IAAI,mBAAmB,SAAU,EAAE;IAAI,OAAQ;QAClD,KAAK;QACL,IAAI;QACJ,KAAK;QACL,KAAK,SAAU,EAAE,EAAE,IAAI;YACnB,IAAI,QAAQ,KAAK;gBACb,OAAO;YACX;YACA,IAAI,KAAK;YACT,OAAO,OAAO,MAAM,KAAK,GAAG,MAAM,CAAC,GAAG,IAAI,EAAE,GAAG,IAAI,KAAK;QAC5D;IACJ;AAAI;AAKG,IAAI,MAAM,SAAU,CAAC;IAAI,OAAO,SAAU,EAAE;QAC/C,OAAO,OAAO,MAAM,KAAK,MAAM,EAAE,GAAG,KAAK;IAC7C;AAAG;AAKI,IAAI,UAAU;IACjB,KAAK;IACL,KAAK;AACT;AAOO,IAAI,KAAK,IAAA,sJAAI,EAAC,GAAG,IAAA,mJAAG,EAAC;AAOrB,IAAI,SAAS,IAAA,uJAAO,EAAC;AAKrB,IAAI,KAAK;AAKT,IAAI,UAAU;IACjB,KAAK;IACL,IAAI;AACR;AAQO,IAAI,MAAM,SAAU,EAAE;IAAI,OAAO,SAAU,GAAG;QACjD,OAAO,OAAO,OAAO,MAAM,OAAO,MAAM,KAAK,MAAM,IAAI,KAAK,CAAC,GAAG,KAAK;IACzE;AAAG;AAII,IAAI,KAAK;AAKT,IAAI,QAAQ;IACf,KAAK;IACL,KAAK;IACL,IAAI;AACR;AAKO,IAAI,cAAc;IACrB,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;AACR;AAKO,IAAI,QAAQ;IACf,KAAK;IACL,KAAK;IACL,IAAI;IACJ,OAAO;AACX;AAKO,IAAI,QAAQ;IACf,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,OAAO;AACX;AAwBO,IAAI,SAAS,SAAU,CAAC,EAAE,CAAC;IAAI,OAAO,SAAU,EAAE;QACrD,OAAO,OAAO,MAAM,IAAI,EAAE,GAAG,GAAG,KAAK;IACzC;AAAG;AAwBI,IAAI,UAAU,SAAU,CAAC;IAAI,OAAO,SAAU,CAAC;QAAI,OAAO,SAAU,EAAE;YACzE,OAAO,OAAO,MAAM,EAAE,KAAK,GAAG,EAAE,GAAG,KAAK;QAC5C;IAAG;AAAG;AAwBC,IAAI,cAAc,SAAU,CAAC,EAAE,CAAC;IAAI,OAAO,SAAU,EAAE;QAC1D,OAAO,OAAO,MAAM,IAAI,EAAE,GAAG,KAAK,EAAE;IACxC;AAAG;AAKI,IAAI,WAAW;IAClB,KAAK;IACL,QAAQ;IACR,SAAS;IACT,aAAa;AACjB;AAuBO,IAAI,WAAW,SAAU,CAAC;IAC7B,OAAO,SAAU,CAAC;QACd,OAAO,SAAU,EAAE;YACf,OAAO,OAAO,MAAM,EAAE,EAAE,CAAC,KAAK,GAAG,IAAI,KAAK,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,GAAG;QACjE;IACJ;AACJ;AAsBO,IAAI,WAAW,SAAU,CAAC;IAC7B,OAAO,SAAU,EAAE;QACf,OAAO,OAAO,MAAM,EAAE,EAAE,CAAC,KAAK,GAAG,IAAI,KAAK,EAAE,GAAG,CAAC,GAAG,KAAK,EAAE;IAC9D;AACJ;AAKO,IAAI,cAAc;IACrB,KAAK;IACL,KAAK;IACL,QAAQ;IACR,SAAS;IACT,aAAa;IACb,UAAU;IACV,UAAU;AACd;AAOO,IAAI,QAAQ,SAAU,CAAC,EAAE,CAAC;IAAI,OAAO,SAAU,EAAE;QACpD,OAAO,OAAO,MAAM,KAAK,EAAE,GAAG,IAAI,KAAK,MAAM,EAAE,GAAG,KAAK;IAC3D;AAAG;AAOI,IAAI,UAAU,SAAU,CAAC;IAAI,OAAO,SAAU,EAAE;QACnD,OAAO,OAAO,MAAM,KAAK,EAAE,GAAG,IAAI,KAAK;IAC3C;AAAG;AAKI,IAAI,YAAY;IACnB,KAAK;IACL,OAAO;IACP,SAAS;AACb;AASO,IAAI,OAAO,SAAU,IAAI;IAAI,OAAO,SAAU,EAAE;QACnD,OAAO,OAAO,MAAM,SAAS;IACjC;AAAG;AAkDI,IAAI,MAAM;AAKV,IAAI,MAAM;IACb,KAAK;IACL,KAAK;IACL,KAAK;AACT;AAIO,IAAI,SAAS,SAAU,CAAC;IAAI,OAAO,SAAU,EAAE;QAClD,OAAO,OAAO,MAAM,KAAK,MAAM,EAAE;IACrC;AAAG;AAKI,IAAI,SAAS;IAChB,KAAK;IACL,KAAK;IACL,QAAQ;AACZ;AAKO,IAAI,WAAW;IAClB,KAAK;IACL,KAAK;IACL,IAAI;IACJ,OAAO;IACP,UAAU;AACd;AAIO,IAAI,aAAa;AAKjB,IAAI,aAAa;IACpB,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,YAAY;AAChB;AAKO,IAAI,aAAa;IACpB,KAAK;IACL,YAAY,0JAAQ;AACxB;AA8BO,IAAI,gBAAgB,WAAW,GAAG,IAAA,iKAAc,EAAC;AA4BjD,IAAI,aACX,WAAW,GAAG,IAAA,8JAAW,EAAC;AAUnB,IAAI,SAAS,wJAAQ;AAOrB,IAAI,UAAU,yJAAS;AASvB,IAAI,SAAS,SAAU,MAAM,EAAE,OAAO;IACzC,OAAO,SAAU,EAAE;QACf,OAAO,OAAO,MAAM,OAAO,GAAG,IAAI,IAAI,QAAQ,GAAG,KAAK;IAC1D;AACJ;AAOO,IAAI,QAAQ;AAmCZ,IAAI,QAAQ;AAOZ,IAAI,OAAO;AASX,IAAI,aAAa,SAAU,MAAM;IACpC,OAAO,SAAU,EAAE;QACf,OAAO,OAAO,MAAM,OAAO,GAAG,IAAI,IAAI,GAAG,KAAK;IAClD;AACJ;AA0BO,IAAI,YAAY;AAQhB,IAAI,OAAO,WAAW,GAAG,IAAA,qJAAK,EAAC;AAM/B,IAAI,UAAU,WAAW,GAAG,IAAA,sJAAQ,EAAC;AAQrC,IAAI,WAAW;AAMf,IAAI,WAAW,WAAW,GAAG,IAAA,uJAAS,EAAC;AAQvC,IAAI,YAAY;AAQhB,IAAI,MAAM,WAAW,GAAG,IAAA,sJAAI,EAAC,GAAG,kJAAa,CAAC;AAS9C,IAAI,WACX,WAAW,GAAG,QAAQ,0JAAQ;AAcvB,IAAI,UAAU;AAId,IAAI,YAAY,WAAW,GAAG,OAAO,0JAAQ;AAO7C,IAAI,cACX,WAAW,GAAG,IAAA,+JAAY,EAAC;AAOpB,IAAI,eAAe,WAAW,GAAG,IAAA,gKAAa,EAAC,YAAY;AAO3D,IAAI,gBAAgB;AAC3B,cAAc,GACd,IAAI,cAAc;IACd,YAAY,WAAW,UAAU;AACrC;AAKO,IAAI,eAAe,WAAW,GAAG,8JAAc,CAAC;AAKhD,IAAI,aAAa,WAAW,GAAG,4JAAY,CAAC;AACnD,cAAc,GACd,IAAI,WAAW;IACX,SAAS;AACb;AAKO,IAAI,kBAAkB,WAAW,GAAG,iKAAiB,CAAC,aAAa;AAKnE,IAAI,gBAAgB,WAAW,GAAG,+JAAe,CAAC,aAAa;AAwC/D,IAAI,eAAe,WAAW,GAAG,IAAA,gKAAa,EAAC,YAAY;AAS3D,IAAI,gBAAgB;AAMpB,IAAI,OAAO,SAAU,EAAE;IAAI,OAAQ,OAAO,MAAM,MAAM,GAAG,IAAI,IAAI,KAAK,GAAG,KAAK;AAAI;AASlF,IAAI,UAAU,SAAU,MAAM;IACjC,OAAO,SAAU,EAAE;QACf,OAAO,OAAO,MAAM,OAAO,GAAG,IAAI,IAAI;IAC1C;AACJ;AAOO,IAAI,SAAS;AAgBb,IAAI,eAAe,SAAU,CAAC;IACjC,OAAO,SAAU,CAAC;QACd,OAAO,KAAK,OAAO,KAAK,KAAK,MAAM;IACvC;AACJ;AA0BO,IAAI,WAAW,SAAU,CAAC,EAAE,OAAO;IACtC,IAAI;QACA,OAAO,MAAM;IACjB,EACA,OAAO,GAAG;QACN,OAAO,KAAK,QAAQ;IACxB;AACJ;AAOO,IAAI,YAAY,SAAU,CAAC,EAAE,OAAO;IACvC,OAAO;QACH,IAAI,IAAI,EAAE;QACV,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;YAC1C,CAAC,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;QACzB;QACA,OAAO,SAAS;YAAc,OAAO,EAAE,KAAK,CAAC,KAAK,GAAG;QAAI,GAAG;IAChE;AACJ;AAOO,IAAI,gBAAgB,SAAU,CAAC;IAClC,IAAI,OAAO,aAAa;IACxB,OAAO,SAAU,CAAC;QAAI,OAAO,IAAA,sJAAI,EAAC,GAAG;IAAO;AAChD;AAOO,IAAI,iBAAiB,SAAU,CAAC;IACnC,IAAI,OAAO,cAAc;IACzB,OAAO,SAAU,CAAC;QAAI,OAAO,QAAQ,KAAK;IAAK;AACnD;AAKO,IAAI,UAAU,WAAW,GAAG,MAAM,0JAAQ,EAAE,0JAAQ;AASpD,SAAS,QAAQ,CAAC;IACrB,IAAI;QACA,OAAO,aAAa,QAAQ,IAAI,IAAI,MAAM,OAAO;IACrD,EACA,OAAO,OAAO;QACV,OAAO,IAAI;IACf;AACJ;AACO,SAAS,KAAK,CAAC;IAClB,OAAO,SAAU,CAAC,EAAE,EAAE;QAClB,IAAI,OAAO,WAAW;YAClB,IAAI,UAAU,KAAK;YACnB,OAAO,SAAU,EAAE;gBAAI,OAAO,QAAQ,GAAG;YAAK;QAClD;QACA,OAAO,OAAO,MAAM,QAAQ,EAAE,MAAM,CAAC,GAAG,GAAG,KAAK;IACpD;AACJ;AAeO,IAAI,SAAS,SAAU,SAAS;IACnC,OAAO,SAAU,EAAE;QACf,OAAO,OAAO,MAAM,QAAQ,UAAU,GAAG,KAAK;IAClD;AACJ;AAQO,IAAI,KAAK,WAAW,GAAG,GAAG,6JAAa;AAKvC,IAAI,SAAS,WAAW,GAAG,IAAA,uJAAO,EAAC;AAC1C,IAAI,OAAO,WAAW,GAAG,IAAA,oJAAK,EAAC;;AAWxB,IAAI,OAAO,WAAW,GAAG,mJAAc,CAAC;AAOxC,IAAI,QAAQ;AAKZ,IAAI,MAAM,WAAW,GAAG,IAAA,kJAAI,EAAC;AAS7B,IAAI,OAAO;AAIX,IAAI,MAAM,WAAW,GAAG,GAAG,oKAAoB;AAU/C,IAAI,yCAAyC,SAAU,CAAC;IAC3D,OAAO,SAAU,EAAE;QACf,IAAI,IAAI,EAAE,GAAG,sJAAM,CAAC;QACpB,IAAI,OAAO,IAAI;YACX,OAAO;QACX;QACA,IAAI,MAAM;YAAC,EAAE,KAAK;SAAC;QACnB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM,EAAE,IAAK;YAChC,IAAI,MAAM,EAAE,GAAG,EAAE,CAAC,EAAE;YACpB,IAAI,OAAO,MAAM;gBACb,OAAO;YACX;YACA,IAAI,IAAI,CAAC,IAAI,KAAK;QACtB;QACA,OAAO,MAAM;IACjB;AACJ;AAOO,IAAI,iCAAiC,SAAU,CAAC;IACnD,IAAI,IAAI,uCAAuC;IAC/C,OAAO,SAAU,EAAE;QAAI,OAAQ,4JAAY,CAAC,MAAM,EAAE,MAAM;IAAM;AACpE;AAOO,IAAI,yBAAyB;AAO7B,IAAI,gBAAgB,SAAU,CAAC;IAAI,OAAO,+BAA+B,SAAU,CAAC,EAAE,CAAC;QAAI,OAAO,EAAE;IAAI;AAAI;AAO5G,IAAI,gBACX,WAAW,GAAG,cAAc,0JAAQ;AAU7B,IAAI,SAAS;AAOb,IAAI,QAAQ;AAOZ,IAAI,aAAa;AAOjB,IAAI,cAAc;AAQlB,SAAS,UAAU,CAAC,EAAE,OAAO;IAChC,OAAO,SAAS;QAAc,OAAO,KAAK,KAAK,CAAC;IAAI,GAAG;AAC3D;AAQO,IAAI,gBAAgB,SAAU,CAAC,EAAE,OAAO;IAC3C,OAAO,SAAS;QACZ,IAAI,IAAI,KAAK,SAAS,CAAC;QACvB,IAAI,OAAO,MAAM,UAAU;YACvB,MAAM,IAAI,MAAM;QACpB;QACA,OAAO;IACX,GAAG;AACP;AAUO,IAAI,SAAS;IAChB,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,QAAQ;IACR,SAAS;IACT,aAAa;IACb,UAAU;IACV,UAAU;IACV,OAAO;IACP,SAAS;IACT,KAAK;IACL,QAAQ;IACR,UAAU;IACV,YAAY;AAChB;AAWO,IAAI,oBACX,WAAW,GAAG,IAAA,gKAAkB,EAAC;AAQ1B,IAAI,iBACX,WAAW,GAAG,IAAA,yKAAoB,EAAC;AAQ5B,IAAI,yBAAyB,SAAU,EAAE,EAAE,EAAE;IAChD,OAAO,IAAA,gKAAkB,EAAC,yBAAyB,KAAK;AAC5D;AAQO,IAAI,sBAAsB,SAAU,EAAE,EAAE,EAAE;IAC7C,OAAO,IAAA,yKAAoB,EAAC,yBAAyB,KAAK;AAC9D;AAQO,SAAS,cAAc,EAAE;IAC5B,IAAI,KAAK,yBAAyB,IAAI,EAAE;IACxC,IAAI,MAAM,iBAAiB,IAAI,GAAG;IAClC,OAAO;QACH,KAAK;QACL,IAAI;QACJ,KAAK;QACL,IAAI;QACJ,OAAO;QACP,OAAO;QACP,SAAS;QACT,QAAQ;QACR,SAAS;QACT,aAAa;QACb,QAAQ;QACR,UAAU;QACV,UAAU;QACV,UAAU;QACV,YAAY;QACZ,IAAI;QACJ,KAAK;IACT;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3457, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/fp-ts/es6/EitherT.js"], "sourcesContent": ["import { ap as ap_ } from './Apply';\nimport * as E from './Either';\nimport { flow, pipe } from './function';\nimport { map as map_ } from './Functor';\nexport function right(F) {\n    return flow(E.right, F.of);\n}\nexport function left(F) {\n    return flow(E.left, F.of);\n}\nexport function rightF(F) {\n    return function (fa) { return F.map(fa, E.right); };\n}\nexport function leftF(F) {\n    return function (fe) { return F.map(fe, E.left); };\n}\nexport function fromNullable(F) {\n    return function (e) { return flow(E.fromNullable(e), F.of); };\n}\nexport function fromNullableK(F) {\n    var fromNullableF = fromNullable(F);\n    return function (e) {\n        var fromNullableFE = fromNullableF(e);\n        return function (f) { return flow(f, fromNullableFE); };\n    };\n}\nexport function chainNullableK(M) {\n    var chainM = chain(M);\n    var fromNullableKM = fromNullableK(M);\n    return function (e) {\n        var fromNullableKMe = fromNullableKM(e);\n        return function (f) { return chainM(fromNullableKMe(f)); };\n    };\n}\nexport function map(F) {\n    return map_(F, E.Functor);\n}\nexport function ap(F) {\n    return ap_(F, E.Apply);\n}\nexport function chain(M) {\n    var flatMapM = flatMap(M);\n    return function (f) { return function (ma) { return flatMapM(ma, f); }; };\n}\n/** @internal */\nexport function flatMap(M) {\n    return function (ma, f) { return M.chain(ma, function (e) { return (E.isLeft(e) ? M.of(e) : f(e.right)); }); };\n}\nexport function alt(M) {\n    return function (second) { return function (first) { return M.chain(first, function (e) { return (E.isLeft(e) ? second() : M.of(e)); }); }; };\n}\nexport function bimap(F) {\n    var mapBothF = mapBoth(F);\n    return function (f, g) { return function (self) { return mapBothF(self, f, g); }; };\n}\n/** @internal */\nexport function mapBoth(F) {\n    return function (self, f, g) { return F.map(self, E.bimap(f, g)); };\n}\nexport function mapLeft(F) {\n    var mapErrorF = mapError(F);\n    return function (f) { return function (self) { return mapErrorF(self, f); }; };\n}\n/** @internal */\nexport function mapError(F) {\n    return function (self, f) { return F.map(self, E.mapLeft(f)); };\n}\nexport function altValidation(M, S) {\n    return function (second) { return function (first) {\n        return M.chain(first, E.match(function (e1) {\n            return M.map(second(), E.mapLeft(function (e2) { return S.concat(e1, e2); }));\n        }, right(M)));\n    }; };\n}\nexport function match(F) {\n    return function (onLeft, onRight) { return function (ma) { return F.map(ma, E.match(onLeft, onRight)); }; };\n}\nexport function matchE(M) {\n    return function (onLeft, onRight) { return function (ma) { return M.chain(ma, E.match(onLeft, onRight)); }; };\n}\nexport function getOrElse(M) {\n    return function (onLeft) { return function (ma) { return M.chain(ma, E.match(onLeft, M.of)); }; };\n}\nexport function orElse(M) {\n    return function (onLeft) { return function (ma) { return M.chain(ma, function (e) { return (E.isLeft(e) ? onLeft(e.left) : M.of(e)); }); }; };\n}\nexport function orElseFirst(M) {\n    var tapErrorM = tapError(M);\n    return function (onLeft) { return function (ma) { return tapErrorM(ma, onLeft); }; };\n}\n/** @internal */\nexport function tapError(M) {\n    var orElseM = orElse(M);\n    return function (ma, onLeft) {\n        return pipe(ma, orElseM(function (e) { return M.map(onLeft(e), function (eb) { return (E.isLeft(eb) ? eb : E.left(e)); }); }));\n    };\n}\nexport function orLeft(M) {\n    return function (onLeft) { return function (ma) {\n        return M.chain(ma, E.match(function (e) { return M.map(onLeft(e), E.left); }, function (a) { return M.of(E.right(a)); }));\n    }; };\n}\nexport function swap(F) {\n    return function (ma) { return F.map(ma, E.swap); };\n}\nexport function toUnion(F) {\n    return function (fa) { return F.map(fa, E.toUnion); };\n}\n/** @deprecated  */\n/* istanbul ignore next */\nexport function getEitherM(M) {\n    var _ap = ap(M);\n    var _map = map(M);\n    var _chain = chain(M);\n    var _alt = alt(M);\n    var _bimap = bimap(M);\n    var _mapLeft = mapLeft(M);\n    var _fold = matchE(M);\n    var _getOrElse = getOrElse(M);\n    var _orElse = orElse(M);\n    return {\n        map: function (fa, f) { return pipe(fa, _map(f)); },\n        ap: function (fab, fa) { return pipe(fab, _ap(fa)); },\n        of: right(M),\n        chain: function (ma, f) { return pipe(ma, _chain(f)); },\n        alt: function (fa, that) { return pipe(fa, _alt(that)); },\n        bimap: function (fea, f, g) { return pipe(fea, _bimap(f, g)); },\n        mapLeft: function (fea, f) { return pipe(fea, _mapLeft(f)); },\n        fold: function (fa, onLeft, onRight) { return pipe(fa, _fold(onLeft, onRight)); },\n        getOrElse: function (fa, onLeft) { return pipe(fa, _getOrElse(onLeft)); },\n        orElse: function (fa, f) { return pipe(fa, _orElse(f)); },\n        swap: swap(M),\n        rightM: rightF(M),\n        leftM: leftF(M),\n        left: left(M)\n    };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;;;;;AACO,SAAS,MAAM,CAAC;IACnB,OAAO,IAAA,sJAAI,EAAC,qJAAO,EAAE,EAAE,EAAE;AAC7B;AACO,SAAS,KAAK,CAAC;IAClB,OAAO,IAAA,sJAAI,EAAC,oJAAM,EAAE,EAAE,EAAE;AAC5B;AACO,SAAS,OAAO,CAAC;IACpB,OAAO,SAAU,EAAE;QAAI,OAAO,EAAE,GAAG,CAAC,IAAI,qJAAO;IAAG;AACtD;AACO,SAAS,MAAM,CAAC;IACnB,OAAO,SAAU,EAAE;QAAI,OAAO,EAAE,GAAG,CAAC,IAAI,oJAAM;IAAG;AACrD;AACO,SAAS,aAAa,CAAC;IAC1B,OAAO,SAAU,CAAC;QAAI,OAAO,IAAA,sJAAI,EAAC,4JAAc,CAAC,IAAI,EAAE,EAAE;IAAG;AAChE;AACO,SAAS,cAAc,CAAC;IAC3B,IAAI,gBAAgB,aAAa;IACjC,OAAO,SAAU,CAAC;QACd,IAAI,iBAAiB,cAAc;QACnC,OAAO,SAAU,CAAC;YAAI,OAAO,IAAA,sJAAI,EAAC,GAAG;QAAiB;IAC1D;AACJ;AACO,SAAS,eAAe,CAAC;IAC5B,IAAI,SAAS,MAAM;IACnB,IAAI,iBAAiB,cAAc;IACnC,OAAO,SAAU,CAAC;QACd,IAAI,kBAAkB,eAAe;QACrC,OAAO,SAAU,CAAC;YAAI,OAAO,OAAO,gBAAgB;QAAK;IAC7D;AACJ;AACO,SAAS,IAAI,CAAC;IACjB,OAAO,IAAA,oJAAI,EAAC,GAAG,uJAAS;AAC5B;AACO,SAAS,GAAG,CAAC;IAChB,OAAO,IAAA,iJAAG,EAAC,GAAG,qJAAO;AACzB;AACO,SAAS,MAAM,CAAC;IACnB,IAAI,WAAW,QAAQ;IACvB,OAAO,SAAU,CAAC;QAAI,OAAO,SAAU,EAAE;YAAI,OAAO,SAAS,IAAI;QAAI;IAAG;AAC5E;AAEO,SAAS,QAAQ,CAAC;IACrB,OAAO,SAAU,EAAE,EAAE,CAAC;QAAI,OAAO,EAAE,KAAK,CAAC,IAAI,SAAU,CAAC;YAAI,OAAQ,sJAAQ,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,EAAE,EAAE,KAAK;QAAI;IAAI;AACjH;AACO,SAAS,IAAI,CAAC;IACjB,OAAO,SAAU,MAAM;QAAI,OAAO,SAAU,KAAK;YAAI,OAAO,EAAE,KAAK,CAAC,OAAO,SAAU,CAAC;gBAAI,OAAQ,sJAAQ,CAAC,KAAK,WAAW,EAAE,EAAE,CAAC;YAAK;QAAI;IAAG;AAChJ;AACO,SAAS,MAAM,CAAC;IACnB,IAAI,WAAW,QAAQ;IACvB,OAAO,SAAU,CAAC,EAAE,CAAC;QAAI,OAAO,SAAU,IAAI;YAAI,OAAO,SAAS,MAAM,GAAG;QAAI;IAAG;AACtF;AAEO,SAAS,QAAQ,CAAC;IACrB,OAAO,SAAU,IAAI,EAAE,CAAC,EAAE,CAAC;QAAI,OAAO,EAAE,GAAG,CAAC,MAAM,qJAAO,CAAC,GAAG;IAAK;AACtE;AACO,SAAS,QAAQ,CAAC;IACrB,IAAI,YAAY,SAAS;IACzB,OAAO,SAAU,CAAC;QAAI,OAAO,SAAU,IAAI;YAAI,OAAO,UAAU,MAAM;QAAI;IAAG;AACjF;AAEO,SAAS,SAAS,CAAC;IACtB,OAAO,SAAU,IAAI,EAAE,CAAC;QAAI,OAAO,EAAE,GAAG,CAAC,MAAM,uJAAS,CAAC;IAAK;AAClE;AACO,SAAS,cAAc,CAAC,EAAE,CAAC;IAC9B,OAAO,SAAU,MAAM;QAAI,OAAO,SAAU,KAAK;YAC7C,OAAO,EAAE,KAAK,CAAC,OAAO,qJAAO,CAAC,SAAU,EAAE;gBACtC,OAAO,EAAE,GAAG,CAAC,UAAU,uJAAS,CAAC,SAAU,EAAE;oBAAI,OAAO,EAAE,MAAM,CAAC,IAAI;gBAAK;YAC9E,GAAG,MAAM;QACb;IAAG;AACP;AACO,SAAS,MAAM,CAAC;IACnB,OAAO,SAAU,MAAM,EAAE,OAAO;QAAI,OAAO,SAAU,EAAE;YAAI,OAAO,EAAE,GAAG,CAAC,IAAI,qJAAO,CAAC,QAAQ;QAAW;IAAG;AAC9G;AACO,SAAS,OAAO,CAAC;IACpB,OAAO,SAAU,MAAM,EAAE,OAAO;QAAI,OAAO,SAAU,EAAE;YAAI,OAAO,EAAE,KAAK,CAAC,IAAI,qJAAO,CAAC,QAAQ;QAAW;IAAG;AAChH;AACO,SAAS,UAAU,CAAC;IACvB,OAAO,SAAU,MAAM;QAAI,OAAO,SAAU,EAAE;YAAI,OAAO,EAAE,KAAK,CAAC,IAAI,qJAAO,CAAC,QAAQ,EAAE,EAAE;QAAI;IAAG;AACpG;AACO,SAAS,OAAO,CAAC;IACpB,OAAO,SAAU,MAAM;QAAI,OAAO,SAAU,EAAE;YAAI,OAAO,EAAE,KAAK,CAAC,IAAI,SAAU,CAAC;gBAAI,OAAQ,sJAAQ,CAAC,KAAK,OAAO,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC;YAAK;QAAI;IAAG;AAChJ;AACO,SAAS,YAAY,CAAC;IACzB,IAAI,YAAY,SAAS;IACzB,OAAO,SAAU,MAAM;QAAI,OAAO,SAAU,EAAE;YAAI,OAAO,UAAU,IAAI;QAAS;IAAG;AACvF;AAEO,SAAS,SAAS,CAAC;IACtB,IAAI,UAAU,OAAO;IACrB,OAAO,SAAU,EAAE,EAAE,MAAM;QACvB,OAAO,IAAA,sJAAI,EAAC,IAAI,QAAQ,SAAU,CAAC;YAAI,OAAO,EAAE,GAAG,CAAC,OAAO,IAAI,SAAU,EAAE;gBAAI,OAAQ,sJAAQ,CAAC,MAAM,KAAK,oJAAM,CAAC;YAAK;QAAI;IAC/H;AACJ;AACO,SAAS,OAAO,CAAC;IACpB,OAAO,SAAU,MAAM;QAAI,OAAO,SAAU,EAAE;YAC1C,OAAO,EAAE,KAAK,CAAC,IAAI,qJAAO,CAAC,SAAU,CAAC;gBAAI,OAAO,EAAE,GAAG,CAAC,OAAO,IAAI,oJAAM;YAAG,GAAG,SAAU,CAAC;gBAAI,OAAO,EAAE,EAAE,CAAC,qJAAO,CAAC;YAAK;QAC1H;IAAG;AACP;AACO,SAAS,KAAK,CAAC;IAClB,OAAO,SAAU,EAAE;QAAI,OAAO,EAAE,GAAG,CAAC,IAAI,oJAAM;IAAG;AACrD;AACO,SAAS,QAAQ,CAAC;IACrB,OAAO,SAAU,EAAE;QAAI,OAAO,EAAE,GAAG,CAAC,IAAI,uJAAS;IAAG;AACxD;AAGO,SAAS,WAAW,CAAC;IACxB,IAAI,MAAM,GAAG;IACb,IAAI,OAAO,IAAI;IACf,IAAI,SAAS,MAAM;IACnB,IAAI,OAAO,IAAI;IACf,IAAI,SAAS,MAAM;IACnB,IAAI,WAAW,QAAQ;IACvB,IAAI,QAAQ,OAAO;IACnB,IAAI,aAAa,UAAU;IAC3B,IAAI,UAAU,OAAO;IACrB,OAAO;QACH,KAAK,SAAU,EAAE,EAAE,CAAC;YAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,KAAK;QAAK;QAClD,IAAI,SAAU,GAAG,EAAE,EAAE;YAAI,OAAO,IAAA,sJAAI,EAAC,KAAK,IAAI;QAAM;QACpD,IAAI,MAAM;QACV,OAAO,SAAU,EAAE,EAAE,CAAC;YAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,OAAO;QAAK;QACtD,KAAK,SAAU,EAAE,EAAE,IAAI;YAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,KAAK;QAAQ;QACxD,OAAO,SAAU,GAAG,EAAE,CAAC,EAAE,CAAC;YAAI,OAAO,IAAA,sJAAI,EAAC,KAAK,OAAO,GAAG;QAAK;QAC9D,SAAS,SAAU,GAAG,EAAE,CAAC;YAAI,OAAO,IAAA,sJAAI,EAAC,KAAK,SAAS;QAAK;QAC5D,MAAM,SAAU,EAAE,EAAE,MAAM,EAAE,OAAO;YAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,MAAM,QAAQ;QAAW;QAChF,WAAW,SAAU,EAAE,EAAE,MAAM;YAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,WAAW;QAAU;QACxE,QAAQ,SAAU,EAAE,EAAE,CAAC;YAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,QAAQ;QAAK;QACxD,MAAM,KAAK;QACX,QAAQ,OAAO;QACf,OAAO,MAAM;QACb,MAAM,KAAK;IACf;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3746, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/fp-ts/es6/Filterable.js"], "sourcesContent": ["/**\n * `Filterable` represents data structures which can be _partitioned_/_filtered_.\n *\n * Adapted from https://github.com/LiamGoodacre/purescript-filterable/blob/master/src/Data/Filterable.purs\n *\n * @since 2.0.0\n */\nimport { compact, separate } from './Compactable';\nimport { pipe } from './function';\nimport { getFunctorComposition } from './Functor';\nimport { getLeft, getRight } from './Option';\nimport { not } from './Predicate';\nimport { separated } from './Separated';\nexport function filter(F, G) {\n    return function (predicate) { return function (fga) { return F.map(fga, function (ga) { return G.filter(ga, predicate); }); }; };\n}\nexport function filterMap(F, G) {\n    return function (f) { return function (fga) { return F.map(fga, function (ga) { return G.filterMap(ga, f); }); }; };\n}\nexport function partition(F, G) {\n    var _filter = filter(F, G);\n    return function (predicate) {\n        var left = _filter(not(predicate));\n        var right = _filter(predicate);\n        return function (fgb) { return separated(left(fgb), right(fgb)); };\n    };\n}\nexport function partitionMap(F, G) {\n    var _filterMap = filterMap(F, G);\n    return function (f) { return function (fga) {\n        return separated(pipe(fga, _filterMap(function (a) { return getLeft(f(a)); })), pipe(fga, _filterMap(function (a) { return getRight(f(a)); })));\n    }; };\n}\n/** @deprecated */\nexport function getFilterableComposition(F, G) {\n    var map = getFunctorComposition(F, G).map;\n    var _compact = compact(F, G);\n    var _separate = separate(F, G, G);\n    var _filter = filter(F, G);\n    var _filterMap = filterMap(F, G);\n    var _partition = partition(F, G);\n    var _partitionMap = partitionMap(F, G);\n    return {\n        map: map,\n        compact: _compact,\n        separate: _separate,\n        filter: function (fga, f) { return pipe(fga, _filter(f)); },\n        filterMap: function (fga, f) { return pipe(fga, _filterMap(f)); },\n        partition: function (fga, p) { return pipe(fga, _partition(p)); },\n        partitionMap: function (fga, f) { return pipe(fga, _partitionMap(f)); }\n    };\n}\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;;;;;;;;;;AACD;AACA;AACA;AACA;AACA;AACA;;;;;;;AACO,SAAS,OAAO,CAAC,EAAE,CAAC;IACvB,OAAO,SAAU,SAAS;QAAI,OAAO,SAAU,GAAG;YAAI,OAAO,EAAE,GAAG,CAAC,KAAK,SAAU,EAAE;gBAAI,OAAO,EAAE,MAAM,CAAC,IAAI;YAAY;QAAI;IAAG;AACnI;AACO,SAAS,UAAU,CAAC,EAAE,CAAC;IAC1B,OAAO,SAAU,CAAC;QAAI,OAAO,SAAU,GAAG;YAAI,OAAO,EAAE,GAAG,CAAC,KAAK,SAAU,EAAE;gBAAI,OAAO,EAAE,SAAS,CAAC,IAAI;YAAI;QAAI;IAAG;AACtH;AACO,SAAS,UAAU,CAAC,EAAE,CAAC;IAC1B,IAAI,UAAU,OAAO,GAAG;IACxB,OAAO,SAAU,SAAS;QACtB,IAAI,OAAO,QAAQ,IAAA,sJAAG,EAAC;QACvB,IAAI,QAAQ,QAAQ;QACpB,OAAO,SAAU,GAAG;YAAI,OAAO,IAAA,4JAAS,EAAC,KAAK,MAAM,MAAM;QAAO;IACrE;AACJ;AACO,SAAS,aAAa,CAAC,EAAE,CAAC;IAC7B,IAAI,aAAa,UAAU,GAAG;IAC9B,OAAO,SAAU,CAAC;QAAI,OAAO,SAAU,GAAG;YACtC,OAAO,IAAA,4JAAS,EAAC,IAAA,sJAAI,EAAC,KAAK,WAAW,SAAU,CAAC;gBAAI,OAAO,IAAA,uJAAO,EAAC,EAAE;YAAK,KAAK,IAAA,sJAAI,EAAC,KAAK,WAAW,SAAU,CAAC;gBAAI,OAAO,IAAA,wJAAQ,EAAC,EAAE;YAAK;QAC/I;IAAG;AACP;AAEO,SAAS,yBAAyB,CAAC,EAAE,CAAC;IACzC,IAAI,MAAM,IAAA,sKAAqB,EAAC,GAAG,GAAG,GAAG;IACzC,IAAI,WAAW,IAAA,4JAAO,EAAC,GAAG;IAC1B,IAAI,YAAY,IAAA,6JAAQ,EAAC,GAAG,GAAG;IAC/B,IAAI,UAAU,OAAO,GAAG;IACxB,IAAI,aAAa,UAAU,GAAG;IAC9B,IAAI,aAAa,UAAU,GAAG;IAC9B,IAAI,gBAAgB,aAAa,GAAG;IACpC,OAAO;QACH,KAAK;QACL,SAAS;QACT,UAAU;QACV,QAAQ,SAAU,GAAG,EAAE,CAAC;YAAI,OAAO,IAAA,sJAAI,EAAC,KAAK,QAAQ;QAAK;QAC1D,WAAW,SAAU,GAAG,EAAE,CAAC;YAAI,OAAO,IAAA,sJAAI,EAAC,KAAK,WAAW;QAAK;QAChE,WAAW,SAAU,GAAG,EAAE,CAAC;YAAI,OAAO,IAAA,sJAAI,EAAC,KAAK,WAAW;QAAK;QAChE,cAAc,SAAU,GAAG,EAAE,CAAC;YAAI,OAAO,IAAA,sJAAI,EAAC,KAAK,cAAc;QAAK;IAC1E;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3846, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/fp-ts/es6/FromIO.js"], "sourcesContent": ["/**\n * Lift a computation from the `IO` monad\n *\n * @since 2.10.0\n */\nimport { tap } from './Chain';\nimport { flow } from './function';\nexport function fromIOK(F) {\n    return function (f) { return flow(f, F.fromIO); };\n}\nexport function chainIOK(F, M) {\n    return function (f) {\n        var g = flow(f, F.fromIO);\n        return function (first) { return M.chain(first, g); };\n    };\n}\nexport function chainFirstIOK(F, M) {\n    var tapIOM = tapIO(F, M);\n    return function (f) { return function (first) { return tapIOM(first, f); }; };\n}\n/** @internal */\nexport function tapIO(F, M) {\n    var chainFirstM = tap(M);\n    return function (self, f) { return chainFirstM(self, flow(f, F.fromIO)); };\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;;;;AACD;AACA;;;AACO,SAAS,QAAQ,CAAC;IACrB,OAAO,SAAU,CAAC;QAAI,OAAO,IAAA,sJAAI,EAAC,GAAG,EAAE,MAAM;IAAG;AACpD;AACO,SAAS,SAAS,CAAC,EAAE,CAAC;IACzB,OAAO,SAAU,CAAC;QACd,IAAI,IAAI,IAAA,sJAAI,EAAC,GAAG,EAAE,MAAM;QACxB,OAAO,SAAU,KAAK;YAAI,OAAO,EAAE,KAAK,CAAC,OAAO;QAAI;IACxD;AACJ;AACO,SAAS,cAAc,CAAC,EAAE,CAAC;IAC9B,IAAI,SAAS,MAAM,GAAG;IACtB,OAAO,SAAU,CAAC;QAAI,OAAO,SAAU,KAAK;YAAI,OAAO,OAAO,OAAO;QAAI;IAAG;AAChF;AAEO,SAAS,MAAM,CAAC,EAAE,CAAC;IACtB,IAAI,cAAc,IAAA,kJAAG,EAAC;IACtB,OAAO,SAAU,IAAI,EAAE,CAAC;QAAI,OAAO,YAAY,MAAM,IAAA,sJAAI,EAAC,GAAG,EAAE,MAAM;IAAI;AAC7E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3895, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/fp-ts/es6/FromTask.js"], "sourcesContent": ["/**\n * Lift a computation from the `Task` monad\n *\n * @since 2.10.0\n */\nimport { tap } from './Chain';\nimport { flow } from './function';\nexport function fromTaskK(F) {\n    return function (f) { return flow(f, F.fromTask); };\n}\nexport function chainTaskK(F, M) {\n    return function (f) {\n        var g = flow(f, F.fromTask);\n        return function (first) { return M.chain(first, g); };\n    };\n}\nexport function chainFirstTaskK(F, M) {\n    var tapTaskM = tapTask(F, M);\n    return function (f) { return function (first) { return tapTaskM(first, f); }; };\n}\n/** @internal */\nexport function tapTask(F, M) {\n    var tapM = tap(M);\n    return function (self, f) { return tapM(self, flow(f, F.fromTask)); };\n}\n"], "names": [], "mappings": "AAAA;;;;CAIC;;;;;;;;;;AACD;AACA;;;AACO,SAAS,UAAU,CAAC;IACvB,OAAO,SAAU,CAAC;QAAI,OAAO,IAAA,sJAAI,EAAC,GAAG,EAAE,QAAQ;IAAG;AACtD;AACO,SAAS,WAAW,CAAC,EAAE,CAAC;IAC3B,OAAO,SAAU,CAAC;QACd,IAAI,IAAI,IAAA,sJAAI,EAAC,GAAG,EAAE,QAAQ;QAC1B,OAAO,SAAU,KAAK;YAAI,OAAO,EAAE,KAAK,CAAC,OAAO;QAAI;IACxD;AACJ;AACO,SAAS,gBAAgB,CAAC,EAAE,CAAC;IAChC,IAAI,WAAW,QAAQ,GAAG;IAC1B,OAAO,SAAU,CAAC;QAAI,OAAO,SAAU,KAAK;YAAI,OAAO,SAAS,OAAO;QAAI;IAAG;AAClF;AAEO,SAAS,QAAQ,CAAC,EAAE,CAAC;IACxB,IAAI,OAAO,IAAA,kJAAG,EAAC;IACf,OAAO,SAAU,IAAI,EAAE,CAAC;QAAI,OAAO,KAAK,MAAM,IAAA,sJAAI,EAAC,GAAG,EAAE,QAAQ;IAAI;AACxE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3944, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/fp-ts/es6/Task.js"], "sourcesContent": ["/**\n * ```ts\n * interface Task<A> {\n *   (): Promise<A>\n * }\n * ```\n *\n * `Task<A>` represents an asynchronous computation that yields a value of type `A` and **never fails**.\n * If you want to represent an asynchronous computation that may fail, please see `TaskEither`.\n *\n * @since 2.0.0\n */\nimport { getApplicativeMonoid } from './Applicative';\nimport { apFirst as apFirst_, apS as apS_, apSecond as apSecond_, getApplySemigroup as getApplySemigroup_ } from './Apply';\nimport * as chainable from './Chain';\nimport { fromIOK as fromIOK_, tapIO as tapIO_ } from './FromIO';\nimport { dual, identity, pipe } from './function';\nimport { as as as_, asUnit as asUnit_, bindTo as bindTo_, flap as flap_, let as let__ } from './Functor';\nimport * as _ from './internal';\n// -------------------------------------------------------------------------------------\n// conversions\n// -------------------------------------------------------------------------------------\n/**\n * @category conversions\n * @since 2.0.0\n */\nexport var fromIO = function (ma) { return function () { return Promise.resolve().then(ma); }; };\n// -------------------------------------------------------------------------------------\n// combinators\n// -------------------------------------------------------------------------------------\n/**\n * Creates a task that will complete after a time delay\n *\n * @example\n * import { sequenceT } from 'fp-ts/Apply'\n * import * as T from 'fp-ts/Task'\n * import { takeRight } from 'fp-ts/Array'\n *\n * async function test() {\n *   const log: Array<string> = []\n *   const append = (message: string): T.Task<void> =>\n *     T.fromIO(() => {\n *       log.push(message)\n *     })\n *   const fa = append('a')\n *   const fb = T.delay(20)(append('b'))\n *   const fc = T.delay(10)(append('c'))\n *   const fd = append('d')\n *   await sequenceT(T.ApplyPar)(fa, fb, fc, fd)()\n *   assert.deepStrictEqual(takeRight(2)(log), ['c', 'b'])\n * }\n *\n * test()\n *\n * @since 2.0.0\n */\nexport function delay(millis) {\n    return function (ma) { return function () {\n        return new Promise(function (resolve) {\n            setTimeout(function () {\n                Promise.resolve().then(ma).then(resolve);\n            }, millis);\n        });\n    }; };\n}\nvar _map = function (fa, f) { return pipe(fa, map(f)); };\nvar _apPar = function (fab, fa) { return pipe(fab, ap(fa)); };\nvar _apSeq = function (fab, fa) { return flatMap(fab, function (f) { return pipe(fa, map(f)); }); };\n/**\n * `map` can be used to turn functions `(a: A) => B` into functions `(fa: F<A>) => F<B>` whose argument and return types\n * use the type constructor `F` to represent some computational context.\n *\n * @category mapping\n * @since 2.0.0\n */\nexport var map = function (f) { return function (fa) { return function () {\n    return Promise.resolve().then(fa).then(f);\n}; }; };\n/**\n * @since 2.0.0\n */\nexport var ap = function (fa) { return function (fab) { return function () {\n    return Promise.all([Promise.resolve().then(fab), Promise.resolve().then(fa)]).then(function (_a) {\n        var f = _a[0], a = _a[1];\n        return f(a);\n    });\n}; }; };\n/**\n * @category constructors\n * @since 2.0.0\n */\nexport var of = function (a) { return function () { return Promise.resolve(a); }; };\n/**\n * @category sequencing\n * @since 2.14.0\n */\nexport var flatMap = /*#__PURE__*/ dual(2, function (ma, f) {\n    return function () {\n        return Promise.resolve()\n            .then(ma)\n            .then(function (a) { return f(a)(); });\n    };\n});\n/**\n * @category sequencing\n * @since 2.0.0\n */\nexport var flatten = /*#__PURE__*/ flatMap(identity);\n/**\n * @category type lambdas\n * @since 2.0.0\n */\nexport var URI = 'Task';\n/**\n * Monoid returning the first completed task.\n *\n * Note: uses `Promise.race` internally.\n *\n * @example\n * import * as T from 'fp-ts/Task'\n *\n * async function test() {\n *   const S = T.getRaceMonoid<string>()\n *   const fa = T.delay(20)(T.of('a'))\n *   const fb = T.delay(10)(T.of('b'))\n *   assert.deepStrictEqual(await S.concat(fa, fb)(), 'b')\n * }\n *\n * test()\n *\n * @category instances\n * @since 2.0.0\n */\nexport function getRaceMonoid() {\n    return {\n        concat: function (x, y) { return function () { return Promise.race([Promise.resolve().then(x), Promise.resolve().then(y)]); }; },\n        empty: never\n    };\n}\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var Functor = {\n    URI: URI,\n    map: _map\n};\n/**\n * Maps the value to the specified constant value.\n *\n * @category mapping\n * @since 2.16.0\n */\nexport var as = dual(2, as_(Functor));\n/**\n * Maps the value to the void constant value.\n *\n * @category mapping\n * @since 2.16.0\n */\nexport var asUnit = asUnit_(Functor);\n/**\n * @category mapping\n * @since 2.10.0\n */\nexport var flap = /*#__PURE__*/ flap_(Functor);\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var Pointed = {\n    URI: URI,\n    of: of\n};\n/**\n * Runs computations in parallel.\n *\n * @category instances\n * @since 2.10.0\n */\nexport var ApplyPar = {\n    URI: URI,\n    map: _map,\n    ap: _apPar\n};\n/**\n * Combine two effectful actions, keeping only the result of the first.\n *\n * @since 2.0.0\n */\nexport var apFirst = /*#__PURE__*/ apFirst_(ApplyPar);\n/**\n * Combine two effectful actions, keeping only the result of the second.\n *\n * @since 2.0.0\n */\nexport var apSecond = /*#__PURE__*/ apSecond_(ApplyPar);\n/**\n * Runs computations in parallel.\n *\n * @category instances\n * @since 2.7.0\n */\nexport var ApplicativePar = {\n    URI: URI,\n    map: _map,\n    ap: _apPar,\n    of: of\n};\n/**\n * Runs computations sequentially.\n *\n * @category instances\n * @since 2.10.0\n */\nexport var ApplySeq = {\n    URI: URI,\n    map: _map,\n    ap: _apSeq\n};\n/**\n * Runs computations sequentially.\n *\n * @category instances\n * @since 2.7.0\n */\nexport var ApplicativeSeq = {\n    URI: URI,\n    map: _map,\n    ap: _apSeq,\n    of: of\n};\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var Chain = {\n    URI: URI,\n    map: _map,\n    ap: _apPar,\n    chain: flatMap\n};\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var Monad = {\n    URI: URI,\n    map: _map,\n    of: of,\n    ap: _apPar,\n    chain: flatMap\n};\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var MonadIO = {\n    URI: URI,\n    map: _map,\n    of: of,\n    ap: _apPar,\n    chain: flatMap,\n    fromIO: fromIO\n};\n/**\n * @category zone of death\n * @since 2.7.0\n * @deprecated\n */\nexport var fromTask = identity;\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var MonadTask = {\n    URI: URI,\n    map: _map,\n    of: of,\n    ap: _apPar,\n    chain: flatMap,\n    fromIO: fromIO,\n    fromTask: fromTask\n};\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var FromIO = {\n    URI: URI,\n    fromIO: fromIO\n};\n/** @internal */\nvar _FlatMap = {\n    flatMap: flatMap\n};\n/** @internal */\nvar _FromIO = {\n    fromIO: FromIO.fromIO\n};\n/**\n * @category sequencing\n * @since 2.16.0\n */\nexport var flatMapIO = _.flatMapIO(_FromIO, _FlatMap);\n/**\n * Composes computations in sequence, using the return value of one computation to determine the next computation and\n * keeping only the result of the first.\n *\n * @category combinators\n * @since 2.15.0\n */\nexport var tap = /*#__PURE__*/ dual(2, chainable.tap(Chain));\n/**\n * Composes computations in sequence, using the return value of one computation to determine the next computation and\n * keeping only the result of the first.\n *\n * @example\n * import { pipe } from 'fp-ts/function'\n * import * as T from 'fp-ts/Task'\n * import * as Console from 'fp-ts/Console'\n *\n * // Will produce `Hello, fp-ts` to the stdout\n * const effect = pipe(\n *   T.of('fp-ts'),\n *   T.tapIO((value) => Console.log(`Hello, ${value}`)),\n * )\n *\n * async function test() {\n *   assert.deepStrictEqual(await effect(), 'fp-ts')\n * }\n *\n * test()\n *\n * @category combinators\n * @since 2.16.0\n */\nexport var tapIO = /*#__PURE__*/ dual(2, tapIO_(FromIO, Chain));\n/**\n * @category lifting\n * @since 2.4.0\n */\nexport var fromIOK = \n/*#__PURE__*/ fromIOK_(FromIO);\n/**\n * Alias of `flatMapIO`.\n *\n * @category legacy\n * @since 2.4.0\n */\nexport var chainIOK = flatMapIO;\n/**\n * Alias of `tapIO`.\n *\n * @category legacy\n * @since 2.10.0\n */\nexport var chainFirstIOK = tapIO;\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var FromTask = {\n    URI: URI,\n    fromIO: fromIO,\n    fromTask: fromTask\n};\n// -------------------------------------------------------------------------------------\n// utils\n// -------------------------------------------------------------------------------------\n/**\n * A `Task` that never completes.\n *\n * @since 2.0.0\n */\nexport var never = function () { return new Promise(function (_) { return undefined; }); };\n// -------------------------------------------------------------------------------------\n// do notation\n// -------------------------------------------------------------------------------------\n/**\n * @category do notation\n * @since 2.9.0\n */\nexport var Do = /*#__PURE__*/ of(_.emptyRecord);\n/**\n * @category do notation\n * @since 2.8.0\n */\nexport var bindTo = /*#__PURE__*/ bindTo_(Functor);\nvar let_ = /*#__PURE__*/ let__(Functor);\nexport { \n/**\n * @category do notation\n * @since 2.13.0\n */\nlet_ as let };\n/**\n * @category do notation\n * @since 2.8.0\n */\nexport var bind = /*#__PURE__*/ chainable.bind(Chain);\n/**\n * @category do notation\n * @since 2.8.0\n */\nexport var apS = /*#__PURE__*/ apS_(ApplyPar);\n/**\n * @since 2.11.0\n */\nexport var ApT = /*#__PURE__*/ of(_.emptyReadonlyArray);\n// -------------------------------------------------------------------------------------\n// array utils\n// -------------------------------------------------------------------------------------\n/**\n * Equivalent to `ReadonlyNonEmptyArray#traverseWithIndex(ApplicativePar)`.\n *\n * @category traversing\n * @since 2.11.0\n */\nexport var traverseReadonlyNonEmptyArrayWithIndex = function (f) {\n    return function (as) {\n        return function () {\n            return Promise.all(as.map(function (a, i) { return Promise.resolve().then(function () { return f(i, a)(); }); }));\n        };\n    };\n};\n/**\n * Equivalent to `ReadonlyArray#traverseWithIndex(ApplicativePar)`.\n *\n * @category traversing\n * @since 2.11.0\n */\nexport var traverseReadonlyArrayWithIndex = function (f) {\n    var g = traverseReadonlyNonEmptyArrayWithIndex(f);\n    return function (as) { return (_.isNonEmpty(as) ? g(as) : ApT); };\n};\n/**\n * Equivalent to `ReadonlyNonEmptyArray#traverseWithIndex(ApplicativeSeq)`.\n *\n * @category traversing\n * @since 2.11.0\n */\nexport var traverseReadonlyNonEmptyArrayWithIndexSeq = function (f) {\n    return function (as) {\n        return function () {\n            return _.tail(as).reduce(function (acc, a, i) {\n                return acc.then(function (bs) {\n                    return Promise.resolve()\n                        .then(f(i + 1, a))\n                        .then(function (b) {\n                        bs.push(b);\n                        return bs;\n                    });\n                });\n            }, Promise.resolve()\n                .then(f(0, _.head(as)))\n                .then(_.singleton));\n        };\n    };\n};\n/**\n * Equivalent to `ReadonlyArray#traverseWithIndex(ApplicativeSeq)`.\n *\n * @category traversing\n * @since 2.11.0\n */\nexport var traverseReadonlyArrayWithIndexSeq = function (f) {\n    var g = traverseReadonlyNonEmptyArrayWithIndexSeq(f);\n    return function (as) { return (_.isNonEmpty(as) ? g(as) : ApT); };\n};\n/**\n * Equivalent to `ReadonlyArray#traverseWithIndex(Applicative)`.\n *\n * @category traversing\n * @since 2.9.0\n */\nexport var traverseArrayWithIndex = traverseReadonlyArrayWithIndex;\n/**\n * Equivalent to `ReadonlyArray#traverse(Applicative)`.\n *\n * @category traversing\n * @since 2.9.0\n */\nexport var traverseArray = function (f) {\n    return traverseReadonlyArrayWithIndex(function (_, a) { return f(a); });\n};\n/**\n * Equivalent to `ReadonlyArray#sequence(Applicative)`.\n *\n * @category traversing\n * @since 2.9.0\n */\nexport var sequenceArray = \n/*#__PURE__*/ traverseArray(identity);\n/**\n * Equivalent to `ReadonlyArray#traverseWithIndex(ApplicativeSeq)`.\n *\n * @category traversing\n * @since 2.9.0\n */\nexport var traverseSeqArrayWithIndex = traverseReadonlyArrayWithIndexSeq;\n/**\n * Equivalent to `ReadonlyArray#traverse(ApplicativeSeq)`.\n *\n * @category traversing\n * @since 2.9.0\n */\nexport var traverseSeqArray = function (f) {\n    return traverseReadonlyArrayWithIndexSeq(function (_, a) { return f(a); });\n};\n/**\n * Equivalent to `ReadonlyArray#sequence(ApplicativeSeq)`.\n *\n * @category traversing\n * @since 2.9.0\n */\nexport var sequenceSeqArray = \n/*#__PURE__*/ traverseSeqArray(identity);\n// -------------------------------------------------------------------------------------\n// legacy\n// -------------------------------------------------------------------------------------\n/**\n * Alias of `flatMap`.\n *\n * @category legacy\n * @since 2.0.0\n */\nexport var chain = flatMap;\n/**\n * Alias of `tap`.\n *\n * @category legacy\n * @since 2.0.0\n */\nexport var chainFirst = tap;\n// -------------------------------------------------------------------------------------\n// deprecated\n// -------------------------------------------------------------------------------------\n/**\n * This instance is deprecated, use small, specific instances instead.\n * For example if a function needs a `Functor` instance, pass `T.Functor` instead of `T.task`\n * (where `T` is from `import T from 'fp-ts/Task'`)\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var task = {\n    URI: URI,\n    map: _map,\n    of: of,\n    ap: _apPar,\n    chain: flatMap,\n    fromIO: fromIO,\n    fromTask: fromTask\n};\n/**\n * This instance is deprecated, use small, specific instances instead.\n * For example if a function needs a `Functor` instance, pass `T.Functor` instead of `T.taskSeq`\n * (where `T` is from `import T from 'fp-ts/Task'`)\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var taskSeq = {\n    URI: URI,\n    map: _map,\n    of: of,\n    ap: _apSeq,\n    chain: flatMap,\n    fromIO: fromIO,\n    fromTask: fromTask\n};\n/**\n * Use [`getApplySemigroup`](./Apply.ts.html#getapplysemigroup) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var getSemigroup = /*#__PURE__*/ getApplySemigroup_(ApplySeq);\n/**\n * Use [`getApplicativeMonoid`](./Applicative.ts.html#getapplicativemonoid) instead.\n *\n * Lift a monoid into 'Task', the inner values are concatenated using the provided `Monoid`.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var getMonoid = /*#__PURE__*/ getApplicativeMonoid(ApplicativeSeq);\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACD;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAQO,IAAI,SAAS,SAAU,EAAE;IAAI,OAAO;QAAc,OAAO,QAAQ,OAAO,GAAG,IAAI,CAAC;IAAK;AAAG;AA8BxF,SAAS,MAAM,MAAM;IACxB,OAAO,SAAU,EAAE;QAAI,OAAO;YAC1B,OAAO,IAAI,QAAQ,SAAU,OAAO;gBAChC,WAAW;oBACP,QAAQ,OAAO,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC;gBACpC,GAAG;YACP;QACJ;IAAG;AACP;AACA,IAAI,OAAO,SAAU,EAAE,EAAE,CAAC;IAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,IAAI;AAAK;AACvD,IAAI,SAAS,SAAU,GAAG,EAAE,EAAE;IAAI,OAAO,IAAA,sJAAI,EAAC,KAAK,GAAG;AAAM;AAC5D,IAAI,SAAS,SAAU,GAAG,EAAE,EAAE;IAAI,OAAO,QAAQ,KAAK,SAAU,CAAC;QAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,IAAI;IAAK;AAAI;AAQ3F,IAAI,MAAM,SAAU,CAAC;IAAI,OAAO,SAAU,EAAE;QAAI,OAAO;YAC1D,OAAO,QAAQ,OAAO,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC;QAC3C;IAAG;AAAG;AAIC,IAAI,KAAK,SAAU,EAAE;IAAI,OAAO,SAAU,GAAG;QAAI,OAAO;YAC3D,OAAO,QAAQ,GAAG,CAAC;gBAAC,QAAQ,OAAO,GAAG,IAAI,CAAC;gBAAM,QAAQ,OAAO,GAAG,IAAI,CAAC;aAAI,EAAE,IAAI,CAAC,SAAU,EAAE;gBAC3F,IAAI,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;gBACxB,OAAO,EAAE;YACb;QACJ;IAAG;AAAG;AAKC,IAAI,KAAK,SAAU,CAAC;IAAI,OAAO;QAAc,OAAO,QAAQ,OAAO,CAAC;IAAI;AAAG;AAK3E,IAAI,UAAU,WAAW,GAAG,IAAA,sJAAI,EAAC,GAAG,SAAU,EAAE,EAAE,CAAC;IACtD,OAAO;QACH,OAAO,QAAQ,OAAO,GACjB,IAAI,CAAC,IACL,IAAI,CAAC,SAAU,CAAC;YAAI,OAAO,EAAE;QAAM;IAC5C;AACJ;AAKO,IAAI,UAAU,WAAW,GAAG,QAAQ,0JAAQ;AAK5C,IAAI,MAAM;AAqBV,SAAS;IACZ,OAAO;QACH,QAAQ,SAAU,CAAC,EAAE,CAAC;YAAI,OAAO;gBAAc,OAAO,QAAQ,IAAI,CAAC;oBAAC,QAAQ,OAAO,GAAG,IAAI,CAAC;oBAAI,QAAQ,OAAO,GAAG,IAAI,CAAC;iBAAG;YAAG;QAAG;QAC/H,OAAO;IACX;AACJ;AAKO,IAAI,UAAU;IACjB,KAAK;IACL,KAAK;AACT;AAOO,IAAI,KAAK,IAAA,sJAAI,EAAC,GAAG,IAAA,mJAAG,EAAC;AAOrB,IAAI,SAAS,IAAA,uJAAO,EAAC;AAKrB,IAAI,OAAO,WAAW,GAAG,IAAA,qJAAK,EAAC;AAK/B,IAAI,UAAU;IACjB,KAAK;IACL,IAAI;AACR;AAOO,IAAI,WAAW;IAClB,KAAK;IACL,KAAK;IACL,IAAI;AACR;AAMO,IAAI,UAAU,WAAW,GAAG,IAAA,sJAAQ,EAAC;AAMrC,IAAI,WAAW,WAAW,GAAG,IAAA,uJAAS,EAAC;AAOvC,IAAI,iBAAiB;IACxB,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;AACR;AAOO,IAAI,WAAW;IAClB,KAAK;IACL,KAAK;IACL,IAAI;AACR;AAOO,IAAI,iBAAiB;IACxB,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;AACR;AAKO,IAAI,QAAQ;IACf,KAAK;IACL,KAAK;IACL,IAAI;IACJ,OAAO;AACX;AAKO,IAAI,QAAQ;IACf,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,OAAO;AACX;AAKO,IAAI,UAAU;IACjB,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,QAAQ;AACZ;AAMO,IAAI,WAAW,0JAAQ;AAKvB,IAAI,YAAY;IACnB,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,QAAQ;IACR,UAAU;AACd;AAKO,IAAI,SAAS;IAChB,KAAK;IACL,QAAQ;AACZ;AACA,cAAc,GACd,IAAI,WAAW;IACX,SAAS;AACb;AACA,cAAc,GACd,IAAI,UAAU;IACV,QAAQ,OAAO,MAAM;AACzB;AAKO,IAAI,YAAY,2JAAW,CAAC,SAAS;AAQrC,IAAI,MAAM,WAAW,GAAG,IAAA,sJAAI,EAAC,GAAG,kJAAa,CAAC;AAyB9C,IAAI,QAAQ,WAAW,GAAG,IAAA,sJAAI,EAAC,GAAG,IAAA,qJAAM,EAAC,QAAQ;AAKjD,IAAI,UACX,WAAW,GAAG,IAAA,uJAAQ,EAAC;AAOhB,IAAI,WAAW;AAOf,IAAI,gBAAgB;AAKpB,IAAI,WAAW;IAClB,KAAK;IACL,QAAQ;IACR,UAAU;AACd;AASO,IAAI,QAAQ;IAAc,OAAO,IAAI,QAAQ,SAAU,CAAC;QAAI,OAAO;IAAW;AAAI;AAQlF,IAAI,KAAK,WAAW,GAAG,GAAG,6JAAa;AAKvC,IAAI,SAAS,WAAW,GAAG,IAAA,uJAAO,EAAC;AAC1C,IAAI,OAAO,WAAW,GAAG,IAAA,oJAAK,EAAC;;AAWxB,IAAI,OAAO,WAAW,GAAG,mJAAc,CAAC;AAKxC,IAAI,MAAM,WAAW,GAAG,IAAA,kJAAI,EAAC;AAI7B,IAAI,MAAM,WAAW,GAAG,GAAG,oKAAoB;AAU/C,IAAI,yCAAyC,SAAU,CAAC;IAC3D,OAAO,SAAU,EAAE;QACf,OAAO;YACH,OAAO,QAAQ,GAAG,CAAC,GAAG,GAAG,CAAC,SAAU,CAAC,EAAE,CAAC;gBAAI,OAAO,QAAQ,OAAO,GAAG,IAAI,CAAC;oBAAc,OAAO,EAAE,GAAG;gBAAM;YAAI;QAClH;IACJ;AACJ;AAOO,IAAI,iCAAiC,SAAU,CAAC;IACnD,IAAI,IAAI,uCAAuC;IAC/C,OAAO,SAAU,EAAE;QAAI,OAAQ,4JAAY,CAAC,MAAM,EAAE,MAAM;IAAM;AACpE;AAOO,IAAI,4CAA4C,SAAU,CAAC;IAC9D,OAAO,SAAU,EAAE;QACf,OAAO;YACH,OAAO,sJAAM,CAAC,IAAI,MAAM,CAAC,SAAU,GAAG,EAAE,CAAC,EAAE,CAAC;gBACxC,OAAO,IAAI,IAAI,CAAC,SAAU,EAAE;oBACxB,OAAO,QAAQ,OAAO,GACjB,IAAI,CAAC,EAAE,IAAI,GAAG,IACd,IAAI,CAAC,SAAU,CAAC;wBACjB,GAAG,IAAI,CAAC;wBACR,OAAO;oBACX;gBACJ;YACJ,GAAG,QAAQ,OAAO,GACb,IAAI,CAAC,EAAE,GAAG,sJAAM,CAAC,MACjB,IAAI,CAAC,2JAAW;QACzB;IACJ;AACJ;AAOO,IAAI,oCAAoC,SAAU,CAAC;IACtD,IAAI,IAAI,0CAA0C;IAClD,OAAO,SAAU,EAAE;QAAI,OAAQ,4JAAY,CAAC,MAAM,EAAE,MAAM;IAAM;AACpE;AAOO,IAAI,yBAAyB;AAO7B,IAAI,gBAAgB,SAAU,CAAC;IAClC,OAAO,+BAA+B,SAAU,CAAC,EAAE,CAAC;QAAI,OAAO,EAAE;IAAI;AACzE;AAOO,IAAI,gBACX,WAAW,GAAG,cAAc,0JAAQ;AAO7B,IAAI,4BAA4B;AAOhC,IAAI,mBAAmB,SAAU,CAAC;IACrC,OAAO,kCAAkC,SAAU,CAAC,EAAE,CAAC;QAAI,OAAO,EAAE;IAAI;AAC5E;AAOO,IAAI,mBACX,WAAW,GAAG,iBAAiB,0JAAQ;AAUhC,IAAI,QAAQ;AAOZ,IAAI,aAAa;AAajB,IAAI,OAAO;IACd,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,QAAQ;IACR,UAAU;AACd;AAUO,IAAI,UAAU;IACjB,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,QAAQ;IACR,UAAU;AACd;AAQO,IAAI,eAAe,WAAW,GAAG,IAAA,gKAAkB,EAAC;AAUpD,IAAI,YAAY,WAAW,GAAG,IAAA,yKAAoB,EAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4333, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/fp-ts/es6/TaskEither.js"], "sourcesContent": ["var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n    return new (P || (P = Promise))(function (resolve, reject) {\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\n    });\n};\nvar __generator = (this && this.__generator) || function (thisArg, body) {\n    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;\n    return g = { next: verb(0), \"throw\": verb(1), \"return\": verb(2) }, typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n    function verb(n) { return function (v) { return step([n, v]); }; }\n    function step(op) {\n        if (f) throw new TypeError(\"Generator is already executing.\");\n        while (g && (g = 0, op[0] && (_ = 0)), _) try {\n            if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n            if (y = 0, t) op = [op[0] & 2, t.value];\n            switch (op[0]) {\n                case 0: case 1: t = op; break;\n                case 4: _.label++; return { value: op[1], done: false };\n                case 5: _.label++; y = op[1]; op = [0]; continue;\n                case 7: op = _.ops.pop(); _.trys.pop(); continue;\n                default:\n                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                    if (t[2]) _.ops.pop();\n                    _.trys.pop(); continue;\n            }\n            op = body.call(thisArg, _);\n        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n    }\n};\nimport { getApplicativeMonoid } from './Applicative';\nimport { ap as ap_, apFirst as apFirst_, apS as apS_, apSecond as apSecond_, getApplySemigroup as getApplySemigroup_ } from './Apply';\nimport * as chainable from './Chain';\nimport { compact as compact_, separate as separate_ } from './Compactable';\nimport * as E from './Either';\nimport * as ET from './EitherT';\nimport { filter as filter_, filterMap as filterMap_, partition as partition_, partitionMap as partitionMap_ } from './Filterable';\nimport { chainOptionK as chainOptionK_, filterOrElse as filterOrElse_, fromEitherK as fromEitherK_, fromOption as fromOption_, fromOptionK as fromOptionK_, fromPredicate as fromPredicate_, tapEither as tapEither_ } from './FromEither';\nimport { fromIOK as fromIOK_, tapIO as tapIO_ } from './FromIO';\nimport { fromTaskK as fromTaskK_, tapTask as tapTask_ } from './FromTask';\nimport { dual, flow, identity, pipe, SK } from './function';\nimport { as as as_, asUnit as asUnit_, bindTo as bindTo_, flap as flap_, let as let__ } from './Functor';\nimport * as _ from './internal';\nimport * as T from './Task';\n// -------------------------------------------------------------------------------------\n// constructors\n// -------------------------------------------------------------------------------------\n/**\n * @category constructors\n * @since 2.0.0\n */\nexport var left = /*#__PURE__*/ ET.left(T.Pointed);\n/**\n * @category constructors\n * @since 2.0.0\n */\nexport var right = /*#__PURE__*/ ET.right(T.Pointed);\n/**\n * @category constructors\n * @since 2.0.0\n */\nexport var rightTask = /*#__PURE__*/ ET.rightF(T.Functor);\n/**\n * @category constructors\n * @since 2.0.0\n */\nexport var leftTask = /*#__PURE__*/ ET.leftF(T.Functor);\n/**\n * @category constructors\n * @since 2.0.0\n */\nexport var rightIO = /*#__PURE__*/ flow(T.fromIO, rightTask);\n/**\n * @category constructors\n * @since 2.0.0\n */\nexport var leftIO = /*#__PURE__*/ flow(T.fromIO, leftTask);\n// -------------------------------------------------------------------------------------\n// conversions\n// -------------------------------------------------------------------------------------\n/**\n * @category conversions\n * @since 2.7.0\n */\nexport var fromIO = rightIO;\n/**\n * @category conversions\n * @since 2.7.0\n */\nexport var fromTask = rightTask;\n/**\n * @category conversions\n * @since 2.0.0\n */\nexport var fromEither = T.of;\n/**\n * @category conversions\n * @since 2.0.0\n */\nexport var fromIOEither = T.fromIO;\n/**\n * @category conversions\n * @since 2.11.0\n */\nexport var fromTaskOption = function (onNone) {\n    return T.map(E.fromOption(onNone));\n};\n/**\n * @category pattern matching\n * @since 2.10.0\n */\nexport var match = \n/*#__PURE__*/ ET.match(T.Functor);\n/**\n * Less strict version of [`match`](#match).\n *\n * The `W` suffix (short for **W**idening) means that the handler return types will be merged.\n *\n * @category pattern matching\n * @since 2.10.0\n */\nexport var matchW = match;\n/**\n * The `E` suffix (short for **E**ffect) means that the handlers return an effect (`Task`).\n *\n * @category pattern matching\n * @since 2.10.0\n */\nexport var matchE = /*#__PURE__*/ ET.matchE(T.Monad);\n/**\n * Alias of [`matchE`](#matche).\n *\n * @category pattern matching\n * @since 2.0.0\n */\nexport var fold = matchE;\n/**\n * Less strict version of [`matchE`](#matche).\n *\n * The `W` suffix (short for **W**idening) means that the handler return types will be merged.\n *\n * @category pattern matching\n * @since 2.10.0\n */\nexport var matchEW = matchE;\n/**\n * Alias of [`matchEW`](#matchew).\n *\n * @category pattern matching\n * @since 2.10.0\n */\nexport var foldW = matchEW;\n/**\n * @category error handling\n * @since 2.0.0\n */\nexport var getOrElse = \n/*#__PURE__*/ ET.getOrElse(T.Monad);\n/**\n * Less strict version of [`getOrElse`](#getorelse).\n *\n * The `W` suffix (short for **W**idening) means that the handler return type will be merged.\n *\n * @category error handling\n * @since 2.6.0\n */\nexport var getOrElseW = getOrElse;\n/**\n * Transforms a `Promise` that may reject to a `Promise` that never rejects and returns an `Either` instead.\n *\n * See also [`tryCatchK`](#trycatchk).\n *\n * @example\n * import { left, right } from 'fp-ts/Either'\n * import { tryCatch } from 'fp-ts/TaskEither'\n *\n * tryCatch(() => Promise.resolve(1), String)().then(result => {\n *   assert.deepStrictEqual(result, right(1))\n * })\n * tryCatch(() => Promise.reject('error'), String)().then(result => {\n *   assert.deepStrictEqual(result, left('error'))\n * })\n *\n * @category interop\n * @since 2.0.0\n */\nexport var tryCatch = function (f, onRejected) {\n    return function () { return __awaiter(void 0, void 0, void 0, function () {\n        var reason_1;\n        return __generator(this, function (_a) {\n            switch (_a.label) {\n                case 0:\n                    _a.trys.push([0, 2, , 3]);\n                    return [4 /*yield*/, f().then(_.right)];\n                case 1: return [2 /*return*/, _a.sent()];\n                case 2:\n                    reason_1 = _a.sent();\n                    return [2 /*return*/, _.left(onRejected(reason_1))];\n                case 3: return [2 /*return*/];\n            }\n        });\n    }); };\n};\n/**\n * Converts a function returning a `Promise` to one returning a `TaskEither`.\n *\n * @category interop\n * @since 2.5.0\n */\nexport var tryCatchK = function (f, onRejected) {\n    return function () {\n        var a = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            a[_i] = arguments[_i];\n        }\n        return tryCatch(function () { return f.apply(void 0, a); }, onRejected);\n    };\n};\n/**\n * @category conversions\n * @since 2.10.0\n */\nexport var toUnion = /*#__PURE__*/ ET.toUnion(T.Functor);\n/**\n * @category conversions\n * @since 2.12.0\n */\nexport var fromNullable = /*#__PURE__*/ ET.fromNullable(T.Pointed);\n/**\n * Use `liftNullable`.\n *\n * @category legacy\n * @since 2.12.0\n */\nexport var fromNullableK = /*#__PURE__*/ ET.fromNullableK(T.Pointed);\n/**\n * Use `flatMapNullable`.\n *\n * @category legacy\n * @since 2.12.0\n */\nexport var chainNullableK = \n/*#__PURE__*/ ET.chainNullableK(T.Monad);\n// -------------------------------------------------------------------------------------\n// combinators\n// -------------------------------------------------------------------------------------\n/**\n * Returns `ma` if is a `Right` or the value returned by `onLeft` otherwise.\n *\n * See also [alt](#alt).\n *\n * @example\n * import * as E from 'fp-ts/Either'\n * import { pipe } from 'fp-ts/function'\n * import * as TE from 'fp-ts/TaskEither'\n *\n * async function test() {\n *   const errorHandler = TE.orElse((error: string) => TE.right(`recovering from ${error}...`))\n *   assert.deepStrictEqual(await pipe(TE.right('ok'), errorHandler)(), E.right('ok'))\n *   assert.deepStrictEqual(await pipe(TE.left('ko'), errorHandler)(), E.right('recovering from ko...'))\n * }\n *\n * test()\n *\n * @category error handling\n * @since 2.0.0\n */\nexport var orElse = \n/*#__PURE__*/ ET.orElse(T.Monad);\n/**\n * Less strict version of [`orElse`](#orelse).\n *\n * The `W` suffix (short for **W**idening) means that the return types will be merged.\n *\n * @category error handling\n * @since 2.10.0\n */\nexport var orElseW = orElse;\n/**\n * Returns an effect that effectfully \"peeks\" at the failure of this effect.\n *\n * @category error handling\n * @since 2.15.0\n */\nexport var tapError = /*#__PURE__*/ dual(2, ET.tapError(T.Monad));\n/**\n * @category error handling\n * @since 2.12.0\n */\nexport var orElseFirstIOK = function (onLeft) { return tapError(fromIOK(onLeft)); };\n/**\n * @category error handling\n * @since 2.12.0\n */\nexport var orElseFirstTaskK = function (onLeft) { return tapError(fromTaskK(onLeft)); };\n/**\n * @category error handling\n * @since 2.11.0\n */\nexport var orLeft = \n/*#__PURE__*/ ET.orLeft(T.Monad);\n/**\n * @since 2.0.0\n */\nexport var swap = /*#__PURE__*/ ET.swap(T.Functor);\n/**\n * @category lifting\n * @since 2.11.0\n */\nexport var fromTaskOptionK = function (onNone) {\n    var from = fromTaskOption(onNone);\n    return function (f) { return flow(f, from); };\n};\n/**\n * Use `flatMapTaskOption`.\n *\n * The `W` suffix (short for **W**idening) means that the error types will be merged.\n *\n * @category legacy\n * @since 2.12.3\n */\nexport var chainTaskOptionKW = function (onNone) {\n    return function (f) {\n        return function (ma) {\n            return flatMap(ma, fromTaskOptionK(onNone)(f));\n        };\n    };\n};\n/**\n * Use `flatMapTaskOption`.\n *\n * @category legacy\n * @since 2.11.0\n */\nexport var chainTaskOptionK = chainTaskOptionKW;\n/**\n * @category lifting\n * @since 2.4.0\n */\nexport var fromIOEitherK = function (f) { return flow(f, fromIOEither); };\nvar _map = function (fa, f) { return pipe(fa, map(f)); };\nvar _apPar = function (fab, fa) { return pipe(fab, ap(fa)); };\nvar _apSeq = function (fab, fa) { return flatMap(fab, function (f) { return pipe(fa, map(f)); }); };\n/* istanbul ignore next */\nvar _alt = function (fa, that) { return pipe(fa, alt(that)); };\n/**\n * `map` can be used to turn functions `(a: A) => B` into functions `(fa: F<A>) => F<B>` whose argument and return types\n * use the type constructor `F` to represent some computational context.\n *\n * @category mapping\n * @since 2.0.0\n */\nexport var map = /*#__PURE__*/ ET.map(T.Functor);\n/**\n * Returns a `TaskEither` whose failure and success channels have been mapped by the specified pair of functions, `f` and `g`.\n *\n * @example\n * import * as TaskEither from 'fp-ts/TaskEither'\n * import * as Either from 'fp-ts/Either'\n *\n * const f = (s: string) => new Error(s)\n * const g = (n: number) => n * 2\n *\n * async function test() {\n *   assert.deepStrictEqual(await TaskEither.mapBoth(TaskEither.right(1), f, g)(), Either.right(2))\n *   assert.deepStrictEqual(await TaskEither.mapBoth(TaskEither.left('err'), f, g)(), Either.left(new Error('err')))\n * }\n *\n * test()\n *\n * @category error handling\n * @since 2.16.0\n */\nexport var mapBoth = /*#__PURE__*/ dual(3, ET.mapBoth(T.Functor));\n/**\n * Alias of `mapBoth`.\n *\n * @category legacy\n * @since 2.0.0\n */\nexport var bimap = mapBoth;\n/**\n * Returns a `TaskEither` with its error channel mapped using the specified function.\n *\n * @example\n * import * as TaskEither from 'fp-ts/TaskEither'\n * import * as Either from 'fp-ts/Either'\n *\n * const f = (s: string) => new Error(s)\n *\n * async function test() {\n *   assert.deepStrictEqual(await TaskEither.mapError(TaskEither.right(1), f)(), Either.right(1))\n *   assert.deepStrictEqual(await TaskEither.mapError(TaskEither.left('err'), f)(), Either.left(new Error('err')))\n * }\n *\n * test()\n *\n * @category error handling\n * @since 2.16.0\n */\nexport var mapError = /*#__PURE__*/ dual(2, ET.mapError(T.Functor));\n/**\n * Alias of `mapError`.\n *\n * @category legacy\n * @since 2.0.0\n */\nexport var mapLeft = mapError;\n/**\n * @since 2.0.0\n */\nexport var ap = \n/*#__PURE__*/ ET.ap(T.ApplyPar);\n/**\n * Less strict version of [`ap`](#ap).\n *\n * The `W` suffix (short for **W**idening) means that the error types will be merged.\n *\n * @since 2.8.0\n */\nexport var apW = ap;\n/**\n * @category sequencing\n * @since 2.14.0\n */\nexport var flatMap = /*#__PURE__*/ dual(2, ET.flatMap(T.Monad));\n/**\n * Less strict version of [`flatten`](#flatten).\n *\n * The `W` suffix (short for **W**idening) means that the error types will be merged.\n *\n * @category sequencing\n * @since 2.11.0\n */\nexport var flattenW = \n/*#__PURE__*/ flatMap(identity);\n/**\n * @category sequencing\n * @since 2.0.0\n */\nexport var flatten = flattenW;\n/**\n * Identifies an associative operation on a type constructor. It is similar to `Semigroup`, except that it applies to\n * types of kind `* -> *`.\n *\n * In case of `TaskEither` returns `fa` if is a `Right` or the value returned by `that` otherwise.\n *\n * See also [orElse](#orelse).\n *\n * @example\n * import * as E from 'fp-ts/Either'\n * import { pipe } from 'fp-ts/function'\n * import * as TE from 'fp-ts/TaskEither'\n *\n * async function test() {\n *   assert.deepStrictEqual(\n *     await pipe(\n *       TE.right(1),\n *       TE.alt(() => TE.right(2))\n *     )(),\n *     E.right(1)\n *   )\n *   assert.deepStrictEqual(\n *     await pipe(\n *       TE.left('a'),\n *       TE.alt(() => TE.right(2))\n *     )(),\n *     E.right(2)\n *   )\n *   assert.deepStrictEqual(\n *     await pipe(\n *       TE.left('a'),\n *       TE.alt(() => TE.left('b'))\n *     )(),\n *     E.left('b')\n *   )\n * }\n *\n * test()\n *\n * @category error handling\n * @since 2.0.0\n */\nexport var alt = \n/*#__PURE__*/ ET.alt(T.Monad);\n/**\n * Less strict version of [`alt`](#alt).\n *\n * The `W` suffix (short for **W**idening) means that the error and the return types will be merged.\n *\n * @category error handling\n * @since 2.9.0\n */\nexport var altW = alt;\n/**\n * @category constructors\n * @since 2.0.0\n */\nexport var of = right;\n/**\n * @since 2.7.0\n */\nexport var throwError = left;\n/**\n * @category type lambdas\n * @since 2.0.0\n */\nexport var URI = 'TaskEither';\n/**\n * The default [`ApplicativePar`](#applicativepar) instance returns the first error, if you want to\n * get all errors you need to provide a way to concatenate them via a `Semigroup`.\n *\n * @example\n * import * as E from 'fp-ts/Either'\n * import { pipe } from 'fp-ts/function'\n * import * as RA from 'fp-ts/ReadonlyArray'\n * import * as S from 'fp-ts/Semigroup'\n * import * as string from 'fp-ts/string'\n * import * as T from 'fp-ts/Task'\n * import * as TE from 'fp-ts/TaskEither'\n *\n * interface User {\n *   readonly id: string\n *   readonly name: string\n * }\n *\n * const remoteDatabase: ReadonlyArray<User> = [\n *   { id: 'id1', name: 'John' },\n *   { id: 'id2', name: 'Mary' },\n *   { id: 'id3', name: 'Joey' }\n * ]\n *\n * const fetchUser = (id: string): TE.TaskEither<string, User> =>\n *   pipe(\n *     remoteDatabase,\n *     RA.findFirst((user) => user.id === id),\n *     TE.fromOption(() => `${id} not found`)\n *   )\n *\n * async function test() {\n *   assert.deepStrictEqual(\n *     await pipe(['id4', 'id5'], RA.traverse(TE.ApplicativePar)(fetchUser))(),\n *     E.left('id4 not found') // <= first error\n *   )\n *\n *   const Applicative = TE.getApplicativeTaskValidation(\n *     T.ApplyPar,\n *     pipe(string.Semigroup, S.intercalate(', '))\n *   )\n *\n *   assert.deepStrictEqual(\n *     await pipe(['id4', 'id5'], RA.traverse(Applicative)(fetchUser))(),\n *     E.left('id4 not found, id5 not found') // <= all errors\n *   )\n * }\n *\n * test()\n *\n * @category error handling\n * @since 2.7.0\n */\nexport function getApplicativeTaskValidation(A, S) {\n    var ap = ap_(A, E.getApplicativeValidation(S));\n    return {\n        URI: URI,\n        _E: undefined,\n        map: _map,\n        ap: function (fab, fa) { return pipe(fab, ap(fa)); },\n        of: of\n    };\n}\n/**\n * The default [`Alt`](#alt) instance returns the last error, if you want to\n * get all errors you need to provide a way to concatenate them via a `Semigroup`.\n *\n * See [`getAltValidation`](./Either.ts.html#getaltvalidation).\n *\n * @category error handling\n * @since 2.7.0\n */\nexport function getAltTaskValidation(S) {\n    var alt = ET.altValidation(T.Monad, S);\n    return {\n        URI: URI,\n        _E: undefined,\n        map: _map,\n        alt: function (fa, that) { return pipe(fa, alt(that)); }\n    };\n}\n/**\n * @category filtering\n * @since 2.10.0\n */\nexport var getCompactable = function (M) {\n    var C = E.getCompactable(M);\n    return {\n        URI: URI,\n        _E: undefined,\n        compact: compact_(T.Functor, C),\n        separate: separate_(T.Functor, C, E.Functor)\n    };\n};\n/**\n * @category filtering\n * @since 2.1.0\n */\nexport function getFilterable(M) {\n    var F = E.getFilterable(M);\n    var C = getCompactable(M);\n    var filter = filter_(T.Functor, F);\n    var filterMap = filterMap_(T.Functor, F);\n    var partition = partition_(T.Functor, F);\n    var partitionMap = partitionMap_(T.Functor, F);\n    return {\n        URI: URI,\n        _E: undefined,\n        map: _map,\n        compact: C.compact,\n        separate: C.separate,\n        filter: function (fa, predicate) { return pipe(fa, filter(predicate)); },\n        filterMap: function (fa, f) { return pipe(fa, filterMap(f)); },\n        partition: function (fa, predicate) { return pipe(fa, partition(predicate)); },\n        partitionMap: function (fa, f) { return pipe(fa, partitionMap(f)); }\n    };\n}\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var Functor = {\n    URI: URI,\n    map: _map\n};\n/**\n * Maps the `Right` value of this `TaskEither` to the specified constant value.\n *\n * @category mapping\n * @since 2.16.0\n */\nexport var as = dual(2, as_(Functor));\n/**\n * Maps the `Right` value of this `TaskEither` to the void constant value.\n *\n * @category mapping\n * @since 2.16.0\n */\nexport var asUnit = asUnit_(Functor);\n/**\n * @category mapping\n * @since 2.10.0\n */\nexport var flap = /*#__PURE__*/ flap_(Functor);\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var Pointed = {\n    URI: URI,\n    of: of\n};\n/**\n * Runs computations in parallel.\n *\n * @category instances\n * @since 2.10.0\n */\nexport var ApplyPar = {\n    URI: URI,\n    map: _map,\n    ap: _apPar\n};\n/**\n * Combine two effectful actions, keeping only the result of the first.\n *\n * @since 2.0.0\n */\nexport var apFirst = /*#__PURE__*/ apFirst_(ApplyPar);\n/**\n * Less strict version of [`apFirst`](#apfirst).\n *\n * The `W` suffix (short for **W**idening) means that the error types will be merged.\n *\n * @since 2.12.0\n */\nexport var apFirstW = apFirst;\n/**\n * Combine two effectful actions, keeping only the result of the second.\n *\n * @since 2.0.0\n */\nexport var apSecond = /*#__PURE__*/ apSecond_(ApplyPar);\n/**\n * Less strict version of [`apSecond`](#apsecond).\n *\n * The `W` suffix (short for **W**idening) means that the error types will be merged.\n *\n * @since 2.12.0\n */\nexport var apSecondW = apSecond;\n/**\n * Runs computations in parallel.\n *\n * @category instances\n * @since 2.7.0\n */\nexport var ApplicativePar = {\n    URI: URI,\n    map: _map,\n    ap: _apPar,\n    of: of\n};\n/**\n * Runs computations sequentially.\n *\n * @category instances\n * @since 2.10.0\n */\nexport var ApplySeq = {\n    URI: URI,\n    map: _map,\n    ap: _apSeq\n};\n/**\n * Runs computations sequentially.\n *\n * @category instances\n * @since 2.7.0\n */\nexport var ApplicativeSeq = {\n    URI: URI,\n    map: _map,\n    ap: _apSeq,\n    of: of\n};\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var Chain = {\n    URI: URI,\n    map: _map,\n    ap: _apPar,\n    chain: flatMap\n};\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var Monad = {\n    URI: URI,\n    map: _map,\n    ap: _apPar,\n    chain: flatMap,\n    of: of\n};\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var MonadIO = {\n    URI: URI,\n    map: _map,\n    ap: _apPar,\n    chain: flatMap,\n    of: of,\n    fromIO: fromIO\n};\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var MonadTask = {\n    URI: URI,\n    map: _map,\n    ap: _apPar,\n    chain: flatMap,\n    of: of,\n    fromIO: fromIO,\n    fromTask: fromTask\n};\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var MonadThrow = {\n    URI: URI,\n    map: _map,\n    ap: _apPar,\n    chain: flatMap,\n    of: of,\n    throwError: throwError\n};\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var FromEither = {\n    URI: URI,\n    fromEither: fromEither\n};\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var FromIO = {\n    URI: URI,\n    fromIO: fromIO\n};\n/**\n * @category instances\n * @since 2.10.0\n */\nexport var FromTask = {\n    URI: URI,\n    fromIO: fromIO,\n    fromTask: fromTask\n};\n/**\n * Composes computations in sequence, using the return value of one computation to determine the next computation and\n * keeping only the result of the first.\n *\n * @category combinators\n * @since 2.15.0\n */\nexport var tap = /*#__PURE__*/ dual(2, chainable.tap(Chain));\n/**\n * Composes computations in sequence, using the return value of one computation to determine the next computation and\n * keeping only the result of the first.\n *\n * @example\n * import * as E from 'fp-ts/Either'\n * import { pipe } from 'fp-ts/function'\n * import * as TE from 'fp-ts/TaskEither'\n *\n * const checkString = (value: string) => pipe(\n *   TE.of(value),\n *   TE.tapEither(() => value.length > 0 ? E.right('ok') : E.left('error'))\n * )\n *\n * async function test() {\n *   assert.deepStrictEqual(await checkString('')(), E.left('error'))\n *   assert.deepStrictEqual(await checkString('fp-ts')(), E.right('fp-ts'))\n * }\n *\n * test()\n *\n * @category combinators\n * @since 2.16.0\n */\nexport var tapEither = /*#__PURE__*/ dual(2, tapEither_(FromEither, Chain));\n/**\n * Composes computations in sequence, using the return value of one computation to determine the next computation and\n * keeping only the result of the first.\n *\n * @example\n * import { pipe } from 'fp-ts/function'\n * import * as TE from 'fp-ts/TaskEither'\n * import * as E from 'fp-ts/Either'\n * import * as Console from 'fp-ts/Console'\n *\n *\n * // Will produce `Hello, fp-ts` to the stdout\n * const effectA = TE.tapIO(\n *   TE.of(1),\n *   (value) => Console.log(`Hello, ${value}`)\n * )\n *\n * // No output to the stdout\n * const effectB = pipe(\n *   TE.left('error'),\n *   TE.tapIO((value) => Console.log(`Hello, ${value}`))\n * )\n *\n * async function test() {\n *   assert.deepStrictEqual(await effectA(), E.of(1))\n *   assert.deepStrictEqual(await effectB(), E.left('error'))\n * }\n *\n * test()\n *\n * @category combinators\n * @since 2.16.0\n */\nexport var tapIO = /*#__PURE__*/ dual(2, tapIO_(FromIO, Chain));\n/**\n * Composes computations in sequence, using the return value of one computation to determine the next computation and\n * keeping only the result of the first.\n *\n * @example\n * import * as TE from 'fp-ts/TaskEither'\n * import * as T from 'fp-ts/Task'\n * import * as E from 'fp-ts/Either'\n *\n *\n * const effect = TE.tapIO(\n *   TE.of(1),\n *   (value) => T.of(value + 1)\n * )\n *\n * async function test() {\n *   assert.deepStrictEqual(await effect(), E.of(1))\n * }\n *\n * test()\n *\n * @category combinators\n * @since 2.16.0\n */\nexport var tapTask = /*#__PURE__*/ dual(2, tapTask_(FromTask, Chain));\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var Bifunctor = {\n    URI: URI,\n    bimap: mapBoth,\n    mapLeft: mapError\n};\n/**\n * @category instances\n * @since 2.7.0\n */\nexport var Alt = {\n    URI: URI,\n    map: _map,\n    alt: _alt\n};\n/**\n * @category conversions\n * @since 2.0.0\n */\nexport var fromOption = \n/*#__PURE__*/ fromOption_(FromEither);\n/**\n * Use `liftOption`.\n *\n * @category legacy\n * @since 2.10.0\n */\nexport var fromOptionK = \n/*#__PURE__*/ fromOptionK_(FromEither);\n/**\n * Use `flatMapOption`.\n *\n * @category legacy\n * @since 2.10.0\n */\nexport var chainOptionK = /*#__PURE__*/ chainOptionK_(FromEither, Chain);\n/**\n * Use `flatMapOption`.\n *\n * @category legacy\n * @since 2.13.2\n */\nexport var chainOptionKW = chainOptionK;\n/** @internal */\nvar _FromEither = {\n    fromEither: FromEither.fromEither\n};\n/**\n * @category lifting\n * @since 2.15.0\n */\nexport var liftNullable = /*#__PURE__*/ _.liftNullable(_FromEither);\n/**\n * @category lifting\n * @since 2.15.0\n */\nexport var liftOption = /*#__PURE__*/ _.liftOption(_FromEither);\n/** @internal */\nvar _FlatMap = {\n    flatMap: flatMap\n};\n/** @internal */\nvar _FromIO = {\n    fromIO: FromIO.fromIO\n};\n/** @internal */\nvar _FromTask = {\n    fromTask: fromTask\n};\n/**\n * @category sequencing\n * @since 2.15.0\n */\nexport var flatMapNullable = /*#__PURE__*/ _.flatMapNullable(_FromEither, _FlatMap);\n/**\n * @category sequencing\n * @since 2.15.0\n */\nexport var flatMapOption = /*#__PURE__*/ _.flatMapOption(_FromEither, _FlatMap);\n/**\n * @category sequencing\n * @since 2.15.0\n */\nexport var flatMapEither = /*#__PURE__*/ _.flatMapEither(_FromEither, _FlatMap);\n/**\n * @category sequencing\n * @since 2.15.0\n */\nexport var flatMapIO = /*#__PURE__*/ _.flatMapIO(_FromIO, _FlatMap);\n/**\n * @category sequencing\n * @since 2.16.0\n */\nexport var flatMapTask = /*#__PURE__*/ _.flatMapTask(_FromTask, _FlatMap);\n/**\n * @category sequencing\n * @since 2.16.0\n */\nexport var flatMapIOEither = /*#__PURE__*/ dual(2, function (self, f) {\n    return flatMap(self, fromIOEitherK(f));\n});\n/**\n * @category sequencing\n * @since 2.16.0\n */\nexport var flatMapTaskOption = /*#__PURE__*/ dual(3, function (self, f, onNone) {\n    return flatMap(self, function (a) { return fromTaskOption(function () { return onNone(a); })(f(a)); });\n});\n/**\n * Alias of `flatMapEither`.\n *\n * @category legacy\n * @since 2.4.0\n */\nexport var chainEitherK = flatMapEither;\n/**\n * Alias of `flatMapEither`.\n *\n * @category legacy\n * @since 2.6.1\n */\nexport var chainEitherKW = flatMapEither;\n/**\n * Alias of `tapEither`.\n *\n * @category legacy\n * @since 2.12.0\n */\nexport var chainFirstEitherK = tapEither;\n/**\n * Alias of `tapEither`.\n *\n * Less strict version of [`chainFirstEitherK`](#chainfirsteitherk).\n *\n * The `W` suffix (short for **W**idening) means that the error types will be merged.\n *\n * @category legacy\n * @since 2.12.0\n */\nexport var chainFirstEitherKW = tapEither;\n/**\n * @category lifting\n * @since 2.0.0\n */\nexport var fromPredicate = /*#__PURE__*/ fromPredicate_(FromEither);\n/**\n * @category filtering\n * @since 2.0.0\n */\nexport var filterOrElse = /*#__PURE__*/ filterOrElse_(FromEither, Chain);\n/**\n * Less strict version of [`filterOrElse`](#filterorelse).\n *\n * The `W` suffix (short for **W**idening) means that the error types will be merged.\n *\n * @category filtering\n * @since 2.9.0\n */\nexport var filterOrElseW = filterOrElse;\n/**\n * @category lifting\n * @since 2.4.0\n */\nexport var fromEitherK = /*#__PURE__*/ fromEitherK_(FromEither);\n/**\n * @category lifting\n * @since 2.10.0\n */\nexport var fromIOK = /*#__PURE__*/ fromIOK_(FromIO);\n/**\n * Alias of `flatMapIO`.\n *\n * @category legacy\n * @since 2.10.0\n */\nexport var chainIOK = flatMapIO;\n/**\n * Alias of `tapIO`.\n *\n * @category legacy\n * @since 2.10.0\n */\nexport var chainFirstIOK = tapIO;\n/**\n * @category lifting\n * @since 2.10.0\n */\nexport var fromTaskK = /*#__PURE__*/ fromTaskK_(FromTask);\n/**\n * Alias of `flatMapTask`.\n *\n * @category legacy\n * @since 2.10.0\n */\nexport var chainTaskK = flatMapTask;\n/**\n * Alias of `tapTask`.\n *\n * @category legacy\n * @since 2.10.0\n */\nexport var chainFirstTaskK = tapTask;\n/**\n * Alias of `flatMapIOEither`.\n *\n * Less strict version of [`chainIOEitherK`](#chainioeitherk).\n *\n * The `W` suffix (short for **W**idening) means that the error types will be merged.\n *\n * @category legacy\n * @since 2.6.1\n */\nexport var chainIOEitherKW = flatMapIOEither;\n/**\n * Alias of `flatMapIOEither`.\n *\n * @category legacy\n * @since 2.4.0\n */\nexport var chainIOEitherK = flatMapIOEither;\nexport function taskify(f) {\n    return function () {\n        var args = Array.prototype.slice.call(arguments);\n        return function () {\n            return new Promise(function (resolve) {\n                var cbResolver = function (e, r) { return (e != null ? resolve(_.left(e)) : resolve(_.right(r))); };\n                f.apply(null, args.concat(cbResolver));\n            });\n        };\n    };\n}\n/**\n * Make sure that a resource is cleaned up in the event of an exception (\\*). The release action is called regardless of\n * whether the body action throws (\\*) or returns.\n *\n * (\\*) i.e. returns a `Left`\n *\n * @since 2.0.0\n */\nexport var bracket = function (acquire, use, release) { return bracketW(acquire, use, release); };\n/**\n * Less strict version of [`bracket`](#bracket).\n *\n * The `W` suffix (short for **W**idening) means that the error types will be merged.\n *\n * @since 2.12.0\n */\nexport var bracketW = function (acquire, use, release) {\n    return flatMap(acquire, function (a) { return T.flatMap(use(a), function (e) { return flatMap(release(a, e), function () { return T.of(e); }); }); });\n};\n// -------------------------------------------------------------------------------------\n// do notation\n// -------------------------------------------------------------------------------------\n/**\n * @category do notation\n * @since 2.9.0\n */\nexport var Do = /*#__PURE__*/ of(_.emptyRecord);\n/**\n * @category do notation\n * @since 2.8.0\n */\nexport var bindTo = /*#__PURE__*/ bindTo_(Functor);\nvar let_ = /*#__PURE__*/ let__(Functor);\nexport { \n/**\n * @category do notation\n * @since 2.13.0\n */\nlet_ as let };\n/**\n * @category do notation\n * @since 2.8.0\n */\nexport var bind = /*#__PURE__*/ chainable.bind(Chain);\n/**\n * The `W` suffix (short for **W**idening) means that the error types will be merged.\n *\n * @category do notation\n * @since 2.8.0\n */\nexport var bindW = bind;\n/**\n * @category do notation\n * @since 2.8.0\n */\nexport var apS = /*#__PURE__*/ apS_(ApplyPar);\n/**\n * Less strict version of [`apS`](#aps).\n *\n * The `W` suffix (short for **W**idening) means that the error types will be merged.\n *\n * @category do notation\n * @since 2.8.0\n */\nexport var apSW = apS;\n/**\n * @since 2.11.0\n */\nexport var ApT = /*#__PURE__*/ of(_.emptyReadonlyArray);\n// -------------------------------------------------------------------------------------\n// array utils\n// -------------------------------------------------------------------------------------\n/**\n * Equivalent to `ReadonlyNonEmptyArray#traverseWithIndex(ApplicativePar)`.\n *\n * @category traversing\n * @since 2.11.0\n */\nexport var traverseReadonlyNonEmptyArrayWithIndex = function (f) {\n    return flow(T.traverseReadonlyNonEmptyArrayWithIndex(f), T.map(E.traverseReadonlyNonEmptyArrayWithIndex(SK)));\n};\n/**\n * Equivalent to `ReadonlyArray#traverseWithIndex(ApplicativePar)`.\n *\n * @category traversing\n * @since 2.11.0\n */\nexport var traverseReadonlyArrayWithIndex = function (f) {\n    var g = traverseReadonlyNonEmptyArrayWithIndex(f);\n    return function (as) { return (_.isNonEmpty(as) ? g(as) : ApT); };\n};\n/**\n * Equivalent to `ReadonlyArray#traverseWithIndex(ApplicativeSeq)`.\n *\n * @category traversing\n * @since 2.11.0\n */\nexport var traverseReadonlyNonEmptyArrayWithIndexSeq = function (f) {\n    return function (as) {\n        return function () {\n            return _.tail(as).reduce(function (acc, a, i) {\n                return acc.then(function (ebs) {\n                    return _.isLeft(ebs)\n                        ? acc\n                        : f(i + 1, a)().then(function (eb) {\n                            if (_.isLeft(eb)) {\n                                return eb;\n                            }\n                            ebs.right.push(eb.right);\n                            return ebs;\n                        });\n                });\n            }, f(0, _.head(as))().then(E.map(_.singleton)));\n        };\n    };\n};\n/**\n * Equivalent to `ReadonlyArray#traverseWithIndex(ApplicativeSeq)`.\n *\n * @category traversing\n * @since 2.11.0\n */\nexport var traverseReadonlyArrayWithIndexSeq = function (f) {\n    var g = traverseReadonlyNonEmptyArrayWithIndexSeq(f);\n    return function (as) { return (_.isNonEmpty(as) ? g(as) : ApT); };\n};\n/**\n * Equivalent to `ReadonlyArray#traverseWithIndex(Applicative)`.\n *\n * @category traversing\n * @since 2.9.0\n */\nexport var traverseArrayWithIndex = traverseReadonlyArrayWithIndex;\n/**\n * Equivalent to `ReadonlyArray#traverse(Applicative)`.\n *\n * @category traversing\n * @since 2.9.0\n */\nexport var traverseArray = function (f) { return traverseReadonlyArrayWithIndex(function (_, a) { return f(a); }); };\n/**\n * Equivalent to `ReadonlyArray#sequence(Applicative)`.\n *\n * @category traversing\n * @since 2.9.0\n */\nexport var sequenceArray = \n/*#__PURE__*/ traverseArray(identity);\n/**\n * Equivalent to `ReadonlyArray#traverseWithIndex(ApplicativeSeq)`.\n *\n * @category traversing\n * @since 2.9.0\n */\nexport var traverseSeqArrayWithIndex = traverseReadonlyArrayWithIndexSeq;\n/**\n * Equivalent to `ReadonlyArray#traverse(ApplicativeSeq)`.\n *\n * @category traversing\n * @since 2.9.0\n */\nexport var traverseSeqArray = function (f) { return traverseReadonlyArrayWithIndexSeq(function (_, a) { return f(a); }); };\n/**\n * Equivalent to `ReadonlyArray#sequence(ApplicativeSeq)`.\n *\n * @category traversing\n * @since 2.9.0\n */\nexport var sequenceSeqArray = \n/*#__PURE__*/ traverseSeqArray(identity);\n// -------------------------------------------------------------------------------------\n// legacy\n// -------------------------------------------------------------------------------------\n/**\n * Alias of `flatMap`.\n *\n * @category legacy\n * @since 2.0.0\n */\nexport var chain = flatMap;\n/**\n * Alias of `flatMap`.\n *\n * @category legacy\n * @since 2.6.0\n */\nexport var chainW = flatMap;\n/**\n * Alias of `tap`.\n *\n * @category legacy\n * @since 2.0.0\n */\nexport var chainFirst = tap;\n/**\n * Alias of `tap`.\n *\n * @category legacy\n * @since 2.8.0\n */\nexport var chainFirstW = tap;\n/**\n * Alias of `tapError`.\n *\n * @category legacy\n * @since 2.11.0\n */\nexport var orElseFirst = tapError;\n/**\n * Alias of `tapError`.\n *\n * @category legacy\n * @since 2.11.0\n */\nexport var orElseFirstW = tapError;\n// -------------------------------------------------------------------------------------\n// deprecated\n// -------------------------------------------------------------------------------------\n/**\n * This instance is deprecated, use small, specific instances instead.\n * For example if a function needs a `Functor` instance, pass `TE.Functor` instead of `TE.taskEither`\n * (where `TE` is from `import TE from 'fp-ts/TaskEither'`)\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var taskEither = {\n    URI: URI,\n    bimap: mapBoth,\n    mapLeft: mapError,\n    map: _map,\n    of: of,\n    ap: _apPar,\n    chain: flatMap,\n    alt: _alt,\n    fromIO: fromIO,\n    fromTask: fromTask,\n    throwError: throwError\n};\n/**\n * This instance is deprecated, use small, specific instances instead.\n * For example if a function needs a `Functor` instance, pass `TE.Functor` instead of `TE.taskEitherSeq`\n * (where `TE` is from `import TE from 'fp-ts/TaskEither'`)\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var taskEitherSeq = {\n    URI: URI,\n    bimap: mapBoth,\n    mapLeft: mapError,\n    map: _map,\n    of: of,\n    ap: _apSeq,\n    chain: flatMap,\n    alt: _alt,\n    fromIO: fromIO,\n    fromTask: fromTask,\n    throwError: throwError\n};\n/**\n * Use [`getApplySemigroup`](./Apply.ts.html#getapplysemigroup) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var getApplySemigroup = \n/*#__PURE__*/ getApplySemigroup_(ApplySeq);\n/**\n * Use [`getApplicativeMonoid`](./Applicative.ts.html#getapplicativemonoid) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var getApplyMonoid = \n/*#__PURE__*/ getApplicativeMonoid(ApplicativeSeq);\n/**\n * Use [`getApplySemigroup`](./Apply.ts.html#getapplysemigroup) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport var getSemigroup = function (S) {\n    return getApplySemigroup_(T.ApplySeq)(E.getSemigroup(S));\n};\n/**\n * Use [`getApplicativeTaskValidation`](#getapplicativetaskvalidation) and [`getAltTaskValidation`](#getalttaskvalidation) instead.\n *\n * @category zone of death\n * @since 2.0.0\n * @deprecated\n */\nexport function getTaskValidation(SE) {\n    var applicativeTaskValidation = getApplicativeTaskValidation(T.ApplicativePar, SE);\n    var altTaskValidation = getAltTaskValidation(SE);\n    return {\n        URI: URI,\n        _E: undefined,\n        map: _map,\n        ap: applicativeTaskValidation.ap,\n        of: of,\n        chain: flatMap,\n        bimap: mapBoth,\n        mapLeft: mapError,\n        alt: altTaskValidation.alt,\n        fromIO: fromIO,\n        fromTask: fromTask,\n        throwError: throwError\n    };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjDA,IAAI,YAAY,4CAAS,yCAAK,SAAS,IAAK,SAAU,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS;IACnF,SAAS,MAAM,KAAK;QAAI,OAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,OAAO;YAAI,QAAQ;QAAQ;IAAI;IAC3G,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,EAAE,SAAU,OAAO,EAAE,MAAM;QACrD,SAAS,UAAU,KAAK;YAAI,IAAI;gBAAE,KAAK,UAAU,IAAI,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC1F,SAAS,SAAS,KAAK;YAAI,IAAI;gBAAE,KAAK,SAAS,CAAC,QAAQ,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC7F,SAAS,KAAK,MAAM;YAAI,OAAO,IAAI,GAAG,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,IAAI,CAAC,WAAW;QAAW;QAC7G,KAAK,CAAC,YAAY,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,CAAC,EAAE,IAAI;IACtE;AACJ;AACA,IAAI,cAAc,4CAAS,yCAAK,WAAW,IAAK,SAAU,OAAO,EAAE,IAAI;IACnE,IAAI,IAAI;QAAE,OAAO;QAAG,MAAM;YAAa,IAAI,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,EAAE;YAAE,OAAO,CAAC,CAAC,EAAE;QAAE;QAAG,MAAM,EAAE;QAAE,KAAK,EAAE;IAAC,GAAG,GAAG,GAAG,GAAG;IAC/G,OAAO,IAAI;QAAE,MAAM,KAAK;QAAI,SAAS,KAAK;QAAI,UAAU,KAAK;IAAG,GAAG,OAAO,WAAW,cAAc,CAAC,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;QAAa,OAAO,IAAI;IAAE,CAAC,GAAG;;;IACvJ,SAAS,KAAK,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,KAAK;gBAAC;gBAAG;aAAE;QAAG;IAAG;IACjE,SAAS,KAAK,EAAE;QACZ,IAAI,GAAG,MAAM,IAAI,UAAU;QAC3B,MAAO,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAG,IAAI;YAC1C,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,SAAS,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO;YAC3J,IAAI,IAAI,GAAG,GAAG,KAAK;gBAAC,EAAE,CAAC,EAAE,GAAG;gBAAG,EAAE,KAAK;aAAC;YACvC,OAAQ,EAAE,CAAC,EAAE;gBACT,KAAK;gBAAG,KAAK;oBAAG,IAAI;oBAAI;gBACxB,KAAK;oBAAG,EAAE,KAAK;oBAAI,OAAO;wBAAE,OAAO,EAAE,CAAC,EAAE;wBAAE,MAAM;oBAAM;gBACtD,KAAK;oBAAG,EAAE,KAAK;oBAAI,IAAI,EAAE,CAAC,EAAE;oBAAE,KAAK;wBAAC;qBAAE;oBAAE;gBACxC,KAAK;oBAAG,KAAK,EAAE,GAAG,CAAC,GAAG;oBAAI,EAAE,IAAI,CAAC,GAAG;oBAAI;gBACxC;oBACI,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG;wBAAE,IAAI;wBAAG;oBAAU;oBAC3G,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,KAAM,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,AAAC,GAAG;wBAAE,EAAE,KAAK,GAAG,EAAE,CAAC,EAAE;wBAAE;oBAAO;oBACrF,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;wBAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;wBAAE,IAAI;wBAAI;oBAAO;oBACpE,IAAI,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;wBAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;wBAAE,EAAE,GAAG,CAAC,IAAI,CAAC;wBAAK;oBAAO;oBAClE,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,GAAG;oBACnB,EAAE,IAAI,CAAC,GAAG;oBAAI;YACtB;YACA,KAAK,KAAK,IAAI,CAAC,SAAS;QAC5B,EAAE,OAAO,GAAG;YAAE,KAAK;gBAAC;gBAAG;aAAE;YAAE,IAAI;QAAG,SAAU;YAAE,IAAI,IAAI;QAAG;QACzD,IAAI,EAAE,CAAC,EAAE,GAAG,GAAG,MAAM,EAAE,CAAC,EAAE;QAAE,OAAO;YAAE,OAAO,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,KAAK;YAAG,MAAM;QAAK;IACnF;AACJ;;;;;;;;;;;;;;;AAsBO,IAAI,OAAO,WAAW,GAAG,qJAAO,CAAC,qJAAS;AAK1C,IAAI,QAAQ,WAAW,GAAG,sJAAQ,CAAC,qJAAS;AAK5C,IAAI,YAAY,WAAW,GAAG,uJAAS,CAAC,qJAAS;AAKjD,IAAI,WAAW,WAAW,GAAG,sJAAQ,CAAC,qJAAS;AAK/C,IAAI,UAAU,WAAW,GAAG,IAAA,sJAAI,EAAC,oJAAQ,EAAE;AAK3C,IAAI,SAAS,WAAW,GAAG,IAAA,sJAAI,EAAC,oJAAQ,EAAE;AAQ1C,IAAI,SAAS;AAKb,IAAI,WAAW;AAKf,IAAI,aAAa,gJAAI;AAKrB,IAAI,eAAe,oJAAQ;AAK3B,IAAI,iBAAiB,SAAU,MAAM;IACxC,OAAO,iJAAK,CAAC,0JAAY,CAAC;AAC9B;AAKO,IAAI,QACX,WAAW,GAAG,sJAAQ,CAAC,qJAAS;AASzB,IAAI,SAAS;AAOb,IAAI,SAAS,WAAW,GAAG,uJAAS,CAAC,mJAAO;AAO5C,IAAI,OAAO;AASX,IAAI,UAAU;AAOd,IAAI,QAAQ;AAKZ,IAAI,YACX,WAAW,GAAG,0JAAY,CAAC,mJAAO;AAS3B,IAAI,aAAa;AAoBjB,IAAI,WAAW,SAAU,CAAC,EAAE,UAAU;IACzC,OAAO;QAAc,OAAO,UAAU,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG;YAC1D,IAAI;YACJ,OAAO,YAAY,IAAI,EAAE,SAAU,EAAE;gBACjC,OAAQ,GAAG,KAAK;oBACZ,KAAK;wBACD,GAAG,IAAI,CAAC,IAAI,CAAC;4BAAC;4BAAG;;4BAAK;yBAAE;wBACxB,OAAO;4BAAC,EAAE,OAAO;4BAAI,IAAI,IAAI,CAAC,uJAAO;yBAAE;oBAC3C,KAAK;wBAAG,OAAO;4BAAC,EAAE,QAAQ;4BAAI,GAAG,IAAI;yBAAG;oBACxC,KAAK;wBACD,WAAW,GAAG,IAAI;wBAClB,OAAO;4BAAC,EAAE,QAAQ;4BAAI,sJAAM,CAAC,WAAW;yBAAW;oBACvD,KAAK;wBAAG,OAAO;4BAAC,EAAE,QAAQ;yBAAG;gBACjC;YACJ;QACJ;IAAI;AACR;AAOO,IAAI,YAAY,SAAU,CAAC,EAAE,UAAU;IAC1C,OAAO;QACH,IAAI,IAAI,EAAE;QACV,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;YAC1C,CAAC,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG;QACzB;QACA,OAAO,SAAS;YAAc,OAAO,EAAE,KAAK,CAAC,KAAK,GAAG;QAAI,GAAG;IAChE;AACJ;AAKO,IAAI,UAAU,WAAW,GAAG,wJAAU,CAAC,qJAAS;AAKhD,IAAI,eAAe,WAAW,GAAG,6JAAe,CAAC,qJAAS;AAO1D,IAAI,gBAAgB,WAAW,GAAG,8JAAgB,CAAC,qJAAS;AAO5D,IAAI,iBACX,WAAW,GAAG,+JAAiB,CAAC,mJAAO;AAyBhC,IAAI,SACX,WAAW,GAAG,uJAAS,CAAC,mJAAO;AASxB,IAAI,UAAU;AAOd,IAAI,WAAW,WAAW,GAAG,IAAA,sJAAI,EAAC,GAAG,yJAAW,CAAC,mJAAO;AAKxD,IAAI,iBAAiB,SAAU,MAAM;IAAI,OAAO,SAAS,QAAQ;AAAU;AAK3E,IAAI,mBAAmB,SAAU,MAAM;IAAI,OAAO,SAAS,UAAU;AAAU;AAK/E,IAAI,SACX,WAAW,GAAG,uJAAS,CAAC,mJAAO;AAIxB,IAAI,OAAO,WAAW,GAAG,qJAAO,CAAC,qJAAS;AAK1C,IAAI,kBAAkB,SAAU,MAAM;IACzC,IAAI,OAAO,eAAe;IAC1B,OAAO,SAAU,CAAC;QAAI,OAAO,IAAA,sJAAI,EAAC,GAAG;IAAO;AAChD;AASO,IAAI,oBAAoB,SAAU,MAAM;IAC3C,OAAO,SAAU,CAAC;QACd,OAAO,SAAU,EAAE;YACf,OAAO,QAAQ,IAAI,gBAAgB,QAAQ;QAC/C;IACJ;AACJ;AAOO,IAAI,mBAAmB;AAKvB,IAAI,gBAAgB,SAAU,CAAC;IAAI,OAAO,IAAA,sJAAI,EAAC,GAAG;AAAe;AACxE,IAAI,OAAO,SAAU,EAAE,EAAE,CAAC;IAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,IAAI;AAAK;AACvD,IAAI,SAAS,SAAU,GAAG,EAAE,EAAE;IAAI,OAAO,IAAA,sJAAI,EAAC,KAAK,GAAG;AAAM;AAC5D,IAAI,SAAS,SAAU,GAAG,EAAE,EAAE;IAAI,OAAO,QAAQ,KAAK,SAAU,CAAC;QAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,IAAI;IAAK;AAAI;AAClG,wBAAwB,GACxB,IAAI,OAAO,SAAU,EAAE,EAAE,IAAI;IAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,IAAI;AAAQ;AAQtD,IAAI,MAAM,WAAW,GAAG,oJAAM,CAAC,qJAAS;AAqBxC,IAAI,UAAU,WAAW,GAAG,IAAA,sJAAI,EAAC,GAAG,wJAAU,CAAC,qJAAS;AAOxD,IAAI,QAAQ;AAoBZ,IAAI,WAAW,WAAW,GAAG,IAAA,sJAAI,EAAC,GAAG,yJAAW,CAAC,qJAAS;AAO1D,IAAI,UAAU;AAId,IAAI,KACX,WAAW,GAAG,mJAAK,CAAC,sJAAU;AAQvB,IAAI,MAAM;AAKV,IAAI,UAAU,WAAW,GAAG,IAAA,sJAAI,EAAC,GAAG,wJAAU,CAAC,mJAAO;AAStD,IAAI,WACX,WAAW,GAAG,QAAQ,0JAAQ;AAKvB,IAAI,UAAU;AA2Cd,IAAI,MACX,WAAW,GAAG,oJAAM,CAAC,mJAAO;AASrB,IAAI,OAAO;AAKX,IAAI,KAAK;AAIT,IAAI,aAAa;AAKjB,IAAI,MAAM;AAsDV,SAAS,6BAA6B,CAAC,EAAE,CAAC;IAC7C,IAAI,KAAK,IAAA,iJAAG,EAAC,GAAG,wKAA0B,CAAC;IAC3C,OAAO;QACH,KAAK;QACL,IAAI;QACJ,KAAK;QACL,IAAI,SAAU,GAAG,EAAE,EAAE;YAAI,OAAO,IAAA,sJAAI,EAAC,KAAK,GAAG;QAAM;QACnD,IAAI;IACR;AACJ;AAUO,SAAS,qBAAqB,CAAC;IAClC,IAAI,MAAM,8JAAgB,CAAC,mJAAO,EAAE;IACpC,OAAO;QACH,KAAK;QACL,IAAI;QACJ,KAAK;QACL,KAAK,SAAU,EAAE,EAAE,IAAI;YAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,IAAI;QAAQ;IAC3D;AACJ;AAKO,IAAI,iBAAiB,SAAU,CAAC;IACnC,IAAI,IAAI,8JAAgB,CAAC;IACzB,OAAO;QACH,KAAK;QACL,IAAI;QACJ,SAAS,IAAA,4JAAQ,EAAC,qJAAS,EAAE;QAC7B,UAAU,IAAA,6JAAS,EAAC,qJAAS,EAAE,GAAG,uJAAS;IAC/C;AACJ;AAKO,SAAS,cAAc,CAAC;IAC3B,IAAI,IAAI,6JAAe,CAAC;IACxB,IAAI,IAAI,eAAe;IACvB,IAAI,SAAS,IAAA,0JAAO,EAAC,qJAAS,EAAE;IAChC,IAAI,YAAY,IAAA,6JAAU,EAAC,qJAAS,EAAE;IACtC,IAAI,YAAY,IAAA,6JAAU,EAAC,qJAAS,EAAE;IACtC,IAAI,eAAe,IAAA,gKAAa,EAAC,qJAAS,EAAE;IAC5C,OAAO;QACH,KAAK;QACL,IAAI;QACJ,KAAK;QACL,SAAS,EAAE,OAAO;QAClB,UAAU,EAAE,QAAQ;QACpB,QAAQ,SAAU,EAAE,EAAE,SAAS;YAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,OAAO;QAAa;QACvE,WAAW,SAAU,EAAE,EAAE,CAAC;YAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,UAAU;QAAK;QAC7D,WAAW,SAAU,EAAE,EAAE,SAAS;YAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,UAAU;QAAa;QAC7E,cAAc,SAAU,EAAE,EAAE,CAAC;YAAI,OAAO,IAAA,sJAAI,EAAC,IAAI,aAAa;QAAK;IACvE;AACJ;AAKO,IAAI,UAAU;IACjB,KAAK;IACL,KAAK;AACT;AAOO,IAAI,KAAK,IAAA,sJAAI,EAAC,GAAG,IAAA,mJAAG,EAAC;AAOrB,IAAI,SAAS,IAAA,uJAAO,EAAC;AAKrB,IAAI,OAAO,WAAW,GAAG,IAAA,qJAAK,EAAC;AAK/B,IAAI,UAAU;IACjB,KAAK;IACL,IAAI;AACR;AAOO,IAAI,WAAW;IAClB,KAAK;IACL,KAAK;IACL,IAAI;AACR;AAMO,IAAI,UAAU,WAAW,GAAG,IAAA,sJAAQ,EAAC;AAQrC,IAAI,WAAW;AAMf,IAAI,WAAW,WAAW,GAAG,IAAA,uJAAS,EAAC;AAQvC,IAAI,YAAY;AAOhB,IAAI,iBAAiB;IACxB,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;AACR;AAOO,IAAI,WAAW;IAClB,KAAK;IACL,KAAK;IACL,IAAI;AACR;AAOO,IAAI,iBAAiB;IACxB,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;AACR;AAKO,IAAI,QAAQ;IACf,KAAK;IACL,KAAK;IACL,IAAI;IACJ,OAAO;AACX;AAKO,IAAI,QAAQ;IACf,KAAK;IACL,KAAK;IACL,IAAI;IACJ,OAAO;IACP,IAAI;AACR;AAKO,IAAI,UAAU;IACjB,KAAK;IACL,KAAK;IACL,IAAI;IACJ,OAAO;IACP,IAAI;IACJ,QAAQ;AACZ;AAKO,IAAI,YAAY;IACnB,KAAK;IACL,KAAK;IACL,IAAI;IACJ,OAAO;IACP,IAAI;IACJ,QAAQ;IACR,UAAU;AACd;AAKO,IAAI,aAAa;IACpB,KAAK;IACL,KAAK;IACL,IAAI;IACJ,OAAO;IACP,IAAI;IACJ,YAAY;AAChB;AAKO,IAAI,aAAa;IACpB,KAAK;IACL,YAAY;AAChB;AAKO,IAAI,SAAS;IAChB,KAAK;IACL,QAAQ;AACZ;AAKO,IAAI,WAAW;IAClB,KAAK;IACL,QAAQ;IACR,UAAU;AACd;AAQO,IAAI,MAAM,WAAW,GAAG,IAAA,sJAAI,EAAC,GAAG,kJAAa,CAAC;AAyB9C,IAAI,YAAY,WAAW,GAAG,IAAA,sJAAI,EAAC,GAAG,IAAA,6JAAU,EAAC,YAAY;AAkC7D,IAAI,QAAQ,WAAW,GAAG,IAAA,sJAAI,EAAC,GAAG,IAAA,qJAAM,EAAC,QAAQ;AAyBjD,IAAI,UAAU,WAAW,GAAG,IAAA,sJAAI,EAAC,GAAG,IAAA,yJAAQ,EAAC,UAAU;AAKvD,IAAI,YAAY;IACnB,KAAK;IACL,OAAO;IACP,SAAS;AACb;AAKO,IAAI,MAAM;IACb,KAAK;IACL,KAAK;IACL,KAAK;AACT;AAKO,IAAI,aACX,WAAW,GAAG,IAAA,8JAAW,EAAC;AAOnB,IAAI,cACX,WAAW,GAAG,IAAA,+JAAY,EAAC;AAOpB,IAAI,eAAe,WAAW,GAAG,IAAA,gKAAa,EAAC,YAAY;AAO3D,IAAI,gBAAgB;AAC3B,cAAc,GACd,IAAI,cAAc;IACd,YAAY,WAAW,UAAU;AACrC;AAKO,IAAI,eAAe,WAAW,GAAG,8JAAc,CAAC;AAKhD,IAAI,aAAa,WAAW,GAAG,4JAAY,CAAC;AACnD,cAAc,GACd,IAAI,WAAW;IACX,SAAS;AACb;AACA,cAAc,GACd,IAAI,UAAU;IACV,QAAQ,OAAO,MAAM;AACzB;AACA,cAAc,GACd,IAAI,YAAY;IACZ,UAAU;AACd;AAKO,IAAI,kBAAkB,WAAW,GAAG,iKAAiB,CAAC,aAAa;AAKnE,IAAI,gBAAgB,WAAW,GAAG,+JAAe,CAAC,aAAa;AAK/D,IAAI,gBAAgB,WAAW,GAAG,+JAAe,CAAC,aAAa;AAK/D,IAAI,YAAY,WAAW,GAAG,2JAAW,CAAC,SAAS;AAKnD,IAAI,cAAc,WAAW,GAAG,6JAAa,CAAC,WAAW;AAKzD,IAAI,kBAAkB,WAAW,GAAG,IAAA,sJAAI,EAAC,GAAG,SAAU,IAAI,EAAE,CAAC;IAChE,OAAO,QAAQ,MAAM,cAAc;AACvC;AAKO,IAAI,oBAAoB,WAAW,GAAG,IAAA,sJAAI,EAAC,GAAG,SAAU,IAAI,EAAE,CAAC,EAAE,MAAM;IAC1E,OAAO,QAAQ,MAAM,SAAU,CAAC;QAAI,OAAO,eAAe;YAAc,OAAO,OAAO;QAAI,GAAG,EAAE;IAAK;AACxG;AAOO,IAAI,eAAe;AAOnB,IAAI,gBAAgB;AAOpB,IAAI,oBAAoB;AAWxB,IAAI,qBAAqB;AAKzB,IAAI,gBAAgB,WAAW,GAAG,IAAA,iKAAc,EAAC;AAKjD,IAAI,eAAe,WAAW,GAAG,IAAA,gKAAa,EAAC,YAAY;AAS3D,IAAI,gBAAgB;AAKpB,IAAI,cAAc,WAAW,GAAG,IAAA,+JAAY,EAAC;AAK7C,IAAI,UAAU,WAAW,GAAG,IAAA,uJAAQ,EAAC;AAOrC,IAAI,WAAW;AAOf,IAAI,gBAAgB;AAKpB,IAAI,YAAY,WAAW,GAAG,IAAA,2JAAU,EAAC;AAOzC,IAAI,aAAa;AAOjB,IAAI,kBAAkB;AAWtB,IAAI,kBAAkB;AAOtB,IAAI,iBAAiB;AACrB,SAAS,QAAQ,CAAC;IACrB,OAAO;QACH,IAAI,OAAO,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;QACtC,OAAO;YACH,OAAO,IAAI,QAAQ,SAAU,OAAO;gBAChC,IAAI,aAAa,SAAU,CAAC,EAAE,CAAC;oBAAI,OAAQ,KAAK,OAAO,QAAQ,sJAAM,CAAC,MAAM,QAAQ,uJAAO,CAAC;gBAAM;gBAClG,EAAE,KAAK,CAAC,MAAM,KAAK,MAAM,CAAC;YAC9B;QACJ;IACJ;AACJ;AASO,IAAI,UAAU,SAAU,OAAO,EAAE,GAAG,EAAE,OAAO;IAAI,OAAO,SAAS,SAAS,KAAK;AAAU;AAQzF,IAAI,WAAW,SAAU,OAAO,EAAE,GAAG,EAAE,OAAO;IACjD,OAAO,QAAQ,SAAS,SAAU,CAAC;QAAI,OAAO,qJAAS,CAAC,IAAI,IAAI,SAAU,CAAC;YAAI,OAAO,QAAQ,QAAQ,GAAG,IAAI;gBAAc,OAAO,gJAAI,CAAC;YAAI;QAAI;IAAI;AACvJ;AAQO,IAAI,KAAK,WAAW,GAAG,GAAG,6JAAa;AAKvC,IAAI,SAAS,WAAW,GAAG,IAAA,uJAAO,EAAC;AAC1C,IAAI,OAAO,WAAW,GAAG,IAAA,oJAAK,EAAC;;AAWxB,IAAI,OAAO,WAAW,GAAG,mJAAc,CAAC;AAOxC,IAAI,QAAQ;AAKZ,IAAI,MAAM,WAAW,GAAG,IAAA,kJAAI,EAAC;AAS7B,IAAI,OAAO;AAIX,IAAI,MAAM,WAAW,GAAG,GAAG,oKAAoB;AAU/C,IAAI,yCAAyC,SAAU,CAAC;IAC3D,OAAO,IAAA,sJAAI,EAAC,oLAAwC,CAAC,IAAI,iJAAK,CAAC,sLAAwC,CAAC,oJAAE;AAC9G;AAOO,IAAI,iCAAiC,SAAU,CAAC;IACnD,IAAI,IAAI,uCAAuC;IAC/C,OAAO,SAAU,EAAE;QAAI,OAAQ,4JAAY,CAAC,MAAM,EAAE,MAAM;IAAM;AACpE;AAOO,IAAI,4CAA4C,SAAU,CAAC;IAC9D,OAAO,SAAU,EAAE;QACf,OAAO;YACH,OAAO,sJAAM,CAAC,IAAI,MAAM,CAAC,SAAU,GAAG,EAAE,CAAC,EAAE,CAAC;gBACxC,OAAO,IAAI,IAAI,CAAC,SAAU,GAAG;oBACzB,OAAO,wJAAQ,CAAC,OACV,MACA,EAAE,IAAI,GAAG,KAAK,IAAI,CAAC,SAAU,EAAE;wBAC7B,IAAI,wJAAQ,CAAC,KAAK;4BACd,OAAO;wBACX;wBACA,IAAI,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK;wBACvB,OAAO;oBACX;gBACR;YACJ,GAAG,EAAE,GAAG,sJAAM,CAAC,OAAO,IAAI,CAAC,mJAAK,CAAC,2JAAW;QAChD;IACJ;AACJ;AAOO,IAAI,oCAAoC,SAAU,CAAC;IACtD,IAAI,IAAI,0CAA0C;IAClD,OAAO,SAAU,EAAE;QAAI,OAAQ,4JAAY,CAAC,MAAM,EAAE,MAAM;IAAM;AACpE;AAOO,IAAI,yBAAyB;AAO7B,IAAI,gBAAgB,SAAU,CAAC;IAAI,OAAO,+BAA+B,SAAU,CAAC,EAAE,CAAC;QAAI,OAAO,EAAE;IAAI;AAAI;AAO5G,IAAI,gBACX,WAAW,GAAG,cAAc,0JAAQ;AAO7B,IAAI,4BAA4B;AAOhC,IAAI,mBAAmB,SAAU,CAAC;IAAI,OAAO,kCAAkC,SAAU,CAAC,EAAE,CAAC;QAAI,OAAO,EAAE;IAAI;AAAI;AAOlH,IAAI,mBACX,WAAW,GAAG,iBAAiB,0JAAQ;AAUhC,IAAI,QAAQ;AAOZ,IAAI,SAAS;AAOb,IAAI,aAAa;AAOjB,IAAI,cAAc;AAOlB,IAAI,cAAc;AAOlB,IAAI,eAAe;AAanB,IAAI,aAAa;IACpB,KAAK;IACL,OAAO;IACP,SAAS;IACT,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,KAAK;IACL,QAAQ;IACR,UAAU;IACV,YAAY;AAChB;AAUO,IAAI,gBAAgB;IACvB,KAAK;IACL,OAAO;IACP,SAAS;IACT,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,KAAK;IACL,QAAQ;IACR,UAAU;IACV,YAAY;AAChB;AAQO,IAAI,oBACX,WAAW,GAAG,IAAA,gKAAkB,EAAC;AAQ1B,IAAI,iBACX,WAAW,GAAG,IAAA,yKAAoB,EAAC;AAQ5B,IAAI,eAAe,SAAU,CAAC;IACjC,OAAO,IAAA,gKAAkB,EAAC,sJAAU,EAAE,4JAAc,CAAC;AACzD;AAQO,SAAS,kBAAkB,EAAE;IAChC,IAAI,4BAA4B,6BAA6B,4JAAgB,EAAE;IAC/E,IAAI,oBAAoB,qBAAqB;IAC7C,OAAO;QACH,KAAK;QACL,IAAI;QACJ,KAAK;QACL,IAAI,0BAA0B,EAAE;QAChC,IAAI;QACJ,OAAO;QACP,OAAO;QACP,SAAS;QACT,KAAK,kBAAkB,GAAG;QAC1B,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}]}