import { NumberFormInput } from '@atlas/components/common/form/number-form-input'
import { Button } from '@atlas/components/ui/button'
import { Form } from '@atlas/components/ui/form'
import { PANEL_WIDTH_DEFAULT } from '@atlas/constants/module'
import type { Module } from '@atlas/lib/api/modules/schemas/modules'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'
import {
  type MasonryAntiOverturningInfillGeometrySchemaInputs,
  type MasonryAntiOverturningParamsFormSchemaInputs,
  masonryAntiOverturningInfillGeometrySchema,
} from '@atlas/types/schemas/masonry/antioverturning-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useQueryClient } from '@tanstack/react-query'
import { Loader2Icon } from 'lucide-react'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useCallback, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'

type Props = {
  session: Session
  projectId: Project['id']
  moduleId: Module['id']
  defaultValues?: Partial<MasonryAntiOverturningInfillGeometrySchemaInputs>
  params: MasonryAntiOverturningParamsFormSchemaInputs
  onSave: () => void
}

export const MasonryAntiOverturningInfillGeometryForm = ({
  session,
  projectId,
  moduleId,
  defaultValues,
  params,
  onSave,
}: Props) => {
  const t = useTranslations(
    'forms.project-params.anti-overturning.infill-geometry',
  )
  const tAction = useTranslations('actions.save-project-params.messages')
  const tCommon = useTranslations('actions.common')
  const queryClient = useQueryClient()

  const form = useForm<MasonryAntiOverturningInfillGeometrySchemaInputs>({
    resolver: zodResolver(masonryAntiOverturningInfillGeometrySchema),
    defaultValues: {
      panelWidth: PANEL_WIDTH_DEFAULT,
      externalFacingThickness: defaultValues?.externalFacingThickness ?? 0,
      internalFacingThickness: defaultValues?.internalFacingThickness ?? 0,
      singleSidePlasterThickness:
        defaultValues?.singleSidePlasterThickness ?? 0,
      netPanelHeight: defaultValues?.netPanelHeight ?? 0,
      panelHeightFromGroundLevel:
        defaultValues?.panelHeightFromGroundLevel ?? 0,
      panelCentroidFromGroundLevel:
        defaultValues?.panelCentroidFromGroundLevel ?? 0,
      fundamentalPeriodPanel: defaultValues?.fundamentalPeriodPanel ?? 0,
    },
  })

  const { mutate, isPending } = useSaveModuleParamsMutation(
    session.accessToken,
    {
      onSuccess: async () => {
        toast.success(tAction('edit.success'))
        await queryClient.invalidateQueries({
          queryKey: ['modules', { projectId, moduleId }],
        })
        onSave()
      },
      onError: error => {
        toast.error(tAction('edit.failure', { error: error.message }))
      },
    },
  )

  const totalBuildingHeight =
    params?.buildingCharacteristics?.totalBuildingHeight ?? 1
  const masonrySpecificWeightExternalFacing =
    params?.materialProperties?.masonrySpecificWeightExternalFacing ?? 0
  const masonrySpecificWeightInternalFacing =
    params?.materialProperties?.masonrySpecificWeightInternalFacing ?? 0
  const plasterSpecificWeight =
    params?.materialProperties?.plasterSpecificWeight ?? 0
  const infillWallElasticModulus =
    params?.materialProperties?.infillWallElasticModulus ?? 0
  const netPanelHeight = form.watch('netPanelHeight')
  const panelWidth = form.watch('panelWidth') ?? PANEL_WIDTH_DEFAULT
  const internalFacingThickness = form.watch('internalFacingThickness')
  const externalFacingThickness = form.watch('externalFacingThickness')
  const singleSidePlasterThickness = form.watch('singleSidePlasterThickness')

  useEffect(() => {
    const panelHeightFromGroundLevel = totalBuildingHeight * 1000
    form.setValue('panelHeightFromGroundLevel', panelHeightFromGroundLevel)
    form.setValue(
      'panelCentroidFromGroundLevel',
      panelHeightFromGroundLevel - netPanelHeight / 2,
    )

    // fundamental panel period calculation:
    // D44 is netPanelHeight
    // D40 is panelWidth
    // D42 is internalFacingThickness
    // D36 is materialProperties.masonrySpecificWeightInternalFacing
    // D41 is externalFacingThickness
    // D35 is materialProperties.masonrySpecificWeightExternalFacing
    // D43 is singleSidePlasterThickness
    // D37 is materialProperties.plasterSpecificWeight
    // D33 is materialProperties.infillWallElasticModulus
    // FORMULA : =(2*3.14*D44^2/3.5156)*SQRT((D40*((D42*D36)+(D41*D35)+(2*D43*D37))*1000/1000000000)/(D33*9.81*1000*D40*(D42+D41+2*D43)^3/12))
    const subInnerPartOne =
      (panelWidth *
        (internalFacingThickness * masonrySpecificWeightInternalFacing +
          externalFacingThickness * masonrySpecificWeightExternalFacing +
          2 * singleSidePlasterThickness * plasterSpecificWeight) *
        1000) /
      1000000000
    const subInnerPartTwo =
      (infillWallElasticModulus *
        9.81 *
        1000 *
        panelWidth *
        (internalFacingThickness +
          externalFacingThickness +
          2 * singleSidePlasterThickness) **
          3) /
      12

    const secondPart = Math.sqrt(subInnerPartOne / subInnerPartTwo)
    const fundamentalPeriodPanel =
      ((2 * 3.14 * netPanelHeight ** 2) / 3.5156) * secondPart

    form.setValue('fundamentalPeriodPanel', fundamentalPeriodPanel)
  }, [
    totalBuildingHeight,
    netPanelHeight,
    panelWidth,
    internalFacingThickness,
    externalFacingThickness,
    singleSidePlasterThickness,
    infillWallElasticModulus,
    form,
    masonrySpecificWeightExternalFacing,
    masonrySpecificWeightInternalFacing,
    plasterSpecificWeight,
  ])

  const handleFormSubmit = useCallback(
    (body: MasonryAntiOverturningInfillGeometrySchemaInputs) => {
      // construct body based on global schema:
      const masonryAntiOverturningParams: MasonryAntiOverturningParamsFormSchemaInputs =
        {
          ...params,
          infillGeometry: body,
        }
      mutate({ projectId, moduleId, body: masonryAntiOverturningParams })
    },
    [mutate, projectId, moduleId, params],
  )

  return (
    <Form {...form}>
      <form
        className="space-y-4 rounded-md border p-4"
        onSubmit={form.handleSubmit(handleFormSubmit)}
      >
        <NumberFormInput
          control={form.control}
          name="panelWidth"
          t={t}
          disabled={true}
        />
        <NumberFormInput
          control={form.control}
          name="externalFacingThickness"
          t={t}
        />
        <NumberFormInput
          control={form.control}
          name="internalFacingThickness"
          t={t}
        />
        <NumberFormInput
          control={form.control}
          name="singleSidePlasterThickness"
          t={t}
        />
        <NumberFormInput control={form.control} name="netPanelHeight" t={t} />
        <NumberFormInput
          control={form.control}
          name="panelHeightFromGroundLevel"
          t={t}
          disabled={true}
        />
        <NumberFormInput
          control={form.control}
          name="panelCentroidFromGroundLevel"
          t={t}
          disabled={true}
        />
        <NumberFormInput
          control={form.control}
          name="fundamentalPeriodPanel"
          t={t}
          disabled={true}
        />
        <Button
          type="submit"
          className="w-full sm:w-auto"
          disabled={isPending}
          onClick={form.handleSubmit(handleFormSubmit)}
        >
          {isPending && <Loader2Icon className="animate-spin" />}
          {tCommon('save')}
        </Button>
      </form>
    </Form>
  )
}
