import { PRODUCT_FIBER_TYPE } from '@atlas/constants/product'
import { nullToUndefinedTransform } from '@atlas/functions/zod/null-to-undefined-transform'
import { z } from 'zod'

// Helper schema for fields that can be either a number or an object with parsedValue
const numericOrParsedValueSchema = z
  .union([
    z.number(),
    z.object({
      source: z.string(),
      parsedValue: z.number(),
    }),
  ])
  .transform(val => {
    if (typeof val === 'number') {
      return val
    }
    return val.parsedValue
  })
  .nullable()
  .optional()
  .transform(nullToUndefinedTransform)

export const productSchema = z.object({
  id: z.string(),
  name: z.string().nullable().optional().transform(nullToUndefinedTransform),
  fiberType: z
    .enum(PRODUCT_FIBER_TYPE)
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  availableWidths: z.array(z.number()).optional().nullable(),
  orientation: z
    .string()
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  thickness: z
    .number()
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  tensileStrength: z
    .number()
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  elasticModulus: z
    .number()
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  characteristicCylindricalCompressiveStrengthFrcMatrix: z
    .number()
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  documentationLink: z
    .string()
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  productType: z
    .string()
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  ultimateStrain: z
    .number()
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  designDeformation: numericOrParsedValueSchema,
  unitStrengthOfTheMesh: z
    .number()
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  width: z.number().nullable().optional().transform(nullToUndefinedTransform),
  density: z.number().nullable().optional().transform(nullToUndefinedTransform),
  maxResistance: z
    .number()
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  weight: z.number().nullable().optional().transform(nullToUndefinedTransform),
  crossSectionArea: z
    .number()
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  diameter: z
    .number()
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  pullOutResistance: z
    .number()
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  connectorsNumberAlongLength: z
    .number()
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  designStrength: z
    .number()
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  maxLayerNumber: z
    .number()
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  systemDeformation: z
    .number()
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  adhesionToConcrete: numericOrParsedValueSchema,
  shearStress: z
    .number()
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  toughnessClass: z
    .string()
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  cylindricCompressiveStrength: z
    .number()
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  characteristicTensileStrength: numericOrParsedValueSchema,
  specificWeight: z
    .number()
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  facingPerformance: z.object().optional().nullable(),
  reinforcementTensileStrength: z
    .number()
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  reinforcementUltimateStrain: z
    .number()
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  conventionalStressLimit: z
    .record(z.string(), z.number())
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  conventionalStrainLimit: z
    .record(z.string(), z.number())
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  matrixBreachStress: z
    .number()
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
})

export const paginatedProductSchema = z.object({
  content: z.array(productSchema),
  pageable: z.object({
    pageNumber: z.number(),
    pageSize: z.number(),
    sort: z.object({
      empty: z.boolean(),
      unsorted: z.boolean(),
      sorted: z.boolean(),
    }),
    offset: z.number(),
    unpaged: z.boolean(),
    paged: z.boolean(),
  }),
  last: z.boolean(),
  totalElements: z.number(),
  totalPages: z.number(),
  first: z.boolean(),
  sort: z.object({
    empty: z.boolean(),
    unsorted: z.boolean(),
    sorted: z.boolean(),
  }),
  size: z.number(),
  number: z.number(),
  numberOfElements: z.number(),
  empty: z.boolean(),
})

export type Product = z.infer<typeof productSchema>
export type PaginatedProducts = z.infer<typeof paginatedProductSchema>
