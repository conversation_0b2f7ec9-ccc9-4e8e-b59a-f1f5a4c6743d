{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/perimeter-and-widespread-intervention-calculation-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { PerimeterAndWidespreadInterventionCalculationResultSchemaInputs } from '@atlas/types/schemas/masonry/antioverturning-form'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  perimeterAndWidespreadInterventionCalculationResult: PerimeterAndWidespreadInterventionCalculationResultSchemaInputs\r\n}\r\n\r\nexport function MasonryAntiOverturningPerimeterAndWidespreadInterventionCalculationResultCard({\r\n  perimeterAndWidespreadInterventionCalculationResult,\r\n}: Props) {\r\n  const {\r\n    overturningMoment,\r\n    stabilizingMomentconnectors,\r\n    totalStabilizingMoment,\r\n    check,\r\n  } = perimeterAndWidespreadInterventionCalculationResult\r\n\r\n  const t = useTranslations(\r\n    'components.calculations.anti-overturning.postInterventionResult.perimeterAndWidespreadIntervention',\r\n  )\r\n  const locale = useLocale()\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <div>\r\n          <span className=\"font-medium\">{t('overturningMoment.label')}:</span>{' '}\r\n          <span>\r\n            {overturningMoment?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n            kNm\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('stabilizingMomentconnectors.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {stabilizingMomentconnectors?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n            kNm\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <div>\r\n            <span className=\"font-medium\">\r\n              {t('totalStabilizingMoment.label')}:\r\n            </span>{' '}\r\n            <span>\r\n              {totalStabilizingMoment?.toLocaleString(locale, {\r\n                maximumFractionDigits: 3,\r\n              })}{' '}\r\n              kNm\r\n            </span>\r\n          </div>\r\n          <span className=\"font-medium\">{t('check.label')}:</span>{' '}\r\n          <Badge\r\n            className={cn(\r\n              'text-base px-3 py-1',\r\n              check ? 'bg-green-600' : 'bg-red-600',\r\n            )}\r\n          >\r\n            {check ? t('check.satisfied') : t('check.notSatisfied')}\r\n          </Badge>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AAEA;AAAA;;;;;;AAMO,SAAS,8EAA8E,EAC5F,mDAAmD,EAC7C;IACN,MAAM,EACJ,iBAAiB,EACjB,2BAA2B,EAC3B,sBAAsB,EACtB,KAAK,EACN,GAAG;IAEJ,MAAM,IAAI,IAAA,yNAAe,EACvB;IAEF,MAAM,SAAS,IAAA,+KAAS;IAExB,qBACE,8OAAC,wIAAI;;0BACH,8OAAC,8IAAU;0BACT,cAAA,8OAAC,6IAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,8OAAC,+IAAW;gBAAC,WAAU;;kCACrB,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAA2B;;;;;;;4BAAS;0CACrE,8OAAC;;oCACE,mBAAmB,eAAe,QAAQ;wCACzC,uBAAuB;oCACzB;oCAAI;oCAAI;;;;;;;;;;;;;kCAIZ,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAqC;;;;;;;4BAClC;0CACR,8OAAC;;oCACE,6BAA6B,eAAe,QAAQ;wCACnD,uBAAuB;oCACzB;oCAAI;oCAAI;;;;;;;;;;;;;kCAIZ,8OAAC;;0CACC,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;;4CACb,EAAE;4CAAgC;;;;;;;oCAC7B;kDACR,8OAAC;;4CACE,wBAAwB,eAAe,QAAQ;gDAC9C,uBAAuB;4CACzB;4CAAI;4CAAI;;;;;;;;;;;;;0CAIZ,8OAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAAe;;;;;;;4BAAS;0CACzD,8OAAC,0IAAK;gCACJ,WAAW,IAAA,yHAAE,EACX,uBACA,QAAQ,iBAAiB;0CAG1B,QAAQ,EAAE,qBAAqB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAM9C", "debugId": null}}, {"offset": {"line": 185, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { WidespreadInterventionCalculationResultSchemaInputs } from '@atlas/types/schemas/masonry/antioverturning-form'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  widespreadInterventionCalculationResult: WidespreadInterventionCalculationResultSchemaInputs\r\n}\r\n\r\nexport function MasonryAntiOverturningWidespreadInterventionCalculationResultCard({\r\n  widespreadInterventionCalculationResult,\r\n}: Props) {\r\n  const {\r\n    panelFundamentalPeriod,\r\n    maximumAcceleration,\r\n    overturningSeismicForce,\r\n    actingMoment,\r\n    midspanAxialStress,\r\n    normalStress,\r\n    unreinforcedSectionResistingMoment,\r\n    regionOneHypothesis,\r\n    regionTwoHypothesis,\r\n    specificResistingMoment,\r\n    check,\r\n  } = widespreadInterventionCalculationResult\r\n\r\n  const t = useTranslations(\r\n    'components.calculations.anti-overturning.postInterventionResult.widespreadIntervention',\r\n  )\r\n  const locale = useLocale()\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('panelFundamentalPeriod.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {panelFundamentalPeriod?.toLocaleString(locale, {\r\n              maximumFractionDigits: 4,\r\n            })}{' '}\r\n            s\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">{t('maximumAcceleration.label')}:</span>{' '}\r\n          <span>\r\n            {maximumAcceleration?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('overturningSeismicForce.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {overturningSeismicForce?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n            kN\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">{t('actingMoment.label')}:</span>{' '}\r\n          <span>\r\n            {actingMoment?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n            kNm\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">{t('midspanAxialStress.label')}:</span>{' '}\r\n          <span>\r\n            {midspanAxialStress?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n            kN\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">{t('normalStress.label')}:</span>{' '}\r\n          <span>\r\n            {normalStress?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n            MPa\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('unreinforcedSectionResistingMoment.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {unreinforcedSectionResistingMoment?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n            kNm/m\r\n          </span>\r\n        </div>\r\n        {regionTwoHypothesis && (\r\n          <div className=\"p-4 border rounded-lg\">\r\n            <h4 className=\"font-semibold mb-2\">\r\n              {t('regionHypothesis.regionTwoTitle')}\r\n            </h4>\r\n            <div>\r\n              <span className=\"font-medium\">\r\n                {t(\r\n                  'regionHypothesis.neutralAxisDistanceFromCompressedFlange.label',\r\n                )}\r\n                :\r\n              </span>{' '}\r\n              <span>\r\n                {regionTwoHypothesis?.neutralAxisDistanceFromCompressedFlange?.toLocaleString(\r\n                  locale,\r\n                  {\r\n                    maximumFractionDigits: 4,\r\n                  },\r\n                )}{' '}\r\n                mm\r\n              </span>\r\n            </div>\r\n            <div>\r\n              <span className=\"font-medium\">\r\n                {t('regionHypothesis.masonryDeformation.label')}:\r\n              </span>{' '}\r\n              <span>\r\n                {regionTwoHypothesis?.masonryDeformation?.toLocaleString(\r\n                  locale,\r\n                  {\r\n                    maximumFractionDigits: 3,\r\n                  },\r\n                )}{' '}\r\n              </span>\r\n            </div>\r\n            <div>\r\n              <span className=\"font-medium\">\r\n                {t(\r\n                  'regionHypothesis.masonryResultantCompressiveStresses.label',\r\n                )}\r\n                :\r\n              </span>{' '}\r\n              <span>\r\n                {regionTwoHypothesis?.masonryResultantCompressiveStresses?.toLocaleString(\r\n                  locale,\r\n                  {\r\n                    maximumFractionDigits: 3,\r\n                  },\r\n                )}{' '}\r\n                kN/m\r\n              </span>\r\n            </div>\r\n            <div>\r\n              <span className=\"font-medium\">\r\n                {t('regionHypothesis.frcmResultantTensileStresses.label')}:\r\n              </span>{' '}\r\n              <span>\r\n                {regionTwoHypothesis?.frcmResultantTensileStresses?.toLocaleString(\r\n                  locale,\r\n                  {\r\n                    maximumFractionDigits: 3,\r\n                  },\r\n                )}{' '}\r\n                kN/m\r\n              </span>\r\n            </div>\r\n            <div>\r\n              <span className=\"font-medium\">\r\n                {t(\r\n                  'regionHypothesis.designResistingMomentReinforcedSection.label',\r\n                )}\r\n                :\r\n              </span>{' '}\r\n              <span>\r\n                {regionTwoHypothesis?.designResistingMomentReinforcedSection?.toLocaleString(\r\n                  locale,\r\n                  {\r\n                    maximumFractionDigits: 3,\r\n                  },\r\n                )}{' '}\r\n                kNm/m\r\n              </span>\r\n            </div>\r\n            <div>\r\n              <span className=\"font-medium\">\r\n                {t('regionHypothesis.hypothesisCheck.label')}:\r\n              </span>{' '}\r\n              <span>{regionTwoHypothesis.hypothesisCheck ? 'ok' : 'no'}</span>\r\n            </div>\r\n          </div>\r\n        )}\r\n        {regionOneHypothesis && (\r\n          <div className=\"p-4 border rounded-lg\">\r\n            <h4 className=\"font-semibold mb-2\">\r\n              {t('regionHypothesis.regionOneTitle')}\r\n            </h4>\r\n            <div>\r\n              <span className=\"font-medium\">\r\n                {t(\r\n                  'regionHypothesis.neutralAxisDistanceFromCompressedFlange.label',\r\n                )}\r\n                :\r\n              </span>{' '}\r\n              <span>\r\n                {regionOneHypothesis?.neutralAxisDistanceFromCompressedFlange?.toLocaleString(\r\n                  locale,\r\n                  {\r\n                    maximumFractionDigits: 4,\r\n                  },\r\n                )}{' '}\r\n                mm\r\n              </span>\r\n            </div>\r\n            <div>\r\n              <span className=\"font-medium\">\r\n                {t('regionHypothesis.masonryDeformation.label')}:\r\n              </span>{' '}\r\n              <span>\r\n                {regionOneHypothesis?.masonryDeformation?.toLocaleString(\r\n                  locale,\r\n                  {\r\n                    maximumFractionDigits: 3,\r\n                  },\r\n                )}{' '}\r\n              </span>\r\n            </div>\r\n            <div>\r\n              <span className=\"font-medium\">\r\n                {t(\r\n                  'regionHypothesis.masonryResultantCompressiveStresses.label',\r\n                )}\r\n                :\r\n              </span>{' '}\r\n              <span>\r\n                {regionOneHypothesis?.masonryResultantCompressiveStresses?.toLocaleString(\r\n                  locale,\r\n                  {\r\n                    maximumFractionDigits: 3,\r\n                  },\r\n                )}{' '}\r\n                kN/m\r\n              </span>\r\n            </div>\r\n            <div>\r\n              <span className=\"font-medium\">\r\n                {t('regionHypothesis.frcmResultantTensileStresses.label')}:\r\n              </span>{' '}\r\n              <span>\r\n                {regionOneHypothesis?.frcmResultantTensileStresses?.toLocaleString(\r\n                  locale,\r\n                  {\r\n                    maximumFractionDigits: 3,\r\n                  },\r\n                )}{' '}\r\n                kN/m\r\n              </span>\r\n            </div>\r\n            <div>\r\n              <span className=\"font-medium\">\r\n                {t(\r\n                  'regionHypothesis.designResistingMomentReinforcedSection.label',\r\n                )}\r\n                :\r\n              </span>{' '}\r\n              <span>\r\n                {regionOneHypothesis?.designResistingMomentReinforcedSection?.toLocaleString(\r\n                  locale,\r\n                  {\r\n                    maximumFractionDigits: 3,\r\n                  },\r\n                )}{' '}\r\n                kNm/m\r\n              </span>\r\n            </div>\r\n            <div>\r\n              <span className=\"font-medium\">\r\n                {t('regionHypothesis.hypothesisCheck.label')}:\r\n              </span>{' '}\r\n              <span>{regionOneHypothesis.hypothesisCheck ? 'ok' : 'no'}</span>\r\n            </div>\r\n          </div>\r\n        )}\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('specificResistingMoment.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {specificResistingMoment?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n            kNm/m\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">{t('check.label')}:</span>{' '}\r\n          <Badge\r\n            className={cn(\r\n              'text-base px-3 py-1',\r\n              check ? 'bg-green-600' : 'bg-red-600',\r\n            )}\r\n          >\r\n            {check ? t('check.satisfied') : t('check.notSatisfied')}\r\n          </Badge>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AAEA;AAAA;;;;;;AAMO,SAAS,kEAAkE,EAChF,uCAAuC,EACjC;IACN,MAAM,EACJ,sBAAsB,EACtB,mBAAmB,EACnB,uBAAuB,EACvB,YAAY,EACZ,kBAAkB,EAClB,YAAY,EACZ,kCAAkC,EAClC,mBAAmB,EACnB,mBAAmB,EACnB,uBAAuB,EACvB,KAAK,EACN,GAAG;IAEJ,MAAM,IAAI,IAAA,yNAAe,EACvB;IAEF,MAAM,SAAS,IAAA,+KAAS;IAExB,qBACE,8OAAC,wIAAI;;0BACH,8OAAC,8IAAU;0BACT,cAAA,8OAAC,6IAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,8OAAC,+IAAW;gBAAC,WAAU;;kCACrB,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAgC;;;;;;;4BAC7B;0CACR,8OAAC;;oCACE,wBAAwB,eAAe,QAAQ;wCAC9C,uBAAuB;oCACzB;oCAAI;oCAAI;;;;;;;;;;;;;kCAIZ,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAA6B;;;;;;;4BAAS;0CACvE,8OAAC;0CACE,qBAAqB,eAAe,QAAQ;oCAC3C,uBAAuB;gCACzB;;;;;;;;;;;;kCAGJ,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAiC;;;;;;;4BAC9B;0CACR,8OAAC;;oCACE,yBAAyB,eAAe,QAAQ;wCAC/C,uBAAuB;oCACzB;oCAAI;oCAAI;;;;;;;;;;;;;kCAIZ,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAAsB;;;;;;;4BAAS;0CAChE,8OAAC;;oCACE,cAAc,eAAe,QAAQ;wCACpC,uBAAuB;oCACzB;oCAAI;oCAAI;;;;;;;;;;;;;kCAIZ,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAA4B;;;;;;;4BAAS;0CACtE,8OAAC;;oCACE,oBAAoB,eAAe,QAAQ;wCAC1C,uBAAuB;oCACzB;oCAAI;oCAAI;;;;;;;;;;;;;kCAIZ,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAAsB;;;;;;;4BAAS;0CAChE,8OAAC;;oCACE,cAAc,eAAe,QAAQ;wCACpC,uBAAuB;oCACzB;oCAAI;oCAAI;;;;;;;;;;;;;kCAIZ,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA4C;;;;;;;4BACzC;0CACR,8OAAC;;oCACE,oCAAoC,eAAe,QAAQ;wCAC1D,uBAAuB;oCACzB;oCAAI;oCAAI;;;;;;;;;;;;;oBAIX,qCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;;4CACb,EACC;4CACA;;;;;;;oCAEI;kDACR,8OAAC;;4CACE,qBAAqB,yCAAyC,eAC7D,QACA;gDACE,uBAAuB;4CACzB;4CACC;4CAAI;;;;;;;;;;;;;0CAIX,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;;4CACb,EAAE;4CAA6C;;;;;;;oCAC1C;kDACR,8OAAC;;4CACE,qBAAqB,oBAAoB,eACxC,QACA;gDACE,uBAAuB;4CACzB;4CACC;;;;;;;;;;;;;0CAGP,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;;4CACb,EACC;4CACA;;;;;;;oCAEI;kDACR,8OAAC;;4CACE,qBAAqB,qCAAqC,eACzD,QACA;gDACE,uBAAuB;4CACzB;4CACC;4CAAI;;;;;;;;;;;;;0CAIX,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;;4CACb,EAAE;4CAAuD;;;;;;;oCACpD;kDACR,8OAAC;;4CACE,qBAAqB,8BAA8B,eAClD,QACA;gDACE,uBAAuB;4CACzB;4CACC;4CAAI;;;;;;;;;;;;;0CAIX,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;;4CACb,EACC;4CACA;;;;;;;oCAEI;kDACR,8OAAC;;4CACE,qBAAqB,wCAAwC,eAC5D,QACA;gDACE,uBAAuB;4CACzB;4CACC;4CAAI;;;;;;;;;;;;;0CAIX,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;;4CACb,EAAE;4CAA0C;;;;;;;oCACvC;kDACR,8OAAC;kDAAM,oBAAoB,eAAe,GAAG,OAAO;;;;;;;;;;;;;;;;;;oBAIzD,qCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;;4CACb,EACC;4CACA;;;;;;;oCAEI;kDACR,8OAAC;;4CACE,qBAAqB,yCAAyC,eAC7D,QACA;gDACE,uBAAuB;4CACzB;4CACC;4CAAI;;;;;;;;;;;;;0CAIX,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;;4CACb,EAAE;4CAA6C;;;;;;;oCAC1C;kDACR,8OAAC;;4CACE,qBAAqB,oBAAoB,eACxC,QACA;gDACE,uBAAuB;4CACzB;4CACC;;;;;;;;;;;;;0CAGP,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;;4CACb,EACC;4CACA;;;;;;;oCAEI;kDACR,8OAAC;;4CACE,qBAAqB,qCAAqC,eACzD,QACA;gDACE,uBAAuB;4CACzB;4CACC;4CAAI;;;;;;;;;;;;;0CAIX,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;;4CACb,EAAE;4CAAuD;;;;;;;oCACpD;kDACR,8OAAC;;4CACE,qBAAqB,8BAA8B,eAClD,QACA;gDACE,uBAAuB;4CACzB;4CACC;4CAAI;;;;;;;;;;;;;0CAIX,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;;4CACb,EACC;4CACA;;;;;;;oCAEI;kDACR,8OAAC;;4CACE,qBAAqB,wCAAwC,eAC5D,QACA;gDACE,uBAAuB;4CACzB;4CACC;4CAAI;;;;;;;;;;;;;0CAIX,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;;4CACb,EAAE;4CAA0C;;;;;;;oCACvC;kDACR,8OAAC;kDAAM,oBAAoB,eAAe,GAAG,OAAO;;;;;;;;;;;;;;;;;;kCAI1D,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAiC;;;;;;;4BAC9B;0CACR,8OAAC;;oCACE,yBAAyB,eAAe,QAAQ;wCAC/C,uBAAuB;oCACzB;oCAAI;oCAAI;;;;;;;;;;;;;kCAIZ,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAAe;;;;;;;4BAAS;0CACzD,8OAAC,0IAAK;gCACJ,WAAW,IAAA,yHAAE,EACX,uBACA,QAAQ,iBAAiB;0CAG1B,QAAQ,EAAE,qBAAqB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAM9C", "debugId": null}}, {"offset": {"line": 943, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-post-intervention-calculation-result.tsx"], "sourcesContent": ["import {\r\n  Tabs,\r\n  Ta<PERSON>Content,\r\n  Ta<PERSON>List,\r\n  TabsTrigger,\r\n} from '@atlas/components/ui/tabs'\r\nimport type { MasonryAntiOverturningPostInterventionCalculationResultSchemaInputs } from '@atlas/types/schemas/masonry/antioverturning-form'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\nimport { MasonryAntiOverturningPerimeterAndWidespreadInterventionCalculationResultCard } from './perimeter-and-widespread-intervention-calculation-result-card'\r\nimport { MasonryAntiOverturningWidespreadInterventionCalculationResultCard } from './widespread-intervention-calculation-result-card'\r\n\r\ntype Props = {\r\n  postInterventionCalculationResult: MasonryAntiOverturningPostInterventionCalculationResultSchemaInputs\r\n}\r\n\r\nexport function MasonryAntiOverturningPostInterventionCalculationResult({\r\n  postInterventionCalculationResult,\r\n}: Props) {\r\n  const t = useTranslations(\r\n    'components.calculations.anti-overturning.postInterventionResult',\r\n  )\r\n  const _locale = useLocale()\r\n\r\n  return (\r\n    <>\r\n      <h3 className=\"text-lg font-medium py-4\">{t('title')}</h3>\r\n      <Tabs defaultValue=\"perimeterAndWidespreadIntervention\">\r\n        <TabsList>\r\n          <TabsTrigger value=\"perimeterAndWidespreadIntervention\">\r\n            {t('perimeterAndWidespreadIntervention.label')}\r\n          </TabsTrigger>\r\n          <TabsTrigger value=\"widespreadIntervention\">\r\n            {t('widespreadIntervention.label')}\r\n          </TabsTrigger>\r\n        </TabsList>\r\n        <TabsContent value=\"perimeterAndWidespreadIntervention\">\r\n          <MasonryAntiOverturningPerimeterAndWidespreadInterventionCalculationResultCard\r\n            perimeterAndWidespreadInterventionCalculationResult={\r\n              postInterventionCalculationResult.perimeterAndWidespreadInterventionCalculationResult ??\r\n              {}\r\n            }\r\n          />\r\n        </TabsContent>\r\n        <TabsContent value=\"widespreadIntervention\">\r\n          <MasonryAntiOverturningWidespreadInterventionCalculationResultCard\r\n            widespreadInterventionCalculationResult={\r\n              postInterventionCalculationResult.widespreadInterventionCalculationResult ??\r\n              {}\r\n            }\r\n          />\r\n        </TabsContent>\r\n      </Tabs>\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAOA;AAAA;AACA;AACA;;;;;;AAMO,SAAS,wDAAwD,EACtE,iCAAiC,EAC3B;IACN,MAAM,IAAI,IAAA,yNAAe,EACvB;IAEF,MAAM,UAAU,IAAA,+KAAS;IAEzB,qBACE;;0BACE,8OAAC;gBAAG,WAAU;0BAA4B,EAAE;;;;;;0BAC5C,8OAAC,wIAAI;gBAAC,cAAa;;kCACjB,8OAAC,4IAAQ;;0CACP,8OAAC,+IAAW;gCAAC,OAAM;0CAChB,EAAE;;;;;;0CAEL,8OAAC,+IAAW;gCAAC,OAAM;0CAChB,EAAE;;;;;;;;;;;;kCAGP,8OAAC,+IAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,kYAA6E;4BAC5E,qDACE,kCAAkC,mDAAmD,IACrF,CAAC;;;;;;;;;;;kCAIP,8OAAC,+IAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,kWAAiE;4BAChE,yCACE,kCAAkC,uCAAuC,IACzE,CAAC;;;;;;;;;;;;;;;;;;;AAOf", "debugId": null}}, {"offset": {"line": 1039, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx"], "sourcesContent": ["import { CustomProductSection } from '@atlas/components/common/atlas/product/custom-product-section'\r\nimport { ProductSummaryCard } from '@atlas/components/common/atlas/product/product-summary-card'\r\nimport { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { productFiberType } from '@atlas/constants/product'\r\nimport type { ModuleWithParamsAntiOverturning } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useModuleCalculation } from '@atlas/lib/mutation/modules/use-module-calculation'\r\nimport { useProductsByCategory } from '@atlas/lib/query/products/use-products-by-category'\r\nimport {\r\n  type MasonryAntiOverturningCompositeReinforcementSystemSchemaInputs,\r\n  masonryAntiOverturningCompositeReinforcementSystemSchema,\r\n} from '@atlas/types/schemas/masonry/antioverturning-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Separator } from '@radix-ui/react-dropdown-menu'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useMemo } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\nimport { MasonryAntiOverturningPostInterventionCalculationResult } from './anti-overturning-post-intervention-calculation-result'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  module: ModuleWithParamsAntiOverturning\r\n}\r\n\r\nexport const MasonryAntiOverturningCompositeReinforcementSystemCalculationForm =\r\n  ({ session, projectId, module }: Props) => {\r\n    const t = useTranslations(\r\n      'forms.calculations.anti-overturning.composite-reinforcement-system',\r\n    )\r\n    const tAction = useTranslations(\r\n      'actions.calculations.anti-overturning.composite-reinforcement-system',\r\n    )\r\n    const tCommon = useTranslations('actions.common')\r\n\r\n    const postInterventionCalculationResult =\r\n      module.postInterventionCalculationResult\r\n    const defaultValues = module?.compositeReinforcementSystemVerifyInput\r\n\r\n    const meshProduct =\r\n      module?.compositeReinforcementSystemVerifyInput?.meshInput?.meshProduct\r\n    const connectorProduct =\r\n      module?.compositeReinforcementSystemVerifyInput?.connectorInput\r\n        ?.connectorProduct\r\n    const matrixProduct =\r\n      module?.compositeReinforcementSystemVerifyInput?.matrixInput\r\n        ?.matrixProduct\r\n\r\n    const form =\r\n      useForm<MasonryAntiOverturningCompositeReinforcementSystemSchemaInputs>({\r\n        resolver: zodResolver(\r\n          masonryAntiOverturningCompositeReinforcementSystemSchema,\r\n        ),\r\n        defaultValues: {\r\n          calculationType: 'POST_INTERVENTION_VERIFY',\r\n          input: {\r\n            meshInput: {\r\n              // to be calculated: D65*D64*D69\r\n              // D65 : thickness, D64: width, D69: reinforced sides number\r\n              resistantAreaPerMeter:\r\n                defaultValues?.meshInput?.resistantAreaPerMeter ??\r\n                (meshProduct?.thickness ?? 0) *\r\n                  (meshProduct?.width ?? 0) *\r\n                  (defaultValues?.meshInput?.reinforcedSidesNumber ?? 0),\r\n              reinforcedSidesNumber:\r\n                defaultValues?.meshInput?.reinforcedSidesNumber ?? 0,\r\n              meshProduct: {\r\n                id: meshProduct?.id ?? '',\r\n                name: meshProduct?.name,\r\n                sourceType:\r\n                  meshProduct?.id === 'CUSTOM' ? 'CUSTOM' : 'DATABASE',\r\n                thickness: meshProduct?.thickness ?? 0,\r\n                tensileStrength: meshProduct?.tensileStrength ?? 0,\r\n                elasticModulus: meshProduct?.elasticModulus ?? 0,\r\n                fiberType: meshProduct?.fiberType ?? productFiberType.CARBON,\r\n              },\r\n            },\r\n            connectorInput: {\r\n              connectorSpacing:\r\n                defaultValues?.connectorInput?.connectorSpacing ?? 0,\r\n              connectorProduct: {\r\n                id: connectorProduct?.id ?? '',\r\n                name: connectorProduct?.name,\r\n                sourceType:\r\n                  connectorProduct?.id === 'custom' ? 'CUSTOM' : 'DATABASE',\r\n                thickness: connectorProduct?.thickness ?? 0,\r\n                tensileStrength: connectorProduct?.tensileStrength ?? 0,\r\n                elasticModulus: connectorProduct?.elasticModulus ?? 0,\r\n                fiberType:\r\n                  connectorProduct?.fiberType ?? productFiberType.CARBON,\r\n              },\r\n            },\r\n            matrixInput: {\r\n              compositeSystemThickness:\r\n                defaultValues?.matrixInput?.compositeSystemThickness ?? 0,\r\n              matrixProduct: {\r\n                id: matrixProduct?.id ?? '',\r\n                name: matrixProduct?.name ?? '',\r\n                sourceType:\r\n                  matrixProduct?.id === 'custom' ? 'CUSTOM' : 'DATABASE',\r\n              },\r\n            },\r\n          },\r\n        },\r\n      })\r\n\r\n    const { mutate, isPending } = useModuleCalculation(session.accessToken, {\r\n      onSuccess: () => {\r\n        toast.success(tAction('calculate.success'))\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('calculate.failure', { error: error.message }))\r\n      },\r\n    })\r\n\r\n    const handleFormSubmit = (\r\n      body: MasonryAntiOverturningCompositeReinforcementSystemSchemaInputs,\r\n    ) => {\r\n      mutate({ projectId, moduleId: module.id, body })\r\n    }\r\n\r\n    const {\r\n      data: productsAll,\r\n      isError: errorGettingProducts,\r\n      isLoading: isLoadingProducts,\r\n    } = useProductsByCategory(session, 'ANTI_OVERTURNING', 0, 100)\r\n\r\n    const productsMeshOptions = [\r\n      ...(productsAll?.content\r\n        .filter(m => m.productType === 'MESH')\r\n        .map(m => ({\r\n          value: m.id,\r\n          label: m.name ?? t('product.unnamed'),\r\n        })) ?? []),\r\n      // temporary disable\r\n      // { value: 'custom', label: t('product.custom') },\r\n    ]\r\n\r\n    const productsConnectorOptions = [\r\n      ...(productsAll?.content\r\n        .filter(m => m.productType === 'CONNECTOR')\r\n        .map(m => ({\r\n          value: m.id,\r\n          label: m.name ?? t('product.unnamed'),\r\n        })) ?? []),\r\n      // temporary disable\r\n      // { value: 'custom', label: t('product.custom') },\r\n    ]\r\n\r\n    const productsMatrixOptions = [\r\n      ...(productsAll?.content\r\n        .filter(m => m.productType === 'MATRIX')\r\n        .map(m => ({\r\n          value: m.id,\r\n          label: m.name ?? t('product.unnamed'),\r\n        })) ?? []),\r\n      // temporary disable\r\n      // { value: 'custom', label: t('product.custom') },\r\n    ]\r\n\r\n    const [productMeshId] = form.watch(['input.meshInput.meshProduct.id'])\r\n    const [productConnectorId] = form.watch([\r\n      'input.connectorInput.connectorProduct.id',\r\n    ])\r\n    const [productMatrixId] = form.watch(['input.matrixInput.matrixProduct.id'])\r\n\r\n    const selectedProductMesh = useMemo(\r\n      () =>\r\n        productsAll?.content\r\n          .filter(m => m.productType === 'MESH')\r\n          .find(p => p.id === productMeshId),\r\n      [productMeshId, productsAll],\r\n    )\r\n\r\n    const selectedProductConnector = useMemo(\r\n      () =>\r\n        productsAll?.content\r\n          .filter(m => m.productType === 'CONNECTOR')\r\n          .find(p => p.id === productConnectorId),\r\n      [productConnectorId, productsAll],\r\n    )\r\n\r\n    const selectedProductMatrix = useMemo(\r\n      () =>\r\n        productsAll?.content\r\n          .filter(m => m.productType === 'MATRIX')\r\n          .find(p => p.id === productMatrixId),\r\n      [productMatrixId, productsAll],\r\n    )\r\n\r\n    const reinforcedSidesNumberValue = form.watch(\r\n      'input.meshInput.reinforcedSidesNumber',\r\n    )\r\n\r\n    useEffect(() => {\r\n      if (productMeshId === 'custom') {\r\n        form.setValue('input.meshInput.meshProduct.sourceType', 'CUSTOM')\r\n      }\r\n      if (productConnectorId === 'custom') {\r\n        form.setValue(\r\n          'input.connectorInput.connectorProduct.sourceType',\r\n          'CUSTOM',\r\n        )\r\n      }\r\n      if (productMatrixId === 'custom') {\r\n        form.setValue('input.matrixInput.matrixProduct.sourceType', 'CUSTOM')\r\n      }\r\n\r\n      if (selectedProductMesh) {\r\n        form.setValue('input.meshInput.meshProduct', {\r\n          id: selectedProductMesh.id,\r\n          name: selectedProductMesh.name,\r\n          sourceType: 'DATABASE',\r\n        })\r\n\r\n        form.setValue(\r\n          'input.meshInput.resistantAreaPerMeter',\r\n          (selectedProductMesh?.thickness ?? 0) *\r\n            (selectedProductMesh?.width ??\r\n              selectedProductMesh.availableWidths?.[0] ??\r\n              0) *\r\n            (reinforcedSidesNumberValue ?? 0),\r\n        )\r\n      }\r\n\r\n      if (selectedProductConnector) {\r\n        form.setValue('input.connectorInput.connectorProduct', {\r\n          id: selectedProductConnector.id,\r\n          name: selectedProductConnector.name,\r\n          sourceType: 'DATABASE',\r\n        })\r\n      }\r\n\r\n      if (selectedProductMatrix) {\r\n        form.setValue('input.matrixInput.matrixProduct', {\r\n          id: selectedProductMatrix.id,\r\n          name: selectedProductMatrix.name,\r\n          sourceType: 'DATABASE',\r\n        })\r\n      }\r\n    }, [\r\n      form,\r\n      productMeshId,\r\n      selectedProductMesh,\r\n      selectedProductConnector,\r\n      selectedProductMatrix,\r\n      reinforcedSidesNumberValue,\r\n      productConnectorId,\r\n      productMatrixId,\r\n    ])\r\n\r\n    return (\r\n      <div className=\"flex flex-col justify-center gap-4\">\r\n        <Form {...form}>\r\n          <form\r\n            className=\"space-y-4 rounded-md border p-4\"\r\n            onSubmit={form.handleSubmit(handleFormSubmit)}\r\n          >\r\n            <Image\r\n              src=\"/assets/masonry-anti-overturning/antibaltamento-verify.jpg\"\r\n              alt=\"composite reinforcement system verify\"\r\n              height={250}\r\n              width={500}\r\n              className=\"mx-auto rounded-md object-contain\"\r\n              priority\r\n            />\r\n            {/* Mesh Section */}\r\n            <h1 className=\"text-xl font-bold\">{t('mesh-sub-heading')}</h1>\r\n            <SelectFormInput\r\n              control={form.control}\r\n              name=\"input.meshInput.meshProduct.id\"\r\n              options={productsMeshOptions}\r\n              t={t}\r\n              loading={isLoadingProducts}\r\n              requestError={errorGettingProducts}\r\n              // get the error message from the form state if any\r\n              errorMessage={\r\n                form.formState.errors.input?.meshInput?.meshProduct?.id?.message\r\n              }\r\n            />\r\n            {productMeshId === 'custom' && <CustomProductSection />}\r\n            {selectedProductMesh && (\r\n              <ProductSummaryCard\r\n                product={selectedProductMesh}\r\n                isRectangularBeam={false}\r\n              />\r\n            )}\r\n            <NumberFormInput\r\n              control={form.control}\r\n              name=\"input.meshInput.reinforcedSidesNumber\"\r\n              t={t}\r\n            />\r\n            <NumberFormInput\r\n              control={form.control}\r\n              name=\"input.meshInput.resistantAreaPerMeter\"\r\n              t={t}\r\n              disabled={true}\r\n            />\r\n            <Separator />\r\n            {/* Connector Section */}\r\n            <h1 className=\"text-xl font-bold\">{t('connector-sub-heading')}</h1>\r\n            <SelectFormInput\r\n              control={form.control}\r\n              name=\"input.connectorInput.connectorProduct.id\"\r\n              options={productsConnectorOptions}\r\n              t={t}\r\n              loading={isLoadingProducts}\r\n              requestError={errorGettingProducts}\r\n              errorMessage={\r\n                form.formState.errors.input?.connectorInput?.connectorProduct\r\n                  ?.id?.message\r\n              }\r\n            />\r\n            {productConnectorId === 'custom' && <CustomProductSection />}\r\n            {selectedProductConnector && (\r\n              <ProductSummaryCard\r\n                product={selectedProductConnector}\r\n                facingMaterial={\r\n                  module?.params?.materialProperties?.facingMaterial\r\n                }\r\n                isRectangularBeam={false}\r\n              />\r\n            )}\r\n            <NumberFormInput\r\n              control={form.control}\r\n              name=\"input.connectorInput.connectorSpacing\"\r\n              t={t}\r\n            />\r\n            <Separator />\r\n            {/* Matrix Section */}\r\n            <h1 className=\"text-xl font-bold\">{t('matrix-sub-heading')}</h1>\r\n            <SelectFormInput\r\n              control={form.control}\r\n              name=\"input.matrixInput.matrixProduct.id\"\r\n              options={productsMatrixOptions}\r\n              t={t}\r\n              loading={isLoadingProducts}\r\n              requestError={errorGettingProducts}\r\n              errorMessage={\r\n                form.formState.errors.input?.matrixInput?.matrixProduct?.id\r\n                  ?.message\r\n              }\r\n            />\r\n            {productMatrixId === 'custom' && <CustomProductSection />}\r\n            {selectedProductMatrix && (\r\n              <ProductSummaryCard\r\n                product={selectedProductMatrix}\r\n                isRectangularBeam={false}\r\n              />\r\n            )}\r\n            <NumberFormInput\r\n              control={form.control}\r\n              name=\"input.matrixInput.compositeSystemThickness\"\r\n              t={t}\r\n            />\r\n            <Button\r\n              type=\"submit\"\r\n              className=\"w-full sm:w-auto\"\r\n              disabled={isPending}\r\n            >\r\n              {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n              {tCommon('calculate')}\r\n            </Button>\r\n          </form>\r\n        </Form>\r\n        {postInterventionCalculationResult && (\r\n          <MasonryAntiOverturningPostInterventionCalculationResult\r\n            postInterventionCalculationResult={\r\n              postInterventionCalculationResult\r\n            }\r\n          />\r\n        )}\r\n      </div>\r\n    )\r\n  }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAIA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;AAQO,MAAM,oEACX,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAS;IACpC,MAAM,IAAI,IAAA,yNAAe,EACvB;IAEF,MAAM,UAAU,IAAA,yNAAe,EAC7B;IAEF,MAAM,UAAU,IAAA,yNAAe,EAAC;IAEhC,MAAM,oCACJ,OAAO,iCAAiC;IAC1C,MAAM,gBAAgB,QAAQ;IAE9B,MAAM,cACJ,QAAQ,yCAAyC,WAAW;IAC9D,MAAM,mBACJ,QAAQ,yCAAyC,gBAC7C;IACN,MAAM,gBACJ,QAAQ,yCAAyC,aAC7C;IAEN,MAAM,OACJ,IAAA,yKAAO,EAAiE;QACtE,UAAU,IAAA,6KAAW,EACnB,yNAAwD;QAE1D,eAAe;YACb,iBAAiB;YACjB,OAAO;gBACL,WAAW;oBACT,gCAAgC;oBAChC,4DAA4D;oBAC5D,uBACE,eAAe,WAAW,yBAC1B,CAAC,aAAa,aAAa,CAAC,IAC1B,CAAC,aAAa,SAAS,CAAC,IACxB,CAAC,eAAe,WAAW,yBAAyB,CAAC;oBACzD,uBACE,eAAe,WAAW,yBAAyB;oBACrD,aAAa;wBACX,IAAI,aAAa,MAAM;wBACvB,MAAM,aAAa;wBACnB,YACE,aAAa,OAAO,WAAW,WAAW;wBAC5C,WAAW,aAAa,aAAa;wBACrC,iBAAiB,aAAa,mBAAmB;wBACjD,gBAAgB,aAAa,kBAAkB;wBAC/C,WAAW,aAAa,aAAa,gJAAgB,CAAC,MAAM;oBAC9D;gBACF;gBACA,gBAAgB;oBACd,kBACE,eAAe,gBAAgB,oBAAoB;oBACrD,kBAAkB;wBAChB,IAAI,kBAAkB,MAAM;wBAC5B,MAAM,kBAAkB;wBACxB,YACE,kBAAkB,OAAO,WAAW,WAAW;wBACjD,WAAW,kBAAkB,aAAa;wBAC1C,iBAAiB,kBAAkB,mBAAmB;wBACtD,gBAAgB,kBAAkB,kBAAkB;wBACpD,WACE,kBAAkB,aAAa,gJAAgB,CAAC,MAAM;oBAC1D;gBACF;gBACA,aAAa;oBACX,0BACE,eAAe,aAAa,4BAA4B;oBAC1D,eAAe;wBACb,IAAI,eAAe,MAAM;wBACzB,MAAM,eAAe,QAAQ;wBAC7B,YACE,eAAe,OAAO,WAAW,WAAW;oBAChD;gBACF;YACF;QACF;IACF;IAEF,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,0LAAoB,EAAC,QAAQ,WAAW,EAAE;QACtE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;QACxB;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,qBAAqB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAClE;IACF;IAEA,MAAM,mBAAmB,CACvB;QAEA,OAAO;YAAE;YAAW,UAAU,OAAO,EAAE;YAAE;QAAK;IAChD;IAEA,MAAM,EACJ,MAAM,WAAW,EACjB,SAAS,oBAAoB,EAC7B,WAAW,iBAAiB,EAC7B,GAAG,IAAA,8LAAqB,EAAC,SAAS,oBAAoB,GAAG;IAE1D,MAAM,sBAAsB;WACtB,aAAa,QACd,OAAO,CAAA,IAAK,EAAE,WAAW,KAAK,QAC9B,IAAI,CAAA,IAAK,CAAC;gBACT,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,IAAI,IAAI,EAAE;YACrB,CAAC,MAAM,EAAE;KAGZ;IAED,MAAM,2BAA2B;WAC3B,aAAa,QACd,OAAO,CAAA,IAAK,EAAE,WAAW,KAAK,aAC9B,IAAI,CAAA,IAAK,CAAC;gBACT,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,IAAI,IAAI,EAAE;YACrB,CAAC,MAAM,EAAE;KAGZ;IAED,MAAM,wBAAwB;WACxB,aAAa,QACd,OAAO,CAAA,IAAK,EAAE,WAAW,KAAK,UAC9B,IAAI,CAAA,IAAK,CAAC;gBACT,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,IAAI,IAAI,EAAE;YACrB,CAAC,MAAM,EAAE;KAGZ;IAED,MAAM,CAAC,cAAc,GAAG,KAAK,KAAK,CAAC;QAAC;KAAiC;IACrE,MAAM,CAAC,mBAAmB,GAAG,KAAK,KAAK,CAAC;QACtC;KACD;IACD,MAAM,CAAC,gBAAgB,GAAG,KAAK,KAAK,CAAC;QAAC;KAAqC;IAE3E,MAAM,sBAAsB,IAAA,gNAAO,EACjC,IACE,aAAa,QACV,OAAO,CAAA,IAAK,EAAE,WAAW,KAAK,QAC9B,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK,gBACxB;QAAC;QAAe;KAAY;IAG9B,MAAM,2BAA2B,IAAA,gNAAO,EACtC,IACE,aAAa,QACV,OAAO,CAAA,IAAK,EAAE,WAAW,KAAK,aAC9B,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK,qBACxB;QAAC;QAAoB;KAAY;IAGnC,MAAM,wBAAwB,IAAA,gNAAO,EACnC,IACE,aAAa,QACV,OAAO,CAAA,IAAK,EAAE,WAAW,KAAK,UAC9B,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK,kBACxB;QAAC;QAAiB;KAAY;IAGhC,MAAM,6BAA6B,KAAK,KAAK,CAC3C;IAGF,IAAA,kNAAS,EAAC;QACR,IAAI,kBAAkB,UAAU;YAC9B,KAAK,QAAQ,CAAC,0CAA0C;QAC1D;QACA,IAAI,uBAAuB,UAAU;YACnC,KAAK,QAAQ,CACX,oDACA;QAEJ;QACA,IAAI,oBAAoB,UAAU;YAChC,KAAK,QAAQ,CAAC,8CAA8C;QAC9D;QAEA,IAAI,qBAAqB;YACvB,KAAK,QAAQ,CAAC,+BAA+B;gBAC3C,IAAI,oBAAoB,EAAE;gBAC1B,MAAM,oBAAoB,IAAI;gBAC9B,YAAY;YACd;YAEA,KAAK,QAAQ,CACX,yCACA,CAAC,qBAAqB,aAAa,CAAC,IAClC,CAAC,qBAAqB,SACpB,oBAAoB,eAAe,EAAE,CAAC,EAAE,IACxC,CAAC,IACH,CAAC,8BAA8B,CAAC;QAEtC;QAEA,IAAI,0BAA0B;YAC5B,KAAK,QAAQ,CAAC,yCAAyC;gBACrD,IAAI,yBAAyB,EAAE;gBAC/B,MAAM,yBAAyB,IAAI;gBACnC,YAAY;YACd;QACF;QAEA,IAAI,uBAAuB;YACzB,KAAK,QAAQ,CAAC,mCAAmC;gBAC/C,IAAI,sBAAsB,EAAE;gBAC5B,MAAM,sBAAsB,IAAI;gBAChC,YAAY;YACd;QACF;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,wIAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,8OAAC;oBACC,WAAU;oBACV,UAAU,KAAK,YAAY,CAAC;;sCAE5B,8OAAC,wIAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,QAAQ;4BACR,OAAO;4BACP,WAAU;4BACV,QAAQ;;;;;;sCAGV,8OAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS;4BACT,GAAG;4BACH,SAAS;4BACT,cAAc;4BACd,mDAAmD;4BACnD,cACE,KAAK,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,WAAW,aAAa,IAAI;;;;;;wBAG5D,kBAAkB,0BAAY,8OAAC,wMAAoB;;;;;wBACnD,qCACC,8OAAC,oMAAkB;4BACjB,SAAS;4BACT,mBAAmB;;;;;;sCAGvB,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,yLAAS;;;;;sCAEV,8OAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS;4BACT,GAAG;4BACH,SAAS;4BACT,cAAc;4BACd,cACE,KAAK,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,gBAAgB,kBACzC,IAAI;;;;;;wBAGX,uBAAuB,0BAAY,8OAAC,wMAAoB;;;;;wBACxD,0CACC,8OAAC,oMAAkB;4BACjB,SAAS;4BACT,gBACE,QAAQ,QAAQ,oBAAoB;4BAEtC,mBAAmB;;;;;;sCAGvB,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,yLAAS;;;;;sCAEV,8OAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS;4BACT,GAAG;4BACH,SAAS;4BACT,cAAc;4BACd,cACE,KAAK,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,aAAa,eAAe,IACrD;;;;;;wBAGP,oBAAoB,0BAAY,8OAAC,wMAAoB;;;;;wBACrD,uCACC,8OAAC,oMAAkB;4BACjB,SAAS;4BACT,mBAAmB;;;;;;sCAGvB,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,4IAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU;;gCAET,2BAAa,8OAAC,oOAAW;oCAAC,WAAU;;;;;;gCACpC,QAAQ;;;;;;;;;;;;;;;;;;YAId,mDACC,8OAAC,iWAAuD;gBACtD,mCACE;;;;;;;;;;;;AAMZ", "debugId": null}}, {"offset": {"line": 1455, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  checkResult: boolean\r\n}\r\n\r\nexport function MasonryAntiOverturningPreInterventionCalculationResultCard({\r\n  checkResult,\r\n}: Props) {\r\n  const t = useTranslations(\r\n    'forms.project-params.anti-overturning.pre-intervention',\r\n  )\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('infillOverturningCheck.title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <div>\r\n          <span className=\"font-medium\">{t('checkResult.label')}:</span>{' '}\r\n          <Badge\r\n            className={cn(\r\n              'text-base px-3 py-1',\r\n              checkResult ? 'bg-green-600' : 'bg-red-600',\r\n            )}\r\n          >\r\n            {checkResult\r\n              ? t('checkResult.satisfied')\r\n              : t('checkResult.notSatisfied')}\r\n          </Badge>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AACA;;;;;;AAMO,SAAS,2DAA2D,EACzE,WAAW,EACL;IACN,MAAM,IAAI,IAAA,yNAAe,EACvB;IAGF,qBACE,8OAAC,wIAAI;;0BACH,8OAAC,8IAAU;0BACT,cAAA,8OAAC,6IAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,8OAAC,+IAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;;sCACC,8OAAC;4BAAK,WAAU;;gCAAe,EAAE;gCAAqB;;;;;;;wBAAS;sCAC/D,8OAAC,0IAAK;4BACJ,WAAW,IAAA,yHAAE,EACX,uBACA,cAAc,iBAAiB;sCAGhC,cACG,EAAE,2BACF,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAMlB", "debugId": null}}, {"offset": {"line": 1532, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { masonryAntiOverturningPreInterventionCalculationResultSchema } from '@atlas/lib/api/modules/schemas/masonry-antioverturning-params'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { MasonryAntiOverturningPreInterventionCalculationResultSchemaInputs } from '@atlas/types/schemas/masonry/antioverturning-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useForm } from 'react-hook-form'\r\nimport { MasonryAntiOverturningPreInterventionCalculationResultCard } from './anti-overturning-pre-intervention-calculation-result-card'\r\n\r\ntype Props = {\r\n  preInterventionCalculationResult?: MasonryAntiOverturningPreInterventionCalculationResultSchemaInputs\r\n  onSave: () => void\r\n}\r\n\r\nexport const MasonryAntiOverturningPreInterventionCalculationResultsForm = ({\r\n  preInterventionCalculationResult,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.project-params.anti-overturning.pre-intervention',\r\n  )\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  const isPreInterventionCalculated = !!preInterventionCalculationResult\r\n\r\n  const form =\r\n    useForm<MasonryAntiOverturningPreInterventionCalculationResultSchemaInputs>(\r\n      {\r\n        resolver: zodResolver(\r\n          masonryAntiOverturningPreInterventionCalculationResultSchema,\r\n        ),\r\n        defaultValues: {\r\n          overturningMoment:\r\n            preInterventionCalculationResult?.overturningMoment ?? 0,\r\n          seismicRiskIndicator:\r\n            preInterventionCalculationResult?.seismicRiskIndicator ?? 0,\r\n          seismicAccelerationCorrespondingToRiskIndicator:\r\n            preInterventionCalculationResult?.seismicAccelerationCorrespondingToRiskIndicator ??\r\n            0,\r\n          stabilizingMomentExternalFacing:\r\n            preInterventionCalculationResult?.stabilizingMomentExternalFacing ??\r\n            0,\r\n          check: preInterventionCalculationResult?.check ?? false,\r\n        },\r\n      },\r\n    )\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className={`space-y-4 rounded-md border p-4 ${!isPreInterventionCalculated ? 'border-red-500' : ''}`}\r\n        onSubmit={form.handleSubmit(onSave)}\r\n      >\r\n        {!isPreInterventionCalculated && (\r\n          <div className=\"mb-4 p-2 rounded bg-red-100 text-red-700 border border-red-300\">\r\n            {t('notCalculated')}\r\n          </div>\r\n        )}\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"overturningMoment\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"stabilizingMomentExternalFacing\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <MasonryAntiOverturningPreInterventionCalculationResultCard\r\n          checkResult={preInterventionCalculationResult?.check || false}\r\n        />\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('seismicRiskIndicator.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {Math.round(\r\n              Number(preInterventionCalculationResult?.seismicRiskIndicator) *\r\n                1000,\r\n            ) / 1000}{' '}\r\n            %\r\n          </span>\r\n          <p\r\n            data-slot=\"form-description\"\r\n            id={'stabilizingMomentExternalFacingId'}\r\n            className={cn('text-muted-foreground text-sm')}\r\n          >\r\n            {t('seismicRiskIndicator.description')}\r\n          </p>\r\n        </div>\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"seismicAccelerationCorrespondingToRiskIndicator\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={!isPreInterventionCalculated}\r\n          onClick={form.handleSubmit(onSave)}\r\n        >\r\n          {tCommon('next')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;AAOO,MAAM,8DAA8D,CAAC,EAC1E,gCAAgC,EAChC,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EACvB;IAEF,MAAM,UAAU,IAAA,yNAAe,EAAC;IAEhC,MAAM,8BAA8B,CAAC,CAAC;IAEtC,MAAM,OACJ,IAAA,yKAAO,EACL;QACE,UAAU,IAAA,6KAAW,EACnB,+OAA4D;QAE9D,eAAe;YACb,mBACE,kCAAkC,qBAAqB;YACzD,sBACE,kCAAkC,wBAAwB;YAC5D,iDACE,kCAAkC,mDAClC;YACF,iCACE,kCAAkC,mCAClC;YACF,OAAO,kCAAkC,SAAS;QACpD;IACF;IAGJ,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAW,CAAC,gCAAgC,EAAE,CAAC,8BAA8B,mBAAmB,IAAI;YACpG,UAAU,KAAK,YAAY,CAAC;;gBAE3B,CAAC,6CACA,8OAAC;oBAAI,WAAU;8BACZ,EAAE;;;;;;8BAGP,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,2WAA0D;oBACzD,aAAa,kCAAkC,SAAS;;;;;;8BAE1D,8OAAC;;sCACC,8OAAC;4BAAK,WAAU;;gCACb,EAAE;gCAA8B;;;;;;;wBAC3B;sCACR,8OAAC;;gCACE,KAAK,KAAK,CACT,OAAO,kCAAkC,wBACvC,QACA;gCAAM;gCAAI;;;;;;;sCAGhB,8OAAC;4BACC,aAAU;4BACV,IAAI;4BACJ,WAAW,IAAA,yHAAE,EAAC;sCAEb,EAAE;;;;;;;;;;;;8BAGP,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,4IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU,CAAC;oBACX,SAAS,KAAK,YAAY,CAAC;8BAE1B,QAAQ;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 1689, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-seismic-demand-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { masonryAntiOverturningSeismicDemandCalculationResultSchema } from '@atlas/lib/api/modules/schemas/masonry-antioverturning-params'\r\nimport type { MasonryAntiOverturningSeismicDemandCalculationResultSchemaInputs } from '@atlas/types/schemas/masonry/antioverturning-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useForm } from 'react-hook-form'\r\n\r\ntype Props = {\r\n  seismicDemandCalculationResult?: MasonryAntiOverturningSeismicDemandCalculationResultSchemaInputs\r\n  onSave: () => void\r\n}\r\n\r\nexport const MasonryAntiOverturningSeismicDemandCalculationResultsForm = ({\r\n  seismicDemandCalculationResult,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.project-params.anti-overturning.seismic-demand',\r\n  )\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  const isSeismicDemandCalculated = !!seismicDemandCalculationResult\r\n\r\n  const form =\r\n    useForm<MasonryAntiOverturningSeismicDemandCalculationResultSchemaInputs>({\r\n      resolver: zodResolver(\r\n        masonryAntiOverturningSeismicDemandCalculationResultSchema,\r\n      ),\r\n      defaultValues: {\r\n        overturningSeismicForce:\r\n          seismicDemandCalculationResult?.overturningSeismicForce ?? 0,\r\n        masonryAndPlasterWeight:\r\n          seismicDemandCalculationResult?.masonryAndPlasterWeight ?? 0,\r\n        maximumAcceleration:\r\n          seismicDemandCalculationResult?.maximumAcceleration ?? 0,\r\n      },\r\n    })\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className={`space-y-4 rounded-md border p-4 ${!isSeismicDemandCalculated ? 'border-red-500' : ''}`}\r\n        onSubmit={form.handleSubmit(onSave)}\r\n      >\r\n        {!isSeismicDemandCalculated && (\r\n          <div className=\"mb-4 p-2 rounded bg-red-100 text-red-700 border border-red-300\">\r\n            {t('notCalculated')}\r\n          </div>\r\n        )}\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"maximumAcceleration\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"masonryAndPlasterWeight\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"overturningSeismicForce\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={!isSeismicDemandCalculated}\r\n          onClick={form.handleSubmit(onSave)}\r\n        >\r\n          {tCommon('next')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;;;;;;;;;AAOO,MAAM,4DAA4D,CAAC,EACxE,8BAA8B,EAC9B,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EACvB;IAEF,MAAM,UAAU,IAAA,yNAAe,EAAC;IAEhC,MAAM,4BAA4B,CAAC,CAAC;IAEpC,MAAM,OACJ,IAAA,yKAAO,EAAmE;QACxE,UAAU,IAAA,6KAAW,EACnB,6OAA0D;QAE5D,eAAe;YACb,yBACE,gCAAgC,2BAA2B;YAC7D,yBACE,gCAAgC,2BAA2B;YAC7D,qBACE,gCAAgC,uBAAuB;QAC3D;IACF;IAEF,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAW,CAAC,gCAAgC,EAAE,CAAC,4BAA4B,mBAAmB,IAAI;YAClG,UAAU,KAAK,YAAY,CAAC;;gBAE3B,CAAC,2CACA,8OAAC;oBAAI,WAAU;8BACZ,EAAE;;;;;;8BAGP,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,4IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU,CAAC;oBACX,SAAS,KAAK,YAAY,CAAC;8BAE1B,QAAQ;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 1792, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-building-characteristics-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { BUILDING_TYPE, MODULE_BUILDING_TYPE } from '@atlas/constants/module'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type MasonryAntiOverturningBuildingCharacteristicsSchemaInputs,\r\n  type MasonryAntiOverturningParamsFormSchemaInputs,\r\n  masonryAntiOverturningBuildingCharacteristicsSchema,\r\n} from '@atlas/types/schemas/masonry/antioverturning-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<MasonryAntiOverturningBuildingCharacteristicsSchemaInputs>\r\n  params: MasonryAntiOverturningParamsFormSchemaInputs\r\n  setParams: (newParams: any) => void\r\n  onSave: () => void\r\n}\r\n\r\nexport const MasonryAntiOverturningBuildingCharacteristicsForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  params,\r\n  setParams,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.project-params.anti-overturning.building-characteristics',\r\n  )\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const buildingFundamentalPeriodCalculation = (\r\n    buildingType: BUILDING_TYPE,\r\n    totalBuildingHeight: number,\r\n  ) => {\r\n    return buildingType === BUILDING_TYPE.REINFORCED_CONCRETE\r\n      ? 0.075 * totalBuildingHeight ** 0.75\r\n      : 0.05 * totalBuildingHeight ** 0.75\r\n  }\r\n\r\n  const defaultBuildingFundamentalPeriod = buildingFundamentalPeriodCalculation(\r\n    defaultValues?.buildingType ?? BUILDING_TYPE.MASONRY,\r\n    defaultValues?.totalBuildingHeight ?? 1,\r\n  )\r\n\r\n  const form =\r\n    useForm<MasonryAntiOverturningBuildingCharacteristicsSchemaInputs>({\r\n      resolver: zodResolver(\r\n        masonryAntiOverturningBuildingCharacteristicsSchema,\r\n      ),\r\n      defaultValues: {\r\n        buildingType:\r\n          defaultValues?.buildingType ?? BUILDING_TYPE.REINFORCED_CONCRETE,\r\n        totalBuildingHeight: defaultValues?.totalBuildingHeight ?? 1,\r\n        buildingFundamentalPeriod: defaultBuildingFundamentalPeriod,\r\n        parameterA: defaultBuildingFundamentalPeriod < 0.5 ? 0.8 : 0.3,\r\n        parameterB:\r\n          defaultBuildingFundamentalPeriod < 0.5\r\n            ? 1.4\r\n            : defaultBuildingFundamentalPeriod > 1\r\n              ? 1\r\n              : 1.2,\r\n        parameterAp:\r\n          defaultBuildingFundamentalPeriod < 0.5\r\n            ? 5\r\n            : defaultBuildingFundamentalPeriod > 1\r\n              ? 2.5\r\n              : 4,\r\n      },\r\n    })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: MasonryAntiOverturningBuildingCharacteristicsSchemaInputs) => {\r\n      // construct body based on global schema:\r\n      const masonryAntiOverturningParams: MasonryAntiOverturningParamsFormSchemaInputs =\r\n        {\r\n          ...params,\r\n          buildingCharacteristics: body,\r\n        }\r\n      mutate({ projectId, moduleId, body: masonryAntiOverturningParams })\r\n    },\r\n    [mutate, projectId, moduleId, params],\r\n  )\r\n\r\n  const buildingType = form.watch('buildingType')\r\n  const totalBuildingHeight = form.watch('totalBuildingHeight')\r\n\r\n  useEffect(() => {\r\n    const buildingFundamentalPeriod =\r\n      buildingType === BUILDING_TYPE.REINFORCED_CONCRETE\r\n        ? 0.075 * totalBuildingHeight ** 0.75\r\n        : 0.05 * totalBuildingHeight ** 0.75\r\n\r\n    form.setValue('parameterA', buildingFundamentalPeriod < 0.5 ? 0.8 : 0.3)\r\n    form.setValue(\r\n      'parameterB',\r\n      buildingFundamentalPeriod < 0.5\r\n        ? 1.4\r\n        : buildingFundamentalPeriod > 1\r\n          ? 1\r\n          : 1.2,\r\n    )\r\n    form.setValue(\r\n      'parameterAp',\r\n      buildingFundamentalPeriod < 0.5\r\n        ? 5\r\n        : buildingFundamentalPeriod > 1\r\n          ? 2.5\r\n          : 4,\r\n    )\r\n    form.setValue('buildingFundamentalPeriod', buildingFundamentalPeriod)\r\n  }, [buildingType, totalBuildingHeight, form])\r\n\r\n  useEffect(() => {\r\n    const subscription = form.watch(values => {\r\n      setParams((prev: any) => ({\r\n        ...prev,\r\n        buildingCharacteristics: values,\r\n      }))\r\n    })\r\n    return () => subscription.unsubscribe()\r\n  }, [form, setParams])\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"buildingType\"\r\n          options={MODULE_BUILDING_TYPE}\r\n          optionLabelFn={p => t(`building.${p}`)}\r\n          t={t}\r\n          disabled\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"totalBuildingHeight\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"buildingFundamentalPeriod\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"parameterA\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"parameterB\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"parameterAp\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAGA;AACA;AAKA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AAYO,MAAM,oDAAoD,CAAC,EAChE,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACN,SAAS,EACT,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EACvB;IAEF,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,uCAAuC,CAC3C,cACA;QAEA,OAAO,iBAAiB,4IAAa,CAAC,mBAAmB,GACrD,QAAQ,uBAAuB,OAC/B,OAAO,uBAAuB;IACpC;IAEA,MAAM,mCAAmC,qCACvC,eAAe,gBAAgB,4IAAa,CAAC,OAAO,EACpD,eAAe,uBAAuB;IAGxC,MAAM,OACJ,IAAA,yKAAO,EAA4D;QACjE,UAAU,IAAA,6KAAW,EACnB,oNAAmD;QAErD,eAAe;YACb,cACE,eAAe,gBAAgB,4IAAa,CAAC,mBAAmB;YAClE,qBAAqB,eAAe,uBAAuB;YAC3D,2BAA2B;YAC3B,YAAY,mCAAmC,MAAM,MAAM;YAC3D,YACE,mCAAmC,MAC/B,MACA,mCAAmC,IACjC,IACA;YACR,aACE,mCAAmC,MAC/B,IACA,mCAAmC,IACjC,MACA;QACV;IACF;IAEF,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAW;wBAAE;wBAAW;oBAAS;iBAAE;YAChD;YACA;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,yCAAyC;QACzC,MAAM,+BACJ;YACE,GAAG,MAAM;YACT,yBAAyB;QAC3B;QACF,OAAO;YAAE;YAAW;YAAU,MAAM;QAA6B;IACnE,GACA;QAAC;QAAQ;QAAW;QAAU;KAAO;IAGvC,MAAM,eAAe,KAAK,KAAK,CAAC;IAChC,MAAM,sBAAsB,KAAK,KAAK,CAAC;IAEvC,IAAA,kNAAS,EAAC;QACR,MAAM,4BACJ,iBAAiB,4IAAa,CAAC,mBAAmB,GAC9C,QAAQ,uBAAuB,OAC/B,OAAO,uBAAuB;QAEpC,KAAK,QAAQ,CAAC,cAAc,4BAA4B,MAAM,MAAM;QACpE,KAAK,QAAQ,CACX,cACA,4BAA4B,MACxB,MACA,4BAA4B,IAC1B,IACA;QAER,KAAK,QAAQ,CACX,eACA,4BAA4B,MACxB,IACA,4BAA4B,IAC1B,MACA;QAER,KAAK,QAAQ,CAAC,6BAA6B;IAC7C,GAAG;QAAC;QAAc;QAAqB;KAAK;IAE5C,IAAA,kNAAS,EAAC;QACR,MAAM,eAAe,KAAK,KAAK,CAAC,CAAA;YAC9B,UAAU,CAAC,OAAc,CAAC;oBACxB,GAAG,IAAI;oBACP,yBAAyB;gBAC3B,CAAC;QACH;QACA,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG;QAAC;QAAM;KAAU;IAEpB,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,mJAAoB;oBAC7B,eAAe,CAAA,IAAK,EAAE,CAAC,SAAS,EAAE,GAAG;oBACrC,GAAG;oBACH,QAAQ;;;;;;8BAEV,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,4IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,8OAAC,oOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 2011, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-infill-geometry-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { PANEL_WIDTH_DEFAULT } from '@atlas/constants/module'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type MasonryAntiOverturningInfillGeometrySchemaInputs,\r\n  type MasonryAntiOverturningParamsFormSchemaInputs,\r\n  masonryAntiOverturningInfillGeometrySchema,\r\n} from '@atlas/types/schemas/masonry/antioverturning-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<MasonryAntiOverturningInfillGeometrySchemaInputs>\r\n  params: MasonryAntiOverturningParamsFormSchemaInputs\r\n  onSave: () => void\r\n}\r\n\r\nexport const MasonryAntiOverturningInfillGeometryForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  params,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.project-params.anti-overturning.infill-geometry',\r\n  )\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const form = useForm<MasonryAntiOverturningInfillGeometrySchemaInputs>({\r\n    resolver: zodResolver(masonryAntiOverturningInfillGeometrySchema),\r\n    defaultValues: {\r\n      panelWidth: PANEL_WIDTH_DEFAULT,\r\n      externalFacingThickness: defaultValues?.externalFacingThickness ?? 0,\r\n      internalFacingThickness: defaultValues?.internalFacingThickness ?? 0,\r\n      singleSidePlasterThickness:\r\n        defaultValues?.singleSidePlasterThickness ?? 0,\r\n      netPanelHeight: defaultValues?.netPanelHeight ?? 0,\r\n      panelHeightFromGroundLevel:\r\n        defaultValues?.panelHeightFromGroundLevel ?? 0,\r\n      panelCentroidFromGroundLevel:\r\n        defaultValues?.panelCentroidFromGroundLevel ?? 0,\r\n      fundamentalPeriodPanel: defaultValues?.fundamentalPeriodPanel ?? 0,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const totalBuildingHeight =\r\n    params?.buildingCharacteristics?.totalBuildingHeight ?? 1\r\n  const masonrySpecificWeightExternalFacing =\r\n    params?.materialProperties?.masonrySpecificWeightExternalFacing ?? 0\r\n  const masonrySpecificWeightInternalFacing =\r\n    params?.materialProperties?.masonrySpecificWeightInternalFacing ?? 0\r\n  const plasterSpecificWeight =\r\n    params?.materialProperties?.plasterSpecificWeight ?? 0\r\n  const infillWallElasticModulus =\r\n    params?.materialProperties?.infillWallElasticModulus ?? 0\r\n  const netPanelHeight = form.watch('netPanelHeight')\r\n  const panelWidth = form.watch('panelWidth') ?? PANEL_WIDTH_DEFAULT\r\n  const internalFacingThickness = form.watch('internalFacingThickness')\r\n  const externalFacingThickness = form.watch('externalFacingThickness')\r\n  const singleSidePlasterThickness = form.watch('singleSidePlasterThickness')\r\n\r\n  useEffect(() => {\r\n    const panelHeightFromGroundLevel = totalBuildingHeight * 1000\r\n    form.setValue('panelHeightFromGroundLevel', panelHeightFromGroundLevel)\r\n    form.setValue(\r\n      'panelCentroidFromGroundLevel',\r\n      panelHeightFromGroundLevel - netPanelHeight / 2,\r\n    )\r\n\r\n    // fundamental panel period calculation:\r\n    // D44 is netPanelHeight\r\n    // D40 is panelWidth\r\n    // D42 is internalFacingThickness\r\n    // D36 is materialProperties.masonrySpecificWeightInternalFacing\r\n    // D41 is externalFacingThickness\r\n    // D35 is materialProperties.masonrySpecificWeightExternalFacing\r\n    // D43 is singleSidePlasterThickness\r\n    // D37 is materialProperties.plasterSpecificWeight\r\n    // D33 is materialProperties.infillWallElasticModulus\r\n    // FORMULA : =(2*3.14*D44^2/3.5156)*SQRT((D40*((D42*D36)+(D41*D35)+(2*D43*D37))*1000/1000000000)/(D33*9.81*1000*D40*(D42+D41+2*D43)^3/12))\r\n    const subInnerPartOne =\r\n      (panelWidth *\r\n        (internalFacingThickness * masonrySpecificWeightInternalFacing +\r\n          externalFacingThickness * masonrySpecificWeightExternalFacing +\r\n          2 * singleSidePlasterThickness * plasterSpecificWeight) *\r\n        1000) /\r\n      1000000000\r\n    const subInnerPartTwo =\r\n      (infillWallElasticModulus *\r\n        9.81 *\r\n        1000 *\r\n        panelWidth *\r\n        (internalFacingThickness +\r\n          externalFacingThickness +\r\n          2 * singleSidePlasterThickness) **\r\n          3) /\r\n      12\r\n\r\n    const secondPart = Math.sqrt(subInnerPartOne / subInnerPartTwo)\r\n    const fundamentalPeriodPanel =\r\n      ((2 * 3.14 * netPanelHeight ** 2) / 3.5156) * secondPart\r\n\r\n    form.setValue('fundamentalPeriodPanel', fundamentalPeriodPanel)\r\n  }, [\r\n    totalBuildingHeight,\r\n    netPanelHeight,\r\n    panelWidth,\r\n    internalFacingThickness,\r\n    externalFacingThickness,\r\n    singleSidePlasterThickness,\r\n    infillWallElasticModulus,\r\n    form,\r\n    masonrySpecificWeightExternalFacing,\r\n    masonrySpecificWeightInternalFacing,\r\n    plasterSpecificWeight,\r\n  ])\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: MasonryAntiOverturningInfillGeometrySchemaInputs) => {\r\n      // construct body based on global schema:\r\n      const masonryAntiOverturningParams: MasonryAntiOverturningParamsFormSchemaInputs =\r\n        {\r\n          ...params,\r\n          infillGeometry: body,\r\n        }\r\n      mutate({ projectId, moduleId, body: masonryAntiOverturningParams })\r\n    },\r\n    [mutate, projectId, moduleId, params],\r\n  )\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"panelWidth\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"externalFacingThickness\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"internalFacingThickness\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"singleSidePlasterThickness\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput control={form.control} name=\"netPanelHeight\" t={t} />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"panelHeightFromGroundLevel\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"panelCentroidFromGroundLevel\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"fundamentalPeriodPanel\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAGA;AACA;AAKA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;AAWO,MAAM,2CAA2C,CAAC,EACvD,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACN,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EACvB;IAEF,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,OAAO,IAAA,yKAAO,EAAmD;QACrE,UAAU,IAAA,6KAAW,EAAC,2MAA0C;QAChE,eAAe;YACb,YAAY,kJAAmB;YAC/B,yBAAyB,eAAe,2BAA2B;YACnE,yBAAyB,eAAe,2BAA2B;YACnE,4BACE,eAAe,8BAA8B;YAC/C,gBAAgB,eAAe,kBAAkB;YACjD,4BACE,eAAe,8BAA8B;YAC/C,8BACE,eAAe,gCAAgC;YACjD,wBAAwB,eAAe,0BAA0B;QACnE;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAW;wBAAE;wBAAW;oBAAS;iBAAE;YAChD;YACA;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,sBACJ,QAAQ,yBAAyB,uBAAuB;IAC1D,MAAM,sCACJ,QAAQ,oBAAoB,uCAAuC;IACrE,MAAM,sCACJ,QAAQ,oBAAoB,uCAAuC;IACrE,MAAM,wBACJ,QAAQ,oBAAoB,yBAAyB;IACvD,MAAM,2BACJ,QAAQ,oBAAoB,4BAA4B;IAC1D,MAAM,iBAAiB,KAAK,KAAK,CAAC;IAClC,MAAM,aAAa,KAAK,KAAK,CAAC,iBAAiB,kJAAmB;IAClE,MAAM,0BAA0B,KAAK,KAAK,CAAC;IAC3C,MAAM,0BAA0B,KAAK,KAAK,CAAC;IAC3C,MAAM,6BAA6B,KAAK,KAAK,CAAC;IAE9C,IAAA,kNAAS,EAAC;QACR,MAAM,6BAA6B,sBAAsB;QACzD,KAAK,QAAQ,CAAC,8BAA8B;QAC5C,KAAK,QAAQ,CACX,gCACA,6BAA6B,iBAAiB;QAGhD,wCAAwC;QACxC,wBAAwB;QACxB,oBAAoB;QACpB,iCAAiC;QACjC,gEAAgE;QAChE,iCAAiC;QACjC,gEAAgE;QAChE,oCAAoC;QACpC,kDAAkD;QAClD,qDAAqD;QACrD,0IAA0I;QAC1I,MAAM,kBACJ,AAAC,aACC,CAAC,0BAA0B,sCACzB,0BAA0B,sCAC1B,IAAI,6BAA6B,qBAAqB,IACxD,OACF;QACF,MAAM,kBACJ,AAAC,2BACC,OACA,OACA,aACA,CAAC,0BACC,0BACA,IAAI,0BAA0B,KAC9B,IACJ;QAEF,MAAM,aAAa,KAAK,IAAI,CAAC,kBAAkB;QAC/C,MAAM,yBACJ,AAAE,IAAI,OAAO,kBAAkB,IAAK,SAAU;QAEhD,KAAK,QAAQ,CAAC,0BAA0B;IAC1C,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,yCAAyC;QACzC,MAAM,+BACJ;YACE,GAAG,MAAM;YACT,gBAAgB;QAClB;QACF,OAAO;YAAE;YAAW;YAAU,MAAM;QAA6B;IACnE,GACA;QAAC;QAAQ;QAAW;QAAU;KAAO;IAGvC,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAiB,GAAG;;;;;;8BACjE,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,4IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,8OAAC,oOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 2259, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-material-properties-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport {\r\n  executionClass,\r\n  FACING_MATERIAL,\r\n  INFILL_WALL_TOPOLOGY,\r\n  loadResistingCategory,\r\n  MODULE_EXECUTION_CLASS,\r\n  MODULE_FACING_MATERIAL,\r\n  MODULE_INFILL_WALL_TOPOLOGY,\r\n  MODULE_LOAD_RESISTING_CATEGORY,\r\n  MODULE_MATERIAL_KNOWLEDGE_LEVEL,\r\n  masonryStrengthSafetyFactorMapping,\r\n  moduleMaterialKnowledgeLevel,\r\n  moduleMaterialKnowledgeLevelValues,\r\n} from '@atlas/constants/module'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type MasonryAntiOverturningMaterialPropertiesSchemaInputs,\r\n  type MasonryAntiOverturningParamsFormSchemaInputs,\r\n  masonryAntiOverturningMaterialPropertiesSchema,\r\n} from '@atlas/types/schemas/masonry/antioverturning-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<MasonryAntiOverturningMaterialPropertiesSchemaInputs>\r\n  params: MasonryAntiOverturningParamsFormSchemaInputs\r\n  setParams: (newParams: any) => void\r\n  onSave: () => void\r\n}\r\n\r\nexport const MasonryAntiOverturningMaterialPropertiesForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  params,\r\n  setParams,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.project-params.anti-overturning.material-properties',\r\n  )\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const form = useForm<MasonryAntiOverturningMaterialPropertiesSchemaInputs>({\r\n    resolver: zodResolver(masonryAntiOverturningMaterialPropertiesSchema),\r\n    defaultValues: {\r\n      infillWallTypology:\r\n        defaultValues?.infillWallTypology ?? INFILL_WALL_TOPOLOGY.SINGLE,\r\n      facingMaterial: defaultValues?.facingMaterial ?? FACING_MATERIAL.BRICK,\r\n      knowledgeLevel:\r\n        defaultValues?.knowledgeLevel ?? moduleMaterialKnowledgeLevel.LC1,\r\n      confidenceFactor:\r\n        defaultValues?.confidenceFactor ??\r\n        moduleMaterialKnowledgeLevelValues.LC1,\r\n      executionClass: defaultValues?.executionClass ?? executionClass.ONE,\r\n      loadResistingCategory:\r\n        defaultValues?.loadResistingCategory ??\r\n        loadResistingCategory.MASONRY_CAT_I_AND_MORTAR_WITH_GUARANTEED_PERFORMANCE,\r\n      masonryStrengthSafetyFactor:\r\n        defaultValues?.masonryStrengthSafetyFactor ?? 0,\r\n      characteristicCompressiveStrength:\r\n        defaultValues?.characteristicCompressiveStrength ?? 0,\r\n      designCompressiveStrength: defaultValues?.designCompressiveStrength ?? 0,\r\n      infillWallElasticModulus: defaultValues?.infillWallElasticModulus ?? 0,\r\n      ultimateMasonryStrain: defaultValues?.ultimateMasonryStrain ?? 0.0035,\r\n      masonrySpecificWeightExternalFacing:\r\n        defaultValues?.masonrySpecificWeightExternalFacing ?? 0,\r\n      masonrySpecificWeightInternalFacing:\r\n        defaultValues?.infillWallTypology === INFILL_WALL_TOPOLOGY.SINGLE\r\n          ? 0\r\n          : (defaultValues?.masonrySpecificWeightInternalFacing ?? 0),\r\n      plasterSpecificWeight: defaultValues?.plasterSpecificWeight ?? 0,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const infillWallTypology = form.watch('infillWallTypology')\r\n  const knowledgeMaterialLevel = form.watch('knowledgeLevel')\r\n  const executionClassFormValue = form.watch('executionClass')\r\n  const loadResistingCategoryFormValue = form.watch('loadResistingCategory')\r\n  const characteristicCompressiveStrength = form.watch(\r\n    'characteristicCompressiveStrength',\r\n  )\r\n\r\n  useEffect(() => {\r\n    const confidenceFactor =\r\n      moduleMaterialKnowledgeLevelValues[knowledgeMaterialLevel]\r\n    form.setValue('confidenceFactor', confidenceFactor)\r\n    const masonryStrengthSafetyFactor =\r\n      masonryStrengthSafetyFactorMapping[loadResistingCategoryFormValue][\r\n        executionClassFormValue\r\n      ]\r\n    form.setValue('masonryStrengthSafetyFactor', masonryStrengthSafetyFactor)\r\n\r\n    form.setValue(\r\n      'designCompressiveStrength',\r\n      characteristicCompressiveStrength /\r\n        masonryStrengthSafetyFactor /\r\n        confidenceFactor,\r\n    )\r\n\r\n    if (infillWallTypology === INFILL_WALL_TOPOLOGY.SINGLE) {\r\n      form.setValue('masonrySpecificWeightInternalFacing', 0)\r\n    }\r\n  }, [\r\n    knowledgeMaterialLevel,\r\n    executionClassFormValue,\r\n    loadResistingCategoryFormValue,\r\n    infillWallTypology,\r\n    characteristicCompressiveStrength,\r\n    form,\r\n  ])\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: MasonryAntiOverturningMaterialPropertiesSchemaInputs) => {\r\n      // construct body based on global schema:\r\n      const masonryAntiOverturningParams: MasonryAntiOverturningParamsFormSchemaInputs =\r\n        {\r\n          ...params,\r\n          materialProperties: body,\r\n        }\r\n      mutate({ projectId, moduleId, body: masonryAntiOverturningParams })\r\n    },\r\n    [mutate, projectId, moduleId, params],\r\n  )\r\n\r\n  useEffect(() => {\r\n    const subscription = form.watch(values => {\r\n      setParams((prev: any) => ({\r\n        ...prev,\r\n        materialProperties: values,\r\n      }))\r\n    })\r\n    return () => subscription.unsubscribe()\r\n  }, [form, setParams])\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"infillWallTypology\"\r\n          options={MODULE_INFILL_WALL_TOPOLOGY}\r\n          optionLabelFn={p => t(`infillWall.${p}`)}\r\n          t={t}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"facingMaterial\"\r\n          options={MODULE_FACING_MATERIAL}\r\n          t={msg => t(`${infillWallTypology}.${msg}`)}\r\n          optionLabelFn={p => t(`facingMaterial.${p}`)}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"knowledgeLevel\"\r\n          options={MODULE_MATERIAL_KNOWLEDGE_LEVEL}\r\n          optionLabelFn={p => t(`materialKnowledge.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"confidenceFactor\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"executionClass\"\r\n          options={MODULE_EXECUTION_CLASS}\r\n          optionLabelFn={p => t(`executionClass.${p}`)}\r\n          t={t}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"loadResistingCategory\"\r\n          options={MODULE_LOAD_RESISTING_CATEGORY}\r\n          optionLabelFn={p => t(`loadResisting.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"masonryStrengthSafetyFactor\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"characteristicCompressiveStrength\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"designCompressiveStrength\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"infillWallElasticModulus\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"ultimateMasonryStrain\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"masonrySpecificWeightExternalFacing\"\r\n          t={msg => t(`${infillWallTypology}.${msg}`)}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"masonrySpecificWeightInternalFacing\"\r\n          t={msg => t(`${infillWallTypology}.${msg}`)}\r\n          disabled={infillWallTypology === INFILL_WALL_TOPOLOGY.SINGLE}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"plasterSpecificWeight\"\r\n          t={t}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAgBA;AACA;AAKA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AAYO,MAAM,+CAA+C,CAAC,EAC3D,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACN,SAAS,EACT,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EACvB;IAEF,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,OAAO,IAAA,yKAAO,EAAuD;QACzE,UAAU,IAAA,6KAAW,EAAC,+MAA8C;QACpE,eAAe;YACb,oBACE,eAAe,sBAAsB,mJAAoB,CAAC,MAAM;YAClE,gBAAgB,eAAe,kBAAkB,8IAAe,CAAC,KAAK;YACtE,gBACE,eAAe,kBAAkB,2JAA4B,CAAC,GAAG;YACnE,kBACE,eAAe,oBACf,iKAAkC,CAAC,GAAG;YACxC,gBAAgB,eAAe,kBAAkB,6IAAc,CAAC,GAAG;YACnE,uBACE,eAAe,yBACf,oJAAqB,CAAC,oDAAoD;YAC5E,6BACE,eAAe,+BAA+B;YAChD,mCACE,eAAe,qCAAqC;YACtD,2BAA2B,eAAe,6BAA6B;YACvE,0BAA0B,eAAe,4BAA4B;YACrE,uBAAuB,eAAe,yBAAyB;YAC/D,qCACE,eAAe,uCAAuC;YACxD,qCACE,eAAe,uBAAuB,mJAAoB,CAAC,MAAM,GAC7D,IACC,eAAe,uCAAuC;YAC7D,uBAAuB,eAAe,yBAAyB;QACjE;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAW;wBAAE;wBAAW;oBAAS;iBAAE;YAChD;YACA;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,qBAAqB,KAAK,KAAK,CAAC;IACtC,MAAM,yBAAyB,KAAK,KAAK,CAAC;IAC1C,MAAM,0BAA0B,KAAK,KAAK,CAAC;IAC3C,MAAM,iCAAiC,KAAK,KAAK,CAAC;IAClD,MAAM,oCAAoC,KAAK,KAAK,CAClD;IAGF,IAAA,kNAAS,EAAC;QACR,MAAM,mBACJ,iKAAkC,CAAC,uBAAuB;QAC5D,KAAK,QAAQ,CAAC,oBAAoB;QAClC,MAAM,8BACJ,iKAAkC,CAAC,+BAA+B,CAChE,wBACD;QACH,KAAK,QAAQ,CAAC,+BAA+B;QAE7C,KAAK,QAAQ,CACX,6BACA,oCACE,8BACA;QAGJ,IAAI,uBAAuB,mJAAoB,CAAC,MAAM,EAAE;YACtD,KAAK,QAAQ,CAAC,uCAAuC;QACvD;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,yCAAyC;QACzC,MAAM,+BACJ;YACE,GAAG,MAAM;YACT,oBAAoB;QACtB;QACF,OAAO;YAAE;YAAW;YAAU,MAAM;QAA6B;IACnE,GACA;QAAC;QAAQ;QAAW;QAAU;KAAO;IAGvC,IAAA,kNAAS,EAAC;QACR,MAAM,eAAe,KAAK,KAAK,CAAC,CAAA;YAC9B,UAAU,CAAC,OAAc,CAAC;oBACxB,GAAG,IAAI;oBACP,oBAAoB;gBACtB,CAAC;QACH;QACA,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG;QAAC;QAAM;KAAU;IACpB,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,0JAA2B;oBACpC,eAAe,CAAA,IAAK,EAAE,CAAC,WAAW,EAAE,GAAG;oBACvC,GAAG;;;;;;8BAEL,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,qJAAsB;oBAC/B,GAAG,CAAA,MAAO,EAAE,GAAG,mBAAmB,CAAC,EAAE,KAAK;oBAC1C,eAAe,CAAA,IAAK,EAAE,CAAC,eAAe,EAAE,GAAG;;;;;;8BAE7C,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,8JAA+B;oBACxC,eAAe,CAAA,IAAK,EAAE,CAAC,kBAAkB,EAAE,GAAG;oBAC9C,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,qJAAsB;oBAC/B,eAAe,CAAA,IAAK,EAAE,CAAC,eAAe,EAAE,GAAG;oBAC3C,GAAG;;;;;;8BAEL,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,6JAA8B;oBACvC,eAAe,CAAA,IAAK,EAAE,CAAC,cAAc,EAAE,GAAG;oBAC1C,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG,CAAA,MAAO,EAAE,GAAG,mBAAmB,CAAC,EAAE,KAAK;;;;;;8BAE5C,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG,CAAA,MAAO,EAAE,GAAG,mBAAmB,CAAC,EAAE,KAAK;oBAC1C,UAAU,uBAAuB,mJAAoB,CAAC,MAAM;;;;;;8BAE9D,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,8OAAC,4IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,8OAAC,oOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 2571, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-site-characteristics-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport {\r\n  MODULE_SUBSOIL_CATEGORY,\r\n  MODULE_TOPOGRAPHIC_CATEGORY,\r\n  SUBSOIL_CATEGORY,\r\n  ssCoefficientValues,\r\n  TOPOGRAPHIC_CATEGORY,\r\n  topographicCoefficientValues,\r\n} from '@atlas/constants/module'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type MasonryAntiOverturningParamsFormSchemaInputs,\r\n  type MasonryAntiOverturningSiteCharacteristicsSchemaInputs,\r\n  masonryAntiOverturningSiteCharacteristicsSchema,\r\n} from '@atlas/types/schemas/masonry/antioverturning-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  params: MasonryAntiOverturningParamsFormSchemaInputs\r\n  defaultValues?: Partial<MasonryAntiOverturningSiteCharacteristicsSchemaInputs>\r\n  setParams: (newParams: any) => void\r\n  onSave: () => void\r\n}\r\n\r\nexport const MasonryAntiOverturningSiteCharacteristicsForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  params,\r\n  setParams,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.project-params.anti-overturning.site-characteristics',\r\n  )\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const form = useForm<MasonryAntiOverturningSiteCharacteristicsSchemaInputs>({\r\n    resolver: zodResolver(masonryAntiOverturningSiteCharacteristicsSchema),\r\n    defaultValues: {\r\n      seismicAccelerationAtSlv: defaultValues?.seismicAccelerationAtSlv ?? 0,\r\n      amplificationFactorAtSlv: defaultValues?.amplificationFactorAtSlv ?? 0,\r\n      subsoilCategory: defaultValues?.subsoilCategory ?? SUBSOIL_CATEGORY.A,\r\n      // use SubsoilCoefficient from key entry of subsoilCategory\r\n      ssCoefficient:\r\n        ssCoefficientValues[\r\n          defaultValues?.subsoilCategory ?? SUBSOIL_CATEGORY.A\r\n        ],\r\n      topographicCategory:\r\n        defaultValues?.topographicCategory ?? TOPOGRAPHIC_CATEGORY.T1,\r\n      stCoefficient:\r\n        topographicCoefficientValues[\r\n          defaultValues?.topographicCategory ?? TOPOGRAPHIC_CATEGORY.T1\r\n        ],\r\n      subsoilCoefficient:\r\n        (defaultValues?.ssCoefficient ?? 0) *\r\n        (defaultValues?.stCoefficient ?? 0),\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const subsoilCategory = form.watch('subsoilCategory') ?? SUBSOIL_CATEGORY.A\r\n  const topographicCategory =\r\n    form.watch('topographicCategory') ?? TOPOGRAPHIC_CATEGORY.T1\r\n\r\n  useEffect(() => {\r\n    form.setValue('ssCoefficient', ssCoefficientValues[subsoilCategory])\r\n    form.setValue(\r\n      'stCoefficient',\r\n      topographicCoefficientValues[topographicCategory],\r\n    )\r\n    form.setValue(\r\n      'subsoilCoefficient',\r\n      ssCoefficientValues[subsoilCategory] *\r\n        topographicCoefficientValues[topographicCategory],\r\n    )\r\n  }, [subsoilCategory, topographicCategory, form])\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: MasonryAntiOverturningSiteCharacteristicsSchemaInputs) => {\r\n      // construct body based on global schema:\r\n      const masonryAntiOverturningParams: MasonryAntiOverturningParamsFormSchemaInputs =\r\n        {\r\n          ...params,\r\n          siteCharacteristics: body,\r\n        }\r\n      mutate({ projectId, moduleId, body: masonryAntiOverturningParams })\r\n    },\r\n    [mutate, projectId, moduleId, params],\r\n  )\r\n\r\n  useEffect(() => {\r\n    const subscription = form.watch(values => {\r\n      setParams((prev: MasonryAntiOverturningParamsFormSchemaInputs) => ({\r\n        ...prev,\r\n        siteCharacteristics: values,\r\n      }))\r\n    })\r\n    return () => subscription.unsubscribe()\r\n  }, [form, setParams])\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <Image\r\n          src=\"/assets/masonry-anti-overturning/antibaltamento-geometria-armatura.jpg\"\r\n          alt=\"site characteristics\"\r\n          height={250}\r\n          width={500}\r\n          className=\"mx-auto rounded-md object-contain\"\r\n          priority\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"seismicAccelerationAtSlv\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"amplificationFactorAtSlv\"\r\n          t={t}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"subsoilCategory\"\r\n          options={MODULE_SUBSOIL_CATEGORY}\r\n          optionLabelFn={p => t(`subsoil.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"ssCoefficient\"\r\n          t={t}\r\n          disabled={true}\r\n          // value is set automatically based on subsoilCategory\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"topographicCategory\"\r\n          options={MODULE_TOPOGRAPHIC_CATEGORY}\r\n          optionLabelFn={p => t(`topographic.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"stCoefficient\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"subsoilCoefficient\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAUA;AACA;AAKA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AAYO,MAAM,gDAAgD,CAAC,EAC5D,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACN,SAAS,EACT,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EACvB;IAEF,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,OAAO,IAAA,yKAAO,EAAwD;QAC1E,UAAU,IAAA,6KAAW,EAAC,gNAA+C;QACrE,eAAe;YACb,0BAA0B,eAAe,4BAA4B;YACrE,0BAA0B,eAAe,4BAA4B;YACrE,iBAAiB,eAAe,mBAAmB,+IAAgB,CAAC,CAAC;YACrE,2DAA2D;YAC3D,eACE,kJAAmB,CACjB,eAAe,mBAAmB,+IAAgB,CAAC,CAAC,CACrD;YACH,qBACE,eAAe,uBAAuB,mJAAoB,CAAC,EAAE;YAC/D,eACE,2JAA4B,CAC1B,eAAe,uBAAuB,mJAAoB,CAAC,EAAE,CAC9D;YACH,oBACE,CAAC,eAAe,iBAAiB,CAAC,IAClC,CAAC,eAAe,iBAAiB,CAAC;QACtC;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAW;wBAAE;wBAAW;oBAAS;iBAAE;YAChD;YACA;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,kBAAkB,KAAK,KAAK,CAAC,sBAAsB,+IAAgB,CAAC,CAAC;IAC3E,MAAM,sBACJ,KAAK,KAAK,CAAC,0BAA0B,mJAAoB,CAAC,EAAE;IAE9D,IAAA,kNAAS,EAAC;QACR,KAAK,QAAQ,CAAC,iBAAiB,kJAAmB,CAAC,gBAAgB;QACnE,KAAK,QAAQ,CACX,iBACA,2JAA4B,CAAC,oBAAoB;QAEnD,KAAK,QAAQ,CACX,sBACA,kJAAmB,CAAC,gBAAgB,GAClC,2JAA4B,CAAC,oBAAoB;IAEvD,GAAG;QAAC;QAAiB;QAAqB;KAAK;IAE/C,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,yCAAyC;QACzC,MAAM,+BACJ;YACE,GAAG,MAAM;YACT,qBAAqB;QACvB;QACF,OAAO;YAAE;YAAW;YAAU,MAAM;QAA6B;IACnE,GACA;QAAC;QAAQ;QAAW;QAAU;KAAO;IAGvC,IAAA,kNAAS,EAAC;QACR,MAAM,eAAe,KAAK,KAAK,CAAC,CAAA;YAC9B,UAAU,CAAC,OAAuD,CAAC;oBACjE,GAAG,IAAI;oBACP,qBAAqB;gBACvB,CAAC;QACH;QACA,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG;QAAC;QAAM;KAAU;IACpB,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,8OAAC,wIAAK;oBACJ,KAAI;oBACJ,KAAI;oBACJ,QAAQ;oBACR,OAAO;oBACP,WAAU;oBACV,QAAQ;;;;;;8BAEV,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,sJAAuB;oBAChC,eAAe,CAAA,IAAK,EAAE,CAAC,QAAQ,EAAE,GAAG;oBACpC,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAGZ,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,0JAA2B;oBACpC,eAAe,CAAA,IAAK,EAAE,CAAC,YAAY,EAAE,GAAG;oBACxC,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,4IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,8OAAC,oOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 2809, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx"], "sourcesContent": ["import {\r\n  Accordion as AccordionComponent,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from '@atlas/components/ui/accordion'\r\nimport type {\r\n  Module,\r\n  ModuleWithParamsAntiOverturning,\r\n} from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport {\r\n  compositeReinforcementSystemInputSchema,\r\n  type MasonryAntiOverturningParamsFormSchemaInputs,\r\n} from '@atlas/types/schemas/masonry/antioverturning-form'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useState } from 'react'\r\nimport { MasonryAntiOverturningCompositeReinforcementSystemCalculationForm } from './calculations/anti-overturning-composite-reinforcement-system-calculation-form'\r\nimport { MasonryAntiOverturningPreInterventionCalculationResultsForm } from './calculations/anti-overturning-pre-intervention-calculation-form'\r\nimport { MasonryAntiOverturningSeismicDemandCalculationResultsForm } from './calculations/anti-overturning-seismic-demand-form'\r\nimport { MasonryAntiOverturningBuildingCharacteristicsForm } from './sections/anti-overturning-building-characteristics-form'\r\nimport { MasonryAntiOverturningInfillGeometryForm } from './sections/anti-overturning-infill-geometry-form'\r\nimport { MasonryAntiOverturningMaterialPropertiesForm } from './sections/anti-overturning-material-properties-form'\r\nimport { MasonryAntiOverturningSiteCharacteristicsForm } from './sections/anti-overturning-site-characteristics-form'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  module: ModuleWithParamsAntiOverturning\r\n}\r\n\r\nexport const MasonryAntiOverturningParamsForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  module,\r\n}: Props) => {\r\n  const [params, setParams] =\r\n    useState<MasonryAntiOverturningParamsFormSchemaInputs>(\r\n      module?.params ?? ({} as MasonryAntiOverturningParamsFormSchemaInputs),\r\n    )\r\n\r\n  const seismicDemandCalculationResult = module?.seismicDemandCalculationResult\r\n  const preInterventionCalculationResult =\r\n    module?.preInterventionCalculationResult\r\n\r\n  const _isCompositeReinforcementSystemDefined =\r\n    preInterventionCalculationResult && seismicDemandCalculationResult\r\n\r\n  const [openItems, setOpenItems] = useState(['0'])\r\n  const t = useTranslations('forms.project-params.anti-overturning')\r\n\r\n  const handleItemSaved = useCallback((id: string) => {\r\n    const nextId = String(Number(id) + 1)\r\n    setOpenItems(old => {\r\n      const temp = old.filter(v => v !== id)\r\n      return old.includes(nextId) ? temp : [...temp, nextId]\r\n    })\r\n  }, [])\r\n\r\n  return (\r\n    <div>\r\n      <AccordionComponent\r\n        type=\"multiple\"\r\n        value={openItems}\r\n        onValueChange={setOpenItems}\r\n      >\r\n        <AccordionItem value=\"0\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('site-characteristics.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <MasonryAntiOverturningSiteCharacteristicsForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={{\r\n                seismicAccelerationAtSlv:\r\n                  params?.siteCharacteristics?.seismicAccelerationAtSlv,\r\n                amplificationFactorAtSlv:\r\n                  params?.siteCharacteristics?.amplificationFactorAtSlv,\r\n                subsoilCategory: params?.siteCharacteristics?.subsoilCategory,\r\n                ssCoefficient: params?.siteCharacteristics?.ssCoefficient,\r\n                topographicCategory:\r\n                  params?.siteCharacteristics?.topographicCategory,\r\n                stCoefficient: params?.siteCharacteristics?.stCoefficient,\r\n                subsoilCoefficient:\r\n                  params?.siteCharacteristics?.subsoilCoefficient,\r\n              }}\r\n              setParams={setParams}\r\n              params={params}\r\n              onSave={() => handleItemSaved('0')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"1\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('building-characteristics.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <MasonryAntiOverturningBuildingCharacteristicsForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={{\r\n                buildingType: params?.buildingCharacteristics?.buildingType,\r\n                totalBuildingHeight:\r\n                  params?.buildingCharacteristics?.totalBuildingHeight,\r\n                buildingFundamentalPeriod:\r\n                  params?.buildingCharacteristics?.buildingFundamentalPeriod,\r\n                parameterA: params?.buildingCharacteristics?.parameterA,\r\n                parameterB: params?.buildingCharacteristics?.parameterB,\r\n                parameterAp: params?.buildingCharacteristics?.parameterAp,\r\n              }}\r\n              setParams={setParams}\r\n              params={params}\r\n              onSave={() => handleItemSaved('1')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"2\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('material-properties.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <MasonryAntiOverturningMaterialPropertiesForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={{\r\n                infillWallTypology:\r\n                  params?.materialProperties?.infillWallTypology,\r\n                facingMaterial: params?.materialProperties?.facingMaterial,\r\n                knowledgeLevel: params?.materialProperties?.knowledgeLevel,\r\n                executionClass: params?.materialProperties?.executionClass,\r\n                loadResistingCategory:\r\n                  params?.materialProperties?.loadResistingCategory,\r\n                characteristicCompressiveStrength:\r\n                  params?.materialProperties?.characteristicCompressiveStrength,\r\n                infillWallElasticModulus:\r\n                  params?.materialProperties?.infillWallElasticModulus,\r\n                masonrySpecificWeightExternalFacing:\r\n                  params?.materialProperties\r\n                    ?.masonrySpecificWeightExternalFacing,\r\n                masonrySpecificWeightInternalFacing:\r\n                  params?.materialProperties\r\n                    ?.masonrySpecificWeightInternalFacing,\r\n                plasterSpecificWeight:\r\n                  params?.materialProperties?.plasterSpecificWeight,\r\n                confidenceFactor: params?.materialProperties?.confidenceFactor,\r\n                masonryStrengthSafetyFactor:\r\n                  params?.materialProperties?.masonryStrengthSafetyFactor,\r\n                designCompressiveStrength:\r\n                  params?.materialProperties?.designCompressiveStrength,\r\n                ultimateMasonryStrain:\r\n                  params?.materialProperties?.ultimateMasonryStrain,\r\n              }}\r\n              params={params}\r\n              setParams={setParams}\r\n              onSave={() => handleItemSaved('2')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"3\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('infill-geometry.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <MasonryAntiOverturningInfillGeometryForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={{\r\n                panelWidth: params?.infillGeometry?.panelWidth,\r\n                externalFacingThickness:\r\n                  params?.infillGeometry?.externalFacingThickness,\r\n                internalFacingThickness:\r\n                  params?.infillGeometry?.internalFacingThickness,\r\n                singleSidePlasterThickness:\r\n                  params?.infillGeometry?.singleSidePlasterThickness,\r\n                netPanelHeight: params?.infillGeometry?.netPanelHeight,\r\n                panelHeightFromGroundLevel:\r\n                  params?.infillGeometry?.panelHeightFromGroundLevel,\r\n                panelCentroidFromGroundLevel:\r\n                  params?.infillGeometry?.panelCentroidFromGroundLevel,\r\n                fundamentalPeriodPanel:\r\n                  params?.infillGeometry?.fundamentalPeriodPanel,\r\n              }}\r\n              params={params}\r\n              onSave={() => handleItemSaved('3')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"4\">\r\n          <AccordionTrigger disabled={!seismicDemandCalculationResult}>\r\n            <h3 className=\"text-lg font-medium\">{t('seismic-demand.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <MasonryAntiOverturningSeismicDemandCalculationResultsForm\r\n              seismicDemandCalculationResult={seismicDemandCalculationResult}\r\n              onSave={() => handleItemSaved('4')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"5\">\r\n          <AccordionTrigger disabled={!preInterventionCalculationResult}>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('pre-intervention.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <MasonryAntiOverturningPreInterventionCalculationResultsForm\r\n              preInterventionCalculationResult={\r\n                preInterventionCalculationResult\r\n              }\r\n              onSave={() => handleItemSaved('5')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"6\">\r\n          <AccordionTrigger disabled={!compositeReinforcementSystemInputSchema}>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('composite-reinforcement-system.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <MasonryAntiOverturningCompositeReinforcementSystemCalculationForm\r\n              session={session}\r\n              projectId={projectId}\r\n              module={module}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n      </AccordionComponent>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAWA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AASO,MAAM,mCAAmC,CAAC,EAC/C,OAAO,EACP,SAAS,EACT,QAAQ,EACR,MAAM,EACA;IACN,MAAM,CAAC,QAAQ,UAAU,GACvB,IAAA,iNAAQ,EACN,QAAQ,UAAW,CAAC;IAGxB,MAAM,iCAAiC,QAAQ;IAC/C,MAAM,mCACJ,QAAQ;IAEV,MAAM,yCACJ,oCAAoC;IAEtC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;QAAC;KAAI;IAChD,MAAM,IAAI,IAAA,yNAAe,EAAC;IAE1B,MAAM,kBAAkB,IAAA,oNAAW,EAAC,CAAC;QACnC,MAAM,SAAS,OAAO,OAAO,MAAM;QACnC,aAAa,CAAA;YACX,MAAM,OAAO,IAAI,MAAM,CAAC,CAAA,IAAK,MAAM;YACnC,OAAO,IAAI,QAAQ,CAAC,UAAU,OAAO;mBAAI;gBAAM;aAAO;QACxD;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;kBACC,cAAA,8OAAC,kJAAkB;YACjB,MAAK;YACL,OAAO;YACP,eAAe;;8BAEf,8OAAC,sJAAa;oBAAC,OAAM;;sCACnB,8OAAC,yJAAgB;sCACf,cAAA,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;;;;;;sCAGP,8OAAC,yJAAgB;sCACf,cAAA,8OAAC,qUAA6C;gCAC5C,SAAS;gCACT,WAAW;gCACX,UAAU;gCACV,eAAe;oCACb,0BACE,QAAQ,qBAAqB;oCAC/B,0BACE,QAAQ,qBAAqB;oCAC/B,iBAAiB,QAAQ,qBAAqB;oCAC9C,eAAe,QAAQ,qBAAqB;oCAC5C,qBACE,QAAQ,qBAAqB;oCAC/B,eAAe,QAAQ,qBAAqB;oCAC5C,oBACE,QAAQ,qBAAqB;gCACjC;gCACA,WAAW;gCACX,QAAQ;gCACR,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,8OAAC,sJAAa;oBAAC,OAAM;;sCACnB,8OAAC,yJAAgB;sCACf,cAAA,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;;;;;;sCAGP,8OAAC,yJAAgB;sCACf,cAAA,8OAAC,6UAAiD;gCAChD,SAAS;gCACT,WAAW;gCACX,UAAU;gCACV,eAAe;oCACb,cAAc,QAAQ,yBAAyB;oCAC/C,qBACE,QAAQ,yBAAyB;oCACnC,2BACE,QAAQ,yBAAyB;oCACnC,YAAY,QAAQ,yBAAyB;oCAC7C,YAAY,QAAQ,yBAAyB;oCAC7C,aAAa,QAAQ,yBAAyB;gCAChD;gCACA,WAAW;gCACX,QAAQ;gCACR,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,8OAAC,sJAAa;oBAAC,OAAM;;sCACnB,8OAAC,yJAAgB;sCACf,cAAA,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;;;;;;sCAGP,8OAAC,yJAAgB;sCACf,cAAA,8OAAC,mUAA4C;gCAC3C,SAAS;gCACT,WAAW;gCACX,UAAU;gCACV,eAAe;oCACb,oBACE,QAAQ,oBAAoB;oCAC9B,gBAAgB,QAAQ,oBAAoB;oCAC5C,gBAAgB,QAAQ,oBAAoB;oCAC5C,gBAAgB,QAAQ,oBAAoB;oCAC5C,uBACE,QAAQ,oBAAoB;oCAC9B,mCACE,QAAQ,oBAAoB;oCAC9B,0BACE,QAAQ,oBAAoB;oCAC9B,qCACE,QAAQ,oBACJ;oCACN,qCACE,QAAQ,oBACJ;oCACN,uBACE,QAAQ,oBAAoB;oCAC9B,kBAAkB,QAAQ,oBAAoB;oCAC9C,6BACE,QAAQ,oBAAoB;oCAC9B,2BACE,QAAQ,oBAAoB;oCAC9B,uBACE,QAAQ,oBAAoB;gCAChC;gCACA,QAAQ;gCACR,WAAW;gCACX,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,8OAAC,sJAAa;oBAAC,OAAM;;sCACnB,8OAAC,yJAAgB;sCACf,cAAA,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;;;;;;sCAGP,8OAAC,yJAAgB;sCACf,cAAA,8OAAC,2TAAwC;gCACvC,SAAS;gCACT,WAAW;gCACX,UAAU;gCACV,eAAe;oCACb,YAAY,QAAQ,gBAAgB;oCACpC,yBACE,QAAQ,gBAAgB;oCAC1B,yBACE,QAAQ,gBAAgB;oCAC1B,4BACE,QAAQ,gBAAgB;oCAC1B,gBAAgB,QAAQ,gBAAgB;oCACxC,4BACE,QAAQ,gBAAgB;oCAC1B,8BACE,QAAQ,gBAAgB;oCAC1B,wBACE,QAAQ,gBAAgB;gCAC5B;gCACA,QAAQ;gCACR,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,8OAAC,sJAAa;oBAAC,OAAM;;sCACnB,8OAAC,yJAAgB;4BAAC,UAAU,CAAC;sCAC3B,cAAA,8OAAC;gCAAG,WAAU;0CAAuB,EAAE;;;;;;;;;;;sCAEzC,8OAAC,yJAAgB;sCACf,cAAA,8OAAC,+UAAyD;gCACxD,gCAAgC;gCAChC,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,8OAAC,sJAAa;oBAAC,OAAM;;sCACnB,8OAAC,yJAAgB;4BAAC,UAAU,CAAC;sCAC3B,cAAA,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;;;;;;sCAGP,8OAAC,yJAAgB;sCACf,cAAA,8OAAC,kWAA2D;gCAC1D,kCACE;gCAEF,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,8OAAC,sJAAa;oBAAC,OAAM;;sCACnB,8OAAC,yJAAgB;4BAAC,UAAU,CAAC,wMAAuC;sCAClE,cAAA,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;;;;;;sCAGP,8OAAC,yJAAgB;sCACf,cAAA,8OAAC,yXAAiE;gCAChE,SAAS;gCACT,WAAW;gCACX,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB", "debugId": null}}, {"offset": {"line": 3199, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx"], "sourcesContent": ["import {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport {\r\n  type DesignStrengthPostInterventionCalculationResultSchemaInputs,\r\n  designStrengthPostInterventionCalculationResultSchema,\r\n} from '@atlas/types/schemas/masonry/crm-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Separator } from '@radix-ui/react-dropdown-menu'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useForm } from 'react-hook-form'\r\n\r\ntype Props = {\r\n  designStrengthPostInterventionCalculationResult: DesignStrengthPostInterventionCalculationResultSchemaInputs\r\n}\r\n\r\nexport const CrmPostInterventionDesignStrengthCalculationResultForm = ({\r\n  designStrengthPostInterventionCalculationResult,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.calculations.crm.postIntervention.designStrengthPostInterventionCalculationResult',\r\n  )\r\n\r\n  const form =\r\n    useForm<DesignStrengthPostInterventionCalculationResultSchemaInputs>({\r\n      resolver: zodResolver(\r\n        designStrengthPostInterventionCalculationResultSchema,\r\n      ),\r\n      defaultValues: {\r\n        panelThickness:\r\n          designStrengthPostInterventionCalculationResult?.panelThickness ?? 0,\r\n        designCompressiveStrength:\r\n          designStrengthPostInterventionCalculationResult?.designCompressiveStrength ??\r\n          0,\r\n        designShearStrengthRegularMasonry:\r\n          designStrengthPostInterventionCalculationResult?.designShearStrengthRegularMasonry ??\r\n          0,\r\n        designShearStrengthIrregularMasonry:\r\n          designStrengthPostInterventionCalculationResult?.designShearStrengthIrregularMasonry ??\r\n          0,\r\n        designNormalElasticityModulus:\r\n          designStrengthPostInterventionCalculationResult?.designNormalElasticityModulus ??\r\n          0,\r\n        designShearElasticityModulus:\r\n          designStrengthPostInterventionCalculationResult?.designShearElasticityModulus ??\r\n          0,\r\n        ultimateCompressiveStrainLinearBehavior:\r\n          designStrengthPostInterventionCalculationResult?.ultimateCompressiveStrainLinearBehavior ??\r\n          0,\r\n        secondCoefficient:\r\n          designStrengthPostInterventionCalculationResult?.secondCoefficient ??\r\n          0,\r\n        panelSelfWeight:\r\n          designStrengthPostInterventionCalculationResult?.panelSelfWeight ?? 0,\r\n        inPlaneBendingStrengthCalculationResult:\r\n          designStrengthPostInterventionCalculationResult?.inPlaneBendingStrengthCalculationResult ?? {\r\n            compressedFlangeNeutralAxisDistance: 0,\r\n            inOrOutplaneBendingMoment: 0,\r\n          },\r\n        outOfPlaneBendingStrengthCalculationResult:\r\n          designStrengthPostInterventionCalculationResult?.outOfPlaneBendingStrengthCalculationResult ?? {\r\n            compressedFlangeNeutralAxisDistance: 0,\r\n            inOrOutplaneBendingMoment: 0,\r\n          },\r\n        inPlaneShearStrengthCalculationResult:\r\n          designStrengthPostInterventionCalculationResult?.inPlaneShearStrengthCalculationResult ?? {\r\n            shearStrength: 0,\r\n            verticalStress: 0,\r\n            wallSlendernessCorrectionCoefficient: 0,\r\n          },\r\n      },\r\n    })\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <div>\r\n          <span className=\"font-medium\">{t('panelThickness.label')}:</span>{' '}\r\n          <span>{form.getValues('panelThickness')}</span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t('panelThickness.description')}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('designCompressiveStrength.label')}:\r\n          </span>{' '}\r\n          <span>{form.getValues('designCompressiveStrength')}</span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t('designCompressiveStrength.description')}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('designShearStrengthRegularMasonry.label')}:\r\n          </span>{' '}\r\n          <span>{form.getValues('designShearStrengthRegularMasonry')}</span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t('designShearStrengthRegularMasonry.description')}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('designShearStrengthIrregularMasonry.label')}:\r\n          </span>{' '}\r\n          <span>{form.getValues('designShearStrengthIrregularMasonry')}</span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t('designShearStrengthIrregularMasonry.description')}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('designNormalElasticityModulus.label')}:\r\n          </span>{' '}\r\n          <span>{form.getValues('designNormalElasticityModulus')}</span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t('designNormalElasticityModulus.description')}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('designShearElasticityModulus.label')}:\r\n          </span>{' '}\r\n          <span>{form.getValues('designShearElasticityModulus')}</span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t('designShearElasticityModulus.description')}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('ultimateCompressiveStrainLinearBehavior.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {form.getValues('ultimateCompressiveStrainLinearBehavior')}\r\n          </span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t('ultimateCompressiveStrainLinearBehavior.description')}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">{t('secondCoefficient.label')}:</span>{' '}\r\n          <span>{form.getValues('secondCoefficient')}</span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t('secondCoefficient.description')}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">{t('panelSelfWeight.label')}:</span>{' '}\r\n          <span>{form.getValues('panelSelfWeight')}</span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t('panelSelfWeight.description')}\r\n          </p>\r\n        </div>\r\n\r\n        <Separator />\r\n        <h1 className=\"text-xl font-bold\">\r\n          {t('inPlaneBendingStrengthCalculationResult.title')}\r\n        </h1>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'inPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {form.getValues(\r\n              'inPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance',\r\n            )}\r\n          </span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t(\r\n              'inPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance.description',\r\n            )}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'inPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {form.getValues(\r\n              'inPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment',\r\n            )}\r\n          </span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t(\r\n              'inPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment.description',\r\n            )}\r\n          </p>\r\n        </div>\r\n\r\n        <Separator />\r\n        <h1 className=\"text-xl font-bold\">\r\n          {t('outOfPlaneBendingStrengthCalculationResult.title')}\r\n        </h1>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'outOfPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {form.getValues(\r\n              'outOfPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance',\r\n            )}\r\n          </span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t(\r\n              'outOfPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance.description',\r\n            )}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'outOfPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {form.getValues(\r\n              'outOfPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment',\r\n            )}\r\n          </span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t(\r\n              'outOfPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment.description',\r\n            )}\r\n          </p>\r\n        </div>\r\n\r\n        <Separator />\r\n        <h1 className=\"text-xl font-bold\">\r\n          {t('inPlaneShearStrengthCalculationResult.title')}\r\n        </h1>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('inPlaneShearStrengthCalculationResult.verticalStress.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {form.getValues(\r\n              'inPlaneShearStrengthCalculationResult.verticalStress',\r\n            )}\r\n          </span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t(\r\n              'inPlaneShearStrengthCalculationResult.verticalStress.description',\r\n            )}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('inPlaneShearStrengthCalculationResult.shearStrength.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {form.getValues(\r\n              'inPlaneShearStrengthCalculationResult.shearStrength',\r\n            )}\r\n          </span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t(\r\n              'inPlaneShearStrengthCalculationResult.shearStrength.description',\r\n            )}\r\n          </p>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAMA;AAIA;AACA;AACA;AACA;;;;;;;;AAMO,MAAM,yDAAyD,CAAC,EACrE,+CAA+C,EACzC;IACN,MAAM,IAAI,IAAA,yNAAe,EACvB;IAGF,MAAM,OACJ,IAAA,yKAAO,EAA8D;QACnE,UAAU,IAAA,6KAAW,EACnB,0MAAqD;QAEvD,eAAe;YACb,gBACE,iDAAiD,kBAAkB;YACrE,2BACE,iDAAiD,6BACjD;YACF,mCACE,iDAAiD,qCACjD;YACF,qCACE,iDAAiD,uCACjD;YACF,+BACE,iDAAiD,iCACjD;YACF,8BACE,iDAAiD,gCACjD;YACF,yCACE,iDAAiD,2CACjD;YACF,mBACE,iDAAiD,qBACjD;YACF,iBACE,iDAAiD,mBAAmB;YACtE,yCACE,iDAAiD,2CAA2C;gBAC1F,qCAAqC;gBACrC,2BAA2B;YAC7B;YACF,4CACE,iDAAiD,8CAA8C;gBAC7F,qCAAqC;gBACrC,2BAA2B;YAC7B;YACF,uCACE,iDAAiD,yCAAyC;gBACxF,eAAe;gBACf,gBAAgB;gBAChB,sCAAsC;YACxC;QACJ;IACF;IAEF,qBACE,8OAAC,wIAAI;;0BACH,8OAAC,8IAAU;0BACT,cAAA,8OAAC,6IAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,8OAAC,+IAAW;gBAAC,WAAU;;kCACrB,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAAwB;;;;;;;4BAAS;0CAClE,8OAAC;0CAAM,KAAK,SAAS,CAAC;;;;;;0CACtB,8OAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;kCAGP,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAmC;;;;;;;4BAChC;0CACR,8OAAC;0CAAM,KAAK,SAAS,CAAC;;;;;;0CACtB,8OAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;kCAGP,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA2C;;;;;;;4BACxC;0CACR,8OAAC;0CAAM,KAAK,SAAS,CAAC;;;;;;0CACtB,8OAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;kCAGP,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA6C;;;;;;;4BAC1C;0CACR,8OAAC;0CAAM,KAAK,SAAS,CAAC;;;;;;0CACtB,8OAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;kCAGP,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAuC;;;;;;;4BACpC;0CACR,8OAAC;0CAAM,KAAK,SAAS,CAAC;;;;;;0CACtB,8OAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;kCAGP,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAsC;;;;;;;4BACnC;0CACR,8OAAC;0CAAM,KAAK,SAAS,CAAC;;;;;;0CACtB,8OAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;kCAGP,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAiD;;;;;;;4BAC9C;0CACR,8OAAC;0CACE,KAAK,SAAS,CAAC;;;;;;0CAElB,8OAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;kCAGP,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAA2B;;;;;;;4BAAS;0CACrE,8OAAC;0CAAM,KAAK,SAAS,CAAC;;;;;;0CACtB,8OAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;kCAGP,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAAyB;;;;;;;4BAAS;0CACnE,8OAAC;0CAAM,KAAK,SAAS,CAAC;;;;;;0CACtB,8OAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;kCAIP,8OAAC,yLAAS;;;;;kCACV,8OAAC;wBAAG,WAAU;kCACX,EAAE;;;;;;kCAEL,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EACC;oCACA;;;;;;;4BAEI;0CACR,8OAAC;0CACE,KAAK,SAAS,CACb;;;;;;0CAGJ,8OAAC;gCAAE,WAAU;0CACV,EACC;;;;;;;;;;;;kCAIN,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EACC;oCACA;;;;;;;4BAEI;0CACR,8OAAC;0CACE,KAAK,SAAS,CACb;;;;;;0CAGJ,8OAAC;gCAAE,WAAU;0CACV,EACC;;;;;;;;;;;;kCAKN,8OAAC,yLAAS;;;;;kCACV,8OAAC;wBAAG,WAAU;kCACX,EAAE;;;;;;kCAEL,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EACC;oCACA;;;;;;;4BAEI;0CACR,8OAAC;0CACE,KAAK,SAAS,CACb;;;;;;0CAGJ,8OAAC;gCAAE,WAAU;0CACV,EACC;;;;;;;;;;;;kCAIN,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EACC;oCACA;;;;;;;4BAEI;0CACR,8OAAC;0CACE,KAAK,SAAS,CACb;;;;;;0CAGJ,8OAAC;gCAAE,WAAU;0CACV,EACC;;;;;;;;;;;;kCAKN,8OAAC,yLAAS;;;;;kCACV,8OAAC;wBAAG,WAAU;kCACX,EAAE;;;;;;kCAEL,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA8D;;;;;;;4BAC3D;0CACR,8OAAC;0CACE,KAAK,SAAS,CACb;;;;;;0CAGJ,8OAAC;gCAAE,WAAU;0CACV,EACC;;;;;;;;;;;;kCAIN,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA6D;;;;;;;4BAC1D;0CACR,8OAAC;0CACE,KAAK,SAAS,CACb;;;;;;0CAGJ,8OAAC;gCAAE,WAAU;0CACV,EACC;;;;;;;;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 3845, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport type { ModuleWithParamsCrm } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useModuleCalculation } from '@atlas/lib/mutation/modules/use-module-calculation'\r\nimport { useProductsByCategory } from '@atlas/lib/query/products/use-products-by-category'\r\nimport {\r\n  type DesignStrengthPostInterventionInputSchemaInputs,\r\n  designStrengthPostInterventionInputSchema,\r\n} from '@atlas/types/schemas/masonry/crm-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Separator } from '@radix-ui/react-dropdown-menu'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useMemo } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\nimport { CrmPostInterventionDesignStrengthCalculationResultForm } from './crm-post-intervention-design-strength-calculation-result-form'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  module: ModuleWithParamsCrm\r\n}\r\n\r\nconst _MESH_OPTIONS_FILTER = [\r\n  'Kimitech WALLMESH HR (Certificata CVT)',\r\n  'Kimitech WALLMESH HR-HD (Marcata CE)',\r\n  'Kimitech WALLMESH MR (Certificata CVT)',\r\n  'Kimitech WALLMESH MR-HD (Marcata CE)',\r\n]\r\n\r\nconst _CONNECTOR_OPTIONS_FILTER = [\r\n  'Kimisteel INOX X-BAR da 8 mm (Marcata CE)',\r\n  'Kimisteel INOX X-BAR da 12 mm (Marcata CE)',\r\n  'Kimisteel INOX X-BAR da 10 mm (Marcata CE)',\r\n  'Kimisteel PLUG VR (Certificata CVT)',\r\n  'Kimisteel PLUG VR HD (Marcata CE)',\r\n]\r\nconst _MATRIX_OPTIONS_FILTER = [\r\n  'Basic MALTA M15',\r\n  'Basic MALTA R3',\r\n  'Betonfix FB',\r\n  'Tectoria M15',\r\n]\r\nexport const CrmPostInterventionDesignStrengthCalculationForm = ({\r\n  session,\r\n  projectId,\r\n  module,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.calculations.crm.postIntervention.designStrengthPostInterventionCalculationResult',\r\n  )\r\n  const tAction = useTranslations(\r\n    'actions.calculations.anti-overturning.composite-reinforcement-system',\r\n  )\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  const designStrengthPostInterventionCalculationResult =\r\n    module.postIntervention?.designStrengthPostInterventionCalculationResult\r\n\r\n  const meshProduct =\r\n    module?.postIntervention?.designStrengthPostInterventionVerifyInput\r\n      ?.meshInput?.meshProduct\r\n  const connectorProduct =\r\n    module?.postIntervention?.designStrengthPostInterventionVerifyInput\r\n      ?.connectorInput?.connectorProduct\r\n  const matrixProduct =\r\n    module?.postIntervention?.designStrengthPostInterventionVerifyInput\r\n      ?.matrixInput?.matrixProduct\r\n\r\n  const form = useForm<DesignStrengthPostInterventionInputSchemaInputs>({\r\n    resolver: zodResolver(designStrengthPostInterventionInputSchema),\r\n    defaultValues: {\r\n      calculationType: 'DESIGN_STRENGTH_VERIFY',\r\n      input: {\r\n        meshInput: {\r\n          meshProduct: {\r\n            id: meshProduct?.id ?? '',\r\n            name: meshProduct?.name,\r\n            sourceType: 'DATABASE',\r\n          },\r\n        },\r\n        connectorInput: {\r\n          connectorProduct: {\r\n            id: connectorProduct?.id ?? '',\r\n            name: connectorProduct?.name,\r\n            sourceType: 'DATABASE',\r\n          },\r\n        },\r\n        matrixInput: {\r\n          matrixProduct: {\r\n            id: matrixProduct?.id ?? '',\r\n            name: matrixProduct?.name ?? '',\r\n            sourceType: 'DATABASE',\r\n          },\r\n        },\r\n        reinforcementTotalThickness:\r\n          module?.postIntervention?.designStrengthPostInterventionVerifyInput\r\n            ?.reinforcementTotalThickness ?? 0,\r\n      },\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useModuleCalculation(session.accessToken, {\r\n    onSuccess: () => {\r\n      toast.success(tAction('calculate.success'))\r\n    },\r\n    onError: error => {\r\n      toast.error(tAction('calculate.failure', { error: error.message }))\r\n    },\r\n  })\r\n\r\n  const handleFormSubmit = (\r\n    body: DesignStrengthPostInterventionInputSchemaInputs,\r\n  ) => {\r\n    mutate({ projectId, moduleId: module.id, body })\r\n  }\r\n\r\n  // LATER CHANGE TO CRM TYPE\r\n  const {\r\n    data: productsAll,\r\n    isError: errorGettingProducts,\r\n    isLoading: isLoadingProducts,\r\n  } = useProductsByCategory(session, 'CRM', 0, 100)\r\n\r\n  const productsMeshOptions = [\r\n    ...(productsAll?.content ?? [])\r\n      .filter(m => m.productType === 'MESH')\r\n      .map(m => ({\r\n        value: m.id,\r\n        label: m.name ?? t('product.unnamed'),\r\n      })),\r\n    // temporary disable\r\n    // { value: 'custom', label: t('product.custom') },\r\n  ]\r\n\r\n  const productsConnectorOptions = [\r\n    ...(productsAll?.content\r\n      .filter(m => m.productType === 'CONNECTOR')\r\n      .map(m => ({\r\n        value: m.id,\r\n        label: m.name ?? t('product.unnamed'),\r\n      })) ?? []),\r\n    // temporary disable\r\n    // { value: 'custom', label: t('product.custom') },\r\n  ]\r\n\r\n  const productsMatrixOptions = [\r\n    ...(productsAll?.content\r\n      .filter(m => m.productType === 'MATRIX')\r\n      .map(m => ({\r\n        value: m.id,\r\n        label: m.name ?? t('product.unnamed'),\r\n      })) ?? []),\r\n    // temporary disable\r\n    // { value: 'custom', label: t('product.custom') },\r\n  ]\r\n\r\n  const [productMeshId] = form.watch(['input.meshInput.meshProduct.id'])\r\n  const [productConnectorId] = form.watch([\r\n    'input.connectorInput.connectorProduct.id',\r\n  ])\r\n  const [productMatrixId] = form.watch(['input.matrixInput.matrixProduct.id'])\r\n\r\n  const selectedProductMesh = useMemo(\r\n    () =>\r\n      productsAll?.content\r\n        .filter(m => m.productType === 'MESH')\r\n        .find(p => p.id === productMeshId),\r\n    [productMeshId, productsAll],\r\n  )\r\n\r\n  const selectedProductConnector = useMemo(\r\n    () =>\r\n      productsAll?.content\r\n        .filter(m => m.productType === 'CONNECTOR')\r\n        .find(p => p.id === productConnectorId),\r\n    [productConnectorId, productsAll],\r\n  )\r\n\r\n  const selectedProductMatrix = useMemo(\r\n    () =>\r\n      productsAll?.content\r\n        .filter(m => m.productType === 'MATRIX')\r\n        .find(p => p.id === productMatrixId),\r\n    [productMatrixId, productsAll],\r\n  )\r\n\r\n  useEffect(() => {\r\n    if (selectedProductMesh) {\r\n      form.setValue('input.meshInput.meshProduct', {\r\n        id: selectedProductMesh.id,\r\n        name: selectedProductMesh.name,\r\n        sourceType: 'DATABASE',\r\n      })\r\n    }\r\n\r\n    if (selectedProductConnector) {\r\n      form.setValue('input.connectorInput.connectorProduct', {\r\n        id: selectedProductConnector.id,\r\n        name: selectedProductConnector.name,\r\n        sourceType: 'DATABASE',\r\n      })\r\n    }\r\n\r\n    if (selectedProductMatrix) {\r\n      form.setValue('input.matrixInput.matrixProduct', {\r\n        id: selectedProductMatrix.id,\r\n        name: selectedProductMatrix.name,\r\n        sourceType: 'DATABASE',\r\n      })\r\n    }\r\n  }, [\r\n    form,\r\n    selectedProductMesh,\r\n    selectedProductConnector,\r\n    selectedProductMatrix,\r\n  ])\r\n\r\n  return (\r\n    <div className=\"flex flex-col justify-center gap-4\">\r\n      <Form {...form}>\r\n        <form\r\n          className=\"space-y-4 rounded-md border p-4\"\r\n          onSubmit={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          <Image\r\n            src=\"/assets/masonry-anti-overturning/antibaltamento-verify.jpg\"\r\n            alt=\"composite reinforcement system verify\"\r\n            height={250}\r\n            width={500}\r\n            className=\"mx-auto rounded-md object-contain\"\r\n            priority\r\n          />\r\n          {/* Mesh Section */}\r\n          <h1 className=\"text-xl font-bold\">{t('mesh-sub-heading')}</h1>\r\n          <SelectFormInput\r\n            control={form.control}\r\n            name=\"input.meshInput.meshProduct.id\"\r\n            options={productsMeshOptions}\r\n            t={t}\r\n            loading={isLoadingProducts}\r\n            requestError={errorGettingProducts}\r\n            // get the error message from the form state if any\r\n            errorMessage={\r\n              form.formState.errors.input?.meshInput?.meshProduct?.id?.message\r\n            }\r\n          />\r\n          <Separator />\r\n          {/* Connector Section */}\r\n          <h1 className=\"text-xl font-bold\">{t('connector-sub-heading')}</h1>\r\n          <SelectFormInput\r\n            control={form.control}\r\n            name=\"input.connectorInput.connectorProduct.id\"\r\n            options={productsConnectorOptions}\r\n            t={t}\r\n            loading={isLoadingProducts}\r\n            requestError={errorGettingProducts}\r\n            errorMessage={\r\n              form.formState.errors.input?.connectorInput?.connectorProduct?.id\r\n                ?.message\r\n            }\r\n          />\r\n          <Separator />\r\n          {/* Matrix Section */}\r\n          <h1 className=\"text-xl font-bold\">{t('matrix-sub-heading')}</h1>\r\n          <SelectFormInput\r\n            control={form.control}\r\n            name=\"input.matrixInput.matrixProduct.id\"\r\n            options={productsMatrixOptions}\r\n            t={t}\r\n            loading={isLoadingProducts}\r\n            requestError={errorGettingProducts}\r\n            errorMessage={\r\n              form.formState.errors.input?.matrixInput?.matrixProduct?.id\r\n                ?.message\r\n            }\r\n          />\r\n          <Separator />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.reinforcementTotalThickness\"\r\n            t={t}\r\n          />\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full sm:w-auto\"\r\n            disabled={isPending}\r\n          >\r\n            {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n            {tCommon('calculate')}\r\n          </Button>\r\n        </form>\r\n      </Form>\r\n      {designStrengthPostInterventionCalculationResult && (\r\n        <CrmPostInterventionDesignStrengthCalculationResultForm\r\n          designStrengthPostInterventionCalculationResult={\r\n            designStrengthPostInterventionCalculationResult\r\n          }\r\n        />\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAGA;AACA;AACA;AAIA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;AAQA,MAAM,uBAAuB;IAC3B;IACA;IACA;IACA;CACD;AAED,MAAM,4BAA4B;IAChC;IACA;IACA;IACA;IACA;CACD;AACD,MAAM,yBAAyB;IAC7B;IACA;IACA;IACA;CACD;AACM,MAAM,mDAAmD,CAAC,EAC/D,OAAO,EACP,SAAS,EACT,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EACvB;IAEF,MAAM,UAAU,IAAA,yNAAe,EAC7B;IAEF,MAAM,UAAU,IAAA,yNAAe,EAAC;IAEhC,MAAM,kDACJ,OAAO,gBAAgB,EAAE;IAE3B,MAAM,cACJ,QAAQ,kBAAkB,2CACtB,WAAW;IACjB,MAAM,mBACJ,QAAQ,kBAAkB,2CACtB,gBAAgB;IACtB,MAAM,gBACJ,QAAQ,kBAAkB,2CACtB,aAAa;IAEnB,MAAM,OAAO,IAAA,yKAAO,EAAkD;QACpE,UAAU,IAAA,6KAAW,EAAC,8LAAyC;QAC/D,eAAe;YACb,iBAAiB;YACjB,OAAO;gBACL,WAAW;oBACT,aAAa;wBACX,IAAI,aAAa,MAAM;wBACvB,MAAM,aAAa;wBACnB,YAAY;oBACd;gBACF;gBACA,gBAAgB;oBACd,kBAAkB;wBAChB,IAAI,kBAAkB,MAAM;wBAC5B,MAAM,kBAAkB;wBACxB,YAAY;oBACd;gBACF;gBACA,aAAa;oBACX,eAAe;wBACb,IAAI,eAAe,MAAM;wBACzB,MAAM,eAAe,QAAQ;wBAC7B,YAAY;oBACd;gBACF;gBACA,6BACE,QAAQ,kBAAkB,2CACtB,+BAA+B;YACvC;QACF;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,0LAAoB,EAAC,QAAQ,WAAW,EAAE;QACtE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;QACxB;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,qBAAqB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAClE;IACF;IAEA,MAAM,mBAAmB,CACvB;QAEA,OAAO;YAAE;YAAW,UAAU,OAAO,EAAE;YAAE;QAAK;IAChD;IAEA,2BAA2B;IAC3B,MAAM,EACJ,MAAM,WAAW,EACjB,SAAS,oBAAoB,EAC7B,WAAW,iBAAiB,EAC7B,GAAG,IAAA,8LAAqB,EAAC,SAAS,OAAO,GAAG;IAE7C,MAAM,sBAAsB;WACvB,CAAC,aAAa,WAAW,EAAE,EAC3B,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK,QAC9B,GAAG,CAAC,CAAA,IAAK,CAAC;gBACT,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,IAAI,IAAI,EAAE;YACrB,CAAC;KAGJ;IAED,MAAM,2BAA2B;WAC3B,aAAa,QACd,OAAO,CAAA,IAAK,EAAE,WAAW,KAAK,aAC9B,IAAI,CAAA,IAAK,CAAC;gBACT,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,IAAI,IAAI,EAAE;YACrB,CAAC,MAAM,EAAE;KAGZ;IAED,MAAM,wBAAwB;WACxB,aAAa,QACd,OAAO,CAAA,IAAK,EAAE,WAAW,KAAK,UAC9B,IAAI,CAAA,IAAK,CAAC;gBACT,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,IAAI,IAAI,EAAE;YACrB,CAAC,MAAM,EAAE;KAGZ;IAED,MAAM,CAAC,cAAc,GAAG,KAAK,KAAK,CAAC;QAAC;KAAiC;IACrE,MAAM,CAAC,mBAAmB,GAAG,KAAK,KAAK,CAAC;QACtC;KACD;IACD,MAAM,CAAC,gBAAgB,GAAG,KAAK,KAAK,CAAC;QAAC;KAAqC;IAE3E,MAAM,sBAAsB,IAAA,gNAAO,EACjC,IACE,aAAa,QACV,OAAO,CAAA,IAAK,EAAE,WAAW,KAAK,QAC9B,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK,gBACxB;QAAC;QAAe;KAAY;IAG9B,MAAM,2BAA2B,IAAA,gNAAO,EACtC,IACE,aAAa,QACV,OAAO,CAAA,IAAK,EAAE,WAAW,KAAK,aAC9B,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK,qBACxB;QAAC;QAAoB;KAAY;IAGnC,MAAM,wBAAwB,IAAA,gNAAO,EACnC,IACE,aAAa,QACV,OAAO,CAAA,IAAK,EAAE,WAAW,KAAK,UAC9B,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK,kBACxB;QAAC;QAAiB;KAAY;IAGhC,IAAA,kNAAS,EAAC;QACR,IAAI,qBAAqB;YACvB,KAAK,QAAQ,CAAC,+BAA+B;gBAC3C,IAAI,oBAAoB,EAAE;gBAC1B,MAAM,oBAAoB,IAAI;gBAC9B,YAAY;YACd;QACF;QAEA,IAAI,0BAA0B;YAC5B,KAAK,QAAQ,CAAC,yCAAyC;gBACrD,IAAI,yBAAyB,EAAE;gBAC/B,MAAM,yBAAyB,IAAI;gBACnC,YAAY;YACd;QACF;QAEA,IAAI,uBAAuB;YACzB,KAAK,QAAQ,CAAC,mCAAmC;gBAC/C,IAAI,sBAAsB,EAAE;gBAC5B,MAAM,sBAAsB,IAAI;gBAChC,YAAY;YACd;QACF;IACF,GAAG;QACD;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,wIAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,8OAAC;oBACC,WAAU;oBACV,UAAU,KAAK,YAAY,CAAC;;sCAE5B,8OAAC,wIAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,QAAQ;4BACR,OAAO;4BACP,WAAU;4BACV,QAAQ;;;;;;sCAGV,8OAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS;4BACT,GAAG;4BACH,SAAS;4BACT,cAAc;4BACd,mDAAmD;4BACnD,cACE,KAAK,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,WAAW,aAAa,IAAI;;;;;;sCAG7D,8OAAC,yLAAS;;;;;sCAEV,8OAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS;4BACT,GAAG;4BACH,SAAS;4BACT,cAAc;4BACd,cACE,KAAK,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,gBAAgB,kBAAkB,IAC3D;;;;;;sCAGR,8OAAC,yLAAS;;;;;sCAEV,8OAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS;4BACT,GAAG;4BACH,SAAS;4BACT,cAAc;4BACd,cACE,KAAK,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,aAAa,eAAe,IACrD;;;;;;sCAGR,8OAAC,yLAAS;;;;;sCACV,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,4IAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU;;gCAET,2BAAa,8OAAC,oOAAW;oCAAC,WAAU;;;;;;gCACpC,QAAQ;;;;;;;;;;;;;;;;;;YAId,iEACC,8OAAC,kXAAsD;gBACrD,iDACE;;;;;;;;;;;;AAMZ", "debugId": null}}, {"offset": {"line": 4183, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/reinforced-masonry-characteristics-params-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport {\r\n  BINDER_MIXTURE_INJECTIONS,\r\n  bindingCoefficientValues,\r\n  CRM_MASONRY_TYPE,\r\n  ENHANCEMENT_CHARACTERISTICS,\r\n  MODULE_BINDER_MIXTURE_INJECTIONS,\r\n  MODULE_REINFORCEMENT_APPLICATION_TYPE,\r\n  maxAmplficationCoefficientValues,\r\n  postInterventionFirstSideReinforcedPlasterCoefficientValues,\r\n  REINFORCEMENT_APPLICATION_TYPE,\r\n} from '@atlas/constants/module'\r\nimport type { ModuleWithParamsCrm } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type CrmFormSchemaInputs,\r\n  type ReinforcedMasonryCharacteristicsParamsSchemaInputs,\r\n  reinforcedMasonryCharacteristicsParamsSchema,\r\n} from '@atlas/types/schemas/masonry/crm-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  module: ModuleWithParamsCrm\r\n  onSave: () => void\r\n}\r\n\r\nexport const ReinforcedMasonryCharacteristicsParamsForm = ({\r\n  session,\r\n  projectId,\r\n  module,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.project-params.crm.crmPostIntervention.reinforcedMasonryCharacteristics',\r\n  )\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const moduleId = module.id\r\n  const reinforcedMasonryCharacteristicsParams =\r\n    module?.postIntervention?.reinforcedMasonryCharacteristicsParams\r\n  const form = useForm<ReinforcedMasonryCharacteristicsParamsSchemaInputs>({\r\n    resolver: zodResolver(reinforcedMasonryCharacteristicsParamsSchema),\r\n    defaultValues: {\r\n      reinforcementApplicationType:\r\n        reinforcedMasonryCharacteristicsParams?.reinforcementApplicationType ??\r\n        REINFORCEMENT_APPLICATION_TYPE.APPLICAZIONE_SU_ENTRAMBI_I_LATI,\r\n      singleFaceApplicationReductionCoefficient:\r\n        reinforcedMasonryCharacteristicsParams?.singleFaceApplicationReductionCoefficient ??\r\n        30,\r\n      reinforcedPlasterCoefficient:\r\n        reinforcedMasonryCharacteristicsParams?.reinforcedPlasterCoefficient ??\r\n        0,\r\n      binderMixturesInjections:\r\n        reinforcedMasonryCharacteristicsParams?.binderMixturesInjections ??\r\n        BINDER_MIXTURE_INJECTIONS.YES,\r\n      correctionCoefficient:\r\n        reinforcedMasonryCharacteristicsParams?.correctionCoefficient ?? 0,\r\n      overallAmplificationCoefficient:\r\n        reinforcedMasonryCharacteristicsParams?.overallAmplificationCoefficient ??\r\n        0,\r\n      amplifiedAverageCompressiveStrength:\r\n        reinforcedMasonryCharacteristicsParams?.amplifiedAverageCompressiveStrength ??\r\n        0,\r\n      amplifiedAverageShearStrengthRegularMasonry:\r\n        reinforcedMasonryCharacteristicsParams?.amplifiedAverageShearStrengthRegularMasonry ??\r\n        0,\r\n      amplifiedAverageShearStrengthIrregularMasonry:\r\n        reinforcedMasonryCharacteristicsParams?.amplifiedAverageShearStrengthIrregularMasonry ??\r\n        0,\r\n      amplifiedAverageNormalElasticityModulus:\r\n        reinforcedMasonryCharacteristicsParams?.amplifiedAverageNormalElasticityModulus ??\r\n        0,\r\n      amplifiedAverageShearElasticityModulus:\r\n        reinforcedMasonryCharacteristicsParams?.amplifiedAverageShearElasticityModulus ??\r\n        0,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const reinforcementApplicationType = form.watch(\r\n    'reinforcementApplicationType',\r\n  )\r\n\r\n  const binderMixtureInjections = form.watch('binderMixturesInjections')\r\n\r\n  const masonryType =\r\n    module.preIntervention?.existingMasonryCharacteristicsParams?.masonryType ??\r\n    CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI\r\n\r\n  const preInterventionEnhancementCharacteristics =\r\n    module.preIntervention?.existingMasonryCharacteristicsParams\r\n      ?.enhancementCharacteristics ?? ENHANCEMENT_CHARACTERISTICS.NON_PRESENTI\r\n  const preInterventionCorrectionCoefficient =\r\n    module.preIntervention?.existingMasonryCharacteristicsParams\r\n      ?.correctionCoefficient ?? 1\r\n  const averageCompressiveStrengthPreIntervention =\r\n    module.preIntervention?.existingMasonryCharacteristicsParams\r\n      ?.averageCompressiveStrength ?? 0\r\n\r\n  useEffect(() => {\r\n    const singleFaceApplicationReductionCoefficient =\r\n      reinforcementApplicationType ===\r\n      REINFORCEMENT_APPLICATION_TYPE.APPLICAZIONE_SU_ENTRAMBI_I_LATI\r\n        ? 0\r\n        : 30\r\n    form.setValue(\r\n      'singleFaceApplicationReductionCoefficient',\r\n      singleFaceApplicationReductionCoefficient,\r\n    )\r\n\r\n    // get the coefficient based on the masonry type:\r\n    // ((VLOOKUP('ANTE-INTERVENTO'!D13,Parametri!B38:J45,7,0)-1)/100*(100-D11))+1\r\n    const reinforcedPlasterCoefficient =\r\n      ((postInterventionFirstSideReinforcedPlasterCoefficientValues[\r\n        masonryType\r\n      ] -\r\n        1) /\r\n        100) *\r\n        (100 - singleFaceApplicationReductionCoefficient) +\r\n      1\r\n    form.setValue('reinforcedPlasterCoefficient', reinforcedPlasterCoefficient)\r\n\r\n    // correctionCoefficient =IF(C13=\"SI\",VLOOKUP('ANTE-INTERVENTO'!D13,Parametri!B38:J45,6,0),1)\r\n    const correctionCoefficient =\r\n      binderMixtureInjections === BINDER_MIXTURE_INJECTIONS.YES\r\n        ? bindingCoefficientValues[masonryType]\r\n        : 1\r\n    form.setValue('correctionCoefficient', correctionCoefficient)\r\n\r\n    // =IF('ANTE-INTERVENTO'!C21=\"non presenti\",\r\n    //    MIN(D13*D12,VLOOKUP('ANTE-INTERVENTO'!D13,Parametri!B38:J45,9,0)),\r\n    //    IF('ANTE-INTERVENTO'!C21=\"Malta buona\",\r\n    //      IF(C13=\"NO\",\r\n    //        MIN('ANTE-INTERVENTO'!D21*D12,VLOOKUP('ANTE-INTERVENTO'!D13,Parametri!B38:J45,9,0)),\r\n    //        MIN(MAX('ANTE-INTERVENTO'!D21,D13)*D12,cerva.cert('ANTE-INTERVENTO'!D13,Parametri!B38:J45,9,0)))))\r\n    const overallAmplificationCoefficient =\r\n      preInterventionEnhancementCharacteristics ===\r\n      ENHANCEMENT_CHARACTERISTICS.NON_PRESENTI\r\n        ? Math.min(\r\n            correctionCoefficient * reinforcedPlasterCoefficient,\r\n            maxAmplficationCoefficientValues[masonryType],\r\n          )\r\n        : preInterventionEnhancementCharacteristics ===\r\n            ENHANCEMENT_CHARACTERISTICS.MALTA_BUONA\r\n          ? binderMixtureInjections === BINDER_MIXTURE_INJECTIONS.NO\r\n            ? Math.min(\r\n                preInterventionCorrectionCoefficient *\r\n                  reinforcedPlasterCoefficient,\r\n                maxAmplficationCoefficientValues[masonryType],\r\n              )\r\n            : Math.min(\r\n                Math.max(\r\n                  preInterventionCorrectionCoefficient,\r\n                  correctionCoefficient,\r\n                ) * reinforcedPlasterCoefficient,\r\n                maxAmplficationCoefficientValues[masonryType],\r\n              )\r\n          : // we added this \"1\" for values other then Malta Buona and Non Presenti, which is not our case now\r\n            1\r\n\r\n    form.setValue(\r\n      'overallAmplificationCoefficient',\r\n      overallAmplificationCoefficient,\r\n    )\r\n\r\n    // amplifiedAverageCompressiveStrength = overallAmplificationCoefficient * 'ANTE-INTERVENTO:amplifiedAverageCompressiveStrength\r\n    const amplifiedAverageCompressiveStrength =\r\n      overallAmplificationCoefficient *\r\n      averageCompressiveStrengthPreIntervention\r\n    form.setValue(\r\n      'amplifiedAverageCompressiveStrength',\r\n      amplifiedAverageCompressiveStrength,\r\n    )\r\n\r\n    // amplifiedAverageShearStrengthRegularMasonry = overallAmplificationCoefficient * 'ANTE-INTERVENTO:amplifiedAverageShearStrengthRegularMasonry\r\n    const amplifiedAverageShearStrengthRegularMasonry =\r\n      overallAmplificationCoefficient *\r\n      (module.preIntervention?.existingMasonryCharacteristicsParams\r\n        ?.averageShearStrengthRegularMasonry ?? 0)\r\n    form.setValue(\r\n      'amplifiedAverageShearStrengthRegularMasonry',\r\n      amplifiedAverageShearStrengthRegularMasonry,\r\n    )\r\n\r\n    // amplifiedAverageShearStrengthIrregularMasonry = overallAmplificationCoefficient * 'ANTE-INTERVENTO:amplifiedAverageShearStrengthIrregularMasonry\r\n    const amplifiedAverageShearStrengthIrregularMasonry =\r\n      overallAmplificationCoefficient *\r\n      (module.preIntervention?.existingMasonryCharacteristicsParams\r\n        ?.averageShearStrengthIrregularMasonry ?? 0)\r\n    form.setValue(\r\n      'amplifiedAverageShearStrengthIrregularMasonry',\r\n      amplifiedAverageShearStrengthIrregularMasonry,\r\n    )\r\n\r\n    // amplifiedAverageNormalElasticityModulus = overallAmplificationCoefficient * 'ANTE-INTERVENTO:amplifiedAverageNormalElasticityModulus\r\n    const amplifiedAverageNormalElasticityModulus =\r\n      overallAmplificationCoefficient *\r\n      (module.preIntervention?.existingMasonryCharacteristicsParams\r\n        ?.averageNormalElasticityModulus ?? 0)\r\n    form.setValue(\r\n      'amplifiedAverageNormalElasticityModulus',\r\n      amplifiedAverageNormalElasticityModulus,\r\n    )\r\n\r\n    // amplifiedAverageShearElasticityModulus = overallAmplificationCoefficient * 'ANTE-INTERVENTO:amplifiedAverageShearElasticityModulus\r\n    const amplifiedAverageShearElasticityModulus =\r\n      overallAmplificationCoefficient *\r\n      (module.preIntervention?.existingMasonryCharacteristicsParams\r\n        ?.averageShearElasticityModulus ?? 0)\r\n    form.setValue(\r\n      'amplifiedAverageShearElasticityModulus',\r\n      amplifiedAverageShearElasticityModulus,\r\n    )\r\n  }, [\r\n    form,\r\n    reinforcementApplicationType,\r\n    masonryType,\r\n    binderMixtureInjections,\r\n    preInterventionEnhancementCharacteristics,\r\n    preInterventionCorrectionCoefficient,\r\n    averageCompressiveStrengthPreIntervention,\r\n    module.preIntervention?.existingMasonryCharacteristicsParams\r\n      ?.averageNormalElasticityModulus,\r\n    module.preIntervention?.existingMasonryCharacteristicsParams\r\n      ?.averageShearElasticityModulus,\r\n    module.preIntervention?.existingMasonryCharacteristicsParams\r\n      ?.averageShearStrengthIrregularMasonry,\r\n    module.preIntervention?.existingMasonryCharacteristicsParams\r\n      ?.averageShearStrengthRegularMasonry,\r\n  ])\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: ReinforcedMasonryCharacteristicsParamsSchemaInputs) => {\r\n      const updatedModule: CrmFormSchemaInputs = {\r\n        ...module,\r\n        preIntervention: module.preIntervention ?? undefined,\r\n        postIntervention: {\r\n          reinforcedMasonryCharacteristicsParams: body,\r\n          designStrengthPostInterventionVerifyInput:\r\n            module.postIntervention\r\n              ?.designStrengthPostInterventionVerifyInput ?? undefined,\r\n          designStrengthPostInterventionCalculationResult:\r\n            module.postIntervention\r\n              ?.designStrengthPostInterventionCalculationResult ?? undefined,\r\n        },\r\n      }\r\n\r\n      mutate({ projectId, moduleId, body: updatedModule })\r\n    },\r\n    [mutate, projectId, moduleId, module],\r\n  )\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <Image\r\n          src=\"/assets/masonry-crm/crm_post_intervento.jpg\"\r\n          alt=\"site characteristics\"\r\n          height={250}\r\n          width={500}\r\n          className=\"mx-auto rounded-md object-contain\"\r\n          priority\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"reinforcementApplicationType\"\r\n          options={MODULE_REINFORCEMENT_APPLICATION_TYPE}\r\n          optionLabelFn={p => t(`reinforcementApplicationType.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"singleFaceApplicationReductionCoefficient\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"reinforcedPlasterCoefficient\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"binderMixturesInjections\"\r\n          options={MODULE_BINDER_MIXTURE_INJECTIONS}\r\n          optionLabelFn={p => t(`binderMixturesInjections.${p}`)}\r\n          t={t}\r\n        />\r\n\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"correctionCoefficient\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"overallAmplificationCoefficient\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"amplifiedAverageCompressiveStrength\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"amplifiedAverageShearStrengthRegularMasonry\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"amplifiedAverageShearStrengthIrregularMasonry\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"amplifiedAverageNormalElasticityModulus\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"amplifiedAverageShearElasticityModulus\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAaA;AACA;AAKA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AASO,MAAM,6CAA6C,CAAC,EACzD,OAAO,EACP,SAAS,EACT,MAAM,EACN,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EACvB;IAEF,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,WAAW,OAAO,EAAE;IAC1B,MAAM,yCACJ,QAAQ,kBAAkB;IAC5B,MAAM,OAAO,IAAA,yKAAO,EAAqD;QACvE,UAAU,IAAA,6KAAW,EAAC,iMAA4C;QAClE,eAAe;YACb,8BACE,wCAAwC,gCACxC,6JAA8B,CAAC,+BAA+B;YAChE,2CACE,wCAAwC,6CACxC;YACF,8BACE,wCAAwC,gCACxC;YACF,0BACE,wCAAwC,4BACxC,wJAAyB,CAAC,GAAG;YAC/B,uBACE,wCAAwC,yBAAyB;YACnE,iCACE,wCAAwC,mCACxC;YACF,qCACE,wCAAwC,uCACxC;YACF,6CACE,wCAAwC,+CACxC;YACF,+CACE,wCAAwC,iDACxC;YACF,yCACE,wCAAwC,2CACxC;YACF,wCACE,wCAAwC,0CACxC;QACJ;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAW;wBAAE;wBAAW;oBAAS;iBAAE;YAChD;YACA;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,+BAA+B,KAAK,KAAK,CAC7C;IAGF,MAAM,0BAA0B,KAAK,KAAK,CAAC;IAE3C,MAAM,cACJ,OAAO,eAAe,EAAE,sCAAsC,eAC9D,+IAAgB,CAAC,oCAAoC;IAEvD,MAAM,4CACJ,OAAO,eAAe,EAAE,sCACpB,8BAA8B,0JAA2B,CAAC,YAAY;IAC5E,MAAM,uCACJ,OAAO,eAAe,EAAE,sCACpB,yBAAyB;IAC/B,MAAM,4CACJ,OAAO,eAAe,EAAE,sCACpB,8BAA8B;IAEpC,IAAA,kNAAS,EAAC;QACR,MAAM,4CACJ,iCACA,6JAA8B,CAAC,+BAA+B,GAC1D,IACA;QACN,KAAK,QAAQ,CACX,6CACA;QAGF,iDAAiD;QACjD,6EAA6E;QAC7E,MAAM,+BACJ,AAAC,CAAC,0LAA2D,CAC3D,YACD,GACC,CAAC,IACD,MACA,CAAC,MAAM,yCAAyC,IAClD;QACF,KAAK,QAAQ,CAAC,gCAAgC;QAE9C,6FAA6F;QAC7F,MAAM,wBACJ,4BAA4B,wJAAyB,CAAC,GAAG,GACrD,uJAAwB,CAAC,YAAY,GACrC;QACN,KAAK,QAAQ,CAAC,yBAAyB;QAEvC,4CAA4C;QAC5C,wEAAwE;QACxE,6CAA6C;QAC7C,oBAAoB;QACpB,8FAA8F;QAC9F,4GAA4G;QAC5G,MAAM,kCACJ,8CACA,0JAA2B,CAAC,YAAY,GACpC,KAAK,GAAG,CACN,wBAAwB,8BACxB,+JAAgC,CAAC,YAAY,IAE/C,8CACE,0JAA2B,CAAC,WAAW,GACvC,4BAA4B,wJAAyB,CAAC,EAAE,GACtD,KAAK,GAAG,CACN,uCACE,8BACF,+JAAgC,CAAC,YAAY,IAE/C,KAAK,GAAG,CACN,KAAK,GAAG,CACN,sCACA,yBACE,8BACJ,+JAAgC,CAAC,YAAY,IAGjD;QAER,KAAK,QAAQ,CACX,mCACA;QAGF,+HAA+H;QAC/H,MAAM,sCACJ,kCACA;QACF,KAAK,QAAQ,CACX,uCACA;QAGF,+IAA+I;QAC/I,MAAM,8CACJ,kCACA,CAAC,OAAO,eAAe,EAAE,sCACrB,sCAAsC,CAAC;QAC7C,KAAK,QAAQ,CACX,+CACA;QAGF,mJAAmJ;QACnJ,MAAM,gDACJ,kCACA,CAAC,OAAO,eAAe,EAAE,sCACrB,wCAAwC,CAAC;QAC/C,KAAK,QAAQ,CACX,iDACA;QAGF,uIAAuI;QACvI,MAAM,0CACJ,kCACA,CAAC,OAAO,eAAe,EAAE,sCACrB,kCAAkC,CAAC;QACzC,KAAK,QAAQ,CACX,2CACA;QAGF,qIAAqI;QACrI,MAAM,yCACJ,kCACA,CAAC,OAAO,eAAe,EAAE,sCACrB,iCAAiC,CAAC;QACxC,KAAK,QAAQ,CACX,0CACA;IAEJ,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA,OAAO,eAAe,EAAE,sCACpB;QACJ,OAAO,eAAe,EAAE,sCACpB;QACJ,OAAO,eAAe,EAAE,sCACpB;QACJ,OAAO,eAAe,EAAE,sCACpB;KACL;IAED,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,MAAM,gBAAqC;YACzC,GAAG,MAAM;YACT,iBAAiB,OAAO,eAAe,IAAI;YAC3C,kBAAkB;gBAChB,wCAAwC;gBACxC,2CACE,OAAO,gBAAgB,EACnB,6CAA6C;gBACnD,iDACE,OAAO,gBAAgB,EACnB,mDAAmD;YAC3D;QACF;QAEA,OAAO;YAAE;YAAW;YAAU,MAAM;QAAc;IACpD,GACA;QAAC;QAAQ;QAAW;QAAU;KAAO;IAGvC,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,8OAAC,wIAAK;oBACJ,KAAI;oBACJ,KAAI;oBACJ,QAAQ;oBACR,OAAO;oBACP,WAAU;oBACV,QAAQ;;;;;;8BAEV,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,oKAAqC;oBAC9C,eAAe,CAAA,IAAK,EAAE,CAAC,6BAA6B,EAAE,GAAG;oBACzD,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,+JAAgC;oBACzC,eAAe,CAAA,IAAK,EAAE,CAAC,yBAAyB,EAAE,GAAG;oBACrD,GAAG;;;;;;8BAGL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,4IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,8OAAC,oOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 4501, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-form.tsx"], "sourcesContent": ["import {\r\n  Accordion as AccordionComponent,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from '@atlas/components/ui/accordion'\r\nimport type { ModuleWithParamsCrm } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useState } from 'react'\r\nimport { CrmPostInterventionDesignStrengthCalculationForm } from './crm-post-intervention-design-strength-calculation-form'\r\nimport { ReinforcedMasonryCharacteristicsParamsForm } from './reinforced-masonry-characteristics-params-form'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  module: ModuleWithParamsCrm\r\n}\r\n\r\nexport const CrmPostInterventionForm = ({\r\n  session,\r\n  projectId,\r\n  module,\r\n}: Props) => {\r\n  const [openItems, setOpenItems] = useState(['0'])\r\n  const t = useTranslations('forms.project-params.crm.crmPostIntervention')\r\n\r\n  const reinforcedMasonryCharacteristics =\r\n    module?.postIntervention?.reinforcedMasonryCharacteristicsParams\r\n  const handleItemSaved = useCallback((id: string) => {\r\n    const nextId = String(Number(id) + 1)\r\n    setOpenItems(old => {\r\n      const temp = old.filter(v => v !== id)\r\n      return old.includes(nextId) ? temp : [...temp, nextId]\r\n    })\r\n  }, [])\r\n\r\n  return (\r\n    <div>\r\n      <AccordionComponent\r\n        type=\"multiple\"\r\n        value={openItems}\r\n        onValueChange={setOpenItems}\r\n      >\r\n        <AccordionItem value=\"0\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('reinforcedMasonryCharacteristics.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <ReinforcedMasonryCharacteristicsParamsForm\r\n              session={session}\r\n              projectId={projectId}\r\n              module={module}\r\n              onSave={() => handleItemSaved('0')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"1\" disabled={!reinforcedMasonryCharacteristics}>\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('designStrengthPostInterventionCalculationResult.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <CrmPostInterventionDesignStrengthCalculationForm\r\n              session={session}\r\n              projectId={projectId}\r\n              module={module}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n      </AccordionComponent>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AASA;AACA;AACA;AACA;;;;;;;AAQO,MAAM,0BAA0B,CAAC,EACtC,OAAO,EACP,SAAS,EACT,MAAM,EACA;IACN,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;QAAC;KAAI;IAChD,MAAM,IAAI,IAAA,yNAAe,EAAC;IAE1B,MAAM,mCACJ,QAAQ,kBAAkB;IAC5B,MAAM,kBAAkB,IAAA,oNAAW,EAAC,CAAC;QACnC,MAAM,SAAS,OAAO,OAAO,MAAM;QACnC,aAAa,CAAA;YACX,MAAM,OAAO,IAAI,MAAM,CAAC,CAAA,IAAK,MAAM;YACnC,OAAO,IAAI,QAAQ,CAAC,UAAU,OAAO;mBAAI;gBAAM;aAAO;QACxD;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;kBACC,cAAA,8OAAC,kJAAkB;YACjB,MAAK;YACL,OAAO;YACP,eAAe;;8BAEf,8OAAC,sJAAa;oBAAC,OAAM;;sCACnB,8OAAC,yJAAgB;sCACf,cAAA,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;;;;;;sCAGP,8OAAC,yJAAgB;sCACf,cAAA,8OAAC,8UAA0C;gCACzC,SAAS;gCACT,WAAW;gCACX,QAAQ;gCACR,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,8OAAC,sJAAa;oBAAC,OAAM;oBAAI,UAAU,CAAC;;sCAClC,8OAAC,yJAAgB;sCACf,cAAA,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;;;;;;sCAGP,8OAAC,yJAAgB;sCACf,cAAA,8OAAC,kWAAgD;gCAC/C,SAAS;gCACT,WAAW;gCACX,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB", "debugId": null}}, {"offset": {"line": 4633, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx"], "sourcesContent": ["import {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { DesignStrengthPreInterventionCalculationResultSchemaInputs } from '@atlas/types/schemas/masonry/crm-form'\r\nimport { Separator } from '@radix-ui/react-separator'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  designStrengthPreInterventionCalculationResult: DesignStrengthPreInterventionCalculationResultSchemaInputs\r\n}\r\n\r\nexport function DesignStrengthPreInterventionCalculationResultCard({\r\n  designStrengthPreInterventionCalculationResult,\r\n}: Props) {\r\n  const t = useTranslations(\r\n    'forms.project-params.crm.crmPreIntervention.designStrengthPreInterventionCalculationResult',\r\n  )\r\n  const _tAction = useTranslations('actions.save-project-params.messages')\r\n  const _tCommon = useTranslations('actions.common')\r\n\r\n  const _locale = useLocale()\r\n\r\n  const excludeKeys = [\r\n    'executionClass',\r\n    'structuralElementsCategory',\r\n    'inPlaneBendingStrengthCalculationResult',\r\n    'outOfPlaneBendingStrengthCalculationResult',\r\n    'inPlaneShearStrengthCalculationResult',\r\n  ]\r\n  // form is not available in this component, so we cannot use NumberFormInput directly here\r\n  // Instead, render the values as disabled inputs for display only\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        {/* Primitive values */}\r\n        {Object.entries(designStrengthPreInterventionCalculationResult)\r\n          .filter(\r\n            ([key, value]) =>\r\n              !excludeKeys.includes(key) &&\r\n              (typeof value === 'string' ||\r\n                typeof value === 'number' ||\r\n                typeof value === 'boolean'),\r\n          )\r\n          .map(([key, value]) => (\r\n            <div key={key}>\r\n              <span className=\"font-medium\">{t(`${key}.label`)}:</span>{' '}\r\n              <span>{String(value)}</span>\r\n              <p\r\n                data-slot=\"form-description\"\r\n                id={key}\r\n                className={cn('text-muted-foreground text-sm')}\r\n              >\r\n                {t(`${key}.description`)}\r\n              </p>\r\n            </div>\r\n          ))}\r\n\r\n        {/* inPlaneBendingStrengthCalculationResult */}\r\n        <Separator />\r\n        <h1 className=\"text-xl font-bold\">\r\n          {t('inPlaneBendingStrengthCalculationResult.title')}\r\n        </h1>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'inPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {designStrengthPreInterventionCalculationResult\r\n              .inPlaneBendingStrengthCalculationResult\r\n              ?.compressedFlangeNeutralAxisDistance ?? ''}\r\n          </span>\r\n          <p className={cn('text-muted-foreground text-sm')}>\r\n            {t(\r\n              'inPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance.description',\r\n            )}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'inPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {designStrengthPreInterventionCalculationResult\r\n              .inPlaneBendingStrengthCalculationResult\r\n              ?.inOrOutplaneBendingMoment ?? ''}\r\n          </span>\r\n          <p className={cn('text-muted-foreground text-sm')}>\r\n            {t(\r\n              'inPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment.description',\r\n            )}\r\n          </p>\r\n        </div>\r\n\r\n        {/* outOfPlaneBendingStrengthCalculationResult */}\r\n        <Separator />\r\n        <h1 className=\"text-xl font-bold\">\r\n          {t('outOfPlaneBendingStrengthCalculationResult.title')}\r\n        </h1>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'outOfPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {designStrengthPreInterventionCalculationResult\r\n              .outOfPlaneBendingStrengthCalculationResult\r\n              ?.compressedFlangeNeutralAxisDistance ?? ''}\r\n          </span>\r\n          <p className={cn('text-muted-foreground text-sm')}>\r\n            {t(\r\n              'outOfPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance.description',\r\n            )}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'outOfPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {designStrengthPreInterventionCalculationResult\r\n              .outOfPlaneBendingStrengthCalculationResult\r\n              ?.inOrOutplaneBendingMoment ?? ''}\r\n          </span>\r\n          <p className={cn('text-muted-foreground text-sm')}>\r\n            {t(\r\n              'outOfPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment.description',\r\n            )}\r\n          </p>\r\n        </div>\r\n\r\n        {/* inPlaneShearStrengthCalculationResult */}\r\n        <Separator />\r\n        <h1 className=\"text-xl font-bold\">\r\n          {t('inPlaneShearStrengthCalculationResult.title')}\r\n        </h1>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('inPlaneShearStrengthCalculationResult.verticalStress.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {designStrengthPreInterventionCalculationResult\r\n              .inPlaneShearStrengthCalculationResult?.verticalStress ?? ''}\r\n          </span>\r\n          <p className={cn('text-muted-foreground text-sm')}>\r\n            {t(\r\n              'inPlaneShearStrengthCalculationResult.verticalStress.description',\r\n            )}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'inPlaneShearStrengthCalculationResult.wallSlendernessCorrectionCoefficient.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {designStrengthPreInterventionCalculationResult\r\n              .inPlaneShearStrengthCalculationResult\r\n              ?.wallSlendernessCorrectionCoefficient ?? ''}\r\n          </span>\r\n          <p className={cn('text-muted-foreground text-sm')}>\r\n            {t(\r\n              'inPlaneShearStrengthCalculationResult.wallSlendernessCorrectionCoefficient.description',\r\n            )}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('inPlaneShearStrengthCalculationResult.shearStrength.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {designStrengthPreInterventionCalculationResult\r\n              .inPlaneShearStrengthCalculationResult?.shearStrength ?? ''}\r\n          </span>\r\n          <p className={cn('text-muted-foreground text-sm')}>\r\n            {t(\r\n              'inPlaneShearStrengthCalculationResult.shearStrength.description',\r\n            )}\r\n          </p>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAMA;AAEA;AACA;AAAA;;;;;;AAMO,SAAS,mDAAmD,EACjE,8CAA8C,EACxC;IACN,MAAM,IAAI,IAAA,yNAAe,EACvB;IAEF,MAAM,WAAW,IAAA,yNAAe,EAAC;IACjC,MAAM,WAAW,IAAA,yNAAe,EAAC;IAEjC,MAAM,UAAU,IAAA,+KAAS;IAEzB,MAAM,cAAc;QAClB;QACA;QACA;QACA;QACA;KACD;IACD,0FAA0F;IAC1F,iEAAiE;IACjE,qBACE,8OAAC,wIAAI;;0BACH,8OAAC,8IAAU;0BACT,cAAA,8OAAC,6IAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,8OAAC,+IAAW;gBAAC,WAAU;;oBAEpB,OAAO,OAAO,CAAC,gDACb,MAAM,CACL,CAAC,CAAC,KAAK,MAAM,GACX,CAAC,YAAY,QAAQ,CAAC,QACtB,CAAC,OAAO,UAAU,YAChB,OAAO,UAAU,YACjB,OAAO,UAAU,SAAS,GAE/B,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAChB,8OAAC;;8CACC,8OAAC;oCAAK,WAAU;;wCAAe,EAAE,GAAG,IAAI,MAAM,CAAC;wCAAE;;;;;;;gCAAS;8CAC1D,8OAAC;8CAAM,OAAO;;;;;;8CACd,8OAAC;oCACC,aAAU;oCACV,IAAI;oCACJ,WAAW,IAAA,yHAAE,EAAC;8CAEb,EAAE,GAAG,IAAI,YAAY,CAAC;;;;;;;2BARjB;;;;;kCAcd,8OAAC,kLAAS;;;;;kCACV,8OAAC;wBAAG,WAAU;kCACX,EAAE;;;;;;kCAEL,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EACC;oCACA;;;;;;;4BAEI;0CACR,8OAAC;0CACE,+CACE,uCAAuC,EACtC,uCAAuC;;;;;;0CAE7C,8OAAC;gCAAE,WAAW,IAAA,yHAAE,EAAC;0CACd,EACC;;;;;;;;;;;;kCAIN,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EACC;oCACA;;;;;;;4BAEI;0CACR,8OAAC;0CACE,+CACE,uCAAuC,EACtC,6BAA6B;;;;;;0CAEnC,8OAAC;gCAAE,WAAW,IAAA,yHAAE,EAAC;0CACd,EACC;;;;;;;;;;;;kCAMN,8OAAC,kLAAS;;;;;kCACV,8OAAC;wBAAG,WAAU;kCACX,EAAE;;;;;;kCAEL,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EACC;oCACA;;;;;;;4BAEI;0CACR,8OAAC;0CACE,+CACE,0CAA0C,EACzC,uCAAuC;;;;;;0CAE7C,8OAAC;gCAAE,WAAW,IAAA,yHAAE,EAAC;0CACd,EACC;;;;;;;;;;;;kCAIN,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EACC;oCACA;;;;;;;4BAEI;0CACR,8OAAC;0CACE,+CACE,0CAA0C,EACzC,6BAA6B;;;;;;0CAEnC,8OAAC;gCAAE,WAAW,IAAA,yHAAE,EAAC;0CACd,EACC;;;;;;;;;;;;kCAMN,8OAAC,kLAAS;;;;;kCACV,8OAAC;wBAAG,WAAU;kCACX,EAAE;;;;;;kCAEL,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA8D;;;;;;;4BAC3D;0CACR,8OAAC;0CACE,+CACE,qCAAqC,EAAE,kBAAkB;;;;;;0CAE9D,8OAAC;gCAAE,WAAW,IAAA,yHAAE,EAAC;0CACd,EACC;;;;;;;;;;;;kCAIN,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EACC;oCACA;;;;;;;4BAEI;0CACR,8OAAC;0CACE,+CACE,qCAAqC,EACpC,wCAAwC;;;;;;0CAE9C,8OAAC;gCAAE,WAAW,IAAA,yHAAE,EAAC;0CACd,EACC;;;;;;;;;;;;kCAIN,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA6D;;;;;;;4BAC1D;0CACR,8OAAC;0CACE,+CACE,qCAAqC,EAAE,iBAAiB;;;;;;0CAE7D,8OAAC;gCAAE,WAAW,IAAA,yHAAE,EAAC;0CACd,EACC;;;;;;;;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 5018, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-form.tsx"], "sourcesContent": ["import { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport {\r\n  executionClass,\r\n  loadResistingCategory,\r\n  MODULE_EXECUTION_CLASS,\r\n  MODULE_LOAD_RESISTING_CATEGORY,\r\n  masonryStrengthSafetyFactorMapping,\r\n} from '@atlas/constants/module'\r\nimport type { ModuleWithParamsCrm } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type CrmFormSchemaInputs,\r\n  type DesignStrengthPreInterventionCalculationResultSchemaInputs,\r\n  designStrengthPreInterventionCalculationResultSchema,\r\n} from '@atlas/types/schemas/masonry/crm-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect, useState } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\nimport { DesignStrengthPreInterventionCalculationResultCard } from './design-strength-pre-intervention-calculation-result-card'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  module: ModuleWithParamsCrm\r\n  onSave: () => void\r\n}\r\n\r\nexport const DesignStrengthPreInterventionCalculationResultForm = ({\r\n  session,\r\n  projectId,\r\n  module,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.project-params.crm.crmPreIntervention.designStrengthPreInterventionCalculationResult',\r\n  )\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const [showCalculationResult, setShowCalculationResult] = useState(false)\r\n\r\n  const moduleId = module.id\r\n  const designStrengthPreInterventionCalculationResult =\r\n    module?.preIntervention?.designStrengthPreInterventionCalculationResult\r\n  const existingMasonryCharacteristicsParams =\r\n    module.preIntervention?.existingMasonryCharacteristicsParams\r\n\r\n  const form =\r\n    useForm<DesignStrengthPreInterventionCalculationResultSchemaInputs>({\r\n      resolver: zodResolver(\r\n        designStrengthPreInterventionCalculationResultSchema,\r\n      ),\r\n      defaultValues: {\r\n        executionClass:\r\n          designStrengthPreInterventionCalculationResult?.executionClass ??\r\n          executionClass.ONE,\r\n        structuralElementsCategory:\r\n          designStrengthPreInterventionCalculationResult?.structuralElementsCategory ??\r\n          loadResistingCategory.MASONRY_CAT_II_AND_ANY_TYPE_OF_MORTAR,\r\n        masonryStrengthSafetyFactor:\r\n          designStrengthPreInterventionCalculationResult?.masonryStrengthSafetyFactor ??\r\n          0,\r\n        designCompressiveStrength:\r\n          designStrengthPreInterventionCalculationResult?.designCompressiveStrength ??\r\n          0,\r\n        designShearStrengthRegularMasonry:\r\n          designStrengthPreInterventionCalculationResult?.designShearStrengthRegularMasonry ??\r\n          0,\r\n        designShearStrengthIrregularMasonry:\r\n          designStrengthPreInterventionCalculationResult?.designShearStrengthIrregularMasonry ??\r\n          0,\r\n        designNormalElasticityModulus:\r\n          designStrengthPreInterventionCalculationResult?.designNormalElasticityModulus ??\r\n          0,\r\n        designShearElasticityModulus:\r\n          designStrengthPreInterventionCalculationResult?.designShearElasticityModulus ??\r\n          0,\r\n        ultimateCompressiveStrainLinearBehavior:\r\n          designStrengthPreInterventionCalculationResult?.ultimateCompressiveStrainLinearBehavior ??\r\n          0,\r\n        ultimateCompressiveStrain:\r\n          designStrengthPreInterventionCalculationResult?.ultimateCompressiveStrain ??\r\n          0.0035,\r\n        firstCoefficient:\r\n          designStrengthPreInterventionCalculationResult?.firstCoefficient ?? 0,\r\n        panelSelfWeight:\r\n          designStrengthPreInterventionCalculationResult?.panelSelfWeight ?? 0,\r\n        inPlaneBendingStrengthCalculationResult:\r\n          designStrengthPreInterventionCalculationResult?.inPlaneBendingStrengthCalculationResult ?? {\r\n            compressedFlangeNeutralAxisDistance: 0,\r\n            inOrOutplaneBendingMoment: 0,\r\n          },\r\n        outOfPlaneBendingStrengthCalculationResult:\r\n          designStrengthPreInterventionCalculationResult?.outOfPlaneBendingStrengthCalculationResult ?? {\r\n            compressedFlangeNeutralAxisDistance: 0,\r\n            inOrOutplaneBendingMoment: 0,\r\n          },\r\n        inPlaneShearStrengthCalculationResult:\r\n          designStrengthPreInterventionCalculationResult?.inPlaneShearStrengthCalculationResult ?? {\r\n            shearStrength: 0,\r\n            verticalStress: 0,\r\n            wallSlendernessCorrectionCoefficient: 0,\r\n          },\r\n      },\r\n    })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const structuralElementsCategoryFormValue = form.watch(\r\n    'structuralElementsCategory',\r\n  )\r\n  const executionClassFormValue = form.watch('executionClass')\r\n  const ultimateCompressiveStrainFormValue =\r\n    form.watch('ultimateCompressiveStrain') ?? 0.0035\r\n  useEffect(() => {\r\n    const masonryStrengthSafetyFactor =\r\n      masonryStrengthSafetyFactorMapping[structuralElementsCategoryFormValue][\r\n        executionClassFormValue\r\n      ]\r\n    form.setValue('masonryStrengthSafetyFactor', masonryStrengthSafetyFactor)\r\n\r\n    const divider =\r\n      masonryStrengthSafetyFactor *\r\n        (existingMasonryCharacteristicsParams?.confidenceFactor ?? 0) ===\r\n      0\r\n        ? 1\r\n        : masonryStrengthSafetyFactor *\r\n          (existingMasonryCharacteristicsParams?.confidenceFactor ?? 0)\r\n    // designCompressiveStrength: amplifiedAverageCompressiveStrength / confidenceFactor / masonryStrengthSafetyFactor\r\n    const designCompressiveStrength =\r\n      (existingMasonryCharacteristicsParams?.amplifiedAverageCompressiveStrength ??\r\n        0) / divider\r\n    form.setValue('designCompressiveStrength', designCompressiveStrength)\r\n\r\n    const designShearStrengthRegularMasonry =\r\n      (existingMasonryCharacteristicsParams?.amplifiedAverageShearStrengthRegularMasonry ??\r\n        0) / divider\r\n    form.setValue(\r\n      'designShearStrengthRegularMasonry',\r\n      designShearStrengthRegularMasonry,\r\n    )\r\n\r\n    const designShearStrengthIrregularMasonry =\r\n      (existingMasonryCharacteristicsParams?.amplifiedAverageShearStrengthIrregularMasonry ??\r\n        0) / divider\r\n    form.setValue(\r\n      'designShearStrengthIrregularMasonry',\r\n      designShearStrengthIrregularMasonry,\r\n    )\r\n\r\n    const designNormalElasticityModulus =\r\n      (existingMasonryCharacteristicsParams?.amplifiedAverageNormalElasticityModulus ??\r\n        0) / masonryStrengthSafetyFactor\r\n    form.setValue(\r\n      'designNormalElasticityModulus',\r\n      designNormalElasticityModulus,\r\n    )\r\n\r\n    const designShearElasticityModulus =\r\n      (existingMasonryCharacteristicsParams?.amplifiedAverageShearElasticityModulus ??\r\n        0) / masonryStrengthSafetyFactor\r\n    form.setValue('designShearElasticityModulus', designShearElasticityModulus)\r\n\r\n    const ultimateCompressiveStrainLinearBehavior =\r\n      designCompressiveStrength / designNormalElasticityModulus\r\n    form.setValue(\r\n      'ultimateCompressiveStrainLinearBehavior',\r\n      ultimateCompressiveStrainLinearBehavior,\r\n    )\r\n\r\n    const firstCoefficient =\r\n      ultimateCompressiveStrainLinearBehavior /\r\n      ultimateCompressiveStrainFormValue\r\n    form.setValue('firstCoefficient', firstCoefficient)\r\n\r\n    const panelThickness =\r\n      existingMasonryCharacteristicsParams?.panelThickness ?? 0\r\n    const panelWidth = existingMasonryCharacteristicsParams?.panelWidth ?? 0\r\n\r\n    const panelHeight = existingMasonryCharacteristicsParams?.panelHeight ?? 0\r\n    const specificWeight =\r\n      existingMasonryCharacteristicsParams?.specificWeight ?? 0\r\n    const panelSelfWeight =\r\n      panelWidth * panelHeight * panelThickness * specificWeight * 1.3\r\n\r\n    form.setValue('panelSelfWeight', panelSelfWeight)\r\n\r\n    // inPlaneBendingStrengthCalculationResult, outOfPlaneBendingStrengthCalculationResult, inPlaneShearStrengthCalculationResult are calculated in another form\r\n    // inOrOutplanBendingMoment: =2*(D39*1000)/(D11*1000*D31)*D37/(2*D37-D36)\r\n\r\n    const inPlaneCompressedFlangeNeutralAxisDistance =\r\n      (((2 * (panelSelfWeight * 1000)) /\r\n        (panelThickness * 1000 * designCompressiveStrength)) *\r\n        ultimateCompressiveStrainFormValue) /\r\n      (2 * ultimateCompressiveStrainFormValue -\r\n        ultimateCompressiveStrainLinearBehavior)\r\n\r\n    // inPlaneBendingMoment\r\n    // =(D31*(D11*1000)*D41/2*(D10*1000)*(1-D38)-D41*((1-D38)^2+D38*((D10*1000)/2-D41+2/3*D38*D41)))/1000/1000\r\n\r\n    const firstValue =\r\n      ((designCompressiveStrength *\r\n        (panelThickness * 1000) *\r\n        inPlaneCompressedFlangeNeutralAxisDistance) /\r\n        2) *\r\n      (panelWidth * 1000) *\r\n      (1 - firstCoefficient)\r\n\r\n    const secondValue =\r\n      inPlaneCompressedFlangeNeutralAxisDistance *\r\n      ((1 - firstCoefficient) ** 2 +\r\n        firstCoefficient *\r\n          ((panelWidth * 1000) / 2 -\r\n            inPlaneCompressedFlangeNeutralAxisDistance +\r\n            (2 / 3) *\r\n              firstCoefficient *\r\n              inPlaneCompressedFlangeNeutralAxisDistance))\r\n\r\n    const inPlaneBendingMoment = (firstValue - secondValue) / 1000 / 1000\r\n\r\n    const inPlaneBendingStrengthCalculationResult = {\r\n      compressedFlangeNeutralAxisDistance: Number(\r\n        inPlaneCompressedFlangeNeutralAxisDistance.toFixed(2),\r\n      ),\r\n      inOrOutplaneBendingMoment: Number(inPlaneBendingMoment.toFixed(2)),\r\n    }\r\n    form.setValue(\r\n      'inPlaneBendingStrengthCalculationResult',\r\n      inPlaneBendingStrengthCalculationResult,\r\n    )\r\n\r\n    // outOfPlaneCompressedFlangeNeutralAxisDistance\r\n    // =D39/(0.85*D31*0.7)\r\n    const outOfPlaneCompressedFlangeNeutralAxisDistance =\r\n      panelSelfWeight / (0.85 * designCompressiveStrength * 0.7)\r\n\r\n    // outOfPlaneBendingMoment\r\n    // =D39*(D11*1000/1000/2-0.7*D44/1000/2)\r\n    const outOfPlaneBendingMoment =\r\n      panelSelfWeight *\r\n      ((panelThickness * 1000) / 1000 / 2 -\r\n        (0.7 * outOfPlaneCompressedFlangeNeutralAxisDistance) / 1000 / 2)\r\n\r\n    const outOfPlaneBendingStrengthCalculationResult = {\r\n      compressedFlangeNeutralAxisDistance: Number(\r\n        outOfPlaneCompressedFlangeNeutralAxisDistance.toFixed(2),\r\n      ),\r\n      inOrOutplaneBendingMoment: Number(outOfPlaneBendingMoment.toFixed(2)),\r\n    }\r\n    form.setValue(\r\n      'outOfPlaneBendingStrengthCalculationResult',\r\n      outOfPlaneBendingStrengthCalculationResult,\r\n    )\r\n\r\n    // inPlaneShearStrengthCalculationResult\r\n    // verticalStress = =D39*1000/(D11*1000*D10*1000)\r\n    const verticalStress =\r\n      (panelSelfWeight * 1000) / (panelThickness * 1000 * panelWidth * 1000)\r\n\r\n    // wallSlendernessCorrectionCoefficient =IF(D12/D10<=1,1,IF(D12/D10<=1.5,D12/D10,1.5))\r\n    const wallSlendernessRatio = panelHeight / panelWidth\r\n    const wallSlendernessCorrectionCoefficient =\r\n      wallSlendernessRatio <= 1\r\n        ? 1\r\n        : wallSlendernessRatio <= 1.5\r\n          ? wallSlendernessRatio\r\n          : 1.5\r\n\r\n    // shearStrength =(D10*1000*D11*1000*(1.5*D32/D48)*SQRT(1+D47/(1.5*D32)))/1000\r\n    const shearStrength =\r\n      (panelWidth *\r\n        1000 *\r\n        panelThickness *\r\n        1000 *\r\n        ((1.5 * designShearStrengthRegularMasonry) /\r\n          wallSlendernessCorrectionCoefficient) *\r\n        Math.sqrt(\r\n          1 + verticalStress / (1.5 * designShearStrengthRegularMasonry),\r\n        )) /\r\n      1000\r\n\r\n    const inPlaneShearStrengthCalculationResult = {\r\n      verticalStress: Number(verticalStress.toFixed(2)),\r\n      wallSlendernessCorrectionCoefficient: Number(\r\n        wallSlendernessCorrectionCoefficient.toFixed(2),\r\n      ),\r\n      shearStrength: Number(shearStrength.toFixed(2)),\r\n    }\r\n    form.setValue(\r\n      'inPlaneShearStrengthCalculationResult',\r\n      inPlaneShearStrengthCalculationResult,\r\n    )\r\n  }, [\r\n    structuralElementsCategoryFormValue,\r\n    executionClassFormValue,\r\n    existingMasonryCharacteristicsParams,\r\n    ultimateCompressiveStrainFormValue,\r\n    form,\r\n  ])\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: DesignStrengthPreInterventionCalculationResultSchemaInputs) => {\r\n      const updatedModule: CrmFormSchemaInputs = {\r\n        ...module,\r\n        preIntervention: {\r\n          existingMasonryCharacteristicsParams:\r\n            module.preIntervention?.existingMasonryCharacteristicsParams ??\r\n            undefined,\r\n          designStrengthPreInterventionCalculationResult: body ?? undefined,\r\n        },\r\n        postIntervention: module?.postIntervention ?? undefined,\r\n      }\r\n\r\n      mutate({ projectId, moduleId, body: updatedModule })\r\n    },\r\n    [mutate, projectId, moduleId, module],\r\n  )\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"executionClass\"\r\n          options={MODULE_EXECUTION_CLASS}\r\n          optionLabelFn={p => t(`executionClass.${p}`)}\r\n          t={t}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"structuralElementsCategory\"\r\n          options={MODULE_LOAD_RESISTING_CATEGORY}\r\n          optionLabelFn={p => t(`structuralElementsCategory.${p}`)}\r\n          t={t}\r\n        />\r\n        <Button\r\n          type=\"button\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={() => setShowCalculationResult(true)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('calculate')}\r\n        </Button>\r\n        {showCalculationResult && (\r\n          <DesignStrengthPreInterventionCalculationResultCard\r\n            designStrengthPreInterventionCalculationResult={form.getValues()}\r\n          />\r\n        )}\r\n        <Button\r\n          type=\"submit\"\r\n          className={`w-full sm:w-auto ${!showCalculationResult ? 'hidden' : ''}`}\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('next')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AASA;AACA;AAKA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AASO,MAAM,qDAAqD,CAAC,EACjE,OAAO,EACP,SAAS,EACT,MAAM,EACN,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EACvB;IAEF,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,IAAA,iNAAQ,EAAC;IAEnE,MAAM,WAAW,OAAO,EAAE;IAC1B,MAAM,iDACJ,QAAQ,iBAAiB;IAC3B,MAAM,uCACJ,OAAO,eAAe,EAAE;IAE1B,MAAM,OACJ,IAAA,yKAAO,EAA6D;QAClE,UAAU,IAAA,6KAAW,EACnB,yMAAoD;QAEtD,eAAe;YACb,gBACE,gDAAgD,kBAChD,6IAAc,CAAC,GAAG;YACpB,4BACE,gDAAgD,8BAChD,oJAAqB,CAAC,qCAAqC;YAC7D,6BACE,gDAAgD,+BAChD;YACF,2BACE,gDAAgD,6BAChD;YACF,mCACE,gDAAgD,qCAChD;YACF,qCACE,gDAAgD,uCAChD;YACF,+BACE,gDAAgD,iCAChD;YACF,8BACE,gDAAgD,gCAChD;YACF,yCACE,gDAAgD,2CAChD;YACF,2BACE,gDAAgD,6BAChD;YACF,kBACE,gDAAgD,oBAAoB;YACtE,iBACE,gDAAgD,mBAAmB;YACrE,yCACE,gDAAgD,2CAA2C;gBACzF,qCAAqC;gBACrC,2BAA2B;YAC7B;YACF,4CACE,gDAAgD,8CAA8C;gBAC5F,qCAAqC;gBACrC,2BAA2B;YAC7B;YACF,uCACE,gDAAgD,yCAAyC;gBACvF,eAAe;gBACf,gBAAgB;gBAChB,sCAAsC;YACxC;QACJ;IACF;IAEF,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAW;wBAAE;wBAAW;oBAAS;iBAAE;YAChD;YACA;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,sCAAsC,KAAK,KAAK,CACpD;IAEF,MAAM,0BAA0B,KAAK,KAAK,CAAC;IAC3C,MAAM,qCACJ,KAAK,KAAK,CAAC,gCAAgC;IAC7C,IAAA,kNAAS,EAAC;QACR,MAAM,8BACJ,iKAAkC,CAAC,oCAAoC,CACrE,wBACD;QACH,KAAK,QAAQ,CAAC,+BAA+B;QAE7C,MAAM,UACJ,8BACE,CAAC,sCAAsC,oBAAoB,CAAC,MAC9D,IACI,IACA,8BACA,CAAC,sCAAsC,oBAAoB,CAAC;QAClE,kHAAkH;QAClH,MAAM,4BACJ,CAAC,sCAAsC,uCACrC,CAAC,IAAI;QACT,KAAK,QAAQ,CAAC,6BAA6B;QAE3C,MAAM,oCACJ,CAAC,sCAAsC,+CACrC,CAAC,IAAI;QACT,KAAK,QAAQ,CACX,qCACA;QAGF,MAAM,sCACJ,CAAC,sCAAsC,iDACrC,CAAC,IAAI;QACT,KAAK,QAAQ,CACX,uCACA;QAGF,MAAM,gCACJ,CAAC,sCAAsC,2CACrC,CAAC,IAAI;QACT,KAAK,QAAQ,CACX,iCACA;QAGF,MAAM,+BACJ,CAAC,sCAAsC,0CACrC,CAAC,IAAI;QACT,KAAK,QAAQ,CAAC,gCAAgC;QAE9C,MAAM,0CACJ,4BAA4B;QAC9B,KAAK,QAAQ,CACX,2CACA;QAGF,MAAM,mBACJ,0CACA;QACF,KAAK,QAAQ,CAAC,oBAAoB;QAElC,MAAM,iBACJ,sCAAsC,kBAAkB;QAC1D,MAAM,aAAa,sCAAsC,cAAc;QAEvE,MAAM,cAAc,sCAAsC,eAAe;QACzE,MAAM,iBACJ,sCAAsC,kBAAkB;QAC1D,MAAM,kBACJ,aAAa,cAAc,iBAAiB,iBAAiB;QAE/D,KAAK,QAAQ,CAAC,mBAAmB;QAEjC,4JAA4J;QAC5J,yEAAyE;QAEzE,MAAM,6CACJ,AAAG,IAAI,CAAC,kBAAkB,IAAI,IAC5B,CAAC,iBAAiB,OAAO,yBAAyB,IAClD,qCACF,CAAC,IAAI,qCACH,uCAAuC;QAE3C,uBAAuB;QACvB,0GAA0G;QAE1G,MAAM,aACJ,AAAE,4BACA,CAAC,iBAAiB,IAAI,IACtB,6CACA,IACF,CAAC,aAAa,IAAI,IAClB,CAAC,IAAI,gBAAgB;QAEvB,MAAM,cACJ,6CACA,CAAC,CAAC,IAAI,gBAAgB,KAAK,IACzB,mBACE,CAAC,AAAC,aAAa,OAAQ,IACrB,6CACA,AAAC,IAAI,IACH,mBACA,0CAA0C,CAAC;QAErD,MAAM,uBAAuB,CAAC,aAAa,WAAW,IAAI,OAAO;QAEjE,MAAM,0CAA0C;YAC9C,qCAAqC,OACnC,2CAA2C,OAAO,CAAC;YAErD,2BAA2B,OAAO,qBAAqB,OAAO,CAAC;QACjE;QACA,KAAK,QAAQ,CACX,2CACA;QAGF,gDAAgD;QAChD,sBAAsB;QACtB,MAAM,gDACJ,kBAAkB,CAAC,OAAO,4BAA4B,GAAG;QAE3D,0BAA0B;QAC1B,wCAAwC;QACxC,MAAM,0BACJ,kBACA,CAAC,AAAC,iBAAiB,OAAQ,OAAO,IAChC,AAAC,MAAM,gDAAiD,OAAO,CAAC;QAEpE,MAAM,6CAA6C;YACjD,qCAAqC,OACnC,8CAA8C,OAAO,CAAC;YAExD,2BAA2B,OAAO,wBAAwB,OAAO,CAAC;QACpE;QACA,KAAK,QAAQ,CACX,8CACA;QAGF,wCAAwC;QACxC,iDAAiD;QACjD,MAAM,iBACJ,AAAC,kBAAkB,OAAQ,CAAC,iBAAiB,OAAO,aAAa,IAAI;QAEvE,sFAAsF;QACtF,MAAM,uBAAuB,cAAc;QAC3C,MAAM,uCACJ,wBAAwB,IACpB,IACA,wBAAwB,MACtB,uBACA;QAER,8EAA8E;QAC9E,MAAM,gBACJ,AAAC,aACC,OACA,iBACA,OACA,CAAC,AAAC,MAAM,oCACN,oCAAoC,IACtC,KAAK,IAAI,CACP,IAAI,iBAAiB,CAAC,MAAM,iCAAiC,KAEjE;QAEF,MAAM,wCAAwC;YAC5C,gBAAgB,OAAO,eAAe,OAAO,CAAC;YAC9C,sCAAsC,OACpC,qCAAqC,OAAO,CAAC;YAE/C,eAAe,OAAO,cAAc,OAAO,CAAC;QAC9C;QACA,KAAK,QAAQ,CACX,yCACA;IAEJ,GAAG;QACD;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,MAAM,gBAAqC;YACzC,GAAG,MAAM;YACT,iBAAiB;gBACf,sCACE,OAAO,eAAe,EAAE,wCACxB;gBACF,gDAAgD,QAAQ;YAC1D;YACA,kBAAkB,QAAQ,oBAAoB;QAChD;QAEA,OAAO;YAAE;YAAW;YAAU,MAAM;QAAc;IACpD,GACA;QAAC;QAAQ;QAAW;QAAU;KAAO;IAGvC,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,qJAAsB;oBAC/B,eAAe,CAAA,IAAK,EAAE,CAAC,eAAe,EAAE,GAAG;oBAC3C,GAAG;;;;;;8BAEL,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,6JAA8B;oBACvC,eAAe,CAAA,IAAK,EAAE,CAAC,2BAA2B,EAAE,GAAG;oBACvD,GAAG;;;;;;8BAEL,8OAAC,4IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,IAAM,yBAAyB;;wBAEvC,2BAAa,8OAAC,oOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;gBAEV,uCACC,8OAAC,qWAAkD;oBACjD,gDAAgD,KAAK,SAAS;;;;;;8BAGlE,8OAAC,4IAAM;oBACL,MAAK;oBACL,WAAW,CAAC,iBAAiB,EAAE,CAAC,wBAAwB,WAAW,IAAI;oBACvE,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,8OAAC,oOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 5295, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport {\r\n  averageCompressiveStrengthValues,\r\n  averageNormalElasticityModulusValues,\r\n  averageShearElasticityModulusValues,\r\n  averageShearStrengthIrregularMasonryValues,\r\n  averageShearStrengthRegularMasonryValues,\r\n  CRM_MASONRY_TYPE,\r\n  correctiveCoefficientValues,\r\n  ENHANCEMENT_CHARACTERISTICS,\r\n  MODULE_CRM_MASONRY_TYPE,\r\n  MODULE_ENHANCEMENT_CHARACTERISTICS,\r\n  MODULE_MATERIAL_KNOWLEDGE_LEVEL,\r\n  moduleMaterialKnowledgeLevel,\r\n  moduleMaterialKnowledgeLevelValues,\r\n  specificWeightValues,\r\n} from '@atlas/constants/module'\r\nimport type { ModuleWithParamsCrm } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type CrmFormSchemaInputs,\r\n  type ExistingMasonryCharacteristicsParamsSchemaInputs,\r\n  existingMasonryCharacteristicsParamsSchema,\r\n} from '@atlas/types/schemas/masonry/crm-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  module: ModuleWithParamsCrm\r\n  onSave: () => void\r\n}\r\n\r\nexport const ExistingMasonryCharacteristicsParamsForm = ({\r\n  session,\r\n  projectId,\r\n  module,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.project-params.crm.crmPreIntervention.existingMasonryCharacteristicsParams',\r\n  )\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const moduleId = module.id\r\n  const existingMasonryCharacteristicsParams =\r\n    module?.preIntervention?.existingMasonryCharacteristicsParams\r\n  const form = useForm<ExistingMasonryCharacteristicsParamsSchemaInputs>({\r\n    resolver: zodResolver(existingMasonryCharacteristicsParamsSchema),\r\n    defaultValues: {\r\n      panelWidth: existingMasonryCharacteristicsParams?.panelWidth ?? 0,\r\n      panelThickness: existingMasonryCharacteristicsParams?.panelThickness ?? 0,\r\n      panelHeight: existingMasonryCharacteristicsParams?.panelHeight ?? 0,\r\n      masonryType:\r\n        existingMasonryCharacteristicsParams?.masonryType ??\r\n        CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA,\r\n      knowledgeLevel:\r\n        existingMasonryCharacteristicsParams?.knowledgeLevel ??\r\n        moduleMaterialKnowledgeLevel.LC1,\r\n      confidenceFactor:\r\n        existingMasonryCharacteristicsParams?.confidenceFactor ??\r\n        moduleMaterialKnowledgeLevelValues.LC1,\r\n      averageCompressiveStrength:\r\n        existingMasonryCharacteristicsParams?.averageCompressiveStrength ?? 0,\r\n      averageShearStrengthRegularMasonry:\r\n        existingMasonryCharacteristicsParams?.averageShearStrengthRegularMasonry ??\r\n        0,\r\n      averageShearStrengthIrregularMasonry:\r\n        existingMasonryCharacteristicsParams?.averageShearStrengthIrregularMasonry ??\r\n        0,\r\n      averageNormalElasticityModulus:\r\n        existingMasonryCharacteristicsParams?.averageNormalElasticityModulus ??\r\n        0,\r\n      averageShearElasticityModulus:\r\n        existingMasonryCharacteristicsParams?.averageShearElasticityModulus ??\r\n        0,\r\n      specificWeight: existingMasonryCharacteristicsParams?.specificWeight ?? 0,\r\n      enhancementCharacteristics:\r\n        existingMasonryCharacteristicsParams?.enhancementCharacteristics ??\r\n        ENHANCEMENT_CHARACTERISTICS.NON_PRESENTI,\r\n      correctionCoefficient:\r\n        existingMasonryCharacteristicsParams?.correctionCoefficient ??\r\n        correctiveCoefficientValues[ENHANCEMENT_CHARACTERISTICS.NON_PRESENTI][\r\n          CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA\r\n        ],\r\n      amplifiedAverageCompressiveStrength:\r\n        existingMasonryCharacteristicsParams?.amplifiedAverageCompressiveStrength ??\r\n        0,\r\n      amplifiedAverageShearStrengthRegularMasonry:\r\n        existingMasonryCharacteristicsParams?.amplifiedAverageShearStrengthRegularMasonry ??\r\n        0,\r\n      amplifiedAverageShearStrengthIrregularMasonry:\r\n        existingMasonryCharacteristicsParams?.amplifiedAverageShearStrengthIrregularMasonry ??\r\n        0,\r\n      amplifiedAverageNormalElasticityModulus:\r\n        existingMasonryCharacteristicsParams?.amplifiedAverageNormalElasticityModulus ??\r\n        0,\r\n      amplifiedAverageShearElasticityModulus:\r\n        existingMasonryCharacteristicsParams?.amplifiedAverageShearElasticityModulus ??\r\n        0,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const knowledgeMaterialLevel = form.watch('knowledgeLevel')\r\n  const masonryType = form.watch('masonryType')\r\n  const enhancementCharacteristics = form.watch('enhancementCharacteristics')\r\n\r\n  useEffect(() => {\r\n    const confidenceFactor =\r\n      moduleMaterialKnowledgeLevelValues[knowledgeMaterialLevel]\r\n    form.setValue('confidenceFactor', confidenceFactor)\r\n\r\n    const averageCompressiveStrength =\r\n      averageCompressiveStrengthValues[masonryType][knowledgeMaterialLevel]\r\n    form.setValue('averageCompressiveStrength', averageCompressiveStrength)\r\n    const averageShearStrengthRegularMasonry =\r\n      averageShearStrengthRegularMasonryValues[masonryType][\r\n        knowledgeMaterialLevel\r\n      ]\r\n    form.setValue(\r\n      'averageShearStrengthRegularMasonry',\r\n      averageShearStrengthRegularMasonry,\r\n    )\r\n    const averageShearStrengthIrregularMasonry =\r\n      averageShearStrengthIrregularMasonryValues[masonryType][\r\n        knowledgeMaterialLevel\r\n      ]\r\n    form.setValue(\r\n      'averageShearStrengthIrregularMasonry',\r\n      averageShearStrengthIrregularMasonry,\r\n    )\r\n    const averageNormalElasticityModulus =\r\n      averageNormalElasticityModulusValues[masonryType][knowledgeMaterialLevel]\r\n    form.setValue(\r\n      'averageNormalElasticityModulus',\r\n      averageNormalElasticityModulus,\r\n    )\r\n    const averageShearElasticityModulus =\r\n      averageShearElasticityModulusValues[masonryType][knowledgeMaterialLevel]\r\n    form.setValue(\r\n      'averageShearElasticityModulus',\r\n      averageShearElasticityModulus,\r\n    )\r\n    const specificWeight = specificWeightValues[masonryType]\r\n    form.setValue('specificWeight', specificWeight)\r\n\r\n    const correctionCoefficient =\r\n      correctiveCoefficientValues[enhancementCharacteristics][masonryType]\r\n    form.setValue('correctionCoefficient', correctionCoefficient)\r\n\r\n    // amplified values:\r\n    form.setValue(\r\n      'amplifiedAverageCompressiveStrength',\r\n      averageCompressiveStrength * correctionCoefficient,\r\n    )\r\n    form.setValue(\r\n      'amplifiedAverageShearStrengthRegularMasonry',\r\n      averageShearStrengthRegularMasonry * correctionCoefficient,\r\n    )\r\n    form.setValue(\r\n      'amplifiedAverageShearStrengthIrregularMasonry',\r\n      averageShearStrengthIrregularMasonry * correctionCoefficient,\r\n    )\r\n    form.setValue(\r\n      'amplifiedAverageNormalElasticityModulus',\r\n      averageNormalElasticityModulus * correctionCoefficient,\r\n    )\r\n    form.setValue(\r\n      'amplifiedAverageShearElasticityModulus',\r\n      averageShearElasticityModulus * correctionCoefficient,\r\n    )\r\n  }, [knowledgeMaterialLevel, masonryType, enhancementCharacteristics, form])\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: ExistingMasonryCharacteristicsParamsSchemaInputs) => {\r\n      const updatedModule: CrmFormSchemaInputs = {\r\n        ...module,\r\n        preIntervention: {\r\n          existingMasonryCharacteristicsParams: body,\r\n          designStrengthPreInterventionCalculationResult:\r\n            module.preIntervention\r\n              ?.designStrengthPreInterventionCalculationResult ?? undefined,\r\n        },\r\n        postIntervention: module?.postIntervention ?? undefined,\r\n      }\r\n\r\n      mutate({ projectId, moduleId, body: updatedModule })\r\n    },\r\n    [mutate, projectId, moduleId, module],\r\n  )\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <Image\r\n          src=\"/assets/masonry-crm/crm-ante-intervento.jpg\"\r\n          alt=\"site characteristics\"\r\n          height={250}\r\n          width={500}\r\n          className=\"mx-auto rounded-md object-contain\"\r\n          priority\r\n        />\r\n        <NumberFormInput control={form.control} name=\"panelWidth\" t={t} />\r\n        <NumberFormInput control={form.control} name=\"panelThickness\" t={t} />\r\n        <NumberFormInput control={form.control} name=\"panelHeight\" t={t} />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"masonryType\"\r\n          options={MODULE_CRM_MASONRY_TYPE}\r\n          optionLabelFn={p => t(`masonryType.${p}`)}\r\n          t={t}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"knowledgeLevel\"\r\n          options={MODULE_MATERIAL_KNOWLEDGE_LEVEL}\r\n          optionLabelFn={p => t(`knowledgeLevel.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"confidenceFactor\"\r\n          t={t}\r\n          disabled={true}\r\n          // value is set automatically based on subsoilCategory\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"averageCompressiveStrength\"\r\n          t={t}\r\n          disabled={true}\r\n          // value is set automatically based on subsoilCategory\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"averageShearStrengthRegularMasonry\"\r\n          t={t}\r\n          disabled={true}\r\n          // value is set automatically based on subsoilCategory\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"averageShearStrengthIrregularMasonry\"\r\n          t={t}\r\n          disabled={true}\r\n          // value is set automatically based on subsoilCategory\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"averageNormalElasticityModulus\"\r\n          t={t}\r\n          disabled={true}\r\n          // value is set automatically based on subsoilCategory\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"averageShearElasticityModulus\"\r\n          t={t}\r\n          disabled={true}\r\n          // value is set automatically based on subsoilCategory\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"specificWeight\"\r\n          t={t}\r\n          disabled={true}\r\n          // value is set automatically based on subsoilCategory\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"enhancementCharacteristics\"\r\n          options={MODULE_ENHANCEMENT_CHARACTERISTICS}\r\n          optionLabelFn={p => t(`enhancementCharacteristics.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"correctionCoefficient\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"amplifiedAverageCompressiveStrength\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"amplifiedAverageShearStrengthRegularMasonry\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"amplifiedAverageShearStrengthIrregularMasonry\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"amplifiedAverageNormalElasticityModulus\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"amplifiedAverageShearElasticityModulus\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAkBA;AACA;AAKA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AASO,MAAM,2CAA2C,CAAC,EACvD,OAAO,EACP,SAAS,EACT,MAAM,EACN,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EACvB;IAEF,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,WAAW,OAAO,EAAE;IAC1B,MAAM,uCACJ,QAAQ,iBAAiB;IAC3B,MAAM,OAAO,IAAA,yKAAO,EAAmD;QACrE,UAAU,IAAA,6KAAW,EAAC,+LAA0C;QAChE,eAAe;YACb,YAAY,sCAAsC,cAAc;YAChE,gBAAgB,sCAAsC,kBAAkB;YACxE,aAAa,sCAAsC,eAAe;YAClE,aACE,sCAAsC,eACtC,+IAAgB,CAAC,gCAAgC;YACnD,gBACE,sCAAsC,kBACtC,2JAA4B,CAAC,GAAG;YAClC,kBACE,sCAAsC,oBACtC,iKAAkC,CAAC,GAAG;YACxC,4BACE,sCAAsC,8BAA8B;YACtE,oCACE,sCAAsC,sCACtC;YACF,sCACE,sCAAsC,wCACtC;YACF,gCACE,sCAAsC,kCACtC;YACF,+BACE,sCAAsC,iCACtC;YACF,gBAAgB,sCAAsC,kBAAkB;YACxE,4BACE,sCAAsC,8BACtC,0JAA2B,CAAC,YAAY;YAC1C,uBACE,sCAAsC,yBACtC,0JAA2B,CAAC,0JAA2B,CAAC,YAAY,CAAC,CACnE,+IAAgB,CAAC,gCAAgC,CAClD;YACH,qCACE,sCAAsC,uCACtC;YACF,6CACE,sCAAsC,+CACtC;YACF,+CACE,sCAAsC,iDACtC;YACF,yCACE,sCAAsC,2CACtC;YACF,wCACE,sCAAsC,0CACtC;QACJ;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAW;wBAAE;wBAAW;oBAAS;iBAAE;YAChD;YACA;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,yBAAyB,KAAK,KAAK,CAAC;IAC1C,MAAM,cAAc,KAAK,KAAK,CAAC;IAC/B,MAAM,6BAA6B,KAAK,KAAK,CAAC;IAE9C,IAAA,kNAAS,EAAC;QACR,MAAM,mBACJ,iKAAkC,CAAC,uBAAuB;QAC5D,KAAK,QAAQ,CAAC,oBAAoB;QAElC,MAAM,6BACJ,+JAAgC,CAAC,YAAY,CAAC,uBAAuB;QACvE,KAAK,QAAQ,CAAC,8BAA8B;QAC5C,MAAM,qCACJ,uKAAwC,CAAC,YAAY,CACnD,uBACD;QACH,KAAK,QAAQ,CACX,sCACA;QAEF,MAAM,uCACJ,yKAA0C,CAAC,YAAY,CACrD,uBACD;QACH,KAAK,QAAQ,CACX,wCACA;QAEF,MAAM,iCACJ,mKAAoC,CAAC,YAAY,CAAC,uBAAuB;QAC3E,KAAK,QAAQ,CACX,kCACA;QAEF,MAAM,gCACJ,kKAAmC,CAAC,YAAY,CAAC,uBAAuB;QAC1E,KAAK,QAAQ,CACX,iCACA;QAEF,MAAM,iBAAiB,mJAAoB,CAAC,YAAY;QACxD,KAAK,QAAQ,CAAC,kBAAkB;QAEhC,MAAM,wBACJ,0JAA2B,CAAC,2BAA2B,CAAC,YAAY;QACtE,KAAK,QAAQ,CAAC,yBAAyB;QAEvC,oBAAoB;QACpB,KAAK,QAAQ,CACX,uCACA,6BAA6B;QAE/B,KAAK,QAAQ,CACX,+CACA,qCAAqC;QAEvC,KAAK,QAAQ,CACX,iDACA,uCAAuC;QAEzC,KAAK,QAAQ,CACX,2CACA,iCAAiC;QAEnC,KAAK,QAAQ,CACX,0CACA,gCAAgC;IAEpC,GAAG;QAAC;QAAwB;QAAa;QAA4B;KAAK;IAE1E,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,MAAM,gBAAqC;YACzC,GAAG,MAAM;YACT,iBAAiB;gBACf,sCAAsC;gBACtC,gDACE,OAAO,eAAe,EAClB,kDAAkD;YAC1D;YACA,kBAAkB,QAAQ,oBAAoB;QAChD;QAEA,OAAO;YAAE;YAAW;YAAU,MAAM;QAAc;IACpD,GACA;QAAC;QAAQ;QAAW;QAAU;KAAO;IAGvC,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,8OAAC,wIAAK;oBACJ,KAAI;oBACJ,KAAI;oBACJ,QAAQ;oBACR,OAAO;oBACP,WAAU;oBACV,QAAQ;;;;;;8BAEV,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAa,GAAG;;;;;;8BAC7D,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAiB,GAAG;;;;;;8BACjE,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAc,GAAG;;;;;;8BAC9D,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,sJAAuB;oBAChC,eAAe,CAAA,IAAK,EAAE,CAAC,YAAY,EAAE,GAAG;oBACxC,GAAG;;;;;;8BAEL,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,8JAA+B;oBACxC,eAAe,CAAA,IAAK,EAAE,CAAC,eAAe,EAAE,GAAG;oBAC3C,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAGZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAGZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAGZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAGZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAGZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAGZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAGZ,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,iKAAkC;oBAC3C,eAAe,CAAA,IAAK,EAAE,CAAC,2BAA2B,EAAE,GAAG;oBACvD,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAGZ,8OAAC,4IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,8OAAC,oOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 5678, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/crm/crm-params-form.tsx"], "sourcesContent": ["import {\r\n  Accordion as AccordionComponent,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from '@atlas/components/ui/accordion'\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>ontent,\r\n  <PERSON><PERSON><PERSON>ist,\r\n  TabsTrigger,\r\n} from '@atlas/components/ui/tabs'\r\nimport type { ModuleWithParamsCrm } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useState } from 'react'\r\nimport { CrmPostInterventionForm } from './sections/post-intervention/crm-post-intervention-form'\r\nimport { DesignStrengthPreInterventionCalculationResultForm } from './sections/pre-intervention/design-strength-pre-intervention-calculation-result-form'\r\nimport { ExistingMasonryCharacteristicsParamsForm } from './sections/pre-intervention/existing-masonry-characteristics-params-form'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  module: ModuleWithParamsCrm\r\n}\r\n\r\nexport const CrmParamsForm = ({ session, projectId, module }: Props) => {\r\n  const [openItems, setOpenItems] = useState(['0'])\r\n  const t = useTranslations('forms.project-params.crm')\r\n\r\n  const [activeTab, setActiveTab] = useState('preIntervention')\r\n\r\n  const handleItemSaved = useCallback((id: string) => {\r\n    if (id === '1') {\r\n      setActiveTab('postIntervention')\r\n      return\r\n    }\r\n\r\n    const nextId = String(Number(id) + 1)\r\n    setOpenItems(old => {\r\n      const temp = old.filter(v => v !== id)\r\n      return old.includes(nextId) ? temp : [...temp, nextId]\r\n    })\r\n  }, [])\r\n\r\n  const isPreInterventionValid =\r\n    !!module?.preIntervention?.designStrengthPreInterventionCalculationResult &&\r\n    !!module?.preIntervention?.existingMasonryCharacteristicsParams\r\n\r\n  return (\r\n    <div>\r\n      <Tabs\r\n        defaultValue=\"preIntervention\"\r\n        value={activeTab}\r\n        onValueChange={setActiveTab}\r\n      >\r\n        <TabsList>\r\n          <TabsTrigger value=\"preIntervention\">\r\n            {t('crmPreIntervention.title')}\r\n          </TabsTrigger>\r\n          <TabsTrigger\r\n            value=\"postIntervention\"\r\n            disabled={!isPreInterventionValid}\r\n          >\r\n            {t('crmPostIntervention.title')}\r\n          </TabsTrigger>\r\n        </TabsList>\r\n        <TabsContent value=\"preIntervention\">\r\n          <AccordionComponent\r\n            type=\"multiple\"\r\n            value={openItems}\r\n            onValueChange={setOpenItems}\r\n          >\r\n            <AccordionItem value=\"0\">\r\n              <AccordionTrigger>\r\n                <h3 className=\"text-lg font-medium\">\r\n                  {t(\r\n                    'crmPreIntervention.existingMasonryCharacteristicsParams.title',\r\n                  )}\r\n                </h3>\r\n              </AccordionTrigger>\r\n              <AccordionContent>\r\n                <ExistingMasonryCharacteristicsParamsForm\r\n                  session={session}\r\n                  projectId={projectId}\r\n                  module={module}\r\n                  onSave={() => handleItemSaved('0')}\r\n                />\r\n              </AccordionContent>\r\n            </AccordionItem>\r\n            <AccordionItem value=\"1\">\r\n              <AccordionTrigger>\r\n                <h3 className=\"text-lg font-medium\">\r\n                  {t(\r\n                    'crmPreIntervention.designStrengthPreInterventionCalculationResult.title',\r\n                  )}\r\n                </h3>\r\n              </AccordionTrigger>\r\n              <AccordionContent>\r\n                <DesignStrengthPreInterventionCalculationResultForm\r\n                  session={session}\r\n                  projectId={projectId}\r\n                  module={module}\r\n                  onSave={() => handleItemSaved('1')}\r\n                />\r\n              </AccordionContent>\r\n            </AccordionItem>\r\n          </AccordionComponent>\r\n        </TabsContent>\r\n        <TabsContent value=\"postIntervention\">\r\n          <CrmPostInterventionForm\r\n            session={session}\r\n            projectId={projectId}\r\n            module={module}\r\n          />\r\n        </TabsContent>\r\n      </Tabs>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAMA;AASA;AACA;AACA;AACA;AACA;;;;;;;;;AAQO,MAAM,gBAAgB,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAS;IACjE,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;QAAC;KAAI;IAChD,MAAM,IAAI,IAAA,yNAAe,EAAC;IAE1B,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;IAE3C,MAAM,kBAAkB,IAAA,oNAAW,EAAC,CAAC;QACnC,IAAI,OAAO,KAAK;YACd,aAAa;YACb;QACF;QAEA,MAAM,SAAS,OAAO,OAAO,MAAM;QACnC,aAAa,CAAA;YACX,MAAM,OAAO,IAAI,MAAM,CAAC,CAAA,IAAK,MAAM;YACnC,OAAO,IAAI,QAAQ,CAAC,UAAU,OAAO;mBAAI;gBAAM;aAAO;QACxD;IACF,GAAG,EAAE;IAEL,MAAM,yBACJ,CAAC,CAAC,QAAQ,iBAAiB,kDAC3B,CAAC,CAAC,QAAQ,iBAAiB;IAE7B,qBACE,8OAAC;kBACC,cAAA,8OAAC,wIAAI;YACH,cAAa;YACb,OAAO;YACP,eAAe;;8BAEf,8OAAC,4IAAQ;;sCACP,8OAAC,+IAAW;4BAAC,OAAM;sCAChB,EAAE;;;;;;sCAEL,8OAAC,+IAAW;4BACV,OAAM;4BACN,UAAU,CAAC;sCAEV,EAAE;;;;;;;;;;;;8BAGP,8OAAC,+IAAW;oBAAC,OAAM;8BACjB,cAAA,8OAAC,kJAAkB;wBACjB,MAAK;wBACL,OAAO;wBACP,eAAe;;0CAEf,8OAAC,sJAAa;gCAAC,OAAM;;kDACnB,8OAAC,yJAAgB;kDACf,cAAA,8OAAC;4CAAG,WAAU;sDACX,EACC;;;;;;;;;;;kDAIN,8OAAC,yJAAgB;kDACf,cAAA,8OAAC,yUAAwC;4CACvC,SAAS;4CACT,WAAW;4CACX,QAAQ;4CACR,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;0CAIpC,8OAAC,sJAAa;gCAAC,OAAM;;kDACnB,8OAAC,yJAAgB;kDACf,cAAA,8OAAC;4CAAG,WAAU;sDACX,EACC;;;;;;;;;;;kDAIN,8OAAC,yJAAgB;kDACf,cAAA,8OAAC,qWAAkD;4CACjD,SAAS;4CACT,WAAW;4CACX,QAAQ;4CACR,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMxC,8OAAC,+IAAW;oBAAC,OAAM;8BACjB,cAAA,8OAAC,oSAAuB;wBACtB,SAAS;wBACT,WAAW;wBACX,QAAQ;;;;;;;;;;;;;;;;;;;;;;AAMpB", "debugId": null}}, {"offset": {"line": 5878, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-nonreinforced-section-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { NonReinforcedSectionSchema } from '@atlas/types/schemas/masonry/frcm-column-form'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  nonReinforcedSectionResults: NonReinforcedSectionSchema\r\n}\r\n\r\nexport function ConfinementNonReinforcedSectionResultCard({\r\n  nonReinforcedSectionResults,\r\n}: Props) {\r\n  const { normalStressStrength, designAxialResistance, check } =\r\n    nonReinforcedSectionResults\r\n\r\n  const t = useTranslations(\r\n    'components.calculations.frcm-column.confinementReinforcementCalculationResult.confinementNonReinforcedSectionResult',\r\n  )\r\n  const locale = useLocale()\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('normalStressStrength.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {normalStressStrength?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('designAxialResistance.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {designAxialResistance?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <span className=\"font-medium\">{t('check.label')}:</span>{' '}\r\n        <Badge\r\n          className={cn(\r\n            'text-base px-3 py-1',\r\n            check ? 'bg-green-600' : 'bg-red-600',\r\n          )}\r\n        >\r\n          {check ? t('check.satisfied') : t('check.notSatisfied')}\r\n        </Badge>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AAEA;AAAA;;;;;;AAMO,SAAS,0CAA0C,EACxD,2BAA2B,EACrB;IACN,MAAM,EAAE,oBAAoB,EAAE,qBAAqB,EAAE,KAAK,EAAE,GAC1D;IAEF,MAAM,IAAI,IAAA,yNAAe,EACvB;IAEF,MAAM,SAAS,IAAA,+KAAS;IAExB,qBACE,8OAAC,wIAAI;;0BACH,8OAAC,8IAAU;0BACT,cAAA,8OAAC,6IAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,8OAAC,+IAAW;gBAAC,WAAU;;kCACrB,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA8B;;;;;;;4BAC3B;0CACR,8OAAC;;oCACE,sBAAsB,eAAe,QAAQ;wCAC5C,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA+B;;;;;;;4BAC5B;0CACR,8OAAC;;oCACE,uBAAuB,eAAe,QAAQ;wCAC7C,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,8OAAC;wBAAK,WAAU;;4BAAe,EAAE;4BAAe;;;;;;;oBAAS;kCACzD,8OAAC,0IAAK;wBACJ,WAAW,IAAA,yHAAE,EACX,uBACA,QAAQ,iBAAiB;kCAG1B,QAAQ,EAAE,qBAAqB,EAAE;;;;;;;;;;;;;;;;;;AAK5C", "debugId": null}}, {"offset": {"line": 6016, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-reinforced-section-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { ReinforcedSectionSchema } from '@atlas/types/schemas/masonry/frcm-column-form'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  reinforcedSectionResults: ReinforcedSectionSchema\r\n}\r\n\r\nexport function ConfinementReinforcedSectionResultCard({\r\n  reinforcedSectionResults,\r\n}: Props) {\r\n  const {\r\n    coefficientOfResistanceIncrease,\r\n    confinedColumnDesignResistanceWithFrcm,\r\n    designAxialResistanceOfTheConfinedColumnWithFrcm,\r\n    check,\r\n  } = reinforcedSectionResults\r\n\r\n  const t = useTranslations(\r\n    'components.calculations.frcm-column.confinementReinforcementCalculationResult.confinementReinforcedSectionResult',\r\n  )\r\n  const locale = useLocale()\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('coefficientOfResistanceIncrease.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {coefficientOfResistanceIncrease?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('confinedColumnDesignResistanceWithFrcm.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {confinedColumnDesignResistanceWithFrcm?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <div>\r\n            <span className=\"font-medium\">\r\n              {t('designAxialResistanceOfTheConfinedColumnWithFrcm.label')}:\r\n            </span>{' '}\r\n            <span>\r\n              {designAxialResistanceOfTheConfinedColumnWithFrcm?.toLocaleString(\r\n                locale,\r\n                {\r\n                  maximumFractionDigits: 3,\r\n                },\r\n              )}{' '}\r\n            </span>\r\n          </div>\r\n          <span className=\"font-medium\">{t('check.label')}:</span>{' '}\r\n          <Badge\r\n            className={cn(\r\n              'text-base px-3 py-1',\r\n              check ? 'bg-green-600' : 'bg-red-600',\r\n            )}\r\n          >\r\n            {check ? t('check.satisfied') : t('check.notSatisfied')}\r\n          </Badge>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AAEA;AAAA;;;;;;AAMO,SAAS,uCAAuC,EACrD,wBAAwB,EAClB;IACN,MAAM,EACJ,+BAA+B,EAC/B,sCAAsC,EACtC,gDAAgD,EAChD,KAAK,EACN,GAAG;IAEJ,MAAM,IAAI,IAAA,yNAAe,EACvB;IAEF,MAAM,SAAS,IAAA,+KAAS;IAExB,qBACE,8OAAC,wIAAI;;0BACH,8OAAC,8IAAU;0BACT,cAAA,8OAAC,6IAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,8OAAC,+IAAW;gBAAC,WAAU;;kCACrB,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAyC;;;;;;;4BACtC;0CACR,8OAAC;;oCACE,iCAAiC,eAAe,QAAQ;wCACvD,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAgD;;;;;;;4BAC7C;0CACR,8OAAC;;oCACE,wCAAwC,eAAe,QAAQ;wCAC9D,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,8OAAC;;0CACC,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;;4CACb,EAAE;4CAA0D;;;;;;;oCACvD;kDACR,8OAAC;;4CACE,kDAAkD,eACjD,QACA;gDACE,uBAAuB;4CACzB;4CACC;;;;;;;;;;;;;0CAGP,8OAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAAe;;;;;;;4BAAS;0CACzD,8OAAC,0IAAK;gCACJ,WAAW,IAAA,yHAAE,EACX,uBACA,QAAQ,iBAAiB;0CAG1B,QAAQ,EAAE,qBAAqB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAM9C", "debugId": null}}, {"offset": {"line": 6194, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx"], "sourcesContent": ["import {\r\n  <PERSON>,\r\n  CardContent,\r\n  <PERSON>Header,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>ontent,\r\n  <PERSON><PERSON><PERSON>ist,\r\n  TabsTrigger,\r\n} from '@atlas/components/ui/tabs'\r\nimport type { ConfinementReinforcementCalculationResultSchema } from '@atlas/types/schemas/masonry/frcm-column-form'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\nimport { ConfinementNonReinforcedSectionResultCard } from './confinement-nonreinforced-section-result-card'\r\nimport { ConfinementReinforcedSectionResultCard } from './confinement-reinforced-section-result-card'\r\n\r\ntype Props = {\r\n  confinementReinforcementCalculationResult: ConfinementReinforcementCalculationResultSchema\r\n}\r\n\r\nexport function FrcmColumnConfinementReinforcementCalculationResult({\r\n  confinementReinforcementCalculationResult,\r\n}: Props) {\r\n  const t = useTranslations(\r\n    'components.calculations.frcm-column.confinementReinforcementCalculationResult',\r\n  )\r\n  const locale = useLocale()\r\n\r\n  return (\r\n    <>\r\n      <h3 className=\"text-lg font-medium py-4\">{t('title')}</h3>\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle>{t('title')}</CardTitle>\r\n        </CardHeader>\r\n        <CardContent className=\"space-y-4\">\r\n          <div>\r\n            <span className=\"font-medium\">\r\n              {t('matrixGeometricPercentage.label')}:\r\n            </span>{' '}\r\n            <span>\r\n              {confinementReinforcementCalculationResult.matrixGeometricPercentage?.toLocaleString(\r\n                locale,\r\n                {\r\n                  maximumFractionDigits: 5,\r\n                },\r\n              )}{' '}\r\n              %\r\n            </span>\r\n          </div>\r\n          <div>\r\n            <span className=\"font-medium\">\r\n              {t('reinforcementGeometricPercentage.label')}:\r\n            </span>{' '}\r\n            <span>\r\n              {confinementReinforcementCalculationResult.reinforcementGeometricPercentage?.toLocaleString(\r\n                locale,\r\n                {\r\n                  maximumFractionDigits: 5,\r\n                },\r\n              )}{' '}\r\n              %\r\n            </span>\r\n          </div>\r\n          <div>\r\n            <span className=\"font-medium\">\r\n              {t('coefficicentOfHorizontalEfficiency.label')}:\r\n            </span>{' '}\r\n            <span>\r\n              {confinementReinforcementCalculationResult.coefficicentOfHorizontalEfficiency?.toLocaleString(\r\n                locale,\r\n                {\r\n                  maximumFractionDigits: 5,\r\n                },\r\n              )}{' '}\r\n            </span>\r\n          </div>\r\n          <div>\r\n            <span className=\"font-medium\">\r\n              {t('coefficicentOfVerticalEfficiency.label')}:\r\n            </span>{' '}\r\n            <span>\r\n              {confinementReinforcementCalculationResult.coefficicentOfVerticalEfficiency?.toLocaleString(\r\n                locale,\r\n                {\r\n                  maximumFractionDigits: 5,\r\n                },\r\n              )}{' '}\r\n            </span>\r\n          </div>\r\n\r\n          <div>\r\n            <span className=\"font-medium\">\r\n              {t('coefficientOfOverallEfficiency.label')}:\r\n            </span>{' '}\r\n            <span>\r\n              {confinementReinforcementCalculationResult.coefficientOfOverallEfficiency?.toLocaleString(\r\n                locale,\r\n                {\r\n                  maximumFractionDigits: 5,\r\n                },\r\n              )}{' '}\r\n            </span>\r\n          </div>\r\n          <div>\r\n            <span className=\"font-medium\">\r\n              {t('coefficientOfEffectivenessOfTheMatrix.label')}:\r\n            </span>{' '}\r\n            <span>\r\n              {confinementReinforcementCalculationResult.coefficientOfEffectivenessOfTheMatrix?.toLocaleString(\r\n                locale,\r\n                {\r\n                  maximumFractionDigits: 5,\r\n                },\r\n              )}{' '}\r\n            </span>\r\n          </div>\r\n          <div>\r\n            <span className=\"font-medium\">\r\n              {t('computationalStrainOfTheComposite.label')}:\r\n            </span>{' '}\r\n            <span>\r\n              {confinementReinforcementCalculationResult.computationalStrainOfTheComposite?.toLocaleString(\r\n                locale,\r\n                {\r\n                  maximumFractionDigits: 5,\r\n                },\r\n              )}{' '}\r\n            </span>\r\n          </div>\r\n          <div>\r\n            <span className=\"font-medium\">{t('confiningPressure.label')}:</span>{' '}\r\n            <span>\r\n              {confinementReinforcementCalculationResult.confiningPressure?.toLocaleString(\r\n                locale,\r\n                {\r\n                  maximumFractionDigits: 5,\r\n                },\r\n              )}{' '}\r\n            </span>\r\n          </div>\r\n          <div>\r\n            <span className=\"font-medium\">\r\n              {t('effectiveConfiningPressure.label')}:\r\n            </span>{' '}\r\n            <span>\r\n              {confinementReinforcementCalculationResult.effectiveConfiningPressure?.toLocaleString(\r\n                locale,\r\n                {\r\n                  maximumFractionDigits: 5,\r\n                },\r\n              )}{' '}\r\n            </span>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n      <Tabs defaultValue=\"nonReinforcedSection\">\r\n        <TabsList>\r\n          <TabsTrigger value=\"nonReinforcedSection\">\r\n            {t('confinementNonReinforcedSectionResult.label')}\r\n          </TabsTrigger>\r\n          <TabsTrigger value=\"reinforcedSection\">\r\n            {t('confinementNonReinforcedSectionResult.label')}\r\n          </TabsTrigger>\r\n        </TabsList>\r\n        <TabsContent value=\"nonReinforcedSection\">\r\n          <ConfinementNonReinforcedSectionResultCard\r\n            nonReinforcedSectionResults={\r\n              confinementReinforcementCalculationResult?.confinementCheck\r\n                ?.nonReinforcedSection ?? {}\r\n            }\r\n          />\r\n        </TabsContent>\r\n        <TabsContent value=\"reinforcedSection\">\r\n          <ConfinementReinforcedSectionResultCard\r\n            reinforcedSectionResults={\r\n              confinementReinforcementCalculationResult?.confinementCheck\r\n                ?.reinforcedSection ?? {}\r\n            }\r\n          />\r\n        </TabsContent>\r\n      </Tabs>\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAMA;AAOA;AAAA;AACA;AACA;;;;;;;AAMO,SAAS,oDAAoD,EAClE,yCAAyC,EACnC;IACN,MAAM,IAAI,IAAA,yNAAe,EACvB;IAEF,MAAM,SAAS,IAAA,+KAAS;IAExB,qBACE;;0BACE,8OAAC;gBAAG,WAAU;0BAA4B,EAAE;;;;;;0BAC5C,8OAAC,wIAAI;;kCACH,8OAAC,8IAAU;kCACT,cAAA,8OAAC,6IAAS;sCAAE,EAAE;;;;;;;;;;;kCAEhB,8OAAC,+IAAW;wBAAC,WAAU;;0CACrB,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;;4CACb,EAAE;4CAAmC;;;;;;;oCAChC;kDACR,8OAAC;;4CACE,0CAA0C,yBAAyB,EAAE,eACpE,QACA;gDACE,uBAAuB;4CACzB;4CACC;4CAAI;;;;;;;;;;;;;0CAIX,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;;4CACb,EAAE;4CAA0C;;;;;;;oCACvC;kDACR,8OAAC;;4CACE,0CAA0C,gCAAgC,EAAE,eAC3E,QACA;gDACE,uBAAuB;4CACzB;4CACC;4CAAI;;;;;;;;;;;;;0CAIX,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;;4CACb,EAAE;4CAA4C;;;;;;;oCACzC;kDACR,8OAAC;;4CACE,0CAA0C,kCAAkC,EAAE,eAC7E,QACA;gDACE,uBAAuB;4CACzB;4CACC;;;;;;;;;;;;;0CAGP,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;;4CACb,EAAE;4CAA0C;;;;;;;oCACvC;kDACR,8OAAC;;4CACE,0CAA0C,gCAAgC,EAAE,eAC3E,QACA;gDACE,uBAAuB;4CACzB;4CACC;;;;;;;;;;;;;0CAIP,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;;4CACb,EAAE;4CAAwC;;;;;;;oCACrC;kDACR,8OAAC;;4CACE,0CAA0C,8BAA8B,EAAE,eACzE,QACA;gDACE,uBAAuB;4CACzB;4CACC;;;;;;;;;;;;;0CAGP,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;;4CACb,EAAE;4CAA+C;;;;;;;oCAC5C;kDACR,8OAAC;;4CACE,0CAA0C,qCAAqC,EAAE,eAChF,QACA;gDACE,uBAAuB;4CACzB;4CACC;;;;;;;;;;;;;0CAGP,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;;4CACb,EAAE;4CAA2C;;;;;;;oCACxC;kDACR,8OAAC;;4CACE,0CAA0C,iCAAiC,EAAE,eAC5E,QACA;gDACE,uBAAuB;4CACzB;4CACC;;;;;;;;;;;;;0CAGP,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;;4CAAe,EAAE;4CAA2B;;;;;;;oCAAS;kDACrE,8OAAC;;4CACE,0CAA0C,iBAAiB,EAAE,eAC5D,QACA;gDACE,uBAAuB;4CACzB;4CACC;;;;;;;;;;;;;0CAGP,8OAAC;;kDACC,8OAAC;wCAAK,WAAU;;4CACb,EAAE;4CAAoC;;;;;;;oCACjC;kDACR,8OAAC;;4CACE,0CAA0C,0BAA0B,EAAE,eACrE,QACA;gDACE,uBAAuB;4CACzB;4CACC;;;;;;;;;;;;;;;;;;;;;;;;;0BAKX,8OAAC,wIAAI;gBAAC,cAAa;;kCACjB,8OAAC,4IAAQ;;0CACP,8OAAC,+IAAW;gCAAC,OAAM;0CAChB,EAAE;;;;;;0CAEL,8OAAC,+IAAW;gCAAC,OAAM;0CAChB,EAAE;;;;;;;;;;;;kCAGP,8OAAC,+IAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,mUAAyC;4BACxC,6BACE,2CAA2C,kBACvC,wBAAwB,CAAC;;;;;;;;;;;kCAInC,8OAAC,+IAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,6TAAsC;4BACrC,0BACE,2CAA2C,kBACvC,qBAAqB,CAAC;;;;;;;;;;;;;;;;;;;AAOxC", "debugId": null}}, {"offset": {"line": 6612, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation.tsx"], "sourcesContent": ["import { CustomProductSection } from '@atlas/components/common/atlas/product/custom-product-section'\r\nimport { ProductSummaryCard } from '@atlas/components/common/atlas/product/product-summary-card'\r\nimport { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport {\r\n  FRM_GEOMETRY_TOPOLOGY,\r\n  MODULE_REINFORCEMENT_ARRANGEMENT,\r\n  REINFORCEMENT_ARRANGEMENT,\r\n} from '@atlas/constants/module'\r\nimport type { ModuleWithParamsFrcmColumn } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useModuleCalculation } from '@atlas/lib/mutation/modules/use-module-calculation'\r\nimport { useProductsByCategory } from '@atlas/lib/query/products/use-products-by-category'\r\nimport {\r\n  frcmColumnConfinementReinforcementInput,\r\n  type frcmColumnConfinementReinforcementSchema,\r\n} from '@atlas/types/schemas/masonry/frcm-column-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useMemo } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\nimport { FrcmColumnConfinementReinforcementCalculationResult } from './frcm-column-confinement-reinforcement-calculation-result'\r\n\r\ntype Props = {\r\n  module: ModuleWithParamsFrcmColumn\r\n  session: Session\r\n  projectId: Project['id']\r\n}\r\n\r\nexport const FrcmColumnConfinementReinforcementCalculation = ({\r\n  session,\r\n  module,\r\n  projectId,\r\n}: Props) => {\r\n  const t = useTranslations('forms.calculations.frcm-column')\r\n  const tAction = useTranslations('actions.calculations.frcm-column')\r\n  const tCommon = useTranslations('actions.common')\r\n  const {\r\n    params,\r\n    confinementReinforcementVerifyInput,\r\n    confinementReinforcementCalculationResult,\r\n  } = module\r\n  const form = useForm<frcmColumnConfinementReinforcementSchema>({\r\n    resolver: zodResolver(frcmColumnConfinementReinforcementInput),\r\n    defaultValues: {\r\n      calculationType: 'CONFINEMENT_VERIFY',\r\n      input: {\r\n        product: {\r\n          id: confinementReinforcementVerifyInput?.product.id,\r\n          name: confinementReinforcementVerifyInput?.product.name,\r\n          sourceType:\r\n            confinementReinforcementVerifyInput?.product.id === 'custom'\r\n              ? 'CUSTOM'\r\n              : 'DATABASE',\r\n        },\r\n        reinforcedArrangement:\r\n          confinementReinforcementVerifyInput?.reinforcedArrangement ??\r\n          REINFORCEMENT_ARRANGEMENT.CONTINUE,\r\n        singleWidthBand:\r\n          confinementReinforcementVerifyInput?.singleWidthBand ?? 0,\r\n        stepsOfTheBand:\r\n          confinementReinforcementVerifyInput?.stepsOfTheBand ?? 0,\r\n        clearDistanceBetweenStripes:\r\n          confinementReinforcementVerifyInput?.clearDistanceBetweenStripes ?? 0,\r\n        minimalTransversalDimension:\r\n          confinementReinforcementVerifyInput?.minimalTransversalDimension ?? 0,\r\n        numberOfReinforcementLayers:\r\n          confinementReinforcementVerifyInput?.numberOfReinforcementLayers ?? 0,\r\n        matrixThicknessOfTheSingleLayer:\r\n          confinementReinforcementVerifyInput?.matrixThicknessOfTheSingleLayer ??\r\n          0,\r\n      },\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useModuleCalculation(session.accessToken, {\r\n    onSuccess: () => {\r\n      toast.success(tAction('calculate.success'))\r\n    },\r\n    onError: error => {\r\n      console.log('ERROR  ', error)\r\n      toast.error(tAction('calculate.failure', { error: error.message }))\r\n    },\r\n  })\r\n\r\n  const handleFormSubmit = (body: frcmColumnConfinementReinforcementSchema) => {\r\n    mutate({ projectId, moduleId: module.id, body })\r\n  }\r\n\r\n  const {\r\n    data: products,\r\n    isError: errorGettingProducts,\r\n    isLoading: isLoadingProducts,\r\n  } = useProductsByCategory(session, 'FRCM_COLUMN', 0, 100)\r\n\r\n  const productsOptions = [\r\n    ...(products?.content.map(m => ({\r\n      value: m.id,\r\n      label: m.name ?? t('product.unnamed'),\r\n    })) ?? []),\r\n  ]\r\n\r\n  const [productId] = form.watch(['input.product.id'])\r\n\r\n  const selectedProduct = useMemo(\r\n    () => products?.content.find(p => p.id === productId),\r\n    [productId, products],\r\n  )\r\n\r\n  const stepsOfTheBandValue = form.watch('input.stepsOfTheBand') ?? 0\r\n  const singleWidthBandValue = form.watch('input.singleWidthBand') ?? 0\r\n  const reinforcedArrangementValue =\r\n    form.watch('input.reinforcedArrangement') ??\r\n    REINFORCEMENT_ARRANGEMENT.CONTINUE\r\n\r\n  const lengthOrDiameterValue =\r\n    params?.geometry?.largerSizeOrColumnDiameter ?? 0\r\n  const smallerSize = params?.geometry?.smallerSize ?? 0\r\n  const topologyValue =\r\n    params?.geometry?.topology ?? FRM_GEOMETRY_TOPOLOGY.RECTANGULAR\r\n  useEffect(() => {\r\n    const clearDistanceBetweenStripes =\r\n      reinforcedArrangementValue === REINFORCEMENT_ARRANGEMENT.CONTINUE\r\n        ? 0\r\n        : stepsOfTheBandValue - singleWidthBandValue\r\n    form.setValue(\r\n      'input.clearDistanceBetweenStripes',\r\n      parseFloat(clearDistanceBetweenStripes.toFixed(2)),\r\n    )\r\n\r\n    // =IF(D41=\"Continuo\",\"-\",IF(C8=\"Rettangolare\",MIN(D9,D10),D9))\r\n    const minimalTransversalDimension =\r\n      reinforcedArrangementValue === REINFORCEMENT_ARRANGEMENT.CONTINUE\r\n        ? 0\r\n        : topologyValue === FRM_GEOMETRY_TOPOLOGY.RECTANGULAR\r\n          ? Math.min(lengthOrDiameterValue, smallerSize)\r\n          : lengthOrDiameterValue\r\n\r\n    form.setValue(\r\n      'input.minimalTransversalDimension',\r\n      parseFloat(minimalTransversalDimension.toFixed(2)),\r\n    )\r\n\r\n    if (!productId) {\r\n      return\r\n    }\r\n    if (productId === 'custom') {\r\n      form.setValue('input.product.sourceType', 'CUSTOM')\r\n    }\r\n\r\n    if (selectedProduct) {\r\n      form.setValue('input.product', {\r\n        ...selectedProduct,\r\n        sourceType: 'DATABASE',\r\n      })\r\n    }\r\n  }, [\r\n    form,\r\n    productId,\r\n    selectedProduct,\r\n    stepsOfTheBandValue,\r\n    singleWidthBandValue,\r\n    lengthOrDiameterValue,\r\n    smallerSize,\r\n    topologyValue,\r\n    reinforcedArrangementValue,\r\n  ])\r\n\r\n  return (\r\n    <div className=\"flex flex-col justify-center gap-4\">\r\n      <Form {...form}>\r\n        <form\r\n          className=\"space-y-4 rounded-md border p-4\"\r\n          onSubmit={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          <h1 className=\"text-3xl font-bold\">{t('heading')}</h1>\r\n          <Image\r\n            src=\"/assets/masonry_frcm/column/FRCM_COLONNE MURATURA.jpg\"\r\n            alt=\"flexural verify\"\r\n            height={250}\r\n            width={500}\r\n            className=\"mx-auto rounded-md object-contain\"\r\n            priority\r\n          />\r\n          <h1 className=\"text-xl font-bold\">{t('sub-heading')}</h1>\r\n          <SelectFormInput\r\n            control={form.control}\r\n            name=\"input.product.id\"\r\n            options={productsOptions}\r\n            t={t}\r\n            loading={isLoadingProducts}\r\n            requestError={errorGettingProducts}\r\n            errorMessage={t('products.error')}\r\n          />\r\n          {productId === 'custom' && <CustomProductSection />}\r\n          {selectedProduct && <ProductSummaryCard product={selectedProduct} />}\r\n          <Separator />\r\n          <SelectFormFixedInput\r\n            control={form.control}\r\n            name=\"input.reinforcedArrangement\"\r\n            options={MODULE_REINFORCEMENT_ARRANGEMENT}\r\n            optionLabelFn={p => t(`input.reinforcedArrangement.${p}`)}\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.singleWidthBand\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.stepsOfTheBand\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.clearDistanceBetweenStripes\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.minimalTransversalDimension\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.numberOfReinforcementLayers\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.matrixThicknessOfTheSingleLayer\"\r\n            t={t}\r\n          />\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full sm:w-auto\"\r\n            disabled={isPending}\r\n          >\r\n            {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n            {tCommon('calculate')}\r\n          </Button>\r\n        </form>\r\n      </Form>\r\n      {confinementReinforcementCalculationResult && (\r\n        <FrcmColumnConfinementReinforcementCalculationResult\r\n          confinementReinforcementCalculationResult={\r\n            confinementReinforcementCalculationResult\r\n          }\r\n        />\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;AAQO,MAAM,gDAAgD,CAAC,EAC5D,OAAO,EACP,MAAM,EACN,SAAS,EACH;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,EACJ,MAAM,EACN,mCAAmC,EACnC,yCAAyC,EAC1C,GAAG;IACJ,MAAM,OAAO,IAAA,yKAAO,EAA2C;QAC7D,UAAU,IAAA,6KAAW,EAAC,uMAAuC;QAC7D,eAAe;YACb,iBAAiB;YACjB,OAAO;gBACL,SAAS;oBACP,IAAI,qCAAqC,QAAQ;oBACjD,MAAM,qCAAqC,QAAQ;oBACnD,YACE,qCAAqC,QAAQ,OAAO,WAChD,WACA;gBACR;gBACA,uBACE,qCAAqC,yBACrC,wJAAyB,CAAC,QAAQ;gBACpC,iBACE,qCAAqC,mBAAmB;gBAC1D,gBACE,qCAAqC,kBAAkB;gBACzD,6BACE,qCAAqC,+BAA+B;gBACtE,6BACE,qCAAqC,+BAA+B;gBACtE,6BACE,qCAAqC,+BAA+B;gBACtE,iCACE,qCAAqC,mCACrC;YACJ;QACF;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,0LAAoB,EAAC,QAAQ,WAAW,EAAE;QACtE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;QACxB;QACA,SAAS,CAAA;YACP,QAAQ,GAAG,CAAC,WAAW;YACvB,iJAAK,CAAC,KAAK,CAAC,QAAQ,qBAAqB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAClE;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAO;YAAE;YAAW,UAAU,OAAO,EAAE;YAAE;QAAK;IAChD;IAEA,MAAM,EACJ,MAAM,QAAQ,EACd,SAAS,oBAAoB,EAC7B,WAAW,iBAAiB,EAC7B,GAAG,IAAA,8LAAqB,EAAC,SAAS,eAAe,GAAG;IAErD,MAAM,kBAAkB;WAClB,UAAU,QAAQ,IAAI,CAAA,IAAK,CAAC;gBAC9B,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,IAAI,IAAI,EAAE;YACrB,CAAC,MAAM,EAAE;KACV;IAED,MAAM,CAAC,UAAU,GAAG,KAAK,KAAK,CAAC;QAAC;KAAmB;IAEnD,MAAM,kBAAkB,IAAA,gNAAO,EAC7B,IAAM,UAAU,QAAQ,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK,YAC3C;QAAC;QAAW;KAAS;IAGvB,MAAM,sBAAsB,KAAK,KAAK,CAAC,2BAA2B;IAClE,MAAM,uBAAuB,KAAK,KAAK,CAAC,4BAA4B;IACpE,MAAM,6BACJ,KAAK,KAAK,CAAC,kCACX,wJAAyB,CAAC,QAAQ;IAEpC,MAAM,wBACJ,QAAQ,UAAU,8BAA8B;IAClD,MAAM,cAAc,QAAQ,UAAU,eAAe;IACrD,MAAM,gBACJ,QAAQ,UAAU,YAAY,oJAAqB,CAAC,WAAW;IACjE,IAAA,kNAAS,EAAC;QACR,MAAM,8BACJ,+BAA+B,wJAAyB,CAAC,QAAQ,GAC7D,IACA,sBAAsB;QAC5B,KAAK,QAAQ,CACX,qCACA,WAAW,4BAA4B,OAAO,CAAC;QAGjD,+DAA+D;QAC/D,MAAM,8BACJ,+BAA+B,wJAAyB,CAAC,QAAQ,GAC7D,IACA,kBAAkB,oJAAqB,CAAC,WAAW,GACjD,KAAK,GAAG,CAAC,uBAAuB,eAChC;QAER,KAAK,QAAQ,CACX,qCACA,WAAW,4BAA4B,OAAO,CAAC;QAGjD,IAAI,CAAC,WAAW;YACd;QACF;QACA,IAAI,cAAc,UAAU;YAC1B,KAAK,QAAQ,CAAC,4BAA4B;QAC5C;QAEA,IAAI,iBAAiB;YACnB,KAAK,QAAQ,CAAC,iBAAiB;gBAC7B,GAAG,eAAe;gBAClB,YAAY;YACd;QACF;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,wIAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,8OAAC;oBACC,WAAU;oBACV,UAAU,KAAK,YAAY,CAAC;;sCAE5B,8OAAC;4BAAG,WAAU;sCAAsB,EAAE;;;;;;sCACtC,8OAAC,wIAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,QAAQ;4BACR,OAAO;4BACP,WAAU;4BACV,QAAQ;;;;;;sCAEV,8OAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS;4BACT,GAAG;4BACH,SAAS;4BACT,cAAc;4BACd,cAAc,EAAE;;;;;;wBAEjB,cAAc,0BAAY,8OAAC,wMAAoB;;;;;wBAC/C,iCAAmB,8OAAC,oMAAkB;4BAAC,SAAS;;;;;;sCACjD,8OAAC,kJAAS;;;;;sCACV,8OAAC,gMAAoB;4BACnB,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS,+JAAgC;4BACzC,eAAe,CAAA,IAAK,EAAE,CAAC,4BAA4B,EAAE,GAAG;4BACxD,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,4IAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU;;gCAET,2BAAa,8OAAC,oOAAW;oCAAC,WAAU;;;;;;gCACpC,QAAQ;;;;;;;;;;;;;;;;;;YAId,2DACC,8OAAC,2VAAmD;gBAClD,2CACE;;;;;;;;;;;;AAMZ", "debugId": null}}, {"offset": {"line": 6932, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-geometry-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport {\r\n  FRM_GEOMETRY_TOPOLOGY,\r\n  MODULE_FRM_GEOMETRY_TOPOLOGY,\r\n} from '@atlas/constants/module'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type FrcmColumnGeometryInput,\r\n  type FrcmColumnParamsSchemaInput,\r\n  frcmColumnGeometrySchema,\r\n} from '@atlas/types/schemas/masonry/frcm-column-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FrcmColumnGeometryInput>\r\n  params: FrcmColumnParamsSchemaInput\r\n  setParams: (newParams: any) => void\r\n  onSave: () => void\r\n}\r\n\r\nexport const FrcmColumnGeometryForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  params,\r\n  setParams,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.frcm-column.geometry')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const form = useForm<FrcmColumnGeometryInput>({\r\n    resolver: zodResolver(frcmColumnGeometrySchema),\r\n    defaultValues: {\r\n      topology: defaultValues?.topology ?? FRM_GEOMETRY_TOPOLOGY.RECTANGULAR,\r\n      largerSizeOrColumnDiameter:\r\n        defaultValues?.largerSizeOrColumnDiameter ?? 0,\r\n      smallerSize: defaultValues?.smallerSize ?? 0,\r\n      crossSectionArea: defaultValues?.crossSectionArea ?? 0,\r\n      crossSectionDiagonal: defaultValues?.crossSectionDiagonal ?? 0,\r\n      cornerRoundingRadius: defaultValues?.cornerRoundingRadius ?? 0,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: FrcmColumnGeometryInput) => {\r\n      // construct body based on global schema:\r\n      const frcmColumnParams: FrcmColumnParamsSchemaInput = {\r\n        ...params,\r\n        geometry: body,\r\n      }\r\n      mutate({ projectId, moduleId, body: frcmColumnParams })\r\n    },\r\n    [mutate, projectId, moduleId, params],\r\n  )\r\n\r\n  const topologyValue = form.watch('topology')\r\n  const smallerSizeValue = form.watch('smallerSize')\r\n  const largerSizeOrColumnDiameterValue = form.watch(\r\n    'largerSizeOrColumnDiameter',\r\n  )\r\n\r\n  useEffect(() => {\r\n    const smallerSize =\r\n      topologyValue === FRM_GEOMETRY_TOPOLOGY.CIRCULAR ? 0 : smallerSizeValue\r\n    form.setValue('smallerSize', parseFloat(smallerSize.toFixed(2)))\r\n\r\n    const crossSectionArea =\r\n      topologyValue === FRM_GEOMETRY_TOPOLOGY.CIRCULAR\r\n        ? Math.PI * (largerSizeOrColumnDiameterValue / 2) ** 2\r\n        : largerSizeOrColumnDiameterValue * smallerSizeValue\r\n    form.setValue('crossSectionArea', parseFloat(crossSectionArea.toFixed(2)))\r\n\r\n    const crossSectionDiagonal =\r\n      topologyValue === FRM_GEOMETRY_TOPOLOGY.CIRCULAR\r\n        ? 0\r\n        : Math.sqrt(\r\n            largerSizeOrColumnDiameterValue ** 2 + smallerSizeValue ** 2,\r\n          )\r\n    form.setValue(\r\n      'crossSectionDiagonal',\r\n      parseFloat(crossSectionDiagonal.toFixed(2)),\r\n    )\r\n  }, [topologyValue, smallerSizeValue, largerSizeOrColumnDiameterValue, form])\r\n\r\n  useEffect(() => {\r\n    const subscription = form.watch(values => {\r\n      setParams((prev: any) => ({\r\n        ...prev,\r\n        buildingCharacteristics: values,\r\n      }))\r\n    })\r\n    return () => subscription.unsubscribe()\r\n  }, [form, setParams])\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <Image\r\n          src=\"/assets/masonry_frcm/FRCM_Muratura.jpg\"\r\n          alt=\"site characteristics\"\r\n          height={250}\r\n          width={500}\r\n          className=\"mx-auto rounded-md object-contain\"\r\n          priority\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"topology\"\r\n          options={MODULE_FRM_GEOMETRY_TOPOLOGY}\r\n          optionLabelFn={p => t(`topology.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"largerSizeOrColumnDiameter\"\r\n          t={msg => t(`${topologyValue}.${msg}`)}\r\n          disabled={topologyValue === FRM_GEOMETRY_TOPOLOGY.CIRCULAR}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"smallerSize\"\r\n          t={msg => t(`${topologyValue}.${msg}`)}\r\n          disabled={topologyValue === FRM_GEOMETRY_TOPOLOGY.CIRCULAR}\r\n        />\r\n\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"crossSectionArea\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"crossSectionDiagonal\"\r\n          t={msg => t(`${topologyValue}.${msg}`)}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"cornerRoundingRadius\"\r\n          t={msg => t(`${topologyValue}.${msg}`)}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAMA;AACA;AAKA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AAYO,MAAM,yBAAyB,CAAC,EACrC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACN,SAAS,EACT,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,OAAO,IAAA,yKAAO,EAA0B;QAC5C,UAAU,IAAA,6KAAW,EAAC,wLAAwB;QAC9C,eAAe;YACb,UAAU,eAAe,YAAY,oJAAqB,CAAC,WAAW;YACtE,4BACE,eAAe,8BAA8B;YAC/C,aAAa,eAAe,eAAe;YAC3C,kBAAkB,eAAe,oBAAoB;YACrD,sBAAsB,eAAe,wBAAwB;YAC7D,sBAAsB,eAAe,wBAAwB;QAC/D;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAW;wBAAE;wBAAW;oBAAS;iBAAE;YAChD;YACA;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,yCAAyC;QACzC,MAAM,mBAAgD;YACpD,GAAG,MAAM;YACT,UAAU;QACZ;QACA,OAAO;YAAE;YAAW;YAAU,MAAM;QAAiB;IACvD,GACA;QAAC;QAAQ;QAAW;QAAU;KAAO;IAGvC,MAAM,gBAAgB,KAAK,KAAK,CAAC;IACjC,MAAM,mBAAmB,KAAK,KAAK,CAAC;IACpC,MAAM,kCAAkC,KAAK,KAAK,CAChD;IAGF,IAAA,kNAAS,EAAC;QACR,MAAM,cACJ,kBAAkB,oJAAqB,CAAC,QAAQ,GAAG,IAAI;QACzD,KAAK,QAAQ,CAAC,eAAe,WAAW,YAAY,OAAO,CAAC;QAE5D,MAAM,mBACJ,kBAAkB,oJAAqB,CAAC,QAAQ,GAC5C,KAAK,EAAE,GAAG,CAAC,kCAAkC,CAAC,KAAK,IACnD,kCAAkC;QACxC,KAAK,QAAQ,CAAC,oBAAoB,WAAW,iBAAiB,OAAO,CAAC;QAEtE,MAAM,uBACJ,kBAAkB,oJAAqB,CAAC,QAAQ,GAC5C,IACA,KAAK,IAAI,CACP,mCAAmC,IAAI,oBAAoB;QAEnE,KAAK,QAAQ,CACX,wBACA,WAAW,qBAAqB,OAAO,CAAC;IAE5C,GAAG;QAAC;QAAe;QAAkB;QAAiC;KAAK;IAE3E,IAAA,kNAAS,EAAC;QACR,MAAM,eAAe,KAAK,KAAK,CAAC,CAAA;YAC9B,UAAU,CAAC,OAAc,CAAC;oBACxB,GAAG,IAAI;oBACP,yBAAyB;gBAC3B,CAAC;QACH;QACA,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG;QAAC;QAAM;KAAU;IAEpB,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,8OAAC,wIAAK;oBACJ,KAAI;oBACJ,KAAI;oBACJ,QAAQ;oBACR,OAAO;oBACP,WAAU;oBACV,QAAQ;;;;;;8BAEV,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,2JAA4B;oBACrC,eAAe,CAAA,IAAK,EAAE,CAAC,SAAS,EAAE,GAAG;oBACrC,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG,CAAA,MAAO,EAAE,GAAG,cAAc,CAAC,EAAE,KAAK;oBACrC,UAAU,kBAAkB,oJAAqB,CAAC,QAAQ;;;;;;8BAE5D,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG,CAAA,MAAO,EAAE,GAAG,cAAc,CAAC,EAAE,KAAK;oBACrC,UAAU,kBAAkB,oJAAqB,CAAC,QAAQ;;;;;;8BAG5D,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG,CAAA,MAAO,EAAE,GAAG,cAAc,CAAC,EAAE,KAAK;oBACrC,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG,CAAA,MAAO,EAAE,GAAG,cAAc,CAAC,EAAE,KAAK;;;;;;8BAEvC,8OAAC,4IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,8OAAC,oOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 7163, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-masonry-characteristics-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport {\r\n  CRM_MASONRY_TYPE,\r\n  characteristicCompressiveStrengthValues,\r\n  characteristicNormalElasticityModulusValues,\r\n  characteristicShearElasticityModulusValues,\r\n  characteristicShearStrengthValues,\r\n  executionClass,\r\n  FACING_MATERIAL,\r\n  loadResistingCategory,\r\n  MODULE_CRM_MASONRY_TYPE,\r\n  MODULE_EXECUTION_CLASS,\r\n  MODULE_FACING_MATERIAL,\r\n  MODULE_GEOMETRY_EXPOSURE,\r\n  MODULE_LOAD_RESISTING_CATEGORY,\r\n  MODULE_MATERIAL_KNOWLEDGE_LEVEL,\r\n  masonryDensityValues,\r\n  masonryStrengthSafetyFactorMapping,\r\n  moduleGeometryExposure,\r\n  moduleMaterialKnowledgeLevel,\r\n  moduleMaterialKnowledgeLevelValues,\r\n} from '@atlas/constants/module'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type FrcmColumnMasonryCharacteristicsInput,\r\n  type FrcmColumnParamsSchemaInput,\r\n  frcmColumnMasonryCharacteristicsSchema,\r\n} from '@atlas/types/schemas/masonry/frcm-column-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FrcmColumnMasonryCharacteristicsInput>\r\n  params: FrcmColumnParamsSchemaInput\r\n  setParams: (newParams: any) => void\r\n  onSave: () => void\r\n}\r\n\r\nexport const FrcmColumnMasonryCharacteristicsForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  params,\r\n  setParams,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.project-params.frcm-column.masonry-characteristics',\r\n  )\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const form = useForm<FrcmColumnMasonryCharacteristicsInput>({\r\n    resolver: zodResolver(frcmColumnMasonryCharacteristicsSchema),\r\n    defaultValues: {\r\n      material: defaultValues?.material ?? FACING_MATERIAL.BRICK,\r\n      enhancementCharacteristics:\r\n        defaultValues?.enhancementCharacteristics ??\r\n        CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA,\r\n      knowledgeLevel:\r\n        defaultValues?.knowledgeLevel ?? moduleMaterialKnowledgeLevel.LC1,\r\n      confidenceFactor: defaultValues?.confidenceFactor ?? 0,\r\n      executionClass: defaultValues?.executionClass ?? executionClass.ONE,\r\n      loadResistantCategory:\r\n        defaultValues?.loadResistantCategory ??\r\n        loadResistingCategory.MASONRY_CAT_I_AND_MORTAR_WITH_GUARANTEED_PERFORMANCE,\r\n      masonrySafetyFactor: defaultValues?.masonrySafetyFactor ?? 0,\r\n      characteristicCompressiveStrength:\r\n        defaultValues?.characteristicCompressiveStrength ?? 0,\r\n      designCompressiveStrength: defaultValues?.designCompressiveStrength ?? 0,\r\n      characteristicShearStrength:\r\n        defaultValues?.characteristicShearStrength ?? 0,\r\n      designShearStrength: defaultValues?.designShearStrength ?? 0,\r\n      normalElasticityModulus: defaultValues?.normalElasticityModulus ?? 0,\r\n      shearElasticityModulus: defaultValues?.shearElasticityModulus ?? 0,\r\n      masonryDensity: defaultValues?.masonryDensity ?? 0,\r\n      exposureType:\r\n        defaultValues?.exposureType ?? moduleGeometryExposure.INTERNAL,\r\n      conversionFactor: defaultValues?.conversionFactor ?? 0,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: FrcmColumnMasonryCharacteristicsInput) => {\r\n      // construct body based on global schema:\r\n      const frcmColumnParams: FrcmColumnParamsSchemaInput = {\r\n        ...params,\r\n        masonryCharacteristics: body,\r\n      }\r\n      mutate({ projectId, moduleId, body: frcmColumnParams })\r\n    },\r\n    [mutate, projectId, moduleId, params],\r\n  )\r\n\r\n  const knowledgeMaterialLevel =\r\n    form.watch('knowledgeLevel') ?? moduleMaterialKnowledgeLevel.LC1\r\n  const executionClassFormValue =\r\n    form.watch('executionClass') ?? executionClass.ONE\r\n  const loadResistingCategoryFormValue =\r\n    form.watch('loadResistantCategory') ??\r\n    loadResistingCategory.MASONRY_CAT_I_AND_MORTAR_WITH_GUARANTEED_PERFORMANCE\r\n  const enhancementCharacteristics =\r\n    form.watch('enhancementCharacteristics') ??\r\n    CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA\r\n  const exposureTypeValue =\r\n    form.watch('exposureType') ?? moduleGeometryExposure.INTERNAL\r\n\r\n  useEffect(() => {\r\n    const calculateGivenStrength = (strengthMinMax: {\r\n      min: number\r\n      max: number\r\n    }) => {\r\n      return knowledgeMaterialLevel === moduleMaterialKnowledgeLevel.LC1\r\n        ? strengthMinMax.min\r\n        : knowledgeMaterialLevel === moduleMaterialKnowledgeLevel.LC3\r\n          ? strengthMinMax.max\r\n          : (strengthMinMax.min + strengthMinMax.max) / 2\r\n    }\r\n\r\n    const confidenceFactor =\r\n      moduleMaterialKnowledgeLevelValues[knowledgeMaterialLevel]\r\n    form.setValue('confidenceFactor', confidenceFactor)\r\n\r\n    const masonryStrengthSafetyFactor =\r\n      masonryStrengthSafetyFactorMapping[loadResistingCategoryFormValue][\r\n        executionClassFormValue\r\n      ]\r\n    form.setValue('masonrySafetyFactor', masonryStrengthSafetyFactor)\r\n\r\n    const characteristicCompressiveStrengthMinMax =\r\n      characteristicCompressiveStrengthValues[enhancementCharacteristics]\r\n\r\n    const characteristicCompressiveStrength = calculateGivenStrength(\r\n      characteristicCompressiveStrengthMinMax,\r\n    )\r\n    form.setValue(\r\n      'characteristicCompressiveStrength',\r\n      characteristicCompressiveStrength,\r\n    )\r\n\r\n    const designCompressiveStrength =\r\n      characteristicCompressiveStrength /\r\n      masonryStrengthSafetyFactor /\r\n      confidenceFactor\r\n    form.setValue('designCompressiveStrength', designCompressiveStrength)\r\n\r\n    const characteristicsShearStrengthMinMax =\r\n      characteristicShearStrengthValues[enhancementCharacteristics]\r\n    const characteristicShearStrength = calculateGivenStrength(\r\n      characteristicsShearStrengthMinMax,\r\n    )\r\n    form.setValue('characteristicShearStrength', characteristicShearStrength)\r\n\r\n    const designShearStrength =\r\n      characteristicShearStrength /\r\n      confidenceFactor /\r\n      masonryStrengthSafetyFactor\r\n    form.setValue('designShearStrength', designShearStrength)\r\n\r\n    const normalElasticityModulusMinMax =\r\n      characteristicNormalElasticityModulusValues[enhancementCharacteristics]\r\n    const normalElasticityModulus =\r\n      (normalElasticityModulusMinMax.min + normalElasticityModulusMinMax.max) /\r\n      2\r\n    form.setValue('normalElasticityModulus', normalElasticityModulus)\r\n\r\n    const shearElasticityModulusMinMax =\r\n      characteristicShearElasticityModulusValues[enhancementCharacteristics]\r\n    const shearElasticityModulus =\r\n      (shearElasticityModulusMinMax.min + shearElasticityModulusMinMax.max) / 2\r\n    form.setValue('shearElasticityModulus', shearElasticityModulus)\r\n\r\n    const masonryDensity = masonryDensityValues[enhancementCharacteristics]\r\n    form.setValue('masonryDensity', masonryDensity)\r\n\r\n    const conversionFactor =\r\n      exposureTypeValue === moduleGeometryExposure.INTERNAL\r\n        ? 0.9\r\n        : exposureTypeValue === moduleGeometryExposure.EXTERNAL\r\n          ? 0.8\r\n          : 0.7\r\n    form.setValue('conversionFactor', conversionFactor)\r\n  }, [\r\n    form,\r\n    knowledgeMaterialLevel,\r\n    executionClassFormValue,\r\n    loadResistingCategoryFormValue,\r\n    enhancementCharacteristics,\r\n    exposureTypeValue,\r\n  ])\r\n\r\n  useEffect(() => {\r\n    const subscription = form.watch(values => {\r\n      setParams((prev: any) => ({\r\n        ...prev,\r\n        buildingCharacteristics: values,\r\n      }))\r\n    })\r\n    return () => subscription.unsubscribe()\r\n  }, [form, setParams])\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"material\"\r\n          options={MODULE_FACING_MATERIAL}\r\n          optionLabelFn={p => t(`material.${p}`)}\r\n          t={t}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"enhancementCharacteristics\"\r\n          options={MODULE_CRM_MASONRY_TYPE}\r\n          optionLabelFn={p => t(`enhancementCharacteristics.${p}`)}\r\n          t={t}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"knowledgeLevel\"\r\n          options={MODULE_MATERIAL_KNOWLEDGE_LEVEL}\r\n          optionLabelFn={p => t(`knowledgeLevel.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"confidenceFactor\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"executionClass\"\r\n          options={MODULE_EXECUTION_CLASS}\r\n          optionLabelFn={p => t(`executionClass.${p}`)}\r\n          t={t}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"loadResistantCategory\"\r\n          options={MODULE_LOAD_RESISTING_CATEGORY}\r\n          optionLabelFn={p => t(`loadResistantCategory.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"masonrySafetyFactor\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"characteristicCompressiveStrength\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"designCompressiveStrength\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"characteristicShearStrength\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"designShearStrength\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"normalElasticityModulus\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"shearElasticityModulus\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"masonryDensity\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"exposureType\"\r\n          options={MODULE_GEOMETRY_EXPOSURE}\r\n          optionLabelFn={p => t(`exposureType.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"conversionFactor\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAuBA;AACA;AAKA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AAYO,MAAM,uCAAuC,CAAC,EACnD,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACN,SAAS,EACT,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EACvB;IAEF,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,OAAO,IAAA,yKAAO,EAAwC;QAC1D,UAAU,IAAA,6KAAW,EAAC,sMAAsC;QAC5D,eAAe;YACb,UAAU,eAAe,YAAY,8IAAe,CAAC,KAAK;YAC1D,4BACE,eAAe,8BACf,+IAAgB,CAAC,gCAAgC;YACnD,gBACE,eAAe,kBAAkB,2JAA4B,CAAC,GAAG;YACnE,kBAAkB,eAAe,oBAAoB;YACrD,gBAAgB,eAAe,kBAAkB,6IAAc,CAAC,GAAG;YACnE,uBACE,eAAe,yBACf,oJAAqB,CAAC,oDAAoD;YAC5E,qBAAqB,eAAe,uBAAuB;YAC3D,mCACE,eAAe,qCAAqC;YACtD,2BAA2B,eAAe,6BAA6B;YACvE,6BACE,eAAe,+BAA+B;YAChD,qBAAqB,eAAe,uBAAuB;YAC3D,yBAAyB,eAAe,2BAA2B;YACnE,wBAAwB,eAAe,0BAA0B;YACjE,gBAAgB,eAAe,kBAAkB;YACjD,cACE,eAAe,gBAAgB,qJAAsB,CAAC,QAAQ;YAChE,kBAAkB,eAAe,oBAAoB;QACvD;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAW;wBAAE;wBAAW;oBAAS;iBAAE;YAChD;YACA;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,yCAAyC;QACzC,MAAM,mBAAgD;YACpD,GAAG,MAAM;YACT,wBAAwB;QAC1B;QACA,OAAO;YAAE;YAAW;YAAU,MAAM;QAAiB;IACvD,GACA;QAAC;QAAQ;QAAW;QAAU;KAAO;IAGvC,MAAM,yBACJ,KAAK,KAAK,CAAC,qBAAqB,2JAA4B,CAAC,GAAG;IAClE,MAAM,0BACJ,KAAK,KAAK,CAAC,qBAAqB,6IAAc,CAAC,GAAG;IACpD,MAAM,iCACJ,KAAK,KAAK,CAAC,4BACX,oJAAqB,CAAC,oDAAoD;IAC5E,MAAM,6BACJ,KAAK,KAAK,CAAC,iCACX,+IAAgB,CAAC,gCAAgC;IACnD,MAAM,oBACJ,KAAK,KAAK,CAAC,mBAAmB,qJAAsB,CAAC,QAAQ;IAE/D,IAAA,kNAAS,EAAC;QACR,MAAM,yBAAyB,CAAC;YAI9B,OAAO,2BAA2B,2JAA4B,CAAC,GAAG,GAC9D,eAAe,GAAG,GAClB,2BAA2B,2JAA4B,CAAC,GAAG,GACzD,eAAe,GAAG,GAClB,CAAC,eAAe,GAAG,GAAG,eAAe,GAAG,IAAI;QACpD;QAEA,MAAM,mBACJ,iKAAkC,CAAC,uBAAuB;QAC5D,KAAK,QAAQ,CAAC,oBAAoB;QAElC,MAAM,8BACJ,iKAAkC,CAAC,+BAA+B,CAChE,wBACD;QACH,KAAK,QAAQ,CAAC,uBAAuB;QAErC,MAAM,0CACJ,sKAAuC,CAAC,2BAA2B;QAErE,MAAM,oCAAoC,uBACxC;QAEF,KAAK,QAAQ,CACX,qCACA;QAGF,MAAM,4BACJ,oCACA,8BACA;QACF,KAAK,QAAQ,CAAC,6BAA6B;QAE3C,MAAM,qCACJ,gKAAiC,CAAC,2BAA2B;QAC/D,MAAM,8BAA8B,uBAClC;QAEF,KAAK,QAAQ,CAAC,+BAA+B;QAE7C,MAAM,sBACJ,8BACA,mBACA;QACF,KAAK,QAAQ,CAAC,uBAAuB;QAErC,MAAM,gCACJ,0KAA2C,CAAC,2BAA2B;QACzE,MAAM,0BACJ,CAAC,8BAA8B,GAAG,GAAG,8BAA8B,GAAG,IACtE;QACF,KAAK,QAAQ,CAAC,2BAA2B;QAEzC,MAAM,+BACJ,yKAA0C,CAAC,2BAA2B;QACxE,MAAM,yBACJ,CAAC,6BAA6B,GAAG,GAAG,6BAA6B,GAAG,IAAI;QAC1E,KAAK,QAAQ,CAAC,0BAA0B;QAExC,MAAM,iBAAiB,mJAAoB,CAAC,2BAA2B;QACvE,KAAK,QAAQ,CAAC,kBAAkB;QAEhC,MAAM,mBACJ,sBAAsB,qJAAsB,CAAC,QAAQ,GACjD,MACA,sBAAsB,qJAAsB,CAAC,QAAQ,GACnD,MACA;QACR,KAAK,QAAQ,CAAC,oBAAoB;IACpC,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;KACD;IAED,IAAA,kNAAS,EAAC;QACR,MAAM,eAAe,KAAK,KAAK,CAAC,CAAA;YAC9B,UAAU,CAAC,OAAc,CAAC;oBACxB,GAAG,IAAI;oBACP,yBAAyB;gBAC3B,CAAC;QACH;QACA,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG;QAAC;QAAM;KAAU;IAEpB,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,qJAAsB;oBAC/B,eAAe,CAAA,IAAK,EAAE,CAAC,SAAS,EAAE,GAAG;oBACrC,GAAG;;;;;;8BAEL,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,sJAAuB;oBAChC,eAAe,CAAA,IAAK,EAAE,CAAC,2BAA2B,EAAE,GAAG;oBACvD,GAAG;;;;;;8BAEL,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,8JAA+B;oBACxC,eAAe,CAAA,IAAK,EAAE,CAAC,eAAe,EAAE,GAAG;oBAC3C,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,qJAAsB;oBAC/B,eAAe,CAAA,IAAK,EAAE,CAAC,eAAe,EAAE,GAAG;oBAC3C,GAAG;;;;;;8BAEL,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,6JAA8B;oBACvC,eAAe,CAAA,IAAK,EAAE,CAAC,sBAAsB,EAAE,GAAG;oBAClD,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,uJAAwB;oBACjC,eAAe,CAAA,IAAK,EAAE,CAAC,aAAa,EAAE,GAAG;oBACzC,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,4IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,8OAAC,oOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 7521, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-stress-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type FrcmColumnParamsSchemaInput,\r\n  type FrcmColumnStressInput,\r\n  frcmColumnStressSchema,\r\n} from '@atlas/types/schemas/masonry/frcm-column-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FrcmColumnStressInput>\r\n  params: FrcmColumnParamsSchemaInput\r\n  setParams: (newParams: any) => void\r\n  onSave: () => void\r\n}\r\n\r\nexport const FrcmColumnStressForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  params,\r\n  setParams,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.frcm-column.stress')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const form = useForm<FrcmColumnStressInput>({\r\n    resolver: zodResolver(frcmColumnStressSchema),\r\n    defaultValues: {\r\n      normalStressCenteredStressing:\r\n        defaultValues?.normalStressCenteredStressing ?? 0,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: FrcmColumnStressInput) => {\r\n      // construct body based on global schema:\r\n      const frcmColumnParams: FrcmColumnParamsSchemaInput = {\r\n        ...params,\r\n        stress: body,\r\n      }\r\n      mutate({ projectId, moduleId, body: frcmColumnParams })\r\n    },\r\n    [mutate, projectId, moduleId, params],\r\n  )\r\n\r\n  useEffect(() => {\r\n    const subscription = form.watch(values => {\r\n      setParams((prev: any) => ({\r\n        ...prev,\r\n        buildingCharacteristics: values,\r\n      }))\r\n    })\r\n    return () => subscription.unsubscribe()\r\n  }, [form, setParams])\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"normalStressCenteredStressing\"\r\n          t={t}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAGA;AACA;AAKA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;AAYO,MAAM,uBAAuB,CAAC,EACnC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACN,SAAS,EACT,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,OAAO,IAAA,yKAAO,EAAwB;QAC1C,UAAU,IAAA,6KAAW,EAAC,sLAAsB;QAC5C,eAAe;YACb,+BACE,eAAe,iCAAiC;QACpD;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAW;wBAAE;wBAAW;oBAAS;iBAAE;YAChD;YACA;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,yCAAyC;QACzC,MAAM,mBAAgD;YACpD,GAAG,MAAM;YACT,QAAQ;QACV;QACA,OAAO;YAAE;YAAW;YAAU,MAAM;QAAiB;IACvD,GACA;QAAC;QAAQ;QAAW;QAAU;KAAO;IAGvC,IAAA,kNAAS,EAAC;QACR,MAAM,eAAe,KAAK,KAAK,CAAC,CAAA;YAC9B,UAAU,CAAC,OAAc,CAAC;oBACxB,GAAG,IAAI;oBACP,yBAAyB;gBAC3B,CAAC;QACH;QACA,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG;QAAC;QAAM;KAAU;IAEpB,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,8OAAC,4IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,8OAAC,oOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 7662, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/frcm-column-params-form.tsx"], "sourcesContent": ["import {\r\n  Accordion as AccordionComponent,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from '@atlas/components/ui/accordion'\r\nimport type {\r\n  Module,\r\n  ModuleWithParamsFrcmColumn,\r\n} from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { FrcmColumnParamsSchemaInput } from '@atlas/types/schemas/masonry/frcm-column-form'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useState } from 'react'\r\nimport { FrcmColumnConfinementReinforcementCalculation } from './calculations/frcm-column-confinement-reinforcement-calculation'\r\nimport { FrcmColumnGeometryForm } from './sections/frcm-column-geometry-form'\r\nimport { FrcmColumnMasonryCharacteristicsForm } from './sections/frcm-column-masonry-characteristics-form'\r\nimport { FrcmColumnStressForm } from './sections/frcm-column-stress-form'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  module: ModuleWithParamsFrcmColumn\r\n}\r\n\r\nexport const FrcmColumnParamsForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  module,\r\n}: Props) => {\r\n  const [params, setParams] = useState<FrcmColumnParamsSchemaInput>(\r\n    module?.params ?? ({} as FrcmColumnParamsSchemaInput),\r\n  )\r\n\r\n  const [openItems, setOpenItems] = useState(['0'])\r\n  const t = useTranslations('forms.project-params.frcm-column')\r\n\r\n  const handleItemSaved = useCallback((id: string) => {\r\n    const nextId = String(Number(id) + 1)\r\n    setOpenItems(old => {\r\n      const temp = old.filter(v => v !== id)\r\n      return old.includes(nextId) ? temp : [...temp, nextId]\r\n    })\r\n  }, [])\r\n\r\n  return (\r\n    <div>\r\n      <AccordionComponent\r\n        type=\"multiple\"\r\n        value={openItems}\r\n        onValueChange={setOpenItems}\r\n      >\r\n        <AccordionItem value=\"0\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">{t('geometry.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <FrcmColumnGeometryForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={params?.geometry}\r\n              setParams={setParams}\r\n              params={params}\r\n              onSave={() => handleItemSaved('0')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"1\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('masonry-characteristics.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <FrcmColumnMasonryCharacteristicsForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={params?.masonryCharacteristics}\r\n              setParams={setParams}\r\n              params={params}\r\n              onSave={() => handleItemSaved('1')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"2\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">{t('stress.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <FrcmColumnStressForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={params.stress}\r\n              params={params}\r\n              setParams={setParams}\r\n              onSave={() => handleItemSaved('2')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem\r\n          value=\"3\"\r\n          disabled={\r\n            !module?.params?.geometry ||\r\n            !module?.params?.masonryCharacteristics ||\r\n            !module?.params?.stress\r\n          }\r\n        >\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('confinement-reinforcement.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <FrcmColumnConfinementReinforcementCalculation\r\n              session={session}\r\n              projectId={projectId}\r\n              module={module}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n      </AccordionComponent>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAaA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AASO,MAAM,uBAAuB,CAAC,EACnC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,MAAM,EACA;IACN,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAClC,QAAQ,UAAW,CAAC;IAGtB,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;QAAC;KAAI;IAChD,MAAM,IAAI,IAAA,yNAAe,EAAC;IAE1B,MAAM,kBAAkB,IAAA,oNAAW,EAAC,CAAC;QACnC,MAAM,SAAS,OAAO,OAAO,MAAM;QACnC,aAAa,CAAA;YACX,MAAM,OAAO,IAAI,MAAM,CAAC,CAAA,IAAK,MAAM;YACnC,OAAO,IAAI,QAAQ,CAAC,UAAU,OAAO;mBAAI;gBAAM;aAAO;QACxD;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;kBACC,cAAA,8OAAC,kJAAkB;YACjB,MAAK;YACL,OAAO;YACP,eAAe;;8BAEf,8OAAC,sJAAa;oBAAC,OAAM;;sCACnB,8OAAC,yJAAgB;sCACf,cAAA,8OAAC;gCAAG,WAAU;0CAAuB,EAAE;;;;;;;;;;;sCAEzC,8OAAC,yJAAgB;sCACf,cAAA,8OAAC,qRAAsB;gCACrB,SAAS;gCACT,WAAW;gCACX,UAAU;gCACV,eAAe,QAAQ;gCACvB,WAAW;gCACX,QAAQ;gCACR,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,8OAAC,sJAAa;oBAAC,OAAM;;sCACnB,8OAAC,yJAAgB;sCACf,cAAA,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;;;;;;sCAGP,8OAAC,yJAAgB;sCACf,cAAA,8OAAC,qTAAoC;gCACnC,SAAS;gCACT,WAAW;gCACX,UAAU;gCACV,eAAe,QAAQ;gCACvB,WAAW;gCACX,QAAQ;gCACR,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,8OAAC,sJAAa;oBAAC,OAAM;;sCACnB,8OAAC,yJAAgB;sCACf,cAAA,8OAAC;gCAAG,WAAU;0CAAuB,EAAE;;;;;;;;;;;sCAEzC,8OAAC,yJAAgB;sCACf,cAAA,8OAAC,iRAAoB;gCACnB,SAAS;gCACT,WAAW;gCACX,UAAU;gCACV,eAAe,OAAO,MAAM;gCAC5B,QAAQ;gCACR,WAAW;gCACX,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,8OAAC,sJAAa;oBACZ,OAAM;oBACN,UACE,CAAC,QAAQ,QAAQ,YACjB,CAAC,QAAQ,QAAQ,0BACjB,CAAC,QAAQ,QAAQ;;sCAGnB,8OAAC,yJAAgB;sCACf,cAAA,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;;;;;;sCAGP,8OAAC,yJAAgB;sCACf,cAAA,8OAAC,2UAA6C;gCAC5C,SAAS;gCACT,WAAW;gCACX,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB", "debugId": null}}, {"offset": {"line": 7885, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { ExtremityDetachementCheckSchema } from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  extremityDetachementCheck?: ExtremityDetachementCheckSchema\r\n}\r\n\r\nexport function ExtremityDetachementCheck({\r\n  extremityDetachementCheck,\r\n}: Props) {\r\n  const {\r\n    appliedSpecificBendingMoment30FromEdge,\r\n    reinforcementDesignStrainForEndDebonding,\r\n    neutralAxisCompressedEdgeDistance,\r\n    masonryStrain,\r\n    resultantCompressiveForceMasonry,\r\n    resultantTensileForceFrcm,\r\n    designMomentCapacityReinforcedSection,\r\n    check,\r\n  } = extremityDetachementCheck || {}\r\n\r\n  const t = useTranslations(\r\n    'components.calculations.maschi-murari.flexuralReinforcementCalculationResult.extremityDetachementCheck',\r\n  )\r\n\r\n  const locale = useLocale()\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('appliedSpecificBendingMoment30FromEdge.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {appliedSpecificBendingMoment30FromEdge?.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('reinforcementDesignStrainForEndDebonding.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {reinforcementDesignStrainForEndDebonding?.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('neutralAxisCompressedEdgeDistance.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {neutralAxisCompressedEdgeDistance?.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">{t('masonryStrain.label')}:</span>{' '}\r\n          <span>\r\n            {masonryStrain?.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('resultantCompressiveForceMasonry.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {resultantCompressiveForceMasonry?.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('resultantTensileForceFrcm.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {resultantTensileForceFrcm?.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('designMomentCapacityReinforcedSection.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {designMomentCapacityReinforcedSection?.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <span className=\"font-medium\">{t('check.label')}:</span>{' '}\r\n        <Badge\r\n          className={cn(\r\n            'text-base px-3 py-1',\r\n            check ? 'bg-green-600' : 'bg-red-600',\r\n          )}\r\n        >\r\n          {check ? t('check.satisfied') : t('check.notSatisfied')}\r\n        </Badge>\r\n        {check ? null : (\r\n          <div>\r\n            <span className=\"font-medium text-red-600\">\r\n              {' '}\r\n              {t('checkResultingText.no')}\r\n            </span>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AAEA;AAAA;;;;;;AAMO,SAAS,0BAA0B,EACxC,yBAAyB,EACnB;IACN,MAAM,EACJ,sCAAsC,EACtC,wCAAwC,EACxC,iCAAiC,EACjC,aAAa,EACb,gCAAgC,EAChC,yBAAyB,EACzB,qCAAqC,EACrC,KAAK,EACN,GAAG,6BAA6B,CAAC;IAElC,MAAM,IAAI,IAAA,yNAAe,EACvB;IAGF,MAAM,SAAS,IAAA,+KAAS;IAExB,qBACE,8OAAC,wIAAI;;0BACH,8OAAC,8IAAU;0BACT,cAAA,8OAAC,6IAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,8OAAC,+IAAW;gBAAC,WAAU;;kCACrB,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAgD;;;;;;;4BAC7C;0CACR,8OAAC;;oCACE,wCAAwC,eAAe,QAAQ;wCAC9D,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAkD;;;;;;;4BAC/C;0CACR,8OAAC;;oCACE,0CAA0C,eAAe,QAAQ;wCAChE,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA2C;;;;;;;4BACxC;0CACR,8OAAC;;oCACE,mCAAmC,eAAe,QAAQ;wCACzD,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAAuB;;;;;;;4BAAS;0CACjE,8OAAC;;oCACE,eAAe,eAAe,QAAQ;wCACrC,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA0C;;;;;;;4BACvC;0CACR,8OAAC;;oCACE,kCAAkC,eAAe,QAAQ;wCACxD,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAmC;;;;;;;4BAChC;0CACR,8OAAC;;oCACE,2BAA2B,eAAe,QAAQ;wCACjD,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA+C;;;;;;;4BAC5C;0CACR,8OAAC;;oCACE,uCAAuC,eAAe,QAAQ;wCAC7D,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,8OAAC;wBAAK,WAAU;;4BAAe,EAAE;4BAAe;;;;;;;oBAAS;kCACzD,8OAAC,0IAAK;wBACJ,WAAW,IAAA,yHAAE,EACX,uBACA,QAAQ,iBAAiB;kCAG1B,QAAQ,EAAE,qBAAqB,EAAE;;;;;;oBAEnC,QAAQ,qBACP,8OAAC;kCACC,cAAA,8OAAC;4BAAK,WAAU;;gCACb;gCACA,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAOjB", "debugId": null}}, {"offset": {"line": 8200, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport {\r\n  Ta<PERSON>,\r\n  TabsContent,\r\n  Ta<PERSON>List,\r\n  TabsTrigger,\r\n} from '@atlas/components/ui/tabs'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { InPlaneFlexuralCheckSchema } from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { Separator } from '@radix-ui/react-separator'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  inPlaneFlexuralCheckSchema?: InPlaneFlexuralCheckSchema\r\n}\r\n\r\nexport function InPlaneFlexuralCheckResultCard({\r\n  inPlaneFlexuralCheckSchema,\r\n}: Props) {\r\n  const { nonReinforcedSection, reinforcedSection } =\r\n    inPlaneFlexuralCheckSchema || {}\r\n\r\n  const t = useTranslations(\r\n    'components.calculations.maschi-murari.flexuralReinforcementCalculationResult.inPlaneFlexuralCheck',\r\n  )\r\n\r\n  const locale = useLocale()\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <h2>{t('nonReinforcedSection.title')}</h2>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('nonReinforcedSection.inPlaneBendingMoment.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {nonReinforcedSection?.inPlaneBendingMoment.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('nonReinforcedSection.neutralAxisCompressedEdgeDistance.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {nonReinforcedSection?.neutralAxisCompressedEdgeDistance.toLocaleString(\r\n              locale,\r\n              {\r\n                maximumFractionDigits: 5,\r\n              },\r\n            )}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('nonReinforcedSection.inPlaneFlexuralCapacity.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {nonReinforcedSection?.inPlaneFlexuralCapacity.toLocaleString(\r\n              locale,\r\n              {\r\n                maximumFractionDigits: 5,\r\n              },\r\n            )}{' '}\r\n          </span>\r\n        </div>\r\n        <span className=\"font-medium\">\r\n          {t('nonReinforcedSection.check.label')}:\r\n        </span>{' '}\r\n        <Badge\r\n          className={cn(\r\n            'text-base px-3 py-1',\r\n            nonReinforcedSection?.check ? 'bg-green-600' : 'bg-red-600',\r\n          )}\r\n        >\r\n          {nonReinforcedSection?.check\r\n            ? t('nonReinforcedSection.check.satisfied')\r\n            : t('nonReinforcedSection.check.notSatisfied')}\r\n        </Badge>\r\n        <Separator />\r\n        <h2>{t('reinforcedSection.title')}</h2>\r\n        <Tabs defaultValue=\"hypothesisOne\">\r\n          <TabsList>\r\n            <TabsTrigger value=\"hypothesisOne\">\r\n              {t('reinforcedSection.hypothesisOne.label')}\r\n            </TabsTrigger>\r\n            <TabsTrigger value=\"hypothesisTwo\">\r\n              {t('reinforcedSection.hypothesisTwo.label')}\r\n            </TabsTrigger>\r\n            <TabsTrigger value=\"hypothesisThree\">\r\n              {t('reinforcedSection.hypothesisThree.label')}\r\n            </TabsTrigger>\r\n          </TabsList>\r\n          <TabsContent value=\"hypothesisOne\">\r\n            {renderHypothesis(\r\n              reinforcedSection?.hypothesisOne,\r\n              'hypothesisOne',\r\n            )}\r\n          </TabsContent>\r\n          <TabsContent value=\"hypothesisTwo\">\r\n            {renderHypothesis(\r\n              reinforcedSection?.hypothesisTwo,\r\n              'hypothesisTwo',\r\n            )}\r\n          </TabsContent>\r\n          <TabsContent value=\"hypothesisThree\">\r\n            {renderHypothesis(\r\n              reinforcedSection?.hypothesisThree,\r\n              'hypothesisThree',\r\n            )}\r\n          </TabsContent>\r\n        </Tabs>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('reinforcedSection.momentCapacity.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {reinforcedSection?.momentCapacity.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <span className=\"font-medium\">\r\n          {t('reinforcedSection.check.label')}:\r\n        </span>{' '}\r\n        <Badge\r\n          className={cn(\r\n            'text-base px-3 py-1',\r\n            reinforcedSection?.check ? 'bg-green-600' : 'bg-red-600',\r\n          )}\r\n        >\r\n          {reinforcedSection?.check\r\n            ? t('reinforcedSection.check.satisfied')\r\n            : t('reinforcedSection.check.notSatisfied')}\r\n        </Badge>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n\r\n  function renderHypothesis(regionHypothesis: any, labelKey: string) {\r\n    if (!regionHypothesis) {\r\n      return null\r\n    }\r\n    return (\r\n      <>\r\n        <h2>{t(`reinforcedSection.${labelKey}.label`)}</h2>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'reinforcedSection.hypothesis.neutralAxisCompressedEdgeDistance.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {regionHypothesis.neutralAxisCompressedEdgeDistance?.toLocaleString(\r\n              locale,\r\n              {\r\n                maximumFractionDigits: 5,\r\n              },\r\n            )}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              `reinforcedSection.${labelKey}.reinforcementOrMasonryStrain.label`,\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {regionHypothesis.reinforcementOrMasonryStrain?.toLocaleString(\r\n              locale,\r\n              {\r\n                maximumFractionDigits: 5,\r\n              },\r\n            )}{' '}\r\n          </span>\r\n        </div>\r\n        <span className=\"font-medium\">\r\n          {t(`reinforcedSection.${labelKey}.check.label`)}:\r\n        </span>{' '}\r\n        <span className={cn('text-base px-3 py-1')}>\r\n          {regionHypothesis.check\r\n            ? t(`reinforcedSection.${labelKey}.check.satisfied`)\r\n            : t(`reinforcedSection.${labelKey}.check.notSatisfied`)}\r\n        </span>\r\n      </>\r\n    )\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AAMA;AAEA;AACA;AAAA;;;;;;;;AAMO,SAAS,+BAA+B,EAC7C,0BAA0B,EACpB;IACN,MAAM,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,GAC/C,8BAA8B,CAAC;IAEjC,MAAM,IAAI,IAAA,yNAAe,EACvB;IAGF,MAAM,SAAS,IAAA,+KAAS;IAExB,qBACE,8OAAC,wIAAI;;0BACH,8OAAC,8IAAU;0BACT,cAAA,8OAAC,6IAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,8OAAC,+IAAW;gBAAC,WAAU;;kCACrB,8OAAC;kCAAI,EAAE;;;;;;kCACP,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAmD;;;;;;;4BAChD;0CACR,8OAAC;;oCACE,sBAAsB,qBAAqB,eAAe,QAAQ;wCACjE,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAgE;;;;;;;4BAC7D;0CACR,8OAAC;;oCACE,sBAAsB,kCAAkC,eACvD,QACA;wCACE,uBAAuB;oCACzB;oCACC;;;;;;;;;;;;;kCAGP,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAsD;;;;;;;4BACnD;0CACR,8OAAC;;oCACE,sBAAsB,wBAAwB,eAC7C,QACA;wCACE,uBAAuB;oCACzB;oCACC;;;;;;;;;;;;;kCAGP,8OAAC;wBAAK,WAAU;;4BACb,EAAE;4BAAoC;;;;;;;oBACjC;kCACR,8OAAC,0IAAK;wBACJ,WAAW,IAAA,yHAAE,EACX,uBACA,sBAAsB,QAAQ,iBAAiB;kCAGhD,sBAAsB,QACnB,EAAE,0CACF,EAAE;;;;;;kCAER,8OAAC,kLAAS;;;;;kCACV,8OAAC;kCAAI,EAAE;;;;;;kCACP,8OAAC,wIAAI;wBAAC,cAAa;;0CACjB,8OAAC,4IAAQ;;kDACP,8OAAC,+IAAW;wCAAC,OAAM;kDAChB,EAAE;;;;;;kDAEL,8OAAC,+IAAW;wCAAC,OAAM;kDAChB,EAAE;;;;;;kDAEL,8OAAC,+IAAW;wCAAC,OAAM;kDAChB,EAAE;;;;;;;;;;;;0CAGP,8OAAC,+IAAW;gCAAC,OAAM;0CAChB,iBACC,mBAAmB,eACnB;;;;;;0CAGJ,8OAAC,+IAAW;gCAAC,OAAM;0CAChB,iBACC,mBAAmB,eACnB;;;;;;0CAGJ,8OAAC,+IAAW;gCAAC,OAAM;0CAChB,iBACC,mBAAmB,iBACnB;;;;;;;;;;;;kCAIN,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA0C;;;;;;;4BACvC;0CACR,8OAAC;;oCACE,mBAAmB,eAAe,eAAe,QAAQ;wCACxD,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,8OAAC;wBAAK,WAAU;;4BACb,EAAE;4BAAiC;;;;;;;oBAC9B;kCACR,8OAAC,0IAAK;wBACJ,WAAW,IAAA,yHAAE,EACX,uBACA,mBAAmB,QAAQ,iBAAiB;kCAG7C,mBAAmB,QAChB,EAAE,uCACF,EAAE;;;;;;;;;;;;;;;;;;;;IAMd,SAAS,iBAAiB,gBAAqB,EAAE,QAAgB;QAC/D,IAAI,CAAC,kBAAkB;YACrB,OAAO;QACT;QACA,qBACE;;8BACE,8OAAC;8BAAI,EAAE,CAAC,kBAAkB,EAAE,SAAS,MAAM,CAAC;;;;;;8BAC5C,8OAAC;;sCACC,8OAAC;4BAAK,WAAU;;gCACb,EACC;gCACA;;;;;;;wBAEI;sCACR,8OAAC;;gCACE,iBAAiB,iCAAiC,EAAE,eACnD,QACA;oCACE,uBAAuB;gCACzB;gCACC;;;;;;;;;;;;;8BAGP,8OAAC;;sCACC,8OAAC;4BAAK,WAAU;;gCACb,EACC,CAAC,kBAAkB,EAAE,SAAS,mCAAmC,CAAC;gCAClE;;;;;;;wBAEI;sCACR,8OAAC;;gCACE,iBAAiB,4BAA4B,EAAE,eAC9C,QACA;oCACE,uBAAuB;gCACzB;gCACC;;;;;;;;;;;;;8BAGP,8OAAC;oBAAK,WAAU;;wBACb,EAAE,CAAC,kBAAkB,EAAE,SAAS,YAAY,CAAC;wBAAE;;;;;;;gBAC1C;8BACR,8OAAC;oBAAK,WAAW,IAAA,yHAAE,EAAC;8BACjB,iBAAiB,KAAK,GACnB,EAAE,CAAC,kBAAkB,EAAE,SAAS,gBAAgB,CAAC,IACjD,EAAE,CAAC,kBAAkB,EAAE,SAAS,mBAAmB,CAAC;;;;;;;;IAIhE;AACF", "debugId": null}}, {"offset": {"line": 8612, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport {\r\n  Ta<PERSON>,\r\n  TabsContent,\r\n  Ta<PERSON>List,\r\n  TabsTrigger,\r\n} from '@atlas/components/ui/tabs'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { OutOfPlaneFlexuralCheckSchema } from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { Separator } from '@radix-ui/react-separator'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  outOfPlaneFlexuralCheckSchema?: OutOfPlaneFlexuralCheckSchema\r\n}\r\n\r\nexport function OutOfPlaneFlexuralCheckResultCard({\r\n  outOfPlaneFlexuralCheckSchema,\r\n}: Props) {\r\n  const { nonReinforcedSection, reinforcedSection } =\r\n    outOfPlaneFlexuralCheckSchema || {}\r\n\r\n  const t = useTranslations(\r\n    'components.calculations.maschi-murari.flexuralReinforcementCalculationResult.outOfPlaneFlexuralCheck',\r\n  )\r\n\r\n  const locale = useLocale()\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <h2>{t('nonReinforcedSection.title')}</h2>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('nonReinforcedSection.appliedDesignBendingMoment.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {nonReinforcedSection?.appliedDesignBendingMoment.toLocaleString(\r\n              locale,\r\n              {\r\n                maximumFractionDigits: 5,\r\n              },\r\n            )}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('nonReinforcedSection.neutralAxisCompressedEdgeDistance.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {nonReinforcedSection?.neutralAxisCompressedEdgeDistance.toLocaleString(\r\n              locale,\r\n              {\r\n                maximumFractionDigits: 5,\r\n              },\r\n            )}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('nonReinforcedSection.resultantCompressiveForceMasonry.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {nonReinforcedSection?.resultantCompressiveForceMasonry.toLocaleString(\r\n              locale,\r\n              {\r\n                maximumFractionDigits: 5,\r\n              },\r\n            )}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('nonReinforcedSection.designBendingMoment.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {nonReinforcedSection?.designBendingMoment.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <span className=\"font-medium\">\r\n          {t('nonReinforcedSection.check.label')}:\r\n        </span>{' '}\r\n        <Badge\r\n          className={cn(\r\n            'text-base px-3 py-1',\r\n            nonReinforcedSection?.check ? 'bg-green-600' : 'bg-red-600',\r\n          )}\r\n        >\r\n          {nonReinforcedSection?.check\r\n            ? t('nonReinforcedSection.check.satisfied')\r\n            : t('nonReinforcedSection.check.notSatisfied')}\r\n        </Badge>\r\n        <Separator />\r\n        <h2>{t('reinforcedSection.title')}</h2>\r\n        <Tabs defaultValue=\"regionHypothesisOne\">\r\n          <TabsList>\r\n            <TabsTrigger value=\"regionHypothesisOne\">\r\n              {t('reinforcedSection.regionHypothesisOne.label')}\r\n            </TabsTrigger>\r\n            <TabsTrigger value=\"regionHypothesisTwo\">\r\n              {t('reinforcedSection.regionHypothesisTwo.label')}\r\n            </TabsTrigger>\r\n          </TabsList>\r\n          <TabsContent value=\"regionHypothesisOne\">\r\n            {renderRegionHypothese(\r\n              reinforcedSection?.regionHypothesisOne,\r\n              'regionHypothesisOne',\r\n            )}\r\n          </TabsContent>\r\n          <TabsContent value=\"regionHypothesisTwo\">\r\n            {renderRegionHypothese(\r\n              reinforcedSection?.regionHypothesisTwo,\r\n              'regionHypothesisTwo',\r\n            )}\r\n          </TabsContent>\r\n        </Tabs>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('reinforcedSection.momentCapacity.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {reinforcedSection?.momentCapacity.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <span className=\"font-medium\">\r\n          {t('reinforcedSection.check.label')}:\r\n        </span>{' '}\r\n        <Badge\r\n          className={cn(\r\n            'text-base px-3 py-1',\r\n            reinforcedSection?.check ? 'bg-green-600' : 'bg-red-600',\r\n          )}\r\n        >\r\n          {reinforcedSection?.check\r\n            ? t('reinforcedSection.check.satisfied')\r\n            : t('reinforcedSection.check.notSatisfied')}\r\n        </Badge>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n\r\n  function renderRegionHypothese(regionHypothesis: any, labelKey: string) {\r\n    if (!regionHypothesis) {\r\n      return null\r\n    }\r\n    return (\r\n      <>\r\n        <h2>{t(`reinforcedSection.${labelKey}.label`)}</h2>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'reinforcedSection.regionHypothesis.neutralAxisCompressedEdgeDistance.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {regionHypothesis.neutralAxisCompressedEdgeDistance?.toLocaleString(\r\n              locale,\r\n              {\r\n                maximumFractionDigits: 5,\r\n              },\r\n            )}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              `reinforcedSection.${labelKey}.reinforcementOrMasonryStrain.label`,\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {regionHypothesis.reinforcementOrMasonryStrain?.toLocaleString(\r\n              locale,\r\n              {\r\n                maximumFractionDigits: 5,\r\n              },\r\n            )}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'reinforcedSection.regionHypothesis.resultantCompressiveForceMasonry.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {regionHypothesis.resultantCompressiveForceMasonry?.toLocaleString(\r\n              locale,\r\n              {\r\n                maximumFractionDigits: 5,\r\n              },\r\n            )}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'reinforcedSection.regionHypothesis.resultantTensileForceFrcm.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {regionHypothesis.resultantTensileForceFrcm?.toLocaleString(\r\n              locale,\r\n              {\r\n                maximumFractionDigits: 5,\r\n              },\r\n            )}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'reinforcedSection.regionHypothesis.designBendingMomentReinforcedSection.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {regionHypothesis.designBendingMomentReinforcedSection?.toLocaleString(\r\n              locale,\r\n              {\r\n                maximumFractionDigits: 5,\r\n              },\r\n            )}{' '}\r\n          </span>\r\n        </div>\r\n        <span className=\"font-medium\">\r\n          {t(`reinforcedSection.${labelKey}.check.label`)}:\r\n        </span>{' '}\r\n        <span className={cn('text-base px-3 py-1')}>\r\n          {regionHypothesis.check\r\n            ? t(`reinforcedSection.${labelKey}.check.satisfied`)\r\n            : t(`reinforcedSection.${labelKey}.check.notSatisfied`)}\r\n        </span>\r\n      </>\r\n    )\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AAMA;AAEA;AACA;AAAA;;;;;;;;AAMO,SAAS,kCAAkC,EAChD,6BAA6B,EACvB;IACN,MAAM,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,GAC/C,iCAAiC,CAAC;IAEpC,MAAM,IAAI,IAAA,yNAAe,EACvB;IAGF,MAAM,SAAS,IAAA,+KAAS;IAExB,qBACE,8OAAC,wIAAI;;0BACH,8OAAC,8IAAU;0BACT,cAAA,8OAAC,6IAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,8OAAC,+IAAW;gBAAC,WAAU;;kCACrB,8OAAC;kCAAI,EAAE;;;;;;kCACP,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAyD;;;;;;;4BACtD;0CACR,8OAAC;;oCACE,sBAAsB,2BAA2B,eAChD,QACA;wCACE,uBAAuB;oCACzB;oCACC;;;;;;;;;;;;;kCAGP,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAgE;;;;;;;4BAC7D;0CACR,8OAAC;;oCACE,sBAAsB,kCAAkC,eACvD,QACA;wCACE,uBAAuB;oCACzB;oCACC;;;;;;;;;;;;;kCAGP,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA+D;;;;;;;4BAC5D;0CACR,8OAAC;;oCACE,sBAAsB,iCAAiC,eACtD,QACA;wCACE,uBAAuB;oCACzB;oCACC;;;;;;;;;;;;;kCAGP,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAkD;;;;;;;4BAC/C;0CACR,8OAAC;;oCACE,sBAAsB,oBAAoB,eAAe,QAAQ;wCAChE,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,8OAAC;wBAAK,WAAU;;4BACb,EAAE;4BAAoC;;;;;;;oBACjC;kCACR,8OAAC,0IAAK;wBACJ,WAAW,IAAA,yHAAE,EACX,uBACA,sBAAsB,QAAQ,iBAAiB;kCAGhD,sBAAsB,QACnB,EAAE,0CACF,EAAE;;;;;;kCAER,8OAAC,kLAAS;;;;;kCACV,8OAAC;kCAAI,EAAE;;;;;;kCACP,8OAAC,wIAAI;wBAAC,cAAa;;0CACjB,8OAAC,4IAAQ;;kDACP,8OAAC,+IAAW;wCAAC,OAAM;kDAChB,EAAE;;;;;;kDAEL,8OAAC,+IAAW;wCAAC,OAAM;kDAChB,EAAE;;;;;;;;;;;;0CAGP,8OAAC,+IAAW;gCAAC,OAAM;0CAChB,sBACC,mBAAmB,qBACnB;;;;;;0CAGJ,8OAAC,+IAAW;gCAAC,OAAM;0CAChB,sBACC,mBAAmB,qBACnB;;;;;;;;;;;;kCAIN,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA0C;;;;;;;4BACvC;0CACR,8OAAC;;oCACE,mBAAmB,eAAe,eAAe,QAAQ;wCACxD,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,8OAAC;wBAAK,WAAU;;4BACb,EAAE;4BAAiC;;;;;;;oBAC9B;kCACR,8OAAC,0IAAK;wBACJ,WAAW,IAAA,yHAAE,EACX,uBACA,mBAAmB,QAAQ,iBAAiB;kCAG7C,mBAAmB,QAChB,EAAE,uCACF,EAAE;;;;;;;;;;;;;;;;;;;;IAMd,SAAS,sBAAsB,gBAAqB,EAAE,QAAgB;QACpE,IAAI,CAAC,kBAAkB;YACrB,OAAO;QACT;QACA,qBACE;;8BACE,8OAAC;8BAAI,EAAE,CAAC,kBAAkB,EAAE,SAAS,MAAM,CAAC;;;;;;8BAC5C,8OAAC;;sCACC,8OAAC;4BAAK,WAAU;;gCACb,EACC;gCACA;;;;;;;wBAEI;sCACR,8OAAC;;gCACE,iBAAiB,iCAAiC,EAAE,eACnD,QACA;oCACE,uBAAuB;gCACzB;gCACC;;;;;;;;;;;;;8BAGP,8OAAC;;sCACC,8OAAC;4BAAK,WAAU;;gCACb,EACC,CAAC,kBAAkB,EAAE,SAAS,mCAAmC,CAAC;gCAClE;;;;;;;wBAEI;sCACR,8OAAC;;gCACE,iBAAiB,4BAA4B,EAAE,eAC9C,QACA;oCACE,uBAAuB;gCACzB;gCACC;;;;;;;;;;;;;8BAGP,8OAAC;;sCACC,8OAAC;4BAAK,WAAU;;gCACb,EACC;gCACA;;;;;;;wBAEI;sCACR,8OAAC;;gCACE,iBAAiB,gCAAgC,EAAE,eAClD,QACA;oCACE,uBAAuB;gCACzB;gCACC;;;;;;;;;;;;;8BAGP,8OAAC;;sCACC,8OAAC;4BAAK,WAAU;;gCACb,EACC;gCACA;;;;;;;wBAEI;sCACR,8OAAC;;gCACE,iBAAiB,yBAAyB,EAAE,eAC3C,QACA;oCACE,uBAAuB;gCACzB;gCACC;;;;;;;;;;;;;8BAGP,8OAAC;;sCACC,8OAAC;4BAAK,WAAU;;gCACb,EACC;gCACA;;;;;;;wBAEI;sCACR,8OAAC;;gCACE,iBAAiB,oCAAoC,EAAE,eACtD,QACA;oCACE,uBAAuB;gCACzB;gCACC;;;;;;;;;;;;;8BAGP,8OAAC;oBAAK,WAAU;;wBACb,EAAE,CAAC,kBAAkB,EAAE,SAAS,YAAY,CAAC;wBAAE;;;;;;;gBAC1C;8BACR,8OAAC;oBAAK,WAAW,IAAA,yHAAE,EAAC;8BACjB,iBAAiB,KAAK,GACnB,EAAE,CAAC,kBAAkB,EAAE,SAAS,gBAAgB,CAAC,IACjD,EAAE,CAAC,kBAAkB,EAAE,SAAS,mBAAmB,CAAC;;;;;;;;IAIhE;AACF", "debugId": null}}, {"offset": {"line": 9136, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-shear-check-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { ShearCheckSchema } from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  shearCheckResults?: ShearCheckSchema\r\n}\r\n\r\nexport function ShearCheckResultCard({ shearCheckResults }: Props) {\r\n  const {\r\n    appliedDesignSpecificShearForce,\r\n    averageNormalStress,\r\n    resultantCompressiveForceMasonry,\r\n    resultantTensileForceFrcm,\r\n    check,\r\n  } = shearCheckResults || {}\r\n\r\n  const t = useTranslations(\r\n    'components.calculations.maschi-murari.flexuralReinforcementCalculationResult.shearCheck',\r\n  )\r\n\r\n  const locale = useLocale()\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('appliedDesignSpecificShearForce.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {appliedDesignSpecificShearForce?.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">{t('averageNormalStress.label')}:</span>{' '}\r\n          <span>\r\n            {averageNormalStress?.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('resultantCompressiveForceMasonry.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {resultantCompressiveForceMasonry?.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('resultantTensileForceFrcm.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {resultantTensileForceFrcm?.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <span className=\"font-medium\">{t('check.label')}:</span>{' '}\r\n        <Badge\r\n          className={cn(\r\n            'text-base px-3 py-1',\r\n            check ? 'bg-green-600' : 'bg-red-600',\r\n          )}\r\n        >\r\n          {check ? t('check.satisfied') : t('check.notSatisfied')}\r\n        </Badge>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AAEA;AAAA;;;;;;AAMO,SAAS,qBAAqB,EAAE,iBAAiB,EAAS;IAC/D,MAAM,EACJ,+BAA+B,EAC/B,mBAAmB,EACnB,gCAAgC,EAChC,yBAAyB,EACzB,KAAK,EACN,GAAG,qBAAqB,CAAC;IAE1B,MAAM,IAAI,IAAA,yNAAe,EACvB;IAGF,MAAM,SAAS,IAAA,+KAAS;IAExB,qBACE,8OAAC,wIAAI;;0BACH,8OAAC,8IAAU;0BACT,cAAA,8OAAC,6IAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,8OAAC,+IAAW;gBAAC,WAAU;;kCACrB,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAyC;;;;;;;4BACtC;0CACR,8OAAC;;oCACE,iCAAiC,eAAe,QAAQ;wCACvD,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAA6B;;;;;;;4BAAS;0CACvE,8OAAC;;oCACE,qBAAqB,eAAe,QAAQ;wCAC3C,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA0C;;;;;;;4BACvC;0CACR,8OAAC;;oCACE,kCAAkC,eAAe,QAAQ;wCACxD,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAmC;;;;;;;4BAChC;0CACR,8OAAC;;oCACE,2BAA2B,eAAe,QAAQ;wCACjD,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,8OAAC;wBAAK,WAAU;;4BAAe,EAAE;4BAAe;;;;;;;oBAAS;kCACzD,8OAAC,0IAAK;wBACJ,WAAW,IAAA,yHAAE,EACX,uBACA,QAAQ,iBAAiB;kCAG1B,QAAQ,EAAE,qBAAqB,EAAE;;;;;;;;;;;;;;;;;;AAK5C", "debugId": null}}, {"offset": {"line": 9338, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation-result.tsx"], "sourcesContent": ["import {\r\n  <PERSON>,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON>Content,\r\n  <PERSON><PERSON>List,\r\n  TabsTrigger,\r\n} from '@atlas/components/ui/tabs'\r\nimport type { MaschiMurariFlexuralReinforcementCalculationResultSchema } from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\nimport { ExtremityDetachementCheck } from './maschi-murari-extremity-detachement-check-result-card'\r\nimport { InPlaneFlexuralCheckResultCard } from './maschi-murari-flexural-in-plane-result-card'\r\nimport { OutOfPlaneFlexuralCheckResultCard } from './maschi-murari-flexural-out-of-plane-result-card'\r\nimport { ShearCheckResultCard } from './maschi-murari-shear-check-result-card'\r\n\r\ntype Props = {\r\n  flexuralReinforcementCalculationResult: MaschiMurariFlexuralReinforcementCalculationResultSchema\r\n}\r\n\r\nexport function MaschiMurariReinforcementFlexuralCalculationResult({\r\n  flexuralReinforcementCalculationResult,\r\n}: Props) {\r\n  const t = useTranslations(\r\n    'components.calculations.maschi-murari.flexuralReinforcementCalculationResult',\r\n  )\r\n  const _locale = useLocale()\r\n\r\n  return (\r\n    <>\r\n      <h3 className=\"text-lg font-medium py-4\">{t('title')}</h3>\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle>{t('title')}</CardTitle>\r\n        </CardHeader>\r\n        <CardContent className=\"space-y-4\">\r\n          <Tabs defaultValue=\"inPlaneFlexuralCheck\">\r\n            <TabsList>\r\n              <TabsTrigger value=\"inPlaneFlexuralCheck\">\r\n                {t('inPlaneFlexuralCheck.label')}\r\n              </TabsTrigger>\r\n              <TabsTrigger value=\"outOfPlaneFlexuralCheck\">\r\n                {t('outOfPlaneFlexuralCheck.label')}\r\n              </TabsTrigger>\r\n              <TabsTrigger value=\"shearCheck\">\r\n                {t('shearCheck.label')}\r\n              </TabsTrigger>\r\n              <TabsTrigger value=\"extremityDetachementCheck\">\r\n                {t('extremityDetachementCheck.label')}\r\n              </TabsTrigger>\r\n            </TabsList>\r\n            <TabsContent value=\"inPlaneFlexuralCheck\">\r\n              <InPlaneFlexuralCheckResultCard\r\n                inPlaneFlexuralCheckSchema={\r\n                  flexuralReinforcementCalculationResult?.inPlaneFlexuralCheck\r\n                }\r\n              />\r\n            </TabsContent>\r\n            <TabsContent value=\"outOfPlaneFlexuralCheck\">\r\n              <OutOfPlaneFlexuralCheckResultCard\r\n                outOfPlaneFlexuralCheckSchema={\r\n                  flexuralReinforcementCalculationResult?.outOfPlaneFlexuralCheck\r\n                }\r\n              />\r\n            </TabsContent>\r\n            <TabsContent value=\"shearCheck\">\r\n              <ShearCheckResultCard\r\n                shearCheckResults={\r\n                  flexuralReinforcementCalculationResult?.shearCheck\r\n                }\r\n              />\r\n            </TabsContent>\r\n            <TabsContent value=\"extremityDetachementCheck\">\r\n              <ExtremityDetachementCheck\r\n                extremityDetachementCheck={\r\n                  flexuralReinforcementCalculationResult?.extremityDetachementCheck\r\n                }\r\n              />\r\n            </TabsContent>\r\n          </Tabs>\r\n        </CardContent>\r\n      </Card>\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAMA;AAOA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;AAMO,SAAS,mDAAmD,EACjE,sCAAsC,EAChC;IACN,MAAM,IAAI,IAAA,yNAAe,EACvB;IAEF,MAAM,UAAU,IAAA,+KAAS;IAEzB,qBACE;;0BACE,8OAAC;gBAAG,WAAU;0BAA4B,EAAE;;;;;;0BAC5C,8OAAC,wIAAI;;kCACH,8OAAC,8IAAU;kCACT,cAAA,8OAAC,6IAAS;sCAAE,EAAE;;;;;;;;;;;kCAEhB,8OAAC,+IAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC,wIAAI;4BAAC,cAAa;;8CACjB,8OAAC,4IAAQ;;sDACP,8OAAC,+IAAW;4CAAC,OAAM;sDAChB,EAAE;;;;;;sDAEL,8OAAC,+IAAW;4CAAC,OAAM;sDAChB,EAAE;;;;;;sDAEL,8OAAC,+IAAW;4CAAC,OAAM;sDAChB,EAAE;;;;;;sDAEL,8OAAC,+IAAW;4CAAC,OAAM;sDAChB,EAAE;;;;;;;;;;;;8CAGP,8OAAC,+IAAW;oCAAC,OAAM;8CACjB,cAAA,8OAAC,0UAA8B;wCAC7B,4BACE,wCAAwC;;;;;;;;;;;8CAI9C,8OAAC,+IAAW;oCAAC,OAAM;8CACjB,cAAA,8OAAC,oVAAiC;wCAChC,+BACE,wCAAwC;;;;;;;;;;;8CAI9C,8OAAC,+IAAW;oCAAC,OAAM;8CACjB,cAAA,8OAAC,uTAAoB;wCACnB,mBACE,wCAAwC;;;;;;;;;;;8CAI9C,8OAAC,+IAAW;oCAAC,OAAM;8CACjB,cAAA,8OAAC,+UAAyB;wCACxB,2BACE,wCAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1D", "debugId": null}}, {"offset": {"line": 9512, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx"], "sourcesContent": ["import { CustomProductSection } from '@atlas/components/common/atlas/product/custom-product-section'\r\nimport { ProductSummaryCard } from '@atlas/components/common/atlas/product/product-summary-card'\r\nimport { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport {\r\n  FACING_MATERIAL,\r\n  MODULE_REINFORCEMENT_ARRANGEMENT,\r\n  MODULE_REINFORCEMENT_FAILURE_MODE,\r\n  REINFORCEMENT_ARRANGEMENT,\r\n  REINFORCEMENT_FAILURE_MODE,\r\n} from '@atlas/constants/module'\r\nimport type { ModuleWithParamsMaschiMurari } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useModuleCalculation } from '@atlas/lib/mutation/modules/use-module-calculation'\r\nimport { useProductsByCategory } from '@atlas/lib/query/products/use-products-by-category'\r\nimport {\r\n  type MaschiMurariFlexuralReinforcementExecutionSchemaInput,\r\n  maschiMurariFlexuralReinforcementExecutionSchema,\r\n} from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useMemo } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\nimport { MaschiMurariReinforcementFlexuralCalculationResult } from './maschi-murari-reinforcement-flexural-calculation-result'\r\n\r\ntype Props = {\r\n  module: ModuleWithParamsMaschiMurari\r\n  session: Session\r\n  projectId: Project['id']\r\n}\r\n\r\n// N.B: this function is temporary, until we remove the quotes in future seed\r\nfunction getCleanedUpValue(\r\n  recordToClean: Record<string, number> | undefined,\r\n  nature: string,\r\n) {\r\n  // nature should be 'BRICK', 'TUFF', or 'STONE'\r\n  if (!recordToClean) {\r\n    return undefined\r\n  }\r\n  const key = `\"${nature}\"` // matches the API key format\r\n  return recordToClean[key]\r\n}\r\n\r\nexport const MaschiMurariFlexuralCalculation = ({\r\n  session,\r\n  module,\r\n  projectId,\r\n}: Props) => {\r\n  const t = useTranslations('forms.calculations.maschi-murari.flexural')\r\n  const tAction = useTranslations('actions.calculations.maschi-murari')\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  const {\r\n    params,\r\n    flexuralReinforcementVerifyExecutionInput,\r\n    flexuralReinforcementCalculationResult,\r\n  } = module\r\n\r\n  const form = useForm<MaschiMurariFlexuralReinforcementExecutionSchemaInput>({\r\n    resolver: zodResolver(maschiMurariFlexuralReinforcementExecutionSchema),\r\n    defaultValues: {\r\n      calculationType: 'FLEXURAL_VERIFY',\r\n      input: {\r\n        product: {\r\n          id: flexuralReinforcementVerifyExecutionInput?.product.id,\r\n          name: flexuralReinforcementVerifyExecutionInput?.product.name,\r\n          sourceType:\r\n            flexuralReinforcementVerifyExecutionInput?.product.id === 'custom'\r\n              ? 'CUSTOM'\r\n              : 'DATABASE',\r\n        },\r\n        sectionFailureMode:\r\n          flexuralReinforcementVerifyExecutionInput?.sectionFailureMode ??\r\n          REINFORCEMENT_FAILURE_MODE.DISTACCO_DI_ESTREMITA,\r\n        designReinforcementStress:\r\n          flexuralReinforcementVerifyExecutionInput?.designReinforcementStress ??\r\n          0,\r\n        designReinforcementStrain:\r\n          flexuralReinforcementVerifyExecutionInput?.designReinforcementStrain ??\r\n          0,\r\n        reinforcedArrangement:\r\n          flexuralReinforcementVerifyExecutionInput?.reinforcedArrangement ??\r\n          REINFORCEMENT_ARRANGEMENT.CONTINUE,\r\n        singleStripWidth:\r\n          flexuralReinforcementVerifyExecutionInput?.singleStripWidth ?? 0,\r\n        stripSpacing:\r\n          flexuralReinforcementVerifyExecutionInput?.stripSpacing ?? 0,\r\n        reinforcementTotalWidthAlongLength:\r\n          flexuralReinforcementVerifyExecutionInput?.reinforcementTotalWidthAlongLength ??\r\n          0,\r\n        compressedEdgeReinforcementFiberDistance:\r\n          flexuralReinforcementVerifyExecutionInput?.compressedEdgeReinforcementFiberDistance ??\r\n          0,\r\n        layersNumber:\r\n          flexuralReinforcementVerifyExecutionInput?.layersNumber ?? 1,\r\n        reinforcedSidesNumber:\r\n          flexuralReinforcementVerifyExecutionInput?.reinforcedSidesNumber ?? 0,\r\n        totalEquivalentThickness:\r\n          flexuralReinforcementVerifyExecutionInput?.totalEquivalentThickness ??\r\n          0,\r\n        firstCoefficient:\r\n          flexuralReinforcementVerifyExecutionInput?.firstCoefficient ?? 0,\r\n        secondCoefficient:\r\n          flexuralReinforcementVerifyExecutionInput?.secondCoefficient ?? 0,\r\n\r\n        // Only for out-of-plane bending\r\n        outOfPlanUnitWidthPanel:\r\n          flexuralReinforcementVerifyExecutionInput?.outOfPlanUnitWidthPanel ??\r\n          1000,\r\n        outOfPlanAppliedDesignAxialStress:\r\n          flexuralReinforcementVerifyExecutionInput?.outOfPlanAppliedDesignAxialStress ??\r\n          0,\r\n        outOfPlaneReinforcementFailureMode:\r\n          flexuralReinforcementVerifyExecutionInput?.outOfPlaneReinforcementFailureMode ??\r\n          REINFORCEMENT_FAILURE_MODE.DISTACCO_DI_ESTREMITA,\r\n        outOfPlaneDesignReinforcementStrain:\r\n          flexuralReinforcementVerifyExecutionInput?.outOfPlaneDesignReinforcementStrain ??\r\n          0,\r\n        outOfPlaneDesignReinforcementStress:\r\n          flexuralReinforcementVerifyExecutionInput?.outOfPlaneDesignReinforcementStress ??\r\n          0,\r\n\r\n        outOfPlaneReinforcementTotalWidthAlongLength:\r\n          flexuralReinforcementVerifyExecutionInput?.outOfPlaneReinforcementTotalWidthAlongLength ??\r\n          0,\r\n        outOfPlaneResistingArea:\r\n          flexuralReinforcementVerifyExecutionInput?.outOfPlaneResistingArea ??\r\n          0,\r\n      },\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useModuleCalculation(session.accessToken, {\r\n    onSuccess: () => {\r\n      toast.success(tAction('calculate.success'))\r\n    },\r\n    onError: error => {\r\n      console.log('ERROR  ', error)\r\n      toast.error(tAction('calculate.failure', { error: error.message }))\r\n    },\r\n  })\r\n\r\n  const handleFormSubmit = (\r\n    body: MaschiMurariFlexuralReinforcementExecutionSchemaInput,\r\n  ) => {\r\n    mutate({ projectId, moduleId: module.id, body })\r\n  }\r\n\r\n  const {\r\n    data: products,\r\n    isError: errorGettingProducts,\r\n    isLoading: isLoadingProducts,\r\n  } = useProductsByCategory(session, 'MASCHI_MURARI', 0, 100)\r\n\r\n  const productsOptions = [\r\n    ...(products?.content.map(m => ({\r\n      value: m.id,\r\n      label: m.name ?? t('product.unnamed'),\r\n    })) ?? []),\r\n  ]\r\n\r\n  const [productId] = form.watch(['input.product.id'])\r\n\r\n  const selectedProduct = useMemo(\r\n    () => products?.content.find(p => p.id === productId),\r\n    [productId, products],\r\n  )\r\n\r\n  const conventionalStrainLimit = selectedProduct?.conventionalStrainLimit\r\n  const conversionFactor = params?.materialProperties?.conversionFactor ?? 0\r\n  const sectionFailureModeValue =\r\n    form.watch('input.sectionFailureMode') ??\r\n    REINFORCEMENT_FAILURE_MODE.DISTACCO_DI_ESTREMITA\r\n\r\n  const structuralElementsNatureValue =\r\n    params?.materialProperties?.structuralElementsNature ??\r\n    FACING_MATERIAL.BRICK\r\n\r\n  const _panelGeometryHeight = params?.panelGeometry?.height ?? 0\r\n  const panelGeometryWidth = params?.panelGeometry?.width ?? 0\r\n  const singleStripWidthValue = form.watch('input.singleStripWidth') ?? 0\r\n  const stripSpacingValue = form.watch('input.stripSpacing') ?? 0\r\n\r\n  const conversionFactorValue =\r\n    params?.materialProperties?.conversionFactor ?? 0\r\n  const ultimateCompressiveStrainLinearBehaviourValue =\r\n    params?.materialProperties?.ultimateCompressiveStrainLinearBehaviour ?? 0\r\n  const ultimateCompressiveStrainValue =\r\n    params?.materialProperties?.ultimateCompressiveStrain ?? 0\r\n\r\n  const appliedNormalStressValue = params?.actions?.appliedNormalStress ?? 0\r\n\r\n  const reinforcedArrangementValue =\r\n    form.watch('input.reinforcedArrangement') ??\r\n    REINFORCEMENT_ARRANGEMENT.CONTINUE\r\n\r\n  const reinforcedSidesNumberValue =\r\n    form.watch('input.reinforcedSidesNumber') ?? 0\r\n\r\n  useEffect(() => {\r\n    form.setValue('input.layersNumber', 1)\r\n    form.setValue('input.outOfPlanUnitWidthPanel', 1000)\r\n    form.setValue(\r\n      'input.outOfPlaneReinforcementFailureMode',\r\n      REINFORCEMENT_FAILURE_MODE.DISTACCO_INTERMEDIO,\r\n    )\r\n    // D61 = designReinforcementStrain =\r\n    // =IF(D42=\"Distacco intermedio\",(1.5*D41*D26/1.5),(D41*D26/1.5))\r\n    // D42 = input.reinforcementFailureMode\r\n    // D41 = product.conventionalStrainLimit\r\n    // D26 = materialProperties.conversionFactor\r\n    const conventionalStrainLimitValue =\r\n      getCleanedUpValue(\r\n        conventionalStrainLimit,\r\n        structuralElementsNatureValue,\r\n      ) ?? 0\r\n\r\n    const designReinforcementStrain =\r\n      sectionFailureModeValue === REINFORCEMENT_FAILURE_MODE.DISTACCO_INTERMEDIO\r\n        ? (1.5 * conventionalStrainLimitValue * conversionFactor) / 1.5\r\n        : (conventionalStrainLimitValue * conversionFactor) / 1.5\r\n\r\n    form.setValue('input.designReinforcementStrain', designReinforcementStrain)\r\n\r\n    // D62 = designReinforcementStress = =D43*D38\r\n    // D43 = designReinforcementStrain\r\n    // D38 = product.elasticModulus\r\n    const elasticModulus = selectedProduct?.elasticModulus ?? 0\r\n    const designReinforcementStress = designReinforcementStrain * elasticModulus\r\n    form.setValue('input.designReinforcementStress', designReinforcementStress)\r\n\r\n    // D66 reinforcementTotalWidthAlongLength =\r\n    //  =IF(D63=\"Continuo\",D9,D64*(D9/D65))\r\n    // D63 = input.reinforcedArrangement\r\n    // D64 = input.singleStripWidth\r\n    // D65 = input.stripSpacing\r\n    // D9 = width\r\n    const reinforcementTotalWidthAlongLength =\r\n      reinforcedArrangementValue === REINFORCEMENT_ARRANGEMENT.CONTINUE\r\n        ? panelGeometryWidth\r\n        : singleStripWidthValue * (panelGeometryWidth / stripSpacingValue)\r\n\r\n    form.setValue(\r\n      'input.reinforcementTotalWidthAlongLength',\r\n      reinforcementTotalWidthAlongLength,\r\n    )\r\n\r\n    // totalEquivalentThickness\r\n    // =IF(D63=\"Continuo\",D69*D57,D69*D57*D64/D65)\r\n    // D69 = reinforcedSidesNumber\r\n    // D64 = input.singleStripWidth\r\n    // D65 = input.stripSpacing\r\n    // D57 = thickness of the product\r\n    const thicknessOfTheProduct = selectedProduct?.thickness ?? 0\r\n    const totalEquivalentThickness =\r\n      reinforcedArrangementValue === REINFORCEMENT_ARRANGEMENT.CONTINUE\r\n        ? reinforcedSidesNumberValue * singleStripWidthValue\r\n        : (reinforcedSidesNumberValue *\r\n            thicknessOfTheProduct *\r\n            singleStripWidthValue) /\r\n          stripSpacingValue\r\n    form.setValue('input.totalEquivalentThickness', totalEquivalentThickness)\r\n\r\n    // first Coefficient = D24/D25\r\n    // D24: materialProperties.ultimateCompressiveStrainLinearBehaviour\r\n    // D25: materialProperties.ultimateCompressiveStrain\r\n    const firstCoefficient =\r\n      ultimateCompressiveStrainLinearBehaviourValue /\r\n      ultimateCompressiveStrainValue\r\n    form.setValue('input.firstCoefficient', firstCoefficient)\r\n\r\n    // second Coefficient = D24/D61\r\n    // D61 = designReinforcementStrain\r\n    const secondCoefficient =\r\n      ultimateCompressiveStrainLinearBehaviourValue / designReinforcementStrain\r\n    form.setValue('input.secondCoefficient', secondCoefficient)\r\n\r\n    // OUT OF PLANE BENDING\r\n    // D79 = outOfPlanAppliedDesignAxialStress=D29\r\n    // D29 = actions.appliedNormalStress\r\n    form.setValue(\r\n      'input.outOfPlanAppliedDesignAxialStress',\r\n      appliedNormalStressValue,\r\n    )\r\n\r\n    // D81 = outOfPlaneDesignReinforcementStrain\r\n    // =  =(1.5*D59*D26/1.5)\r\n    // D59 = product.conventionalStrainLimit\r\n    // D26 = materialProperties.conversionFactor\r\n    const outOfPlaneDesignReinforcementStrain =\r\n      (1.5 * conventionalStrainLimitValue * conversionFactorValue) / 1.5\r\n    form.setValue(\r\n      'input.outOfPlaneDesignReinforcementStrain',\r\n      outOfPlaneDesignReinforcementStrain,\r\n    )\r\n\r\n    // D82= outOfPlaneDesignReinforcementStrain=D81*D56\r\n    // D56 = product.elasticModulus\r\n    form.setValue(\r\n      'input.outOfPlaneDesignReinforcementStress',\r\n      outOfPlaneDesignReinforcementStrain * elasticModulus,\r\n    )\r\n\r\n    // D83 = outOfPlaneReinforcementTotalWidthAlongLength\r\n    // =IF(D63=\"Continuo\",D78,D64*(D78/D65))\r\n    // D78 = width = outOfPlanUnitWidthPanel = 1000\r\n    // D63 = input.reinforcedArrangement\r\n    // D64 = input.singleStripWidth\r\n    // D65 = input.stripSpacing\r\n    const outOfPlaneReinforcementTotalWidthAlongLength =\r\n      reinforcedArrangementValue === REINFORCEMENT_ARRANGEMENT.CONTINUE\r\n        ? 1000\r\n        : singleStripWidthValue * (1000 / stripSpacingValue)\r\n    form.setValue(\r\n      'input.outOfPlaneReinforcementTotalWidthAlongLength',\r\n      outOfPlaneReinforcementTotalWidthAlongLength,\r\n    )\r\n\r\n    // D85 = outOfPlaneResistingArea\r\n    // =D57*D68*D83\r\n    // D57 = thickness of the product\r\n    // D68 = reinforcedLayersNumber = 1\r\n    form.setValue(\r\n      'input.outOfPlaneResistingArea',\r\n      thicknessOfTheProduct * 1 * outOfPlaneReinforcementTotalWidthAlongLength,\r\n    )\r\n\r\n    if (!productId) {\r\n      return\r\n    }\r\n    if (productId === 'custom') {\r\n      form.setValue('input.product.sourceType', 'CUSTOM')\r\n    }\r\n\r\n    if (selectedProduct) {\r\n      form.setValue('input.product', {\r\n        ...selectedProduct,\r\n        sourceType: 'DATABASE',\r\n      })\r\n    }\r\n  }, [\r\n    form,\r\n    productId,\r\n    selectedProduct,\r\n    conversionFactor,\r\n    sectionFailureModeValue,\r\n    conventionalStrainLimit,\r\n    structuralElementsNatureValue,\r\n    panelGeometryWidth,\r\n    singleStripWidthValue,\r\n    stripSpacingValue,\r\n    reinforcedArrangementValue,\r\n    reinforcedSidesNumberValue,\r\n    ultimateCompressiveStrainLinearBehaviourValue,\r\n    ultimateCompressiveStrainValue,\r\n    appliedNormalStressValue,\r\n    conversionFactorValue,\r\n  ])\r\n\r\n  return (\r\n    <div className=\"flex flex-col justify-center gap-4\">\r\n      <Form {...form}>\r\n        <form\r\n          className=\"space-y-4 rounded-md border p-4\"\r\n          onSubmit={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          <h1 className=\"text-3xl font-bold\">{t('heading')}</h1>\r\n          <Image\r\n            src=\"/assets/masonry_frcm/column/FRCM_COLONNE MURATURA.jpg\"\r\n            alt=\"flexural verify\"\r\n            height={250}\r\n            width={500}\r\n            className=\"mx-auto rounded-md object-contain\"\r\n            priority\r\n          />\r\n          <h1 className=\"text-xl font-bold\">{t('inPlane.title')}</h1>\r\n          <SelectFormInput\r\n            control={form.control}\r\n            name=\"input.product.id\"\r\n            options={productsOptions}\r\n            t={t}\r\n            loading={isLoadingProducts}\r\n            requestError={errorGettingProducts}\r\n            errorMessage={t('products.error')}\r\n          />\r\n          {productId === 'custom' && <CustomProductSection />}\r\n          {selectedProduct && <ProductSummaryCard product={selectedProduct} />}\r\n          <Separator />\r\n          <SelectFormFixedInput\r\n            control={form.control}\r\n            name=\"input.sectionFailureMode\"\r\n            options={MODULE_REINFORCEMENT_FAILURE_MODE}\r\n            optionLabelFn={p => t(`input.sectionFailureMode.${p}`)}\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.designReinforcementStrain\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.designReinforcementStress\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <SelectFormFixedInput\r\n            control={form.control}\r\n            name=\"input.reinforcedArrangement\"\r\n            options={MODULE_REINFORCEMENT_ARRANGEMENT}\r\n            optionLabelFn={p => t(`input.reinforcedArrangement.${p}`)}\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.singleStripWidth\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.stripSpacing\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.reinforcementTotalWidthAlongLength\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.compressedEdgeReinforcementFiberDistance\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.layersNumber\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.reinforcedSidesNumber\"\r\n            t={t}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.totalEquivalentThickness\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.firstCoefficient\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.secondCoefficient\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <Separator />\r\n          <h1 className=\"text-xl font-bold\">{t('outOfPlane.title')}</h1>\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.outOfPlanUnitWidthPanel\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.outOfPlanAppliedDesignAxialStress\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <SelectFormFixedInput\r\n            control={form.control}\r\n            name=\"input.outOfPlaneReinforcementFailureMode\"\r\n            options={MODULE_REINFORCEMENT_FAILURE_MODE}\r\n            optionLabelFn={p =>\r\n              t(`input.outOfPlaneReinforcementFailureMode.${p}`)\r\n            }\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.outOfPlaneDesignReinforcementStrain\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.outOfPlaneDesignReinforcementStress\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.outOfPlaneReinforcementTotalWidthAlongLength\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.outOfPlaneResistingArea\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full sm:w-auto\"\r\n            disabled={isPending}\r\n          >\r\n            {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n            {tCommon('calculate')}\r\n          </Button>\r\n        </form>\r\n      </Form>\r\n      {flexuralReinforcementCalculationResult && (\r\n        <MaschiMurariReinforcementFlexuralCalculationResult\r\n          flexuralReinforcementCalculationResult={\r\n            flexuralReinforcementCalculationResult\r\n          }\r\n        />\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AASA;AACA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;AAQA,6EAA6E;AAC7E,SAAS,kBACP,aAAiD,EACjD,MAAc;IAEd,+CAA+C;IAC/C,IAAI,CAAC,eAAe;QAClB,OAAO;IACT;IACA,MAAM,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,6BAA6B;;IACvD,OAAO,aAAa,CAAC,IAAI;AAC3B;AAEO,MAAM,kCAAkC,CAAC,EAC9C,OAAO,EACP,MAAM,EACN,SAAS,EACH;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAEhC,MAAM,EACJ,MAAM,EACN,yCAAyC,EACzC,sCAAsC,EACvC,GAAG;IAEJ,MAAM,OAAO,IAAA,yKAAO,EAAwD;QAC1E,UAAU,IAAA,6KAAW,EAAC,kNAAgD;QACtE,eAAe;YACb,iBAAiB;YACjB,OAAO;gBACL,SAAS;oBACP,IAAI,2CAA2C,QAAQ;oBACvD,MAAM,2CAA2C,QAAQ;oBACzD,YACE,2CAA2C,QAAQ,OAAO,WACtD,WACA;gBACR;gBACA,oBACE,2CAA2C,sBAC3C,yJAA0B,CAAC,qBAAqB;gBAClD,2BACE,2CAA2C,6BAC3C;gBACF,2BACE,2CAA2C,6BAC3C;gBACF,uBACE,2CAA2C,yBAC3C,wJAAyB,CAAC,QAAQ;gBACpC,kBACE,2CAA2C,oBAAoB;gBACjE,cACE,2CAA2C,gBAAgB;gBAC7D,oCACE,2CAA2C,sCAC3C;gBACF,0CACE,2CAA2C,4CAC3C;gBACF,cACE,2CAA2C,gBAAgB;gBAC7D,uBACE,2CAA2C,yBAAyB;gBACtE,0BACE,2CAA2C,4BAC3C;gBACF,kBACE,2CAA2C,oBAAoB;gBACjE,mBACE,2CAA2C,qBAAqB;gBAElE,gCAAgC;gBAChC,yBACE,2CAA2C,2BAC3C;gBACF,mCACE,2CAA2C,qCAC3C;gBACF,oCACE,2CAA2C,sCAC3C,yJAA0B,CAAC,qBAAqB;gBAClD,qCACE,2CAA2C,uCAC3C;gBACF,qCACE,2CAA2C,uCAC3C;gBAEF,8CACE,2CAA2C,gDAC3C;gBACF,yBACE,2CAA2C,2BAC3C;YACJ;QACF;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,0LAAoB,EAAC,QAAQ,WAAW,EAAE;QACtE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;QACxB;QACA,SAAS,CAAA;YACP,QAAQ,GAAG,CAAC,WAAW;YACvB,iJAAK,CAAC,KAAK,CAAC,QAAQ,qBAAqB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAClE;IACF;IAEA,MAAM,mBAAmB,CACvB;QAEA,OAAO;YAAE;YAAW,UAAU,OAAO,EAAE;YAAE;QAAK;IAChD;IAEA,MAAM,EACJ,MAAM,QAAQ,EACd,SAAS,oBAAoB,EAC7B,WAAW,iBAAiB,EAC7B,GAAG,IAAA,8LAAqB,EAAC,SAAS,iBAAiB,GAAG;IAEvD,MAAM,kBAAkB;WAClB,UAAU,QAAQ,IAAI,CAAA,IAAK,CAAC;gBAC9B,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,IAAI,IAAI,EAAE;YACrB,CAAC,MAAM,EAAE;KACV;IAED,MAAM,CAAC,UAAU,GAAG,KAAK,KAAK,CAAC;QAAC;KAAmB;IAEnD,MAAM,kBAAkB,IAAA,gNAAO,EAC7B,IAAM,UAAU,QAAQ,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK,YAC3C;QAAC;QAAW;KAAS;IAGvB,MAAM,0BAA0B,iBAAiB;IACjD,MAAM,mBAAmB,QAAQ,oBAAoB,oBAAoB;IACzE,MAAM,0BACJ,KAAK,KAAK,CAAC,+BACX,yJAA0B,CAAC,qBAAqB;IAElD,MAAM,gCACJ,QAAQ,oBAAoB,4BAC5B,8IAAe,CAAC,KAAK;IAEvB,MAAM,uBAAuB,QAAQ,eAAe,UAAU;IAC9D,MAAM,qBAAqB,QAAQ,eAAe,SAAS;IAC3D,MAAM,wBAAwB,KAAK,KAAK,CAAC,6BAA6B;IACtE,MAAM,oBAAoB,KAAK,KAAK,CAAC,yBAAyB;IAE9D,MAAM,wBACJ,QAAQ,oBAAoB,oBAAoB;IAClD,MAAM,gDACJ,QAAQ,oBAAoB,4CAA4C;IAC1E,MAAM,iCACJ,QAAQ,oBAAoB,6BAA6B;IAE3D,MAAM,2BAA2B,QAAQ,SAAS,uBAAuB;IAEzE,MAAM,6BACJ,KAAK,KAAK,CAAC,kCACX,wJAAyB,CAAC,QAAQ;IAEpC,MAAM,6BACJ,KAAK,KAAK,CAAC,kCAAkC;IAE/C,IAAA,kNAAS,EAAC;QACR,KAAK,QAAQ,CAAC,sBAAsB;QACpC,KAAK,QAAQ,CAAC,iCAAiC;QAC/C,KAAK,QAAQ,CACX,4CACA,yJAA0B,CAAC,mBAAmB;QAEhD,oCAAoC;QACpC,iEAAiE;QACjE,uCAAuC;QACvC,wCAAwC;QACxC,4CAA4C;QAC5C,MAAM,+BACJ,kBACE,yBACA,kCACG;QAEP,MAAM,4BACJ,4BAA4B,yJAA0B,CAAC,mBAAmB,GACtE,AAAC,MAAM,+BAA+B,mBAAoB,MAC1D,AAAC,+BAA+B,mBAAoB;QAE1D,KAAK,QAAQ,CAAC,mCAAmC;QAEjD,6CAA6C;QAC7C,kCAAkC;QAClC,+BAA+B;QAC/B,MAAM,iBAAiB,iBAAiB,kBAAkB;QAC1D,MAAM,4BAA4B,4BAA4B;QAC9D,KAAK,QAAQ,CAAC,mCAAmC;QAEjD,2CAA2C;QAC3C,uCAAuC;QACvC,oCAAoC;QACpC,+BAA+B;QAC/B,2BAA2B;QAC3B,aAAa;QACb,MAAM,qCACJ,+BAA+B,wJAAyB,CAAC,QAAQ,GAC7D,qBACA,wBAAwB,CAAC,qBAAqB,iBAAiB;QAErE,KAAK,QAAQ,CACX,4CACA;QAGF,2BAA2B;QAC3B,8CAA8C;QAC9C,8BAA8B;QAC9B,+BAA+B;QAC/B,2BAA2B;QAC3B,iCAAiC;QACjC,MAAM,wBAAwB,iBAAiB,aAAa;QAC5D,MAAM,2BACJ,+BAA+B,wJAAyB,CAAC,QAAQ,GAC7D,6BAA6B,wBAC7B,AAAC,6BACC,wBACA,wBACF;QACN,KAAK,QAAQ,CAAC,kCAAkC;QAEhD,8BAA8B;QAC9B,mEAAmE;QACnE,oDAAoD;QACpD,MAAM,mBACJ,gDACA;QACF,KAAK,QAAQ,CAAC,0BAA0B;QAExC,+BAA+B;QAC/B,kCAAkC;QAClC,MAAM,oBACJ,gDAAgD;QAClD,KAAK,QAAQ,CAAC,2BAA2B;QAEzC,uBAAuB;QACvB,8CAA8C;QAC9C,oCAAoC;QACpC,KAAK,QAAQ,CACX,2CACA;QAGF,4CAA4C;QAC5C,wBAAwB;QACxB,wCAAwC;QACxC,4CAA4C;QAC5C,MAAM,sCACJ,AAAC,MAAM,+BAA+B,wBAAyB;QACjE,KAAK,QAAQ,CACX,6CACA;QAGF,mDAAmD;QACnD,+BAA+B;QAC/B,KAAK,QAAQ,CACX,6CACA,sCAAsC;QAGxC,qDAAqD;QACrD,wCAAwC;QACxC,+CAA+C;QAC/C,oCAAoC;QACpC,+BAA+B;QAC/B,2BAA2B;QAC3B,MAAM,+CACJ,+BAA+B,wJAAyB,CAAC,QAAQ,GAC7D,OACA,wBAAwB,CAAC,OAAO,iBAAiB;QACvD,KAAK,QAAQ,CACX,sDACA;QAGF,gCAAgC;QAChC,eAAe;QACf,iCAAiC;QACjC,mCAAmC;QACnC,KAAK,QAAQ,CACX,iCACA,wBAAwB,IAAI;QAG9B,IAAI,CAAC,WAAW;YACd;QACF;QACA,IAAI,cAAc,UAAU;YAC1B,KAAK,QAAQ,CAAC,4BAA4B;QAC5C;QAEA,IAAI,iBAAiB;YACnB,KAAK,QAAQ,CAAC,iBAAiB;gBAC7B,GAAG,eAAe;gBAClB,YAAY;YACd;QACF;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,wIAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,8OAAC;oBACC,WAAU;oBACV,UAAU,KAAK,YAAY,CAAC;;sCAE5B,8OAAC;4BAAG,WAAU;sCAAsB,EAAE;;;;;;sCACtC,8OAAC,wIAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,QAAQ;4BACR,OAAO;4BACP,WAAU;4BACV,QAAQ;;;;;;sCAEV,8OAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS;4BACT,GAAG;4BACH,SAAS;4BACT,cAAc;4BACd,cAAc,EAAE;;;;;;wBAEjB,cAAc,0BAAY,8OAAC,wMAAoB;;;;;wBAC/C,iCAAmB,8OAAC,oMAAkB;4BAAC,SAAS;;;;;;sCACjD,8OAAC,kJAAS;;;;;sCACV,8OAAC,gMAAoB;4BACnB,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS,gKAAiC;4BAC1C,eAAe,CAAA,IAAK,EAAE,CAAC,yBAAyB,EAAE,GAAG;4BACrD,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,gMAAoB;4BACnB,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS,+JAAgC;4BACzC,eAAe,CAAA,IAAK,EAAE,CAAC,4BAA4B,EAAE,GAAG;4BACxD,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAGL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,kJAAS;;;;;sCACV,8OAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,gMAAoB;4BACnB,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS,gKAAiC;4BAC1C,eAAe,CAAA,IACb,EAAE,CAAC,yCAAyC,EAAE,GAAG;4BAEnD,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAGZ,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,4IAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU;;gCAET,2BAAa,8OAAC,oOAAW;oCAAC,WAAU;;;;;;gCACpC,QAAQ;;;;;;;;;;;;;;;;;;YAId,wDACC,8OAAC,uWAAkD;gBACjD,wCACE;;;;;;;;;;;;AAMZ", "debugId": null}}, {"offset": {"line": 10081, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-nonreinforced-section-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { NonReinforcedSectionShearSchema } from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  nonReinforcedSectionShearResults?: NonReinforcedSectionShearSchema\r\n}\r\n\r\nexport function ShearNonReinforcedSectionResultCard({\r\n  nonReinforcedSectionShearResults,\r\n}: Props) {\r\n  const {\r\n    inPlaneAppliedShear,\r\n    verticalStress,\r\n    correctionFactorBasedOnWallSlenderness,\r\n    shearResistanceNotReinforcedMasonry,\r\n    check,\r\n  } = nonReinforcedSectionShearResults || {}\r\n\r\n  const t = useTranslations(\r\n    'components.calculations.maschi-murari.shearReinforcementCalculationResult.nonReinforcedSectionResult',\r\n  )\r\n\r\n  const locale = useLocale()\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <div>\r\n          <span className=\"font-medium\">{t('inPlaneAppliedShear.label')}:</span>{' '}\r\n          <span>\r\n            {inPlaneAppliedShear?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">{t('verticalStress.label')}:</span>{' '}\r\n          <span>\r\n            {verticalStress?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('correctionFactorBasedOnWallSlenderness.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {correctionFactorBasedOnWallSlenderness?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('shearResistanceNotReinforcedMasonry.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {shearResistanceNotReinforcedMasonry?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <span className=\"font-medium\">{t('check.label')}:</span>{' '}\r\n        <Badge\r\n          className={cn(\r\n            'text-base px-3 py-1',\r\n            check ? 'bg-green-600' : 'bg-red-600',\r\n          )}\r\n        >\r\n          {check ? t('check.satisfied') : t('check.notSatisfied')}\r\n        </Badge>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AAEA;AAAA;;;;;;AAMO,SAAS,oCAAoC,EAClD,gCAAgC,EAC1B;IACN,MAAM,EACJ,mBAAmB,EACnB,cAAc,EACd,sCAAsC,EACtC,mCAAmC,EACnC,KAAK,EACN,GAAG,oCAAoC,CAAC;IAEzC,MAAM,IAAI,IAAA,yNAAe,EACvB;IAGF,MAAM,SAAS,IAAA,+KAAS;IAExB,qBACE,8OAAC,wIAAI;;0BACH,8OAAC,8IAAU;0BACT,cAAA,8OAAC,6IAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,8OAAC,+IAAW;gBAAC,WAAU;;kCACrB,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAA6B;;;;;;;4BAAS;0CACvE,8OAAC;;oCACE,qBAAqB,eAAe,QAAQ;wCAC3C,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAAwB;;;;;;;4BAAS;0CAClE,8OAAC;;oCACE,gBAAgB,eAAe,QAAQ;wCACtC,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAgD;;;;;;;4BAC7C;0CACR,8OAAC;;oCACE,wCAAwC,eAAe,QAAQ;wCAC9D,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA6C;;;;;;;4BAC1C;0CACR,8OAAC;;oCACE,qCAAqC,eAAe,QAAQ;wCAC3D,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,8OAAC;wBAAK,WAAU;;4BAAe,EAAE;4BAAe;;;;;;;oBAAS;kCACzD,8OAAC,0IAAK;wBACJ,WAAW,IAAA,yHAAE,EACX,uBACA,QAAQ,iBAAiB;kCAG1B,QAAQ,EAAE,qBAAqB,EAAE;;;;;;;;;;;;;;;;;;AAK5C", "debugId": null}}, {"offset": {"line": 10283, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-reinforced-section-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { ReinforcedSectionShearSchema } from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  reinforcedSectionShearResults?: ReinforcedSectionShearSchema\r\n}\r\n\r\nexport function ShearReinforcedSectionResultCard({\r\n  reinforcedSectionShearResults,\r\n}: Props) {\r\n  const {\r\n    shearResistanceReinforcementContribution,\r\n    totalShearResistance,\r\n    firstCheck,\r\n    shearResistanceFromMasonryDiagonalCompression,\r\n    secondCheck,\r\n  } = reinforcedSectionShearResults || {}\r\n\r\n  const t = useTranslations(\r\n    'components.calculations.maschi-murari.shearReinforcementCalculationResult.reinforcedSectionResult',\r\n  )\r\n\r\n  const locale = useLocale()\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('shearResistanceReinforcementContribution.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {shearResistanceReinforcementContribution?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('totalShearResistance.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {totalShearResistance?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <span className=\"font-medium\">{t('firstCheck.label')}:</span>{' '}\r\n        <Badge\r\n          className={cn(\r\n            'text-base px-3 py-1',\r\n            firstCheck ? 'bg-green-600' : 'bg-red-600',\r\n          )}\r\n        >\r\n          {firstCheck\r\n            ? t('firstCheck.satisfied')\r\n            : t('firstCheck.notSatisfied')}\r\n        </Badge>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('shearResistanceFromMasonryDiagonalCompression.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {shearResistanceFromMasonryDiagonalCompression?.toLocaleString(\r\n              locale,\r\n              {\r\n                maximumFractionDigits: 3,\r\n              },\r\n            )}{' '}\r\n          </span>\r\n        </div>\r\n        <span className=\"font-medium\">{t('secondCheck.label')}:</span>{' '}\r\n        <Badge\r\n          className={cn(\r\n            'text-base px-3 py-1',\r\n            secondCheck ? 'bg-green-600' : 'bg-red-600',\r\n          )}\r\n        >\r\n          {secondCheck\r\n            ? t('secondCheck.satisfied')\r\n            : t('secondCheck.notSatisfied')}\r\n        </Badge>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AAEA;AAAA;;;;;;AAMO,SAAS,iCAAiC,EAC/C,6BAA6B,EACvB;IACN,MAAM,EACJ,wCAAwC,EACxC,oBAAoB,EACpB,UAAU,EACV,6CAA6C,EAC7C,WAAW,EACZ,GAAG,iCAAiC,CAAC;IAEtC,MAAM,IAAI,IAAA,yNAAe,EACvB;IAGF,MAAM,SAAS,IAAA,+KAAS;IAExB,qBACE,8OAAC,wIAAI;;0BACH,8OAAC,8IAAU;0BACT,cAAA,8OAAC,6IAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,8OAAC,+IAAW;gBAAC,WAAU;;kCACrB,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAkD;;;;;;;4BAC/C;0CACR,8OAAC;;oCACE,0CAA0C,eAAe,QAAQ;wCAChE,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA8B;;;;;;;4BAC3B;0CACR,8OAAC;;oCACE,sBAAsB,eAAe,QAAQ;wCAC5C,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,8OAAC;wBAAK,WAAU;;4BAAe,EAAE;4BAAoB;;;;;;;oBAAS;kCAC9D,8OAAC,0IAAK;wBACJ,WAAW,IAAA,yHAAE,EACX,uBACA,aAAa,iBAAiB;kCAG/B,aACG,EAAE,0BACF,EAAE;;;;;;kCAER,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAuD;;;;;;;4BACpD;0CACR,8OAAC;;oCACE,+CAA+C,eAC9C,QACA;wCACE,uBAAuB;oCACzB;oCACC;;;;;;;;;;;;;kCAGP,8OAAC;wBAAK,WAAU;;4BAAe,EAAE;4BAAqB;;;;;;;oBAAS;kCAC/D,8OAAC,0IAAK;wBACJ,WAAW,IAAA,yHAAE,EACX,uBACA,cAAc,iBAAiB;kCAGhC,cACG,EAAE,2BACF,EAAE;;;;;;;;;;;;;;;;;;AAKhB", "debugId": null}}, {"offset": {"line": 10473, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation-result.tsx"], "sourcesContent": ["import {\r\n  <PERSON>,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON>Content,\r\n  <PERSON><PERSON>List,\r\n  TabsTrigger,\r\n} from '@atlas/components/ui/tabs'\r\nimport type { MaschiMurariShearReinforcementCalculationResultSchema } from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\nimport { ShearNonReinforcedSectionResultCard } from './maschi-murari-shear-nonreinforced-section-result-card'\r\nimport { ShearReinforcedSectionResultCard } from './maschi-murari-shear-reinforced-section-result-card'\r\n\r\ntype Props = {\r\n  shearReinforcementCalculationResult: MaschiMurariShearReinforcementCalculationResultSchema\r\n}\r\n\r\nexport function MaschiMurariReinforcementShearCalculationResult({\r\n  shearReinforcementCalculationResult,\r\n}: Props) {\r\n  const t = useTranslations(\r\n    'components.calculations.maschi-murari.shearReinforcementCalculationResult',\r\n  )\r\n  const _locale = useLocale()\r\n\r\n  return (\r\n    <>\r\n      <h3 className=\"text-lg font-medium py-4\">{t('title')}</h3>\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle>{t('title')}</CardTitle>\r\n        </CardHeader>\r\n        <CardContent className=\"space-y-4\">\r\n          <Tabs defaultValue=\"nonReinforcedSection\">\r\n            <TabsList>\r\n              <TabsTrigger value=\"nonReinforcedSection\">\r\n                {t('shearReinforcedSectionResult.label')}\r\n              </TabsTrigger>\r\n              <TabsTrigger value=\"reinforcedSection\">\r\n                {t('shearReinforcedSectionResult.label')}\r\n              </TabsTrigger>\r\n            </TabsList>\r\n            <TabsContent value=\"nonReinforcedSection\">\r\n              <ShearNonReinforcedSectionResultCard\r\n                nonReinforcedSectionShearResults={\r\n                  shearReinforcementCalculationResult?.inPlaneShearCheck\r\n                    ?.nonReinforcedSection\r\n                }\r\n              />\r\n            </TabsContent>\r\n            <TabsContent value=\"reinforcedSection\">\r\n              <ShearReinforcedSectionResultCard\r\n                reinforcedSectionShearResults={\r\n                  shearReinforcementCalculationResult?.inPlaneShearCheck\r\n                    ?.reinforcedSection\r\n                }\r\n              />\r\n            </TabsContent>\r\n          </Tabs>\r\n        </CardContent>\r\n      </Card>\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAMA;AAOA;AAAA;AACA;AACA;;;;;;;AAMO,SAAS,gDAAgD,EAC9D,mCAAmC,EAC7B;IACN,MAAM,IAAI,IAAA,yNAAe,EACvB;IAEF,MAAM,UAAU,IAAA,+KAAS;IAEzB,qBACE;;0BACE,8OAAC;gBAAG,WAAU;0BAA4B,EAAE;;;;;;0BAC5C,8OAAC,wIAAI;;kCACH,8OAAC,8IAAU;kCACT,cAAA,8OAAC,6IAAS;sCAAE,EAAE;;;;;;;;;;;kCAEhB,8OAAC,+IAAW;wBAAC,WAAU;kCACrB,cAAA,8OAAC,wIAAI;4BAAC,cAAa;;8CACjB,8OAAC,4IAAQ;;sDACP,8OAAC,+IAAW;4CAAC,OAAM;sDAChB,EAAE;;;;;;sDAEL,8OAAC,+IAAW;4CAAC,OAAM;sDAChB,EAAE;;;;;;;;;;;;8CAGP,8OAAC,+IAAW;oCAAC,OAAM;8CACjB,cAAA,8OAAC,sVAAmC;wCAClC,kCACE,qCAAqC,mBACjC;;;;;;;;;;;8CAIV,8OAAC,+IAAW;oCAAC,OAAM;8CACjB,cAAA,8OAAC,gVAAgC;wCAC/B,+BACE,qCAAqC,mBACjC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStB", "debugId": null}}, {"offset": {"line": 10599, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx"], "sourcesContent": ["import { CustomProductSection } from '@atlas/components/common/atlas/product/custom-product-section'\r\nimport { ProductSummaryCard } from '@atlas/components/common/atlas/product/product-summary-card'\r\nimport { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport {\r\n  FACING_MATERIAL,\r\n  MODULE_REINFORCEMENT_ARRANGEMENT,\r\n  MODULE_REINFORCEMENT_FAILURE_MODE,\r\n  REINFORCEMENT_ARRANGEMENT,\r\n  REINFORCEMENT_FAILURE_MODE,\r\n} from '@atlas/constants/module'\r\nimport type { ModuleWithParamsMaschiMurari } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useModuleCalculation } from '@atlas/lib/mutation/modules/use-module-calculation'\r\nimport { useProductsByCategory } from '@atlas/lib/query/products/use-products-by-category'\r\nimport {\r\n  type MaschiMurariShearReinforcementExecutionSchemaInput,\r\n  maschiMurariShearReinforcementExecutionSchema,\r\n} from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useMemo } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\nimport { MaschiMurariReinforcementShearCalculationResult } from './maschi-murari-reinforcement-shear-calculation-result'\r\n\r\ntype Props = {\r\n  module: ModuleWithParamsMaschiMurari\r\n  session: Session\r\n  projectId: Project['id']\r\n  onNext: () => void\r\n}\r\n\r\n// N.B: this function is temporary, until we remove the quotes in future seed\r\nfunction getCleanedUpValue(\r\n  recordToClean: Record<string, number> | undefined,\r\n  nature: string,\r\n) {\r\n  // nature should be 'BRICK', 'TUFF', or 'STONE'\r\n  if (!recordToClean) {\r\n    return undefined\r\n  }\r\n  const key = `\"${nature}\"` // matches the API key format\r\n  return recordToClean[key]\r\n}\r\n\r\nexport const MaschiMurariShearCalculation = ({\r\n  session,\r\n  module,\r\n  projectId,\r\n  onNext,\r\n}: Props) => {\r\n  const t = useTranslations('forms.calculations.maschi-murari.shear')\r\n  const tAction = useTranslations('actions.calculations.maschi-murari')\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  const {\r\n    params,\r\n    shearReinforcementVerifyExecutionInput,\r\n    shearReinforcementCalculationResult,\r\n  } = module\r\n\r\n  const form = useForm<MaschiMurariShearReinforcementExecutionSchemaInput>({\r\n    resolver: zodResolver(maschiMurariShearReinforcementExecutionSchema),\r\n    defaultValues: {\r\n      calculationType: 'SHEAR_VERIFY',\r\n      input: {\r\n        product: {\r\n          id: shearReinforcementVerifyExecutionInput?.product.id,\r\n          name: shearReinforcementVerifyExecutionInput?.product.name,\r\n          sourceType:\r\n            shearReinforcementVerifyExecutionInput?.product.id === 'custom'\r\n              ? 'CUSTOM'\r\n              : 'DATABASE',\r\n        },\r\n        reinforcementFailureMode:\r\n          shearReinforcementVerifyExecutionInput?.reinforcementFailureMode ??\r\n          REINFORCEMENT_FAILURE_MODE.DISTACCO_DI_ESTREMITA,\r\n        designReinforcementStress:\r\n          shearReinforcementVerifyExecutionInput?.designReinforcementStress ??\r\n          0,\r\n        designReinforcementStrain:\r\n          shearReinforcementVerifyExecutionInput?.designReinforcementStrain ??\r\n          0,\r\n        reinforcedArrangement:\r\n          shearReinforcementVerifyExecutionInput?.reinforcedArrangement ??\r\n          REINFORCEMENT_ARRANGEMENT.CONTINUE,\r\n        singleStripWidth:\r\n          shearReinforcementVerifyExecutionInput?.singleStripWidth ?? 0,\r\n        stripSpacing: shearReinforcementVerifyExecutionInput?.stripSpacing ?? 0,\r\n        totalReinforcementWidthPerpendicularShearDirection:\r\n          shearReinforcementVerifyExecutionInput?.totalReinforcementWidthPerpendicularShearDirection ??\r\n          0,\r\n        compressedEdgeReinforcementFiberDistance:\r\n          shearReinforcementVerifyExecutionInput?.compressedEdgeReinforcementFiberDistance ??\r\n          0,\r\n        layersNumber: shearReinforcementVerifyExecutionInput?.layersNumber ?? 1,\r\n        reinforcedSidesNumber:\r\n          shearReinforcementVerifyExecutionInput?.reinforcedSidesNumber ?? 0,\r\n      },\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useModuleCalculation(session.accessToken, {\r\n    onSuccess: () => {\r\n      toast.success(tAction('calculate.success'))\r\n    },\r\n    onError: error => {\r\n      console.log('ERROR  ', error)\r\n      toast.error(tAction('calculate.failure', { error: error.message }))\r\n    },\r\n  })\r\n\r\n  const handleFormSubmit = (\r\n    body: MaschiMurariShearReinforcementExecutionSchemaInput,\r\n  ) => {\r\n    mutate({ projectId, moduleId: module.id, body })\r\n  }\r\n\r\n  const {\r\n    data: products,\r\n    isError: errorGettingProducts,\r\n    isLoading: isLoadingProducts,\r\n  } = useProductsByCategory(session, 'MASCHI_MURARI', 0, 100)\r\n\r\n  const productsOptions = [\r\n    ...(products?.content.map(m => ({\r\n      value: m.id,\r\n      label: m.name ?? t('product.unnamed'),\r\n    })) ?? []),\r\n  ]\r\n\r\n  const [productId] = form.watch(['input.product.id'])\r\n\r\n  const selectedProduct = useMemo(\r\n    () => products?.content.find(p => p.id === productId),\r\n    [productId, products],\r\n  )\r\n\r\n  const conventionalStrainLimit = selectedProduct?.conventionalStrainLimit\r\n  const conversionFactor = params?.materialProperties?.conversionFactor ?? 0\r\n  const reinforcementFailureModeValue = form.watch(\r\n    'input.reinforcementFailureMode',\r\n  )\r\n\r\n  const structuralElementsNatureValue =\r\n    params?.materialProperties?.structuralElementsNature ??\r\n    FACING_MATERIAL.BRICK\r\n\r\n  const panelGeometryHeight = params?.panelGeometry?.height ?? 0\r\n  const panelGeometryWidth = params?.panelGeometry?.width ?? 0\r\n  const singleStripWidthValue = form.watch('input.singleStripWidth') ?? 0\r\n  const stripSpacingValue = form.watch('input.stripSpacing') ?? 0\r\n\r\n  const reinforcedArrangementValue = form.watch('input.reinforcedArrangement')\r\n\r\n  useEffect(() => {\r\n    // D43 = designReinforcementStrain =\r\n    // =IF(D42=\"Distacco intermedio\",(1.5*D41*D26/1.5),(D41*D26/1.5))\r\n    // D42 = input.reinforcementFailureMode\r\n    // D41 = product.conventionalStrainLimit\r\n    // D26 = materialProperties.conversionFactor\r\n    const conventionalStrainLimitValue =\r\n      getCleanedUpValue(\r\n        conventionalStrainLimit,\r\n        structuralElementsNatureValue,\r\n      ) ?? 0\r\n\r\n    const designReinforcementStrain =\r\n      reinforcementFailureModeValue ===\r\n      REINFORCEMENT_FAILURE_MODE.DISTACCO_INTERMEDIO\r\n        ? (1.5 * conventionalStrainLimitValue * conversionFactor) / 1.5\r\n        : (conventionalStrainLimitValue * conversionFactor) / 1.5\r\n\r\n    form.setValue('input.designReinforcementStrain', designReinforcementStrain)\r\n\r\n    // D62 = designReinforcementStress = =D43*D38\r\n    // D43 = designReinforcementStrain\r\n    // D38 = product.elasticModulus\r\n    const elasticModulus = selectedProduct?.elasticModulus ?? 0\r\n    const designReinforcementStress = designReinforcementStrain * elasticModulus\r\n    form.setValue('input.designReinforcementStress', designReinforcementStress)\r\n\r\n    // D48 totalReinforcementWidthPerpendicularShearDirection =\r\n    //  =IF(D45=\"Continuo\",MIN(D8,D9),MIN(D46*(D8/D47),D8,D9))\r\n    // D45 = input.reinforcedArrangement\r\n    // D8 = panelGeometry.height\r\n    // D9 = panelGeometry.width\r\n    // D46 = input.singleStripWidth\r\n    // D47 = input.stripSpacing\r\n\r\n    const totalReinforcementWidthPerpendicularShearDirection =\r\n      reinforcedArrangementValue === REINFORCEMENT_ARRANGEMENT.CONTINUE\r\n        ? Math.min(panelGeometryHeight, panelGeometryWidth)\r\n        : Math.min(\r\n            singleStripWidthValue * (panelGeometryHeight / stripSpacingValue),\r\n            panelGeometryHeight,\r\n            panelGeometryWidth,\r\n          )\r\n    form.setValue(\r\n      'input.totalReinforcementWidthPerpendicularShearDirection',\r\n      totalReinforcementWidthPerpendicularShearDirection,\r\n    )\r\n\r\n    form.setValue('input.layersNumber', 1)\r\n    if (!productId) {\r\n      return\r\n    }\r\n    if (productId === 'custom') {\r\n      form.setValue('input.product.sourceType', 'CUSTOM')\r\n    }\r\n\r\n    if (selectedProduct) {\r\n      form.setValue('input.product', {\r\n        ...selectedProduct,\r\n        sourceType: 'DATABASE',\r\n      })\r\n    }\r\n  }, [\r\n    form,\r\n    productId,\r\n    selectedProduct,\r\n    conversionFactor,\r\n    reinforcementFailureModeValue,\r\n    conventionalStrainLimit,\r\n    structuralElementsNatureValue,\r\n    panelGeometryHeight,\r\n    panelGeometryWidth,\r\n    singleStripWidthValue,\r\n    stripSpacingValue,\r\n    reinforcedArrangementValue,\r\n  ])\r\n\r\n  return (\r\n    <div className=\"flex flex-col justify-center gap-4\">\r\n      <Form {...form}>\r\n        <form\r\n          className=\"space-y-4 rounded-md border p-4\"\r\n          onSubmit={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          <h1 className=\"text-3xl font-bold\">{t('heading')}</h1>\r\n          <Image\r\n            src=\"/assets/masonry_frcm/maschi-murari/FRCM_PANNELLI MURARI_Diffuso.jpg\"\r\n            alt=\"shear verify\"\r\n            height={250}\r\n            width={500}\r\n            className=\"mx-auto rounded-md object-contain\"\r\n            priority\r\n          />\r\n          <h1 className=\"text-xl font-bold\">{t('sub-heading')}</h1>\r\n          <SelectFormInput\r\n            control={form.control}\r\n            name=\"input.product.id\"\r\n            options={productsOptions}\r\n            t={t}\r\n            loading={isLoadingProducts}\r\n            requestError={errorGettingProducts}\r\n            errorMessage={t('products.error')}\r\n          />\r\n          {productId === 'custom' && <CustomProductSection />}\r\n          {selectedProduct && <ProductSummaryCard product={selectedProduct} />}\r\n          <Separator />\r\n          <SelectFormFixedInput\r\n            control={form.control}\r\n            name=\"input.reinforcementFailureMode\"\r\n            options={MODULE_REINFORCEMENT_FAILURE_MODE}\r\n            optionLabelFn={p => t(`input.reinforcementFailureMode.${p}`)}\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.designReinforcementStrain\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.designReinforcementStress\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <SelectFormFixedInput\r\n            control={form.control}\r\n            name=\"input.reinforcedArrangement\"\r\n            options={MODULE_REINFORCEMENT_ARRANGEMENT}\r\n            optionLabelFn={p => t(`input.reinforcedArrangement.${p}`)}\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.singleStripWidth\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.stripSpacing\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.totalReinforcementWidthPerpendicularShearDirection\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.layersNumber\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.reinforcedSidesNumber\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.compressedEdgeReinforcementFiberDistance\"\r\n            t={t}\r\n          />\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full sm:w-auto\"\r\n            disabled={isPending}\r\n          >\r\n            {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n            {tCommon('calculate')}\r\n          </Button>\r\n        </form>\r\n      </Form>\r\n      {shearReinforcementCalculationResult && (\r\n        <>\r\n          <MaschiMurariReinforcementShearCalculationResult\r\n            shearReinforcementCalculationResult={\r\n              shearReinforcementCalculationResult\r\n            }\r\n          />\r\n          <Button type=\"submit\" className=\"w-full sm:w-auto\" onClick={onNext}>\r\n            {tCommon('next')}\r\n          </Button>\r\n        </>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AASA;AACA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;AASA,6EAA6E;AAC7E,SAAS,kBACP,aAAiD,EACjD,MAAc;IAEd,+CAA+C;IAC/C,IAAI,CAAC,eAAe;QAClB,OAAO;IACT;IACA,MAAM,MAAM,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,6BAA6B;;IACvD,OAAO,aAAa,CAAC,IAAI;AAC3B;AAEO,MAAM,+BAA+B,CAAC,EAC3C,OAAO,EACP,MAAM,EACN,SAAS,EACT,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAEhC,MAAM,EACJ,MAAM,EACN,sCAAsC,EACtC,mCAAmC,EACpC,GAAG;IAEJ,MAAM,OAAO,IAAA,yKAAO,EAAqD;QACvE,UAAU,IAAA,6KAAW,EAAC,+MAA6C;QACnE,eAAe;YACb,iBAAiB;YACjB,OAAO;gBACL,SAAS;oBACP,IAAI,wCAAwC,QAAQ;oBACpD,MAAM,wCAAwC,QAAQ;oBACtD,YACE,wCAAwC,QAAQ,OAAO,WACnD,WACA;gBACR;gBACA,0BACE,wCAAwC,4BACxC,yJAA0B,CAAC,qBAAqB;gBAClD,2BACE,wCAAwC,6BACxC;gBACF,2BACE,wCAAwC,6BACxC;gBACF,uBACE,wCAAwC,yBACxC,wJAAyB,CAAC,QAAQ;gBACpC,kBACE,wCAAwC,oBAAoB;gBAC9D,cAAc,wCAAwC,gBAAgB;gBACtE,oDACE,wCAAwC,sDACxC;gBACF,0CACE,wCAAwC,4CACxC;gBACF,cAAc,wCAAwC,gBAAgB;gBACtE,uBACE,wCAAwC,yBAAyB;YACrE;QACF;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,0LAAoB,EAAC,QAAQ,WAAW,EAAE;QACtE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;QACxB;QACA,SAAS,CAAA;YACP,QAAQ,GAAG,CAAC,WAAW;YACvB,iJAAK,CAAC,KAAK,CAAC,QAAQ,qBAAqB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAClE;IACF;IAEA,MAAM,mBAAmB,CACvB;QAEA,OAAO;YAAE;YAAW,UAAU,OAAO,EAAE;YAAE;QAAK;IAChD;IAEA,MAAM,EACJ,MAAM,QAAQ,EACd,SAAS,oBAAoB,EAC7B,WAAW,iBAAiB,EAC7B,GAAG,IAAA,8LAAqB,EAAC,SAAS,iBAAiB,GAAG;IAEvD,MAAM,kBAAkB;WAClB,UAAU,QAAQ,IAAI,CAAA,IAAK,CAAC;gBAC9B,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,IAAI,IAAI,EAAE;YACrB,CAAC,MAAM,EAAE;KACV;IAED,MAAM,CAAC,UAAU,GAAG,KAAK,KAAK,CAAC;QAAC;KAAmB;IAEnD,MAAM,kBAAkB,IAAA,gNAAO,EAC7B,IAAM,UAAU,QAAQ,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK,YAC3C;QAAC;QAAW;KAAS;IAGvB,MAAM,0BAA0B,iBAAiB;IACjD,MAAM,mBAAmB,QAAQ,oBAAoB,oBAAoB;IACzE,MAAM,gCAAgC,KAAK,KAAK,CAC9C;IAGF,MAAM,gCACJ,QAAQ,oBAAoB,4BAC5B,8IAAe,CAAC,KAAK;IAEvB,MAAM,sBAAsB,QAAQ,eAAe,UAAU;IAC7D,MAAM,qBAAqB,QAAQ,eAAe,SAAS;IAC3D,MAAM,wBAAwB,KAAK,KAAK,CAAC,6BAA6B;IACtE,MAAM,oBAAoB,KAAK,KAAK,CAAC,yBAAyB;IAE9D,MAAM,6BAA6B,KAAK,KAAK,CAAC;IAE9C,IAAA,kNAAS,EAAC;QACR,oCAAoC;QACpC,iEAAiE;QACjE,uCAAuC;QACvC,wCAAwC;QACxC,4CAA4C;QAC5C,MAAM,+BACJ,kBACE,yBACA,kCACG;QAEP,MAAM,4BACJ,kCACA,yJAA0B,CAAC,mBAAmB,GAC1C,AAAC,MAAM,+BAA+B,mBAAoB,MAC1D,AAAC,+BAA+B,mBAAoB;QAE1D,KAAK,QAAQ,CAAC,mCAAmC;QAEjD,6CAA6C;QAC7C,kCAAkC;QAClC,+BAA+B;QAC/B,MAAM,iBAAiB,iBAAiB,kBAAkB;QAC1D,MAAM,4BAA4B,4BAA4B;QAC9D,KAAK,QAAQ,CAAC,mCAAmC;QAEjD,2DAA2D;QAC3D,0DAA0D;QAC1D,oCAAoC;QACpC,4BAA4B;QAC5B,2BAA2B;QAC3B,+BAA+B;QAC/B,2BAA2B;QAE3B,MAAM,qDACJ,+BAA+B,wJAAyB,CAAC,QAAQ,GAC7D,KAAK,GAAG,CAAC,qBAAqB,sBAC9B,KAAK,GAAG,CACN,wBAAwB,CAAC,sBAAsB,iBAAiB,GAChE,qBACA;QAER,KAAK,QAAQ,CACX,4DACA;QAGF,KAAK,QAAQ,CAAC,sBAAsB;QACpC,IAAI,CAAC,WAAW;YACd;QACF;QACA,IAAI,cAAc,UAAU;YAC1B,KAAK,QAAQ,CAAC,4BAA4B;QAC5C;QAEA,IAAI,iBAAiB;YACnB,KAAK,QAAQ,CAAC,iBAAiB;gBAC7B,GAAG,eAAe;gBAClB,YAAY;YACd;QACF;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,wIAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,8OAAC;oBACC,WAAU;oBACV,UAAU,KAAK,YAAY,CAAC;;sCAE5B,8OAAC;4BAAG,WAAU;sCAAsB,EAAE;;;;;;sCACtC,8OAAC,wIAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,QAAQ;4BACR,OAAO;4BACP,WAAU;4BACV,QAAQ;;;;;;sCAEV,8OAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS;4BACT,GAAG;4BACH,SAAS;4BACT,cAAc;4BACd,cAAc,EAAE;;;;;;wBAEjB,cAAc,0BAAY,8OAAC,wMAAoB;;;;;wBAC/C,iCAAmB,8OAAC,oMAAkB;4BAAC,SAAS;;;;;;sCACjD,8OAAC,kJAAS;;;;;sCACV,8OAAC,gMAAoB;4BACnB,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS,gKAAiC;4BAC1C,eAAe,CAAA,IAAK,EAAE,CAAC,+BAA+B,EAAE,GAAG;4BAC3D,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,gMAAoB;4BACnB,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS,+JAAgC;4BACzC,eAAe,CAAA,IAAK,EAAE,CAAC,4BAA4B,EAAE,GAAG;4BACxD,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,4IAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU;;gCAET,2BAAa,8OAAC,oOAAW;oCAAC,WAAU;;;;;;gCACpC,QAAQ;;;;;;;;;;;;;;;;;;YAId,qDACC;;kCACE,8OAAC,8VAA+C;wBAC9C,qCACE;;;;;;kCAGJ,8OAAC,4IAAM;wBAAC,MAAK;wBAAS,WAAU;wBAAmB,SAAS;kCACzD,QAAQ;;;;;;;;;;;;;;AAMrB", "debugId": null}}, {"offset": {"line": 11002, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-actions-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type MaschiMurariActionsSchemaInputs,\r\n  type MaschiMurariParamsSchemaInputs,\r\n  maschiMurariActionsSchema,\r\n} from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<MaschiMurariActionsSchemaInputs>\r\n  params: MaschiMurariParamsSchemaInputs\r\n  setParams: (newParams: any) => void\r\n  onSave: () => void\r\n}\r\n\r\nexport const MaschiMurariActionsForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  params,\r\n  setParams,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.maschi-murari.actions')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const form = useForm<MaschiMurariActionsSchemaInputs>({\r\n    resolver: zodResolver(maschiMurariActionsSchema),\r\n    defaultValues: {\r\n      appliedNormalStress: defaultValues?.appliedNormalStress ?? 0,\r\n      inPlaneBendingMoment: defaultValues?.inPlaneBendingMoment ?? 0,\r\n      outOfPlaneBendingMoment: defaultValues?.outOfPlaneBendingMoment ?? 0,\r\n      inPlaneAppliedShear: defaultValues?.inPlaneAppliedShear ?? 0,\r\n      outOfPlaneAppliedShear: defaultValues?.outOfPlaneAppliedShear ?? 0,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: MaschiMurariActionsSchemaInputs) => {\r\n      // construct body based on global schema:\r\n      const maschiMurariParams: MaschiMurariParamsSchemaInputs = {\r\n        ...params,\r\n        actions: body,\r\n      }\r\n      mutate({ projectId, moduleId, body: maschiMurariParams })\r\n    },\r\n    [mutate, projectId, moduleId, params],\r\n  )\r\n\r\n  useEffect(() => {\r\n    const subscription = form.watch(values => {\r\n      setParams((prev: any) => ({\r\n        ...prev,\r\n        buildingCharacteristics: values,\r\n      }))\r\n    })\r\n    return () => subscription.unsubscribe()\r\n  }, [form, setParams])\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"appliedNormalStress\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"inPlaneBendingMoment\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"outOfPlaneBendingMoment\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"inPlaneAppliedShear\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"outOfPlaneAppliedShear\"\r\n          t={t}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAGA;AACA;AAKA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;AAYO,MAAM,0BAA0B,CAAC,EACtC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACN,SAAS,EACT,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,OAAO,IAAA,yKAAO,EAAkC;QACpD,UAAU,IAAA,6KAAW,EAAC,2LAAyB;QAC/C,eAAe;YACb,qBAAqB,eAAe,uBAAuB;YAC3D,sBAAsB,eAAe,wBAAwB;YAC7D,yBAAyB,eAAe,2BAA2B;YACnE,qBAAqB,eAAe,uBAAuB;YAC3D,wBAAwB,eAAe,0BAA0B;QACnE;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAW;wBAAE;wBAAW;oBAAS;iBAAE;YAChD;YACA;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,yCAAyC;QACzC,MAAM,qBAAqD;YACzD,GAAG,MAAM;YACT,SAAS;QACX;QACA,OAAO;YAAE;YAAW;YAAU,MAAM;QAAmB;IACzD,GACA;QAAC;QAAQ;QAAW;QAAU;KAAO;IAGvC,IAAA,kNAAS,EAAC;QACR,MAAM,eAAe,KAAK,KAAK,CAAC,CAAA;YAC9B,UAAU,CAAC,OAAc,CAAC;oBACxB,GAAG,IAAI;oBACP,yBAAyB;gBAC3B,CAAC;QACH;QACA,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG;QAAC;QAAM;KAAU;IAEpB,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,8OAAC,4IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,8OAAC,oOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 11183, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-material-properties-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport {\r\n  CRM_MASONRY_TYPE,\r\n  characteristicCompressiveStrengthValues,\r\n  characteristicNormalElasticityModulusValues,\r\n  characteristicShearElasticityModulusValues,\r\n  characteristicShearStrengthValues,\r\n  executionClass,\r\n  FACING_MATERIAL,\r\n  loadResistingCategory,\r\n  MODULE_CRM_MASONRY_TYPE,\r\n  MODULE_EXECUTION_CLASS,\r\n  MODULE_FACING_MATERIAL,\r\n  MODULE_GEOMETRY_EXPOSURE,\r\n  MODULE_LOAD_RESISTING_CATEGORY,\r\n  MODULE_MATERIAL_KNOWLEDGE_LEVEL,\r\n  masonryStrengthSafetyFactorMapping,\r\n  moduleGeometryExposure,\r\n  moduleMaterialKnowledgeLevel,\r\n  moduleMaterialKnowledgeLevelValues,\r\n} from '@atlas/constants/module'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type MaschiMurariMaterialPropertiesSchemaInputs,\r\n  type MaschiMurariParamsSchemaInputs,\r\n  maschiMurariMaterialPropertiesSchema,\r\n} from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<MaschiMurariMaterialPropertiesSchemaInputs>\r\n  params: MaschiMurariParamsSchemaInputs\r\n  setParams: (newParams: any) => void\r\n  onSave: () => void\r\n}\r\n\r\nexport const MaschiMurariMaterialPropertiesForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  params,\r\n  setParams,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.project-params.maschi-murari.material-properties',\r\n  )\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const form = useForm<MaschiMurariMaterialPropertiesSchemaInputs>({\r\n    resolver: zodResolver(maschiMurariMaterialPropertiesSchema),\r\n    defaultValues: {\r\n      structuralElementsNature:\r\n        defaultValues?.structuralElementsNature ?? FACING_MATERIAL.BRICK,\r\n      masonryType:\r\n        defaultValues?.masonryType ??\r\n        CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA,\r\n      knowledgeLevel:\r\n        defaultValues?.knowledgeLevel ?? moduleMaterialKnowledgeLevel.LC1,\r\n      confidenceFactor: defaultValues?.confidenceFactor ?? 0,\r\n      executionClass: defaultValues?.executionClass ?? executionClass.ONE,\r\n      loadResistantCategory:\r\n        defaultValues?.loadResistantCategory ??\r\n        loadResistingCategory.MASONRY_CAT_I_AND_MORTAR_WITH_GUARANTEED_PERFORMANCE,\r\n      masonrySafetyFactor: defaultValues?.masonrySafetyFactor ?? 0,\r\n      characteristicCompressiveStrength:\r\n        defaultValues?.characteristicCompressiveStrength ?? 0,\r\n      designCompressiveStrength: defaultValues?.designCompressiveStrength ?? 0,\r\n      characteristicShearStrength:\r\n        defaultValues?.characteristicShearStrength ?? 0,\r\n      designShearStrength: defaultValues?.designShearStrength ?? 0,\r\n      elasticModulus: defaultValues?.elasticModulus ?? 0,\r\n      shearModulus: defaultValues?.shearModulus ?? 0,\r\n      ultimateCompressiveStrainLinearBehaviour:\r\n        defaultValues?.ultimateCompressiveStrainLinearBehaviour ?? 0,\r\n      ultimateCompressiveStrain:\r\n        defaultValues?.ultimateCompressiveStrain ?? 0.0035,\r\n      exposure: defaultValues?.exposure ?? moduleGeometryExposure.INTERNAL,\r\n      conversionFactor: defaultValues?.conversionFactor ?? 0,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: MaschiMurariMaterialPropertiesSchemaInputs) => {\r\n      // construct body based on global schema:\r\n      const maschiMurariParams: MaschiMurariParamsSchemaInputs = {\r\n        ...params,\r\n        materialProperties: body,\r\n      }\r\n      mutate({ projectId, moduleId, body: maschiMurariParams })\r\n    },\r\n    [mutate, projectId, moduleId, params],\r\n  )\r\n\r\n  const knowledgeMaterialLevel =\r\n    form.watch('knowledgeLevel') ?? moduleMaterialKnowledgeLevel.LC1\r\n  const executionClassFormValue =\r\n    form.watch('executionClass') ?? executionClass.ONE\r\n  const loadResistingCategoryFormValue =\r\n    form.watch('loadResistantCategory') ??\r\n    loadResistingCategory.MASONRY_CAT_I_AND_MORTAR_WITH_GUARANTEED_PERFORMANCE\r\n  const masonryTypeValue =\r\n    form.watch('masonryType') ??\r\n    CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA\r\n  const exposureValue =\r\n    form.watch('exposure') ?? moduleGeometryExposure.INTERNAL\r\n\r\n  useEffect(() => {\r\n    const calculateGivenStrength = (strengthMinMax: {\r\n      min: number\r\n      max: number\r\n    }) => {\r\n      return knowledgeMaterialLevel === moduleMaterialKnowledgeLevel.LC1\r\n        ? strengthMinMax.min\r\n        : knowledgeMaterialLevel === moduleMaterialKnowledgeLevel.LC3\r\n          ? strengthMinMax.max\r\n          : (strengthMinMax.min + strengthMinMax.max) / 2\r\n    }\r\n\r\n    const confidenceFactor =\r\n      moduleMaterialKnowledgeLevelValues[knowledgeMaterialLevel]\r\n    form.setValue('confidenceFactor', confidenceFactor)\r\n\r\n    const masonryStrengthSafetyFactor =\r\n      masonryStrengthSafetyFactorMapping[loadResistingCategoryFormValue][\r\n        executionClassFormValue\r\n      ]\r\n    form.setValue('masonrySafetyFactor', masonryStrengthSafetyFactor)\r\n\r\n    const characteristicCompressiveStrengthMinMax =\r\n      characteristicCompressiveStrengthValues[masonryTypeValue]\r\n\r\n    const characteristicCompressiveStrength = calculateGivenStrength(\r\n      characteristicCompressiveStrengthMinMax,\r\n    )\r\n    form.setValue(\r\n      'characteristicCompressiveStrength',\r\n      characteristicCompressiveStrength,\r\n    )\r\n\r\n    const designCompressiveStrength =\r\n      characteristicCompressiveStrength /\r\n      masonryStrengthSafetyFactor /\r\n      confidenceFactor\r\n    form.setValue('designCompressiveStrength', designCompressiveStrength)\r\n\r\n    const characteristicsShearStrengthMinMax =\r\n      characteristicShearStrengthValues[masonryTypeValue]\r\n    const characteristicShearStrength = calculateGivenStrength(\r\n      characteristicsShearStrengthMinMax,\r\n    )\r\n    form.setValue('characteristicShearStrength', characteristicShearStrength)\r\n\r\n    const designShearStrength =\r\n      characteristicShearStrength /\r\n      confidenceFactor /\r\n      masonryStrengthSafetyFactor\r\n    form.setValue('designShearStrength', designShearStrength)\r\n\r\n    const normalElasticityModulusMinMax =\r\n      characteristicNormalElasticityModulusValues[masonryTypeValue]\r\n    const normalElasticityModulus =\r\n      (normalElasticityModulusMinMax.min + normalElasticityModulusMinMax.max) /\r\n      2\r\n    form.setValue('elasticModulus', normalElasticityModulus)\r\n\r\n    const shearElasticityModulusMinMax =\r\n      characteristicShearElasticityModulusValues[masonryTypeValue]\r\n    const shearElasticityModulus =\r\n      (shearElasticityModulusMinMax.min + shearElasticityModulusMinMax.max) / 2\r\n    form.setValue('shearModulus', shearElasticityModulus)\r\n\r\n    // =D19/D22\r\n    const ultimateCompressiveStrainLinearBehaviour =\r\n      designCompressiveStrength / normalElasticityModulus\r\n    form.setValue(\r\n      'ultimateCompressiveStrainLinearBehaviour',\r\n      ultimateCompressiveStrainLinearBehaviour,\r\n    )\r\n\r\n    const ultimateCompressiveStrain = 0.0035\r\n    form.setValue('ultimateCompressiveStrain', ultimateCompressiveStrain)\r\n\r\n    const conversionFactor =\r\n      exposureValue === moduleGeometryExposure.INTERNAL\r\n        ? 0.9\r\n        : exposureValue === moduleGeometryExposure.EXTERNAL\r\n          ? 0.8\r\n          : 0.7\r\n    form.setValue('conversionFactor', conversionFactor)\r\n  }, [\r\n    form,\r\n    knowledgeMaterialLevel,\r\n    executionClassFormValue,\r\n    loadResistingCategoryFormValue,\r\n    masonryTypeValue,\r\n    exposureValue,\r\n  ])\r\n\r\n  useEffect(() => {\r\n    const subscription = form.watch(values => {\r\n      setParams((prev: any) => ({\r\n        ...prev,\r\n        buildingCharacteristics: values,\r\n      }))\r\n    })\r\n    return () => subscription.unsubscribe()\r\n  }, [form, setParams])\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"structuralElementsNature\"\r\n          options={MODULE_FACING_MATERIAL}\r\n          optionLabelFn={p => t(`structuralElementsNature.${p}`)}\r\n          t={t}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"masonryType\"\r\n          options={MODULE_CRM_MASONRY_TYPE}\r\n          optionLabelFn={p => t(`masonryType.${p}`)}\r\n          t={t}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"knowledgeLevel\"\r\n          options={MODULE_MATERIAL_KNOWLEDGE_LEVEL}\r\n          optionLabelFn={p => t(`knowledgeLevel.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"confidenceFactor\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"executionClass\"\r\n          options={MODULE_EXECUTION_CLASS}\r\n          optionLabelFn={p => t(`executionClass.${p}`)}\r\n          t={t}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"loadResistantCategory\"\r\n          options={MODULE_LOAD_RESISTING_CATEGORY}\r\n          optionLabelFn={p => t(`loadResistantCategory.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"masonrySafetyFactor\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"characteristicCompressiveStrength\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"designCompressiveStrength\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"characteristicShearStrength\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"designShearStrength\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"elasticModulus\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"shearModulus\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"ultimateCompressiveStrainLinearBehaviour\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"ultimateCompressiveStrain\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"exposure\"\r\n          options={MODULE_GEOMETRY_EXPOSURE}\r\n          optionLabelFn={p => t(`exposure.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"conversionFactor\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAsBA;AACA;AAKA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AAYO,MAAM,qCAAqC,CAAC,EACjD,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACN,SAAS,EACT,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EACvB;IAEF,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,OAAO,IAAA,yKAAO,EAA6C;QAC/D,UAAU,IAAA,6KAAW,EAAC,sMAAoC;QAC1D,eAAe;YACb,0BACE,eAAe,4BAA4B,8IAAe,CAAC,KAAK;YAClE,aACE,eAAe,eACf,+IAAgB,CAAC,gCAAgC;YACnD,gBACE,eAAe,kBAAkB,2JAA4B,CAAC,GAAG;YACnE,kBAAkB,eAAe,oBAAoB;YACrD,gBAAgB,eAAe,kBAAkB,6IAAc,CAAC,GAAG;YACnE,uBACE,eAAe,yBACf,oJAAqB,CAAC,oDAAoD;YAC5E,qBAAqB,eAAe,uBAAuB;YAC3D,mCACE,eAAe,qCAAqC;YACtD,2BAA2B,eAAe,6BAA6B;YACvE,6BACE,eAAe,+BAA+B;YAChD,qBAAqB,eAAe,uBAAuB;YAC3D,gBAAgB,eAAe,kBAAkB;YACjD,cAAc,eAAe,gBAAgB;YAC7C,0CACE,eAAe,4CAA4C;YAC7D,2BACE,eAAe,6BAA6B;YAC9C,UAAU,eAAe,YAAY,qJAAsB,CAAC,QAAQ;YACpE,kBAAkB,eAAe,oBAAoB;QACvD;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAW;wBAAE;wBAAW;oBAAS;iBAAE;YAChD;YACA;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,yCAAyC;QACzC,MAAM,qBAAqD;YACzD,GAAG,MAAM;YACT,oBAAoB;QACtB;QACA,OAAO;YAAE;YAAW;YAAU,MAAM;QAAmB;IACzD,GACA;QAAC;QAAQ;QAAW;QAAU;KAAO;IAGvC,MAAM,yBACJ,KAAK,KAAK,CAAC,qBAAqB,2JAA4B,CAAC,GAAG;IAClE,MAAM,0BACJ,KAAK,KAAK,CAAC,qBAAqB,6IAAc,CAAC,GAAG;IACpD,MAAM,iCACJ,KAAK,KAAK,CAAC,4BACX,oJAAqB,CAAC,oDAAoD;IAC5E,MAAM,mBACJ,KAAK,KAAK,CAAC,kBACX,+IAAgB,CAAC,gCAAgC;IACnD,MAAM,gBACJ,KAAK,KAAK,CAAC,eAAe,qJAAsB,CAAC,QAAQ;IAE3D,IAAA,kNAAS,EAAC;QACR,MAAM,yBAAyB,CAAC;YAI9B,OAAO,2BAA2B,2JAA4B,CAAC,GAAG,GAC9D,eAAe,GAAG,GAClB,2BAA2B,2JAA4B,CAAC,GAAG,GACzD,eAAe,GAAG,GAClB,CAAC,eAAe,GAAG,GAAG,eAAe,GAAG,IAAI;QACpD;QAEA,MAAM,mBACJ,iKAAkC,CAAC,uBAAuB;QAC5D,KAAK,QAAQ,CAAC,oBAAoB;QAElC,MAAM,8BACJ,iKAAkC,CAAC,+BAA+B,CAChE,wBACD;QACH,KAAK,QAAQ,CAAC,uBAAuB;QAErC,MAAM,0CACJ,sKAAuC,CAAC,iBAAiB;QAE3D,MAAM,oCAAoC,uBACxC;QAEF,KAAK,QAAQ,CACX,qCACA;QAGF,MAAM,4BACJ,oCACA,8BACA;QACF,KAAK,QAAQ,CAAC,6BAA6B;QAE3C,MAAM,qCACJ,gKAAiC,CAAC,iBAAiB;QACrD,MAAM,8BAA8B,uBAClC;QAEF,KAAK,QAAQ,CAAC,+BAA+B;QAE7C,MAAM,sBACJ,8BACA,mBACA;QACF,KAAK,QAAQ,CAAC,uBAAuB;QAErC,MAAM,gCACJ,0KAA2C,CAAC,iBAAiB;QAC/D,MAAM,0BACJ,CAAC,8BAA8B,GAAG,GAAG,8BAA8B,GAAG,IACtE;QACF,KAAK,QAAQ,CAAC,kBAAkB;QAEhC,MAAM,+BACJ,yKAA0C,CAAC,iBAAiB;QAC9D,MAAM,yBACJ,CAAC,6BAA6B,GAAG,GAAG,6BAA6B,GAAG,IAAI;QAC1E,KAAK,QAAQ,CAAC,gBAAgB;QAE9B,WAAW;QACX,MAAM,2CACJ,4BAA4B;QAC9B,KAAK,QAAQ,CACX,4CACA;QAGF,MAAM,4BAA4B;QAClC,KAAK,QAAQ,CAAC,6BAA6B;QAE3C,MAAM,mBACJ,kBAAkB,qJAAsB,CAAC,QAAQ,GAC7C,MACA,kBAAkB,qJAAsB,CAAC,QAAQ,GAC/C,MACA;QACR,KAAK,QAAQ,CAAC,oBAAoB;IACpC,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;KACD;IAED,IAAA,kNAAS,EAAC;QACR,MAAM,eAAe,KAAK,KAAK,CAAC,CAAA;YAC9B,UAAU,CAAC,OAAc,CAAC;oBACxB,GAAG,IAAI;oBACP,yBAAyB;gBAC3B,CAAC;QACH;QACA,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG;QAAC;QAAM;KAAU;IAEpB,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,qJAAsB;oBAC/B,eAAe,CAAA,IAAK,EAAE,CAAC,yBAAyB,EAAE,GAAG;oBACrD,GAAG;;;;;;8BAEL,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,sJAAuB;oBAChC,eAAe,CAAA,IAAK,EAAE,CAAC,YAAY,EAAE,GAAG;oBACxC,GAAG;;;;;;8BAEL,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,8JAA+B;oBACxC,eAAe,CAAA,IAAK,EAAE,CAAC,eAAe,EAAE,GAAG;oBAC3C,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,qJAAsB;oBAC/B,eAAe,CAAA,IAAK,EAAE,CAAC,eAAe,EAAE,GAAG;oBAC3C,GAAG;;;;;;8BAEL,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,6JAA8B;oBACvC,eAAe,CAAA,IAAK,EAAE,CAAC,sBAAsB,EAAE,GAAG;oBAClD,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,uJAAwB;oBACjC,eAAe,CAAA,IAAK,EAAE,CAAC,SAAS,EAAE,GAAG;oBACrC,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,4IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,8OAAC,oOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 11555, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-panel-geometry-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type MaschiMurariPanelGeometrySchemaInputs,\r\n  type MaschiMurariParamsSchemaInputs,\r\n  maschiMurariPanelGeometrySchema,\r\n} from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<MaschiMurariPanelGeometrySchemaInputs>\r\n  params: MaschiMurariParamsSchemaInputs\r\n  setParams: (newParams: any) => void\r\n  onSave: () => void\r\n}\r\n\r\nexport const MaschiMurariPanelGeometryForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  params,\r\n  setParams,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.maschi-murari.panel-geometry')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const form = useForm<MaschiMurariPanelGeometrySchemaInputs>({\r\n    resolver: zodResolver(maschiMurariPanelGeometrySchema),\r\n    defaultValues: {\r\n      height: defaultValues?.height ?? 0,\r\n      width: defaultValues?.width ?? 0,\r\n      thickness: defaultValues?.thickness ?? 0,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: MaschiMurariPanelGeometrySchemaInputs) => {\r\n      // construct body based on global schema:\r\n      const maschiMurariParams: MaschiMurariParamsSchemaInputs = {\r\n        ...params,\r\n        panelGeometry: body,\r\n      }\r\n      mutate({ projectId, moduleId, body: maschiMurariParams })\r\n    },\r\n    [mutate, projectId, moduleId, params],\r\n  )\r\n\r\n  useEffect(() => {\r\n    const subscription = form.watch(values => {\r\n      setParams((prev: any) => ({\r\n        ...prev,\r\n        buildingCharacteristics: values,\r\n      }))\r\n    })\r\n    return () => subscription.unsubscribe()\r\n  }, [form, setParams])\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <Image\r\n          src=\"/assets/masonry_frcm/FRCM_Muratura.jpg\"\r\n          alt=\"site characteristics\"\r\n          height={250}\r\n          width={500}\r\n          className=\"mx-auto rounded-md object-contain\"\r\n          priority\r\n        />\r\n        <NumberFormInput control={form.control} name=\"height\" t={t} />\r\n        <NumberFormInput control={form.control} name=\"width\" t={t} />\r\n        <NumberFormInput control={form.control} name=\"thickness\" t={t} />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAGA;AACA;AAKA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;AAYO,MAAM,gCAAgC,CAAC,EAC5C,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACN,SAAS,EACT,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,OAAO,IAAA,yKAAO,EAAwC;QAC1D,UAAU,IAAA,6KAAW,EAAC,iMAA+B;QACrD,eAAe;YACb,QAAQ,eAAe,UAAU;YACjC,OAAO,eAAe,SAAS;YAC/B,WAAW,eAAe,aAAa;QACzC;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAW;wBAAE;wBAAW;oBAAS;iBAAE;YAChD;YACA;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,yCAAyC;QACzC,MAAM,qBAAqD;YACzD,GAAG,MAAM;YACT,eAAe;QACjB;QACA,OAAO;YAAE;YAAW;YAAU,MAAM;QAAmB;IACzD,GACA;QAAC;QAAQ;QAAW;QAAU;KAAO;IAGvC,IAAA,kNAAS,EAAC;QACR,MAAM,eAAe,KAAK,KAAK,CAAC,CAAA;YAC9B,UAAU,CAAC,OAAc,CAAC;oBACxB,GAAG,IAAI;oBACP,yBAAyB;gBAC3B,CAAC;QACH;QACA,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG;QAAC;QAAM;KAAU;IAEpB,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,8OAAC,wIAAK;oBACJ,KAAI;oBACJ,KAAI;oBACJ,QAAQ;oBACR,OAAO;oBACP,WAAU;oBACV,QAAQ;;;;;;8BAEV,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAS,GAAG;;;;;;8BACzD,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAQ,GAAG;;;;;;8BACxD,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAY,GAAG;;;;;;8BAC5D,8OAAC,4IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,8OAAC,oOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 11730, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx"], "sourcesContent": ["import {\r\n  Accordion as AccordionComponent,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from '@atlas/components/ui/accordion'\r\nimport type {\r\n  Module,\r\n  ModuleWithParamsMaschiMurari,\r\n} from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { MaschiMurariParamsSchemaInputs } from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useState } from 'react'\r\nimport { MaschiMurariFlexuralCalculation } from './calculations/flexural/maschi-murari-reinforcement-flexural-calculation'\r\nimport { MaschiMurariShearCalculation } from './calculations/shear/maschi-murari-reinforcement-shear-calculation'\r\nimport { MaschiMurariActionsForm } from './sections/maschi-murari-actions-form'\r\nimport { MaschiMurariMaterialPropertiesForm } from './sections/maschi-murari-material-properties-form'\r\nimport { MaschiMurariPanelGeometryForm } from './sections/maschi-murari-panel-geometry-form'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  module: ModuleWithParamsMaschiMurari\r\n}\r\n\r\nexport const MaschiMurariParamsForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  module,\r\n}: Props) => {\r\n  const [params, setParams] = useState<MaschiMurariParamsSchemaInputs>(\r\n    module?.params ?? ({} as MaschiMurariParamsSchemaInputs),\r\n  )\r\n\r\n  const [openItems, setOpenItems] = useState(['0'])\r\n  const t = useTranslations('forms.project-params.maschi-murari')\r\n\r\n  const handleItemSaved = useCallback((id: string) => {\r\n    const nextId = String(Number(id) + 1)\r\n    setOpenItems(old => {\r\n      const temp = old.filter(v => v !== id)\r\n      return old.includes(nextId) ? temp : [...temp, nextId]\r\n    })\r\n  }, [])\r\n\r\n  return (\r\n    <div>\r\n      <AccordionComponent\r\n        type=\"multiple\"\r\n        value={openItems}\r\n        onValueChange={setOpenItems}\r\n      >\r\n        <AccordionItem value=\"0\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">{t('panel-geometry.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <MaschiMurariPanelGeometryForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={params?.panelGeometry}\r\n              setParams={setParams}\r\n              params={params}\r\n              onSave={() => handleItemSaved('0')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"1\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('material-properties.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <MaschiMurariMaterialPropertiesForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={params?.materialProperties}\r\n              setParams={setParams}\r\n              params={params}\r\n              onSave={() => handleItemSaved('1')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"2\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">{t('actions.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <MaschiMurariActionsForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={params.actions}\r\n              params={params}\r\n              setParams={setParams}\r\n              onSave={() => handleItemSaved('2')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem\r\n          value=\"3\"\r\n          disabled={\r\n            !module?.params?.panelGeometry ||\r\n            !module?.params?.materialProperties ||\r\n            !module?.params?.actions\r\n          }\r\n        >\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">{t('shear.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <MaschiMurariShearCalculation\r\n              session={session}\r\n              projectId={projectId}\r\n              module={module}\r\n              onNext={() => handleItemSaved('3')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem\r\n          value=\"4\"\r\n          disabled={\r\n            !module?.params?.panelGeometry ||\r\n            !module?.params?.materialProperties ||\r\n            !module?.params?.actions\r\n          }\r\n        >\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">{t('flexural.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <MaschiMurariFlexuralCalculation\r\n              session={session}\r\n              projectId={projectId}\r\n              module={module}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n      </AccordionComponent>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AASO,MAAM,yBAAyB,CAAC,EACrC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,MAAM,EACA;IACN,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,iNAAQ,EAClC,QAAQ,UAAW,CAAC;IAGtB,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;QAAC;KAAI;IAChD,MAAM,IAAI,IAAA,yNAAe,EAAC;IAE1B,MAAM,kBAAkB,IAAA,oNAAW,EAAC,CAAC;QACnC,MAAM,SAAS,OAAO,OAAO,MAAM;QACnC,aAAa,CAAA;YACX,MAAM,OAAO,IAAI,MAAM,CAAC,CAAA,IAAK,MAAM;YACnC,OAAO,IAAI,QAAQ,CAAC,UAAU,OAAO;mBAAI;gBAAM;aAAO;QACxD;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;kBACC,cAAA,8OAAC,kJAAkB;YACjB,MAAK;YACL,OAAO;YACP,eAAe;;8BAEf,8OAAC,sJAAa;oBAAC,OAAM;;sCACnB,8OAAC,yJAAgB;sCACf,cAAA,8OAAC;gCAAG,WAAU;0CAAuB,EAAE;;;;;;;;;;;sCAEzC,8OAAC,yJAAgB;sCACf,cAAA,8OAAC,ySAA6B;gCAC5B,SAAS;gCACT,WAAW;gCACX,UAAU;gCACV,eAAe,QAAQ;gCACvB,WAAW;gCACX,QAAQ;gCACR,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,8OAAC,sJAAa;oBAAC,OAAM;;sCACnB,8OAAC,yJAAgB;sCACf,cAAA,8OAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;;;;;;sCAGP,8OAAC,yJAAgB;sCACf,cAAA,8OAAC,mTAAkC;gCACjC,SAAS;gCACT,WAAW;gCACX,UAAU;gCACV,eAAe,QAAQ;gCACvB,WAAW;gCACX,QAAQ;gCACR,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,8OAAC,sJAAa;oBAAC,OAAM;;sCACnB,8OAAC,yJAAgB;sCACf,cAAA,8OAAC;gCAAG,WAAU;0CAAuB,EAAE;;;;;;;;;;;sCAEzC,8OAAC,yJAAgB;sCACf,cAAA,8OAAC,yRAAuB;gCACtB,SAAS;gCACT,WAAW;gCACX,UAAU;gCACV,eAAe,OAAO,OAAO;gCAC7B,QAAQ;gCACR,WAAW;gCACX,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,8OAAC,sJAAa;oBACZ,OAAM;oBACN,UACE,CAAC,QAAQ,QAAQ,iBACjB,CAAC,QAAQ,QAAQ,sBACjB,CAAC,QAAQ,QAAQ;;sCAGnB,8OAAC,yJAAgB;sCACf,cAAA,8OAAC;gCAAG,WAAU;0CAAuB,EAAE;;;;;;;;;;;sCAEzC,8OAAC,yJAAgB;sCACf,cAAA,8OAAC,iUAA4B;gCAC3B,SAAS;gCACT,WAAW;gCACX,QAAQ;gCACR,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,8OAAC,sJAAa;oBACZ,OAAM;oBACN,UACE,CAAC,QAAQ,QAAQ,iBACjB,CAAC,QAAQ,QAAQ,sBACjB,CAAC,QAAQ,QAAQ;;sCAGnB,8OAAC,yJAAgB;sCACf,cAAA,8OAAC;gCAAG,WAAU;0CAAuB,EAAE;;;;;;;;;;;sCAEzC,8OAAC,yJAAgB;sCACf,cAAA,8OAAC,0UAA+B;gCAC9B,SAAS;gCACT,WAAW;gCACX,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB", "debugId": null}}]}