import { Badge } from '@atlas/components/ui/badge'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@atlas/components/ui/card'
import { cn } from '@atlas/lib/utils'
import type { ReinforcedSectionSchema } from '@atlas/types/schemas/masonry/frcm-column-form'
import { useLocale, useTranslations } from 'next-intl'

type Props = {
  reinforcedSectionResults: ReinforcedSectionSchema
}

export function ConfinementReinforcedSectionResultCard({
  reinforcedSectionResults,
}: Props) {
  const {
    coefficientOfResistanceIncrease,
    confinedColumnDesignResistanceWithFrcm,
    designAxialResistanceOfTheConfinedColumnWithFrcm,
    check,
  } = reinforcedSectionResults

  const t = useTranslations(
    'components.calculations.frcm-column.confinementReinforcementCalculationResult.confinementReinforcedSectionResult',
  )
  const locale = useLocale()

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('title')}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <span className="font-medium">
            {t('coefficientOfResistanceIncrease.label')}:
          </span>{' '}
          <span>
            {coefficientOfResistanceIncrease?.toLocaleString(locale, {
              maximumFractionDigits: 3,
            })}{' '}
          </span>
        </div>
        <div>
          <span className="font-medium">
            {t('confinedColumnDesignResistanceWithFrcm.label')}:
          </span>{' '}
          <span>
            {confinedColumnDesignResistanceWithFrcm?.toLocaleString(locale, {
              maximumFractionDigits: 3,
            })}{' '}
          </span>
        </div>
        <div>
          <div>
            <span className="font-medium">
              {t('designAxialResistanceOfTheConfinedColumnWithFrcm.label')}:
            </span>{' '}
            <span>
              {designAxialResistanceOfTheConfinedColumnWithFrcm?.toLocaleString(
                locale,
                {
                  maximumFractionDigits: 3,
                },
              )}{' '}
            </span>
          </div>
          <span className="font-medium">{t('check.label')}:</span>{' '}
          <Badge
            className={cn(
              'text-base px-3 py-1',
              check ? 'bg-green-600' : 'bg-red-600',
            )}
          >
            {check ? t('check.satisfied') : t('check.notSatisfied')}
          </Badge>
        </div>
      </CardContent>
    </Card>
  )
}
