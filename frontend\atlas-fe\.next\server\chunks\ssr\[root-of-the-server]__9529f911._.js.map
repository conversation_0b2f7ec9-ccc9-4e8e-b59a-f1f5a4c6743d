{"version": 3, "sources": [], "sections": [{"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/api/auth/doFinalSignOutHandshake.ts"], "sourcesContent": ["import axios, { type AxiosError } from 'axios'\r\nimport type { JWT } from 'next-auth/jwt'\r\n\r\n// this performs the final handshake for the keycloak\r\n// provider, the way it's written could also potentially\r\n// perform the action for other providers as well\r\nexport const doFinalSignOutHandshake = async (jwt: JWT) => {\r\n  const { idToken } = jwt\r\n\r\n  try {\r\n    // Add the id_token_hint to the query string\r\n    const params = new URLSearchParams()\r\n    params.append('id_token_hint', idToken)\r\n    const { status, statusText } = await axios.get(\r\n      `${process.env.AUTH_KEYCLOAK_ISSUER}/protocol/openid-connect/logout?${params.toString()}`,\r\n    )\r\n\r\n    // The response body should contain a confirmation that the user has been logged out\r\n    console.debug('Completed post-logout handshake', status, statusText)\r\n  } catch (e: any) {\r\n    console.error(\r\n      'Unable to perform post-logout handshake',\r\n      (e as AxiosError)?.code || e,\r\n    )\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;AAMO,MAAM,0BAA0B,OAAO;IAC5C,MAAM,EAAE,OAAO,EAAE,GAAG;IAEpB,IAAI;QACF,4CAA4C;QAC5C,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,CAAC,iBAAiB;QAC/B,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,gJAAK,CAAC,GAAG,CAC5C,GAAG,QAAQ,GAAG,CAAC,oBAAoB,CAAC,gCAAgC,EAAE,OAAO,QAAQ,IAAI;QAG3F,oFAAoF;QACpF,QAAQ,KAAK,CAAC,mCAAmC,QAAQ;IAC3D,EAAE,OAAO,GAAQ;QACf,QAAQ,KAAK,CACX,2CACA,AAAC,GAAkB,QAAQ;IAE/B;AACF", "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/api/auth/refreshAccessToken.ts"], "sourcesContent": ["import type { JWT } from 'next-auth/jwt'\r\n\r\nexport const refreshAccessToken = async (token: JWT): Promise<JWT> => {\r\n  try {\r\n    if (\r\n      token.refreshTokenExpires &&\r\n      Date.now() >= token.refreshTokenExpires * 1000\r\n    ) {\r\n      return {\r\n        ...token,\r\n        error: 'RefreshTokenExpired',\r\n        errorDetails: 'The refresh token itself has expired.',\r\n      }\r\n    }\r\n\r\n    if (!token.refreshToken) {\r\n      throw new Error('MissingRefreshToken')\r\n    }\r\n\r\n    const clientId = process.env.AUTH_KEYCLOAK_ID\r\n    const clientSecret = process.env.AUTH_KEYCLOAK_SECRET\r\n    const tokenUrl = `${process.env.AUTH_KEYCLOAK_ISSUER}/protocol/openid-connect/token`\r\n\r\n    if (!(clientId && tokenUrl)) {\r\n      throw new Error('MissingKeycloakConfig')\r\n    }\r\n\r\n    const params = new URLSearchParams()\r\n    params.append('client_id', clientId)\r\n    if (clientSecret) {\r\n      params.append('client_secret', clientSecret)\r\n    }\r\n    params.append('grant_type', 'refresh_token')\r\n    params.append('refresh_token', token.refreshToken)\r\n\r\n    console.log('Try to refresh access token...')\r\n    const response = await fetch(tokenUrl, {\r\n      method: 'POST',\r\n      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },\r\n      body: params.toString(),\r\n    })\r\n\r\n    const refreshedTokens = await response.json()\r\n\r\n    if (!response.ok) {\r\n      console.error('Error during refresh token:', refreshedTokens)\r\n      if (refreshedTokens.error === 'invalid_grant') {\r\n        return {\r\n          ...token,\r\n          error: 'RefreshTokenInvalidated',\r\n          errorDetails:\r\n            refreshedTokens.error_description ||\r\n            'Refresh token validation failed on server.',\r\n        }\r\n      }\r\n      throw new Error(refreshedTokens.error || 'RefreshFailed')\r\n    }\r\n\r\n    const nowInSeconds = Math.floor(Date.now() / 1000)\r\n    const newAccessTokenExpiresAt = nowInSeconds + refreshedTokens.expires_in\r\n    let newRefreshTokenExpiresAt = token.refreshTokenExpires\r\n    if (typeof refreshedTokens.refreshTokenExpires === 'number') {\r\n      newRefreshTokenExpiresAt =\r\n        nowInSeconds + refreshedTokens.refreshTokenExpires\r\n    }\r\n\r\n    return {\r\n      ...token,\r\n      accessToken: refreshedTokens.access_token,\r\n      idToken: refreshedTokens.id_token,\r\n      accessTokenExpires: newAccessTokenExpiresAt,\r\n      refreshToken: refreshedTokens.refresh_token ?? token.refreshToken,\r\n      refreshTokenExpires: newRefreshTokenExpiresAt,\r\n      error: undefined,\r\n      errorDetails: undefined,\r\n    }\r\n  } catch (error: any) {\r\n    console.error('Exception during refresh token:', error)\r\n    return {\r\n      ...token,\r\n      error: 'RefreshAccessTokenError',\r\n      errorDetails: error.message || 'UnknownError',\r\n    }\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAEO,MAAM,qBAAqB,OAAO;IACvC,IAAI;QACF,IACE,MAAM,mBAAmB,IACzB,KAAK,GAAG,MAAM,MAAM,mBAAmB,GAAG,MAC1C;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,OAAO;gBACP,cAAc;YAChB;QACF;QAEA,IAAI,CAAC,MAAM,YAAY,EAAE;YACvB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,QAAQ,GAAG,CAAC,gBAAgB;QAC7C,MAAM,eAAe,QAAQ,GAAG,CAAC,oBAAoB;QACrD,MAAM,WAAW,GAAG,QAAQ,GAAG,CAAC,oBAAoB,CAAC,8BAA8B,CAAC;QAEpF,IAAI,CAAC,CAAC,YAAY,QAAQ,GAAG;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,CAAC,aAAa;QAC3B,IAAI,cAAc;YAChB,OAAO,MAAM,CAAC,iBAAiB;QACjC;QACA,OAAO,MAAM,CAAC,cAAc;QAC5B,OAAO,MAAM,CAAC,iBAAiB,MAAM,YAAY;QAEjD,QAAQ,GAAG,CAAC;QACZ,MAAM,WAAW,MAAM,MAAM,UAAU;YACrC,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAoC;YAC/D,MAAM,OAAO,QAAQ;QACvB;QAEA,MAAM,kBAAkB,MAAM,SAAS,IAAI;QAE3C,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,IAAI,gBAAgB,KAAK,KAAK,iBAAiB;gBAC7C,OAAO;oBACL,GAAG,KAAK;oBACR,OAAO;oBACP,cACE,gBAAgB,iBAAiB,IACjC;gBACJ;YACF;YACA,MAAM,IAAI,MAAM,gBAAgB,KAAK,IAAI;QAC3C;QAEA,MAAM,eAAe,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;QAC7C,MAAM,0BAA0B,eAAe,gBAAgB,UAAU;QACzE,IAAI,2BAA2B,MAAM,mBAAmB;QACxD,IAAI,OAAO,gBAAgB,mBAAmB,KAAK,UAAU;YAC3D,2BACE,eAAe,gBAAgB,mBAAmB;QACtD;QAEA,OAAO;YACL,GAAG,KAAK;YACR,aAAa,gBAAgB,YAAY;YACzC,SAAS,gBAAgB,QAAQ;YACjC,oBAAoB;YACpB,cAAc,gBAAgB,aAAa,IAAI,MAAM,YAAY;YACjE,qBAAqB;YACrB,OAAO;YACP,cAAc;QAChB;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;YACL,GAAG,KAAK;YACR,OAAO;YACP,cAAc,MAAM,OAAO,IAAI;QACjC;IACF;AACF", "debugId": null}}, {"offset": {"line": 186, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/auth.ts"], "sourcesContent": ["import { doFinalSignOutHandshake } from '@atlas/lib/api/auth/doFinalSignOutHandshake'\r\nimport { refreshAccessToken } from '@atlas/lib/api/auth/refreshAccessToken'\r\nimport type { AdapterSession } from '@auth/core/adapters'\r\nimport NextAuth, { type Session } from 'next-auth'\r\nimport type { JWT } from 'next-auth/jwt'\r\nimport Keycloak from 'next-auth/providers/keycloak'\r\n\r\nconst ACCESS_TOKEN_BUFFER_SECONDS = 60\r\n\r\nexport const { handlers, signIn, signOut, auth } = NextAuth({\r\n  providers: [Keycloak],\r\n\r\n  callbacks: {\r\n    /**\r\n     * Controlla se l'utente è autorizzato ad accedere.\r\n     * Utile per middleware o protezione server-side.\r\n     *\r\n     */\r\n    authorized({ auth }) {\r\n      return !!auth\r\n    },\r\n\r\n    /**\r\n     * Callback JWT: Gestisce la creazione e l'aggiornamento del token JWT.\r\n     * Eseguito al login iniziale e ad ogni richiesta successiva che usa il JWT.\r\n     */\r\n    async jwt({ token, account, user }) {\r\n      const now = Math.floor(Date.now() / 1000)\r\n\r\n      if (account?.access_token && account?.id_token && user) {\r\n        console.debug('[AUTH] jwt - Initial sign-in.')\r\n\r\n        const accessTokenExpires =\r\n          account.expires_at ?? now + (account.expires_in ?? 300)\r\n\r\n        let refreshTokenExpires: number | undefined\r\n        if (typeof account.refresh_expires_in === 'number') {\r\n          refreshTokenExpires = now + account.refresh_expires_in\r\n          console.debug(\r\n            `[AUTH] jwt - Refresh token initial expiry: ${new Date(refreshTokenExpires * 1000).toISOString()}`,\r\n          )\r\n        }\r\n\r\n        return {\r\n          ...token,\r\n          accessToken: account.access_token,\r\n          idToken: account.id_token,\r\n          refreshToken: account.refresh_token,\r\n          accessTokenExpires: accessTokenExpires,\r\n          refreshTokenExpires: refreshTokenExpires,\r\n          user: user,\r\n          error: undefined,\r\n        }\r\n      }\r\n\r\n      if (token.refreshTokenExpires && now >= token.refreshTokenExpires) {\r\n        console.debug(\r\n          '[AUTH] jwt - Refresh token expired. Invalidating session.',\r\n        )\r\n        return null\r\n      }\r\n\r\n      const isAccessTokenValid =\r\n        token.accessTokenExpires &&\r\n        now < token.accessTokenExpires - ACCESS_TOKEN_BUFFER_SECONDS\r\n\r\n      if (isAccessTokenValid) {\r\n        return token\r\n      }\r\n\r\n      console.debug(\r\n        '[AUTH] jwt - Access token expired or requires refresh. Attempting refresh...',\r\n      )\r\n\r\n      if (!token.refreshToken) {\r\n        console.debug('[AUTH] jwt - Cannot refresh: Missing refresh token.')\r\n        return null\r\n      }\r\n\r\n      try {\r\n        const refreshedToken = await refreshAccessToken(token)\r\n        if (\r\n          refreshedToken.error === 'RefreshTokenExpired' ||\r\n          refreshedToken.error === 'RefreshTokenInvalidated'\r\n        ) {\r\n          console.debug(\r\n            `[AUTH] jwt - Refresh failed (${refreshedToken.error}). Invalidating session.`,\r\n          )\r\n          return null\r\n        }\r\n\r\n        return refreshedToken\r\n      } catch (error) {\r\n        console.error('[AUTH] jwt - Exception during token refresh:', error)\r\n        return null\r\n      }\r\n    },\r\n\r\n    /**\r\n     * Callback Session: Gestisce l'oggetto sessione accessibile lato client.\r\n     * Riceve il token JWT aggiornato dalla callback `jwt`.\r\n     */\r\n    session({ session, token }: { session: Session; token: JWT }): Session {\r\n      session.accessToken = token.accessToken\r\n      session.idToken = token.idToken\r\n      session.user = token.user\r\n      session.error = token.error\r\n      session.accessTokenExpires = token.accessTokenExpires\r\n\r\n      if (session.error) {\r\n        console.warn(\r\n          `[AUTH] session - Propagating token error to client session: ${session.error}`,\r\n        )\r\n      }\r\n\r\n      return session\r\n    },\r\n  },\r\n  session: {\r\n    strategy: 'jwt',\r\n  },\r\n  events: {\r\n    signOut: (\r\n      message:\r\n        | { token: JWT | null }\r\n        // biome-ignore lint/suspicious/noConfusingVoidType: necessary\r\n        | { session: void | AdapterSession | null },\r\n    ) => {\r\n      if ('token' in message && message.token) {\r\n        return doFinalSignOutHandshake(message.token)\r\n      }\r\n      return Promise.resolve()\r\n    },\r\n  },\r\n})\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AAEA;AAEA;AAAA;;;;;AAEA,MAAM,8BAA8B;AAE7B,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,IAAA,gKAAQ,EAAC;IAC1D,WAAW;QAAC,kKAAQ;KAAC;IAErB,WAAW;QACT;;;;KAIC,GACD,YAAW,EAAE,IAAI,EAAE;YACjB,OAAO,CAAC,CAAC;QACX;QAEA;;;KAGC,GACD,MAAM,KAAI,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE;YAChC,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;YAEpC,IAAI,SAAS,gBAAgB,SAAS,YAAY,MAAM;gBACtD,QAAQ,KAAK,CAAC;gBAEd,MAAM,qBACJ,QAAQ,UAAU,IAAI,MAAM,CAAC,QAAQ,UAAU,IAAI,GAAG;gBAExD,IAAI;gBACJ,IAAI,OAAO,QAAQ,kBAAkB,KAAK,UAAU;oBAClD,sBAAsB,MAAM,QAAQ,kBAAkB;oBACtD,QAAQ,KAAK,CACX,CAAC,2CAA2C,EAAE,IAAI,KAAK,sBAAsB,MAAM,WAAW,IAAI;gBAEtG;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,aAAa,QAAQ,YAAY;oBACjC,SAAS,QAAQ,QAAQ;oBACzB,cAAc,QAAQ,aAAa;oBACnC,oBAAoB;oBACpB,qBAAqB;oBACrB,MAAM;oBACN,OAAO;gBACT;YACF;YAEA,IAAI,MAAM,mBAAmB,IAAI,OAAO,MAAM,mBAAmB,EAAE;gBACjE,QAAQ,KAAK,CACX;gBAEF,OAAO;YACT;YAEA,MAAM,qBACJ,MAAM,kBAAkB,IACxB,MAAM,MAAM,kBAAkB,GAAG;YAEnC,IAAI,oBAAoB;gBACtB,OAAO;YACT;YAEA,QAAQ,KAAK,CACX;YAGF,IAAI,CAAC,MAAM,YAAY,EAAE;gBACvB,QAAQ,KAAK,CAAC;gBACd,OAAO;YACT;YAEA,IAAI;gBACF,MAAM,iBAAiB,MAAM,IAAA,qKAAkB,EAAC;gBAChD,IACE,eAAe,KAAK,KAAK,yBACzB,eAAe,KAAK,KAAK,2BACzB;oBACA,QAAQ,KAAK,CACX,CAAC,6BAA6B,EAAE,eAAe,KAAK,CAAC,wBAAwB,CAAC;oBAEhF,OAAO;gBACT;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gDAAgD;gBAC9D,OAAO;YACT;QACF;QAEA;;;KAGC,GACD,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAoC;YAC1D,QAAQ,WAAW,GAAG,MAAM,WAAW;YACvC,QAAQ,OAAO,GAAG,MAAM,OAAO;YAC/B,QAAQ,IAAI,GAAG,MAAM,IAAI;YACzB,QAAQ,KAAK,GAAG,MAAM,KAAK;YAC3B,QAAQ,kBAAkB,GAAG,MAAM,kBAAkB;YAErD,IAAI,QAAQ,KAAK,EAAE;gBACjB,QAAQ,IAAI,CACV,CAAC,4DAA4D,EAAE,QAAQ,KAAK,EAAE;YAElF;YAEA,OAAO;QACT;IACF;IACA,SAAS;QACP,UAAU;IACZ;IACA,QAAQ;QACN,SAAS,CACP;YAKA,IAAI,WAAW,WAAW,QAAQ,KAAK,EAAE;gBACvC,OAAO,IAAA,+KAAuB,EAAC,QAAQ,KAAK;YAC9C;YACA,OAAO,QAAQ,OAAO;QACxB;IACF;AACF", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\r\nimport { twMerge } from 'tailwind-merge'\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/button.tsx"], "sourcesContent": ["import { cn } from '@atlas/lib/utils'\r\nimport { Slot } from '@radix-ui/react-slot'\r\nimport { cva, type VariantProps } from 'class-variance-authority'\r\nimport type * as React from 'react'\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\r\n        destructive:\r\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\r\n        secondary:\r\n          'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\r\n        ghost:\r\n          'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\r\n        link: 'text-primary underline-offset-4 hover:underline',\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\r\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\r\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\r\n        icon: 'size-9',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default',\r\n    },\r\n  },\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : 'button'\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;;AAGA,MAAM,iBAAiB,IAAA,uKAAG,EACxB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,wKAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 370, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/login-form.tsx"], "sourcesContent": ["import { signIn } from '@atlas/auth'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Link } from '@atlas/i18n/routing'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport { getLocale, getTranslations } from 'next-intl/server'\r\n\r\nexport const LoginForm = async ({\r\n  className,\r\n}: React.ComponentPropsWithoutRef<'div'>) => {\r\n  const t = await getTranslations('components.login-form')\r\n  const locale = await getLocale()\r\n\r\n  return (\r\n    <div className={cn('flex flex-col gap-6', className)}>\r\n      <div className=\"flex flex-col items-center gap-2 text-center\">\r\n        <h1 className=\"text-2xl font-bold\"> {t('title')}</h1>\r\n        <p className=\"text-balance text-sm text-muted-foreground\">\r\n          {t('description')}\r\n        </p>\r\n      </div>\r\n      <div className=\"grid gap-6\">\r\n        <Button\r\n          className=\"w-full\"\r\n          onClick={async () => {\r\n            'use server'\r\n            await signIn('keycloak', { redirectTo: '/dashboard' })\r\n          }}\r\n        >\r\n          {t('buttons.sign-in.label')}\r\n        </Button>\r\n        <Link\r\n          href={`${process.env.NEXT_PUBLIC_AUTH_KEYCLOAK_ISSUER}/login-actions/reset-credentials`}\r\n          className=\"text-sm text-muted-foreground underline text-center\"\r\n        >\r\n          {t('buttons.forgot-password.label')}\r\n        </Link>\r\n      </div>\r\n      <div className=\"flex flex-row gap-1 text-sm items-center justify-center\">\r\n        {t('labels.dont-have-an-account')}\r\n        <Button variant=\"ghost\" asChild>\r\n          <Link\r\n            href={`${process.env.NEXT_PUBLIC_AUTH_KEYCLOAK_ISSUER}/protocol/openid-connect/registrations?client_id=${process.env.NEXT_PUBLIC_AUTH_CLIENT_ID}&response_type=code&scope=openid&redirect_uri=${process.env.NEXT_PUBLIC_AUTH_REDIRECT_URI}&kc_locale=${locale}`}\r\n          >\r\n            {t('buttons.sign-up.label')}\r\n          </Link>\r\n        </Button>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AACA;AAAA;;;;;;;;MAmBmB,uCAAT;IAEE,MAAM,IAAA,qHAAM,EAAC,YAAY;QAAE,YAAY;IAAa;AACtD;AAHS,+OAAA;AAjBZ,MAAM,YAAY,OAAO,EAC9B,SAAS,EAC6B;IACtC,MAAM,IAAI,MAAM,IAAA,2QAAe,EAAC;IAChC,MAAM,SAAS,MAAM,IAAA,yPAAS;IAE9B,qBACE,8OAAC;QAAI,WAAW,IAAA,yHAAE,EAAC,uBAAuB;;0BACxC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAAqB;4BAAE,EAAE;;;;;;;kCACvC,8OAAC;wBAAE,WAAU;kCACV,EAAE;;;;;;;;;;;;0BAGP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4IAAM;wBACL,WAAU;wBACV,SAAS;kCAKR,EAAE;;;;;;kCAEL,8OAAC,8HAAI;wBACH,MAAM,0EAAgD,gCAAgC,CAAC;wBACvF,WAAU;kCAET,EAAE;;;;;;;;;;;;0BAGP,8OAAC;gBAAI,WAAU;;oBACZ,EAAE;kCACH,8OAAC,4IAAM;wBAAC,SAAQ;wBAAQ,OAAO;kCAC7B,cAAA,8OAAC,8HAAI;4BACH,MAAM,0EAAgD,iDAAiD,4CAAyC,8CAA8C,4DAA4C,WAAW,EAAE,QAAQ;sCAE9P,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 495, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/.next-internal/server/app/%5Blocale%5D/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {$$RSC_SERVER_ACTION_0 as '0044f9ee86f66c90fe7b1aeac96677d3571a4ae54e'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/language-toggler.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const LanguageToggle = registerClientReference(\n    function() { throw new Error(\"Attempted to call LanguageToggle() from the server but LanguageToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/common/atlas/language-toggler.tsx <module evaluation>\",\n    \"LanguageToggle\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,iBAAiB,IAAA,wQAAuB,EACjD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,kFACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 538, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/language-toggler.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const LanguageToggle = registerClientReference(\n    function() { throw new Error(\"Attempted to call LanguageToggle() from the server but LanguageToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/common/atlas/language-toggler.tsx\",\n    \"LanguageToggle\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,iBAAiB,IAAA,wQAAuB,EACjD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 552, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 560, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/theme-mode-toggler.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeModeToggle = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeModeToggle() from the server but ThemeModeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/common/atlas/theme-mode-toggler.tsx <module evaluation>\",\n    \"ThemeModeToggle\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,kBAAkB,IAAA,wQAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,oFACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 574, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/theme-mode-toggler.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeModeToggle = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeModeToggle() from the server but ThemeModeToggle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/common/atlas/theme-mode-toggler.tsx\",\n    \"ThemeModeToggle\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,kBAAkB,IAAA,wQAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,gEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 588, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 596, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/pages/home/<USER>/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const HomePageLogo = registerClientReference(\n    function() { throw new Error(\"Attempted to call HomePageLogo() from the server but HomePageLogo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/pages/home/<USER>\",\n    \"HomePageLogo\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,eAAe,IAAA,wQAAuB,EAC/C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,8EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 610, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/pages/home/<USER>/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const HomePageLogo = registerClientReference(\n    function() { throw new Error(\"Attempted to call HomePageLogo() from the server but HomePageLogo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/pages/home/<USER>\",\n    \"HomePageLogo\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,eAAe,IAAA,wQAAuB,EAC/C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,0DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 624, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 632, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/pages/home/<USER>"], "sourcesContent": ["import { LanguageToggle } from '@atlas/components/common/atlas/language-toggler'\r\nimport { LoginForm } from '@atlas/components/common/atlas/login-form'\r\nimport { ThemeModeToggle } from '@atlas/components/common/atlas/theme-mode-toggler'\r\nimport Image from 'next/image'\r\nimport { getTranslations } from 'next-intl/server'\r\nimport { HomePageLogo } from './home-page-logo'\r\n\r\nexport const HomePage = async () => {\r\n  const t = await getTranslations('pages.home')\r\n\r\n  return (\r\n    <div className=\"grid min-h-svh lg:grid-cols-2\">\r\n      <div className=\"flex flex-col gap-4 p-6 md:p-10\">\r\n        <div className=\"flex gap-2 justify-between\">\r\n          <HomePageLogo />\r\n          <div className=\"flex gap-2 justify-between\">\r\n            <LanguageToggle />\r\n            <ThemeModeToggle />\r\n          </div>\r\n        </div>\r\n        <div className=\"flex flex-1 items-center justify-center\">\r\n          <div className=\"w-full max-w-xs\">\r\n            <LoginForm />\r\n          </div>\r\n        </div>\r\n      </div>\r\n      <div className=\"relative hidden bg-muted lg:block\">\r\n        <Image\r\n          fill\r\n          src=\"/assets/login.png\"\r\n          alt={t('images.main.alt')}\r\n          className=\"absolute inset-0 h-full w-full object-cover dark:brightness-[0.7] dark:grayscale\"\r\n        />\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEO,MAAM,WAAW;IACtB,MAAM,IAAI,MAAM,IAAA,2QAAe,EAAC;IAEhC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,2KAAY;;;;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8KAAc;;;;;kDACf,8OAAC,oLAAe;;;;;;;;;;;;;;;;;kCAGpB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,mKAAS;;;;;;;;;;;;;;;;;;;;;0BAIhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,wIAAK;oBACJ,IAAI;oBACJ,KAAI;oBACJ,KAAK,EAAE;oBACP,WAAU;;;;;;;;;;;;;;;;;AAKpB", "debugId": null}}, {"offset": {"line": 744, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/app/%5Blocale%5D/page.tsx"], "sourcesContent": ["import { HomePage } from '@atlas/components/pages/home/<USER>'\r\nimport { getTranslations } from 'next-intl/server'\r\n\r\nexport const generateMetadata = async ({\r\n  params,\r\n}: {\r\n  params: Promise<{ locale: string }>\r\n}) => {\r\n  const { locale } = await params\r\n  const t = await getTranslations({ locale, namespace: 'pages.home.meta' })\r\n\r\n  return {\r\n    title: t('title'),\r\n    description: t('description'),\r\n  }\r\n}\r\n\r\nexport default HomePage\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,MAAM,mBAAmB,OAAO,EACrC,MAAM,EAGP;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,IAAI,MAAM,IAAA,2QAAe,EAAC;QAAE;QAAQ,WAAW;IAAkB;IAEvE,OAAO;QACL,OAAO,EAAE;QACT,aAAa,EAAE;IACjB;AACF;uCAEe,+JAAQ", "debugId": null}}]}