'use client'

import { NumberFormInput } from '@atlas/components/common/form/number-form-input'
import { SelectFormInput } from '@atlas/components/common/form/select-form-input'
import { Button } from '@atlas/components/ui/button'
import { Card, CardContent } from '@atlas/components/ui/card'
import { Checkbox } from '@atlas/components/ui/checkbox'
import { Form } from '@atlas/components/ui/form'
import { Label } from '@atlas/components/ui/label'
import type { ModuleWithParamsSlab } from '@atlas/lib/api/modules/schemas/modules'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import { useModuleCalculation } from '@atlas/lib/mutation/modules/use-module-calculation'
import { useProductsByType } from '@atlas/lib/query/products/use-products-by-type'
import {
  type SlabShearCalculationInput,
  slabShearCalculationSchema,
} from '@atlas/types/schemas/slab-form'
import { zodResolver } from '@hookform/resolvers/zod'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useEffect, useId, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { SlabShearResultCard } from './slab-shear-result-card'

type Props = {
  module: ModuleWithParamsSlab
  session: Session
  projectId: Project['id']
}

const SECTION_FILL_TYPES = [
  { value: 'Full strip', label: 'Full Strip' },
  { value: 'Joist web', label: 'Joist Web' },
  { value: 'Semi-full strip', label: 'Semi-Full Strip' },
]

export const SlabShearCalculation = ({ session, module, projectId }: Props) => {
  const t = useTranslations('forms.calculations.slab.shear')
  const tAction = useTranslations('actions.calculations.slab.shear')
  const tCommon = useTranslations('actions.common')
  const checkboxId = useId()

  const { mutate, isPending } = useModuleCalculation(session.accessToken)

  // Get results from module
  const { shearVerifyExecutionInput, shearVerifyExecutionResult } = module

  // Debug logging
  console.log('🔍 Slab Shear Debug:', {
    shearVerifyExecutionInput: shearVerifyExecutionInput,
    shearVerifyExecutionResult: shearVerifyExecutionResult,
    'module keys': Object.keys(module),
  })

  // Get FRC product name from module params
  const frcProductName =
    module.params?.slabFrcReinforcement?.frcReinforcementType

  const {
    data: products,
    isError: errorGettingProducts,
    isLoading: isLoadingProducts,
  } = useProductsByType(session, 'SLAB', 0, 100)

  // Find product by name from FRC reinforcement
  const frcProduct = useMemo(
    () => products?.content.find(p => p.name === frcProductName),
    [frcProductName, products],
  )

  const form = useForm<SlabShearCalculationInput>({
    resolver: zodResolver(slabShearCalculationSchema),
    defaultValues: {
      calculationType: 'SHEAR_VERIFY',
      input: {
        sectionFillType:
          shearVerifyExecutionInput?.sectionFillType ?? 'Full strip',
        shearForce: shearVerifyExecutionInput?.shearForce ?? 30,
        isCantilever: shearVerifyExecutionInput?.isCantilever ?? false,
        productInput: {
          id: shearVerifyExecutionInput?.product?.id ?? frcProduct?.id ?? '',
          name:
            shearVerifyExecutionInput?.product?.name ?? frcProduct?.name ?? '',
          sourceType:
            shearVerifyExecutionInput?.product?.sourceType ??
            (frcProduct ? 'DATABASE' : 'CUSTOM'),
          frcSlabThickness:
            shearVerifyExecutionInput?.product?.thickness ??
            frcProduct?.thickness,
          elasticModulus:
            shearVerifyExecutionInput?.product?.elasticModulus ??
            frcProduct?.elasticModulus,
          cylindricCompressiveStrength:
            shearVerifyExecutionInput?.product?.cylindricCompressiveStrength ??
            frcProduct?.cylindricCompressiveStrength,
          characteristicTensileStrength:
            shearVerifyExecutionInput?.product?.characteristicTensileStrength ??
            frcProduct?.characteristicTensileStrength,
          specificWeight:
            shearVerifyExecutionInput?.product?.specificWeight ??
            frcProduct?.specificWeight,
          adhesionToConcrete:
            shearVerifyExecutionInput?.product?.adhesionToConcrete ??
            frcProduct?.adhesionToConcrete,
          fiberType:
            shearVerifyExecutionInput?.product?.fiberType ??
            frcProduct?.fiberType ??
            'CARBON',
        },
      },
    },
  })

  const productsOptions = [
    ...(products?.content.map(m => ({
      value: m.id,
      label: m.name ?? t('product.unnamed'),
    })) ?? []),
    { value: 'custom', label: t('product.custom') },
  ]

  const [productId] = form.watch(['input.productInput.id'])
  const selectedProduct = useMemo(
    () => products?.content.find(p => p.id === productId),
    [productId, products],
  )

  useEffect(() => {
    if (!productId || productId === 'custom') {
      return
    }

    if (selectedProduct) {
      form.setValue('input.productInput.id', selectedProduct.id)
      form.setValue('input.productInput.name', selectedProduct.name || '')
      form.setValue('input.productInput.sourceType', 'DATABASE')

      // Auto-populate product properties - use correct property names from Product schema
      if (selectedProduct.thickness) {
        form.setValue(
          'input.productInput.frcSlabThickness',
          selectedProduct.thickness,
        )
      }
      if (selectedProduct.elasticModulus) {
        form.setValue(
          'input.productInput.elasticModulus',
          selectedProduct.elasticModulus,
        )
      }
      if (
        selectedProduct.characteristicCylindricalCompressiveStrengthFrcMatrix
      ) {
        form.setValue(
          'input.productInput.cylindricCompressiveStrength',
          selectedProduct.characteristicCylindricalCompressiveStrengthFrcMatrix,
        )
      }
      if (selectedProduct.characteristicTensileStrength) {
        form.setValue(
          'input.productInput.characteristicTensileStrength',
          selectedProduct.characteristicTensileStrength,
        )
      }
      if (selectedProduct.specificWeight) {
        form.setValue(
          'input.productInput.specificWeight',
          selectedProduct.specificWeight,
        )
      }
      if (selectedProduct.adhesionToConcrete) {
        form.setValue(
          'input.productInput.adhesionToConcrete',
          selectedProduct.adhesionToConcrete,
        )
      }
      if (selectedProduct.fiberType) {
        form.setValue('input.productInput.fiberType', selectedProduct.fiberType)
      }
    }
  }, [form, productId, selectedProduct])

  const onSubmit = (data: SlabShearCalculationInput) => {
    mutate(
      {
        projectId,
        moduleId: module.id,
        body: data,
      },
      {
        onSuccess: () => {
          toast.success(tAction('calculate.success'))
        },
        onError: (error: any) => {
          toast.error(tAction('calculate.failure', { error: error.message }))
        },
      },
    )
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardContent className="pt-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <h1 className="text-3xl font-bold">{t('heading')}</h1>

              <SelectFormInput
                control={form.control}
                name="input.productInput.id"
                options={productsOptions}
                t={t}
                loading={isLoadingProducts}
                requestError={errorGettingProducts}
              />

              <SelectFormInput
                control={form.control}
                name="input.sectionFillType"
                t={t}
                options={SECTION_FILL_TYPES}
              />

              <NumberFormInput
                control={form.control}
                name="input.shearForce"
                t={t}
              />

              <div className="flex items-center space-x-2">
                <Checkbox
                  id={checkboxId}
                  checked={form.watch('input.isCantilever')}
                  onCheckedChange={checked =>
                    form.setValue('input.isCantilever', !!checked)
                  }
                />
                <Label htmlFor={checkboxId}>{t('is-cantilever')}</Label>
              </div>

              <Button type="submit" disabled={isPending}>
                {isPending ? tAction('calculating') : tCommon('calculate')}
              </Button>
            </form>
          </Form>
        </CardContent>
      </Card>

      {shearVerifyExecutionResult && (
        <SlabShearResultCard result={shearVerifyExecutionResult} />
      )}
    </div>
  )
}
