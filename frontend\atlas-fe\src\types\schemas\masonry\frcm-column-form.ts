import {
  MODULE_CRM_MASONRY_TYPE,
  MODULE_EXECUTION_CLASS,
  MODULE_FACING_MATERIAL,
  MODULE_FRM_GEOMETRY_TOPOLOGY,
  MODULE_GEOMETRY_EXPOSURE,
  MODULE_LOAD_RESISTING_CATEGORY,
  MODULE_MATERIAL_KNOWLEDGE_LEVEL,
  REINFORCEMENT_ARRANGEMENT,
} from '@atlas/constants/module'
import { nullToUndefinedTransform } from '@atlas/functions/zod/null-to-undefined-transform'
import { productSchema } from '@atlas/lib/api/products/schemas/product'
import z from 'zod'
import { productFormSchema } from '../product-form-schema'

export const frcmColumnGeometrySchema = z.object({
  topology: z.enum(MODULE_FRM_GEOMETRY_TOPOLOGY),
  largerSizeOrColumnDiameter: z.number(),
  smallerSize: z.number(),
  crossSectionArea: z.number(),
  crossSectionDiagonal: z.number(),
  cornerRoundingRadius: z.number(),
})

export const frcmColumnMasonryCharacteristicsSchema = z.object({
  material: z.enum(MODULE_FACING_MATERIAL),
  enhancementCharacteristics: z.enum(MODULE_CRM_MASONRY_TYPE),
  knowledgeLevel: z.enum(MODULE_MATERIAL_KNOWLEDGE_LEVEL).optional().nullable(),
  confidenceFactor: z.number().optional().nullable(),
  executionClass: z.enum(MODULE_EXECUTION_CLASS),
  loadResistantCategory: z.enum(MODULE_LOAD_RESISTING_CATEGORY),
  masonrySafetyFactor: z.number().optional().nullable(),
  characteristicCompressiveStrength: z.number().optional().nullable(),
  designCompressiveStrength: z.number().optional().nullable(),
  characteristicShearStrength: z.number().optional().nullable(),
  designShearStrength: z.number().optional().nullable(),
  normalElasticityModulus: z.number().optional().nullable(),
  shearElasticityModulus: z.number().optional().nullable(),
  masonryDensity: z.number().optional().nullable(),
  exposureType: z.enum(MODULE_GEOMETRY_EXPOSURE).optional().nullable(),
  conversionFactor: z.number().optional().nullable(),
})

export const frcmColumnStressSchema = z.object({
  normalStressCenteredStressing: z.number().optional().nullable(),
})

export const frcmColumnParamsSchema = z.object({
  geometry: frcmColumnGeometrySchema
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  masonryCharacteristics: frcmColumnMasonryCharacteristicsSchema
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  stress: frcmColumnStressSchema
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
})

export const confinementReinforcementVerifyInput = z.object({
  product: productSchema,
  reinforcedArrangement: z.enum(REINFORCEMENT_ARRANGEMENT),
  singleWidthBand: z.number(),
  stepsOfTheBand: z.number(),
  clearDistanceBetweenStripes: z.number(),
  minimalTransversalDimension: z.number(),
  numberOfReinforcementLayers: z.number(),
  matrixThicknessOfTheSingleLayer: z.number(),
})

export const confinementReinforcementInput = z.object({
  product: productFormSchema,
  reinforcedArrangement: z.enum(REINFORCEMENT_ARRANGEMENT),
  singleWidthBand: z.number(),
  stepsOfTheBand: z.number(),
  clearDistanceBetweenStripes: z.number(),
  minimalTransversalDimension: z.number(),
  numberOfReinforcementLayers: z.number(),
  matrixThicknessOfTheSingleLayer: z.number(),
})

export const frcmColumnConfinementReinforcementInput = z.object({
  calculationType: z.literal('CONFINEMENT_VERIFY'),
  input: confinementReinforcementInput,
})

export const nonReinforcedSectionSchema = z.object({
  normalStressStrength: z.number().optional().nullable(),
  designAxialResistance: z.number().optional().nullable(),
  check: z.boolean().optional().nullable(),
})

export const reinforcedSectionSchema = z.object({
  coefficientOfResistanceIncrease: z.number().optional().nullable(),
  confinedColumnDesignResistanceWithFrcm: z.number().optional().nullable(),
  designAxialResistanceOfTheConfinedColumnWithFrcm: z
    .number()
    .optional()
    .nullable(),
  check: z.boolean().optional().nullable(),
})

export const confinementCheckResultSchema = z.object({
  nonReinforcedSection: nonReinforcedSectionSchema
    .optional()
    .nullable()
    .transform(nullToUndefinedTransform),
  reinforcedSection: reinforcedSectionSchema
    .optional()
    .nullable()
    .transform(nullToUndefinedTransform),
})

export const confinementReinforcementCalculationResult = z.object({
  matrixGeometricPercentage: z.number().optional().nullable(),
  reinforcementGeometricPercentage: z.number().optional().nullable(),
  coefficicentOfHorizontalEfficiency: z.number().optional().nullable(),
  coefficicentOfVerticalEfficiency: z.number().optional().nullable(),
  coefficientOfOverallEfficiency: z.number().optional().nullable(),
  coefficientOfEffectivenessOfTheMatrix: z.number().optional().nullable(),
  computationalStrainOfTheComposite: z.number().optional().nullable(),
  confiningPressure: z.number().optional().nullable(),
  effectiveConfiningPressure: z.number().optional().nullable(),
  confinementCheck: confinementCheckResultSchema
    .optional()
    .nullable()
    .transform(nullToUndefinedTransform),
})

export type ConfinementReinforcementVerifyInputSchema = z.infer<
  typeof confinementReinforcementVerifyInput
>

export type ConfinementReinforcementInputSchema = z.infer<
  typeof confinementReinforcementInput
>
export type ConfinementReinforcementCalculationResultSchema = z.infer<
  typeof confinementReinforcementCalculationResult
>
export type frcmColumnConfinementReinforcementSchema = z.infer<
  typeof frcmColumnConfinementReinforcementInput
>

export type FrcmColumnParamsSchemaInput = z.infer<typeof frcmColumnParamsSchema>
export type FrcmColumnGeometryInput = z.infer<typeof frcmColumnGeometrySchema>
export type FrcmColumnMasonryCharacteristicsInput = z.infer<
  typeof frcmColumnMasonryCharacteristicsSchema
>
export type FrcmColumnStressInput = z.infer<typeof frcmColumnStressSchema>

export type NonReinforcedSectionSchema = z.infer<
  typeof nonReinforcedSectionSchema
>
export type ReinforcedSectionSchema = z.infer<typeof reinforcedSectionSchema>
export type ConfinementCheckResultSchema = z.infer<
  typeof confinementCheckResultSchema
>
