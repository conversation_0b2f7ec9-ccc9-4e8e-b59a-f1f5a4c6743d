import { CustomProductSection } from '@atlas/components/common/atlas/product/custom-product-section'
import { ProductSummaryCard } from '@atlas/components/common/atlas/product/product-summary-card'
import { NumberFormInput } from '@atlas/components/common/form/number-form-input'
import { SelectFormInput } from '@atlas/components/common/form/select-form-input'
import { Button } from '@atlas/components/ui/button'
import { Form } from '@atlas/components/ui/form'
import { productFiberType } from '@atlas/constants/product'
import type { ModuleWithParamsAntiOverturning } from '@atlas/lib/api/modules/schemas/modules'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import { useModuleCalculation } from '@atlas/lib/mutation/modules/use-module-calculation'
import { useProductsByCategory } from '@atlas/lib/query/products/use-products-by-category'
import {
  type MasonryAntiOverturningCompositeReinforcementSystemSchemaInputs,
  masonryAntiOverturningCompositeReinforcementSystemSchema,
} from '@atlas/types/schemas/masonry/antioverturning-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Separator } from '@radix-ui/react-dropdown-menu'
import { Loader2Icon } from 'lucide-react'
import Image from 'next/image'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useEffect, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { MasonryAntiOverturningPostInterventionCalculationResult } from './anti-overturning-post-intervention-calculation-result'

type Props = {
  session: Session
  projectId: Project['id']
  module: ModuleWithParamsAntiOverturning
}

export const MasonryAntiOverturningCompositeReinforcementSystemCalculationForm =
  ({ session, projectId, module }: Props) => {
    const t = useTranslations(
      'forms.calculations.anti-overturning.composite-reinforcement-system',
    )
    const tAction = useTranslations(
      'actions.calculations.anti-overturning.composite-reinforcement-system',
    )
    const tCommon = useTranslations('actions.common')

    const postInterventionCalculationResult =
      module.postInterventionCalculationResult
    const defaultValues = module?.compositeReinforcementSystemVerifyInput

    const meshProduct =
      module?.compositeReinforcementSystemVerifyInput?.meshInput?.meshProduct
    const connectorProduct =
      module?.compositeReinforcementSystemVerifyInput?.connectorInput
        ?.connectorProduct
    const matrixProduct =
      module?.compositeReinforcementSystemVerifyInput?.matrixInput
        ?.matrixProduct

    const form =
      useForm<MasonryAntiOverturningCompositeReinforcementSystemSchemaInputs>({
        resolver: zodResolver(
          masonryAntiOverturningCompositeReinforcementSystemSchema,
        ),
        defaultValues: {
          calculationType: 'POST_INTERVENTION_VERIFY',
          input: {
            meshInput: {
              // to be calculated: D65*D64*D69
              // D65 : thickness, D64: width, D69: reinforced sides number
              resistantAreaPerMeter:
                defaultValues?.meshInput?.resistantAreaPerMeter ??
                (meshProduct?.thickness ?? 0) *
                  (meshProduct?.width ?? 0) *
                  (defaultValues?.meshInput?.reinforcedSidesNumber ?? 0),
              reinforcedSidesNumber:
                defaultValues?.meshInput?.reinforcedSidesNumber ?? 0,
              meshProduct: {
                id: meshProduct?.id ?? '',
                name: meshProduct?.name,
                sourceType:
                  meshProduct?.id === 'CUSTOM' ? 'CUSTOM' : 'DATABASE',
                thickness: meshProduct?.thickness ?? 0,
                tensileStrength: meshProduct?.tensileStrength ?? 0,
                elasticModulus: meshProduct?.elasticModulus ?? 0,
                fiberType: meshProduct?.fiberType ?? productFiberType.CARBON,
              },
            },
            connectorInput: {
              connectorSpacing:
                defaultValues?.connectorInput?.connectorSpacing ?? 0,
              connectorProduct: {
                id: connectorProduct?.id ?? '',
                name: connectorProduct?.name,
                sourceType:
                  connectorProduct?.id === 'custom' ? 'CUSTOM' : 'DATABASE',
                thickness: connectorProduct?.thickness ?? 0,
                tensileStrength: connectorProduct?.tensileStrength ?? 0,
                elasticModulus: connectorProduct?.elasticModulus ?? 0,
                fiberType:
                  connectorProduct?.fiberType ?? productFiberType.CARBON,
              },
            },
            matrixInput: {
              compositeSystemThickness:
                defaultValues?.matrixInput?.compositeSystemThickness ?? 0,
              matrixProduct: {
                id: matrixProduct?.id ?? '',
                name: matrixProduct?.name ?? '',
                sourceType:
                  matrixProduct?.id === 'custom' ? 'CUSTOM' : 'DATABASE',
              },
            },
          },
        },
      })

    const { mutate, isPending } = useModuleCalculation(session.accessToken, {
      onSuccess: () => {
        toast.success(tAction('calculate.success'))
      },
      onError: error => {
        toast.error(tAction('calculate.failure', { error: error.message }))
      },
    })

    const handleFormSubmit = (
      body: MasonryAntiOverturningCompositeReinforcementSystemSchemaInputs,
    ) => {
      mutate({ projectId, moduleId: module.id, body })
    }

    const {
      data: productsAll,
      isError: errorGettingProducts,
      isLoading: isLoadingProducts,
    } = useProductsByCategory(session, 'ANTI_OVERTURNING', 0, 100)

    const productsMeshOptions = [
      ...(productsAll?.content
        .filter(m => m.productType === 'MESH')
        .map(m => ({
          value: m.id,
          label: m.name ?? t('product.unnamed'),
        })) ?? []),
      // temporary disable
      // { value: 'custom', label: t('product.custom') },
    ]

    const productsConnectorOptions = [
      ...(productsAll?.content
        .filter(m => m.productType === 'CONNECTOR')
        .map(m => ({
          value: m.id,
          label: m.name ?? t('product.unnamed'),
        })) ?? []),
      // temporary disable
      // { value: 'custom', label: t('product.custom') },
    ]

    const productsMatrixOptions = [
      ...(productsAll?.content
        .filter(m => m.productType === 'MATRIX')
        .map(m => ({
          value: m.id,
          label: m.name ?? t('product.unnamed'),
        })) ?? []),
      // temporary disable
      // { value: 'custom', label: t('product.custom') },
    ]

    const [productMeshId] = form.watch(['input.meshInput.meshProduct.id'])
    const [productConnectorId] = form.watch([
      'input.connectorInput.connectorProduct.id',
    ])
    const [productMatrixId] = form.watch(['input.matrixInput.matrixProduct.id'])

    const selectedProductMesh = useMemo(
      () =>
        productsAll?.content
          .filter(m => m.productType === 'MESH')
          .find(p => p.id === productMeshId),
      [productMeshId, productsAll],
    )

    const selectedProductConnector = useMemo(
      () =>
        productsAll?.content
          .filter(m => m.productType === 'CONNECTOR')
          .find(p => p.id === productConnectorId),
      [productConnectorId, productsAll],
    )

    const selectedProductMatrix = useMemo(
      () =>
        productsAll?.content
          .filter(m => m.productType === 'MATRIX')
          .find(p => p.id === productMatrixId),
      [productMatrixId, productsAll],
    )

    const reinforcedSidesNumberValue = form.watch(
      'input.meshInput.reinforcedSidesNumber',
    )

    useEffect(() => {
      if (productMeshId === 'custom') {
        form.setValue('input.meshInput.meshProduct.sourceType', 'CUSTOM')
      }
      if (productConnectorId === 'custom') {
        form.setValue(
          'input.connectorInput.connectorProduct.sourceType',
          'CUSTOM',
        )
      }
      if (productMatrixId === 'custom') {
        form.setValue('input.matrixInput.matrixProduct.sourceType', 'CUSTOM')
      }

      if (selectedProductMesh) {
        form.setValue('input.meshInput.meshProduct', {
          id: selectedProductMesh.id,
          name: selectedProductMesh.name,
          sourceType: 'DATABASE',
        })

        form.setValue(
          'input.meshInput.resistantAreaPerMeter',
          (selectedProductMesh?.thickness ?? 0) *
            (selectedProductMesh?.width ??
              selectedProductMesh.availableWidths?.[0] ??
              0) *
            (reinforcedSidesNumberValue ?? 0),
        )
      }

      if (selectedProductConnector) {
        form.setValue('input.connectorInput.connectorProduct', {
          id: selectedProductConnector.id,
          name: selectedProductConnector.name,
          sourceType: 'DATABASE',
        })
      }

      if (selectedProductMatrix) {
        form.setValue('input.matrixInput.matrixProduct', {
          id: selectedProductMatrix.id,
          name: selectedProductMatrix.name,
          sourceType: 'DATABASE',
        })
      }
    }, [
      form,
      productMeshId,
      selectedProductMesh,
      selectedProductConnector,
      selectedProductMatrix,
      reinforcedSidesNumberValue,
      productConnectorId,
      productMatrixId,
    ])

    return (
      <div className="flex flex-col justify-center gap-4">
        <Form {...form}>
          <form
            className="space-y-4 rounded-md border p-4"
            onSubmit={form.handleSubmit(handleFormSubmit)}
          >
            <Image
              src="/assets/masonry-anti-overturning/antibaltamento-verify.jpg"
              alt="composite reinforcement system verify"
              height={250}
              width={500}
              className="mx-auto rounded-md object-contain"
              priority
            />
            {/* Mesh Section */}
            <h1 className="text-xl font-bold">{t('mesh-sub-heading')}</h1>
            <SelectFormInput
              control={form.control}
              name="input.meshInput.meshProduct.id"
              options={productsMeshOptions}
              t={t}
              loading={isLoadingProducts}
              requestError={errorGettingProducts}
              // get the error message from the form state if any
              errorMessage={
                form.formState.errors.input?.meshInput?.meshProduct?.id?.message
              }
            />
            {productMeshId === 'custom' && <CustomProductSection />}
            {selectedProductMesh && (
              <ProductSummaryCard
                product={selectedProductMesh}
                isRectangularBeam={false}
              />
            )}
            <NumberFormInput
              control={form.control}
              name="input.meshInput.reinforcedSidesNumber"
              t={t}
            />
            <NumberFormInput
              control={form.control}
              name="input.meshInput.resistantAreaPerMeter"
              t={t}
              disabled={true}
            />
            <Separator />
            {/* Connector Section */}
            <h1 className="text-xl font-bold">{t('connector-sub-heading')}</h1>
            <SelectFormInput
              control={form.control}
              name="input.connectorInput.connectorProduct.id"
              options={productsConnectorOptions}
              t={t}
              loading={isLoadingProducts}
              requestError={errorGettingProducts}
              errorMessage={
                form.formState.errors.input?.connectorInput?.connectorProduct
                  ?.id?.message
              }
            />
            {productConnectorId === 'custom' && <CustomProductSection />}
            {selectedProductConnector && (
              <ProductSummaryCard
                product={selectedProductConnector}
                facingMaterial={
                  module?.params?.materialProperties?.facingMaterial
                }
                isRectangularBeam={false}
              />
            )}
            <NumberFormInput
              control={form.control}
              name="input.connectorInput.connectorSpacing"
              t={t}
            />
            <Separator />
            {/* Matrix Section */}
            <h1 className="text-xl font-bold">{t('matrix-sub-heading')}</h1>
            <SelectFormInput
              control={form.control}
              name="input.matrixInput.matrixProduct.id"
              options={productsMatrixOptions}
              t={t}
              loading={isLoadingProducts}
              requestError={errorGettingProducts}
              errorMessage={
                form.formState.errors.input?.matrixInput?.matrixProduct?.id
                  ?.message
              }
            />
            {productMatrixId === 'custom' && <CustomProductSection />}
            {selectedProductMatrix && (
              <ProductSummaryCard
                product={selectedProductMatrix}
                isRectangularBeam={false}
              />
            )}
            <NumberFormInput
              control={form.control}
              name="input.matrixInput.compositeSystemThickness"
              t={t}
            />
            <Button
              type="submit"
              className="w-full sm:w-auto"
              disabled={isPending}
            >
              {isPending && <Loader2Icon className="animate-spin" />}
              {tCommon('calculate')}
            </Button>
          </form>
        </Form>
        {postInterventionCalculationResult && (
          <MasonryAntiOverturningPostInterventionCalculationResult
            postInterventionCalculationResult={
              postInterventionCalculationResult
            }
          />
        )}
      </div>
    )
  }
