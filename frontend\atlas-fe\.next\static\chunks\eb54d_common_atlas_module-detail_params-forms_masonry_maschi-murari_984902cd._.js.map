{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-extremity-detachement-check-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { ExtremityDetachementCheckSchema } from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  extremityDetachementCheck?: ExtremityDetachementCheckSchema\r\n}\r\n\r\nexport function ExtremityDetachementCheck({\r\n  extremityDetachementCheck,\r\n}: Props) {\r\n  const {\r\n    appliedSpecificBendingMoment30FromEdge,\r\n    reinforcementDesignStrainForEndDebonding,\r\n    neutralAxisCompressedEdgeDistance,\r\n    masonryStrain,\r\n    resultantCompressiveForceMasonry,\r\n    resultantTensileForceFrcm,\r\n    designMomentCapacityReinforcedSection,\r\n    check,\r\n  } = extremityDetachementCheck || {}\r\n\r\n  const t = useTranslations(\r\n    'components.calculations.maschi-murari.flexuralReinforcementCalculationResult.extremityDetachementCheck',\r\n  )\r\n\r\n  const locale = useLocale()\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('appliedSpecificBendingMoment30FromEdge.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {appliedSpecificBendingMoment30FromEdge?.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('reinforcementDesignStrainForEndDebonding.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {reinforcementDesignStrainForEndDebonding?.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('neutralAxisCompressedEdgeDistance.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {neutralAxisCompressedEdgeDistance?.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">{t('masonryStrain.label')}:</span>{' '}\r\n          <span>\r\n            {masonryStrain?.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('resultantCompressiveForceMasonry.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {resultantCompressiveForceMasonry?.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('resultantTensileForceFrcm.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {resultantTensileForceFrcm?.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('designMomentCapacityReinforcedSection.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {designMomentCapacityReinforcedSection?.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <span className=\"font-medium\">{t('check.label')}:</span>{' '}\r\n        <Badge\r\n          className={cn(\r\n            'text-base px-3 py-1',\r\n            check ? 'bg-green-600' : 'bg-red-600',\r\n          )}\r\n        >\r\n          {check ? t('check.satisfied') : t('check.notSatisfied')}\r\n        </Badge>\r\n        {check ? null : (\r\n          <div>\r\n            <span className=\"font-medium text-red-600\">\r\n              {' '}\r\n              {t('checkResultingText.no')}\r\n            </span>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AAEA;AAAA;;;;;;;AAMO,SAAS,0BAA0B,KAElC;QAFkC,EACxC,yBAAyB,EACnB,GAFkC;;IAGxC,MAAM,EACJ,sCAAsC,EACtC,wCAAwC,EACxC,iCAAiC,EACjC,aAAa,EACb,gCAAgC,EAChC,yBAAyB,EACzB,qCAAqC,EACrC,KAAK,EACN,GAAG,6BAA6B,CAAC;IAElC,MAAM,IAAI,IAAA,4NAAe,EACvB;IAGF,MAAM,SAAS,IAAA,kLAAS;IAExB,qBACE,6LAAC,2IAAI;;0BACH,6LAAC,iJAAU;0BACT,cAAA,6LAAC,gJAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,6LAAC,kJAAW;gBAAC,WAAU;;kCACrB,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAgD;;;;;;;4BAC7C;0CACR,6LAAC;;oCACE,mDAAA,6DAAA,uCAAwC,cAAc,CAAC,QAAQ;wCAC9D,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAkD;;;;;;;4BAC/C;0CACR,6LAAC;;oCACE,qDAAA,+DAAA,yCAA0C,cAAc,CAAC,QAAQ;wCAChE,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA2C;;;;;;;4BACxC;0CACR,6LAAC;;oCACE,8CAAA,wDAAA,kCAAmC,cAAc,CAAC,QAAQ;wCACzD,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAAuB;;;;;;;4BAAS;0CACjE,6LAAC;;oCACE,0BAAA,oCAAA,cAAe,cAAc,CAAC,QAAQ;wCACrC,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA0C;;;;;;;4BACvC;0CACR,6LAAC;;oCACE,6CAAA,uDAAA,iCAAkC,cAAc,CAAC,QAAQ;wCACxD,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAmC;;;;;;;4BAChC;0CACR,6LAAC;;oCACE,sCAAA,gDAAA,0BAA2B,cAAc,CAAC,QAAQ;wCACjD,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA+C;;;;;;;4BAC5C;0CACR,6LAAC;;oCACE,kDAAA,4DAAA,sCAAuC,cAAc,CAAC,QAAQ;wCAC7D,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,6LAAC;wBAAK,WAAU;;4BAAe,EAAE;4BAAe;;;;;;;oBAAS;kCACzD,6LAAC,6IAAK;wBACJ,WAAW,IAAA,4HAAE,EACX,uBACA,QAAQ,iBAAiB;kCAG1B,QAAQ,EAAE,qBAAqB,EAAE;;;;;;oBAEnC,QAAQ,qBACP,6LAAC;kCACC,cAAA,6LAAC;4BAAK,WAAU;;gCACb;gCACA,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAOjB;GAlHgB;;QAcJ,4NAAe;QAIV,kLAAS;;;KAlBV", "debugId": null}}, {"offset": {"line": 334, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-in-plane-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport {\r\n  Ta<PERSON>,\r\n  TabsContent,\r\n  Ta<PERSON>List,\r\n  TabsTrigger,\r\n} from '@atlas/components/ui/tabs'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { InPlaneFlexuralCheckSchema } from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { Separator } from '@radix-ui/react-separator'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  inPlaneFlexuralCheckSchema?: InPlaneFlexuralCheckSchema\r\n}\r\n\r\nexport function InPlaneFlexuralCheckResultCard({\r\n  inPlaneFlexuralCheckSchema,\r\n}: Props) {\r\n  const { nonReinforcedSection, reinforcedSection } =\r\n    inPlaneFlexuralCheckSchema || {}\r\n\r\n  const t = useTranslations(\r\n    'components.calculations.maschi-murari.flexuralReinforcementCalculationResult.inPlaneFlexuralCheck',\r\n  )\r\n\r\n  const locale = useLocale()\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <h2>{t('nonReinforcedSection.title')}</h2>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('nonReinforcedSection.inPlaneBendingMoment.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {nonReinforcedSection?.inPlaneBendingMoment.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('nonReinforcedSection.neutralAxisCompressedEdgeDistance.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {nonReinforcedSection?.neutralAxisCompressedEdgeDistance.toLocaleString(\r\n              locale,\r\n              {\r\n                maximumFractionDigits: 5,\r\n              },\r\n            )}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('nonReinforcedSection.inPlaneFlexuralCapacity.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {nonReinforcedSection?.inPlaneFlexuralCapacity.toLocaleString(\r\n              locale,\r\n              {\r\n                maximumFractionDigits: 5,\r\n              },\r\n            )}{' '}\r\n          </span>\r\n        </div>\r\n        <span className=\"font-medium\">\r\n          {t('nonReinforcedSection.check.label')}:\r\n        </span>{' '}\r\n        <Badge\r\n          className={cn(\r\n            'text-base px-3 py-1',\r\n            nonReinforcedSection?.check ? 'bg-green-600' : 'bg-red-600',\r\n          )}\r\n        >\r\n          {nonReinforcedSection?.check\r\n            ? t('nonReinforcedSection.check.satisfied')\r\n            : t('nonReinforcedSection.check.notSatisfied')}\r\n        </Badge>\r\n        <Separator />\r\n        <h2>{t('reinforcedSection.title')}</h2>\r\n        <Tabs defaultValue=\"hypothesisOne\">\r\n          <TabsList>\r\n            <TabsTrigger value=\"hypothesisOne\">\r\n              {t('reinforcedSection.hypothesisOne.label')}\r\n            </TabsTrigger>\r\n            <TabsTrigger value=\"hypothesisTwo\">\r\n              {t('reinforcedSection.hypothesisTwo.label')}\r\n            </TabsTrigger>\r\n            <TabsTrigger value=\"hypothesisThree\">\r\n              {t('reinforcedSection.hypothesisThree.label')}\r\n            </TabsTrigger>\r\n          </TabsList>\r\n          <TabsContent value=\"hypothesisOne\">\r\n            {renderHypothesis(\r\n              reinforcedSection?.hypothesisOne,\r\n              'hypothesisOne',\r\n            )}\r\n          </TabsContent>\r\n          <TabsContent value=\"hypothesisTwo\">\r\n            {renderHypothesis(\r\n              reinforcedSection?.hypothesisTwo,\r\n              'hypothesisTwo',\r\n            )}\r\n          </TabsContent>\r\n          <TabsContent value=\"hypothesisThree\">\r\n            {renderHypothesis(\r\n              reinforcedSection?.hypothesisThree,\r\n              'hypothesisThree',\r\n            )}\r\n          </TabsContent>\r\n        </Tabs>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('reinforcedSection.momentCapacity.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {reinforcedSection?.momentCapacity.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <span className=\"font-medium\">\r\n          {t('reinforcedSection.check.label')}:\r\n        </span>{' '}\r\n        <Badge\r\n          className={cn(\r\n            'text-base px-3 py-1',\r\n            reinforcedSection?.check ? 'bg-green-600' : 'bg-red-600',\r\n          )}\r\n        >\r\n          {reinforcedSection?.check\r\n            ? t('reinforcedSection.check.satisfied')\r\n            : t('reinforcedSection.check.notSatisfied')}\r\n        </Badge>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n\r\n  function renderHypothesis(regionHypothesis: any, labelKey: string) {\r\n    if (!regionHypothesis) {\r\n      return null\r\n    }\r\n    return (\r\n      <>\r\n        <h2>{t(`reinforcedSection.${labelKey}.label`)}</h2>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'reinforcedSection.hypothesis.neutralAxisCompressedEdgeDistance.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {regionHypothesis.neutralAxisCompressedEdgeDistance?.toLocaleString(\r\n              locale,\r\n              {\r\n                maximumFractionDigits: 5,\r\n              },\r\n            )}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              `reinforcedSection.${labelKey}.reinforcementOrMasonryStrain.label`,\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {regionHypothesis.reinforcementOrMasonryStrain?.toLocaleString(\r\n              locale,\r\n              {\r\n                maximumFractionDigits: 5,\r\n              },\r\n            )}{' '}\r\n          </span>\r\n        </div>\r\n        <span className=\"font-medium\">\r\n          {t(`reinforcedSection.${labelKey}.check.label`)}:\r\n        </span>{' '}\r\n        <span className={cn('text-base px-3 py-1')}>\r\n          {regionHypothesis.check\r\n            ? t(`reinforcedSection.${labelKey}.check.satisfied`)\r\n            : t(`reinforcedSection.${labelKey}.check.notSatisfied`)}\r\n        </span>\r\n      </>\r\n    )\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AAMA;AAEA;AACA;AAAA;;;;;;;;;AAMO,SAAS,+BAA+B,KAEvC;QAFuC,EAC7C,0BAA0B,EACpB,GAFuC;;IAG7C,MAAM,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,GAC/C,8BAA8B,CAAC;IAEjC,MAAM,IAAI,IAAA,4NAAe,EACvB;IAGF,MAAM,SAAS,IAAA,kLAAS;IAExB,qBACE,6LAAC,2IAAI;;0BACH,6LAAC,iJAAU;0BACT,cAAA,6LAAC,gJAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,6LAAC,kJAAW;gBAAC,WAAU;;kCACrB,6LAAC;kCAAI,EAAE;;;;;;kCACP,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAmD;;;;;;;4BAChD;0CACR,6LAAC;;oCACE,iCAAA,2CAAA,qBAAsB,oBAAoB,CAAC,cAAc,CAAC,QAAQ;wCACjE,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAgE;;;;;;;4BAC7D;0CACR,6LAAC;;oCACE,iCAAA,2CAAA,qBAAsB,iCAAiC,CAAC,cAAc,CACrE,QACA;wCACE,uBAAuB;oCACzB;oCACC;;;;;;;;;;;;;kCAGP,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAsD;;;;;;;4BACnD;0CACR,6LAAC;;oCACE,iCAAA,2CAAA,qBAAsB,uBAAuB,CAAC,cAAc,CAC3D,QACA;wCACE,uBAAuB;oCACzB;oCACC;;;;;;;;;;;;;kCAGP,6LAAC;wBAAK,WAAU;;4BACb,EAAE;4BAAoC;;;;;;;oBACjC;kCACR,6LAAC,6IAAK;wBACJ,WAAW,IAAA,4HAAE,EACX,uBACA,CAAA,iCAAA,2CAAA,qBAAsB,KAAK,IAAG,iBAAiB;kCAGhD,CAAA,iCAAA,2CAAA,qBAAsB,KAAK,IACxB,EAAE,0CACF,EAAE;;;;;;kCAER,6LAAC,qLAAS;;;;;kCACV,6LAAC;kCAAI,EAAE;;;;;;kCACP,6LAAC,2IAAI;wBAAC,cAAa;;0CACjB,6LAAC,+IAAQ;;kDACP,6LAAC,kJAAW;wCAAC,OAAM;kDAChB,EAAE;;;;;;kDAEL,6LAAC,kJAAW;wCAAC,OAAM;kDAChB,EAAE;;;;;;kDAEL,6LAAC,kJAAW;wCAAC,OAAM;kDAChB,EAAE;;;;;;;;;;;;0CAGP,6LAAC,kJAAW;gCAAC,OAAM;0CAChB,iBACC,8BAAA,wCAAA,kBAAmB,aAAa,EAChC;;;;;;0CAGJ,6LAAC,kJAAW;gCAAC,OAAM;0CAChB,iBACC,8BAAA,wCAAA,kBAAmB,aAAa,EAChC;;;;;;0CAGJ,6LAAC,kJAAW;gCAAC,OAAM;0CAChB,iBACC,8BAAA,wCAAA,kBAAmB,eAAe,EAClC;;;;;;;;;;;;kCAIN,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA0C;;;;;;;4BACvC;0CACR,6LAAC;;oCACE,8BAAA,wCAAA,kBAAmB,cAAc,CAAC,cAAc,CAAC,QAAQ;wCACxD,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,6LAAC;wBAAK,WAAU;;4BACb,EAAE;4BAAiC;;;;;;;oBAC9B;kCACR,6LAAC,6IAAK;wBACJ,WAAW,IAAA,4HAAE,EACX,uBACA,CAAA,8BAAA,wCAAA,kBAAmB,KAAK,IAAG,iBAAiB;kCAG7C,CAAA,8BAAA,wCAAA,kBAAmB,KAAK,IACrB,EAAE,uCACF,EAAE;;;;;;;;;;;;;;;;;;;;IAMd,SAAS,iBAAiB,gBAAqB,EAAE,QAAgB;YAetD,qDAgBA;QA9BT,IAAI,CAAC,kBAAkB;YACrB,OAAO;QACT;QACA,qBACE;;8BACE,6LAAC;8BAAI,EAAE,AAAC,qBAA6B,OAAT,UAAS;;;;;;8BACrC,6LAAC;;sCACC,6LAAC;4BAAK,WAAU;;gCACb,EACC;gCACA;;;;;;;wBAEI;sCACR,6LAAC;;iCACE,sDAAA,iBAAiB,iCAAiC,cAAlD,0EAAA,oDAAoD,cAAc,CACjE,QACA;oCACE,uBAAuB;gCACzB;gCACC;;;;;;;;;;;;;8BAGP,6LAAC;;sCACC,6LAAC;4BAAK,WAAU;;gCACb,EACC,AAAC,qBAA6B,OAAT,UAAS;gCAC9B;;;;;;;wBAEI;sCACR,6LAAC;;iCACE,iDAAA,iBAAiB,4BAA4B,cAA7C,qEAAA,+CAA+C,cAAc,CAC5D,QACA;oCACE,uBAAuB;gCACzB;gCACC;;;;;;;;;;;;;8BAGP,6LAAC;oBAAK,WAAU;;wBACb,EAAE,AAAC,qBAA6B,OAAT,UAAS;wBAAe;;;;;;;gBAC1C;8BACR,6LAAC;oBAAK,WAAW,IAAA,4HAAE,EAAC;8BACjB,iBAAiB,KAAK,GACnB,EAAE,AAAC,qBAA6B,OAAT,UAAS,uBAChC,EAAE,AAAC,qBAA6B,OAAT,UAAS;;;;;;;;IAI5C;AACF;GAlLgB;;QAMJ,4NAAe;QAIV,kLAAS;;;KAVV", "debugId": null}}, {"offset": {"line": 762, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-flexural-out-of-plane-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport {\r\n  Ta<PERSON>,\r\n  TabsContent,\r\n  Ta<PERSON>List,\r\n  TabsTrigger,\r\n} from '@atlas/components/ui/tabs'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { OutOfPlaneFlexuralCheckSchema } from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { Separator } from '@radix-ui/react-separator'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  outOfPlaneFlexuralCheckSchema?: OutOfPlaneFlexuralCheckSchema\r\n}\r\n\r\nexport function OutOfPlaneFlexuralCheckResultCard({\r\n  outOfPlaneFlexuralCheckSchema,\r\n}: Props) {\r\n  const { nonReinforcedSection, reinforcedSection } =\r\n    outOfPlaneFlexuralCheckSchema || {}\r\n\r\n  const t = useTranslations(\r\n    'components.calculations.maschi-murari.flexuralReinforcementCalculationResult.outOfPlaneFlexuralCheck',\r\n  )\r\n\r\n  const locale = useLocale()\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <h2>{t('nonReinforcedSection.title')}</h2>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('nonReinforcedSection.appliedDesignBendingMoment.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {nonReinforcedSection?.appliedDesignBendingMoment.toLocaleString(\r\n              locale,\r\n              {\r\n                maximumFractionDigits: 5,\r\n              },\r\n            )}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('nonReinforcedSection.neutralAxisCompressedEdgeDistance.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {nonReinforcedSection?.neutralAxisCompressedEdgeDistance.toLocaleString(\r\n              locale,\r\n              {\r\n                maximumFractionDigits: 5,\r\n              },\r\n            )}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('nonReinforcedSection.resultantCompressiveForceMasonry.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {nonReinforcedSection?.resultantCompressiveForceMasonry.toLocaleString(\r\n              locale,\r\n              {\r\n                maximumFractionDigits: 5,\r\n              },\r\n            )}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('nonReinforcedSection.designBendingMoment.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {nonReinforcedSection?.designBendingMoment.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <span className=\"font-medium\">\r\n          {t('nonReinforcedSection.check.label')}:\r\n        </span>{' '}\r\n        <Badge\r\n          className={cn(\r\n            'text-base px-3 py-1',\r\n            nonReinforcedSection?.check ? 'bg-green-600' : 'bg-red-600',\r\n          )}\r\n        >\r\n          {nonReinforcedSection?.check\r\n            ? t('nonReinforcedSection.check.satisfied')\r\n            : t('nonReinforcedSection.check.notSatisfied')}\r\n        </Badge>\r\n        <Separator />\r\n        <h2>{t('reinforcedSection.title')}</h2>\r\n        <Tabs defaultValue=\"regionHypothesisOne\">\r\n          <TabsList>\r\n            <TabsTrigger value=\"regionHypothesisOne\">\r\n              {t('reinforcedSection.regionHypothesisOne.label')}\r\n            </TabsTrigger>\r\n            <TabsTrigger value=\"regionHypothesisTwo\">\r\n              {t('reinforcedSection.regionHypothesisTwo.label')}\r\n            </TabsTrigger>\r\n          </TabsList>\r\n          <TabsContent value=\"regionHypothesisOne\">\r\n            {renderRegionHypothese(\r\n              reinforcedSection?.regionHypothesisOne,\r\n              'regionHypothesisOne',\r\n            )}\r\n          </TabsContent>\r\n          <TabsContent value=\"regionHypothesisTwo\">\r\n            {renderRegionHypothese(\r\n              reinforcedSection?.regionHypothesisTwo,\r\n              'regionHypothesisTwo',\r\n            )}\r\n          </TabsContent>\r\n        </Tabs>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('reinforcedSection.momentCapacity.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {reinforcedSection?.momentCapacity.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <span className=\"font-medium\">\r\n          {t('reinforcedSection.check.label')}:\r\n        </span>{' '}\r\n        <Badge\r\n          className={cn(\r\n            'text-base px-3 py-1',\r\n            reinforcedSection?.check ? 'bg-green-600' : 'bg-red-600',\r\n          )}\r\n        >\r\n          {reinforcedSection?.check\r\n            ? t('reinforcedSection.check.satisfied')\r\n            : t('reinforcedSection.check.notSatisfied')}\r\n        </Badge>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n\r\n  function renderRegionHypothese(regionHypothesis: any, labelKey: string) {\r\n    if (!regionHypothesis) {\r\n      return null\r\n    }\r\n    return (\r\n      <>\r\n        <h2>{t(`reinforcedSection.${labelKey}.label`)}</h2>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'reinforcedSection.regionHypothesis.neutralAxisCompressedEdgeDistance.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {regionHypothesis.neutralAxisCompressedEdgeDistance?.toLocaleString(\r\n              locale,\r\n              {\r\n                maximumFractionDigits: 5,\r\n              },\r\n            )}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              `reinforcedSection.${labelKey}.reinforcementOrMasonryStrain.label`,\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {regionHypothesis.reinforcementOrMasonryStrain?.toLocaleString(\r\n              locale,\r\n              {\r\n                maximumFractionDigits: 5,\r\n              },\r\n            )}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'reinforcedSection.regionHypothesis.resultantCompressiveForceMasonry.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {regionHypothesis.resultantCompressiveForceMasonry?.toLocaleString(\r\n              locale,\r\n              {\r\n                maximumFractionDigits: 5,\r\n              },\r\n            )}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'reinforcedSection.regionHypothesis.resultantTensileForceFrcm.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {regionHypothesis.resultantTensileForceFrcm?.toLocaleString(\r\n              locale,\r\n              {\r\n                maximumFractionDigits: 5,\r\n              },\r\n            )}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'reinforcedSection.regionHypothesis.designBendingMomentReinforcedSection.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {regionHypothesis.designBendingMomentReinforcedSection?.toLocaleString(\r\n              locale,\r\n              {\r\n                maximumFractionDigits: 5,\r\n              },\r\n            )}{' '}\r\n          </span>\r\n        </div>\r\n        <span className=\"font-medium\">\r\n          {t(`reinforcedSection.${labelKey}.check.label`)}:\r\n        </span>{' '}\r\n        <span className={cn('text-base px-3 py-1')}>\r\n          {regionHypothesis.check\r\n            ? t(`reinforcedSection.${labelKey}.check.satisfied`)\r\n            : t(`reinforcedSection.${labelKey}.check.notSatisfied`)}\r\n        </span>\r\n      </>\r\n    )\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AAMA;AAEA;AACA;AAAA;;;;;;;;;AAMO,SAAS,kCAAkC,KAE1C;QAF0C,EAChD,6BAA6B,EACvB,GAF0C;;IAGhD,MAAM,EAAE,oBAAoB,EAAE,iBAAiB,EAAE,GAC/C,iCAAiC,CAAC;IAEpC,MAAM,IAAI,IAAA,4NAAe,EACvB;IAGF,MAAM,SAAS,IAAA,kLAAS;IAExB,qBACE,6LAAC,2IAAI;;0BACH,6LAAC,iJAAU;0BACT,cAAA,6LAAC,gJAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,6LAAC,kJAAW;gBAAC,WAAU;;kCACrB,6LAAC;kCAAI,EAAE;;;;;;kCACP,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAyD;;;;;;;4BACtD;0CACR,6LAAC;;oCACE,iCAAA,2CAAA,qBAAsB,0BAA0B,CAAC,cAAc,CAC9D,QACA;wCACE,uBAAuB;oCACzB;oCACC;;;;;;;;;;;;;kCAGP,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAgE;;;;;;;4BAC7D;0CACR,6LAAC;;oCACE,iCAAA,2CAAA,qBAAsB,iCAAiC,CAAC,cAAc,CACrE,QACA;wCACE,uBAAuB;oCACzB;oCACC;;;;;;;;;;;;;kCAGP,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA+D;;;;;;;4BAC5D;0CACR,6LAAC;;oCACE,iCAAA,2CAAA,qBAAsB,gCAAgC,CAAC,cAAc,CACpE,QACA;wCACE,uBAAuB;oCACzB;oCACC;;;;;;;;;;;;;kCAGP,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAkD;;;;;;;4BAC/C;0CACR,6LAAC;;oCACE,iCAAA,2CAAA,qBAAsB,mBAAmB,CAAC,cAAc,CAAC,QAAQ;wCAChE,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,6LAAC;wBAAK,WAAU;;4BACb,EAAE;4BAAoC;;;;;;;oBACjC;kCACR,6LAAC,6IAAK;wBACJ,WAAW,IAAA,4HAAE,EACX,uBACA,CAAA,iCAAA,2CAAA,qBAAsB,KAAK,IAAG,iBAAiB;kCAGhD,CAAA,iCAAA,2CAAA,qBAAsB,KAAK,IACxB,EAAE,0CACF,EAAE;;;;;;kCAER,6LAAC,qLAAS;;;;;kCACV,6LAAC;kCAAI,EAAE;;;;;;kCACP,6LAAC,2IAAI;wBAAC,cAAa;;0CACjB,6LAAC,+IAAQ;;kDACP,6LAAC,kJAAW;wCAAC,OAAM;kDAChB,EAAE;;;;;;kDAEL,6LAAC,kJAAW;wCAAC,OAAM;kDAChB,EAAE;;;;;;;;;;;;0CAGP,6LAAC,kJAAW;gCAAC,OAAM;0CAChB,sBACC,8BAAA,wCAAA,kBAAmB,mBAAmB,EACtC;;;;;;0CAGJ,6LAAC,kJAAW;gCAAC,OAAM;0CAChB,sBACC,8BAAA,wCAAA,kBAAmB,mBAAmB,EACtC;;;;;;;;;;;;kCAIN,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA0C;;;;;;;4BACvC;0CACR,6LAAC;;oCACE,8BAAA,wCAAA,kBAAmB,cAAc,CAAC,cAAc,CAAC,QAAQ;wCACxD,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,6LAAC;wBAAK,WAAU;;4BACb,EAAE;4BAAiC;;;;;;;oBAC9B;kCACR,6LAAC,6IAAK;wBACJ,WAAW,IAAA,4HAAE,EACX,uBACA,CAAA,8BAAA,wCAAA,kBAAmB,KAAK,IAAG,iBAAiB;kCAG7C,CAAA,8BAAA,wCAAA,kBAAmB,KAAK,IACrB,EAAE,uCACF,EAAE;;;;;;;;;;;;;;;;;;;;IAMd,SAAS,sBAAsB,gBAAqB,EAAE,QAAgB;YAe3D,qDAgBA,gDAgBA,oDAgBA,6CAgBA;QA9ET,IAAI,CAAC,kBAAkB;YACrB,OAAO;QACT;QACA,qBACE;;8BACE,6LAAC;8BAAI,EAAE,AAAC,qBAA6B,OAAT,UAAS;;;;;;8BACrC,6LAAC;;sCACC,6LAAC;4BAAK,WAAU;;gCACb,EACC;gCACA;;;;;;;wBAEI;sCACR,6LAAC;;iCACE,sDAAA,iBAAiB,iCAAiC,cAAlD,0EAAA,oDAAoD,cAAc,CACjE,QACA;oCACE,uBAAuB;gCACzB;gCACC;;;;;;;;;;;;;8BAGP,6LAAC;;sCACC,6LAAC;4BAAK,WAAU;;gCACb,EACC,AAAC,qBAA6B,OAAT,UAAS;gCAC9B;;;;;;;wBAEI;sCACR,6LAAC;;iCACE,iDAAA,iBAAiB,4BAA4B,cAA7C,qEAAA,+CAA+C,cAAc,CAC5D,QACA;oCACE,uBAAuB;gCACzB;gCACC;;;;;;;;;;;;;8BAGP,6LAAC;;sCACC,6LAAC;4BAAK,WAAU;;gCACb,EACC;gCACA;;;;;;;wBAEI;sCACR,6LAAC;;iCACE,qDAAA,iBAAiB,gCAAgC,cAAjD,yEAAA,mDAAmD,cAAc,CAChE,QACA;oCACE,uBAAuB;gCACzB;gCACC;;;;;;;;;;;;;8BAGP,6LAAC;;sCACC,6LAAC;4BAAK,WAAU;;gCACb,EACC;gCACA;;;;;;;wBAEI;sCACR,6LAAC;;iCACE,8CAAA,iBAAiB,yBAAyB,cAA1C,kEAAA,4CAA4C,cAAc,CACzD,QACA;oCACE,uBAAuB;gCACzB;gCACC;;;;;;;;;;;;;8BAGP,6LAAC;;sCACC,6LAAC;4BAAK,WAAU;;gCACb,EACC;gCACA;;;;;;;wBAEI;sCACR,6LAAC;;iCACE,yDAAA,iBAAiB,oCAAoC,cAArD,6EAAA,uDAAuD,cAAc,CACpE,QACA;oCACE,uBAAuB;gCACzB;gCACC;;;;;;;;;;;;;8BAGP,6LAAC;oBAAK,WAAU;;wBACb,EAAE,AAAC,qBAA6B,OAAT,UAAS;wBAAe;;;;;;;gBAC1C;8BACR,6LAAC;oBAAK,WAAW,IAAA,4HAAE,EAAC;8BACjB,iBAAiB,KAAK,GACnB,EAAE,AAAC,qBAA6B,OAAT,UAAS,uBAChC,EAAE,AAAC,qBAA6B,OAAT,UAAS;;;;;;;;IAI5C;AACF;GAtOgB;;QAMJ,4NAAe;QAIV,kLAAS;;;KAVV", "debugId": null}}, {"offset": {"line": 1302, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-shear-check-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { ShearCheckSchema } from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  shearCheckResults?: ShearCheckSchema\r\n}\r\n\r\nexport function ShearCheckResultCard({ shearCheckResults }: Props) {\r\n  const {\r\n    appliedDesignSpecificShearForce,\r\n    averageNormalStress,\r\n    resultantCompressiveForceMasonry,\r\n    resultantTensileForceFrcm,\r\n    check,\r\n  } = shearCheckResults || {}\r\n\r\n  const t = useTranslations(\r\n    'components.calculations.maschi-murari.flexuralReinforcementCalculationResult.shearCheck',\r\n  )\r\n\r\n  const locale = useLocale()\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('appliedDesignSpecificShearForce.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {appliedDesignSpecificShearForce?.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">{t('averageNormalStress.label')}:</span>{' '}\r\n          <span>\r\n            {averageNormalStress?.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('resultantCompressiveForceMasonry.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {resultantCompressiveForceMasonry?.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('resultantTensileForceFrcm.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {resultantTensileForceFrcm?.toLocaleString(locale, {\r\n              maximumFractionDigits: 5,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <span className=\"font-medium\">{t('check.label')}:</span>{' '}\r\n        <Badge\r\n          className={cn(\r\n            'text-base px-3 py-1',\r\n            check ? 'bg-green-600' : 'bg-red-600',\r\n          )}\r\n        >\r\n          {check ? t('check.satisfied') : t('check.notSatisfied')}\r\n        </Badge>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AAEA;AAAA;;;;;;;AAMO,SAAS,qBAAqB,KAA4B;QAA5B,EAAE,iBAAiB,EAAS,GAA5B;;IACnC,MAAM,EACJ,+BAA+B,EAC/B,mBAAmB,EACnB,gCAAgC,EAChC,yBAAyB,EACzB,KAAK,EACN,GAAG,qBAAqB,CAAC;IAE1B,MAAM,IAAI,IAAA,4NAAe,EACvB;IAGF,MAAM,SAAS,IAAA,kLAAS;IAExB,qBACE,6LAAC,2IAAI;;0BACH,6LAAC,iJAAU;0BACT,cAAA,6LAAC,gJAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,6LAAC,kJAAW;gBAAC,WAAU;;kCACrB,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAyC;;;;;;;4BACtC;0CACR,6LAAC;;oCACE,4CAAA,sDAAA,gCAAiC,cAAc,CAAC,QAAQ;wCACvD,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAA6B;;;;;;;4BAAS;0CACvE,6LAAC;;oCACE,gCAAA,0CAAA,oBAAqB,cAAc,CAAC,QAAQ;wCAC3C,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA0C;;;;;;;4BACvC;0CACR,6LAAC;;oCACE,6CAAA,uDAAA,iCAAkC,cAAc,CAAC,QAAQ;wCACxD,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAmC;;;;;;;4BAChC;0CACR,6LAAC;;oCACE,sCAAA,gDAAA,0BAA2B,cAAc,CAAC,QAAQ;wCACjD,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,6LAAC;wBAAK,WAAU;;4BAAe,EAAE;4BAAe;;;;;;;oBAAS;kCACzD,6LAAC,6IAAK;wBACJ,WAAW,IAAA,4HAAE,EACX,uBACA,QAAQ,iBAAiB;kCAG1B,QAAQ,EAAE,qBAAqB,EAAE;;;;;;;;;;;;;;;;;;AAK5C;GAvEgB;;QASJ,4NAAe;QAIV,kLAAS;;;KAbV", "debugId": null}}, {"offset": {"line": 1519, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation-result.tsx"], "sourcesContent": ["import {\r\n  <PERSON>,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON>Content,\r\n  <PERSON><PERSON>List,\r\n  TabsTrigger,\r\n} from '@atlas/components/ui/tabs'\r\nimport type { MaschiMurariFlexuralReinforcementCalculationResultSchema } from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\nimport { ExtremityDetachementCheck } from './maschi-murari-extremity-detachement-check-result-card'\r\nimport { InPlaneFlexuralCheckResultCard } from './maschi-murari-flexural-in-plane-result-card'\r\nimport { OutOfPlaneFlexuralCheckResultCard } from './maschi-murari-flexural-out-of-plane-result-card'\r\nimport { ShearCheckResultCard } from './maschi-murari-shear-check-result-card'\r\n\r\ntype Props = {\r\n  flexuralReinforcementCalculationResult: MaschiMurariFlexuralReinforcementCalculationResultSchema\r\n}\r\n\r\nexport function MaschiMurariReinforcementFlexuralCalculationResult({\r\n  flexuralReinforcementCalculationResult,\r\n}: Props) {\r\n  const t = useTranslations(\r\n    'components.calculations.maschi-murari.flexuralReinforcementCalculationResult',\r\n  )\r\n  const _locale = useLocale()\r\n\r\n  return (\r\n    <>\r\n      <h3 className=\"text-lg font-medium py-4\">{t('title')}</h3>\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle>{t('title')}</CardTitle>\r\n        </CardHeader>\r\n        <CardContent className=\"space-y-4\">\r\n          <Tabs defaultValue=\"inPlaneFlexuralCheck\">\r\n            <TabsList>\r\n              <TabsTrigger value=\"inPlaneFlexuralCheck\">\r\n                {t('inPlaneFlexuralCheck.label')}\r\n              </TabsTrigger>\r\n              <TabsTrigger value=\"outOfPlaneFlexuralCheck\">\r\n                {t('outOfPlaneFlexuralCheck.label')}\r\n              </TabsTrigger>\r\n              <TabsTrigger value=\"shearCheck\">\r\n                {t('shearCheck.label')}\r\n              </TabsTrigger>\r\n              <TabsTrigger value=\"extremityDetachementCheck\">\r\n                {t('extremityDetachementCheck.label')}\r\n              </TabsTrigger>\r\n            </TabsList>\r\n            <TabsContent value=\"inPlaneFlexuralCheck\">\r\n              <InPlaneFlexuralCheckResultCard\r\n                inPlaneFlexuralCheckSchema={\r\n                  flexuralReinforcementCalculationResult?.inPlaneFlexuralCheck\r\n                }\r\n              />\r\n            </TabsContent>\r\n            <TabsContent value=\"outOfPlaneFlexuralCheck\">\r\n              <OutOfPlaneFlexuralCheckResultCard\r\n                outOfPlaneFlexuralCheckSchema={\r\n                  flexuralReinforcementCalculationResult?.outOfPlaneFlexuralCheck\r\n                }\r\n              />\r\n            </TabsContent>\r\n            <TabsContent value=\"shearCheck\">\r\n              <ShearCheckResultCard\r\n                shearCheckResults={\r\n                  flexuralReinforcementCalculationResult?.shearCheck\r\n                }\r\n              />\r\n            </TabsContent>\r\n            <TabsContent value=\"extremityDetachementCheck\">\r\n              <ExtremityDetachementCheck\r\n                extremityDetachementCheck={\r\n                  flexuralReinforcementCalculationResult?.extremityDetachementCheck\r\n                }\r\n              />\r\n            </TabsContent>\r\n          </Tabs>\r\n        </CardContent>\r\n      </Card>\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAMA;AAOA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;;AAMO,SAAS,mDAAmD,KAE3D;QAF2D,EACjE,sCAAsC,EAChC,GAF2D;;IAGjE,MAAM,IAAI,IAAA,4NAAe,EACvB;IAEF,MAAM,UAAU,IAAA,kLAAS;IAEzB,qBACE;;0BACE,6LAAC;gBAAG,WAAU;0BAA4B,EAAE;;;;;;0BAC5C,6LAAC,2IAAI;;kCACH,6LAAC,iJAAU;kCACT,cAAA,6LAAC,gJAAS;sCAAE,EAAE;;;;;;;;;;;kCAEhB,6LAAC,kJAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC,2IAAI;4BAAC,cAAa;;8CACjB,6LAAC,+IAAQ;;sDACP,6LAAC,kJAAW;4CAAC,OAAM;sDAChB,EAAE;;;;;;sDAEL,6LAAC,kJAAW;4CAAC,OAAM;sDAChB,EAAE;;;;;;sDAEL,6LAAC,kJAAW;4CAAC,OAAM;sDAChB,EAAE;;;;;;sDAEL,6LAAC,kJAAW;4CAAC,OAAM;sDAChB,EAAE;;;;;;;;;;;;8CAGP,6LAAC,kJAAW;oCAAC,OAAM;8CACjB,cAAA,6LAAC,6UAA8B;wCAC7B,0BAA0B,EACxB,mDAAA,6DAAA,uCAAwC,oBAAoB;;;;;;;;;;;8CAIlE,6LAAC,kJAAW;oCAAC,OAAM;8CACjB,cAAA,6LAAC,uVAAiC;wCAChC,6BAA6B,EAC3B,mDAAA,6DAAA,uCAAwC,uBAAuB;;;;;;;;;;;8CAIrE,6LAAC,kJAAW;oCAAC,OAAM;8CACjB,cAAA,6LAAC,0TAAoB;wCACnB,iBAAiB,EACf,mDAAA,6DAAA,uCAAwC,UAAU;;;;;;;;;;;8CAIxD,6LAAC,kJAAW;oCAAC,OAAM;8CACjB,cAAA,6LAAC,kVAAyB;wCACxB,yBAAyB,EACvB,mDAAA,6DAAA,uCAAwC,yBAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASnF;GAhEgB;;QAGJ,4NAAe;QAGT,kLAAS;;;KANX", "debugId": null}}, {"offset": {"line": 1708, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/flexural/maschi-murari-reinforcement-flexural-calculation.tsx"], "sourcesContent": ["import { CustomProductSection } from '@atlas/components/common/atlas/product/custom-product-section'\r\nimport { ProductSummaryCard } from '@atlas/components/common/atlas/product/product-summary-card'\r\nimport { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport {\r\n  FACING_MATERIAL,\r\n  MODULE_REINFORCEMENT_ARRANGEMENT,\r\n  MODULE_REINFORCEMENT_FAILURE_MODE,\r\n  REINFORCEMENT_ARRANGEMENT,\r\n  REINFORCEMENT_FAILURE_MODE,\r\n} from '@atlas/constants/module'\r\nimport type { ModuleWithParamsMaschiMurari } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useModuleCalculation } from '@atlas/lib/mutation/modules/use-module-calculation'\r\nimport { useProductsByCategory } from '@atlas/lib/query/products/use-products-by-category'\r\nimport {\r\n  type MaschiMurariFlexuralReinforcementExecutionSchemaInput,\r\n  maschiMurariFlexuralReinforcementExecutionSchema,\r\n} from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useMemo } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\nimport { MaschiMurariReinforcementFlexuralCalculationResult } from './maschi-murari-reinforcement-flexural-calculation-result'\r\n\r\ntype Props = {\r\n  module: ModuleWithParamsMaschiMurari\r\n  session: Session\r\n  projectId: Project['id']\r\n}\r\n\r\n// N.B: this function is temporary, until we remove the quotes in future seed\r\nfunction getCleanedUpValue(\r\n  recordToClean: Record<string, number> | undefined,\r\n  nature: string,\r\n) {\r\n  // nature should be 'BRICK', 'TUFF', or 'STONE'\r\n  if (!recordToClean) {\r\n    return undefined\r\n  }\r\n  const key = `\"${nature}\"` // matches the API key format\r\n  return recordToClean[key]\r\n}\r\n\r\nexport const MaschiMurariFlexuralCalculation = ({\r\n  session,\r\n  module,\r\n  projectId,\r\n}: Props) => {\r\n  const t = useTranslations('forms.calculations.maschi-murari.flexural')\r\n  const tAction = useTranslations('actions.calculations.maschi-murari')\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  const {\r\n    params,\r\n    flexuralReinforcementVerifyExecutionInput,\r\n    flexuralReinforcementCalculationResult,\r\n  } = module\r\n\r\n  const form = useForm<MaschiMurariFlexuralReinforcementExecutionSchemaInput>({\r\n    resolver: zodResolver(maschiMurariFlexuralReinforcementExecutionSchema),\r\n    defaultValues: {\r\n      calculationType: 'FLEXURAL_VERIFY',\r\n      input: {\r\n        product: {\r\n          id: flexuralReinforcementVerifyExecutionInput?.product.id,\r\n          name: flexuralReinforcementVerifyExecutionInput?.product.name,\r\n          sourceType:\r\n            flexuralReinforcementVerifyExecutionInput?.product.id === 'custom'\r\n              ? 'CUSTOM'\r\n              : 'DATABASE',\r\n        },\r\n        sectionFailureMode:\r\n          flexuralReinforcementVerifyExecutionInput?.sectionFailureMode ??\r\n          REINFORCEMENT_FAILURE_MODE.DISTACCO_DI_ESTREMITA,\r\n        designReinforcementStress:\r\n          flexuralReinforcementVerifyExecutionInput?.designReinforcementStress ??\r\n          0,\r\n        designReinforcementStrain:\r\n          flexuralReinforcementVerifyExecutionInput?.designReinforcementStrain ??\r\n          0,\r\n        reinforcedArrangement:\r\n          flexuralReinforcementVerifyExecutionInput?.reinforcedArrangement ??\r\n          REINFORCEMENT_ARRANGEMENT.CONTINUE,\r\n        singleStripWidth:\r\n          flexuralReinforcementVerifyExecutionInput?.singleStripWidth ?? 0,\r\n        stripSpacing:\r\n          flexuralReinforcementVerifyExecutionInput?.stripSpacing ?? 0,\r\n        reinforcementTotalWidthAlongLength:\r\n          flexuralReinforcementVerifyExecutionInput?.reinforcementTotalWidthAlongLength ??\r\n          0,\r\n        compressedEdgeReinforcementFiberDistance:\r\n          flexuralReinforcementVerifyExecutionInput?.compressedEdgeReinforcementFiberDistance ??\r\n          0,\r\n        layersNumber:\r\n          flexuralReinforcementVerifyExecutionInput?.layersNumber ?? 1,\r\n        reinforcedSidesNumber:\r\n          flexuralReinforcementVerifyExecutionInput?.reinforcedSidesNumber ?? 0,\r\n        totalEquivalentThickness:\r\n          flexuralReinforcementVerifyExecutionInput?.totalEquivalentThickness ??\r\n          0,\r\n        firstCoefficient:\r\n          flexuralReinforcementVerifyExecutionInput?.firstCoefficient ?? 0,\r\n        secondCoefficient:\r\n          flexuralReinforcementVerifyExecutionInput?.secondCoefficient ?? 0,\r\n\r\n        // Only for out-of-plane bending\r\n        outOfPlanUnitWidthPanel:\r\n          flexuralReinforcementVerifyExecutionInput?.outOfPlanUnitWidthPanel ??\r\n          1000,\r\n        outOfPlanAppliedDesignAxialStress:\r\n          flexuralReinforcementVerifyExecutionInput?.outOfPlanAppliedDesignAxialStress ??\r\n          0,\r\n        outOfPlaneReinforcementFailureMode:\r\n          flexuralReinforcementVerifyExecutionInput?.outOfPlaneReinforcementFailureMode ??\r\n          REINFORCEMENT_FAILURE_MODE.DISTACCO_DI_ESTREMITA,\r\n        outOfPlaneDesignReinforcementStrain:\r\n          flexuralReinforcementVerifyExecutionInput?.outOfPlaneDesignReinforcementStrain ??\r\n          0,\r\n        outOfPlaneDesignReinforcementStress:\r\n          flexuralReinforcementVerifyExecutionInput?.outOfPlaneDesignReinforcementStress ??\r\n          0,\r\n\r\n        outOfPlaneReinforcementTotalWidthAlongLength:\r\n          flexuralReinforcementVerifyExecutionInput?.outOfPlaneReinforcementTotalWidthAlongLength ??\r\n          0,\r\n        outOfPlaneResistingArea:\r\n          flexuralReinforcementVerifyExecutionInput?.outOfPlaneResistingArea ??\r\n          0,\r\n      },\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useModuleCalculation(session.accessToken, {\r\n    onSuccess: () => {\r\n      toast.success(tAction('calculate.success'))\r\n    },\r\n    onError: error => {\r\n      console.log('ERROR  ', error)\r\n      toast.error(tAction('calculate.failure', { error: error.message }))\r\n    },\r\n  })\r\n\r\n  const handleFormSubmit = (\r\n    body: MaschiMurariFlexuralReinforcementExecutionSchemaInput,\r\n  ) => {\r\n    mutate({ projectId, moduleId: module.id, body })\r\n  }\r\n\r\n  const {\r\n    data: products,\r\n    isError: errorGettingProducts,\r\n    isLoading: isLoadingProducts,\r\n  } = useProductsByCategory(session, 'MASCHI_MURARI', 0, 100)\r\n\r\n  const productsOptions = [\r\n    ...(products?.content.map(m => ({\r\n      value: m.id,\r\n      label: m.name ?? t('product.unnamed'),\r\n    })) ?? []),\r\n  ]\r\n\r\n  const [productId] = form.watch(['input.product.id'])\r\n\r\n  const selectedProduct = useMemo(\r\n    () => products?.content.find(p => p.id === productId),\r\n    [productId, products],\r\n  )\r\n\r\n  const conventionalStrainLimit = selectedProduct?.conventionalStrainLimit\r\n  const conversionFactor = params?.materialProperties?.conversionFactor ?? 0\r\n  const sectionFailureModeValue =\r\n    form.watch('input.sectionFailureMode') ??\r\n    REINFORCEMENT_FAILURE_MODE.DISTACCO_DI_ESTREMITA\r\n\r\n  const structuralElementsNatureValue =\r\n    params?.materialProperties?.structuralElementsNature ??\r\n    FACING_MATERIAL.BRICK\r\n\r\n  const _panelGeometryHeight = params?.panelGeometry?.height ?? 0\r\n  const panelGeometryWidth = params?.panelGeometry?.width ?? 0\r\n  const singleStripWidthValue = form.watch('input.singleStripWidth') ?? 0\r\n  const stripSpacingValue = form.watch('input.stripSpacing') ?? 0\r\n\r\n  const conversionFactorValue =\r\n    params?.materialProperties?.conversionFactor ?? 0\r\n  const ultimateCompressiveStrainLinearBehaviourValue =\r\n    params?.materialProperties?.ultimateCompressiveStrainLinearBehaviour ?? 0\r\n  const ultimateCompressiveStrainValue =\r\n    params?.materialProperties?.ultimateCompressiveStrain ?? 0\r\n\r\n  const appliedNormalStressValue = params?.actions?.appliedNormalStress ?? 0\r\n\r\n  const reinforcedArrangementValue =\r\n    form.watch('input.reinforcedArrangement') ??\r\n    REINFORCEMENT_ARRANGEMENT.CONTINUE\r\n\r\n  const reinforcedSidesNumberValue =\r\n    form.watch('input.reinforcedSidesNumber') ?? 0\r\n\r\n  useEffect(() => {\r\n    form.setValue('input.layersNumber', 1)\r\n    form.setValue('input.outOfPlanUnitWidthPanel', 1000)\r\n    form.setValue(\r\n      'input.outOfPlaneReinforcementFailureMode',\r\n      REINFORCEMENT_FAILURE_MODE.DISTACCO_INTERMEDIO,\r\n    )\r\n    // D61 = designReinforcementStrain =\r\n    // =IF(D42=\"Distacco intermedio\",(1.5*D41*D26/1.5),(D41*D26/1.5))\r\n    // D42 = input.reinforcementFailureMode\r\n    // D41 = product.conventionalStrainLimit\r\n    // D26 = materialProperties.conversionFactor\r\n    const conventionalStrainLimitValue =\r\n      getCleanedUpValue(\r\n        conventionalStrainLimit,\r\n        structuralElementsNatureValue,\r\n      ) ?? 0\r\n\r\n    const designReinforcementStrain =\r\n      sectionFailureModeValue === REINFORCEMENT_FAILURE_MODE.DISTACCO_INTERMEDIO\r\n        ? (1.5 * conventionalStrainLimitValue * conversionFactor) / 1.5\r\n        : (conventionalStrainLimitValue * conversionFactor) / 1.5\r\n\r\n    form.setValue('input.designReinforcementStrain', designReinforcementStrain)\r\n\r\n    // D62 = designReinforcementStress = =D43*D38\r\n    // D43 = designReinforcementStrain\r\n    // D38 = product.elasticModulus\r\n    const elasticModulus = selectedProduct?.elasticModulus ?? 0\r\n    const designReinforcementStress = designReinforcementStrain * elasticModulus\r\n    form.setValue('input.designReinforcementStress', designReinforcementStress)\r\n\r\n    // D66 reinforcementTotalWidthAlongLength =\r\n    //  =IF(D63=\"Continuo\",D9,D64*(D9/D65))\r\n    // D63 = input.reinforcedArrangement\r\n    // D64 = input.singleStripWidth\r\n    // D65 = input.stripSpacing\r\n    // D9 = width\r\n    const reinforcementTotalWidthAlongLength =\r\n      reinforcedArrangementValue === REINFORCEMENT_ARRANGEMENT.CONTINUE\r\n        ? panelGeometryWidth\r\n        : singleStripWidthValue * (panelGeometryWidth / stripSpacingValue)\r\n\r\n    form.setValue(\r\n      'input.reinforcementTotalWidthAlongLength',\r\n      reinforcementTotalWidthAlongLength,\r\n    )\r\n\r\n    // totalEquivalentThickness\r\n    // =IF(D63=\"Continuo\",D69*D57,D69*D57*D64/D65)\r\n    // D69 = reinforcedSidesNumber\r\n    // D64 = input.singleStripWidth\r\n    // D65 = input.stripSpacing\r\n    // D57 = thickness of the product\r\n    const thicknessOfTheProduct = selectedProduct?.thickness ?? 0\r\n    const totalEquivalentThickness =\r\n      reinforcedArrangementValue === REINFORCEMENT_ARRANGEMENT.CONTINUE\r\n        ? reinforcedSidesNumberValue * singleStripWidthValue\r\n        : (reinforcedSidesNumberValue *\r\n            thicknessOfTheProduct *\r\n            singleStripWidthValue) /\r\n          stripSpacingValue\r\n    form.setValue('input.totalEquivalentThickness', totalEquivalentThickness)\r\n\r\n    // first Coefficient = D24/D25\r\n    // D24: materialProperties.ultimateCompressiveStrainLinearBehaviour\r\n    // D25: materialProperties.ultimateCompressiveStrain\r\n    const firstCoefficient =\r\n      ultimateCompressiveStrainLinearBehaviourValue /\r\n      ultimateCompressiveStrainValue\r\n    form.setValue('input.firstCoefficient', firstCoefficient)\r\n\r\n    // second Coefficient = D24/D61\r\n    // D61 = designReinforcementStrain\r\n    const secondCoefficient =\r\n      ultimateCompressiveStrainLinearBehaviourValue / designReinforcementStrain\r\n    form.setValue('input.secondCoefficient', secondCoefficient)\r\n\r\n    // OUT OF PLANE BENDING\r\n    // D79 = outOfPlanAppliedDesignAxialStress=D29\r\n    // D29 = actions.appliedNormalStress\r\n    form.setValue(\r\n      'input.outOfPlanAppliedDesignAxialStress',\r\n      appliedNormalStressValue,\r\n    )\r\n\r\n    // D81 = outOfPlaneDesignReinforcementStrain\r\n    // =  =(1.5*D59*D26/1.5)\r\n    // D59 = product.conventionalStrainLimit\r\n    // D26 = materialProperties.conversionFactor\r\n    const outOfPlaneDesignReinforcementStrain =\r\n      (1.5 * conventionalStrainLimitValue * conversionFactorValue) / 1.5\r\n    form.setValue(\r\n      'input.outOfPlaneDesignReinforcementStrain',\r\n      outOfPlaneDesignReinforcementStrain,\r\n    )\r\n\r\n    // D82= outOfPlaneDesignReinforcementStrain=D81*D56\r\n    // D56 = product.elasticModulus\r\n    form.setValue(\r\n      'input.outOfPlaneDesignReinforcementStress',\r\n      outOfPlaneDesignReinforcementStrain * elasticModulus,\r\n    )\r\n\r\n    // D83 = outOfPlaneReinforcementTotalWidthAlongLength\r\n    // =IF(D63=\"Continuo\",D78,D64*(D78/D65))\r\n    // D78 = width = outOfPlanUnitWidthPanel = 1000\r\n    // D63 = input.reinforcedArrangement\r\n    // D64 = input.singleStripWidth\r\n    // D65 = input.stripSpacing\r\n    const outOfPlaneReinforcementTotalWidthAlongLength =\r\n      reinforcedArrangementValue === REINFORCEMENT_ARRANGEMENT.CONTINUE\r\n        ? 1000\r\n        : singleStripWidthValue * (1000 / stripSpacingValue)\r\n    form.setValue(\r\n      'input.outOfPlaneReinforcementTotalWidthAlongLength',\r\n      outOfPlaneReinforcementTotalWidthAlongLength,\r\n    )\r\n\r\n    // D85 = outOfPlaneResistingArea\r\n    // =D57*D68*D83\r\n    // D57 = thickness of the product\r\n    // D68 = reinforcedLayersNumber = 1\r\n    form.setValue(\r\n      'input.outOfPlaneResistingArea',\r\n      thicknessOfTheProduct * 1 * outOfPlaneReinforcementTotalWidthAlongLength,\r\n    )\r\n\r\n    if (!productId) {\r\n      return\r\n    }\r\n    if (productId === 'custom') {\r\n      form.setValue('input.product.sourceType', 'CUSTOM')\r\n    }\r\n\r\n    if (selectedProduct) {\r\n      form.setValue('input.product', {\r\n        ...selectedProduct,\r\n        sourceType: 'DATABASE',\r\n      })\r\n    }\r\n  }, [\r\n    form,\r\n    productId,\r\n    selectedProduct,\r\n    conversionFactor,\r\n    sectionFailureModeValue,\r\n    conventionalStrainLimit,\r\n    structuralElementsNatureValue,\r\n    panelGeometryWidth,\r\n    singleStripWidthValue,\r\n    stripSpacingValue,\r\n    reinforcedArrangementValue,\r\n    reinforcedSidesNumberValue,\r\n    ultimateCompressiveStrainLinearBehaviourValue,\r\n    ultimateCompressiveStrainValue,\r\n    appliedNormalStressValue,\r\n    conversionFactorValue,\r\n  ])\r\n\r\n  return (\r\n    <div className=\"flex flex-col justify-center gap-4\">\r\n      <Form {...form}>\r\n        <form\r\n          className=\"space-y-4 rounded-md border p-4\"\r\n          onSubmit={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          <h1 className=\"text-3xl font-bold\">{t('heading')}</h1>\r\n          <Image\r\n            src=\"/assets/masonry_frcm/column/FRCM_COLONNE MURATURA.jpg\"\r\n            alt=\"flexural verify\"\r\n            height={250}\r\n            width={500}\r\n            className=\"mx-auto rounded-md object-contain\"\r\n            priority\r\n          />\r\n          <h1 className=\"text-xl font-bold\">{t('inPlane.title')}</h1>\r\n          <SelectFormInput\r\n            control={form.control}\r\n            name=\"input.product.id\"\r\n            options={productsOptions}\r\n            t={t}\r\n            loading={isLoadingProducts}\r\n            requestError={errorGettingProducts}\r\n            errorMessage={t('products.error')}\r\n          />\r\n          {productId === 'custom' && <CustomProductSection />}\r\n          {selectedProduct && <ProductSummaryCard product={selectedProduct} />}\r\n          <Separator />\r\n          <SelectFormFixedInput\r\n            control={form.control}\r\n            name=\"input.sectionFailureMode\"\r\n            options={MODULE_REINFORCEMENT_FAILURE_MODE}\r\n            optionLabelFn={p => t(`input.sectionFailureMode.${p}`)}\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.designReinforcementStrain\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.designReinforcementStress\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <SelectFormFixedInput\r\n            control={form.control}\r\n            name=\"input.reinforcedArrangement\"\r\n            options={MODULE_REINFORCEMENT_ARRANGEMENT}\r\n            optionLabelFn={p => t(`input.reinforcedArrangement.${p}`)}\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.singleStripWidth\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.stripSpacing\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.reinforcementTotalWidthAlongLength\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.compressedEdgeReinforcementFiberDistance\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.layersNumber\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.reinforcedSidesNumber\"\r\n            t={t}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.totalEquivalentThickness\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.firstCoefficient\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.secondCoefficient\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <Separator />\r\n          <h1 className=\"text-xl font-bold\">{t('outOfPlane.title')}</h1>\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.outOfPlanUnitWidthPanel\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.outOfPlanAppliedDesignAxialStress\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <SelectFormFixedInput\r\n            control={form.control}\r\n            name=\"input.outOfPlaneReinforcementFailureMode\"\r\n            options={MODULE_REINFORCEMENT_FAILURE_MODE}\r\n            optionLabelFn={p =>\r\n              t(`input.outOfPlaneReinforcementFailureMode.${p}`)\r\n            }\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.outOfPlaneDesignReinforcementStrain\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.outOfPlaneDesignReinforcementStress\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.outOfPlaneReinforcementTotalWidthAlongLength\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.outOfPlaneResistingArea\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full sm:w-auto\"\r\n            disabled={isPending}\r\n          >\r\n            {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n            {tCommon('calculate')}\r\n          </Button>\r\n        </form>\r\n      </Form>\r\n      {flexuralReinforcementCalculationResult && (\r\n        <MaschiMurariReinforcementFlexuralCalculationResult\r\n          flexuralReinforcementCalculationResult={\r\n            flexuralReinforcementCalculationResult\r\n          }\r\n        />\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AASA;AACA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;AAQA,6EAA6E;AAC7E,SAAS,kBACP,aAAiD,EACjD,MAAc;IAEd,+CAA+C;IAC/C,IAAI,CAAC,eAAe;QAClB,OAAO;IACT;IACA,MAAM,MAAM,AAAC,IAAU,OAAP,QAAO,KAAG,6BAA6B;;IACvD,OAAO,aAAa,CAAC,IAAI;AAC3B;AAEO,MAAM,kCAAkC;QAAC,EAC9C,OAAO,EACP,MAAM,EACN,SAAS,EACH;QA0HmB,4BAMvB,6BAG2B,uBACF,wBAKzB,6BAEA,6BAEA,6BAE+B;;IA9IjC,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAEhC,MAAM,EACJ,MAAM,EACN,yCAAyC,EACzC,sCAAsC,EACvC,GAAG;QAgBI,+DAGA,sEAGA,sEAGA,kEAGA,6DAEA,yDAEA,+EAGA,qFAGA,yDAEA,kEAEA,qEAGA,6DAEA,8DAIA,oEAGA,8EAGA,+EAGA,gFAGA,gFAIA,yFAGA;IApER,MAAM,OAAO,IAAA,4KAAO,EAAwD;QAC1E,UAAU,IAAA,gLAAW,EAAC,qNAAgD;QACtE,eAAe;YACb,iBAAiB;YACjB,OAAO;gBACL,SAAS;oBACP,EAAE,EAAE,sDAAA,gEAAA,0CAA2C,OAAO,CAAC,EAAE;oBACzD,IAAI,EAAE,sDAAA,gEAAA,0CAA2C,OAAO,CAAC,IAAI;oBAC7D,YACE,CAAA,sDAAA,gEAAA,0CAA2C,OAAO,CAAC,EAAE,MAAK,WACtD,WACA;gBACR;gBACA,oBACE,CAAA,gEAAA,sDAAA,gEAAA,0CAA2C,kBAAkB,cAA7D,2EAAA,gEACA,4JAA0B,CAAC,qBAAqB;gBAClD,2BACE,CAAA,uEAAA,sDAAA,gEAAA,0CAA2C,yBAAyB,cAApE,kFAAA,uEACA;gBACF,2BACE,CAAA,uEAAA,sDAAA,gEAAA,0CAA2C,yBAAyB,cAApE,kFAAA,uEACA;gBACF,uBACE,CAAA,mEAAA,sDAAA,gEAAA,0CAA2C,qBAAqB,cAAhE,8EAAA,mEACA,2JAAyB,CAAC,QAAQ;gBACpC,kBACE,CAAA,8DAAA,sDAAA,gEAAA,0CAA2C,gBAAgB,cAA3D,yEAAA,8DAA+D;gBACjE,cACE,CAAA,0DAAA,sDAAA,gEAAA,0CAA2C,YAAY,cAAvD,qEAAA,0DAA2D;gBAC7D,oCACE,CAAA,gFAAA,sDAAA,gEAAA,0CAA2C,kCAAkC,cAA7E,2FAAA,gFACA;gBACF,0CACE,CAAA,sFAAA,sDAAA,gEAAA,0CAA2C,wCAAwC,cAAnF,iGAAA,sFACA;gBACF,cACE,CAAA,0DAAA,sDAAA,gEAAA,0CAA2C,YAAY,cAAvD,qEAAA,0DAA2D;gBAC7D,uBACE,CAAA,mEAAA,sDAAA,gEAAA,0CAA2C,qBAAqB,cAAhE,8EAAA,mEAAoE;gBACtE,0BACE,CAAA,sEAAA,sDAAA,gEAAA,0CAA2C,wBAAwB,cAAnE,iFAAA,sEACA;gBACF,kBACE,CAAA,8DAAA,sDAAA,gEAAA,0CAA2C,gBAAgB,cAA3D,yEAAA,8DAA+D;gBACjE,mBACE,CAAA,+DAAA,sDAAA,gEAAA,0CAA2C,iBAAiB,cAA5D,0EAAA,+DAAgE;gBAElE,gCAAgC;gBAChC,yBACE,CAAA,qEAAA,sDAAA,gEAAA,0CAA2C,uBAAuB,cAAlE,gFAAA,qEACA;gBACF,mCACE,CAAA,+EAAA,sDAAA,gEAAA,0CAA2C,iCAAiC,cAA5E,0FAAA,+EACA;gBACF,oCACE,CAAA,gFAAA,sDAAA,gEAAA,0CAA2C,kCAAkC,cAA7E,2FAAA,gFACA,4JAA0B,CAAC,qBAAqB;gBAClD,qCACE,CAAA,iFAAA,sDAAA,gEAAA,0CAA2C,mCAAmC,cAA9E,4FAAA,iFACA;gBACF,qCACE,CAAA,iFAAA,sDAAA,gEAAA,0CAA2C,mCAAmC,cAA9E,4FAAA,iFACA;gBAEF,8CACE,CAAA,0FAAA,sDAAA,gEAAA,0CAA2C,4CAA4C,cAAvF,qGAAA,0FACA;gBACF,yBACE,CAAA,qEAAA,sDAAA,gEAAA,0CAA2C,uBAAuB,cAAlE,gFAAA,qEACA;YACJ;QACF;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,6LAAoB,EAAC,QAAQ,WAAW,EAAE;QACtE,SAAS;oEAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;YACxB;;QACA,OAAO;oEAAE,CAAA;gBACP,QAAQ,GAAG,CAAC,WAAW;gBACvB,oJAAK,CAAC,KAAK,CAAC,QAAQ,qBAAqB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAClE;;IACF;IAEA,MAAM,mBAAmB,CACvB;QAEA,OAAO;YAAE;YAAW,UAAU,OAAO,EAAE;YAAE;QAAK;IAChD;IAEA,MAAM,EACJ,MAAM,QAAQ,EACd,SAAS,oBAAoB,EAC7B,WAAW,iBAAiB,EAC7B,GAAG,IAAA,iMAAqB,EAAC,SAAS,iBAAiB,GAAG;QAGjD;IADN,MAAM,kBAAkB;WAClB,CAAA,wBAAA,qBAAA,+BAAA,SAAU,OAAO,CAAC,GAAG,CAAC,CAAA;gBAEjB;mBAFuB;gBAC9B,OAAO,EAAE,EAAE;gBACX,OAAO,CAAA,UAAA,EAAE,IAAI,cAAN,qBAAA,UAAU,EAAE;YACrB;wBAHI,mCAAA,wBAGG,EAAE;KACV;IAED,MAAM,CAAC,UAAU,GAAG,KAAK,KAAK,CAAC;QAAC;KAAmB;IAEnD,MAAM,kBAAkB,IAAA,wKAAO;oEAC7B,IAAM,qBAAA,+BAAA,SAAU,OAAO,CAAC,IAAI;4EAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;mEAC3C;QAAC;QAAW;KAAS;IAGvB,MAAM,0BAA0B,4BAAA,sCAAA,gBAAiB,uBAAuB;QAC/C;IAAzB,MAAM,mBAAmB,CAAA,8CAAA,mBAAA,8BAAA,6BAAA,OAAQ,kBAAkB,cAA1B,iDAAA,2BAA4B,gBAAgB,cAA5C,yDAAA,8CAAgD;QAEvE;IADF,MAAM,0BACJ,CAAA,cAAA,KAAK,KAAK,CAAC,yCAAX,yBAAA,cACA,4JAA0B,CAAC,qBAAqB;QAGhD;IADF,MAAM,gCACJ,CAAA,sDAAA,mBAAA,8BAAA,8BAAA,OAAQ,kBAAkB,cAA1B,kDAAA,4BAA4B,wBAAwB,cAApD,iEAAA,sDACA,iJAAe,CAAC,KAAK;QAEM;IAA7B,MAAM,uBAAuB,CAAA,+BAAA,mBAAA,8BAAA,wBAAA,OAAQ,aAAa,cAArB,4CAAA,sBAAuB,MAAM,cAA7B,0CAAA,+BAAiC;QACnC;IAA3B,MAAM,qBAAqB,CAAA,8BAAA,mBAAA,8BAAA,yBAAA,OAAQ,aAAa,cAArB,6CAAA,uBAAuB,KAAK,cAA5B,yCAAA,8BAAgC;QAC7B;IAA9B,MAAM,wBAAwB,CAAA,eAAA,KAAK,KAAK,CAAC,uCAAX,0BAAA,eAAwC;QAC5C;IAA1B,MAAM,oBAAoB,CAAA,eAAA,KAAK,KAAK,CAAC,mCAAX,0BAAA,eAAoC;QAG5D;IADF,MAAM,wBACJ,CAAA,+CAAA,mBAAA,8BAAA,8BAAA,OAAQ,kBAAkB,cAA1B,kDAAA,4BAA4B,gBAAgB,cAA5C,0DAAA,+CAAgD;QAEhD;IADF,MAAM,gDACJ,CAAA,sEAAA,mBAAA,8BAAA,8BAAA,OAAQ,kBAAkB,cAA1B,kDAAA,4BAA4B,wCAAwC,cAApE,iFAAA,sEAAwE;QAExE;IADF,MAAM,iCACJ,CAAA,uDAAA,mBAAA,8BAAA,8BAAA,OAAQ,kBAAkB,cAA1B,kDAAA,4BAA4B,yBAAyB,cAArD,kEAAA,uDAAyD;QAE1B;IAAjC,MAAM,2BAA2B,CAAA,sCAAA,mBAAA,8BAAA,kBAAA,OAAQ,OAAO,cAAf,sCAAA,gBAAiB,mBAAmB,cAApC,iDAAA,sCAAwC;QAGvE;IADF,MAAM,6BACJ,CAAA,eAAA,KAAK,KAAK,CAAC,4CAAX,0BAAA,eACA,2JAAyB,CAAC,QAAQ;QAGlC;IADF,MAAM,6BACJ,CAAA,eAAA,KAAK,KAAK,CAAC,4CAAX,0BAAA,eAA6C;IAE/C,IAAA,0KAAS;qDAAC;YACR,KAAK,QAAQ,CAAC,sBAAsB;YACpC,KAAK,QAAQ,CAAC,iCAAiC;YAC/C,KAAK,QAAQ,CACX,4CACA,4JAA0B,CAAC,mBAAmB;gBAQ9C;YANF,oCAAoC;YACpC,iEAAiE;YACjE,uCAAuC;YACvC,wCAAwC;YACxC,4CAA4C;YAC5C,MAAM,+BACJ,CAAA,qBAAA,kBACE,yBACA,4CAFF,gCAAA,qBAGK;YAEP,MAAM,4BACJ,4BAA4B,4JAA0B,CAAC,mBAAmB,GACtE,AAAC,MAAM,+BAA+B,mBAAoB,MAC1D,AAAC,+BAA+B,mBAAoB;YAE1D,KAAK,QAAQ,CAAC,mCAAmC;gBAK1B;YAHvB,6CAA6C;YAC7C,kCAAkC;YAClC,+BAA+B;YAC/B,MAAM,iBAAiB,CAAA,kCAAA,4BAAA,sCAAA,gBAAiB,cAAc,cAA/B,6CAAA,kCAAmC;YAC1D,MAAM,4BAA4B,4BAA4B;YAC9D,KAAK,QAAQ,CAAC,mCAAmC;YAEjD,2CAA2C;YAC3C,uCAAuC;YACvC,oCAAoC;YACpC,+BAA+B;YAC/B,2BAA2B;YAC3B,aAAa;YACb,MAAM,qCACJ,+BAA+B,2JAAyB,CAAC,QAAQ,GAC7D,qBACA,wBAAwB,CAAC,qBAAqB,iBAAiB;YAErE,KAAK,QAAQ,CACX,4CACA;gBAS4B;YAN9B,2BAA2B;YAC3B,8CAA8C;YAC9C,8BAA8B;YAC9B,+BAA+B;YAC/B,2BAA2B;YAC3B,iCAAiC;YACjC,MAAM,wBAAwB,CAAA,6BAAA,4BAAA,sCAAA,gBAAiB,SAAS,cAA1B,wCAAA,6BAA8B;YAC5D,MAAM,2BACJ,+BAA+B,2JAAyB,CAAC,QAAQ,GAC7D,6BAA6B,wBAC7B,AAAC,6BACC,wBACA,wBACF;YACN,KAAK,QAAQ,CAAC,kCAAkC;YAEhD,8BAA8B;YAC9B,mEAAmE;YACnE,oDAAoD;YACpD,MAAM,mBACJ,gDACA;YACF,KAAK,QAAQ,CAAC,0BAA0B;YAExC,+BAA+B;YAC/B,kCAAkC;YAClC,MAAM,oBACJ,gDAAgD;YAClD,KAAK,QAAQ,CAAC,2BAA2B;YAEzC,uBAAuB;YACvB,8CAA8C;YAC9C,oCAAoC;YACpC,KAAK,QAAQ,CACX,2CACA;YAGF,4CAA4C;YAC5C,wBAAwB;YACxB,wCAAwC;YACxC,4CAA4C;YAC5C,MAAM,sCACJ,AAAC,MAAM,+BAA+B,wBAAyB;YACjE,KAAK,QAAQ,CACX,6CACA;YAGF,mDAAmD;YACnD,+BAA+B;YAC/B,KAAK,QAAQ,CACX,6CACA,sCAAsC;YAGxC,qDAAqD;YACrD,wCAAwC;YACxC,+CAA+C;YAC/C,oCAAoC;YACpC,+BAA+B;YAC/B,2BAA2B;YAC3B,MAAM,+CACJ,+BAA+B,2JAAyB,CAAC,QAAQ,GAC7D,OACA,wBAAwB,CAAC,OAAO,iBAAiB;YACvD,KAAK,QAAQ,CACX,sDACA;YAGF,gCAAgC;YAChC,eAAe;YACf,iCAAiC;YACjC,mCAAmC;YACnC,KAAK,QAAQ,CACX,iCACA,wBAAwB,IAAI;YAG9B,IAAI,CAAC,WAAW;gBACd;YACF;YACA,IAAI,cAAc,UAAU;gBAC1B,KAAK,QAAQ,CAAC,4BAA4B;YAC5C;YAEA,IAAI,iBAAiB;gBACnB,KAAK,QAAQ,CAAC,iBAAiB;oBAC7B,GAAG,eAAe;oBAClB,YAAY;gBACd;YACF;QACF;oDAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,2IAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,6LAAC;oBACC,WAAU;oBACV,UAAU,KAAK,YAAY,CAAC;;sCAE5B,6LAAC;4BAAG,WAAU;sCAAsB,EAAE;;;;;;sCACtC,6LAAC,2IAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,QAAQ;4BACR,OAAO;4BACP,WAAU;4BACV,QAAQ;;;;;;sCAEV,6LAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS;4BACT,GAAG;4BACH,SAAS;4BACT,cAAc;4BACd,cAAc,EAAE;;;;;;wBAEjB,cAAc,0BAAY,6LAAC,2MAAoB;;;;;wBAC/C,iCAAmB,6LAAC,uMAAkB;4BAAC,SAAS;;;;;;sCACjD,6LAAC,qJAAS;;;;;sCACV,6LAAC,mMAAoB;4BACnB,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS,mKAAiC;4BAC1C,eAAe,CAAA,IAAK,EAAE,AAAC,4BAA6B,OAAF;4BAClD,GAAG;;;;;;sCAEL,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,mMAAoB;4BACnB,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS,kKAAgC;4BACzC,eAAe,CAAA,IAAK,EAAE,AAAC,+BAAgC,OAAF;4BACrD,GAAG;;;;;;sCAEL,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAGL,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,qJAAS;;;;;sCACV,6LAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,mMAAoB;4BACnB,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS,mKAAiC;4BAC1C,eAAe,CAAA,IACb,EAAE,AAAC,4CAA6C,OAAF;4BAEhD,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAGZ,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,+IAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU;;gCAET,2BAAa,6LAAC,uOAAW;oCAAC,WAAU;;;;;;gCACpC,QAAQ;;;;;;;;;;;;;;;;;;YAId,wDACC,6LAAC,0WAAkD;gBACjD,wCACE;;;;;;;;;;;;AAMZ;GA1ea;;QAKD,4NAAe;QACT,4NAAe;QACf,4NAAe;QAQlB,4KAAO;QA0EU,6LAAoB;QAoB9C,iMAAqB;;;KA7Gd", "debugId": null}}, {"offset": {"line": 2328, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-nonreinforced-section-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { NonReinforcedSectionShearSchema } from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  nonReinforcedSectionShearResults?: NonReinforcedSectionShearSchema\r\n}\r\n\r\nexport function ShearNonReinforcedSectionResultCard({\r\n  nonReinforcedSectionShearResults,\r\n}: Props) {\r\n  const {\r\n    inPlaneAppliedShear,\r\n    verticalStress,\r\n    correctionFactorBasedOnWallSlenderness,\r\n    shearResistanceNotReinforcedMasonry,\r\n    check,\r\n  } = nonReinforcedSectionShearResults || {}\r\n\r\n  const t = useTranslations(\r\n    'components.calculations.maschi-murari.shearReinforcementCalculationResult.nonReinforcedSectionResult',\r\n  )\r\n\r\n  const locale = useLocale()\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <div>\r\n          <span className=\"font-medium\">{t('inPlaneAppliedShear.label')}:</span>{' '}\r\n          <span>\r\n            {inPlaneAppliedShear?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">{t('verticalStress.label')}:</span>{' '}\r\n          <span>\r\n            {verticalStress?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('correctionFactorBasedOnWallSlenderness.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {correctionFactorBasedOnWallSlenderness?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('shearResistanceNotReinforcedMasonry.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {shearResistanceNotReinforcedMasonry?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <span className=\"font-medium\">{t('check.label')}:</span>{' '}\r\n        <Badge\r\n          className={cn(\r\n            'text-base px-3 py-1',\r\n            check ? 'bg-green-600' : 'bg-red-600',\r\n          )}\r\n        >\r\n          {check ? t('check.satisfied') : t('check.notSatisfied')}\r\n        </Badge>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AAEA;AAAA;;;;;;;AAMO,SAAS,oCAAoC,KAE5C;QAF4C,EAClD,gCAAgC,EAC1B,GAF4C;;IAGlD,MAAM,EACJ,mBAAmB,EACnB,cAAc,EACd,sCAAsC,EACtC,mCAAmC,EACnC,KAAK,EACN,GAAG,oCAAoC,CAAC;IAEzC,MAAM,IAAI,IAAA,4NAAe,EACvB;IAGF,MAAM,SAAS,IAAA,kLAAS;IAExB,qBACE,6LAAC,2IAAI;;0BACH,6LAAC,iJAAU;0BACT,cAAA,6LAAC,gJAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,6LAAC,kJAAW;gBAAC,WAAU;;kCACrB,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAA6B;;;;;;;4BAAS;0CACvE,6LAAC;;oCACE,gCAAA,0CAAA,oBAAqB,cAAc,CAAC,QAAQ;wCAC3C,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAAwB;;;;;;;4BAAS;0CAClE,6LAAC;;oCACE,2BAAA,qCAAA,eAAgB,cAAc,CAAC,QAAQ;wCACtC,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAgD;;;;;;;4BAC7C;0CACR,6LAAC;;oCACE,mDAAA,6DAAA,uCAAwC,cAAc,CAAC,QAAQ;wCAC9D,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA6C;;;;;;;4BAC1C;0CACR,6LAAC;;oCACE,gDAAA,0DAAA,oCAAqC,cAAc,CAAC,QAAQ;wCAC3D,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,6LAAC;wBAAK,WAAU;;4BAAe,EAAE;4BAAe;;;;;;;oBAAS;kCACzD,6LAAC,6IAAK;wBACJ,WAAW,IAAA,4HAAE,EACX,uBACA,QAAQ,iBAAiB;kCAG1B,QAAQ,EAAE,qBAAqB,EAAE;;;;;;;;;;;;;;;;;;AAK5C;GAvEgB;;QAWJ,4NAAe;QAIV,kLAAS;;;KAfV", "debugId": null}}, {"offset": {"line": 2545, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-shear-reinforced-section-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { ReinforcedSectionShearSchema } from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  reinforcedSectionShearResults?: ReinforcedSectionShearSchema\r\n}\r\n\r\nexport function ShearReinforcedSectionResultCard({\r\n  reinforcedSectionShearResults,\r\n}: Props) {\r\n  const {\r\n    shearResistanceReinforcementContribution,\r\n    totalShearResistance,\r\n    firstCheck,\r\n    shearResistanceFromMasonryDiagonalCompression,\r\n    secondCheck,\r\n  } = reinforcedSectionShearResults || {}\r\n\r\n  const t = useTranslations(\r\n    'components.calculations.maschi-murari.shearReinforcementCalculationResult.reinforcedSectionResult',\r\n  )\r\n\r\n  const locale = useLocale()\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('shearResistanceReinforcementContribution.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {shearResistanceReinforcementContribution?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('totalShearResistance.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {totalShearResistance?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <span className=\"font-medium\">{t('firstCheck.label')}:</span>{' '}\r\n        <Badge\r\n          className={cn(\r\n            'text-base px-3 py-1',\r\n            firstCheck ? 'bg-green-600' : 'bg-red-600',\r\n          )}\r\n        >\r\n          {firstCheck\r\n            ? t('firstCheck.satisfied')\r\n            : t('firstCheck.notSatisfied')}\r\n        </Badge>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('shearResistanceFromMasonryDiagonalCompression.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {shearResistanceFromMasonryDiagonalCompression?.toLocaleString(\r\n              locale,\r\n              {\r\n                maximumFractionDigits: 3,\r\n              },\r\n            )}{' '}\r\n          </span>\r\n        </div>\r\n        <span className=\"font-medium\">{t('secondCheck.label')}:</span>{' '}\r\n        <Badge\r\n          className={cn(\r\n            'text-base px-3 py-1',\r\n            secondCheck ? 'bg-green-600' : 'bg-red-600',\r\n          )}\r\n        >\r\n          {secondCheck\r\n            ? t('secondCheck.satisfied')\r\n            : t('secondCheck.notSatisfied')}\r\n        </Badge>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AAEA;AAAA;;;;;;;AAMO,SAAS,iCAAiC,KAEzC;QAFyC,EAC/C,6BAA6B,EACvB,GAFyC;;IAG/C,MAAM,EACJ,wCAAwC,EACxC,oBAAoB,EACpB,UAAU,EACV,6CAA6C,EAC7C,WAAW,EACZ,GAAG,iCAAiC,CAAC;IAEtC,MAAM,IAAI,IAAA,4NAAe,EACvB;IAGF,MAAM,SAAS,IAAA,kLAAS;IAExB,qBACE,6LAAC,2IAAI;;0BACH,6LAAC,iJAAU;0BACT,cAAA,6LAAC,gJAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,6LAAC,kJAAW;gBAAC,WAAU;;kCACrB,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAkD;;;;;;;4BAC/C;0CACR,6LAAC;;oCACE,qDAAA,+DAAA,yCAA0C,cAAc,CAAC,QAAQ;wCAChE,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA8B;;;;;;;4BAC3B;0CACR,6LAAC;;oCACE,iCAAA,2CAAA,qBAAsB,cAAc,CAAC,QAAQ;wCAC5C,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,6LAAC;wBAAK,WAAU;;4BAAe,EAAE;4BAAoB;;;;;;;oBAAS;kCAC9D,6LAAC,6IAAK;wBACJ,WAAW,IAAA,4HAAE,EACX,uBACA,aAAa,iBAAiB;kCAG/B,aACG,EAAE,0BACF,EAAE;;;;;;kCAER,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAuD;;;;;;;4BACpD;0CACR,6LAAC;;oCACE,0DAAA,oEAAA,8CAA+C,cAAc,CAC5D,QACA;wCACE,uBAAuB;oCACzB;oCACC;;;;;;;;;;;;;kCAGP,6LAAC;wBAAK,WAAU;;4BAAe,EAAE;4BAAqB;;;;;;;oBAAS;kCAC/D,6LAAC,6IAAK;wBACJ,WAAW,IAAA,4HAAE,EACX,uBACA,cAAc,iBAAiB;kCAGhC,cACG,EAAE,2BACF,EAAE;;;;;;;;;;;;;;;;;;AAKhB;GAjFgB;;QAWJ,4NAAe;QAIV,kLAAS;;;KAfV", "debugId": null}}, {"offset": {"line": 2750, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation-result.tsx"], "sourcesContent": ["import {\r\n  <PERSON>,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON>Content,\r\n  <PERSON><PERSON>List,\r\n  TabsTrigger,\r\n} from '@atlas/components/ui/tabs'\r\nimport type { MaschiMurariShearReinforcementCalculationResultSchema } from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\nimport { ShearNonReinforcedSectionResultCard } from './maschi-murari-shear-nonreinforced-section-result-card'\r\nimport { ShearReinforcedSectionResultCard } from './maschi-murari-shear-reinforced-section-result-card'\r\n\r\ntype Props = {\r\n  shearReinforcementCalculationResult: MaschiMurariShearReinforcementCalculationResultSchema\r\n}\r\n\r\nexport function MaschiMurariReinforcementShearCalculationResult({\r\n  shearReinforcementCalculationResult,\r\n}: Props) {\r\n  const t = useTranslations(\r\n    'components.calculations.maschi-murari.shearReinforcementCalculationResult',\r\n  )\r\n  const _locale = useLocale()\r\n\r\n  return (\r\n    <>\r\n      <h3 className=\"text-lg font-medium py-4\">{t('title')}</h3>\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle>{t('title')}</CardTitle>\r\n        </CardHeader>\r\n        <CardContent className=\"space-y-4\">\r\n          <Tabs defaultValue=\"nonReinforcedSection\">\r\n            <TabsList>\r\n              <TabsTrigger value=\"nonReinforcedSection\">\r\n                {t('shearReinforcedSectionResult.label')}\r\n              </TabsTrigger>\r\n              <TabsTrigger value=\"reinforcedSection\">\r\n                {t('shearReinforcedSectionResult.label')}\r\n              </TabsTrigger>\r\n            </TabsList>\r\n            <TabsContent value=\"nonReinforcedSection\">\r\n              <ShearNonReinforcedSectionResultCard\r\n                nonReinforcedSectionShearResults={\r\n                  shearReinforcementCalculationResult?.inPlaneShearCheck\r\n                    ?.nonReinforcedSection\r\n                }\r\n              />\r\n            </TabsContent>\r\n            <TabsContent value=\"reinforcedSection\">\r\n              <ShearReinforcedSectionResultCard\r\n                reinforcedSectionShearResults={\r\n                  shearReinforcementCalculationResult?.inPlaneShearCheck\r\n                    ?.reinforcedSection\r\n                }\r\n              />\r\n            </TabsContent>\r\n          </Tabs>\r\n        </CardContent>\r\n      </Card>\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAMA;AAOA;AAAA;AACA;AACA;;;;;;;;AAMO,SAAS,gDAAgD,KAExD;QAFwD,EAC9D,mCAAmC,EAC7B,GAFwD;QA4B9C,wDAQA;;IAjChB,MAAM,IAAI,IAAA,4NAAe,EACvB;IAEF,MAAM,UAAU,IAAA,kLAAS;IAEzB,qBACE;;0BACE,6LAAC;gBAAG,WAAU;0BAA4B,EAAE;;;;;;0BAC5C,6LAAC,2IAAI;;kCACH,6LAAC,iJAAU;kCACT,cAAA,6LAAC,gJAAS;sCAAE,EAAE;;;;;;;;;;;kCAEhB,6LAAC,kJAAW;wBAAC,WAAU;kCACrB,cAAA,6LAAC,2IAAI;4BAAC,cAAa;;8CACjB,6LAAC,+IAAQ;;sDACP,6LAAC,kJAAW;4CAAC,OAAM;sDAChB,EAAE;;;;;;sDAEL,6LAAC,kJAAW;4CAAC,OAAM;sDAChB,EAAE;;;;;;;;;;;;8CAGP,6LAAC,kJAAW;oCAAC,OAAM;8CACjB,cAAA,6LAAC,yVAAmC;wCAClC,gCAAgC,EAC9B,gDAAA,2DAAA,yDAAA,oCAAqC,iBAAiB,cAAtD,6EAAA,uDACI,oBAAoB;;;;;;;;;;;8CAI9B,6LAAC,kJAAW;oCAAC,OAAM;8CACjB,cAAA,6LAAC,mVAAgC;wCAC/B,6BAA6B,EAC3B,gDAAA,2DAAA,0DAAA,oCAAqC,iBAAiB,cAAtD,8EAAA,wDACI,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASvC;GA9CgB;;QAGJ,4NAAe;QAGT,kLAAS;;;KANX", "debugId": null}}, {"offset": {"line": 2892, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/calculations/shear/maschi-murari-reinforcement-shear-calculation.tsx"], "sourcesContent": ["import { CustomProductSection } from '@atlas/components/common/atlas/product/custom-product-section'\r\nimport { ProductSummaryCard } from '@atlas/components/common/atlas/product/product-summary-card'\r\nimport { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport {\r\n  FACING_MATERIAL,\r\n  MODULE_REINFORCEMENT_ARRANGEMENT,\r\n  MODULE_REINFORCEMENT_FAILURE_MODE,\r\n  REINFORCEMENT_ARRANGEMENT,\r\n  REINFORCEMENT_FAILURE_MODE,\r\n} from '@atlas/constants/module'\r\nimport type { ModuleWithParamsMaschiMurari } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useModuleCalculation } from '@atlas/lib/mutation/modules/use-module-calculation'\r\nimport { useProductsByCategory } from '@atlas/lib/query/products/use-products-by-category'\r\nimport {\r\n  type MaschiMurariShearReinforcementExecutionSchemaInput,\r\n  maschiMurariShearReinforcementExecutionSchema,\r\n} from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useMemo } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\nimport { MaschiMurariReinforcementShearCalculationResult } from './maschi-murari-reinforcement-shear-calculation-result'\r\n\r\ntype Props = {\r\n  module: ModuleWithParamsMaschiMurari\r\n  session: Session\r\n  projectId: Project['id']\r\n  onNext: () => void\r\n}\r\n\r\n// N.B: this function is temporary, until we remove the quotes in future seed\r\nfunction getCleanedUpValue(\r\n  recordToClean: Record<string, number> | undefined,\r\n  nature: string,\r\n) {\r\n  // nature should be 'BRICK', 'TUFF', or 'STONE'\r\n  if (!recordToClean) {\r\n    return undefined\r\n  }\r\n  const key = `\"${nature}\"` // matches the API key format\r\n  return recordToClean[key]\r\n}\r\n\r\nexport const MaschiMurariShearCalculation = ({\r\n  session,\r\n  module,\r\n  projectId,\r\n  onNext,\r\n}: Props) => {\r\n  const t = useTranslations('forms.calculations.maschi-murari.shear')\r\n  const tAction = useTranslations('actions.calculations.maschi-murari')\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  const {\r\n    params,\r\n    shearReinforcementVerifyExecutionInput,\r\n    shearReinforcementCalculationResult,\r\n  } = module\r\n\r\n  const form = useForm<MaschiMurariShearReinforcementExecutionSchemaInput>({\r\n    resolver: zodResolver(maschiMurariShearReinforcementExecutionSchema),\r\n    defaultValues: {\r\n      calculationType: 'SHEAR_VERIFY',\r\n      input: {\r\n        product: {\r\n          id: shearReinforcementVerifyExecutionInput?.product.id,\r\n          name: shearReinforcementVerifyExecutionInput?.product.name,\r\n          sourceType:\r\n            shearReinforcementVerifyExecutionInput?.product.id === 'custom'\r\n              ? 'CUSTOM'\r\n              : 'DATABASE',\r\n        },\r\n        reinforcementFailureMode:\r\n          shearReinforcementVerifyExecutionInput?.reinforcementFailureMode ??\r\n          REINFORCEMENT_FAILURE_MODE.DISTACCO_DI_ESTREMITA,\r\n        designReinforcementStress:\r\n          shearReinforcementVerifyExecutionInput?.designReinforcementStress ??\r\n          0,\r\n        designReinforcementStrain:\r\n          shearReinforcementVerifyExecutionInput?.designReinforcementStrain ??\r\n          0,\r\n        reinforcedArrangement:\r\n          shearReinforcementVerifyExecutionInput?.reinforcedArrangement ??\r\n          REINFORCEMENT_ARRANGEMENT.CONTINUE,\r\n        singleStripWidth:\r\n          shearReinforcementVerifyExecutionInput?.singleStripWidth ?? 0,\r\n        stripSpacing: shearReinforcementVerifyExecutionInput?.stripSpacing ?? 0,\r\n        totalReinforcementWidthPerpendicularShearDirection:\r\n          shearReinforcementVerifyExecutionInput?.totalReinforcementWidthPerpendicularShearDirection ??\r\n          0,\r\n        compressedEdgeReinforcementFiberDistance:\r\n          shearReinforcementVerifyExecutionInput?.compressedEdgeReinforcementFiberDistance ??\r\n          0,\r\n        layersNumber: shearReinforcementVerifyExecutionInput?.layersNumber ?? 1,\r\n        reinforcedSidesNumber:\r\n          shearReinforcementVerifyExecutionInput?.reinforcedSidesNumber ?? 0,\r\n      },\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useModuleCalculation(session.accessToken, {\r\n    onSuccess: () => {\r\n      toast.success(tAction('calculate.success'))\r\n    },\r\n    onError: error => {\r\n      console.log('ERROR  ', error)\r\n      toast.error(tAction('calculate.failure', { error: error.message }))\r\n    },\r\n  })\r\n\r\n  const handleFormSubmit = (\r\n    body: MaschiMurariShearReinforcementExecutionSchemaInput,\r\n  ) => {\r\n    mutate({ projectId, moduleId: module.id, body })\r\n  }\r\n\r\n  const {\r\n    data: products,\r\n    isError: errorGettingProducts,\r\n    isLoading: isLoadingProducts,\r\n  } = useProductsByCategory(session, 'MASCHI_MURARI', 0, 100)\r\n\r\n  const productsOptions = [\r\n    ...(products?.content.map(m => ({\r\n      value: m.id,\r\n      label: m.name ?? t('product.unnamed'),\r\n    })) ?? []),\r\n  ]\r\n\r\n  const [productId] = form.watch(['input.product.id'])\r\n\r\n  const selectedProduct = useMemo(\r\n    () => products?.content.find(p => p.id === productId),\r\n    [productId, products],\r\n  )\r\n\r\n  const conventionalStrainLimit = selectedProduct?.conventionalStrainLimit\r\n  const conversionFactor = params?.materialProperties?.conversionFactor ?? 0\r\n  const reinforcementFailureModeValue = form.watch(\r\n    'input.reinforcementFailureMode',\r\n  )\r\n\r\n  const structuralElementsNatureValue =\r\n    params?.materialProperties?.structuralElementsNature ??\r\n    FACING_MATERIAL.BRICK\r\n\r\n  const panelGeometryHeight = params?.panelGeometry?.height ?? 0\r\n  const panelGeometryWidth = params?.panelGeometry?.width ?? 0\r\n  const singleStripWidthValue = form.watch('input.singleStripWidth') ?? 0\r\n  const stripSpacingValue = form.watch('input.stripSpacing') ?? 0\r\n\r\n  const reinforcedArrangementValue = form.watch('input.reinforcedArrangement')\r\n\r\n  useEffect(() => {\r\n    // D43 = designReinforcementStrain =\r\n    // =IF(D42=\"Distacco intermedio\",(1.5*D41*D26/1.5),(D41*D26/1.5))\r\n    // D42 = input.reinforcementFailureMode\r\n    // D41 = product.conventionalStrainLimit\r\n    // D26 = materialProperties.conversionFactor\r\n    const conventionalStrainLimitValue =\r\n      getCleanedUpValue(\r\n        conventionalStrainLimit,\r\n        structuralElementsNatureValue,\r\n      ) ?? 0\r\n\r\n    const designReinforcementStrain =\r\n      reinforcementFailureModeValue ===\r\n      REINFORCEMENT_FAILURE_MODE.DISTACCO_INTERMEDIO\r\n        ? (1.5 * conventionalStrainLimitValue * conversionFactor) / 1.5\r\n        : (conventionalStrainLimitValue * conversionFactor) / 1.5\r\n\r\n    form.setValue('input.designReinforcementStrain', designReinforcementStrain)\r\n\r\n    // D62 = designReinforcementStress = =D43*D38\r\n    // D43 = designReinforcementStrain\r\n    // D38 = product.elasticModulus\r\n    const elasticModulus = selectedProduct?.elasticModulus ?? 0\r\n    const designReinforcementStress = designReinforcementStrain * elasticModulus\r\n    form.setValue('input.designReinforcementStress', designReinforcementStress)\r\n\r\n    // D48 totalReinforcementWidthPerpendicularShearDirection =\r\n    //  =IF(D45=\"Continuo\",MIN(D8,D9),MIN(D46*(D8/D47),D8,D9))\r\n    // D45 = input.reinforcedArrangement\r\n    // D8 = panelGeometry.height\r\n    // D9 = panelGeometry.width\r\n    // D46 = input.singleStripWidth\r\n    // D47 = input.stripSpacing\r\n\r\n    const totalReinforcementWidthPerpendicularShearDirection =\r\n      reinforcedArrangementValue === REINFORCEMENT_ARRANGEMENT.CONTINUE\r\n        ? Math.min(panelGeometryHeight, panelGeometryWidth)\r\n        : Math.min(\r\n            singleStripWidthValue * (panelGeometryHeight / stripSpacingValue),\r\n            panelGeometryHeight,\r\n            panelGeometryWidth,\r\n          )\r\n    form.setValue(\r\n      'input.totalReinforcementWidthPerpendicularShearDirection',\r\n      totalReinforcementWidthPerpendicularShearDirection,\r\n    )\r\n\r\n    form.setValue('input.layersNumber', 1)\r\n    if (!productId) {\r\n      return\r\n    }\r\n    if (productId === 'custom') {\r\n      form.setValue('input.product.sourceType', 'CUSTOM')\r\n    }\r\n\r\n    if (selectedProduct) {\r\n      form.setValue('input.product', {\r\n        ...selectedProduct,\r\n        sourceType: 'DATABASE',\r\n      })\r\n    }\r\n  }, [\r\n    form,\r\n    productId,\r\n    selectedProduct,\r\n    conversionFactor,\r\n    reinforcementFailureModeValue,\r\n    conventionalStrainLimit,\r\n    structuralElementsNatureValue,\r\n    panelGeometryHeight,\r\n    panelGeometryWidth,\r\n    singleStripWidthValue,\r\n    stripSpacingValue,\r\n    reinforcedArrangementValue,\r\n  ])\r\n\r\n  return (\r\n    <div className=\"flex flex-col justify-center gap-4\">\r\n      <Form {...form}>\r\n        <form\r\n          className=\"space-y-4 rounded-md border p-4\"\r\n          onSubmit={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          <h1 className=\"text-3xl font-bold\">{t('heading')}</h1>\r\n          <Image\r\n            src=\"/assets/masonry_frcm/maschi-murari/FRCM_PANNELLI MURARI_Diffuso.jpg\"\r\n            alt=\"shear verify\"\r\n            height={250}\r\n            width={500}\r\n            className=\"mx-auto rounded-md object-contain\"\r\n            priority\r\n          />\r\n          <h1 className=\"text-xl font-bold\">{t('sub-heading')}</h1>\r\n          <SelectFormInput\r\n            control={form.control}\r\n            name=\"input.product.id\"\r\n            options={productsOptions}\r\n            t={t}\r\n            loading={isLoadingProducts}\r\n            requestError={errorGettingProducts}\r\n            errorMessage={t('products.error')}\r\n          />\r\n          {productId === 'custom' && <CustomProductSection />}\r\n          {selectedProduct && <ProductSummaryCard product={selectedProduct} />}\r\n          <Separator />\r\n          <SelectFormFixedInput\r\n            control={form.control}\r\n            name=\"input.reinforcementFailureMode\"\r\n            options={MODULE_REINFORCEMENT_FAILURE_MODE}\r\n            optionLabelFn={p => t(`input.reinforcementFailureMode.${p}`)}\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.designReinforcementStrain\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.designReinforcementStress\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <SelectFormFixedInput\r\n            control={form.control}\r\n            name=\"input.reinforcedArrangement\"\r\n            options={MODULE_REINFORCEMENT_ARRANGEMENT}\r\n            optionLabelFn={p => t(`input.reinforcedArrangement.${p}`)}\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.singleStripWidth\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.stripSpacing\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.totalReinforcementWidthPerpendicularShearDirection\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.layersNumber\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.reinforcedSidesNumber\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.compressedEdgeReinforcementFiberDistance\"\r\n            t={t}\r\n          />\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full sm:w-auto\"\r\n            disabled={isPending}\r\n          >\r\n            {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n            {tCommon('calculate')}\r\n          </Button>\r\n        </form>\r\n      </Form>\r\n      {shearReinforcementCalculationResult && (\r\n        <>\r\n          <MaschiMurariReinforcementShearCalculationResult\r\n            shearReinforcementCalculationResult={\r\n              shearReinforcementCalculationResult\r\n            }\r\n          />\r\n          <Button type=\"submit\" className=\"w-full sm:w-auto\" onClick={onNext}>\r\n            {tCommon('next')}\r\n          </Button>\r\n        </>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AASA;AACA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;AASA,6EAA6E;AAC7E,SAAS,kBACP,aAAiD,EACjD,MAAc;IAEd,+CAA+C;IAC/C,IAAI,CAAC,eAAe;QAClB,OAAO;IACT;IACA,MAAM,MAAM,AAAC,IAAU,OAAP,QAAO,KAAG,6BAA6B;;IACvD,OAAO,aAAa,CAAC,IAAI;AAC3B;AAEO,MAAM,+BAA+B;QAAC,EAC3C,OAAO,EACP,MAAM,EACN,SAAS,EACT,MAAM,EACA;QAyFmB,4BAMvB,6BAG0B,uBACD;;IAlG3B,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAEhC,MAAM,EACJ,MAAM,EACN,sCAAsC,EACtC,mCAAmC,EACpC,GAAG;QAgBI,kEAGA,mEAGA,mEAGA,+DAGA,0DACY,sDAEZ,4FAGA,kFAEY,sDAEZ;IApCR,MAAM,OAAO,IAAA,4KAAO,EAAqD;QACvE,UAAU,IAAA,gLAAW,EAAC,kNAA6C;QACnE,eAAe;YACb,iBAAiB;YACjB,OAAO;gBACL,SAAS;oBACP,EAAE,EAAE,mDAAA,6DAAA,uCAAwC,OAAO,CAAC,EAAE;oBACtD,IAAI,EAAE,mDAAA,6DAAA,uCAAwC,OAAO,CAAC,IAAI;oBAC1D,YACE,CAAA,mDAAA,6DAAA,uCAAwC,OAAO,CAAC,EAAE,MAAK,WACnD,WACA;gBACR;gBACA,0BACE,CAAA,mEAAA,mDAAA,6DAAA,uCAAwC,wBAAwB,cAAhE,8EAAA,mEACA,4JAA0B,CAAC,qBAAqB;gBAClD,2BACE,CAAA,oEAAA,mDAAA,6DAAA,uCAAwC,yBAAyB,cAAjE,+EAAA,oEACA;gBACF,2BACE,CAAA,oEAAA,mDAAA,6DAAA,uCAAwC,yBAAyB,cAAjE,+EAAA,oEACA;gBACF,uBACE,CAAA,gEAAA,mDAAA,6DAAA,uCAAwC,qBAAqB,cAA7D,2EAAA,gEACA,2JAAyB,CAAC,QAAQ;gBACpC,kBACE,CAAA,2DAAA,mDAAA,6DAAA,uCAAwC,gBAAgB,cAAxD,sEAAA,2DAA4D;gBAC9D,cAAc,CAAA,uDAAA,mDAAA,6DAAA,uCAAwC,YAAY,cAApD,kEAAA,uDAAwD;gBACtE,oDACE,CAAA,6FAAA,mDAAA,6DAAA,uCAAwC,kDAAkD,cAA1F,wGAAA,6FACA;gBACF,0CACE,CAAA,mFAAA,mDAAA,6DAAA,uCAAwC,wCAAwC,cAAhF,8FAAA,mFACA;gBACF,cAAc,CAAA,uDAAA,mDAAA,6DAAA,uCAAwC,YAAY,cAApD,kEAAA,uDAAwD;gBACtE,uBACE,CAAA,gEAAA,mDAAA,6DAAA,uCAAwC,qBAAqB,cAA7D,2EAAA,gEAAiE;YACrE;QACF;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,6LAAoB,EAAC,QAAQ,WAAW,EAAE;QACtE,SAAS;iEAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;YACxB;;QACA,OAAO;iEAAE,CAAA;gBACP,QAAQ,GAAG,CAAC,WAAW;gBACvB,oJAAK,CAAC,KAAK,CAAC,QAAQ,qBAAqB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAClE;;IACF;IAEA,MAAM,mBAAmB,CACvB;QAEA,OAAO;YAAE;YAAW,UAAU,OAAO,EAAE;YAAE;QAAK;IAChD;IAEA,MAAM,EACJ,MAAM,QAAQ,EACd,SAAS,oBAAoB,EAC7B,WAAW,iBAAiB,EAC7B,GAAG,IAAA,iMAAqB,EAAC,SAAS,iBAAiB,GAAG;QAGjD;IADN,MAAM,kBAAkB;WAClB,CAAA,wBAAA,qBAAA,+BAAA,SAAU,OAAO,CAAC,GAAG,CAAC,CAAA;gBAEjB;mBAFuB;gBAC9B,OAAO,EAAE,EAAE;gBACX,OAAO,CAAA,UAAA,EAAE,IAAI,cAAN,qBAAA,UAAU,EAAE;YACrB;wBAHI,mCAAA,wBAGG,EAAE;KACV;IAED,MAAM,CAAC,UAAU,GAAG,KAAK,KAAK,CAAC;QAAC;KAAmB;IAEnD,MAAM,kBAAkB,IAAA,wKAAO;iEAC7B,IAAM,qBAAA,+BAAA,SAAU,OAAO,CAAC,IAAI;yEAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;gEAC3C;QAAC;QAAW;KAAS;IAGvB,MAAM,0BAA0B,4BAAA,sCAAA,gBAAiB,uBAAuB;QAC/C;IAAzB,MAAM,mBAAmB,CAAA,8CAAA,mBAAA,8BAAA,6BAAA,OAAQ,kBAAkB,cAA1B,iDAAA,2BAA4B,gBAAgB,cAA5C,yDAAA,8CAAgD;IACzE,MAAM,gCAAgC,KAAK,KAAK,CAC9C;QAIA;IADF,MAAM,gCACJ,CAAA,sDAAA,mBAAA,8BAAA,8BAAA,OAAQ,kBAAkB,cAA1B,kDAAA,4BAA4B,wBAAwB,cAApD,iEAAA,sDACA,iJAAe,CAAC,KAAK;QAEK;IAA5B,MAAM,sBAAsB,CAAA,+BAAA,mBAAA,8BAAA,wBAAA,OAAQ,aAAa,cAArB,4CAAA,sBAAuB,MAAM,cAA7B,0CAAA,+BAAiC;QAClC;IAA3B,MAAM,qBAAqB,CAAA,8BAAA,mBAAA,8BAAA,yBAAA,OAAQ,aAAa,cAArB,6CAAA,uBAAuB,KAAK,cAA5B,yCAAA,8BAAgC;QAC7B;IAA9B,MAAM,wBAAwB,CAAA,cAAA,KAAK,KAAK,CAAC,uCAAX,yBAAA,cAAwC;QAC5C;IAA1B,MAAM,oBAAoB,CAAA,eAAA,KAAK,KAAK,CAAC,mCAAX,0BAAA,eAAoC;IAE9D,MAAM,6BAA6B,KAAK,KAAK,CAAC;IAE9C,IAAA,0KAAS;kDAAC;gBAON;YANF,oCAAoC;YACpC,iEAAiE;YACjE,uCAAuC;YACvC,wCAAwC;YACxC,4CAA4C;YAC5C,MAAM,+BACJ,CAAA,qBAAA,kBACE,yBACA,4CAFF,gCAAA,qBAGK;YAEP,MAAM,4BACJ,kCACA,4JAA0B,CAAC,mBAAmB,GAC1C,AAAC,MAAM,+BAA+B,mBAAoB,MAC1D,AAAC,+BAA+B,mBAAoB;YAE1D,KAAK,QAAQ,CAAC,mCAAmC;gBAK1B;YAHvB,6CAA6C;YAC7C,kCAAkC;YAClC,+BAA+B;YAC/B,MAAM,iBAAiB,CAAA,kCAAA,4BAAA,sCAAA,gBAAiB,cAAc,cAA/B,6CAAA,kCAAmC;YAC1D,MAAM,4BAA4B,4BAA4B;YAC9D,KAAK,QAAQ,CAAC,mCAAmC;YAEjD,2DAA2D;YAC3D,0DAA0D;YAC1D,oCAAoC;YACpC,4BAA4B;YAC5B,2BAA2B;YAC3B,+BAA+B;YAC/B,2BAA2B;YAE3B,MAAM,qDACJ,+BAA+B,2JAAyB,CAAC,QAAQ,GAC7D,KAAK,GAAG,CAAC,qBAAqB,sBAC9B,KAAK,GAAG,CACN,wBAAwB,CAAC,sBAAsB,iBAAiB,GAChE,qBACA;YAER,KAAK,QAAQ,CACX,4DACA;YAGF,KAAK,QAAQ,CAAC,sBAAsB;YACpC,IAAI,CAAC,WAAW;gBACd;YACF;YACA,IAAI,cAAc,UAAU;gBAC1B,KAAK,QAAQ,CAAC,4BAA4B;YAC5C;YAEA,IAAI,iBAAiB;gBACnB,KAAK,QAAQ,CAAC,iBAAiB;oBAC7B,GAAG,eAAe;oBAClB,YAAY;gBACd;YACF;QACF;iDAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,2IAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,6LAAC;oBACC,WAAU;oBACV,UAAU,KAAK,YAAY,CAAC;;sCAE5B,6LAAC;4BAAG,WAAU;sCAAsB,EAAE;;;;;;sCACtC,6LAAC,2IAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,QAAQ;4BACR,OAAO;4BACP,WAAU;4BACV,QAAQ;;;;;;sCAEV,6LAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS;4BACT,GAAG;4BACH,SAAS;4BACT,cAAc;4BACd,cAAc,EAAE;;;;;;wBAEjB,cAAc,0BAAY,6LAAC,2MAAoB;;;;;wBAC/C,iCAAmB,6LAAC,uMAAkB;4BAAC,SAAS;;;;;;sCACjD,6LAAC,qJAAS;;;;;sCACV,6LAAC,mMAAoB;4BACnB,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS,mKAAiC;4BAC1C,eAAe,CAAA,IAAK,EAAE,AAAC,kCAAmC,OAAF;4BACxD,GAAG;;;;;;sCAEL,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,mMAAoB;4BACnB,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS,kKAAgC;4BACzC,eAAe,CAAA,IAAK,EAAE,AAAC,+BAAgC,OAAF;4BACrD,GAAG;;;;;;sCAEL,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,6LAAC,+IAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU;;gCAET,2BAAa,6LAAC,uOAAW;oCAAC,WAAU;;;;;;gCACpC,QAAQ;;;;;;;;;;;;;;;;;;YAId,qDACC;;kCACE,6LAAC,iWAA+C;wBAC9C,qCACE;;;;;;kCAGJ,6LAAC,+IAAM;wBAAC,MAAK;wBAAS,WAAU;wBAAmB,SAAS;kCACzD,QAAQ;;;;;;;;;;;;;;AAMrB;GA1Sa;;QAMD,4NAAe;QACT,4NAAe;QACf,4NAAe;QAQlB,4KAAO;QAyCU,6LAAoB;QAoB9C,iMAAqB;;;KA7Ed", "debugId": null}}, {"offset": {"line": 3338, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-actions-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type MaschiMurariActionsSchemaInputs,\r\n  type MaschiMurariParamsSchemaInputs,\r\n  maschiMurariActionsSchema,\r\n} from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<MaschiMurariActionsSchemaInputs>\r\n  params: MaschiMurariParamsSchemaInputs\r\n  setParams: (newParams: any) => void\r\n  onSave: () => void\r\n}\r\n\r\nexport const MaschiMurariActionsForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  params,\r\n  setParams,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.maschi-murari.actions')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const form = useForm<MaschiMurariActionsSchemaInputs>({\r\n    resolver: zodResolver(maschiMurariActionsSchema),\r\n    defaultValues: {\r\n      appliedNormalStress: defaultValues?.appliedNormalStress ?? 0,\r\n      inPlaneBendingMoment: defaultValues?.inPlaneBendingMoment ?? 0,\r\n      outOfPlaneBendingMoment: defaultValues?.outOfPlaneBendingMoment ?? 0,\r\n      inPlaneAppliedShear: defaultValues?.inPlaneAppliedShear ?? 0,\r\n      outOfPlaneAppliedShear: defaultValues?.outOfPlaneAppliedShear ?? 0,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: MaschiMurariActionsSchemaInputs) => {\r\n      // construct body based on global schema:\r\n      const maschiMurariParams: MaschiMurariParamsSchemaInputs = {\r\n        ...params,\r\n        actions: body,\r\n      }\r\n      mutate({ projectId, moduleId, body: maschiMurariParams })\r\n    },\r\n    [mutate, projectId, moduleId, params],\r\n  )\r\n\r\n  useEffect(() => {\r\n    const subscription = form.watch(values => {\r\n      setParams((prev: any) => ({\r\n        ...prev,\r\n        buildingCharacteristics: values,\r\n      }))\r\n    })\r\n    return () => subscription.unsubscribe()\r\n  }, [form, setParams])\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"appliedNormalStress\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"inPlaneBendingMoment\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"outOfPlaneBendingMoment\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"inPlaneAppliedShear\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"outOfPlaneAppliedShear\"\r\n          t={t}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAGA;AACA;AAKA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;AAYO,MAAM,0BAA0B;QAAC,EACtC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACN,SAAS,EACT,MAAM,EACA;;IACN,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,cAAc,IAAA,2MAAc;QAKT,oCACC,qCACG,wCACJ,oCACG;IAP5B,MAAM,OAAO,IAAA,4KAAO,EAAkC;QACpD,UAAU,IAAA,gLAAW,EAAC,8LAAyB;QAC/C,eAAe;YACb,qBAAqB,CAAA,qCAAA,0BAAA,oCAAA,cAAe,mBAAmB,cAAlC,gDAAA,qCAAsC;YAC3D,sBAAsB,CAAA,sCAAA,0BAAA,oCAAA,cAAe,oBAAoB,cAAnC,iDAAA,sCAAuC;YAC7D,yBAAyB,CAAA,yCAAA,0BAAA,oCAAA,cAAe,uBAAuB,cAAtC,oDAAA,yCAA0C;YACnE,qBAAqB,CAAA,qCAAA,0BAAA,oCAAA,cAAe,mBAAmB,cAAlC,gDAAA,qCAAsC;YAC3D,wBAAwB,CAAA,wCAAA,0BAAA,oCAAA,cAAe,sBAAsB,cAArC,mDAAA,wCAAyC;QACnE;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,mNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,SAAS;mEAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;gBACtB,MAAM,YAAY,iBAAiB,CAAC;oBAClC,UAAU;wBAAC;wBAAW;4BAAE;4BAAW;wBAAS;qBAAE;gBAChD;gBACA;YACF;;QACA,OAAO;mEAAE,CAAA;gBACP,oJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAC7D;;IACF;IAGF,MAAM,mBAAmB,IAAA,4KAAW;iEAClC,CAAC;YACC,yCAAyC;YACzC,MAAM,qBAAqD;gBACzD,GAAG,MAAM;gBACT,SAAS;YACX;YACA,OAAO;gBAAE;gBAAW;gBAAU,MAAM;YAAmB;QACzD;gEACA;QAAC;QAAQ;QAAW;QAAU;KAAO;IAGvC,IAAA,0KAAS;6CAAC;YACR,MAAM,eAAe,KAAK,KAAK;kEAAC,CAAA;oBAC9B;0EAAU,CAAC,OAAc,CAAC;gCACxB,GAAG,IAAI;gCACP,yBAAyB;4BAC3B,CAAC;;gBACH;;YACA;qDAAO,IAAM,aAAa,WAAW;;QACvC;4CAAG;QAAC;QAAM;KAAU;IAEpB,qBACE,6LAAC,2IAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,6LAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,6LAAC,+IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,6LAAC,uOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB;GA1Ga;;QASD,4NAAe;QACT,4NAAe;QACf,4NAAe;QACX,2MAAc;QAErB,4KAAO;QAWU,mNAA2B;;;KAzB9C", "debugId": null}}, {"offset": {"line": 3553, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-material-properties-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport {\r\n  CRM_MASONRY_TYPE,\r\n  characteristicCompressiveStrengthValues,\r\n  characteristicNormalElasticityModulusValues,\r\n  characteristicShearElasticityModulusValues,\r\n  characteristicShearStrengthValues,\r\n  executionClass,\r\n  FACING_MATERIAL,\r\n  loadResistingCategory,\r\n  MODULE_CRM_MASONRY_TYPE,\r\n  MODULE_EXECUTION_CLASS,\r\n  MODULE_FACING_MATERIAL,\r\n  MODULE_GEOMETRY_EXPOSURE,\r\n  MODULE_LOAD_RESISTING_CATEGORY,\r\n  MODULE_MATERIAL_KNOWLEDGE_LEVEL,\r\n  masonryStrengthSafetyFactorMapping,\r\n  moduleGeometryExposure,\r\n  moduleMaterialKnowledgeLevel,\r\n  moduleMaterialKnowledgeLevelValues,\r\n} from '@atlas/constants/module'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type MaschiMurariMaterialPropertiesSchemaInputs,\r\n  type MaschiMurariParamsSchemaInputs,\r\n  maschiMurariMaterialPropertiesSchema,\r\n} from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<MaschiMurariMaterialPropertiesSchemaInputs>\r\n  params: MaschiMurariParamsSchemaInputs\r\n  setParams: (newParams: any) => void\r\n  onSave: () => void\r\n}\r\n\r\nexport const MaschiMurariMaterialPropertiesForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  params,\r\n  setParams,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.project-params.maschi-murari.material-properties',\r\n  )\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const form = useForm<MaschiMurariMaterialPropertiesSchemaInputs>({\r\n    resolver: zodResolver(maschiMurariMaterialPropertiesSchema),\r\n    defaultValues: {\r\n      structuralElementsNature:\r\n        defaultValues?.structuralElementsNature ?? FACING_MATERIAL.BRICK,\r\n      masonryType:\r\n        defaultValues?.masonryType ??\r\n        CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA,\r\n      knowledgeLevel:\r\n        defaultValues?.knowledgeLevel ?? moduleMaterialKnowledgeLevel.LC1,\r\n      confidenceFactor: defaultValues?.confidenceFactor ?? 0,\r\n      executionClass: defaultValues?.executionClass ?? executionClass.ONE,\r\n      loadResistantCategory:\r\n        defaultValues?.loadResistantCategory ??\r\n        loadResistingCategory.MASONRY_CAT_I_AND_MORTAR_WITH_GUARANTEED_PERFORMANCE,\r\n      masonrySafetyFactor: defaultValues?.masonrySafetyFactor ?? 0,\r\n      characteristicCompressiveStrength:\r\n        defaultValues?.characteristicCompressiveStrength ?? 0,\r\n      designCompressiveStrength: defaultValues?.designCompressiveStrength ?? 0,\r\n      characteristicShearStrength:\r\n        defaultValues?.characteristicShearStrength ?? 0,\r\n      designShearStrength: defaultValues?.designShearStrength ?? 0,\r\n      elasticModulus: defaultValues?.elasticModulus ?? 0,\r\n      shearModulus: defaultValues?.shearModulus ?? 0,\r\n      ultimateCompressiveStrainLinearBehaviour:\r\n        defaultValues?.ultimateCompressiveStrainLinearBehaviour ?? 0,\r\n      ultimateCompressiveStrain:\r\n        defaultValues?.ultimateCompressiveStrain ?? 0.0035,\r\n      exposure: defaultValues?.exposure ?? moduleGeometryExposure.INTERNAL,\r\n      conversionFactor: defaultValues?.conversionFactor ?? 0,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: MaschiMurariMaterialPropertiesSchemaInputs) => {\r\n      // construct body based on global schema:\r\n      const maschiMurariParams: MaschiMurariParamsSchemaInputs = {\r\n        ...params,\r\n        materialProperties: body,\r\n      }\r\n      mutate({ projectId, moduleId, body: maschiMurariParams })\r\n    },\r\n    [mutate, projectId, moduleId, params],\r\n  )\r\n\r\n  const knowledgeMaterialLevel =\r\n    form.watch('knowledgeLevel') ?? moduleMaterialKnowledgeLevel.LC1\r\n  const executionClassFormValue =\r\n    form.watch('executionClass') ?? executionClass.ONE\r\n  const loadResistingCategoryFormValue =\r\n    form.watch('loadResistantCategory') ??\r\n    loadResistingCategory.MASONRY_CAT_I_AND_MORTAR_WITH_GUARANTEED_PERFORMANCE\r\n  const masonryTypeValue =\r\n    form.watch('masonryType') ??\r\n    CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA\r\n  const exposureValue =\r\n    form.watch('exposure') ?? moduleGeometryExposure.INTERNAL\r\n\r\n  useEffect(() => {\r\n    const calculateGivenStrength = (strengthMinMax: {\r\n      min: number\r\n      max: number\r\n    }) => {\r\n      return knowledgeMaterialLevel === moduleMaterialKnowledgeLevel.LC1\r\n        ? strengthMinMax.min\r\n        : knowledgeMaterialLevel === moduleMaterialKnowledgeLevel.LC3\r\n          ? strengthMinMax.max\r\n          : (strengthMinMax.min + strengthMinMax.max) / 2\r\n    }\r\n\r\n    const confidenceFactor =\r\n      moduleMaterialKnowledgeLevelValues[knowledgeMaterialLevel]\r\n    form.setValue('confidenceFactor', confidenceFactor)\r\n\r\n    const masonryStrengthSafetyFactor =\r\n      masonryStrengthSafetyFactorMapping[loadResistingCategoryFormValue][\r\n        executionClassFormValue\r\n      ]\r\n    form.setValue('masonrySafetyFactor', masonryStrengthSafetyFactor)\r\n\r\n    const characteristicCompressiveStrengthMinMax =\r\n      characteristicCompressiveStrengthValues[masonryTypeValue]\r\n\r\n    const characteristicCompressiveStrength = calculateGivenStrength(\r\n      characteristicCompressiveStrengthMinMax,\r\n    )\r\n    form.setValue(\r\n      'characteristicCompressiveStrength',\r\n      characteristicCompressiveStrength,\r\n    )\r\n\r\n    const designCompressiveStrength =\r\n      characteristicCompressiveStrength /\r\n      masonryStrengthSafetyFactor /\r\n      confidenceFactor\r\n    form.setValue('designCompressiveStrength', designCompressiveStrength)\r\n\r\n    const characteristicsShearStrengthMinMax =\r\n      characteristicShearStrengthValues[masonryTypeValue]\r\n    const characteristicShearStrength = calculateGivenStrength(\r\n      characteristicsShearStrengthMinMax,\r\n    )\r\n    form.setValue('characteristicShearStrength', characteristicShearStrength)\r\n\r\n    const designShearStrength =\r\n      characteristicShearStrength /\r\n      confidenceFactor /\r\n      masonryStrengthSafetyFactor\r\n    form.setValue('designShearStrength', designShearStrength)\r\n\r\n    const normalElasticityModulusMinMax =\r\n      characteristicNormalElasticityModulusValues[masonryTypeValue]\r\n    const normalElasticityModulus =\r\n      (normalElasticityModulusMinMax.min + normalElasticityModulusMinMax.max) /\r\n      2\r\n    form.setValue('elasticModulus', normalElasticityModulus)\r\n\r\n    const shearElasticityModulusMinMax =\r\n      characteristicShearElasticityModulusValues[masonryTypeValue]\r\n    const shearElasticityModulus =\r\n      (shearElasticityModulusMinMax.min + shearElasticityModulusMinMax.max) / 2\r\n    form.setValue('shearModulus', shearElasticityModulus)\r\n\r\n    // =D19/D22\r\n    const ultimateCompressiveStrainLinearBehaviour =\r\n      designCompressiveStrength / normalElasticityModulus\r\n    form.setValue(\r\n      'ultimateCompressiveStrainLinearBehaviour',\r\n      ultimateCompressiveStrainLinearBehaviour,\r\n    )\r\n\r\n    const ultimateCompressiveStrain = 0.0035\r\n    form.setValue('ultimateCompressiveStrain', ultimateCompressiveStrain)\r\n\r\n    const conversionFactor =\r\n      exposureValue === moduleGeometryExposure.INTERNAL\r\n        ? 0.9\r\n        : exposureValue === moduleGeometryExposure.EXTERNAL\r\n          ? 0.8\r\n          : 0.7\r\n    form.setValue('conversionFactor', conversionFactor)\r\n  }, [\r\n    form,\r\n    knowledgeMaterialLevel,\r\n    executionClassFormValue,\r\n    loadResistingCategoryFormValue,\r\n    masonryTypeValue,\r\n    exposureValue,\r\n  ])\r\n\r\n  useEffect(() => {\r\n    const subscription = form.watch(values => {\r\n      setParams((prev: any) => ({\r\n        ...prev,\r\n        buildingCharacteristics: values,\r\n      }))\r\n    })\r\n    return () => subscription.unsubscribe()\r\n  }, [form, setParams])\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"structuralElementsNature\"\r\n          options={MODULE_FACING_MATERIAL}\r\n          optionLabelFn={p => t(`structuralElementsNature.${p}`)}\r\n          t={t}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"masonryType\"\r\n          options={MODULE_CRM_MASONRY_TYPE}\r\n          optionLabelFn={p => t(`masonryType.${p}`)}\r\n          t={t}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"knowledgeLevel\"\r\n          options={MODULE_MATERIAL_KNOWLEDGE_LEVEL}\r\n          optionLabelFn={p => t(`knowledgeLevel.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"confidenceFactor\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"executionClass\"\r\n          options={MODULE_EXECUTION_CLASS}\r\n          optionLabelFn={p => t(`executionClass.${p}`)}\r\n          t={t}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"loadResistantCategory\"\r\n          options={MODULE_LOAD_RESISTING_CATEGORY}\r\n          optionLabelFn={p => t(`loadResistantCategory.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"masonrySafetyFactor\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"characteristicCompressiveStrength\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"designCompressiveStrength\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"characteristicShearStrength\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"designShearStrength\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"elasticModulus\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"shearModulus\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"ultimateCompressiveStrainLinearBehaviour\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"ultimateCompressiveStrain\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"exposure\"\r\n          options={MODULE_GEOMETRY_EXPOSURE}\r\n          optionLabelFn={p => t(`exposure.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"conversionFactor\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAsBA;AACA;AAKA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AAYO,MAAM,qCAAqC;QAAC,EACjD,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACN,SAAS,EACT,MAAM,EACA;;IACN,MAAM,IAAI,IAAA,4NAAe,EACvB;IAEF,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,cAAc,IAAA,2MAAc;QAM5B,yCAEA,4BAGA,+BACgB,iCACF,+BAEd,sCAEmB,oCAEnB,kDACyB,0CAEzB,4CACmB,oCACL,+BACF,6BAEZ,yDAEA,0CACQ,yBACQ;IA7BtB,MAAM,OAAO,IAAA,4KAAO,EAA6C;QAC/D,UAAU,IAAA,gLAAW,EAAC,yMAAoC;QAC1D,eAAe;YACb,0BACE,CAAA,0CAAA,0BAAA,oCAAA,cAAe,wBAAwB,cAAvC,qDAAA,0CAA2C,iJAAe,CAAC,KAAK;YAClE,aACE,CAAA,6BAAA,0BAAA,oCAAA,cAAe,WAAW,cAA1B,wCAAA,6BACA,kJAAgB,CAAC,gCAAgC;YACnD,gBACE,CAAA,gCAAA,0BAAA,oCAAA,cAAe,cAAc,cAA7B,2CAAA,gCAAiC,8JAA4B,CAAC,GAAG;YACnE,kBAAkB,CAAA,kCAAA,0BAAA,oCAAA,cAAe,gBAAgB,cAA/B,6CAAA,kCAAmC;YACrD,gBAAgB,CAAA,gCAAA,0BAAA,oCAAA,cAAe,cAAc,cAA7B,2CAAA,gCAAiC,gJAAc,CAAC,GAAG;YACnE,uBACE,CAAA,uCAAA,0BAAA,oCAAA,cAAe,qBAAqB,cAApC,kDAAA,uCACA,uJAAqB,CAAC,oDAAoD;YAC5E,qBAAqB,CAAA,qCAAA,0BAAA,oCAAA,cAAe,mBAAmB,cAAlC,gDAAA,qCAAsC;YAC3D,mCACE,CAAA,mDAAA,0BAAA,oCAAA,cAAe,iCAAiC,cAAhD,8DAAA,mDAAoD;YACtD,2BAA2B,CAAA,2CAAA,0BAAA,oCAAA,cAAe,yBAAyB,cAAxC,sDAAA,2CAA4C;YACvE,6BACE,CAAA,6CAAA,0BAAA,oCAAA,cAAe,2BAA2B,cAA1C,wDAAA,6CAA8C;YAChD,qBAAqB,CAAA,qCAAA,0BAAA,oCAAA,cAAe,mBAAmB,cAAlC,gDAAA,qCAAsC;YAC3D,gBAAgB,CAAA,gCAAA,0BAAA,oCAAA,cAAe,cAAc,cAA7B,2CAAA,gCAAiC;YACjD,cAAc,CAAA,8BAAA,0BAAA,oCAAA,cAAe,YAAY,cAA3B,yCAAA,8BAA+B;YAC7C,0CACE,CAAA,0DAAA,0BAAA,oCAAA,cAAe,wCAAwC,cAAvD,qEAAA,0DAA2D;YAC7D,2BACE,CAAA,2CAAA,0BAAA,oCAAA,cAAe,yBAAyB,cAAxC,sDAAA,2CAA4C;YAC9C,UAAU,CAAA,0BAAA,0BAAA,oCAAA,cAAe,QAAQ,cAAvB,qCAAA,0BAA2B,wJAAsB,CAAC,QAAQ;YACpE,kBAAkB,CAAA,kCAAA,0BAAA,oCAAA,cAAe,gBAAgB,cAA/B,6CAAA,kCAAmC;QACvD;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,mNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,SAAS;8EAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;gBACtB,MAAM,YAAY,iBAAiB,CAAC;oBAClC,UAAU;wBAAC;wBAAW;4BAAE;4BAAW;wBAAS;qBAAE;gBAChD;gBACA;YACF;;QACA,OAAO;8EAAE,CAAA;gBACP,oJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAC7D;;IACF;IAGF,MAAM,mBAAmB,IAAA,4KAAW;4EAClC,CAAC;YACC,yCAAyC;YACzC,MAAM,qBAAqD;gBACzD,GAAG,MAAM;gBACT,oBAAoB;YACtB;YACA,OAAO;gBAAE;gBAAW;gBAAU,MAAM;YAAmB;QACzD;2EACA;QAAC;QAAQ;QAAW;QAAU;KAAO;QAIrC;IADF,MAAM,yBACJ,CAAA,cAAA,KAAK,KAAK,CAAC,+BAAX,yBAAA,cAAgC,8JAA4B,CAAC,GAAG;QAEhE;IADF,MAAM,0BACJ,CAAA,eAAA,KAAK,KAAK,CAAC,+BAAX,0BAAA,eAAgC,gJAAc,CAAC,GAAG;QAElD;IADF,MAAM,iCACJ,CAAA,eAAA,KAAK,KAAK,CAAC,sCAAX,0BAAA,eACA,uJAAqB,CAAC,oDAAoD;QAE1E;IADF,MAAM,mBACJ,CAAA,eAAA,KAAK,KAAK,CAAC,4BAAX,0BAAA,eACA,kJAAgB,CAAC,gCAAgC;QAEjD;IADF,MAAM,gBACJ,CAAA,eAAA,KAAK,KAAK,CAAC,yBAAX,0BAAA,eAA0B,wJAAsB,CAAC,QAAQ;IAE3D,IAAA,0KAAS;wDAAC;YACR,MAAM;uFAAyB,CAAC;oBAI9B,OAAO,2BAA2B,8JAA4B,CAAC,GAAG,GAC9D,eAAe,GAAG,GAClB,2BAA2B,8JAA4B,CAAC,GAAG,GACzD,eAAe,GAAG,GAClB,CAAC,eAAe,GAAG,GAAG,eAAe,GAAG,IAAI;gBACpD;;YAEA,MAAM,mBACJ,oKAAkC,CAAC,uBAAuB;YAC5D,KAAK,QAAQ,CAAC,oBAAoB;YAElC,MAAM,8BACJ,oKAAkC,CAAC,+BAA+B,CAChE,wBACD;YACH,KAAK,QAAQ,CAAC,uBAAuB;YAErC,MAAM,0CACJ,yKAAuC,CAAC,iBAAiB;YAE3D,MAAM,oCAAoC,uBACxC;YAEF,KAAK,QAAQ,CACX,qCACA;YAGF,MAAM,4BACJ,oCACA,8BACA;YACF,KAAK,QAAQ,CAAC,6BAA6B;YAE3C,MAAM,qCACJ,mKAAiC,CAAC,iBAAiB;YACrD,MAAM,8BAA8B,uBAClC;YAEF,KAAK,QAAQ,CAAC,+BAA+B;YAE7C,MAAM,sBACJ,8BACA,mBACA;YACF,KAAK,QAAQ,CAAC,uBAAuB;YAErC,MAAM,gCACJ,6KAA2C,CAAC,iBAAiB;YAC/D,MAAM,0BACJ,CAAC,8BAA8B,GAAG,GAAG,8BAA8B,GAAG,IACtE;YACF,KAAK,QAAQ,CAAC,kBAAkB;YAEhC,MAAM,+BACJ,4KAA0C,CAAC,iBAAiB;YAC9D,MAAM,yBACJ,CAAC,6BAA6B,GAAG,GAAG,6BAA6B,GAAG,IAAI;YAC1E,KAAK,QAAQ,CAAC,gBAAgB;YAE9B,WAAW;YACX,MAAM,2CACJ,4BAA4B;YAC9B,KAAK,QAAQ,CACX,4CACA;YAGF,MAAM,4BAA4B;YAClC,KAAK,QAAQ,CAAC,6BAA6B;YAE3C,MAAM,mBACJ,kBAAkB,wJAAsB,CAAC,QAAQ,GAC7C,MACA,kBAAkB,wJAAsB,CAAC,QAAQ,GAC/C,MACA;YACR,KAAK,QAAQ,CAAC,oBAAoB;QACpC;uDAAG;QACD;QACA;QACA;QACA;QACA;QACA;KACD;IAED,IAAA,0KAAS;wDAAC;YACR,MAAM,eAAe,KAAK,KAAK;6EAAC,CAAA;oBAC9B;qFAAU,CAAC,OAAc,CAAC;gCACxB,GAAG,IAAI;gCACP,yBAAyB;4BAC3B,CAAC;;gBACH;;YACA;gEAAO,IAAM,aAAa,WAAW;;QACvC;uDAAG;QAAC;QAAM;KAAU;IAEpB,qBACE,6LAAC,2IAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,6LAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,wJAAsB;oBAC/B,eAAe,CAAA,IAAK,EAAE,AAAC,4BAA6B,OAAF;oBAClD,GAAG;;;;;;8BAEL,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,yJAAuB;oBAChC,eAAe,CAAA,IAAK,EAAE,AAAC,eAAgB,OAAF;oBACrC,GAAG;;;;;;8BAEL,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,iKAA+B;oBACxC,eAAe,CAAA,IAAK,EAAE,AAAC,kBAAmB,OAAF;oBACxC,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,wJAAsB;oBAC/B,eAAe,CAAA,IAAK,EAAE,AAAC,kBAAmB,OAAF;oBACxC,GAAG;;;;;;8BAEL,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,gKAA8B;oBACvC,eAAe,CAAA,IAAK,EAAE,AAAC,yBAA0B,OAAF;oBAC/C,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,0JAAwB;oBACjC,eAAe,CAAA,IAAK,EAAE,AAAC,YAAa,OAAF;oBAClC,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,+IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,6LAAC,uOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB;GA9Ta;;QASD,4NAAe;QAGT,4NAAe;QACf,4NAAe;QACX,2MAAc;QAErB,4KAAO;QAiCU,mNAA2B;;;KAjD9C", "debugId": null}}, {"offset": {"line": 3968, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/sections/maschi-murari-panel-geometry-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type MaschiMurariPanelGeometrySchemaInputs,\r\n  type MaschiMurariParamsSchemaInputs,\r\n  maschiMurariPanelGeometrySchema,\r\n} from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<MaschiMurariPanelGeometrySchemaInputs>\r\n  params: MaschiMurariParamsSchemaInputs\r\n  setParams: (newParams: any) => void\r\n  onSave: () => void\r\n}\r\n\r\nexport const MaschiMurariPanelGeometryForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  params,\r\n  setParams,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.maschi-murari.panel-geometry')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const form = useForm<MaschiMurariPanelGeometrySchemaInputs>({\r\n    resolver: zodResolver(maschiMurariPanelGeometrySchema),\r\n    defaultValues: {\r\n      height: defaultValues?.height ?? 0,\r\n      width: defaultValues?.width ?? 0,\r\n      thickness: defaultValues?.thickness ?? 0,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: MaschiMurariPanelGeometrySchemaInputs) => {\r\n      // construct body based on global schema:\r\n      const maschiMurariParams: MaschiMurariParamsSchemaInputs = {\r\n        ...params,\r\n        panelGeometry: body,\r\n      }\r\n      mutate({ projectId, moduleId, body: maschiMurariParams })\r\n    },\r\n    [mutate, projectId, moduleId, params],\r\n  )\r\n\r\n  useEffect(() => {\r\n    const subscription = form.watch(values => {\r\n      setParams((prev: any) => ({\r\n        ...prev,\r\n        buildingCharacteristics: values,\r\n      }))\r\n    })\r\n    return () => subscription.unsubscribe()\r\n  }, [form, setParams])\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <Image\r\n          src=\"/assets/masonry_frcm/FRCM_Muratura.jpg\"\r\n          alt=\"site characteristics\"\r\n          height={250}\r\n          width={500}\r\n          className=\"mx-auto rounded-md object-contain\"\r\n          priority\r\n        />\r\n        <NumberFormInput control={form.control} name=\"height\" t={t} />\r\n        <NumberFormInput control={form.control} name=\"width\" t={t} />\r\n        <NumberFormInput control={form.control} name=\"thickness\" t={t} />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAGA;AACA;AAKA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AAYO,MAAM,gCAAgC;QAAC,EAC5C,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACN,SAAS,EACT,MAAM,EACA;;IACN,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,cAAc,IAAA,2MAAc;QAKtB,uBACD,sBACI;IALf,MAAM,OAAO,IAAA,4KAAO,EAAwC;QAC1D,UAAU,IAAA,gLAAW,EAAC,oMAA+B;QACrD,eAAe;YACb,QAAQ,CAAA,wBAAA,0BAAA,oCAAA,cAAe,MAAM,cAArB,mCAAA,wBAAyB;YACjC,OAAO,CAAA,uBAAA,0BAAA,oCAAA,cAAe,KAAK,cAApB,kCAAA,uBAAwB;YAC/B,WAAW,CAAA,2BAAA,0BAAA,oCAAA,cAAe,SAAS,cAAxB,sCAAA,2BAA4B;QACzC;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,mNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,SAAS;yEAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;gBACtB,MAAM,YAAY,iBAAiB,CAAC;oBAClC,UAAU;wBAAC;wBAAW;4BAAE;4BAAW;wBAAS;qBAAE;gBAChD;gBACA;YACF;;QACA,OAAO;yEAAE,CAAA;gBACP,oJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAC7D;;IACF;IAGF,MAAM,mBAAmB,IAAA,4KAAW;uEAClC,CAAC;YACC,yCAAyC;YACzC,MAAM,qBAAqD;gBACzD,GAAG,MAAM;gBACT,eAAe;YACjB;YACA,OAAO;gBAAE;gBAAW;gBAAU,MAAM;YAAmB;QACzD;sEACA;QAAC;QAAQ;QAAW;QAAU;KAAO;IAGvC,IAAA,0KAAS;mDAAC;YACR,MAAM,eAAe,KAAK,KAAK;wEAAC,CAAA;oBAC9B;gFAAU,CAAC,OAAc,CAAC;gCACxB,GAAG,IAAI;gCACP,yBAAyB;4BAC3B,CAAC;;gBACH;;YACA;2DAAO,IAAM,aAAa,WAAW;;QACvC;kDAAG;QAAC;QAAM;KAAU;IAEpB,qBACE,6LAAC,2IAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,6LAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,6LAAC,2IAAK;oBACJ,KAAI;oBACJ,KAAI;oBACJ,QAAQ;oBACR,OAAO;oBACP,WAAU;oBACV,QAAQ;;;;;;8BAEV,6LAAC,qLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAS,GAAG;;;;;;8BACzD,6LAAC,qLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAQ,GAAG;;;;;;8BACxD,6LAAC,qLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAY,GAAG;;;;;;8BAC5D,6LAAC,+IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,6LAAC,uOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB;GA1Fa;;QASD,4NAAe;QACT,4NAAe;QACf,4NAAe;QACX,2MAAc;QAErB,4KAAO;QASU,mNAA2B;;;KAvB9C", "debugId": null}}, {"offset": {"line": 4177, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/maschi-murari/maschi-murari-params-form.tsx"], "sourcesContent": ["import {\r\n  Accordion as AccordionComponent,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from '@atlas/components/ui/accordion'\r\nimport type {\r\n  Module,\r\n  ModuleWithParamsMaschiMurari,\r\n} from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { MaschiMurariParamsSchemaInputs } from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useState } from 'react'\r\nimport { MaschiMurariFlexuralCalculation } from './calculations/flexural/maschi-murari-reinforcement-flexural-calculation'\r\nimport { MaschiMurariShearCalculation } from './calculations/shear/maschi-murari-reinforcement-shear-calculation'\r\nimport { MaschiMurariActionsForm } from './sections/maschi-murari-actions-form'\r\nimport { MaschiMurariMaterialPropertiesForm } from './sections/maschi-murari-material-properties-form'\r\nimport { MaschiMurariPanelGeometryForm } from './sections/maschi-murari-panel-geometry-form'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  module: ModuleWithParamsMaschiMurari\r\n}\r\n\r\nexport const MaschiMurariParamsForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  module,\r\n}: Props) => {\r\n  const [params, setParams] = useState<MaschiMurariParamsSchemaInputs>(\r\n    module?.params ?? ({} as MaschiMurariParamsSchemaInputs),\r\n  )\r\n\r\n  const [openItems, setOpenItems] = useState(['0'])\r\n  const t = useTranslations('forms.project-params.maschi-murari')\r\n\r\n  const handleItemSaved = useCallback((id: string) => {\r\n    const nextId = String(Number(id) + 1)\r\n    setOpenItems(old => {\r\n      const temp = old.filter(v => v !== id)\r\n      return old.includes(nextId) ? temp : [...temp, nextId]\r\n    })\r\n  }, [])\r\n\r\n  return (\r\n    <div>\r\n      <AccordionComponent\r\n        type=\"multiple\"\r\n        value={openItems}\r\n        onValueChange={setOpenItems}\r\n      >\r\n        <AccordionItem value=\"0\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">{t('panel-geometry.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <MaschiMurariPanelGeometryForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={params?.panelGeometry}\r\n              setParams={setParams}\r\n              params={params}\r\n              onSave={() => handleItemSaved('0')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"1\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('material-properties.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <MaschiMurariMaterialPropertiesForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={params?.materialProperties}\r\n              setParams={setParams}\r\n              params={params}\r\n              onSave={() => handleItemSaved('1')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"2\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">{t('actions.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <MaschiMurariActionsForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={params.actions}\r\n              params={params}\r\n              setParams={setParams}\r\n              onSave={() => handleItemSaved('2')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem\r\n          value=\"3\"\r\n          disabled={\r\n            !module?.params?.panelGeometry ||\r\n            !module?.params?.materialProperties ||\r\n            !module?.params?.actions\r\n          }\r\n        >\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">{t('shear.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <MaschiMurariShearCalculation\r\n              session={session}\r\n              projectId={projectId}\r\n              module={module}\r\n              onNext={() => handleItemSaved('3')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem\r\n          value=\"4\"\r\n          disabled={\r\n            !module?.params?.panelGeometry ||\r\n            !module?.params?.materialProperties ||\r\n            !module?.params?.actions\r\n          }\r\n        >\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">{t('flexural.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <MaschiMurariFlexuralCalculation\r\n              session={session}\r\n              projectId={projectId}\r\n              module={module}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n      </AccordionComponent>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAaA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AASO,MAAM,yBAAyB;QAAC,EACrC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,MAAM,EACA;QA4EK,gBACA,iBACA,iBAkBA,iBACA,iBACA;;QAhGT;IADF,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAClC,CAAA,kBAAA,mBAAA,6BAAA,OAAQ,MAAM,cAAd,6BAAA,kBAAmB,CAAC;IAGtB,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAC;QAAC;KAAI;IAChD,MAAM,IAAI,IAAA,4NAAe,EAAC;IAE1B,MAAM,kBAAkB,IAAA,4KAAW;+DAAC,CAAC;YACnC,MAAM,SAAS,OAAO,OAAO,MAAM;YACnC;uEAAa,CAAA;oBACX,MAAM,OAAO,IAAI,MAAM;oFAAC,CAAA,IAAK,MAAM;;oBACnC,OAAO,IAAI,QAAQ,CAAC,UAAU,OAAO;2BAAI;wBAAM;qBAAO;gBACxD;;QACF;8DAAG,EAAE;IAEL,qBACE,6LAAC;kBACC,cAAA,6LAAC,qJAAkB;YACjB,MAAK;YACL,OAAO;YACP,eAAe;;8BAEf,6LAAC,yJAAa;oBAAC,OAAM;;sCACnB,6LAAC,4JAAgB;sCACf,cAAA,6LAAC;gCAAG,WAAU;0CAAuB,EAAE;;;;;;;;;;;sCAEzC,6LAAC,4JAAgB;sCACf,cAAA,6LAAC,4SAA6B;gCAC5B,SAAS;gCACT,WAAW;gCACX,UAAU;gCACV,aAAa,EAAE,mBAAA,6BAAA,OAAQ,aAAa;gCACpC,WAAW;gCACX,QAAQ;gCACR,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,6LAAC,yJAAa;oBAAC,OAAM;;sCACnB,6LAAC,4JAAgB;sCACf,cAAA,6LAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;;;;;;sCAGP,6LAAC,4JAAgB;sCACf,cAAA,6LAAC,sTAAkC;gCACjC,SAAS;gCACT,WAAW;gCACX,UAAU;gCACV,aAAa,EAAE,mBAAA,6BAAA,OAAQ,kBAAkB;gCACzC,WAAW;gCACX,QAAQ;gCACR,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,6LAAC,yJAAa;oBAAC,OAAM;;sCACnB,6LAAC,4JAAgB;sCACf,cAAA,6LAAC;gCAAG,WAAU;0CAAuB,EAAE;;;;;;;;;;;sCAEzC,6LAAC,4JAAgB;sCACf,cAAA,6LAAC,4RAAuB;gCACtB,SAAS;gCACT,WAAW;gCACX,UAAU;gCACV,eAAe,OAAO,OAAO;gCAC7B,QAAQ;gCACR,WAAW;gCACX,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,6LAAC,yJAAa;oBACZ,OAAM;oBACN,UACE,EAAC,mBAAA,8BAAA,iBAAA,OAAQ,MAAM,cAAd,qCAAA,eAAgB,aAAa,KAC9B,EAAC,mBAAA,8BAAA,kBAAA,OAAQ,MAAM,cAAd,sCAAA,gBAAgB,kBAAkB,KACnC,EAAC,mBAAA,8BAAA,kBAAA,OAAQ,MAAM,cAAd,sCAAA,gBAAgB,OAAO;;sCAG1B,6LAAC,4JAAgB;sCACf,cAAA,6LAAC;gCAAG,WAAU;0CAAuB,EAAE;;;;;;;;;;;sCAEzC,6LAAC,4JAAgB;sCACf,cAAA,6LAAC,oUAA4B;gCAC3B,SAAS;gCACT,WAAW;gCACX,QAAQ;gCACR,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,6LAAC,yJAAa;oBACZ,OAAM;oBACN,UACE,EAAC,mBAAA,8BAAA,kBAAA,OAAQ,MAAM,cAAd,sCAAA,gBAAgB,aAAa,KAC9B,EAAC,mBAAA,8BAAA,kBAAA,OAAQ,MAAM,cAAd,sCAAA,gBAAgB,kBAAkB,KACnC,EAAC,mBAAA,8BAAA,kBAAA,OAAQ,MAAM,cAAd,sCAAA,gBAAgB,OAAO;;sCAG1B,6LAAC,4JAAgB;sCACf,cAAA,6LAAC;gCAAG,WAAU;0CAAuB,EAAE;;;;;;;;;;;sCAEzC,6LAAC,4JAAgB;sCACf,cAAA,6LAAC,6UAA+B;gCAC9B,SAAS;gCACT,WAAW;gCACX,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB;GAxHa;;QAWD,4NAAe;;;KAXd", "debugId": null}}]}