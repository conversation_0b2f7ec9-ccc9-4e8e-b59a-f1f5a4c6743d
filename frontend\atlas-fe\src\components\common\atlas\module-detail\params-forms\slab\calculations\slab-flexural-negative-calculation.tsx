import { SlabFlexuralResultCard } from '@atlas/components/common/atlas/module-detail/params-forms/slab/calculations/slab-flexural-result-card'
import { CustomProductSection } from '@atlas/components/common/atlas/product/custom-product-section'
import { ProductSummaryCard } from '@atlas/components/common/atlas/product/product-summary-card'
import { NumberFormInput } from '@atlas/components/common/form/number-form-input'
import { SelectFormInput } from '@atlas/components/common/form/select-form-input'
import { Button } from '@atlas/components/ui/button'
import { Form } from '@atlas/components/ui/form'
import { Separator } from '@atlas/components/ui/separator'
import type { ModuleWithParamsSlab } from '@atlas/lib/api/modules/schemas/modules'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import { useModuleCalculation } from '@atlas/lib/mutation/modules/use-module-calculation'
import { useProductsByType } from '@atlas/lib/query/products/use-products-by-type'
import {
  type SlabFlexuralNegativeCalculationInput,
  slabFlexuralNegativeCalculationSchema,
} from '@atlas/types/schemas/slab-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Loader2Icon } from 'lucide-react'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useEffect, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'

type Props = {
  module: ModuleWithParamsSlab
  session: Session
  projectId: Project['id']
}

export const SlabFlexuralNegativeCalculation = ({
  session,
  module,
  projectId,
}: Props) => {
  const t = useTranslations('forms.calculations.slab.flexural-negative')
  const tAction = useTranslations('actions.calculations.slab.flexural-negative')
  const tCommon = useTranslations('actions.common')

  // Extract the execution input and result for negative flexural (M- / support verification)
  const executionInput = module.flexuralVerifyExecutionInputMMinus
  const calculationResult = module.flexuralVerifyExecutionResultMMinus

  // Debug logging
  console.log('🔍 Slab Flexural Negative Debug:', {
    flexuralVerifyExecutionInputMMinus:
      module.flexuralVerifyExecutionInputMMinus,
    flexuralVerifyExecutionResultMMinus:
      module.flexuralVerifyExecutionResultMMinus,
    executionInput: executionInput,
    calculationResult: calculationResult,
  })

  // Get FRC product name from module params
  const frcProductName =
    module.params?.slabFrcReinforcement?.frcReinforcementType

  const {
    data: products,
    isError: errorGettingProducts,
    isLoading: isLoadingProducts,
  } = useProductsByType(session, 'SLAB', 0, 100)

  // Find product by name from FRC reinforcement
  const frcProduct = useMemo(
    () => products?.content.find(p => p.name === frcProductName),
    [frcProductName, products],
  )

  const form = useForm<SlabFlexuralNegativeCalculationInput>({
    resolver: zodResolver(slabFlexuralNegativeCalculationSchema),
    defaultValues: {
      calculationType: 'FLEXURAL_VERIFY',
      input: {
        bendingMoment: executionInput?.bendingMoment ?? 40,
        isSpanVerification: false,
        productInput: {
          id: executionInput?.product?.id ?? frcProduct?.id ?? '',
          name: executionInput?.product?.name ?? frcProduct?.name ?? '',
          sourceType:
            executionInput?.product?.sourceType ??
            (frcProduct ? 'DATABASE' : 'CUSTOM'),
          frcSlabThickness:
            executionInput?.product?.thickness ?? frcProduct?.thickness,
          elasticModulus:
            executionInput?.product?.elasticModulus ??
            frcProduct?.elasticModulus,
          cylindricCompressiveStrength:
            executionInput?.product?.cylindricCompressiveStrength ??
            frcProduct?.cylindricCompressiveStrength,
          characteristicTensileStrength:
            executionInput?.product?.characteristicTensileStrength ??
            frcProduct?.characteristicTensileStrength,
          specificWeight:
            executionInput?.product?.specificWeight ??
            frcProduct?.specificWeight,
          adhesionToConcrete:
            executionInput?.product?.adhesionToConcrete ??
            frcProduct?.adhesionToConcrete,
          fiberType:
            executionInput?.product?.fiberType ??
            frcProduct?.fiberType ??
            'CARBON',
        },
      },
    },
  })

  const { mutate, isPending } = useModuleCalculation(session.accessToken, {
    onSuccess: () => {
      toast.success(tAction('calculate.success'))
    },
    onError: error => {
      toast.error(tAction('calculate.failure', { error: error.message }))
    },
  })

  const handleFormSubmit = (body: SlabFlexuralNegativeCalculationInput) => {
    mutate({ projectId, moduleId: module.id, body })
  }

  const productsOptions = [
    ...(products?.content.map(m => ({
      value: m.id,
      label: m.name ?? t('product.unnamed'),
    })) ?? []),
    { value: 'custom', label: t('product.custom') },
  ]

  const [productId] = form.watch(['input.productInput.id'])

  const selectedProduct = useMemo(
    () => products?.content.find(p => p.id === productId),
    [productId, products],
  )

  useEffect(() => {
    if (!productId) {
      return
    }
    if (productId === 'custom') {
      form.setValue('input.productInput.sourceType', 'CUSTOM')
      return
    }

    if (selectedProduct) {
      form.setValue('input.productInput', {
        id: selectedProduct.id,
        name: selectedProduct.name ?? '',
        sourceType: 'DATABASE',
        frcSlabThickness: selectedProduct.thickness,
        elasticModulus: selectedProduct.elasticModulus,
        cylindricCompressiveStrength:
          selectedProduct.cylindricCompressiveStrength,
        characteristicTensileStrength:
          selectedProduct.characteristicTensileStrength,
        specificWeight: selectedProduct.specificWeight,
        adhesionToConcrete: selectedProduct.adhesionToConcrete,
        fiberType: selectedProduct.fiberType ?? 'CARBON',
      })
    }
  }, [form, productId, selectedProduct])

  return (
    <div className="flex flex-col justify-center gap-4">
      <Form {...form}>
        <form
          className="space-y-4 rounded-md border p-4"
          onSubmit={form.handleSubmit(handleFormSubmit)}
        >
          <h1 className="text-3xl font-bold">{t('heading')}</h1>
          <p className="text-muted-foreground">{t('description')}</p>

          <Separator />

          <h2 className="text-xl font-bold">{t('product-section.title')}</h2>
          <SelectFormInput
            control={form.control}
            name="input.productInput.id"
            options={productsOptions}
            t={t}
            loading={isLoadingProducts}
            requestError={errorGettingProducts}
            errorMessage={t('products.error')}
          />

          {productId === 'custom' && <CustomProductSection />}
          {selectedProduct && <ProductSummaryCard product={selectedProduct} />}

          <Separator />

          <NumberFormInput
            control={form.control}
            name="input.bendingMoment"
            t={t}
          />

          <Button
            type="submit"
            className="w-full sm:w-auto"
            disabled={isPending}
          >
            {isPending && <Loader2Icon className="animate-spin" />}
            {tCommon('calculate')}
          </Button>
        </form>
      </Form>

      {calculationResult && (
        <SlabFlexuralResultCard result={calculationResult as any} />
      )}
    </div>
  )
}
