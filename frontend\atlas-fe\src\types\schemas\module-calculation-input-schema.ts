import {
  pillarFlexuralCalculationSchema,
  pillarShearCalculationSchema,
} from '@atlas/types/schemas/pillar-form'
import {
  rectangularBeamFlexuralCalculationSchema,
  rectangularBeamShearCalculationSchema,
} from '@atlas/types/schemas/rectangular-beam-from'
import {
  slabFlexuralNegativeCalculationSchema,
  slabFlexuralPositiveCalculationSchema,
  slabInterfaceSlipCalculationSchema,
  slabShearCalculationSchema,
} from '@atlas/types/schemas/slab-form'
import { z } from 'zod'
import { masonryAntiOverturningCompositeReinforcementSystemSchema } from './masonry/antioverturning-form'
import { designStrengthPostInterventionInputSchema } from './masonry/crm-form'
import { frcmColumnConfinementReinforcementInput } from './masonry/frcm-column-form'
import {
  maschiMurariFlexuralReinforcementExecutionSchema,
  maschiMurariShearReinforcementExecutionSchema,
} from './masonry/maschi-murari-form'

export const moduleCalculationInputSchema = z.union([
  rectangularBeamFlexuralCalculationSchema,
  rectangularBeamShearCalculationSchema,
  pillarFlexuralCalculationSchema,
  pillarShearCalculationSchema,
  slabFlexuralPositiveCalculationSchema,
  slabFlexuralNegativeCalculationSchema,
  slabShearCalculationSchema,
  slabInterfaceSlipCalculationSchema,
  masonryAntiOverturningCompositeReinforcementSystemSchema,
  designStrengthPostInterventionInputSchema,
  frcmColumnConfinementReinforcementInput,
  maschiMurariFlexuralReinforcementExecutionSchema,
  maschiMurariShearReinforcementExecutionSchema,
])

export type ModuleCalculationInput = z.infer<
  typeof moduleCalculationInputSchema
>
