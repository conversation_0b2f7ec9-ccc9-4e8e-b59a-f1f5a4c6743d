import { Badge } from '@atlas/components/ui/badge'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@atlas/components/ui/card'
import { cn } from '@atlas/lib/utils'
import type { NonReinforcedSectionShearSchema } from '@atlas/types/schemas/masonry/maschi-murari-form'
import { useLocale, useTranslations } from 'next-intl'

type Props = {
  nonReinforcedSectionShearResults?: NonReinforcedSectionShearSchema
}

export function ShearNonReinforcedSectionResultCard({
  nonReinforcedSectionShearResults,
}: Props) {
  const {
    inPlaneAppliedShear,
    verticalStress,
    correctionFactorBasedOnWallSlenderness,
    shearResistanceNotReinforcedMasonry,
    check,
  } = nonReinforcedSectionShearResults || {}

  const t = useTranslations(
    'components.calculations.maschi-murari.shearReinforcementCalculationResult.nonReinforcedSectionResult',
  )

  const locale = useLocale()

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('title')}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <span className="font-medium">{t('inPlaneAppliedShear.label')}:</span>{' '}
          <span>
            {inPlaneAppliedShear?.toLocaleString(locale, {
              maximumFractionDigits: 3,
            })}{' '}
          </span>
        </div>
        <div>
          <span className="font-medium">{t('verticalStress.label')}:</span>{' '}
          <span>
            {verticalStress?.toLocaleString(locale, {
              maximumFractionDigits: 3,
            })}{' '}
          </span>
        </div>
        <div>
          <span className="font-medium">
            {t('correctionFactorBasedOnWallSlenderness.label')}:
          </span>{' '}
          <span>
            {correctionFactorBasedOnWallSlenderness?.toLocaleString(locale, {
              maximumFractionDigits: 3,
            })}{' '}
          </span>
        </div>
        <div>
          <span className="font-medium">
            {t('shearResistanceNotReinforcedMasonry.label')}:
          </span>{' '}
          <span>
            {shearResistanceNotReinforcedMasonry?.toLocaleString(locale, {
              maximumFractionDigits: 3,
            })}{' '}
          </span>
        </div>
        <span className="font-medium">{t('check.label')}:</span>{' '}
        <Badge
          className={cn(
            'text-base px-3 py-1',
            check ? 'bg-green-600' : 'bg-red-600',
          )}
        >
          {check ? t('check.satisfied') : t('check.notSatisfied')}
        </Badge>
      </CardContent>
    </Card>
  )
}
