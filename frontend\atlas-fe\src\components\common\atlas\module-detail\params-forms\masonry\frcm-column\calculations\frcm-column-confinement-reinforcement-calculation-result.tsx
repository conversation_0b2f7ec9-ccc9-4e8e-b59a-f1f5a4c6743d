import {
  <PERSON>,
  CardContent,
  <PERSON>Header,
  CardTitle,
} from '@atlas/components/ui/card'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  TabsTrigger,
} from '@atlas/components/ui/tabs'
import type { ConfinementReinforcementCalculationResultSchema } from '@atlas/types/schemas/masonry/frcm-column-form'
import { useLocale, useTranslations } from 'next-intl'
import { ConfinementNonReinforcedSectionResultCard } from './confinement-nonreinforced-section-result-card'
import { ConfinementReinforcedSectionResultCard } from './confinement-reinforced-section-result-card'

type Props = {
  confinementReinforcementCalculationResult: ConfinementReinforcementCalculationResultSchema
}

export function FrcmColumnConfinementReinforcementCalculationResult({
  confinementReinforcementCalculationResult,
}: Props) {
  const t = useTranslations(
    'components.calculations.frcm-column.confinementReinforcementCalculationResult',
  )
  const locale = useLocale()

  return (
    <>
      <h3 className="text-lg font-medium py-4">{t('title')}</h3>
      <Card>
        <CardHeader>
          <CardTitle>{t('title')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <span className="font-medium">
              {t('matrixGeometricPercentage.label')}:
            </span>{' '}
            <span>
              {confinementReinforcementCalculationResult.matrixGeometricPercentage?.toLocaleString(
                locale,
                {
                  maximumFractionDigits: 5,
                },
              )}{' '}
              %
            </span>
          </div>
          <div>
            <span className="font-medium">
              {t('reinforcementGeometricPercentage.label')}:
            </span>{' '}
            <span>
              {confinementReinforcementCalculationResult.reinforcementGeometricPercentage?.toLocaleString(
                locale,
                {
                  maximumFractionDigits: 5,
                },
              )}{' '}
              %
            </span>
          </div>
          <div>
            <span className="font-medium">
              {t('coefficicentOfHorizontalEfficiency.label')}:
            </span>{' '}
            <span>
              {confinementReinforcementCalculationResult.coefficicentOfHorizontalEfficiency?.toLocaleString(
                locale,
                {
                  maximumFractionDigits: 5,
                },
              )}{' '}
            </span>
          </div>
          <div>
            <span className="font-medium">
              {t('coefficicentOfVerticalEfficiency.label')}:
            </span>{' '}
            <span>
              {confinementReinforcementCalculationResult.coefficicentOfVerticalEfficiency?.toLocaleString(
                locale,
                {
                  maximumFractionDigits: 5,
                },
              )}{' '}
            </span>
          </div>

          <div>
            <span className="font-medium">
              {t('coefficientOfOverallEfficiency.label')}:
            </span>{' '}
            <span>
              {confinementReinforcementCalculationResult.coefficientOfOverallEfficiency?.toLocaleString(
                locale,
                {
                  maximumFractionDigits: 5,
                },
              )}{' '}
            </span>
          </div>
          <div>
            <span className="font-medium">
              {t('coefficientOfEffectivenessOfTheMatrix.label')}:
            </span>{' '}
            <span>
              {confinementReinforcementCalculationResult.coefficientOfEffectivenessOfTheMatrix?.toLocaleString(
                locale,
                {
                  maximumFractionDigits: 5,
                },
              )}{' '}
            </span>
          </div>
          <div>
            <span className="font-medium">
              {t('computationalStrainOfTheComposite.label')}:
            </span>{' '}
            <span>
              {confinementReinforcementCalculationResult.computationalStrainOfTheComposite?.toLocaleString(
                locale,
                {
                  maximumFractionDigits: 5,
                },
              )}{' '}
            </span>
          </div>
          <div>
            <span className="font-medium">{t('confiningPressure.label')}:</span>{' '}
            <span>
              {confinementReinforcementCalculationResult.confiningPressure?.toLocaleString(
                locale,
                {
                  maximumFractionDigits: 5,
                },
              )}{' '}
            </span>
          </div>
          <div>
            <span className="font-medium">
              {t('effectiveConfiningPressure.label')}:
            </span>{' '}
            <span>
              {confinementReinforcementCalculationResult.effectiveConfiningPressure?.toLocaleString(
                locale,
                {
                  maximumFractionDigits: 5,
                },
              )}{' '}
            </span>
          </div>
        </CardContent>
      </Card>
      <Tabs defaultValue="nonReinforcedSection">
        <TabsList>
          <TabsTrigger value="nonReinforcedSection">
            {t('confinementNonReinforcedSectionResult.label')}
          </TabsTrigger>
          <TabsTrigger value="reinforcedSection">
            {t('confinementNonReinforcedSectionResult.label')}
          </TabsTrigger>
        </TabsList>
        <TabsContent value="nonReinforcedSection">
          <ConfinementNonReinforcedSectionResultCard
            nonReinforcedSectionResults={
              confinementReinforcementCalculationResult?.confinementCheck
                ?.nonReinforcedSection ?? {}
            }
          />
        </TabsContent>
        <TabsContent value="reinforcedSection">
          <ConfinementReinforcedSectionResultCard
            reinforcedSectionResults={
              confinementReinforcementCalculationResult?.confinementCheck
                ?.reinforcedSection ?? {}
            }
          />
        </TabsContent>
      </Tabs>
    </>
  )
}
