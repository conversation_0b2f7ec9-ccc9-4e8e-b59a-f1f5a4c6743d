{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/sonner.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useTheme } from 'next-themes'\r\nimport { Toaster as Sonner, type ToasterProps } from 'sonner'\r\n\r\nconst Toaster = ({ ...props }: ToasterProps) => {\r\n  const { theme = 'system' } = useTheme()\r\n\r\n  return (\r\n    <Sonner\r\n      theme={theme as ToasterProps['theme']}\r\n      className=\"toaster group\"\r\n      style={\r\n        {\r\n          '--normal-bg': 'var(--popover)',\r\n          '--normal-text': 'var(--popover-foreground)',\r\n          '--normal-border': 'var(--border)',\r\n        } as React.CSSProperties\r\n      }\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Toaster }\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,IAAA,4JAAQ;IAErC,qBACE,8OAAC,mJAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 61, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/providers/query-provider.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query'\r\nimport { ReactQueryDevtools } from '@tanstack/react-query-devtools'\r\nimport { useState } from 'react'\r\n\r\nexport default function QueryProvider({\r\n  children,\r\n}: {\r\n  children: React.ReactNode\r\n}) {\r\n  const [queryClient] = useState(\r\n    () =>\r\n      new QueryClient({\r\n        defaultOptions: {\r\n          queries: {\r\n            refetchOnWindowFocus: true,\r\n            staleTime: 60 * 1000,\r\n          },\r\n        },\r\n      }),\r\n  )\r\n\r\n  return (\r\n    <QueryClientProvider client={queryClient}>\r\n      {children}\r\n      {process.env.NODE_ENV !== 'production' && (\r\n        <ReactQueryDevtools initialIsOpen={false} />\r\n      )}\r\n    </QueryClientProvider>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAAA;AACA;AACA;AAJA;;;;;AAMe,SAAS,cAAc,EACpC,QAAQ,EAGT;IACC,MAAM,CAAC,YAAY,GAAG,IAAA,iNAAQ,EAC5B,IACE,IAAI,4LAAW,CAAC;YACd,gBAAgB;gBACd,SAAS;oBACP,sBAAsB;oBACtB,WAAW,KAAK;gBAClB;YACF;QACF;IAGJ,qBACE,8OAAC,6MAAmB;QAAC,QAAQ;;YAC1B;YACA,oDAAyB,8BACxB,8OAAC,0MAAkB;gBAAC,eAAe;;;;;;;;;;;;AAI3C", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/providers/app-theme-provider.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { ThemeProvider } from 'next-themes'\r\nimport type { PropsWithChildren } from 'react'\r\n\r\ntype Props = PropsWithChildren\r\n\r\nexport const AppThemeProvider = ({ children }: Props) => (\r\n  <ThemeProvider\r\n    attribute=\"class\"\r\n    enableSystem\r\n    defaultTheme=\"system\"\r\n    disableTransitionOnChange\r\n  >\r\n    {children}\r\n  </ThemeProvider>\r\n)\r\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAOO,MAAM,mBAAmB,CAAC,EAAE,QAAQ,EAAS,iBAClD,8OAAC,iKAAa;QACZ,WAAU;QACV,YAAY;QACZ,cAAa;QACb,yBAAyB;kBAExB", "debugId": null}}]}