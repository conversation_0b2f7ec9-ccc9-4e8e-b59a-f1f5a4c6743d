import { PRODUCT_FIBER_TYPE } from '@atlas/constants/product'
import { z } from 'zod'

const productCustomSchema = z.object({
  id: z.string(),
  sourceType: z.literal('CUSTOM'),
  name: z.string().optional().nullable(),
  thickness: z.number().optional().nullable(),
  tensileStrength: z.number().optional().nullable(),
  elasticModulus: z.number().optional().nullable(),
  networkDeformation: z.number().optional().nullable(),
  matrixBreakdownVoltage: z.number().optional().nullable(),
  conventionalLimitVoltage: z.number().optional().nullable(),
  fiberType: z.enum(PRODUCT_FIBER_TYPE),
})

const productDatabaseSchema = z.object({
  sourceType: z.literal('DATABASE'),
  id: z.string(),
  name: z.string().optional().nullable(),
})

export const productFormSchema = z.discriminatedUnion('sourceType', [
  productCustomSchema,
  productDatabaseSchema,
])
