import { NumberFormInput } from '@atlas/components/common/form/number-form-input'
import { SelectFormInput } from '@atlas/components/common/form/select-form-input'
import { Button } from '@atlas/components/ui/button'
import { Form } from '@atlas/components/ui/form'
import type { Module } from '@atlas/lib/api/modules/schemas/modules'
import { woodGeometryPropertiesSchema } from '@atlas/lib/api/modules/schemas/wood-params'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'
import { zodResolver } from '@hookform/resolvers/zod'

import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useCallback, useEffect } from 'react'
import { useForm, useWatch } from 'react-hook-form'
import { toast } from 'sonner'
import { z } from 'zod'
const formSchema = woodGeometryPropertiesSchema.extend({
  id: z.string().optional(),
})

type FormSchema = z.infer<typeof formSchema>

type Props = {
  session: Session
  projectId: Project['id']
  moduleId: Module['id']
  defaultValues?: Partial<FormSchema>
  materialProperties?: {
    characteristicBendingStrength?: number
    characteristicShearStrength?: number
    meanElasticityModulus?: number
    partialMaterialFactor?: number
  }
  onSave: () => void
}

const SERVICE_CLASS_OPTIONS = [
  { value: 'SERVICE_CLASS_1', label: 'Classe di servizio 1' },
  { value: 'SERVICE_CLASS_2', label: 'Classe di servizio 2' },
  { value: 'SERVICE_CLASS_3', label: 'Classe di servizio 3' },
]

const LOAD_DURATION_OPTIONS = [
  { value: 'PERMANENTE', label: 'Permanente' },
  { value: 'LUNGA', label: 'Lunga' },
  { value: 'MEDIA', label: 'Media' },
  { value: 'BREVE', label: 'Breve' },
  { value: 'INSTANTANEA', label: 'Instantanea' },
]

const KMOD_TABLE = {
  SERVICE_CLASS_1: {
    PERMANENTE: 0.6,
    LUNGA: 0.7,
    MEDIA: 0.8,
    BREVE: 0.9,
    INSTANTANEA: 1.1,
  },
  SERVICE_CLASS_2: {
    PERMANENTE: 0.6,
    LUNGA: 0.7,
    MEDIA: 0.8,
    BREVE: 0.9,
    INSTANTANEA: 1.1,
  },
  SERVICE_CLASS_3: {
    PERMANENTE: 0.5,
    LUNGA: 0.55,
    MEDIA: 0.65,
    BREVE: 0.7,
    INSTANTANEA: 0.9,
  },
}

const KDEF_VALUES = {
  SERVICE_CLASS_1: 0.6,
  SERVICE_CLASS_2: 0.8,
  SERVICE_CLASS_3: 2.0,
}

export const WoodBeamGeometryForm = ({
  session,
  projectId,
  moduleId,
  defaultValues,
  materialProperties,
  onSave,
}: Props) => {
  const t = useTranslations('forms.project-params.wood.geometric_properties')
  const tAction = useTranslations('actions.save-project-params.messages')
  const tCommon = useTranslations('actions.common')

  const { mutate, isPending } = useSaveModuleParamsMutation(
    session.accessToken,
    {
      onSuccess: () => {
        toast.success(tAction('edit.success'))
        onSave()
      },
      onError: () => {
        toast.error(tAction('edit.error'))
      },
    },
  )

  const form = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      id: defaultValues?.id,
      beamSectionWidth: defaultValues?.beamSectionWidth ?? 0,
      beamSectionHeight: defaultValues?.beamSectionHeight ?? 0,
      beamSpacing: defaultValues?.beamSpacing ?? 0,
      beamSpan: defaultValues?.beamSpan ?? 0,
      sectionModulus: defaultValues?.sectionModulus ?? 0,
      inertiaMomentAboutY: defaultValues?.inertiaMomentAboutY ?? 0,
      serviceClass: defaultValues?.serviceClass ?? 'SERVICE_CLASS_1',
      loadDuration: defaultValues?.loadDuration ?? 'MEDIA',
      correctionFactor: defaultValues?.correctionFactor ?? 0.8,
      deformabilityFactor: defaultValues?.deformabilityFactor ?? 0.6,
      designBendingStrength: defaultValues?.designBendingStrength ?? 0,
      designShearStrength: defaultValues?.designShearStrength ?? 0,
      elasticityInstantaneousModulus:
        defaultValues?.elasticityInstantaneousModulus ?? 0,
      longTermElasticityModulus: defaultValues?.longTermElasticityModulus ?? 0,
    },
    shouldUnregister: false,
  })

  const beamSectionWidth = useWatch({ control: form.control, name: 'beamSectionWidth' })
  const beamSectionHeight = useWatch({ control: form.control, name: 'beamSectionHeight' })
  const serviceClass = useWatch({ control: form.control, name: 'serviceClass' })
  const loadDuration = useWatch({ control: form.control, name: 'loadDuration' })
  const correctionFactor = useWatch({ control: form.control, name: 'correctionFactor' })
  const deformabilityFactor = useWatch({ control: form.control, name: 'deformabilityFactor' })

  useEffect(() => {
    if (beamSectionWidth > 0 && beamSectionHeight > 0) {
      const sectionModulus = (beamSectionWidth * Math.pow(beamSectionHeight, 2)) / 6
      const inertiaMoment = (beamSectionWidth * Math.pow(beamSectionHeight, 3)) / 12
      form.setValue('sectionModulus', sectionModulus)
      form.setValue('inertiaMomentAboutY', inertiaMoment)
    }
  }, [beamSectionWidth, beamSectionHeight, form])

  useEffect(() => {
    if (serviceClass && loadDuration) {
      const correction =
        KMOD_TABLE[serviceClass as keyof typeof KMOD_TABLE]?.[
          loadDuration as keyof (typeof KMOD_TABLE)['SERVICE_CLASS_1']
        ] ?? 0.8
      form.setValue('correctionFactor', correction)
    }

    if (serviceClass) {
      const deformability = KDEF_VALUES[serviceClass as keyof typeof KDEF_VALUES] ?? 0.6
      form.setValue('deformabilityFactor', deformability)
    }
  }, [serviceClass, loadDuration, form])

  useEffect(() => {
    const {
      characteristicBendingStrength = 0,
      characteristicShearStrength = 0,
      partialMaterialFactor = 1.3,
      meanElasticityModulus = 0,
    } = materialProperties ?? {}

    if (
      correctionFactor > 0 &&
      partialMaterialFactor > 0 &&
      characteristicBendingStrength > 0 &&
      characteristicShearStrength > 0
    ) {
      const designBendingStrength =
        (characteristicBendingStrength * correctionFactor) / partialMaterialFactor
      const designShearStrength =
        (characteristicShearStrength * correctionFactor) / partialMaterialFactor

      form.setValue('designBendingStrength', designBendingStrength)
      form.setValue('designShearStrength', designShearStrength)
    }

    if (meanElasticityModulus > 0) {
      form.setValue('elasticityInstantaneousModulus', meanElasticityModulus)
      if (deformabilityFactor > 0) {
        const longTermModulus = meanElasticityModulus / (1 + deformabilityFactor)
        form.setValue('longTermElasticityModulus', longTermModulus)
      }
    }
  }, [correctionFactor, deformabilityFactor, materialProperties, form])

  const handleFormSubmit = useCallback(
    (data: FormSchema) => {
      mutate({ projectId, moduleId, body: { geometry: data as any } })
    },
    [mutate, projectId, moduleId],
  )

  return (
    <Form {...form}>
      <form
        className="space-y-4 rounded-md border p-4 grow"
        onSubmit={form.handleSubmit(handleFormSubmit)}
      >
        {/* User Input Fields */}
        <NumberFormInput control={form.control} name="beamSectionWidth" t={t} required />
        <NumberFormInput control={form.control} name="beamSectionHeight" t={t} required />
        <NumberFormInput control={form.control} name="beamSpacing" t={t} required />
        <NumberFormInput control={form.control} name="beamSpan" t={t} required />

        {/* Calculated Fields */}
        <NumberFormInput
          control={form.control}
          name="sectionModulus"
          t={t}
          disabled
          decimalPlaces={5}
        />
        <NumberFormInput
          control={form.control}
          name="inertiaMomentAboutY"
          t={t}
          disabled
          decimalPlaces={5}
        />

        {/* Dropdown Fields */}
        <SelectFormInput
          control={form.control}
          name="serviceClass"
          options={SERVICE_CLASS_OPTIONS}
          t={t}
          required
        />
        <SelectFormInput
          control={form.control}
          name="loadDuration"
          options={LOAD_DURATION_OPTIONS}
          t={t}
          required
        />

        {/* More Calculated Fields */}
        <NumberFormInput control={form.control} name="correctionFactor" t={t} disabled decimalPlaces={3} />
        <NumberFormInput control={form.control} name="deformabilityFactor" t={t} disabled decimalPlaces={3} />
        <NumberFormInput control={form.control} name="designBendingStrength" t={t} disabled decimalPlaces={3} />
        <NumberFormInput control={form.control} name="designShearStrength" t={t} disabled decimalPlaces={3} />
        <NumberFormInput control={form.control} name="elasticityInstantaneousModulus" t={t} disabled decimalPlaces={3} />
        <NumberFormInput control={form.control} name="longTermElasticityModulus" t={t} disabled decimalPlaces={3} />
        <Button type="submit" disabled={isPending}>
          {isPending ? tCommon('saving') : tCommon('save')}
        </Button>
      </form>
    </Form>
  )
}
