(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/perimeter-and-widespread-intervention-calculation-result-card.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "MasonryAntiOverturningPerimeterAndWidespreadInterventionCalculationResultCard",
    ()=>MasonryAntiOverturningPerimeterAndWidespreadInterventionCalculationResultCard
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/use-intl/dist/esm/development/react.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
function MasonryAntiOverturningPerimeterAndWidespreadInterventionCalculationResultCard(param) {
    let { perimeterAndWidespreadInterventionCalculationResult } = param;
    _s();
    const { overturningMoment, stabilizingMomentconnectors, totalStabilizingMoment, check } = perimeterAndWidespreadInterventionCalculationResult;
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('components.calculations.anti-overturning.postInterventionResult.perimeterAndWidespreadIntervention');
    const locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                    children: t('title')
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/perimeter-and-widespread-intervention-calculation-result-card.tsx",
                    lineNumber: 34,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/perimeter-and-widespread-intervention-calculation-result-card.tsx",
                lineNumber: 33,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                className: "space-y-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('overturningMoment.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/perimeter-and-widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 38,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    overturningMoment === null || overturningMoment === void 0 ? void 0 : overturningMoment.toLocaleString(locale, {
                                        maximumFractionDigits: 3
                                    }),
                                    ' ',
                                    "kNm"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/perimeter-and-widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 39,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/perimeter-and-widespread-intervention-calculation-result-card.tsx",
                        lineNumber: 37,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('stabilizingMomentconnectors.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/perimeter-and-widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 47,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    stabilizingMomentconnectors === null || stabilizingMomentconnectors === void 0 ? void 0 : stabilizingMomentconnectors.toLocaleString(locale, {
                                        maximumFractionDigits: 3
                                    }),
                                    ' ',
                                    "kNm"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/perimeter-and-widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 50,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/perimeter-and-widespread-intervention-calculation-result-card.tsx",
                        lineNumber: 46,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: [
                                            t('totalStabilizingMoment.label'),
                                            ":"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/perimeter-and-widespread-intervention-calculation-result-card.tsx",
                                        lineNumber: 59,
                                        columnNumber: 13
                                    }, this),
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            totalStabilizingMoment === null || totalStabilizingMoment === void 0 ? void 0 : totalStabilizingMoment.toLocaleString(locale, {
                                                maximumFractionDigits: 3
                                            }),
                                            ' ',
                                            "kNm"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/perimeter-and-widespread-intervention-calculation-result-card.tsx",
                                        lineNumber: 62,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/perimeter-and-widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 58,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('check.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/perimeter-and-widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 69,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-base px-3 py-1', check ? 'bg-green-600' : 'bg-red-600'),
                                children: check ? t('check.satisfied') : t('check.notSatisfied')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/perimeter-and-widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 70,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/perimeter-and-widespread-intervention-calculation-result-card.tsx",
                        lineNumber: 57,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/perimeter-and-widespread-intervention-calculation-result-card.tsx",
                lineNumber: 36,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/perimeter-and-widespread-intervention-calculation-result-card.tsx",
        lineNumber: 32,
        columnNumber: 5
    }, this);
}
_s(MasonryAntiOverturningPerimeterAndWidespreadInterventionCalculationResultCard, "ibHvjDxkLpek5QWjIfNYywjof5I=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"]
    ];
});
_c = MasonryAntiOverturningPerimeterAndWidespreadInterventionCalculationResultCard;
var _c;
__turbopack_context__.k.register(_c, "MasonryAntiOverturningPerimeterAndWidespreadInterventionCalculationResultCard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "MasonryAntiOverturningWidespreadInterventionCalculationResultCard",
    ()=>MasonryAntiOverturningWidespreadInterventionCalculationResultCard
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/use-intl/dist/esm/development/react.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
function MasonryAntiOverturningWidespreadInterventionCalculationResultCard(param) {
    let { widespreadInterventionCalculationResult } = param;
    var _regionTwoHypothesis_neutralAxisDistanceFromCompressedFlange, _regionTwoHypothesis_masonryDeformation, _regionTwoHypothesis_masonryResultantCompressiveStresses, _regionTwoHypothesis_frcmResultantTensileStresses, _regionTwoHypothesis_designResistingMomentReinforcedSection, _regionOneHypothesis_neutralAxisDistanceFromCompressedFlange, _regionOneHypothesis_masonryDeformation, _regionOneHypothesis_masonryResultantCompressiveStresses, _regionOneHypothesis_frcmResultantTensileStresses, _regionOneHypothesis_designResistingMomentReinforcedSection;
    _s();
    const { panelFundamentalPeriod, maximumAcceleration, overturningSeismicForce, actingMoment, midspanAxialStress, normalStress, unreinforcedSectionResistingMoment, regionOneHypothesis, regionTwoHypothesis, specificResistingMoment, check } = widespreadInterventionCalculationResult;
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('components.calculations.anti-overturning.postInterventionResult.widespreadIntervention');
    const locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                    children: t('title')
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                    lineNumber: 41,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                lineNumber: 40,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                className: "space-y-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('panelFundamentalPeriod.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 45,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    panelFundamentalPeriod === null || panelFundamentalPeriod === void 0 ? void 0 : panelFundamentalPeriod.toLocaleString(locale, {
                                        maximumFractionDigits: 4
                                    }),
                                    ' ',
                                    "s"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 48,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                        lineNumber: 44,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('maximumAcceleration.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 56,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: maximumAcceleration === null || maximumAcceleration === void 0 ? void 0 : maximumAcceleration.toLocaleString(locale, {
                                    maximumFractionDigits: 3
                                })
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 57,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                        lineNumber: 55,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('overturningSeismicForce.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 64,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    overturningSeismicForce === null || overturningSeismicForce === void 0 ? void 0 : overturningSeismicForce.toLocaleString(locale, {
                                        maximumFractionDigits: 3
                                    }),
                                    ' ',
                                    "kN"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 67,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                        lineNumber: 63,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('actingMoment.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 75,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    actingMoment === null || actingMoment === void 0 ? void 0 : actingMoment.toLocaleString(locale, {
                                        maximumFractionDigits: 3
                                    }),
                                    ' ',
                                    "kNm"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 76,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                        lineNumber: 74,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('midspanAxialStress.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 84,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    midspanAxialStress === null || midspanAxialStress === void 0 ? void 0 : midspanAxialStress.toLocaleString(locale, {
                                        maximumFractionDigits: 3
                                    }),
                                    ' ',
                                    "kN"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 85,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                        lineNumber: 83,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('normalStress.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 93,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    normalStress === null || normalStress === void 0 ? void 0 : normalStress.toLocaleString(locale, {
                                        maximumFractionDigits: 3
                                    }),
                                    ' ',
                                    "MPa"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 94,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                        lineNumber: 92,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('unreinforcedSectionResistingMoment.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 102,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    unreinforcedSectionResistingMoment === null || unreinforcedSectionResistingMoment === void 0 ? void 0 : unreinforcedSectionResistingMoment.toLocaleString(locale, {
                                        maximumFractionDigits: 3
                                    }),
                                    ' ',
                                    "kNm/m"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 105,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                        lineNumber: 101,
                        columnNumber: 9
                    }, this),
                    regionTwoHypothesis && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-4 border rounded-lg",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                className: "font-semibold mb-2",
                                children: t('regionHypothesis.regionTwoTitle')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 114,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: [
                                            t('regionHypothesis.neutralAxisDistanceFromCompressedFlange.label'),
                                            ":"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                        lineNumber: 118,
                                        columnNumber: 15
                                    }, this),
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            regionTwoHypothesis === null || regionTwoHypothesis === void 0 ? void 0 : (_regionTwoHypothesis_neutralAxisDistanceFromCompressedFlange = regionTwoHypothesis.neutralAxisDistanceFromCompressedFlange) === null || _regionTwoHypothesis_neutralAxisDistanceFromCompressedFlange === void 0 ? void 0 : _regionTwoHypothesis_neutralAxisDistanceFromCompressedFlange.toLocaleString(locale, {
                                                maximumFractionDigits: 4
                                            }),
                                            ' ',
                                            "mm"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                        lineNumber: 124,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 117,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: [
                                            t('regionHypothesis.masonryDeformation.label'),
                                            ":"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                        lineNumber: 135,
                                        columnNumber: 15
                                    }, this),
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            regionTwoHypothesis === null || regionTwoHypothesis === void 0 ? void 0 : (_regionTwoHypothesis_masonryDeformation = regionTwoHypothesis.masonryDeformation) === null || _regionTwoHypothesis_masonryDeformation === void 0 ? void 0 : _regionTwoHypothesis_masonryDeformation.toLocaleString(locale, {
                                                maximumFractionDigits: 3
                                            }),
                                            ' '
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                        lineNumber: 138,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 134,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: [
                                            t('regionHypothesis.masonryResultantCompressiveStresses.label'),
                                            ":"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                        lineNumber: 148,
                                        columnNumber: 15
                                    }, this),
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            regionTwoHypothesis === null || regionTwoHypothesis === void 0 ? void 0 : (_regionTwoHypothesis_masonryResultantCompressiveStresses = regionTwoHypothesis.masonryResultantCompressiveStresses) === null || _regionTwoHypothesis_masonryResultantCompressiveStresses === void 0 ? void 0 : _regionTwoHypothesis_masonryResultantCompressiveStresses.toLocaleString(locale, {
                                                maximumFractionDigits: 3
                                            }),
                                            ' ',
                                            "kN/m"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                        lineNumber: 154,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 147,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: [
                                            t('regionHypothesis.frcmResultantTensileStresses.label'),
                                            ":"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                        lineNumber: 165,
                                        columnNumber: 15
                                    }, this),
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            regionTwoHypothesis === null || regionTwoHypothesis === void 0 ? void 0 : (_regionTwoHypothesis_frcmResultantTensileStresses = regionTwoHypothesis.frcmResultantTensileStresses) === null || _regionTwoHypothesis_frcmResultantTensileStresses === void 0 ? void 0 : _regionTwoHypothesis_frcmResultantTensileStresses.toLocaleString(locale, {
                                                maximumFractionDigits: 3
                                            }),
                                            ' ',
                                            "kN/m"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                        lineNumber: 168,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 164,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: [
                                            t('regionHypothesis.designResistingMomentReinforcedSection.label'),
                                            ":"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                        lineNumber: 179,
                                        columnNumber: 15
                                    }, this),
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            regionTwoHypothesis === null || regionTwoHypothesis === void 0 ? void 0 : (_regionTwoHypothesis_designResistingMomentReinforcedSection = regionTwoHypothesis.designResistingMomentReinforcedSection) === null || _regionTwoHypothesis_designResistingMomentReinforcedSection === void 0 ? void 0 : _regionTwoHypothesis_designResistingMomentReinforcedSection.toLocaleString(locale, {
                                                maximumFractionDigits: 3
                                            }),
                                            ' ',
                                            "kNm/m"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                        lineNumber: 185,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 178,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: [
                                            t('regionHypothesis.hypothesisCheck.label'),
                                            ":"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                        lineNumber: 196,
                                        columnNumber: 15
                                    }, this),
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: regionTwoHypothesis.hypothesisCheck ? 'ok' : 'no'
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                        lineNumber: 199,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 195,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                        lineNumber: 113,
                        columnNumber: 11
                    }, this),
                    regionOneHypothesis && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-4 border rounded-lg",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                className: "font-semibold mb-2",
                                children: t('regionHypothesis.regionOneTitle')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 205,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: [
                                            t('regionHypothesis.neutralAxisDistanceFromCompressedFlange.label'),
                                            ":"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                        lineNumber: 209,
                                        columnNumber: 15
                                    }, this),
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            regionOneHypothesis === null || regionOneHypothesis === void 0 ? void 0 : (_regionOneHypothesis_neutralAxisDistanceFromCompressedFlange = regionOneHypothesis.neutralAxisDistanceFromCompressedFlange) === null || _regionOneHypothesis_neutralAxisDistanceFromCompressedFlange === void 0 ? void 0 : _regionOneHypothesis_neutralAxisDistanceFromCompressedFlange.toLocaleString(locale, {
                                                maximumFractionDigits: 4
                                            }),
                                            ' ',
                                            "mm"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                        lineNumber: 215,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 208,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: [
                                            t('regionHypothesis.masonryDeformation.label'),
                                            ":"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                        lineNumber: 226,
                                        columnNumber: 15
                                    }, this),
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            regionOneHypothesis === null || regionOneHypothesis === void 0 ? void 0 : (_regionOneHypothesis_masonryDeformation = regionOneHypothesis.masonryDeformation) === null || _regionOneHypothesis_masonryDeformation === void 0 ? void 0 : _regionOneHypothesis_masonryDeformation.toLocaleString(locale, {
                                                maximumFractionDigits: 3
                                            }),
                                            ' '
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                        lineNumber: 229,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 225,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: [
                                            t('regionHypothesis.masonryResultantCompressiveStresses.label'),
                                            ":"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                        lineNumber: 239,
                                        columnNumber: 15
                                    }, this),
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            regionOneHypothesis === null || regionOneHypothesis === void 0 ? void 0 : (_regionOneHypothesis_masonryResultantCompressiveStresses = regionOneHypothesis.masonryResultantCompressiveStresses) === null || _regionOneHypothesis_masonryResultantCompressiveStresses === void 0 ? void 0 : _regionOneHypothesis_masonryResultantCompressiveStresses.toLocaleString(locale, {
                                                maximumFractionDigits: 3
                                            }),
                                            ' ',
                                            "kN/m"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                        lineNumber: 245,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 238,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: [
                                            t('regionHypothesis.frcmResultantTensileStresses.label'),
                                            ":"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                        lineNumber: 256,
                                        columnNumber: 15
                                    }, this),
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            regionOneHypothesis === null || regionOneHypothesis === void 0 ? void 0 : (_regionOneHypothesis_frcmResultantTensileStresses = regionOneHypothesis.frcmResultantTensileStresses) === null || _regionOneHypothesis_frcmResultantTensileStresses === void 0 ? void 0 : _regionOneHypothesis_frcmResultantTensileStresses.toLocaleString(locale, {
                                                maximumFractionDigits: 3
                                            }),
                                            ' ',
                                            "kN/m"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                        lineNumber: 259,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 255,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: [
                                            t('regionHypothesis.designResistingMomentReinforcedSection.label'),
                                            ":"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                        lineNumber: 270,
                                        columnNumber: 15
                                    }, this),
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            regionOneHypothesis === null || regionOneHypothesis === void 0 ? void 0 : (_regionOneHypothesis_designResistingMomentReinforcedSection = regionOneHypothesis.designResistingMomentReinforcedSection) === null || _regionOneHypothesis_designResistingMomentReinforcedSection === void 0 ? void 0 : _regionOneHypothesis_designResistingMomentReinforcedSection.toLocaleString(locale, {
                                                maximumFractionDigits: 3
                                            }),
                                            ' ',
                                            "kNm/m"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                        lineNumber: 276,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 269,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: [
                                            t('regionHypothesis.hypothesisCheck.label'),
                                            ":"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                        lineNumber: 287,
                                        columnNumber: 15
                                    }, this),
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: regionOneHypothesis.hypothesisCheck ? 'ok' : 'no'
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                        lineNumber: 290,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 286,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                        lineNumber: 204,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('specificResistingMoment.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 295,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    specificResistingMoment === null || specificResistingMoment === void 0 ? void 0 : specificResistingMoment.toLocaleString(locale, {
                                        maximumFractionDigits: 3
                                    }),
                                    ' ',
                                    "kNm/m"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 298,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                        lineNumber: 294,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('check.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 306,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-base px-3 py-1', check ? 'bg-green-600' : 'bg-red-600'),
                                children: check ? t('check.satisfied') : t('check.notSatisfied')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                                lineNumber: 307,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                        lineNumber: 305,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
                lineNumber: 43,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx",
        lineNumber: 39,
        columnNumber: 5
    }, this);
}
_s(MasonryAntiOverturningWidespreadInterventionCalculationResultCard, "ibHvjDxkLpek5QWjIfNYywjof5I=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"]
    ];
});
_c = MasonryAntiOverturningWidespreadInterventionCalculationResultCard;
var _c;
__turbopack_context__.k.register(_c, "MasonryAntiOverturningWidespreadInterventionCalculationResultCard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-post-intervention-calculation-result.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "MasonryAntiOverturningPostInterventionCalculationResult",
    ()=>MasonryAntiOverturningPostInterventionCalculationResult
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/tabs.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/use-intl/dist/esm/development/react.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$anti$2d$overturning$2f$calculations$2f$perimeter$2d$and$2d$widespread$2d$intervention$2d$calculation$2d$result$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/perimeter-and-widespread-intervention-calculation-result-card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$anti$2d$overturning$2f$calculations$2f$widespread$2d$intervention$2d$calculation$2d$result$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
function MasonryAntiOverturningPostInterventionCalculationResult(param) {
    let { postInterventionCalculationResult } = param;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('components.calculations.anti-overturning.postInterventionResult');
    const _locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"])();
    var _postInterventionCalculationResult_perimeterAndWidespreadInterventionCalculationResult, _postInterventionCalculationResult_widespreadInterventionCalculationResult;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                className: "text-lg font-medium py-4",
                children: t('title')
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-post-intervention-calculation-result.tsx",
                lineNumber: 26,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tabs"], {
                defaultValue: "perimeterAndWidespreadIntervention",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsList"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                value: "perimeterAndWidespreadIntervention",
                                children: t('perimeterAndWidespreadIntervention.label')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-post-intervention-calculation-result.tsx",
                                lineNumber: 29,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                value: "widespreadIntervention",
                                children: t('widespreadIntervention.label')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-post-intervention-calculation-result.tsx",
                                lineNumber: 32,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-post-intervention-calculation-result.tsx",
                        lineNumber: 28,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                        value: "perimeterAndWidespreadIntervention",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$anti$2d$overturning$2f$calculations$2f$perimeter$2d$and$2d$widespread$2d$intervention$2d$calculation$2d$result$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MasonryAntiOverturningPerimeterAndWidespreadInterventionCalculationResultCard"], {
                            perimeterAndWidespreadInterventionCalculationResult: (_postInterventionCalculationResult_perimeterAndWidespreadInterventionCalculationResult = postInterventionCalculationResult.perimeterAndWidespreadInterventionCalculationResult) !== null && _postInterventionCalculationResult_perimeterAndWidespreadInterventionCalculationResult !== void 0 ? _postInterventionCalculationResult_perimeterAndWidespreadInterventionCalculationResult : {}
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-post-intervention-calculation-result.tsx",
                            lineNumber: 37,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-post-intervention-calculation-result.tsx",
                        lineNumber: 36,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                        value: "widespreadIntervention",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$anti$2d$overturning$2f$calculations$2f$widespread$2d$intervention$2d$calculation$2d$result$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MasonryAntiOverturningWidespreadInterventionCalculationResultCard"], {
                            widespreadInterventionCalculationResult: (_postInterventionCalculationResult_widespreadInterventionCalculationResult = postInterventionCalculationResult.widespreadInterventionCalculationResult) !== null && _postInterventionCalculationResult_widespreadInterventionCalculationResult !== void 0 ? _postInterventionCalculationResult_widespreadInterventionCalculationResult : {}
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-post-intervention-calculation-result.tsx",
                            lineNumber: 45,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-post-intervention-calculation-result.tsx",
                        lineNumber: 44,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-post-intervention-calculation-result.tsx",
                lineNumber: 27,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
_s(MasonryAntiOverturningPostInterventionCalculationResult, "YWbB9+16OW5GSQS5KLw4ThFOoro=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"]
    ];
});
_c = MasonryAntiOverturningPostInterventionCalculationResult;
var _c;
__turbopack_context__.k.register(_c, "MasonryAntiOverturningPostInterventionCalculationResult");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "MasonryAntiOverturningCompositeReinforcementSystemCalculationForm",
    ()=>MasonryAntiOverturningCompositeReinforcementSystemCalculationForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$product$2f$custom$2d$product$2d$section$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/product/custom-product-section.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$product$2f$product$2d$summary$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/product/product-summary-card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/select-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$product$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/product.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$module$2d$calculation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-module-calculation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$products$2f$use$2d$products$2d$by$2d$category$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/query/products/use-products-by-category.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$antioverturning$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/masonry/antioverturning-form.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dropdown$2d$menu$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$anti$2d$overturning$2f$calculations$2f$anti$2d$overturning$2d$post$2d$intervention$2d$calculation$2d$result$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-post-intervention-calculation-result.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const MasonryAntiOverturningCompositeReinforcementSystemCalculationForm = (param)=>{
    let { session, projectId, module } = param;
    var _module_compositeReinforcementSystemVerifyInput_meshInput, _module_compositeReinforcementSystemVerifyInput, _module_compositeReinforcementSystemVerifyInput_connectorInput, _module_compositeReinforcementSystemVerifyInput1, _module_compositeReinforcementSystemVerifyInput_matrixInput, _module_compositeReinforcementSystemVerifyInput2, _defaultValues_meshInput, _defaultValues_meshInput1, _defaultValues_meshInput2, _defaultValues_connectorInput, _defaultValues_matrixInput, _form_formState_errors_input_meshInput_meshProduct_id, _form_formState_errors_input_meshInput_meshProduct, _form_formState_errors_input_meshInput, _form_formState_errors_input, _form_formState_errors_input_connectorInput_connectorProduct_id, _form_formState_errors_input_connectorInput_connectorProduct, _form_formState_errors_input_connectorInput, _form_formState_errors_input1, _module_params_materialProperties, _module_params, _form_formState_errors_input_matrixInput_matrixProduct_id, _form_formState_errors_input_matrixInput_matrixProduct, _form_formState_errors_input_matrixInput, _form_formState_errors_input2;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.calculations.anti-overturning.composite-reinforcement-system');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.calculations.anti-overturning.composite-reinforcement-system');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const postInterventionCalculationResult = module.postInterventionCalculationResult;
    const defaultValues = module === null || module === void 0 ? void 0 : module.compositeReinforcementSystemVerifyInput;
    const meshProduct = module === null || module === void 0 ? void 0 : (_module_compositeReinforcementSystemVerifyInput = module.compositeReinforcementSystemVerifyInput) === null || _module_compositeReinforcementSystemVerifyInput === void 0 ? void 0 : (_module_compositeReinforcementSystemVerifyInput_meshInput = _module_compositeReinforcementSystemVerifyInput.meshInput) === null || _module_compositeReinforcementSystemVerifyInput_meshInput === void 0 ? void 0 : _module_compositeReinforcementSystemVerifyInput_meshInput.meshProduct;
    const connectorProduct = module === null || module === void 0 ? void 0 : (_module_compositeReinforcementSystemVerifyInput1 = module.compositeReinforcementSystemVerifyInput) === null || _module_compositeReinforcementSystemVerifyInput1 === void 0 ? void 0 : (_module_compositeReinforcementSystemVerifyInput_connectorInput = _module_compositeReinforcementSystemVerifyInput1.connectorInput) === null || _module_compositeReinforcementSystemVerifyInput_connectorInput === void 0 ? void 0 : _module_compositeReinforcementSystemVerifyInput_connectorInput.connectorProduct;
    const matrixProduct = module === null || module === void 0 ? void 0 : (_module_compositeReinforcementSystemVerifyInput2 = module.compositeReinforcementSystemVerifyInput) === null || _module_compositeReinforcementSystemVerifyInput2 === void 0 ? void 0 : (_module_compositeReinforcementSystemVerifyInput_matrixInput = _module_compositeReinforcementSystemVerifyInput2.matrixInput) === null || _module_compositeReinforcementSystemVerifyInput_matrixInput === void 0 ? void 0 : _module_compositeReinforcementSystemVerifyInput_matrixInput.matrixProduct;
    var _meshProduct_thickness, _meshProduct_width, _defaultValues_meshInput_reinforcedSidesNumber, _defaultValues_meshInput_resistantAreaPerMeter, _defaultValues_meshInput_reinforcedSidesNumber1, _meshProduct_id, _meshProduct_thickness1, _meshProduct_tensileStrength, _meshProduct_elasticModulus, _meshProduct_fiberType, _defaultValues_connectorInput_connectorSpacing, _connectorProduct_id, _connectorProduct_thickness, _connectorProduct_tensileStrength, _connectorProduct_elasticModulus, _connectorProduct_fiberType, _defaultValues_matrixInput_compositeSystemThickness, _matrixProduct_id, _matrixProduct_name;
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$antioverturning$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["masonryAntiOverturningCompositeReinforcementSystemSchema"]),
        defaultValues: {
            calculationType: 'POST_INTERVENTION_VERIFY',
            input: {
                meshInput: {
                    // to be calculated: D65*D64*D69
                    // D65 : thickness, D64: width, D69: reinforced sides number
                    resistantAreaPerMeter: (_defaultValues_meshInput_resistantAreaPerMeter = defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_meshInput = defaultValues.meshInput) === null || _defaultValues_meshInput === void 0 ? void 0 : _defaultValues_meshInput.resistantAreaPerMeter) !== null && _defaultValues_meshInput_resistantAreaPerMeter !== void 0 ? _defaultValues_meshInput_resistantAreaPerMeter : ((_meshProduct_thickness = meshProduct === null || meshProduct === void 0 ? void 0 : meshProduct.thickness) !== null && _meshProduct_thickness !== void 0 ? _meshProduct_thickness : 0) * ((_meshProduct_width = meshProduct === null || meshProduct === void 0 ? void 0 : meshProduct.width) !== null && _meshProduct_width !== void 0 ? _meshProduct_width : 0) * ((_defaultValues_meshInput_reinforcedSidesNumber = defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_meshInput1 = defaultValues.meshInput) === null || _defaultValues_meshInput1 === void 0 ? void 0 : _defaultValues_meshInput1.reinforcedSidesNumber) !== null && _defaultValues_meshInput_reinforcedSidesNumber !== void 0 ? _defaultValues_meshInput_reinforcedSidesNumber : 0),
                    reinforcedSidesNumber: (_defaultValues_meshInput_reinforcedSidesNumber1 = defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_meshInput2 = defaultValues.meshInput) === null || _defaultValues_meshInput2 === void 0 ? void 0 : _defaultValues_meshInput2.reinforcedSidesNumber) !== null && _defaultValues_meshInput_reinforcedSidesNumber1 !== void 0 ? _defaultValues_meshInput_reinforcedSidesNumber1 : 0,
                    meshProduct: {
                        id: (_meshProduct_id = meshProduct === null || meshProduct === void 0 ? void 0 : meshProduct.id) !== null && _meshProduct_id !== void 0 ? _meshProduct_id : '',
                        name: meshProduct === null || meshProduct === void 0 ? void 0 : meshProduct.name,
                        sourceType: (meshProduct === null || meshProduct === void 0 ? void 0 : meshProduct.id) === 'CUSTOM' ? 'CUSTOM' : 'DATABASE',
                        thickness: (_meshProduct_thickness1 = meshProduct === null || meshProduct === void 0 ? void 0 : meshProduct.thickness) !== null && _meshProduct_thickness1 !== void 0 ? _meshProduct_thickness1 : 0,
                        tensileStrength: (_meshProduct_tensileStrength = meshProduct === null || meshProduct === void 0 ? void 0 : meshProduct.tensileStrength) !== null && _meshProduct_tensileStrength !== void 0 ? _meshProduct_tensileStrength : 0,
                        elasticModulus: (_meshProduct_elasticModulus = meshProduct === null || meshProduct === void 0 ? void 0 : meshProduct.elasticModulus) !== null && _meshProduct_elasticModulus !== void 0 ? _meshProduct_elasticModulus : 0,
                        fiberType: (_meshProduct_fiberType = meshProduct === null || meshProduct === void 0 ? void 0 : meshProduct.fiberType) !== null && _meshProduct_fiberType !== void 0 ? _meshProduct_fiberType : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$product$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["productFiberType"].CARBON
                    }
                },
                connectorInput: {
                    connectorSpacing: (_defaultValues_connectorInput_connectorSpacing = defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_connectorInput = defaultValues.connectorInput) === null || _defaultValues_connectorInput === void 0 ? void 0 : _defaultValues_connectorInput.connectorSpacing) !== null && _defaultValues_connectorInput_connectorSpacing !== void 0 ? _defaultValues_connectorInput_connectorSpacing : 0,
                    connectorProduct: {
                        id: (_connectorProduct_id = connectorProduct === null || connectorProduct === void 0 ? void 0 : connectorProduct.id) !== null && _connectorProduct_id !== void 0 ? _connectorProduct_id : '',
                        name: connectorProduct === null || connectorProduct === void 0 ? void 0 : connectorProduct.name,
                        sourceType: (connectorProduct === null || connectorProduct === void 0 ? void 0 : connectorProduct.id) === 'custom' ? 'CUSTOM' : 'DATABASE',
                        thickness: (_connectorProduct_thickness = connectorProduct === null || connectorProduct === void 0 ? void 0 : connectorProduct.thickness) !== null && _connectorProduct_thickness !== void 0 ? _connectorProduct_thickness : 0,
                        tensileStrength: (_connectorProduct_tensileStrength = connectorProduct === null || connectorProduct === void 0 ? void 0 : connectorProduct.tensileStrength) !== null && _connectorProduct_tensileStrength !== void 0 ? _connectorProduct_tensileStrength : 0,
                        elasticModulus: (_connectorProduct_elasticModulus = connectorProduct === null || connectorProduct === void 0 ? void 0 : connectorProduct.elasticModulus) !== null && _connectorProduct_elasticModulus !== void 0 ? _connectorProduct_elasticModulus : 0,
                        fiberType: (_connectorProduct_fiberType = connectorProduct === null || connectorProduct === void 0 ? void 0 : connectorProduct.fiberType) !== null && _connectorProduct_fiberType !== void 0 ? _connectorProduct_fiberType : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$product$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["productFiberType"].CARBON
                    }
                },
                matrixInput: {
                    compositeSystemThickness: (_defaultValues_matrixInput_compositeSystemThickness = defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_matrixInput = defaultValues.matrixInput) === null || _defaultValues_matrixInput === void 0 ? void 0 : _defaultValues_matrixInput.compositeSystemThickness) !== null && _defaultValues_matrixInput_compositeSystemThickness !== void 0 ? _defaultValues_matrixInput_compositeSystemThickness : 0,
                    matrixProduct: {
                        id: (_matrixProduct_id = matrixProduct === null || matrixProduct === void 0 ? void 0 : matrixProduct.id) !== null && _matrixProduct_id !== void 0 ? _matrixProduct_id : '',
                        name: (_matrixProduct_name = matrixProduct === null || matrixProduct === void 0 ? void 0 : matrixProduct.name) !== null && _matrixProduct_name !== void 0 ? _matrixProduct_name : '',
                        sourceType: (matrixProduct === null || matrixProduct === void 0 ? void 0 : matrixProduct.id) === 'custom' ? 'CUSTOM' : 'DATABASE'
                    }
                }
            }
        }
    });
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$module$2d$calculation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useModuleCalculation"])(session.accessToken, {
        onSuccess: {
            "MasonryAntiOverturningCompositeReinforcementSystemCalculationForm.useModuleCalculation": ()=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(tAction('calculate.success'));
            }
        }["MasonryAntiOverturningCompositeReinforcementSystemCalculationForm.useModuleCalculation"],
        onError: {
            "MasonryAntiOverturningCompositeReinforcementSystemCalculationForm.useModuleCalculation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(tAction('calculate.failure', {
                    error: error.message
                }));
            }
        }["MasonryAntiOverturningCompositeReinforcementSystemCalculationForm.useModuleCalculation"]
    });
    const handleFormSubmit = (body)=>{
        mutate({
            projectId,
            moduleId: module.id,
            body
        });
    };
    const { data: productsAll, isError: errorGettingProducts, isLoading: isLoadingProducts } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$products$2f$use$2d$products$2d$by$2d$category$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useProductsByCategory"])(session, 'ANTI_OVERTURNING', 0, 100);
    var _productsAll_content_filter_map;
    const productsMeshOptions = [
        ...(_productsAll_content_filter_map = productsAll === null || productsAll === void 0 ? void 0 : productsAll.content.filter((m)=>m.productType === 'MESH').map((m)=>{
            var _m_name;
            return {
                value: m.id,
                label: (_m_name = m.name) !== null && _m_name !== void 0 ? _m_name : t('product.unnamed')
            };
        })) !== null && _productsAll_content_filter_map !== void 0 ? _productsAll_content_filter_map : []
    ];
    var _productsAll_content_filter_map1;
    const productsConnectorOptions = [
        ...(_productsAll_content_filter_map1 = productsAll === null || productsAll === void 0 ? void 0 : productsAll.content.filter((m)=>m.productType === 'CONNECTOR').map((m)=>{
            var _m_name;
            return {
                value: m.id,
                label: (_m_name = m.name) !== null && _m_name !== void 0 ? _m_name : t('product.unnamed')
            };
        })) !== null && _productsAll_content_filter_map1 !== void 0 ? _productsAll_content_filter_map1 : []
    ];
    var _productsAll_content_filter_map2;
    const productsMatrixOptions = [
        ...(_productsAll_content_filter_map2 = productsAll === null || productsAll === void 0 ? void 0 : productsAll.content.filter((m)=>m.productType === 'MATRIX').map((m)=>{
            var _m_name;
            return {
                value: m.id,
                label: (_m_name = m.name) !== null && _m_name !== void 0 ? _m_name : t('product.unnamed')
            };
        })) !== null && _productsAll_content_filter_map2 !== void 0 ? _productsAll_content_filter_map2 : []
    ];
    const [productMeshId] = form.watch([
        'input.meshInput.meshProduct.id'
    ]);
    const [productConnectorId] = form.watch([
        'input.connectorInput.connectorProduct.id'
    ]);
    const [productMatrixId] = form.watch([
        'input.matrixInput.matrixProduct.id'
    ]);
    const selectedProductMesh = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "MasonryAntiOverturningCompositeReinforcementSystemCalculationForm.useMemo[selectedProductMesh]": ()=>productsAll === null || productsAll === void 0 ? void 0 : productsAll.content.filter({
                "MasonryAntiOverturningCompositeReinforcementSystemCalculationForm.useMemo[selectedProductMesh]": (m)=>m.productType === 'MESH'
            }["MasonryAntiOverturningCompositeReinforcementSystemCalculationForm.useMemo[selectedProductMesh]"]).find({
                "MasonryAntiOverturningCompositeReinforcementSystemCalculationForm.useMemo[selectedProductMesh]": (p)=>p.id === productMeshId
            }["MasonryAntiOverturningCompositeReinforcementSystemCalculationForm.useMemo[selectedProductMesh]"])
    }["MasonryAntiOverturningCompositeReinforcementSystemCalculationForm.useMemo[selectedProductMesh]"], [
        productMeshId,
        productsAll
    ]);
    const selectedProductConnector = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "MasonryAntiOverturningCompositeReinforcementSystemCalculationForm.useMemo[selectedProductConnector]": ()=>productsAll === null || productsAll === void 0 ? void 0 : productsAll.content.filter({
                "MasonryAntiOverturningCompositeReinforcementSystemCalculationForm.useMemo[selectedProductConnector]": (m)=>m.productType === 'CONNECTOR'
            }["MasonryAntiOverturningCompositeReinforcementSystemCalculationForm.useMemo[selectedProductConnector]"]).find({
                "MasonryAntiOverturningCompositeReinforcementSystemCalculationForm.useMemo[selectedProductConnector]": (p)=>p.id === productConnectorId
            }["MasonryAntiOverturningCompositeReinforcementSystemCalculationForm.useMemo[selectedProductConnector]"])
    }["MasonryAntiOverturningCompositeReinforcementSystemCalculationForm.useMemo[selectedProductConnector]"], [
        productConnectorId,
        productsAll
    ]);
    const selectedProductMatrix = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "MasonryAntiOverturningCompositeReinforcementSystemCalculationForm.useMemo[selectedProductMatrix]": ()=>productsAll === null || productsAll === void 0 ? void 0 : productsAll.content.filter({
                "MasonryAntiOverturningCompositeReinforcementSystemCalculationForm.useMemo[selectedProductMatrix]": (m)=>m.productType === 'MATRIX'
            }["MasonryAntiOverturningCompositeReinforcementSystemCalculationForm.useMemo[selectedProductMatrix]"]).find({
                "MasonryAntiOverturningCompositeReinforcementSystemCalculationForm.useMemo[selectedProductMatrix]": (p)=>p.id === productMatrixId
            }["MasonryAntiOverturningCompositeReinforcementSystemCalculationForm.useMemo[selectedProductMatrix]"])
    }["MasonryAntiOverturningCompositeReinforcementSystemCalculationForm.useMemo[selectedProductMatrix]"], [
        productMatrixId,
        productsAll
    ]);
    const reinforcedSidesNumberValue = form.watch('input.meshInput.reinforcedSidesNumber');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MasonryAntiOverturningCompositeReinforcementSystemCalculationForm.useEffect": ()=>{
            if (productMeshId === 'custom') {
                form.setValue('input.meshInput.meshProduct.sourceType', 'CUSTOM');
            }
            if (productConnectorId === 'custom') {
                form.setValue('input.connectorInput.connectorProduct.sourceType', 'CUSTOM');
            }
            if (productMatrixId === 'custom') {
                form.setValue('input.matrixInput.matrixProduct.sourceType', 'CUSTOM');
            }
            if (selectedProductMesh) {
                var _selectedProductMesh_availableWidths;
                form.setValue('input.meshInput.meshProduct', {
                    id: selectedProductMesh.id,
                    name: selectedProductMesh.name,
                    sourceType: 'DATABASE'
                });
                var _selectedProductMesh_thickness, _selectedProductMesh_width, _ref;
                form.setValue('input.meshInput.resistantAreaPerMeter', ((_selectedProductMesh_thickness = selectedProductMesh === null || selectedProductMesh === void 0 ? void 0 : selectedProductMesh.thickness) !== null && _selectedProductMesh_thickness !== void 0 ? _selectedProductMesh_thickness : 0) * ((_ref = (_selectedProductMesh_width = selectedProductMesh === null || selectedProductMesh === void 0 ? void 0 : selectedProductMesh.width) !== null && _selectedProductMesh_width !== void 0 ? _selectedProductMesh_width : (_selectedProductMesh_availableWidths = selectedProductMesh.availableWidths) === null || _selectedProductMesh_availableWidths === void 0 ? void 0 : _selectedProductMesh_availableWidths[0]) !== null && _ref !== void 0 ? _ref : 0) * (reinforcedSidesNumberValue !== null && reinforcedSidesNumberValue !== void 0 ? reinforcedSidesNumberValue : 0));
            }
            if (selectedProductConnector) {
                form.setValue('input.connectorInput.connectorProduct', {
                    id: selectedProductConnector.id,
                    name: selectedProductConnector.name,
                    sourceType: 'DATABASE'
                });
            }
            if (selectedProductMatrix) {
                form.setValue('input.matrixInput.matrixProduct', {
                    id: selectedProductMatrix.id,
                    name: selectedProductMatrix.name,
                    sourceType: 'DATABASE'
                });
            }
        }
    }["MasonryAntiOverturningCompositeReinforcementSystemCalculationForm.useEffect"], [
        form,
        productMeshId,
        selectedProductMesh,
        selectedProductConnector,
        selectedProductMatrix,
        reinforcedSidesNumberValue,
        productConnectorId,
        productMatrixId
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col justify-center gap-4",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Form"], {
                ...form,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                    className: "space-y-4 rounded-md border p-4",
                    onSubmit: form.handleSubmit(handleFormSubmit),
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            src: "/assets/masonry-anti-overturning/antibaltamento-verify.jpg",
                            alt: "composite reinforcement system verify",
                            height: 250,
                            width: 500,
                            className: "mx-auto rounded-md object-contain",
                            priority: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx",
                            lineNumber: 266,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-xl font-bold",
                            children: t('mesh-sub-heading')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx",
                            lineNumber: 275,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormInput"], {
                            control: form.control,
                            name: "input.meshInput.meshProduct.id",
                            options: productsMeshOptions,
                            t: t,
                            loading: isLoadingProducts,
                            requestError: errorGettingProducts,
                            // get the error message from the form state if any
                            errorMessage: (_form_formState_errors_input = form.formState.errors.input) === null || _form_formState_errors_input === void 0 ? void 0 : (_form_formState_errors_input_meshInput = _form_formState_errors_input.meshInput) === null || _form_formState_errors_input_meshInput === void 0 ? void 0 : (_form_formState_errors_input_meshInput_meshProduct = _form_formState_errors_input_meshInput.meshProduct) === null || _form_formState_errors_input_meshInput_meshProduct === void 0 ? void 0 : (_form_formState_errors_input_meshInput_meshProduct_id = _form_formState_errors_input_meshInput_meshProduct.id) === null || _form_formState_errors_input_meshInput_meshProduct_id === void 0 ? void 0 : _form_formState_errors_input_meshInput_meshProduct_id.message
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx",
                            lineNumber: 276,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        productMeshId === 'custom' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$product$2f$custom$2d$product$2d$section$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CustomProductSection"], {}, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx",
                            lineNumber: 288,
                            columnNumber: 44
                        }, ("TURBOPACK compile-time value", void 0)),
                        selectedProductMesh && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$product$2f$product$2d$summary$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ProductSummaryCard"], {
                            product: selectedProductMesh,
                            isRectangularBeam: false
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx",
                            lineNumber: 290,
                            columnNumber: 15
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.meshInput.reinforcedSidesNumber",
                            t: t
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx",
                            lineNumber: 295,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.meshInput.resistantAreaPerMeter",
                            t: t,
                            disabled: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx",
                            lineNumber: 300,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dropdown$2d$menu$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx",
                            lineNumber: 306,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-xl font-bold",
                            children: t('connector-sub-heading')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx",
                            lineNumber: 308,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormInput"], {
                            control: form.control,
                            name: "input.connectorInput.connectorProduct.id",
                            options: productsConnectorOptions,
                            t: t,
                            loading: isLoadingProducts,
                            requestError: errorGettingProducts,
                            errorMessage: (_form_formState_errors_input1 = form.formState.errors.input) === null || _form_formState_errors_input1 === void 0 ? void 0 : (_form_formState_errors_input_connectorInput = _form_formState_errors_input1.connectorInput) === null || _form_formState_errors_input_connectorInput === void 0 ? void 0 : (_form_formState_errors_input_connectorInput_connectorProduct = _form_formState_errors_input_connectorInput.connectorProduct) === null || _form_formState_errors_input_connectorInput_connectorProduct === void 0 ? void 0 : (_form_formState_errors_input_connectorInput_connectorProduct_id = _form_formState_errors_input_connectorInput_connectorProduct.id) === null || _form_formState_errors_input_connectorInput_connectorProduct_id === void 0 ? void 0 : _form_formState_errors_input_connectorInput_connectorProduct_id.message
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx",
                            lineNumber: 309,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        productConnectorId === 'custom' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$product$2f$custom$2d$product$2d$section$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CustomProductSection"], {}, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx",
                            lineNumber: 321,
                            columnNumber: 49
                        }, ("TURBOPACK compile-time value", void 0)),
                        selectedProductConnector && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$product$2f$product$2d$summary$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ProductSummaryCard"], {
                            product: selectedProductConnector,
                            facingMaterial: module === null || module === void 0 ? void 0 : (_module_params = module.params) === null || _module_params === void 0 ? void 0 : (_module_params_materialProperties = _module_params.materialProperties) === null || _module_params_materialProperties === void 0 ? void 0 : _module_params_materialProperties.facingMaterial,
                            isRectangularBeam: false
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx",
                            lineNumber: 323,
                            columnNumber: 15
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.connectorInput.connectorSpacing",
                            t: t
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx",
                            lineNumber: 331,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dropdown$2d$menu$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx",
                            lineNumber: 336,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-xl font-bold",
                            children: t('matrix-sub-heading')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx",
                            lineNumber: 338,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormInput"], {
                            control: form.control,
                            name: "input.matrixInput.matrixProduct.id",
                            options: productsMatrixOptions,
                            t: t,
                            loading: isLoadingProducts,
                            requestError: errorGettingProducts,
                            errorMessage: (_form_formState_errors_input2 = form.formState.errors.input) === null || _form_formState_errors_input2 === void 0 ? void 0 : (_form_formState_errors_input_matrixInput = _form_formState_errors_input2.matrixInput) === null || _form_formState_errors_input_matrixInput === void 0 ? void 0 : (_form_formState_errors_input_matrixInput_matrixProduct = _form_formState_errors_input_matrixInput.matrixProduct) === null || _form_formState_errors_input_matrixInput_matrixProduct === void 0 ? void 0 : (_form_formState_errors_input_matrixInput_matrixProduct_id = _form_formState_errors_input_matrixInput_matrixProduct.id) === null || _form_formState_errors_input_matrixInput_matrixProduct_id === void 0 ? void 0 : _form_formState_errors_input_matrixInput_matrixProduct_id.message
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx",
                            lineNumber: 339,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        productMatrixId === 'custom' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$product$2f$custom$2d$product$2d$section$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CustomProductSection"], {}, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx",
                            lineNumber: 351,
                            columnNumber: 46
                        }, ("TURBOPACK compile-time value", void 0)),
                        selectedProductMatrix && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$product$2f$product$2d$summary$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ProductSummaryCard"], {
                            product: selectedProductMatrix,
                            isRectangularBeam: false
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx",
                            lineNumber: 353,
                            columnNumber: 15
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.matrixInput.compositeSystemThickness",
                            t: t
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx",
                            lineNumber: 358,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                            type: "submit",
                            className: "w-full sm:w-auto",
                            disabled: isPending,
                            children: [
                                isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                                    className: "animate-spin"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx",
                                    lineNumber: 368,
                                    columnNumber: 29
                                }, ("TURBOPACK compile-time value", void 0)),
                                tCommon('calculate')
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx",
                            lineNumber: 363,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx",
                    lineNumber: 262,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx",
                lineNumber: 261,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0)),
            postInterventionCalculationResult && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$anti$2d$overturning$2f$calculations$2f$anti$2d$overturning$2d$post$2d$intervention$2d$calculation$2d$result$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MasonryAntiOverturningPostInterventionCalculationResult"], {
                postInterventionCalculationResult: postInterventionCalculationResult
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx",
                lineNumber: 374,
                columnNumber: 11
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx",
        lineNumber: 260,
        columnNumber: 7
    }, ("TURBOPACK compile-time value", void 0));
};
_s(MasonryAntiOverturningCompositeReinforcementSystemCalculationForm, "+8zH8WF6MUDE7X+ivaFwUGibKEA=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$module$2d$calculation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useModuleCalculation"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$products$2f$use$2d$products$2d$by$2d$category$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useProductsByCategory"]
    ];
});
_c = MasonryAntiOverturningCompositeReinforcementSystemCalculationForm;
var _c;
__turbopack_context__.k.register(_c, "MasonryAntiOverturningCompositeReinforcementSystemCalculationForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-result-card.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "MasonryAntiOverturningPreInterventionCalculationResultCard",
    ()=>MasonryAntiOverturningPreInterventionCalculationResultCard
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
function MasonryAntiOverturningPreInterventionCalculationResultCard(param) {
    let { checkResult } = param;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.anti-overturning.pre-intervention');
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                    children: t('infillOverturningCheck.title')
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-result-card.tsx",
                    lineNumber: 25,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-result-card.tsx",
                lineNumber: 24,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                className: "space-y-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: [
                                t('checkResult.label'),
                                ":"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-result-card.tsx",
                            lineNumber: 29,
                            columnNumber: 11
                        }, this),
                        ' ',
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-base px-3 py-1', checkResult ? 'bg-green-600' : 'bg-red-600'),
                            children: checkResult ? t('checkResult.satisfied') : t('checkResult.notSatisfied')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-result-card.tsx",
                            lineNumber: 30,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-result-card.tsx",
                    lineNumber: 28,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-result-card.tsx",
                lineNumber: 27,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-result-card.tsx",
        lineNumber: 23,
        columnNumber: 5
    }, this);
}
_s(MasonryAntiOverturningPreInterventionCalculationResultCard, "h6+q2O3NJKPY5uL0BIJGLIanww8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"]
    ];
});
_c = MasonryAntiOverturningPreInterventionCalculationResultCard;
var _c;
__turbopack_context__.k.register(_c, "MasonryAntiOverturningPreInterventionCalculationResultCard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "MasonryAntiOverturningPreInterventionCalculationResultsForm",
    ()=>MasonryAntiOverturningPreInterventionCalculationResultsForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$masonry$2d$antioverturning$2d$params$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/modules/schemas/masonry-antioverturning-params.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$anti$2d$overturning$2f$calculations$2f$anti$2d$overturning$2d$pre$2d$intervention$2d$calculation$2d$result$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-result-card.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
const MasonryAntiOverturningPreInterventionCalculationResultsForm = (param)=>{
    let { preInterventionCalculationResult, onSave } = param;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.anti-overturning.pre-intervention');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const isPreInterventionCalculated = !!preInterventionCalculationResult;
    var _preInterventionCalculationResult_overturningMoment, _preInterventionCalculationResult_seismicRiskIndicator, _preInterventionCalculationResult_seismicAccelerationCorrespondingToRiskIndicator, _preInterventionCalculationResult_stabilizingMomentExternalFacing, _preInterventionCalculationResult_check;
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$masonry$2d$antioverturning$2d$params$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["masonryAntiOverturningPreInterventionCalculationResultSchema"]),
        defaultValues: {
            overturningMoment: (_preInterventionCalculationResult_overturningMoment = preInterventionCalculationResult === null || preInterventionCalculationResult === void 0 ? void 0 : preInterventionCalculationResult.overturningMoment) !== null && _preInterventionCalculationResult_overturningMoment !== void 0 ? _preInterventionCalculationResult_overturningMoment : 0,
            seismicRiskIndicator: (_preInterventionCalculationResult_seismicRiskIndicator = preInterventionCalculationResult === null || preInterventionCalculationResult === void 0 ? void 0 : preInterventionCalculationResult.seismicRiskIndicator) !== null && _preInterventionCalculationResult_seismicRiskIndicator !== void 0 ? _preInterventionCalculationResult_seismicRiskIndicator : 0,
            seismicAccelerationCorrespondingToRiskIndicator: (_preInterventionCalculationResult_seismicAccelerationCorrespondingToRiskIndicator = preInterventionCalculationResult === null || preInterventionCalculationResult === void 0 ? void 0 : preInterventionCalculationResult.seismicAccelerationCorrespondingToRiskIndicator) !== null && _preInterventionCalculationResult_seismicAccelerationCorrespondingToRiskIndicator !== void 0 ? _preInterventionCalculationResult_seismicAccelerationCorrespondingToRiskIndicator : 0,
            stabilizingMomentExternalFacing: (_preInterventionCalculationResult_stabilizingMomentExternalFacing = preInterventionCalculationResult === null || preInterventionCalculationResult === void 0 ? void 0 : preInterventionCalculationResult.stabilizingMomentExternalFacing) !== null && _preInterventionCalculationResult_stabilizingMomentExternalFacing !== void 0 ? _preInterventionCalculationResult_stabilizingMomentExternalFacing : 0,
            check: (_preInterventionCalculationResult_check = preInterventionCalculationResult === null || preInterventionCalculationResult === void 0 ? void 0 : preInterventionCalculationResult.check) !== null && _preInterventionCalculationResult_check !== void 0 ? _preInterventionCalculationResult_check : false
        }
    });
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Form"], {
        ...form,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            className: "space-y-4 rounded-md border p-4 ".concat(!isPreInterventionCalculated ? 'border-red-500' : ''),
            onSubmit: form.handleSubmit(onSave),
            children: [
                !isPreInterventionCalculated && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mb-4 p-2 rounded bg-red-100 text-red-700 border border-red-300",
                    children: t('notCalculated')
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-form.tsx",
                    lineNumber: 57,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "overturningMoment",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-form.tsx",
                    lineNumber: 61,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "stabilizingMomentExternalFacing",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-form.tsx",
                    lineNumber: 67,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$anti$2d$overturning$2f$calculations$2f$anti$2d$overturning$2d$pre$2d$intervention$2d$calculation$2d$result$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MasonryAntiOverturningPreInterventionCalculationResultCard"], {
                    checkResult: (preInterventionCalculationResult === null || preInterventionCalculationResult === void 0 ? void 0 : preInterventionCalculationResult.check) || false
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-form.tsx",
                    lineNumber: 73,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "font-medium",
                            children: [
                                t('seismicRiskIndicator.label'),
                                ":"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-form.tsx",
                            lineNumber: 77,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        ' ',
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            children: [
                                Math.round(Number(preInterventionCalculationResult === null || preInterventionCalculationResult === void 0 ? void 0 : preInterventionCalculationResult.seismicRiskIndicator) * 1000) / 1000,
                                ' ',
                                "%"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-form.tsx",
                            lineNumber: 80,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            "data-slot": "form-description",
                            id: 'stabilizingMomentExternalFacingId',
                            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-muted-foreground text-sm'),
                            children: t('seismicRiskIndicator.description')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-form.tsx",
                            lineNumber: 87,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-form.tsx",
                    lineNumber: 76,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "seismicAccelerationCorrespondingToRiskIndicator",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-form.tsx",
                    lineNumber: 95,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    type: "submit",
                    className: "w-full sm:w-auto",
                    disabled: !isPreInterventionCalculated,
                    onClick: form.handleSubmit(onSave),
                    children: tCommon('next')
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-form.tsx",
                    lineNumber: 101,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-form.tsx",
            lineNumber: 52,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-form.tsx",
        lineNumber: 51,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(MasonryAntiOverturningPreInterventionCalculationResultsForm, "sa0cjE9pnKacw/If9MW4+mZEdjs=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"]
    ];
});
_c = MasonryAntiOverturningPreInterventionCalculationResultsForm;
var _c;
__turbopack_context__.k.register(_c, "MasonryAntiOverturningPreInterventionCalculationResultsForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-seismic-demand-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "MasonryAntiOverturningSeismicDemandCalculationResultsForm",
    ()=>MasonryAntiOverturningSeismicDemandCalculationResultsForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$masonry$2d$antioverturning$2d$params$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/modules/schemas/masonry-antioverturning-params.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
const MasonryAntiOverturningSeismicDemandCalculationResultsForm = (param)=>{
    let { seismicDemandCalculationResult, onSave } = param;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.anti-overturning.seismic-demand');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const isSeismicDemandCalculated = !!seismicDemandCalculationResult;
    var _seismicDemandCalculationResult_overturningSeismicForce, _seismicDemandCalculationResult_masonryAndPlasterWeight, _seismicDemandCalculationResult_maximumAcceleration;
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$masonry$2d$antioverturning$2d$params$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["masonryAntiOverturningSeismicDemandCalculationResultSchema"]),
        defaultValues: {
            overturningSeismicForce: (_seismicDemandCalculationResult_overturningSeismicForce = seismicDemandCalculationResult === null || seismicDemandCalculationResult === void 0 ? void 0 : seismicDemandCalculationResult.overturningSeismicForce) !== null && _seismicDemandCalculationResult_overturningSeismicForce !== void 0 ? _seismicDemandCalculationResult_overturningSeismicForce : 0,
            masonryAndPlasterWeight: (_seismicDemandCalculationResult_masonryAndPlasterWeight = seismicDemandCalculationResult === null || seismicDemandCalculationResult === void 0 ? void 0 : seismicDemandCalculationResult.masonryAndPlasterWeight) !== null && _seismicDemandCalculationResult_masonryAndPlasterWeight !== void 0 ? _seismicDemandCalculationResult_masonryAndPlasterWeight : 0,
            maximumAcceleration: (_seismicDemandCalculationResult_maximumAcceleration = seismicDemandCalculationResult === null || seismicDemandCalculationResult === void 0 ? void 0 : seismicDemandCalculationResult.maximumAcceleration) !== null && _seismicDemandCalculationResult_maximumAcceleration !== void 0 ? _seismicDemandCalculationResult_maximumAcceleration : 0
        }
    });
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Form"], {
        ...form,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            className: "space-y-4 rounded-md border p-4 ".concat(!isSeismicDemandCalculated ? 'border-red-500' : ''),
            onSubmit: form.handleSubmit(onSave),
            children: [
                !isSeismicDemandCalculated && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mb-4 p-2 rounded bg-red-100 text-red-700 border border-red-300",
                    children: t('notCalculated')
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-seismic-demand-form.tsx",
                    lineNumber: 48,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "maximumAcceleration",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-seismic-demand-form.tsx",
                    lineNumber: 52,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "masonryAndPlasterWeight",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-seismic-demand-form.tsx",
                    lineNumber: 58,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "overturningSeismicForce",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-seismic-demand-form.tsx",
                    lineNumber: 64,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    type: "submit",
                    className: "w-full sm:w-auto",
                    disabled: !isSeismicDemandCalculated,
                    onClick: form.handleSubmit(onSave),
                    children: tCommon('next')
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-seismic-demand-form.tsx",
                    lineNumber: 70,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-seismic-demand-form.tsx",
            lineNumber: 43,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-seismic-demand-form.tsx",
        lineNumber: 42,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(MasonryAntiOverturningSeismicDemandCalculationResultsForm, "sa0cjE9pnKacw/If9MW4+mZEdjs=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"]
    ];
});
_c = MasonryAntiOverturningSeismicDemandCalculationResultsForm;
var _c;
__turbopack_context__.k.register(_c, "MasonryAntiOverturningSeismicDemandCalculationResultsForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-building-characteristics-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "MasonryAntiOverturningBuildingCharacteristicsForm",
    ()=>MasonryAntiOverturningBuildingCharacteristicsForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/select-form-fixed-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-save-module-params-mutation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$antioverturning$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/masonry/antioverturning-form.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const MasonryAntiOverturningBuildingCharacteristicsForm = (param)=>{
    let { session, projectId, moduleId, defaultValues, params, setParams, onSave } = param;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.anti-overturning.building-characteristics');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.save-project-params.messages');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const buildingFundamentalPeriodCalculation = (buildingType, totalBuildingHeight)=>{
        return buildingType === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BUILDING_TYPE"].REINFORCED_CONCRETE ? 0.075 * totalBuildingHeight ** 0.75 : 0.05 * totalBuildingHeight ** 0.75;
    };
    var _defaultValues_buildingType, _defaultValues_totalBuildingHeight;
    const defaultBuildingFundamentalPeriod = buildingFundamentalPeriodCalculation((_defaultValues_buildingType = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.buildingType) !== null && _defaultValues_buildingType !== void 0 ? _defaultValues_buildingType : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BUILDING_TYPE"].MASONRY, (_defaultValues_totalBuildingHeight = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.totalBuildingHeight) !== null && _defaultValues_totalBuildingHeight !== void 0 ? _defaultValues_totalBuildingHeight : 1);
    var _defaultValues_buildingType1, _defaultValues_totalBuildingHeight1;
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$antioverturning$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["masonryAntiOverturningBuildingCharacteristicsSchema"]),
        defaultValues: {
            buildingType: (_defaultValues_buildingType1 = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.buildingType) !== null && _defaultValues_buildingType1 !== void 0 ? _defaultValues_buildingType1 : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BUILDING_TYPE"].REINFORCED_CONCRETE,
            totalBuildingHeight: (_defaultValues_totalBuildingHeight1 = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.totalBuildingHeight) !== null && _defaultValues_totalBuildingHeight1 !== void 0 ? _defaultValues_totalBuildingHeight1 : 1,
            buildingFundamentalPeriod: defaultBuildingFundamentalPeriod,
            parameterA: defaultBuildingFundamentalPeriod < 0.5 ? 0.8 : 0.3,
            parameterB: defaultBuildingFundamentalPeriod < 0.5 ? 1.4 : defaultBuildingFundamentalPeriod > 1 ? 1 : 1.2,
            parameterAp: defaultBuildingFundamentalPeriod < 0.5 ? 5 : defaultBuildingFundamentalPeriod > 1 ? 2.5 : 4
        }
    });
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"])(session.accessToken, {
        onSuccess: {
            "MasonryAntiOverturningBuildingCharacteristicsForm.useSaveModuleParamsMutation": async ()=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(tAction('edit.success'));
                await queryClient.invalidateQueries({
                    queryKey: [
                        'modules',
                        {
                            projectId,
                            moduleId
                        }
                    ]
                });
                onSave();
            }
        }["MasonryAntiOverturningBuildingCharacteristicsForm.useSaveModuleParamsMutation"],
        onError: {
            "MasonryAntiOverturningBuildingCharacteristicsForm.useSaveModuleParamsMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(tAction('edit.failure', {
                    error: error.message
                }));
            }
        }["MasonryAntiOverturningBuildingCharacteristicsForm.useSaveModuleParamsMutation"]
    });
    const handleFormSubmit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "MasonryAntiOverturningBuildingCharacteristicsForm.useCallback[handleFormSubmit]": (body)=>{
            // construct body based on global schema:
            const masonryAntiOverturningParams = {
                ...params,
                buildingCharacteristics: body
            };
            mutate({
                projectId,
                moduleId,
                body: masonryAntiOverturningParams
            });
        }
    }["MasonryAntiOverturningBuildingCharacteristicsForm.useCallback[handleFormSubmit]"], [
        mutate,
        projectId,
        moduleId,
        params
    ]);
    const buildingType = form.watch('buildingType');
    const totalBuildingHeight = form.watch('totalBuildingHeight');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MasonryAntiOverturningBuildingCharacteristicsForm.useEffect": ()=>{
            const buildingFundamentalPeriod = buildingType === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BUILDING_TYPE"].REINFORCED_CONCRETE ? 0.075 * totalBuildingHeight ** 0.75 : 0.05 * totalBuildingHeight ** 0.75;
            form.setValue('parameterA', buildingFundamentalPeriod < 0.5 ? 0.8 : 0.3);
            form.setValue('parameterB', buildingFundamentalPeriod < 0.5 ? 1.4 : buildingFundamentalPeriod > 1 ? 1 : 1.2);
            form.setValue('parameterAp', buildingFundamentalPeriod < 0.5 ? 5 : buildingFundamentalPeriod > 1 ? 2.5 : 4);
            form.setValue('buildingFundamentalPeriod', buildingFundamentalPeriod);
        }
    }["MasonryAntiOverturningBuildingCharacteristicsForm.useEffect"], [
        buildingType,
        totalBuildingHeight,
        form
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MasonryAntiOverturningBuildingCharacteristicsForm.useEffect": ()=>{
            const subscription = form.watch({
                "MasonryAntiOverturningBuildingCharacteristicsForm.useEffect.subscription": (values)=>{
                    setParams({
                        "MasonryAntiOverturningBuildingCharacteristicsForm.useEffect.subscription": (prev)=>({
                                ...prev,
                                buildingCharacteristics: values
                            })
                    }["MasonryAntiOverturningBuildingCharacteristicsForm.useEffect.subscription"]);
                }
            }["MasonryAntiOverturningBuildingCharacteristicsForm.useEffect.subscription"]);
            return ({
                "MasonryAntiOverturningBuildingCharacteristicsForm.useEffect": ()=>subscription.unsubscribe()
            })["MasonryAntiOverturningBuildingCharacteristicsForm.useEffect"];
        }
    }["MasonryAntiOverturningBuildingCharacteristicsForm.useEffect"], [
        form,
        setParams
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Form"], {
        ...form,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            className: "space-y-4 rounded-md border p-4",
            onSubmit: form.handleSubmit(handleFormSubmit),
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "buildingType",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_BUILDING_TYPE"],
                    optionLabelFn: (p)=>t("building.".concat(p)),
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-building-characteristics-form.tsx",
                    lineNumber: 163,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "totalBuildingHeight",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-building-characteristics-form.tsx",
                    lineNumber: 171,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "buildingFundamentalPeriod",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-building-characteristics-form.tsx",
                    lineNumber: 176,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "parameterA",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-building-characteristics-form.tsx",
                    lineNumber: 182,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "parameterB",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-building-characteristics-form.tsx",
                    lineNumber: 188,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "parameterAp",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-building-characteristics-form.tsx",
                    lineNumber: 194,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    type: "submit",
                    className: "w-full sm:w-auto",
                    disabled: isPending,
                    onClick: form.handleSubmit(handleFormSubmit),
                    children: [
                        isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                            className: "animate-spin"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-building-characteristics-form.tsx",
                            lineNumber: 206,
                            columnNumber: 25
                        }, ("TURBOPACK compile-time value", void 0)),
                        tCommon('save')
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-building-characteristics-form.tsx",
                    lineNumber: 200,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-building-characteristics-form.tsx",
            lineNumber: 159,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-building-characteristics-form.tsx",
        lineNumber: 158,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(MasonryAntiOverturningBuildingCharacteristicsForm, "q8qy4WSiQgq59By9bYU1Gd3awn8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"]
    ];
});
_c = MasonryAntiOverturningBuildingCharacteristicsForm;
var _c;
__turbopack_context__.k.register(_c, "MasonryAntiOverturningBuildingCharacteristicsForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-infill-geometry-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "MasonryAntiOverturningInfillGeometryForm",
    ()=>MasonryAntiOverturningInfillGeometryForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-save-module-params-mutation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$antioverturning$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/masonry/antioverturning-form.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
const MasonryAntiOverturningInfillGeometryForm = (param)=>{
    let { session, projectId, moduleId, defaultValues, params, onSave } = param;
    var _params_buildingCharacteristics, _params_materialProperties, _params_materialProperties1, _params_materialProperties2, _params_materialProperties3;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.anti-overturning.infill-geometry');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.save-project-params.messages');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    var _defaultValues_externalFacingThickness, _defaultValues_internalFacingThickness, _defaultValues_singleSidePlasterThickness, _defaultValues_netPanelHeight, _defaultValues_panelHeightFromGroundLevel, _defaultValues_panelCentroidFromGroundLevel, _defaultValues_fundamentalPeriodPanel;
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$antioverturning$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["masonryAntiOverturningInfillGeometrySchema"]),
        defaultValues: {
            panelWidth: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PANEL_WIDTH_DEFAULT"],
            externalFacingThickness: (_defaultValues_externalFacingThickness = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.externalFacingThickness) !== null && _defaultValues_externalFacingThickness !== void 0 ? _defaultValues_externalFacingThickness : 0,
            internalFacingThickness: (_defaultValues_internalFacingThickness = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.internalFacingThickness) !== null && _defaultValues_internalFacingThickness !== void 0 ? _defaultValues_internalFacingThickness : 0,
            singleSidePlasterThickness: (_defaultValues_singleSidePlasterThickness = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.singleSidePlasterThickness) !== null && _defaultValues_singleSidePlasterThickness !== void 0 ? _defaultValues_singleSidePlasterThickness : 0,
            netPanelHeight: (_defaultValues_netPanelHeight = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.netPanelHeight) !== null && _defaultValues_netPanelHeight !== void 0 ? _defaultValues_netPanelHeight : 0,
            panelHeightFromGroundLevel: (_defaultValues_panelHeightFromGroundLevel = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.panelHeightFromGroundLevel) !== null && _defaultValues_panelHeightFromGroundLevel !== void 0 ? _defaultValues_panelHeightFromGroundLevel : 0,
            panelCentroidFromGroundLevel: (_defaultValues_panelCentroidFromGroundLevel = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.panelCentroidFromGroundLevel) !== null && _defaultValues_panelCentroidFromGroundLevel !== void 0 ? _defaultValues_panelCentroidFromGroundLevel : 0,
            fundamentalPeriodPanel: (_defaultValues_fundamentalPeriodPanel = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.fundamentalPeriodPanel) !== null && _defaultValues_fundamentalPeriodPanel !== void 0 ? _defaultValues_fundamentalPeriodPanel : 0
        }
    });
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"])(session.accessToken, {
        onSuccess: {
            "MasonryAntiOverturningInfillGeometryForm.useSaveModuleParamsMutation": async ()=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(tAction('edit.success'));
                await queryClient.invalidateQueries({
                    queryKey: [
                        'modules',
                        {
                            projectId,
                            moduleId
                        }
                    ]
                });
                onSave();
            }
        }["MasonryAntiOverturningInfillGeometryForm.useSaveModuleParamsMutation"],
        onError: {
            "MasonryAntiOverturningInfillGeometryForm.useSaveModuleParamsMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(tAction('edit.failure', {
                    error: error.message
                }));
            }
        }["MasonryAntiOverturningInfillGeometryForm.useSaveModuleParamsMutation"]
    });
    var _params_buildingCharacteristics_totalBuildingHeight;
    const totalBuildingHeight = (_params_buildingCharacteristics_totalBuildingHeight = params === null || params === void 0 ? void 0 : (_params_buildingCharacteristics = params.buildingCharacteristics) === null || _params_buildingCharacteristics === void 0 ? void 0 : _params_buildingCharacteristics.totalBuildingHeight) !== null && _params_buildingCharacteristics_totalBuildingHeight !== void 0 ? _params_buildingCharacteristics_totalBuildingHeight : 1;
    var _params_materialProperties_masonrySpecificWeightExternalFacing;
    const masonrySpecificWeightExternalFacing = (_params_materialProperties_masonrySpecificWeightExternalFacing = params === null || params === void 0 ? void 0 : (_params_materialProperties = params.materialProperties) === null || _params_materialProperties === void 0 ? void 0 : _params_materialProperties.masonrySpecificWeightExternalFacing) !== null && _params_materialProperties_masonrySpecificWeightExternalFacing !== void 0 ? _params_materialProperties_masonrySpecificWeightExternalFacing : 0;
    var _params_materialProperties_masonrySpecificWeightInternalFacing;
    const masonrySpecificWeightInternalFacing = (_params_materialProperties_masonrySpecificWeightInternalFacing = params === null || params === void 0 ? void 0 : (_params_materialProperties1 = params.materialProperties) === null || _params_materialProperties1 === void 0 ? void 0 : _params_materialProperties1.masonrySpecificWeightInternalFacing) !== null && _params_materialProperties_masonrySpecificWeightInternalFacing !== void 0 ? _params_materialProperties_masonrySpecificWeightInternalFacing : 0;
    var _params_materialProperties_plasterSpecificWeight;
    const plasterSpecificWeight = (_params_materialProperties_plasterSpecificWeight = params === null || params === void 0 ? void 0 : (_params_materialProperties2 = params.materialProperties) === null || _params_materialProperties2 === void 0 ? void 0 : _params_materialProperties2.plasterSpecificWeight) !== null && _params_materialProperties_plasterSpecificWeight !== void 0 ? _params_materialProperties_plasterSpecificWeight : 0;
    var _params_materialProperties_infillWallElasticModulus;
    const infillWallElasticModulus = (_params_materialProperties_infillWallElasticModulus = params === null || params === void 0 ? void 0 : (_params_materialProperties3 = params.materialProperties) === null || _params_materialProperties3 === void 0 ? void 0 : _params_materialProperties3.infillWallElasticModulus) !== null && _params_materialProperties_infillWallElasticModulus !== void 0 ? _params_materialProperties_infillWallElasticModulus : 0;
    const netPanelHeight = form.watch('netPanelHeight');
    var _form_watch;
    const panelWidth = (_form_watch = form.watch('panelWidth')) !== null && _form_watch !== void 0 ? _form_watch : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PANEL_WIDTH_DEFAULT"];
    const internalFacingThickness = form.watch('internalFacingThickness');
    const externalFacingThickness = form.watch('externalFacingThickness');
    const singleSidePlasterThickness = form.watch('singleSidePlasterThickness');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MasonryAntiOverturningInfillGeometryForm.useEffect": ()=>{
            const panelHeightFromGroundLevel = totalBuildingHeight * 1000;
            form.setValue('panelHeightFromGroundLevel', panelHeightFromGroundLevel);
            form.setValue('panelCentroidFromGroundLevel', panelHeightFromGroundLevel - netPanelHeight / 2);
            // fundamental panel period calculation:
            // D44 is netPanelHeight
            // D40 is panelWidth
            // D42 is internalFacingThickness
            // D36 is materialProperties.masonrySpecificWeightInternalFacing
            // D41 is externalFacingThickness
            // D35 is materialProperties.masonrySpecificWeightExternalFacing
            // D43 is singleSidePlasterThickness
            // D37 is materialProperties.plasterSpecificWeight
            // D33 is materialProperties.infillWallElasticModulus
            // FORMULA : =(2*3.14*D44^2/3.5156)*SQRT((D40*((D42*D36)+(D41*D35)+(2*D43*D37))*1000/1000000000)/(D33*9.81*1000*D40*(D42+D41+2*D43)^3/12))
            const subInnerPartOne = panelWidth * (internalFacingThickness * masonrySpecificWeightInternalFacing + externalFacingThickness * masonrySpecificWeightExternalFacing + 2 * singleSidePlasterThickness * plasterSpecificWeight) * 1000 / 1000000000;
            const subInnerPartTwo = infillWallElasticModulus * 9.81 * 1000 * panelWidth * (internalFacingThickness + externalFacingThickness + 2 * singleSidePlasterThickness) ** 3 / 12;
            const secondPart = Math.sqrt(subInnerPartOne / subInnerPartTwo);
            const fundamentalPeriodPanel = 2 * 3.14 * netPanelHeight ** 2 / 3.5156 * secondPart;
            form.setValue('fundamentalPeriodPanel', fundamentalPeriodPanel);
        }
    }["MasonryAntiOverturningInfillGeometryForm.useEffect"], [
        totalBuildingHeight,
        netPanelHeight,
        panelWidth,
        internalFacingThickness,
        externalFacingThickness,
        singleSidePlasterThickness,
        infillWallElasticModulus,
        form,
        masonrySpecificWeightExternalFacing,
        masonrySpecificWeightInternalFacing,
        plasterSpecificWeight
    ]);
    const handleFormSubmit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "MasonryAntiOverturningInfillGeometryForm.useCallback[handleFormSubmit]": (body)=>{
            // construct body based on global schema:
            const masonryAntiOverturningParams = {
                ...params,
                infillGeometry: body
            };
            mutate({
                projectId,
                moduleId,
                body: masonryAntiOverturningParams
            });
        }
    }["MasonryAntiOverturningInfillGeometryForm.useCallback[handleFormSubmit]"], [
        mutate,
        projectId,
        moduleId,
        params
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Form"], {
        ...form,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            className: "space-y-4 rounded-md border p-4",
            onSubmit: form.handleSubmit(handleFormSubmit),
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "panelWidth",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-infill-geometry-form.tsx",
                    lineNumber: 170,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "externalFacingThickness",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-infill-geometry-form.tsx",
                    lineNumber: 176,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "internalFacingThickness",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-infill-geometry-form.tsx",
                    lineNumber: 181,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "singleSidePlasterThickness",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-infill-geometry-form.tsx",
                    lineNumber: 186,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "netPanelHeight",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-infill-geometry-form.tsx",
                    lineNumber: 191,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "panelHeightFromGroundLevel",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-infill-geometry-form.tsx",
                    lineNumber: 192,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "panelCentroidFromGroundLevel",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-infill-geometry-form.tsx",
                    lineNumber: 198,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "fundamentalPeriodPanel",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-infill-geometry-form.tsx",
                    lineNumber: 204,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    type: "submit",
                    className: "w-full sm:w-auto",
                    disabled: isPending,
                    onClick: form.handleSubmit(handleFormSubmit),
                    children: [
                        isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                            className: "animate-spin"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-infill-geometry-form.tsx",
                            lineNumber: 216,
                            columnNumber: 25
                        }, ("TURBOPACK compile-time value", void 0)),
                        tCommon('save')
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-infill-geometry-form.tsx",
                    lineNumber: 210,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-infill-geometry-form.tsx",
            lineNumber: 166,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-infill-geometry-form.tsx",
        lineNumber: 165,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(MasonryAntiOverturningInfillGeometryForm, "DwT9Ft7cYVTNSvwpcnZpHVbBj1M=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"]
    ];
});
_c = MasonryAntiOverturningInfillGeometryForm;
var _c;
__turbopack_context__.k.register(_c, "MasonryAntiOverturningInfillGeometryForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-material-properties-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "MasonryAntiOverturningMaterialPropertiesForm",
    ()=>MasonryAntiOverturningMaterialPropertiesForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/select-form-fixed-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-save-module-params-mutation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$antioverturning$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/masonry/antioverturning-form.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const MasonryAntiOverturningMaterialPropertiesForm = (param)=>{
    let { session, projectId, moduleId, defaultValues, params, setParams, onSave } = param;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.anti-overturning.material-properties');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.save-project-params.messages');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    var _defaultValues_infillWallTypology, _defaultValues_facingMaterial, _defaultValues_knowledgeLevel, _defaultValues_confidenceFactor, _defaultValues_executionClass, _defaultValues_loadResistingCategory, _defaultValues_masonryStrengthSafetyFactor, _defaultValues_characteristicCompressiveStrength, _defaultValues_designCompressiveStrength, _defaultValues_infillWallElasticModulus, _defaultValues_ultimateMasonryStrain, _defaultValues_masonrySpecificWeightExternalFacing, _defaultValues_masonrySpecificWeightInternalFacing, _defaultValues_plasterSpecificWeight;
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$antioverturning$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["masonryAntiOverturningMaterialPropertiesSchema"]),
        defaultValues: {
            infillWallTypology: (_defaultValues_infillWallTypology = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.infillWallTypology) !== null && _defaultValues_infillWallTypology !== void 0 ? _defaultValues_infillWallTypology : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INFILL_WALL_TOPOLOGY"].SINGLE,
            facingMaterial: (_defaultValues_facingMaterial = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.facingMaterial) !== null && _defaultValues_facingMaterial !== void 0 ? _defaultValues_facingMaterial : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FACING_MATERIAL"].BRICK,
            knowledgeLevel: (_defaultValues_knowledgeLevel = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.knowledgeLevel) !== null && _defaultValues_knowledgeLevel !== void 0 ? _defaultValues_knowledgeLevel : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleMaterialKnowledgeLevel"].LC1,
            confidenceFactor: (_defaultValues_confidenceFactor = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.confidenceFactor) !== null && _defaultValues_confidenceFactor !== void 0 ? _defaultValues_confidenceFactor : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleMaterialKnowledgeLevelValues"].LC1,
            executionClass: (_defaultValues_executionClass = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.executionClass) !== null && _defaultValues_executionClass !== void 0 ? _defaultValues_executionClass : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["executionClass"].ONE,
            loadResistingCategory: (_defaultValues_loadResistingCategory = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.loadResistingCategory) !== null && _defaultValues_loadResistingCategory !== void 0 ? _defaultValues_loadResistingCategory : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["loadResistingCategory"].MASONRY_CAT_I_AND_MORTAR_WITH_GUARANTEED_PERFORMANCE,
            masonryStrengthSafetyFactor: (_defaultValues_masonryStrengthSafetyFactor = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.masonryStrengthSafetyFactor) !== null && _defaultValues_masonryStrengthSafetyFactor !== void 0 ? _defaultValues_masonryStrengthSafetyFactor : 0,
            characteristicCompressiveStrength: (_defaultValues_characteristicCompressiveStrength = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.characteristicCompressiveStrength) !== null && _defaultValues_characteristicCompressiveStrength !== void 0 ? _defaultValues_characteristicCompressiveStrength : 0,
            designCompressiveStrength: (_defaultValues_designCompressiveStrength = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.designCompressiveStrength) !== null && _defaultValues_designCompressiveStrength !== void 0 ? _defaultValues_designCompressiveStrength : 0,
            infillWallElasticModulus: (_defaultValues_infillWallElasticModulus = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.infillWallElasticModulus) !== null && _defaultValues_infillWallElasticModulus !== void 0 ? _defaultValues_infillWallElasticModulus : 0,
            ultimateMasonryStrain: (_defaultValues_ultimateMasonryStrain = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.ultimateMasonryStrain) !== null && _defaultValues_ultimateMasonryStrain !== void 0 ? _defaultValues_ultimateMasonryStrain : 0.0035,
            masonrySpecificWeightExternalFacing: (_defaultValues_masonrySpecificWeightExternalFacing = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.masonrySpecificWeightExternalFacing) !== null && _defaultValues_masonrySpecificWeightExternalFacing !== void 0 ? _defaultValues_masonrySpecificWeightExternalFacing : 0,
            masonrySpecificWeightInternalFacing: (defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.infillWallTypology) === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INFILL_WALL_TOPOLOGY"].SINGLE ? 0 : (_defaultValues_masonrySpecificWeightInternalFacing = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.masonrySpecificWeightInternalFacing) !== null && _defaultValues_masonrySpecificWeightInternalFacing !== void 0 ? _defaultValues_masonrySpecificWeightInternalFacing : 0,
            plasterSpecificWeight: (_defaultValues_plasterSpecificWeight = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.plasterSpecificWeight) !== null && _defaultValues_plasterSpecificWeight !== void 0 ? _defaultValues_plasterSpecificWeight : 0
        }
    });
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"])(session.accessToken, {
        onSuccess: {
            "MasonryAntiOverturningMaterialPropertiesForm.useSaveModuleParamsMutation": async ()=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(tAction('edit.success'));
                await queryClient.invalidateQueries({
                    queryKey: [
                        'modules',
                        {
                            projectId,
                            moduleId
                        }
                    ]
                });
                onSave();
            }
        }["MasonryAntiOverturningMaterialPropertiesForm.useSaveModuleParamsMutation"],
        onError: {
            "MasonryAntiOverturningMaterialPropertiesForm.useSaveModuleParamsMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(tAction('edit.failure', {
                    error: error.message
                }));
            }
        }["MasonryAntiOverturningMaterialPropertiesForm.useSaveModuleParamsMutation"]
    });
    const infillWallTypology = form.watch('infillWallTypology');
    const knowledgeMaterialLevel = form.watch('knowledgeLevel');
    const executionClassFormValue = form.watch('executionClass');
    const loadResistingCategoryFormValue = form.watch('loadResistingCategory');
    const characteristicCompressiveStrength = form.watch('characteristicCompressiveStrength');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MasonryAntiOverturningMaterialPropertiesForm.useEffect": ()=>{
            const confidenceFactor = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleMaterialKnowledgeLevelValues"][knowledgeMaterialLevel];
            form.setValue('confidenceFactor', confidenceFactor);
            const masonryStrengthSafetyFactor = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["masonryStrengthSafetyFactorMapping"][loadResistingCategoryFormValue][executionClassFormValue];
            form.setValue('masonryStrengthSafetyFactor', masonryStrengthSafetyFactor);
            form.setValue('designCompressiveStrength', characteristicCompressiveStrength / masonryStrengthSafetyFactor / confidenceFactor);
            if (infillWallTypology === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INFILL_WALL_TOPOLOGY"].SINGLE) {
                form.setValue('masonrySpecificWeightInternalFacing', 0);
            }
        }
    }["MasonryAntiOverturningMaterialPropertiesForm.useEffect"], [
        knowledgeMaterialLevel,
        executionClassFormValue,
        loadResistingCategoryFormValue,
        infillWallTypology,
        characteristicCompressiveStrength,
        form
    ]);
    const handleFormSubmit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "MasonryAntiOverturningMaterialPropertiesForm.useCallback[handleFormSubmit]": (body)=>{
            // construct body based on global schema:
            const masonryAntiOverturningParams = {
                ...params,
                materialProperties: body
            };
            mutate({
                projectId,
                moduleId,
                body: masonryAntiOverturningParams
            });
        }
    }["MasonryAntiOverturningMaterialPropertiesForm.useCallback[handleFormSubmit]"], [
        mutate,
        projectId,
        moduleId,
        params
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MasonryAntiOverturningMaterialPropertiesForm.useEffect": ()=>{
            const subscription = form.watch({
                "MasonryAntiOverturningMaterialPropertiesForm.useEffect.subscription": (values)=>{
                    setParams({
                        "MasonryAntiOverturningMaterialPropertiesForm.useEffect.subscription": (prev)=>({
                                ...prev,
                                materialProperties: values
                            })
                    }["MasonryAntiOverturningMaterialPropertiesForm.useEffect.subscription"]);
                }
            }["MasonryAntiOverturningMaterialPropertiesForm.useEffect.subscription"]);
            return ({
                "MasonryAntiOverturningMaterialPropertiesForm.useEffect": ()=>subscription.unsubscribe()
            })["MasonryAntiOverturningMaterialPropertiesForm.useEffect"];
        }
    }["MasonryAntiOverturningMaterialPropertiesForm.useEffect"], [
        form,
        setParams
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Form"], {
        ...form,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            className: "space-y-4 rounded-md border p-4",
            onSubmit: form.handleSubmit(handleFormSubmit),
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "infillWallTypology",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_INFILL_WALL_TOPOLOGY"],
                    optionLabelFn: (p)=>t("infillWall.".concat(p)),
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-material-properties-form.tsx",
                    lineNumber: 175,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "facingMaterial",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_FACING_MATERIAL"],
                    t: (msg)=>t("".concat(infillWallTypology, ".").concat(msg)),
                    optionLabelFn: (p)=>t("facingMaterial.".concat(p))
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-material-properties-form.tsx",
                    lineNumber: 182,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "knowledgeLevel",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_MATERIAL_KNOWLEDGE_LEVEL"],
                    optionLabelFn: (p)=>t("materialKnowledge.".concat(p)),
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-material-properties-form.tsx",
                    lineNumber: 189,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "confidenceFactor",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-material-properties-form.tsx",
                    lineNumber: 196,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "executionClass",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_EXECUTION_CLASS"],
                    optionLabelFn: (p)=>t("executionClass.".concat(p)),
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-material-properties-form.tsx",
                    lineNumber: 202,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "loadResistingCategory",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_LOAD_RESISTING_CATEGORY"],
                    optionLabelFn: (p)=>t("loadResisting.".concat(p)),
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-material-properties-form.tsx",
                    lineNumber: 209,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "masonryStrengthSafetyFactor",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-material-properties-form.tsx",
                    lineNumber: 216,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "characteristicCompressiveStrength",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-material-properties-form.tsx",
                    lineNumber: 222,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "designCompressiveStrength",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-material-properties-form.tsx",
                    lineNumber: 227,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "infillWallElasticModulus",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-material-properties-form.tsx",
                    lineNumber: 233,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "ultimateMasonryStrain",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-material-properties-form.tsx",
                    lineNumber: 238,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "masonrySpecificWeightExternalFacing",
                    t: (msg)=>t("".concat(infillWallTypology, ".").concat(msg))
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-material-properties-form.tsx",
                    lineNumber: 244,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "masonrySpecificWeightInternalFacing",
                    t: (msg)=>t("".concat(infillWallTypology, ".").concat(msg)),
                    disabled: infillWallTypology === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INFILL_WALL_TOPOLOGY"].SINGLE
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-material-properties-form.tsx",
                    lineNumber: 249,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "plasterSpecificWeight",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-material-properties-form.tsx",
                    lineNumber: 255,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    type: "submit",
                    className: "w-full sm:w-auto",
                    disabled: isPending,
                    onClick: form.handleSubmit(handleFormSubmit),
                    children: [
                        isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                            className: "animate-spin"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-material-properties-form.tsx",
                            lineNumber: 266,
                            columnNumber: 25
                        }, ("TURBOPACK compile-time value", void 0)),
                        tCommon('save')
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-material-properties-form.tsx",
                    lineNumber: 260,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-material-properties-form.tsx",
            lineNumber: 171,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-material-properties-form.tsx",
        lineNumber: 170,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(MasonryAntiOverturningMaterialPropertiesForm, "sW1iiW6iWPaLgYPE2hjeEl6hDOs=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"]
    ];
});
_c = MasonryAntiOverturningMaterialPropertiesForm;
var _c;
__turbopack_context__.k.register(_c, "MasonryAntiOverturningMaterialPropertiesForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-site-characteristics-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "MasonryAntiOverturningSiteCharacteristicsForm",
    ()=>MasonryAntiOverturningSiteCharacteristicsForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/select-form-fixed-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-save-module-params-mutation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$antioverturning$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/masonry/antioverturning-form.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const MasonryAntiOverturningSiteCharacteristicsForm = (param)=>{
    let { session, projectId, moduleId, defaultValues, params, setParams, onSave } = param;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.anti-overturning.site-characteristics');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.save-project-params.messages');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    var _defaultValues_seismicAccelerationAtSlv, _defaultValues_amplificationFactorAtSlv, _defaultValues_subsoilCategory, _defaultValues_subsoilCategory1, _defaultValues_topographicCategory, _defaultValues_topographicCategory1, _defaultValues_ssCoefficient, _defaultValues_stCoefficient;
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$antioverturning$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["masonryAntiOverturningSiteCharacteristicsSchema"]),
        defaultValues: {
            seismicAccelerationAtSlv: (_defaultValues_seismicAccelerationAtSlv = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.seismicAccelerationAtSlv) !== null && _defaultValues_seismicAccelerationAtSlv !== void 0 ? _defaultValues_seismicAccelerationAtSlv : 0,
            amplificationFactorAtSlv: (_defaultValues_amplificationFactorAtSlv = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.amplificationFactorAtSlv) !== null && _defaultValues_amplificationFactorAtSlv !== void 0 ? _defaultValues_amplificationFactorAtSlv : 0,
            subsoilCategory: (_defaultValues_subsoilCategory = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.subsoilCategory) !== null && _defaultValues_subsoilCategory !== void 0 ? _defaultValues_subsoilCategory : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SUBSOIL_CATEGORY"].A,
            // use SubsoilCoefficient from key entry of subsoilCategory
            ssCoefficient: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ssCoefficientValues"][(_defaultValues_subsoilCategory1 = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.subsoilCategory) !== null && _defaultValues_subsoilCategory1 !== void 0 ? _defaultValues_subsoilCategory1 : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SUBSOIL_CATEGORY"].A],
            topographicCategory: (_defaultValues_topographicCategory = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.topographicCategory) !== null && _defaultValues_topographicCategory !== void 0 ? _defaultValues_topographicCategory : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TOPOGRAPHIC_CATEGORY"].T1,
            stCoefficient: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["topographicCoefficientValues"][(_defaultValues_topographicCategory1 = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.topographicCategory) !== null && _defaultValues_topographicCategory1 !== void 0 ? _defaultValues_topographicCategory1 : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TOPOGRAPHIC_CATEGORY"].T1],
            subsoilCoefficient: ((_defaultValues_ssCoefficient = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.ssCoefficient) !== null && _defaultValues_ssCoefficient !== void 0 ? _defaultValues_ssCoefficient : 0) * ((_defaultValues_stCoefficient = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.stCoefficient) !== null && _defaultValues_stCoefficient !== void 0 ? _defaultValues_stCoefficient : 0)
        }
    });
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"])(session.accessToken, {
        onSuccess: {
            "MasonryAntiOverturningSiteCharacteristicsForm.useSaveModuleParamsMutation": async ()=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(tAction('edit.success'));
                await queryClient.invalidateQueries({
                    queryKey: [
                        'modules',
                        {
                            projectId,
                            moduleId
                        }
                    ]
                });
                onSave();
            }
        }["MasonryAntiOverturningSiteCharacteristicsForm.useSaveModuleParamsMutation"],
        onError: {
            "MasonryAntiOverturningSiteCharacteristicsForm.useSaveModuleParamsMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(tAction('edit.failure', {
                    error: error.message
                }));
            }
        }["MasonryAntiOverturningSiteCharacteristicsForm.useSaveModuleParamsMutation"]
    });
    var _form_watch;
    const subsoilCategory = (_form_watch = form.watch('subsoilCategory')) !== null && _form_watch !== void 0 ? _form_watch : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SUBSOIL_CATEGORY"].A;
    var _form_watch1;
    const topographicCategory = (_form_watch1 = form.watch('topographicCategory')) !== null && _form_watch1 !== void 0 ? _form_watch1 : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TOPOGRAPHIC_CATEGORY"].T1;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MasonryAntiOverturningSiteCharacteristicsForm.useEffect": ()=>{
            form.setValue('ssCoefficient', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ssCoefficientValues"][subsoilCategory]);
            form.setValue('stCoefficient', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["topographicCoefficientValues"][topographicCategory]);
            form.setValue('subsoilCoefficient', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ssCoefficientValues"][subsoilCategory] * __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["topographicCoefficientValues"][topographicCategory]);
        }
    }["MasonryAntiOverturningSiteCharacteristicsForm.useEffect"], [
        subsoilCategory,
        topographicCategory,
        form
    ]);
    const handleFormSubmit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "MasonryAntiOverturningSiteCharacteristicsForm.useCallback[handleFormSubmit]": (body)=>{
            // construct body based on global schema:
            const masonryAntiOverturningParams = {
                ...params,
                siteCharacteristics: body
            };
            mutate({
                projectId,
                moduleId,
                body: masonryAntiOverturningParams
            });
        }
    }["MasonryAntiOverturningSiteCharacteristicsForm.useCallback[handleFormSubmit]"], [
        mutate,
        projectId,
        moduleId,
        params
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "MasonryAntiOverturningSiteCharacteristicsForm.useEffect": ()=>{
            const subscription = form.watch({
                "MasonryAntiOverturningSiteCharacteristicsForm.useEffect.subscription": (values)=>{
                    setParams({
                        "MasonryAntiOverturningSiteCharacteristicsForm.useEffect.subscription": (prev)=>({
                                ...prev,
                                siteCharacteristics: values
                            })
                    }["MasonryAntiOverturningSiteCharacteristicsForm.useEffect.subscription"]);
                }
            }["MasonryAntiOverturningSiteCharacteristicsForm.useEffect.subscription"]);
            return ({
                "MasonryAntiOverturningSiteCharacteristicsForm.useEffect": ()=>subscription.unsubscribe()
            })["MasonryAntiOverturningSiteCharacteristicsForm.useEffect"];
        }
    }["MasonryAntiOverturningSiteCharacteristicsForm.useEffect"], [
        form,
        setParams
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Form"], {
        ...form,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            className: "space-y-4 rounded-md border p-4",
            onSubmit: form.handleSubmit(handleFormSubmit),
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    src: "/assets/masonry-anti-overturning/antibaltamento-geometria-armatura.jpg",
                    alt: "site characteristics",
                    height: 250,
                    width: 500,
                    className: "mx-auto rounded-md object-contain",
                    priority: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-site-characteristics-form.tsx",
                    lineNumber: 141,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "seismicAccelerationAtSlv",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-site-characteristics-form.tsx",
                    lineNumber: 149,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "amplificationFactorAtSlv",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-site-characteristics-form.tsx",
                    lineNumber: 154,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "subsoilCategory",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_SUBSOIL_CATEGORY"],
                    optionLabelFn: (p)=>t("subsoil.".concat(p)),
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-site-characteristics-form.tsx",
                    lineNumber: 159,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "ssCoefficient",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-site-characteristics-form.tsx",
                    lineNumber: 166,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "topographicCategory",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_TOPOGRAPHIC_CATEGORY"],
                    optionLabelFn: (p)=>t("topographic.".concat(p)),
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-site-characteristics-form.tsx",
                    lineNumber: 173,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "stCoefficient",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-site-characteristics-form.tsx",
                    lineNumber: 180,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "subsoilCoefficient",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-site-characteristics-form.tsx",
                    lineNumber: 186,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    type: "submit",
                    className: "w-full sm:w-auto",
                    disabled: isPending,
                    onClick: form.handleSubmit(handleFormSubmit),
                    children: [
                        isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                            className: "animate-spin"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-site-characteristics-form.tsx",
                            lineNumber: 198,
                            columnNumber: 25
                        }, ("TURBOPACK compile-time value", void 0)),
                        tCommon('save')
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-site-characteristics-form.tsx",
                    lineNumber: 192,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-site-characteristics-form.tsx",
            lineNumber: 137,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-site-characteristics-form.tsx",
        lineNumber: 136,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(MasonryAntiOverturningSiteCharacteristicsForm, "sW1iiW6iWPaLgYPE2hjeEl6hDOs=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"]
    ];
});
_c = MasonryAntiOverturningSiteCharacteristicsForm;
var _c;
__turbopack_context__.k.register(_c, "MasonryAntiOverturningSiteCharacteristicsForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "MasonryAntiOverturningParamsForm",
    ()=>MasonryAntiOverturningParamsForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/accordion.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$antioverturning$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/masonry/antioverturning-form.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$anti$2d$overturning$2f$calculations$2f$anti$2d$overturning$2d$composite$2d$reinforcement$2d$system$2d$calculation$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$anti$2d$overturning$2f$calculations$2f$anti$2d$overturning$2d$pre$2d$intervention$2d$calculation$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$anti$2d$overturning$2f$calculations$2f$anti$2d$overturning$2d$seismic$2d$demand$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-seismic-demand-form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$anti$2d$overturning$2f$sections$2f$anti$2d$overturning$2d$building$2d$characteristics$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-building-characteristics-form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$anti$2d$overturning$2f$sections$2f$anti$2d$overturning$2d$infill$2d$geometry$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-infill-geometry-form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$anti$2d$overturning$2f$sections$2f$anti$2d$overturning$2d$material$2d$properties$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-material-properties-form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$anti$2d$overturning$2f$sections$2f$anti$2d$overturning$2d$site$2d$characteristics$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-site-characteristics-form.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
const MasonryAntiOverturningParamsForm = (param)=>{
    let { session, projectId, moduleId, module } = param;
    var _params_siteCharacteristics, _params_siteCharacteristics1, _params_siteCharacteristics2, _params_siteCharacteristics3, _params_siteCharacteristics4, _params_siteCharacteristics5, _params_siteCharacteristics6, _params_buildingCharacteristics, _params_buildingCharacteristics1, _params_buildingCharacteristics2, _params_buildingCharacteristics3, _params_buildingCharacteristics4, _params_buildingCharacteristics5, _params_materialProperties, _params_materialProperties1, _params_materialProperties2, _params_materialProperties3, _params_materialProperties4, _params_materialProperties5, _params_materialProperties6, _params_materialProperties7, _params_materialProperties8, _params_materialProperties9, _params_materialProperties10, _params_materialProperties11, _params_materialProperties12, _params_materialProperties13, _params_infillGeometry, _params_infillGeometry1, _params_infillGeometry2, _params_infillGeometry3, _params_infillGeometry4, _params_infillGeometry5, _params_infillGeometry6, _params_infillGeometry7;
    _s();
    var _module_params;
    const [params, setParams] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])((_module_params = module === null || module === void 0 ? void 0 : module.params) !== null && _module_params !== void 0 ? _module_params : {});
    const seismicDemandCalculationResult = module === null || module === void 0 ? void 0 : module.seismicDemandCalculationResult;
    const preInterventionCalculationResult = module === null || module === void 0 ? void 0 : module.preInterventionCalculationResult;
    const _isCompositeReinforcementSystemDefined = preInterventionCalculationResult && seismicDemandCalculationResult;
    const [openItems, setOpenItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([
        '0'
    ]);
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.anti-overturning');
    const handleItemSaved = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "MasonryAntiOverturningParamsForm.useCallback[handleItemSaved]": (id)=>{
            const nextId = String(Number(id) + 1);
            setOpenItems({
                "MasonryAntiOverturningParamsForm.useCallback[handleItemSaved]": (old)=>{
                    const temp = old.filter({
                        "MasonryAntiOverturningParamsForm.useCallback[handleItemSaved].temp": (v)=>v !== id
                    }["MasonryAntiOverturningParamsForm.useCallback[handleItemSaved].temp"]);
                    return old.includes(nextId) ? temp : [
                        ...temp,
                        nextId
                    ];
                }
            }["MasonryAntiOverturningParamsForm.useCallback[handleItemSaved]"]);
        }
    }["MasonryAntiOverturningParamsForm.useCallback[handleItemSaved]"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Accordion"], {
            type: "multiple",
            value: openItems,
            onValueChange: setOpenItems,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionItem"], {
                    value: "0",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-medium",
                                children: t('site-characteristics.title')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                                lineNumber: 72,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                            lineNumber: 71,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionContent"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$anti$2d$overturning$2f$sections$2f$anti$2d$overturning$2d$site$2d$characteristics$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MasonryAntiOverturningSiteCharacteristicsForm"], {
                                session: session,
                                projectId: projectId,
                                moduleId: moduleId,
                                defaultValues: {
                                    seismicAccelerationAtSlv: params === null || params === void 0 ? void 0 : (_params_siteCharacteristics = params.siteCharacteristics) === null || _params_siteCharacteristics === void 0 ? void 0 : _params_siteCharacteristics.seismicAccelerationAtSlv,
                                    amplificationFactorAtSlv: params === null || params === void 0 ? void 0 : (_params_siteCharacteristics1 = params.siteCharacteristics) === null || _params_siteCharacteristics1 === void 0 ? void 0 : _params_siteCharacteristics1.amplificationFactorAtSlv,
                                    subsoilCategory: params === null || params === void 0 ? void 0 : (_params_siteCharacteristics2 = params.siteCharacteristics) === null || _params_siteCharacteristics2 === void 0 ? void 0 : _params_siteCharacteristics2.subsoilCategory,
                                    ssCoefficient: params === null || params === void 0 ? void 0 : (_params_siteCharacteristics3 = params.siteCharacteristics) === null || _params_siteCharacteristics3 === void 0 ? void 0 : _params_siteCharacteristics3.ssCoefficient,
                                    topographicCategory: params === null || params === void 0 ? void 0 : (_params_siteCharacteristics4 = params.siteCharacteristics) === null || _params_siteCharacteristics4 === void 0 ? void 0 : _params_siteCharacteristics4.topographicCategory,
                                    stCoefficient: params === null || params === void 0 ? void 0 : (_params_siteCharacteristics5 = params.siteCharacteristics) === null || _params_siteCharacteristics5 === void 0 ? void 0 : _params_siteCharacteristics5.stCoefficient,
                                    subsoilCoefficient: params === null || params === void 0 ? void 0 : (_params_siteCharacteristics6 = params.siteCharacteristics) === null || _params_siteCharacteristics6 === void 0 ? void 0 : _params_siteCharacteristics6.subsoilCoefficient
                                },
                                setParams: setParams,
                                params: params,
                                onSave: ()=>handleItemSaved('0')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                                lineNumber: 77,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                            lineNumber: 76,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                    lineNumber: 70,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionItem"], {
                    value: "1",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-medium",
                                children: t('building-characteristics.title')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                                lineNumber: 102,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                            lineNumber: 101,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionContent"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$anti$2d$overturning$2f$sections$2f$anti$2d$overturning$2d$building$2d$characteristics$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MasonryAntiOverturningBuildingCharacteristicsForm"], {
                                session: session,
                                projectId: projectId,
                                moduleId: moduleId,
                                defaultValues: {
                                    buildingType: params === null || params === void 0 ? void 0 : (_params_buildingCharacteristics = params.buildingCharacteristics) === null || _params_buildingCharacteristics === void 0 ? void 0 : _params_buildingCharacteristics.buildingType,
                                    totalBuildingHeight: params === null || params === void 0 ? void 0 : (_params_buildingCharacteristics1 = params.buildingCharacteristics) === null || _params_buildingCharacteristics1 === void 0 ? void 0 : _params_buildingCharacteristics1.totalBuildingHeight,
                                    buildingFundamentalPeriod: params === null || params === void 0 ? void 0 : (_params_buildingCharacteristics2 = params.buildingCharacteristics) === null || _params_buildingCharacteristics2 === void 0 ? void 0 : _params_buildingCharacteristics2.buildingFundamentalPeriod,
                                    parameterA: params === null || params === void 0 ? void 0 : (_params_buildingCharacteristics3 = params.buildingCharacteristics) === null || _params_buildingCharacteristics3 === void 0 ? void 0 : _params_buildingCharacteristics3.parameterA,
                                    parameterB: params === null || params === void 0 ? void 0 : (_params_buildingCharacteristics4 = params.buildingCharacteristics) === null || _params_buildingCharacteristics4 === void 0 ? void 0 : _params_buildingCharacteristics4.parameterB,
                                    parameterAp: params === null || params === void 0 ? void 0 : (_params_buildingCharacteristics5 = params.buildingCharacteristics) === null || _params_buildingCharacteristics5 === void 0 ? void 0 : _params_buildingCharacteristics5.parameterAp
                                },
                                setParams: setParams,
                                params: params,
                                onSave: ()=>handleItemSaved('1')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                                lineNumber: 107,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                            lineNumber: 106,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                    lineNumber: 100,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionItem"], {
                    value: "2",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-medium",
                                children: t('material-properties.title')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                                lineNumber: 129,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                            lineNumber: 128,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionContent"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$anti$2d$overturning$2f$sections$2f$anti$2d$overturning$2d$material$2d$properties$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MasonryAntiOverturningMaterialPropertiesForm"], {
                                session: session,
                                projectId: projectId,
                                moduleId: moduleId,
                                defaultValues: {
                                    infillWallTypology: params === null || params === void 0 ? void 0 : (_params_materialProperties = params.materialProperties) === null || _params_materialProperties === void 0 ? void 0 : _params_materialProperties.infillWallTypology,
                                    facingMaterial: params === null || params === void 0 ? void 0 : (_params_materialProperties1 = params.materialProperties) === null || _params_materialProperties1 === void 0 ? void 0 : _params_materialProperties1.facingMaterial,
                                    knowledgeLevel: params === null || params === void 0 ? void 0 : (_params_materialProperties2 = params.materialProperties) === null || _params_materialProperties2 === void 0 ? void 0 : _params_materialProperties2.knowledgeLevel,
                                    executionClass: params === null || params === void 0 ? void 0 : (_params_materialProperties3 = params.materialProperties) === null || _params_materialProperties3 === void 0 ? void 0 : _params_materialProperties3.executionClass,
                                    loadResistingCategory: params === null || params === void 0 ? void 0 : (_params_materialProperties4 = params.materialProperties) === null || _params_materialProperties4 === void 0 ? void 0 : _params_materialProperties4.loadResistingCategory,
                                    characteristicCompressiveStrength: params === null || params === void 0 ? void 0 : (_params_materialProperties5 = params.materialProperties) === null || _params_materialProperties5 === void 0 ? void 0 : _params_materialProperties5.characteristicCompressiveStrength,
                                    infillWallElasticModulus: params === null || params === void 0 ? void 0 : (_params_materialProperties6 = params.materialProperties) === null || _params_materialProperties6 === void 0 ? void 0 : _params_materialProperties6.infillWallElasticModulus,
                                    masonrySpecificWeightExternalFacing: params === null || params === void 0 ? void 0 : (_params_materialProperties7 = params.materialProperties) === null || _params_materialProperties7 === void 0 ? void 0 : _params_materialProperties7.masonrySpecificWeightExternalFacing,
                                    masonrySpecificWeightInternalFacing: params === null || params === void 0 ? void 0 : (_params_materialProperties8 = params.materialProperties) === null || _params_materialProperties8 === void 0 ? void 0 : _params_materialProperties8.masonrySpecificWeightInternalFacing,
                                    plasterSpecificWeight: params === null || params === void 0 ? void 0 : (_params_materialProperties9 = params.materialProperties) === null || _params_materialProperties9 === void 0 ? void 0 : _params_materialProperties9.plasterSpecificWeight,
                                    confidenceFactor: params === null || params === void 0 ? void 0 : (_params_materialProperties10 = params.materialProperties) === null || _params_materialProperties10 === void 0 ? void 0 : _params_materialProperties10.confidenceFactor,
                                    masonryStrengthSafetyFactor: params === null || params === void 0 ? void 0 : (_params_materialProperties11 = params.materialProperties) === null || _params_materialProperties11 === void 0 ? void 0 : _params_materialProperties11.masonryStrengthSafetyFactor,
                                    designCompressiveStrength: params === null || params === void 0 ? void 0 : (_params_materialProperties12 = params.materialProperties) === null || _params_materialProperties12 === void 0 ? void 0 : _params_materialProperties12.designCompressiveStrength,
                                    ultimateMasonryStrain: params === null || params === void 0 ? void 0 : (_params_materialProperties13 = params.materialProperties) === null || _params_materialProperties13 === void 0 ? void 0 : _params_materialProperties13.ultimateMasonryStrain
                                },
                                params: params,
                                setParams: setParams,
                                onSave: ()=>handleItemSaved('2')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                                lineNumber: 134,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                            lineNumber: 133,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                    lineNumber: 127,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionItem"], {
                    value: "3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-medium",
                                children: t('infill-geometry.title')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                                lineNumber: 174,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                            lineNumber: 173,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionContent"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$anti$2d$overturning$2f$sections$2f$anti$2d$overturning$2d$infill$2d$geometry$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MasonryAntiOverturningInfillGeometryForm"], {
                                session: session,
                                projectId: projectId,
                                moduleId: moduleId,
                                defaultValues: {
                                    panelWidth: params === null || params === void 0 ? void 0 : (_params_infillGeometry = params.infillGeometry) === null || _params_infillGeometry === void 0 ? void 0 : _params_infillGeometry.panelWidth,
                                    externalFacingThickness: params === null || params === void 0 ? void 0 : (_params_infillGeometry1 = params.infillGeometry) === null || _params_infillGeometry1 === void 0 ? void 0 : _params_infillGeometry1.externalFacingThickness,
                                    internalFacingThickness: params === null || params === void 0 ? void 0 : (_params_infillGeometry2 = params.infillGeometry) === null || _params_infillGeometry2 === void 0 ? void 0 : _params_infillGeometry2.internalFacingThickness,
                                    singleSidePlasterThickness: params === null || params === void 0 ? void 0 : (_params_infillGeometry3 = params.infillGeometry) === null || _params_infillGeometry3 === void 0 ? void 0 : _params_infillGeometry3.singleSidePlasterThickness,
                                    netPanelHeight: params === null || params === void 0 ? void 0 : (_params_infillGeometry4 = params.infillGeometry) === null || _params_infillGeometry4 === void 0 ? void 0 : _params_infillGeometry4.netPanelHeight,
                                    panelHeightFromGroundLevel: params === null || params === void 0 ? void 0 : (_params_infillGeometry5 = params.infillGeometry) === null || _params_infillGeometry5 === void 0 ? void 0 : _params_infillGeometry5.panelHeightFromGroundLevel,
                                    panelCentroidFromGroundLevel: params === null || params === void 0 ? void 0 : (_params_infillGeometry6 = params.infillGeometry) === null || _params_infillGeometry6 === void 0 ? void 0 : _params_infillGeometry6.panelCentroidFromGroundLevel,
                                    fundamentalPeriodPanel: params === null || params === void 0 ? void 0 : (_params_infillGeometry7 = params.infillGeometry) === null || _params_infillGeometry7 === void 0 ? void 0 : _params_infillGeometry7.fundamentalPeriodPanel
                                },
                                params: params,
                                onSave: ()=>handleItemSaved('3')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                                lineNumber: 179,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                            lineNumber: 178,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                    lineNumber: 172,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionItem"], {
                    value: "4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                            disabled: !seismicDemandCalculationResult,
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-medium",
                                children: t('seismic-demand.title')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                                lineNumber: 206,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                            lineNumber: 205,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionContent"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$anti$2d$overturning$2f$calculations$2f$anti$2d$overturning$2d$seismic$2d$demand$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MasonryAntiOverturningSeismicDemandCalculationResultsForm"], {
                                seismicDemandCalculationResult: seismicDemandCalculationResult,
                                onSave: ()=>handleItemSaved('4')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                                lineNumber: 209,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                            lineNumber: 208,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                    lineNumber: 204,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionItem"], {
                    value: "5",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                            disabled: !preInterventionCalculationResult,
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-medium",
                                children: t('pre-intervention.title')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                                lineNumber: 217,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                            lineNumber: 216,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionContent"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$anti$2d$overturning$2f$calculations$2f$anti$2d$overturning$2d$pre$2d$intervention$2d$calculation$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MasonryAntiOverturningPreInterventionCalculationResultsForm"], {
                                preInterventionCalculationResult: preInterventionCalculationResult,
                                onSave: ()=>handleItemSaved('5')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                                lineNumber: 222,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                            lineNumber: 221,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                    lineNumber: 215,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionItem"], {
                    value: "6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                            disabled: !__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$antioverturning$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["compositeReinforcementSystemInputSchema"],
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-medium",
                                children: t('composite-reinforcement-system.title')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                                lineNumber: 232,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                            lineNumber: 231,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionContent"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$anti$2d$overturning$2f$calculations$2f$anti$2d$overturning$2d$composite$2d$reinforcement$2d$system$2d$calculation$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MasonryAntiOverturningCompositeReinforcementSystemCalculationForm"], {
                                session: session,
                                projectId: projectId,
                                module: module
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                                lineNumber: 237,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                            lineNumber: 236,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
                    lineNumber: 230,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
            lineNumber: 65,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx",
        lineNumber: 64,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(MasonryAntiOverturningParamsForm, "GZ+9bV2A//hdWrAHhWBrg/UgU+s=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"]
    ];
});
_c = MasonryAntiOverturningParamsForm;
var _c;
__turbopack_context__.k.register(_c, "MasonryAntiOverturningParamsForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
]);

//# sourceMappingURL=eb54d_common_atlas_module-detail_params-forms_masonry_anti-overturning_040bd72d._.js.map