import { NumberFormInput } from '@atlas/components/common/form/number-form-input'
import { Badge } from '@atlas/components/ui/badge'
import { Button } from '@atlas/components/ui/button'
import { Form } from '@atlas/components/ui/form'
import type { Module } from '@atlas/lib/api/modules/schemas/modules'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'
import { zodResolver } from '@hookform/resolvers/zod'
import { Loader2Icon } from 'lucide-react'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useCallback, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

const formSchema = z.object({
  maximumBendingMoment: z
    .number()
    .min(0, 'Maximum bending moment must be positive'),
  maximumShearForce: z.number().min(0, 'Maximum shear force must be positive'),
  designBendingStress: z
    .number()
    .min(0, 'Design bending stress must be positive'),
  designBendingStrength: z
    .number()
    .min(0, 'Design bending strength must be positive'),
  designBendingCheck: z
    .number()
    .min(0, 'Design bending check must be positive'),
  designShearStress: z.number().min(0, 'Design shear stress must be positive'),
  designShearStrength: z
    .number()
    .min(0, 'Design shear strength must be positive'),
  designShearCheck: z.number().min(0, 'Design shear check must be positive'),
  permanentLoadPerLinearMeter: z
    .number()
    .min(0, 'Permanent load per linear meter must be positive'),
  imposedLoadPerLinearMeter: z
    .number()
    .min(0, 'Imposed load per linear meter must be positive'),
  instantaneousDeflectionPermanentLoad: z
    .number()
    .min(0, 'Instantaneous deflection permanent load must be positive'),
  instantaneousDeflectionImposedLoad: z
    .number()
    .min(0, 'Instantaneous deflection imposed load must be positive'),
  instantaneousDeflectionTotalLoads: z
    .number()
    .min(0, 'Instantaneous deflection total loads must be positive'),
  instantaneousDeflectionCheck: z
    .number()
    .min(0, 'Instantaneous deflection check must be positive'),
  combinationFactor: z.number().min(0, 'Combination factor must be positive'),
  finalDeflectionTotalLoads: z
    .number()
    .min(0, 'Final deflection total loads must be positive'),
  finalCheckResult: z.number().min(0, 'Final check result must be positive'),
})

type FormSchema = z.infer<typeof formSchema>

type Props = {
  session: Session
  projectId: Project['id']
  moduleId: Module['id']
  defaultValues?: Partial<FormSchema>
  preInterventionData?: any
  materialProperties?: any
  geometryProperties?: any
  compositeGeometry?: any
  compositeProperties?: any
  onSave?: () => void
  initialDeformation?: any
}

export const WoodPostInterventionCheckForm = ({
  session,
  projectId,
  moduleId,
  defaultValues,
  preInterventionData,
  materialProperties,
  geometryProperties,
  compositeGeometry,
  compositeProperties,
  onSave,
  initialDeformation,
}: Props) => {
  const t = useTranslations(
    'forms.project-params.wood.resultOfPostIntervationCheck',
  )
  const tCommon = useTranslations('actions.common')

  const form = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      maximumBendingMoment:
        defaultValues?.maximumBendingMoment ||
        preInterventionData?.maximumBendingMoment ||
        0,
      maximumShearForce:
        defaultValues?.maximumShearForce ||
        preInterventionData?.maximumShearForce ||
        0,
      designBendingStress: defaultValues?.designBendingStress || 0,
      designBendingStrength: defaultValues?.designBendingStrength || 0,
      designBendingCheck: defaultValues?.designBendingCheck || 0,
      designShearStress: defaultValues?.designShearStress || 0,
      designShearStrength: defaultValues?.designShearStrength || 0,
      designShearCheck: defaultValues?.designShearCheck || 0,
      permanentLoadPerLinearMeter:
        defaultValues?.permanentLoadPerLinearMeter ||
        preInterventionData?.permanentLoadPerLinearMeter ||
        0,
      imposedLoadPerLinearMeter:
        defaultValues?.imposedLoadPerLinearMeter ||
        preInterventionData?.imposedLoadPerLinearMeter ||
        0,
      instantaneousDeflectionPermanentLoad:
        defaultValues?.instantaneousDeflectionPermanentLoad || 0,
      instantaneousDeflectionImposedLoad:
        defaultValues?.instantaneousDeflectionImposedLoad || 0,
      instantaneousDeflectionTotalLoads:
        defaultValues?.instantaneousDeflectionTotalLoads || 0,
      instantaneousDeflectionCheck:
        defaultValues?.instantaneousDeflectionCheck || 0,
      combinationFactor:
        defaultValues?.combinationFactor ||
        preInterventionData?.combinationFactor ||
        0,
      finalDeflectionTotalLoads: defaultValues?.finalDeflectionTotalLoads || 0,
      finalCheckResult: defaultValues?.finalCheckResult || 0,
    },
  })

  // Reset form when defaultValues change (for saved data after refresh)
  useEffect(() => {
    if (
      defaultValues &&
      typeof defaultValues === 'object' &&
      Object.keys(defaultValues).length > 0
    ) {
      // Create a complete form data object with all fields
      const formData = {
        maximumBendingMoment:
          defaultValues.maximumBendingMoment ??
          preInterventionData?.maximumBendingMoment ??
          0,
        maximumShearForce:
          defaultValues.maximumShearForce ??
          preInterventionData?.maximumShearForce ??
          0,
        designBendingStress: defaultValues.designBendingStress ?? 0,
        designBendingStrength: defaultValues.designBendingStrength ?? 0,
        designBendingCheck: defaultValues.designBendingCheck ?? 0,
        designShearStress: defaultValues.designShearStress ?? 0,
        designShearStrength: defaultValues.designShearStrength ?? 0,
        designShearCheck: defaultValues.designShearCheck ?? 0,
        permanentLoadPerLinearMeter:
          defaultValues.permanentLoadPerLinearMeter ??
          preInterventionData?.permanentLoadPerLinearMeter ??
          0,
        imposedLoadPerLinearMeter:
          defaultValues.imposedLoadPerLinearMeter ??
          preInterventionData?.imposedLoadPerLinearMeter ??
          0,
        instantaneousDeflectionPermanentLoad:
          defaultValues.instantaneousDeflectionPermanentLoad ?? 0,
        instantaneousDeflectionImposedLoad:
          defaultValues.instantaneousDeflectionImposedLoad ?? 0,
        instantaneousDeflectionTotalLoads:
          defaultValues.instantaneousDeflectionTotalLoads ?? 0,
        instantaneousDeflectionCheck:
          defaultValues.instantaneousDeflectionCheck ?? 0,
        combinationFactor:
          defaultValues.combinationFactor ??
          preInterventionData?.combinationFactor ??
          0,
        finalDeflectionTotalLoads: defaultValues.finalDeflectionTotalLoads ?? 0,
        finalCheckResult: defaultValues.finalCheckResult ?? 0,
      }

      form.reset(formData)
    } else {
    }
  }, [defaultValues, preInterventionData, form])

  const { mutate, isPending } = useSaveModuleParamsMutation(session.accessToken)

  // Helper function to determine check result
  const getCheckResult = (
    value: number,
    threshold: number,
    checkType: 'lessThanOrEqual' | 'greaterThanOrEqual' = 'lessThanOrEqual',
  ) => {
    if (checkType === 'lessThanOrEqual') {
      // For bending and shear checks: <= threshold = "Satisfy", > threshold = "Non-satisfy"
      return value <= threshold ? 'Satisfy' : 'Non-satisfy'
    }
    // For deflection checks: >= threshold = "Satisfy", < threshold = "Non-satisfy"
    return value >= threshold ? 'Satisfy' : 'Non-satisfy'
  }

  // Helper function to get badge variant
  const getBadgeVariant = (result: string) => {
    return result === 'Satisfy' ? 'default' : 'destructive'
  }

  const handleFormSubmit = useCallback(
    (data: FormSchema) => {
      // Save as part of postIntervationCheck structure with all inherited data
      mutate({
        projectId,
        moduleId,
        body: {
          postIntervationCheck: {
            // Include inherited data from pre-intervention
            initialDeformation: initialDeformation || 0,
            materialProperties: materialProperties || null,
            geometry: geometryProperties || null,
            // Include composite data from props
            compositeGeometry: compositeGeometry || null,
            compositeProperties: compositeProperties || null,
            // Include the current form data (only 3 fields now)
            resultOfPostIntervationCheck: data,
          },
        } as any,
      })
      onSave?.()
    },
    [
      mutate,
      projectId,
      moduleId,
      onSave,
      materialProperties,
      geometryProperties,
      compositeGeometry,
      compositeProperties,
      initialDeformation,
    ],
  )

  // Calculate derived values when dependencies change
  useEffect(() => {
    // Set values from pre-intervention data first (if available)
    if (preInterventionData) {
      const preIntervention = preInterventionData

      // Set values from pre-intervention data (only if not already saved)
      if (
        !defaultValues?.maximumBendingMoment &&
        preIntervention.maximumBendingMoment !== undefined
      ) {
        form.setValue(
          'maximumBendingMoment',
          preIntervention.maximumBendingMoment,
        )
      }
      if (
        !defaultValues?.maximumShearForce &&
        preIntervention.maximumShearForce !== undefined
      ) {
        form.setValue('maximumShearForce', preIntervention.maximumShearForce)
      }
      if (
        !defaultValues?.permanentLoadPerLinearMeter &&
        preIntervention.permanentLoadPerLinearMeter !== undefined
      ) {
        form.setValue(
          'permanentLoadPerLinearMeter',
          preIntervention.permanentLoadPerLinearMeter,
        )
      }
      if (
        !defaultValues?.imposedLoadPerLinearMeter &&
        preIntervention.imposedLoadPerLinearMeter !== undefined
      ) {
        form.setValue(
          'imposedLoadPerLinearMeter',
          preIntervention.imposedLoadPerLinearMeter,
        )
      }
      if (
        !defaultValues?.combinationFactor &&
        preIntervention.combinationFactor !== undefined
      ) {
        form.setValue('combinationFactor', preIntervention.combinationFactor)
      }
    }

    // Calculate design_bending_stress if we have the required data
    if (preInterventionData) {
      const preIntervention = preInterventionData
      const maximumBendingMoment =
        form.getValues('maximumBendingMoment') ||
        preIntervention.maximumBendingMoment ||
        0

      // Try to get sectionModulus from composite properties (preferred)
      if (
        compositeProperties?.sectionModulus &&
        compositeProperties.sectionModulus > 0
      ) {
        const designBendingStress =
          maximumBendingMoment / (compositeProperties.sectionModulus * 1000)
        form.setValue('designBendingStress', designBendingStress)
      }
      // Fallback: try to get sectionModulus from geometry properties (from pre-intervention forms)
      else if (
        geometryProperties?.sectionModulus &&
        geometryProperties.sectionModulus > 0
      ) {
        const designBendingStress =
          maximumBendingMoment / (geometryProperties.sectionModulus * 1000)
        form.setValue('designBendingStress', designBendingStress)
      } else {
        // Set to 0 if we can't calculate (composite properties not ready yet)
        form.setValue('designBendingStress', 0)
      }
    }

    // Calculate design_bending_strength = MIN(pre ma "designBendingStrength", frp_design_maximum_strain*frp_elasticity_modulus)
    if (materialProperties && compositeProperties) {
      // Get designBendingStrength from previous forms (1,2,3,4)
      const preDesignBendingStrength =
        geometryProperties.designBendingStrength || 0

      // Get frp_design_maximum_strain and frp_elasticity_modulus from 5th section (Composite Properties)
      const frpDesignMaximumStrain =
        compositeProperties.frpDesignMaximumStrain || 0
      const frpElasticityModulus = compositeProperties.frpElasticityModulus || 0

      // Calculate the MIN of both values
      const designBendingStrength = Math.min(
        preDesignBendingStrength,
        frpDesignMaximumStrain * frpElasticityModulus,
      )

      form.setValue('designBendingStrength', designBendingStrength)

      // Calculate designBendingCheck = design_bending_stress / design_bending_strength
      const designBendingStress = form.getValues('designBendingStress') || 0
      const designBendingCheck =
        designBendingStrength > 0
          ? designBendingStress / designBendingStrength
          : 0
      form.setValue('designBendingCheck', designBendingCheck)
    } else {
      // Set to 0 if we don't have the required data
      form.setValue('designBendingStrength', 0)
      form.setValue('designBendingCheck', 0)
    }

    // Calculate shear stress and strength
    if (geometryProperties) {
      const maximumShearForce =
        form.getValues('maximumShearForce') ||
        preInterventionData?.maximumShearForce ||
        0

      // Calculate design_shear_stress = 3*1000*maximum_shear_force/(2*1000*beamSectionHeight*1000*beamSectionWidth)
      const beamSectionHeight = geometryProperties.beamSectionHeight || 0
      const beamSectionWidth = geometryProperties.beamSectionWidth || 0

      if (beamSectionHeight > 0 && beamSectionWidth > 0) {
        const designShearStress =
          (3 * 1000 * maximumShearForce) /
          (2 * 1000 * beamSectionHeight * 1000 * beamSectionWidth)
        form.setValue('designShearStress', designShearStress)

        // design_shear_strength = Pre ma "designShearStrength"
        const designShearStrength = geometryProperties.designShearStrength || 0
        form.setValue('designShearStrength', designShearStrength)

        // Calculate designShearCheck = design_shear_stress / design_shear_strength
        const designShearCheck =
          designShearStrength > 0 ? designShearStress / designShearStrength : 0
        form.setValue('designShearCheck', designShearCheck)
      } else {
        form.setValue('designShearStress', 0)
        form.setValue('designShearStrength', 0)
        form.setValue('designShearCheck', 0)
      }
    } else {
      form.setValue('designShearStress', 0)
      form.setValue('designShearStrength', 0)
      form.setValue('designShearCheck', 0)
    }

    // Calculate deflection values
    if (geometryProperties && compositeProperties) {
      const permanentLoadPerLinearMeter =
        form.getValues('permanentLoadPerLinearMeter') ||
        preInterventionData?.permanentLoadPerLinearMeter ||
        0
      const beamSpan = geometryProperties.beamSpan || 0
      const elasticityInstantaneousModulus =
        geometryProperties.elasticityInstantaneousModulus || 0

      // Get moment of inertia from composite properties
      const momentOfInertiaAboutY =
        compositeProperties.momentOfInertiaAboutY || 0

      if (
        beamSpan > 0 &&
        elasticityInstantaneousModulus > 0 &&
        momentOfInertiaAboutY > 0
      ) {
        // instantaneous_deflection_permanent_load = 100*(5*permanent_load_per_linear_meter*beamSpan^4)/(384*1000*designShearStrength*moment_of_inertia_about_y)
        const instantaneousDeflectionPermanentLoad =
          100 *
          ((5 * permanentLoadPerLinearMeter * beamSpan ** 4) /
            (384 *
              1000 *
              elasticityInstantaneousModulus *
              momentOfInertiaAboutY))
        form.setValue(
          'instantaneousDeflectionPermanentLoad',
          instantaneousDeflectionPermanentLoad,
        )

        // instantaneous_deflection_imposed_load = 100*(5*imposed_load_per_linear_meter*beamSpan^4)/(384*1000*designShearStrength*moment_of_inertia_about_y)
        const imposedLoadPerLinearMeter =
          form.getValues('imposedLoadPerLinearMeter') ||
          preInterventionData?.imposedLoadPerLinearMeter ||
          0
        const instantaneousDeflectionImposedLoad =
          (100 * (5 * imposedLoadPerLinearMeter * beamSpan ** 4)) /
          (384 * 1000 * elasticityInstantaneousModulus * momentOfInertiaAboutY)
        form.setValue(
          'instantaneousDeflectionImposedLoad',
          instantaneousDeflectionImposedLoad,
        )

        // instantaneous_deflection_total_loads = instantaneous_deflection_permanent_load + instantaneous_deflection_imposed_load + initialDeformation
        const finalInitialDeformation = initialDeformation || 0
        const instantaneousDeflectionTotalLoads =
          instantaneousDeflectionPermanentLoad +
          instantaneousDeflectionImposedLoad +
          finalInitialDeformation
        form.setValue(
          'instantaneousDeflectionTotalLoads',
          instantaneousDeflectionTotalLoads,
        )

        // instantaneousDeflectionCheck = 100*beamSpan / instantaneous_deflection_total_loads (< 300 = "Non-satisfy", >= 300 = "Satisfy")
        const instantaneousDeflectionCheck =
          instantaneousDeflectionTotalLoads > 0
            ? (100 * beamSpan) / instantaneousDeflectionTotalLoads
            : 0
        form.setValue(
          'instantaneousDeflectionCheck',
          instantaneousDeflectionCheck,
        )

        // final_deflection_total_loads = instantaneous_deflection_permanent_load*(1+deformabilityFactor)+instantaneous_deflection_imposed_load*(1+combination_factor*deformabilityFactor)+initialDeformation
        const deformabilityFactor = geometryProperties.deformabilityFactor || 0
        const combinationFactor =
          form.getValues('combinationFactor') ||
          preInterventionData?.combinationFactor ||
          0
        const finalDeflectionTotalLoads =
          instantaneousDeflectionPermanentLoad * (1 + deformabilityFactor) +
          instantaneousDeflectionImposedLoad *
            (1 + combinationFactor * deformabilityFactor) +
          finalInitialDeformation
        form.setValue('finalDeflectionTotalLoads', finalDeflectionTotalLoads)

        // finalCheckResult = 100*beamSpan/final_deflection_total_loads (>= 200 = "Satisfy", < 200 = "Non-Satisfy")
        const finalCheckResult =
          finalDeflectionTotalLoads > 0
            ? (100 * beamSpan) / finalDeflectionTotalLoads
            : 0
        form.setValue('finalCheckResult', finalCheckResult)
      } else {
        form.setValue('instantaneousDeflectionPermanentLoad', 0)
        form.setValue('instantaneousDeflectionImposedLoad', 0)
        form.setValue('instantaneousDeflectionTotalLoads', 0)
        form.setValue('instantaneousDeflectionCheck', 0)
        form.setValue('finalDeflectionTotalLoads', 0)
        form.setValue('finalCheckResult', 0)
      }
    } else {
      form.setValue('instantaneousDeflectionPermanentLoad', 0)
      form.setValue('instantaneousDeflectionImposedLoad', 0)
      form.setValue('instantaneousDeflectionTotalLoads', 0)
      form.setValue('instantaneousDeflectionCheck', 0)
      form.setValue('finalDeflectionTotalLoads', 0)
      form.setValue('finalCheckResult', 0)
    }
  }, [
    preInterventionData,
    compositeProperties,
    geometryProperties,
    materialProperties,
    defaultValues,
    form,
    initialDeformation,
  ])

  return (
    <div className="flex flex-col 2xl:flex-row justify-center gap-2">
      <Form {...form}>
        <form
          className="space-y-4 rounded-md border p-4 grow"
          onSubmit={form.handleSubmit(handleFormSubmit)}
        >
          {/* 1. maximum_bending_moment = pre ma "maximumBendingMoment" */}
          <NumberFormInput
            control={form.control}
            name="maximumBendingMoment"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />

          {/* 2. maximum_shear_force = pre ma "maximumShearForce" */}
          <NumberFormInput
            control={form.control}
            name="maximumShearForce"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />

          {/* 3. design_bending_stress = maximum_bending_moment/(section_modulus*1000) */}
          <NumberFormInput
            control={form.control}
            name="designBendingStress"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />

          {/* 4. design_bending_strength = MIN(pre ma "designBendingStrength", frp_design_maximum_strain*frp_elasticity_modulus) */}
          <NumberFormInput
            control={form.control}
            name="designBendingStrength"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />

          {/* 5. designBendingCheck = design_bending_stress / design_bending_strength <= 1 "Non-satisfy" else "satisfy" */}
          <div className="flex items-start gap-4">
            <NumberFormInput
              control={form.control}
              name="designBendingCheck"
              t={t}
              disabled={true}
              fieldContainerClassName="flex-1"
              decimalPlaces={3}
            />
            <Badge
              variant={getBadgeVariant(
                getCheckResult(form.watch('designBendingCheck'), 1),
              )}
              className="mt-7"
            >
              {getCheckResult(form.watch('designBendingCheck'), 1)}
            </Badge>
          </div>

          {/* 6. design_shear_stress = 3*1000*maximum_shear_force/(2*1000*beamSectionHeight*1000*beamSectionWidth) */}
          <NumberFormInput
            control={form.control}
            name="designShearStress"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />

          {/* 7. design_shear_strength = Pre ma "designShearStrength" */}
          <NumberFormInput
            control={form.control}
            name="designShearStrength"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />

          {/* 8. designShearCheck = design_shear_stress / design_shear_strength <= 1 "satisfy" else "Non-satisfy" */}
          <div className="flex items-start gap-4">
            <NumberFormInput
              control={form.control}
              name="designShearCheck"
              t={t}
              disabled={true}
              fieldContainerClassName="flex-1"
              decimalPlaces={3}
            />
            <Badge
              variant={getBadgeVariant(
                getCheckResult(form.watch('designShearCheck'), 1),
              )}
              className="mt-7"
            >
              {getCheckResult(form.watch('designShearCheck'), 1)}
            </Badge>
          </div>

          {/* 9. permanent_load_per_linear_meter = Pre ma "permanentLoadPerLinearMeter" */}
          <NumberFormInput
            control={form.control}
            name="permanentLoadPerLinearMeter"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />

          {/* 10. imposed_load_per_linear_meter = pre ma "imposedLoadPerLinearMeter" */}
          <NumberFormInput
            control={form.control}
            name="imposedLoadPerLinearMeter"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />

          {/* 11. instantaneous_deflection_permanent_load = 100*(5*permanent_load_per_linear_meter*beamSpan^4)/(384*1000*designShearStrength*moment_of_inertia_about_y) */}
          <NumberFormInput
            control={form.control}
            name="instantaneousDeflectionPermanentLoad"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />

          {/* 12. instantaneous_deflection_imposed_load = 100*(5*imposed_load_per_linear_meter*beamSpan^4)/(384*1000*designShearStrength*moment_of_inertia_about_y) */}
          <NumberFormInput
            control={form.control}
            name="instantaneousDeflectionImposedLoad"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />

          {/* 13. instantaneous_deflection_total_loads = instantaneous_deflection_permanent_load + instantaneous_deflection_imposed_load + initialDeformation */}
          <NumberFormInput
            control={form.control}
            name="instantaneousDeflectionTotalLoads"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />

          {/* 14. instantaneousDeflectionCheck = 100*beamSpan / instantaneous_deflection_total_loads (< 300 = "Non-satisfy", >= 300 = "Satisfy") */}
          <div className="flex items-start gap-4">
            <NumberFormInput
              control={form.control}
              name="instantaneousDeflectionCheck"
              t={t}
              disabled={true}
              fieldContainerClassName="flex-1"
              decimalPlaces={3}
            />
            <Badge
              variant={getBadgeVariant(
                getCheckResult(
                  form.watch('instantaneousDeflectionCheck'),
                  300,
                  'greaterThanOrEqual',
                ),
              )}
              className="mt-7"
            >
              {getCheckResult(
                form.watch('instantaneousDeflectionCheck'),
                300,
                'greaterThanOrEqual',
              )}
            </Badge>
          </div>

          {/* 15. combination_factor = pre ma "combinationFactor" */}
          <NumberFormInput
            control={form.control}
            name="combinationFactor"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />

          {/* 16. final_deflection_total_loads = instantaneous_deflection_permanent_load*(1+deformabilityFactor)+instantaneous_deflection_imposed_load*(1+combination_factor*deformabilityFactor)+initialDeformation */}
          <NumberFormInput
            control={form.control}
            name="finalDeflectionTotalLoads"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />

          {/* 17. finalCheckResult = 100*beamSpan/final_deflection_total_loads (>= 200 = "Satisfy", < 200 = "Non-Satisfy") */}
          <div className="flex items-start gap-4">
            <NumberFormInput
              control={form.control}
              name="finalCheckResult"
              t={t}
              disabled={true}
              fieldContainerClassName="flex-1"
              decimalPlaces={3}
            />
            <Badge
              variant={getBadgeVariant(
                getCheckResult(
                  form.watch('finalCheckResult'),
                  200,
                  'greaterThanOrEqual',
                ),
              )}
              className="mt-7"
            >
              {getCheckResult(
                form.watch('finalCheckResult'),
                200,
                'greaterThanOrEqual',
              )}
            </Badge>
          </div>

          <Button
            type="submit"
            className="w-full sm:w-auto"
            disabled={isPending}
          >
            {isPending && <Loader2Icon className="animate-spin" />}
            {tCommon('save')}
          </Button>
        </form>
      </Form>
    </div>
  )
}
