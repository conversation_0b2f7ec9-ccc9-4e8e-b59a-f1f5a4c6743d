import { PillarParamsForm } from '@atlas/components/common/atlas/module-detail/params-forms/pillar/pillar-params-form'
import { RectangularBeamParamsForm } from '@atlas/components/common/atlas/module-detail/params-forms/rectangular-beam/rectangular-beam-params-form'
import { SlabParamsForm } from '@atlas/components/common/atlas/module-detail/params-forms/slab/slab-params-form'
import { Alert, AlertDescription, AlertTitle } from '@atlas/components/ui/alert'
import { Card, CardContent } from '@atlas/components/ui/card'
import type { ModuleWithParams } from '@atlas/lib/api/modules/schemas/modules'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { MasonryAntiOverturningParamsForm } from './params-forms/masonry/anti-overturning/anti-overturning-params-form'
import { CrmParamsForm } from './params-forms/masonry/crm/crm-params-form'
import { FrcmColumnParamsForm } from './params-forms/masonry/frcm-column/frcm-column-params-form'
import { MaschiMurariParamsForm } from './params-forms/masonry/maschi-murari/maschi-murari-params-form'
import { TBeamParamsForm } from './params-forms/t-beam/t-beam-params-form'
import { WoodParamsForm } from './params-forms/wood/wood-params-form'

type Props = {
  projectId: string
  module: ModuleWithParams
  session: Session
}

export const ModuleParamsSection = ({ projectId, module, session }: Props) => {
  const t = useTranslations('components.module-detail')

  const renderParamsContent = () => {
    switch (module.type) {
      case 'RECTANGULAR_BEAM':
        return (
          <RectangularBeamParamsForm
            projectId={projectId}
            moduleId={module.id}
            session={session}
            module={module}
          />
        )
      case 'T_BEAM':
        return (
          <TBeamParamsForm
            projectId={projectId}
            moduleId={module.id}
            session={session}
            module={module}
          />
        )
      case 'SLAB':
        return (
          <SlabParamsForm
            projectId={projectId}
            moduleId={module.id}
            session={session}
            module={module}
          />
        )
      case 'PILLAR':
        return (
          <PillarParamsForm
            projectId={projectId}
            moduleId={module.id}
            session={session}
            module={module}
          />
        )
      case 'ANTI_OVERTURNING':
        return (
          <MasonryAntiOverturningParamsForm
            projectId={projectId}
            moduleId={module.id}
            session={session}
            module={module}
          />
        )
      case 'CRM':
        return (
          <CrmParamsForm
            projectId={projectId}
            session={session}
            module={module}
          />
        )
      case 'FRCM_COLUMN':
        return (
          <FrcmColumnParamsForm
            projectId={projectId}
            moduleId={module.id}
            session={session}
            module={module}
          />
        )
      case 'WOOD':
        return (
          <WoodParamsForm
            projectId={projectId}
            moduleId={module.id}
            session={session}
            module={module}
          />
        )
      case 'MASCHI_MURARI':
        return (
          <MaschiMurariParamsForm
            projectId={projectId}
            moduleId={module.id}
            session={session}
            module={module}
          />
        )
      default:
        return (
          <Alert variant="default">
            <AlertTitle>{t('params.no-form-available.title')}</AlertTitle>
            <AlertDescription>
              {t('params.no-form-available.description', {
                projectType: module.type!,
              })}
            </AlertDescription>
          </Alert>
        )
    }
  }

  return (
    <Card className="container mx-auto max-w-2xl">
      <CardContent>{renderParamsContent()}</CardContent>
    </Card>
  )
}
