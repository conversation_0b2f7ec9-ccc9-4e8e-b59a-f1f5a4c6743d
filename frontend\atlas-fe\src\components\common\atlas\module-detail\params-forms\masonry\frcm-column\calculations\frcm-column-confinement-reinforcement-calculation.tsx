import { CustomProductSection } from '@atlas/components/common/atlas/product/custom-product-section'
import { ProductSummaryCard } from '@atlas/components/common/atlas/product/product-summary-card'
import { NumberFormInput } from '@atlas/components/common/form/number-form-input'
import { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'
import { SelectFormInput } from '@atlas/components/common/form/select-form-input'
import { Button } from '@atlas/components/ui/button'
import { Form } from '@atlas/components/ui/form'
import { Separator } from '@atlas/components/ui/separator'
import {
  FRM_GEOMETRY_TOPOLOGY,
  MODULE_REINFORCEMENT_ARRANGEMENT,
  REINFORCEMENT_ARRANGEMENT,
} from '@atlas/constants/module'
import type { ModuleWithParamsFrcmColumn } from '@atlas/lib/api/modules/schemas/modules'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import { useModuleCalculation } from '@atlas/lib/mutation/modules/use-module-calculation'
import { useProductsByCategory } from '@atlas/lib/query/products/use-products-by-category'
import {
  frcmColumnConfinementReinforcementInput,
  type frcmColumnConfinementReinforcementSchema,
} from '@atlas/types/schemas/masonry/frcm-column-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Loader2Icon } from 'lucide-react'
import Image from 'next/image'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useEffect, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { FrcmColumnConfinementReinforcementCalculationResult } from './frcm-column-confinement-reinforcement-calculation-result'

type Props = {
  module: ModuleWithParamsFrcmColumn
  session: Session
  projectId: Project['id']
}

export const FrcmColumnConfinementReinforcementCalculation = ({
  session,
  module,
  projectId,
}: Props) => {
  const t = useTranslations('forms.calculations.frcm-column')
  const tAction = useTranslations('actions.calculations.frcm-column')
  const tCommon = useTranslations('actions.common')
  const {
    params,
    confinementReinforcementVerifyInput,
    confinementReinforcementCalculationResult,
  } = module
  const form = useForm<frcmColumnConfinementReinforcementSchema>({
    resolver: zodResolver(frcmColumnConfinementReinforcementInput),
    defaultValues: {
      calculationType: 'CONFINEMENT_VERIFY',
      input: {
        product: {
          id: confinementReinforcementVerifyInput?.product.id,
          name: confinementReinforcementVerifyInput?.product.name,
          sourceType:
            confinementReinforcementVerifyInput?.product.id === 'custom'
              ? 'CUSTOM'
              : 'DATABASE',
        },
        reinforcedArrangement:
          confinementReinforcementVerifyInput?.reinforcedArrangement ??
          REINFORCEMENT_ARRANGEMENT.CONTINUE,
        singleWidthBand:
          confinementReinforcementVerifyInput?.singleWidthBand ?? 0,
        stepsOfTheBand:
          confinementReinforcementVerifyInput?.stepsOfTheBand ?? 0,
        clearDistanceBetweenStripes:
          confinementReinforcementVerifyInput?.clearDistanceBetweenStripes ?? 0,
        minimalTransversalDimension:
          confinementReinforcementVerifyInput?.minimalTransversalDimension ?? 0,
        numberOfReinforcementLayers:
          confinementReinforcementVerifyInput?.numberOfReinforcementLayers ?? 0,
        matrixThicknessOfTheSingleLayer:
          confinementReinforcementVerifyInput?.matrixThicknessOfTheSingleLayer ??
          0,
      },
    },
  })

  const { mutate, isPending } = useModuleCalculation(session.accessToken, {
    onSuccess: () => {
      toast.success(tAction('calculate.success'))
    },
    onError: error => {
      console.log('ERROR  ', error)
      toast.error(tAction('calculate.failure', { error: error.message }))
    },
  })

  const handleFormSubmit = (body: frcmColumnConfinementReinforcementSchema) => {
    mutate({ projectId, moduleId: module.id, body })
  }

  const {
    data: products,
    isError: errorGettingProducts,
    isLoading: isLoadingProducts,
  } = useProductsByCategory(session, 'FRCM_COLUMN', 0, 100)

  const productsOptions = [
    ...(products?.content.map(m => ({
      value: m.id,
      label: m.name ?? t('product.unnamed'),
    })) ?? []),
  ]

  const [productId] = form.watch(['input.product.id'])

  const selectedProduct = useMemo(
    () => products?.content.find(p => p.id === productId),
    [productId, products],
  )

  const stepsOfTheBandValue = form.watch('input.stepsOfTheBand') ?? 0
  const singleWidthBandValue = form.watch('input.singleWidthBand') ?? 0
  const reinforcedArrangementValue =
    form.watch('input.reinforcedArrangement') ??
    REINFORCEMENT_ARRANGEMENT.CONTINUE

  const lengthOrDiameterValue =
    params?.geometry?.largerSizeOrColumnDiameter ?? 0
  const smallerSize = params?.geometry?.smallerSize ?? 0
  const topologyValue =
    params?.geometry?.topology ?? FRM_GEOMETRY_TOPOLOGY.RECTANGULAR
  useEffect(() => {
    const clearDistanceBetweenStripes =
      reinforcedArrangementValue === REINFORCEMENT_ARRANGEMENT.CONTINUE
        ? 0
        : stepsOfTheBandValue - singleWidthBandValue
    form.setValue(
      'input.clearDistanceBetweenStripes',
      parseFloat(clearDistanceBetweenStripes.toFixed(2)),
    )

    // =IF(D41="Continuo","-",IF(C8="Rettangolare",MIN(D9,D10),D9))
    const minimalTransversalDimension =
      reinforcedArrangementValue === REINFORCEMENT_ARRANGEMENT.CONTINUE
        ? 0
        : topologyValue === FRM_GEOMETRY_TOPOLOGY.RECTANGULAR
          ? Math.min(lengthOrDiameterValue, smallerSize)
          : lengthOrDiameterValue

    form.setValue(
      'input.minimalTransversalDimension',
      parseFloat(minimalTransversalDimension.toFixed(2)),
    )

    if (!productId) {
      return
    }
    if (productId === 'custom') {
      form.setValue('input.product.sourceType', 'CUSTOM')
    }

    if (selectedProduct) {
      form.setValue('input.product', {
        ...selectedProduct,
        sourceType: 'DATABASE',
      })
    }
  }, [
    form,
    productId,
    selectedProduct,
    stepsOfTheBandValue,
    singleWidthBandValue,
    lengthOrDiameterValue,
    smallerSize,
    topologyValue,
    reinforcedArrangementValue,
  ])

  return (
    <div className="flex flex-col justify-center gap-4">
      <Form {...form}>
        <form
          className="space-y-4 rounded-md border p-4"
          onSubmit={form.handleSubmit(handleFormSubmit)}
        >
          <h1 className="text-3xl font-bold">{t('heading')}</h1>
          <Image
            src="/assets/masonry_frcm/column/FRCM_COLONNE MURATURA.jpg"
            alt="flexural verify"
            height={250}
            width={500}
            className="mx-auto rounded-md object-contain"
            priority
          />
          <h1 className="text-xl font-bold">{t('sub-heading')}</h1>
          <SelectFormInput
            control={form.control}
            name="input.product.id"
            options={productsOptions}
            t={t}
            loading={isLoadingProducts}
            requestError={errorGettingProducts}
            errorMessage={t('products.error')}
          />
          {productId === 'custom' && <CustomProductSection />}
          {selectedProduct && <ProductSummaryCard product={selectedProduct} />}
          <Separator />
          <SelectFormFixedInput
            control={form.control}
            name="input.reinforcedArrangement"
            options={MODULE_REINFORCEMENT_ARRANGEMENT}
            optionLabelFn={p => t(`input.reinforcedArrangement.${p}`)}
            t={t}
          />
          <NumberFormInput
            control={form.control}
            name="input.singleWidthBand"
            t={t}
          />
          <NumberFormInput
            control={form.control}
            name="input.stepsOfTheBand"
            t={t}
          />
          <NumberFormInput
            control={form.control}
            name="input.clearDistanceBetweenStripes"
            t={t}
            disabled={true}
          />
          <NumberFormInput
            control={form.control}
            name="input.minimalTransversalDimension"
            t={t}
            disabled={true}
          />
          <NumberFormInput
            control={form.control}
            name="input.numberOfReinforcementLayers"
            t={t}
          />
          <NumberFormInput
            control={form.control}
            name="input.matrixThicknessOfTheSingleLayer"
            t={t}
          />
          <Button
            type="submit"
            className="w-full sm:w-auto"
            disabled={isPending}
          >
            {isPending && <Loader2Icon className="animate-spin" />}
            {tCommon('calculate')}
          </Button>
        </form>
      </Form>
      {confinementReinforcementCalculationResult && (
        <FrcmColumnConfinementReinforcementCalculationResult
          confinementReinforcementCalculationResult={
            confinementReinforcementCalculationResult
          }
        />
      )}
    </div>
  )
}
