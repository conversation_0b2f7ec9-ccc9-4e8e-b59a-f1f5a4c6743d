import { Badge } from '@atlas/components/ui/badge'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@atlas/components/ui/card'
import { Separator } from '@atlas/components/ui/separator'
import type { SlabInterfaceSlipCalculationResult } from '@atlas/lib/api/modules/schemas/slab-params'
import { cn } from '@atlas/lib/utils'
import { useLocale, useTranslations } from 'next-intl'

interface SlabInterfaceSlipResultCardProps {
  result: SlabInterfaceSlipCalculationResult
}

export function SlabInterfaceSlipResultCard({
  result,
}: SlabInterfaceSlipResultCardProps) {
  const t = useTranslations('forms.calculations.slab.interface-slip-result')
  const locale = useLocale()

  const isVerified = result?.checkResult ?? false

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>{t('title')}</span>
          <Badge
            className={cn(
              'text-base px-3 py-1',
              isVerified ? 'bg-green-600' : 'bg-red-600',
            )}
          >
            {isVerified ? t('verified') : t('not-verified')}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Input Section */}
        {result.inputShearForce !== undefined &&
          result.inputShearForce !== null && (
            <div>
              <span className="text-muted-foreground">
                {t('input-shear-force')}:
              </span>
              <span className="ml-2 font-medium text-lg">
                {result.inputShearForce.toLocaleString(locale, {
                  maximumFractionDigits: 2,
                })}{' '}
                kN
              </span>
            </div>
          )}

        <Separator />

        {/* Calculation Results */}
        <div>
          <h4 className="font-semibold mb-3">{t('calculation-results')}</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            {result.negativeMomentAreaHomogenizationCoefficient !== undefined &&
              result.negativeMomentAreaHomogenizationCoefficient !== null && (
                <div>
                  <span className="text-muted-foreground">
                    {t('homogenization-coefficient')}:
                  </span>
                  <span className="ml-2 font-medium">
                    {result.negativeMomentAreaHomogenizationCoefficient.toFixed(
                      3,
                    )}
                  </span>
                </div>
              )}
            {result.neutralAxisCompressedFlangeDistance !== undefined &&
              result.neutralAxisCompressedFlangeDistance !== null && (
                <div>
                  <span className="text-muted-foreground">
                    {t('neutral-axis-distance')}:
                  </span>
                  <span className="ml-2 font-medium">
                    {result.neutralAxisCompressedFlangeDistance.toLocaleString(
                      locale,
                      { maximumFractionDigits: 2 },
                    )}{' '}
                    mm
                  </span>
                </div>
              )}
            {result.translationalEquilibrium !== undefined &&
              result.translationalEquilibrium !== null && (
                <div>
                  <span className="text-muted-foreground">
                    {t('translational-equilibrium')}:
                  </span>
                  <span className="ml-2 font-medium">
                    {result.translationalEquilibrium.toExponential(4)}
                  </span>
                </div>
              )}
            {result.staticMoment !== undefined &&
              result.staticMoment !== null && (
                <div>
                  <span className="text-muted-foreground">
                    {t('static-moment')}:
                  </span>
                  <span className="ml-2 font-medium">
                    {result.staticMoment.toLocaleString(locale, {
                      maximumFractionDigits: 2,
                    })}{' '}
                    mm³
                  </span>
                </div>
              )}
            {result.neutralAxisInertiaMoment !== undefined &&
              result.neutralAxisInertiaMoment !== null && (
                <div>
                  <span className="text-muted-foreground">
                    {t('inertia-moment')}:
                  </span>
                  <span className="ml-2 font-medium">
                    {result.neutralAxisInertiaMoment.toExponential(4)} mm⁴
                  </span>
                </div>
              )}
          </div>
        </div>

        <Separator />

        {/* Interface Verification */}
        <div>
          <h4 className="font-semibold mb-3">{t('interface-verification')}</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            {result.interfaceShearStress !== undefined &&
              result.interfaceShearStress !== null && (
                <div>
                  <span className="text-muted-foreground">
                    {t('interface-shear-stress')}:
                  </span>
                  <span className="ml-2 font-medium text-lg">
                    {result.interfaceShearStress.toFixed(3)} MPa
                  </span>
                </div>
              )}
            {result.frcBondStrength !== undefined &&
              result.frcBondStrength !== null && (
                <div>
                  <span className="text-muted-foreground">
                    {t('frc-bond-strength')}:
                  </span>
                  <span className="ml-2 font-medium text-lg">
                    {result.frcBondStrength.toFixed(3)} MPa
                  </span>
                </div>
              )}
            {result.concreteToConcreteFrictionCoefficient !== undefined &&
              result.concreteToConcreteFrictionCoefficient !== null && (
                <div>
                  <span className="text-muted-foreground">
                    {t('friction-coefficient')}:
                  </span>
                  <span className="ml-2 font-medium">
                    {result.concreteToConcreteFrictionCoefficient.toFixed(2)}
                  </span>
                </div>
              )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
