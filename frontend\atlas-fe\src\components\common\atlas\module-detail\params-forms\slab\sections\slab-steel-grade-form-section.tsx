import { NumberFormInput } from '@atlas/components/common/form/number-form-input'
import { SelectFormInput } from '@atlas/components/common/form/select-form-input'
import { StringFormInput } from '@atlas/components/common/form/string-form-input'
import { moduleMaterialKnowledgeLevelValues } from '@atlas/constants/module'
import {
  calculateSlabDesignYieldStrengthForBrittleMechanisms,
  calculateSlabDesignYieldStrengthForDuctileMechanisms,
} from '@atlas/functions/forms/slab-form-calculations'
import { useSteelGradesMaterials } from '@atlas/lib/query/materials/use-steel-grades-materials'
import type { SlabMaterialFormInputs } from '@atlas/types/schemas/slab-form'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useEffect, useMemo } from 'react'
import { useFormContext } from 'react-hook-form'

export function SlabSteelGradeFormSection({ session }: { session: Session }) {
  const t = useTranslations('forms.project-params.slab.materialProperties')
  const form = useFormContext<SlabMaterialFormInputs>()

  const {
    data: steelMaterials,
    isError: steelMaterialsError,
    isLoading: steelMaterialsLoading,
  } = useSteelGradesMaterials({ session, body: { size: 100 } })

  const customOption = { value: 'custom', label: t('steelGrade.custom') }
  const steelMaterialsOptions = [
    ...(steelMaterials?.content.map(m => ({ value: m.id, label: m.name })) ??
      []),
    customOption,
  ]

  const steelGradeKnowledgeLevel = form.watch('steelGradeKnowledgeLevel')
  const steelGradeId = form.watch('steelGrade.id')
  const yieldStrength = form.watch('steelGrade.yieldStrength')

  const isCustomSelected = steelGradeId === 'custom'

  const selectedSteelMaterial = useMemo(
    () => steelMaterials?.content.find(m => m.id === steelGradeId),
    [steelGradeId, steelMaterials],
  )

  useEffect(() => {
    if (!selectedSteelMaterial || isCustomSelected) {
      return
    }
    const selected = selectedSteelMaterial
    if (form.getValues('steelGrade.name') !== selected.name) {
      form.setValue('steelGrade.name', selected.name)
    }
    if (form.getValues('steelGrade.yieldStrength') !== selected.yieldStrength) {
      form.setValue('steelGrade.yieldStrength', selected.yieldStrength)
    }
    if (
      form.getValues('steelGrade.tensileStrength') !== selected.tensileStrength
    ) {
      form.setValue('steelGrade.tensileStrength', selected.tensileStrength)
    }
    if (
      form.getValues('steelGrade.elongationPercentage') !==
      selected.elongationPercentage
    ) {
      form.setValue(
        'steelGrade.elongationPercentage',
        selected.elongationPercentage,
      )
    }
    if (
      form.getValues('steelGrade.elasticModulus') !== selected.elasticModulus
    ) {
      form.setValue('steelGrade.elasticModulus', selected.elasticModulus)
    }
  }, [form, isCustomSelected, selectedSteelMaterial])

  useEffect(() => {
    if (
      steelGradeKnowledgeLevel &&
      yieldStrength !== undefined &&
      yieldStrength !== null
    ) {
      const confidenceFactor =
        moduleMaterialKnowledgeLevelValues[steelGradeKnowledgeLevel]
      form.setValue(
        'steelGrade.designYieldStrengthForDuctileMechanisms',
        calculateSlabDesignYieldStrengthForDuctileMechanisms(
          yieldStrength,
          confidenceFactor,
        ),
      )
      form.setValue(
        'steelGrade.designYieldStrengthForBrittleMechanisms',
        calculateSlabDesignYieldStrengthForBrittleMechanisms(
          yieldStrength,
          confidenceFactor,
        ),
      )
    }
  }, [steelGradeKnowledgeLevel, yieldStrength, form])

  return (
    <>
      <SelectFormInput
        control={form.control}
        name="steelGrade.id"
        options={steelMaterialsOptions}
        t={t}
        loading={steelMaterialsLoading}
        requestError={steelMaterialsError}
        errorMessage={t('steelGrade.error')}
      />
      {isCustomSelected && (
        <StringFormInput control={form.control} name="steelGrade.name" t={t} />
      )}
      <NumberFormInput
        control={form.control}
        name="steelGrade.yieldStrength"
        t={t}
        disabled={!isCustomSelected}
      />
      <NumberFormInput
        control={form.control}
        name="steelGrade.elasticModulus"
        t={t}
        disabled={!isCustomSelected}
      />
      <NumberFormInput
        control={form.control}
        name="steelGrade.designYieldStrengthForDuctileMechanisms"
        t={t}
        disabled={true}
      />
      <NumberFormInput
        control={form.control}
        name="steelGrade.designYieldStrengthForBrittleMechanisms"
        t={t}
        disabled={true}
      />
    </>
  )
}
