import { WoodFlexuralCalculation } from '@atlas/components/common/atlas/module-detail/params-forms/wood/calculations/wood-flexural-calculation'
import { WoodShearCalculation } from '@atlas/components/common/atlas/module-detail/params-forms/wood/calculations/wood-shear-calculation'
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Ta<PERSON><PERSON>ist,
  TabsTrigger,
} from '@atlas/components/ui/tabs'
import type { ModuleWithParamsWood } from '@atlas/lib/api/modules/schemas/modules'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'

type Props = {
  module: ModuleWithParamsWood
  session: Session
  projectId: string
}

export const WoodCalculations = ({ module, session, projectId }: Props) => {
  const t = useTranslations('forms.project-params.wood')

  return (
    <>
      <h3 className="text-lg font-medium py-4">{t('calculation.title')}</h3>
      <Tabs defaultValue="flexural">
        <TabsList>
          <TabsTrigger value="flexural">{t('flexural.label')}</TabsTrigger>
          <TabsTrigger value="shear">{t('shear.label')}</TabsTrigger>
          <TabsTrigger value="confinement">{t('confinemet.label')}</TabsTrigger>
        </TabsList>
        <TabsContent value="flexural">
          <WoodFlexuralCalculation
            session={session}
            module={module}
            projectId={projectId}
          />
        </TabsContent>
        <TabsContent value="shear">
          <WoodShearCalculation
            session={session}
            module={module}
            projectId={projectId}
          />
        </TabsContent>
        <TabsContent value="confinement" />
      </Tabs>
    </>
  )
}
