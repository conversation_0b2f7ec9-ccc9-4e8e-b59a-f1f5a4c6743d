import { NumberFormInput } from '@atlas/components/common/form/number-form-input'
import { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'
import { Button } from '@atlas/components/ui/button'
import { Form } from '@atlas/components/ui/form'
import {
  CRM_MASONRY_TYPE,
  characteristicCompressiveStrengthValues,
  characteristicNormalElasticityModulusValues,
  characteristicShearElasticityModulusValues,
  characteristicShearStrengthValues,
  executionClass,
  FACING_MATERIAL,
  loadResistingCategory,
  MODULE_CRM_MASONRY_TYPE,
  MODULE_EXECUTION_CLASS,
  MODULE_FACING_MATERIAL,
  MODULE_GEOMETRY_EXPOSURE,
  MODULE_LOAD_RESISTING_CATEGORY,
  MODULE_MATERIAL_KNOWLEDGE_LEVEL,
  masonryStrengthSafetyFactorMapping,
  moduleGeometryExposure,
  moduleMaterialKnowledgeLevel,
  moduleMaterialKnowledgeLevelValues,
} from '@atlas/constants/module'
import type { Module } from '@atlas/lib/api/modules/schemas/modules'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'
import {
  type MaschiMurariMaterialPropertiesSchemaInputs,
  type MaschiMurariParamsSchemaInputs,
  maschiMurariMaterialPropertiesSchema,
} from '@atlas/types/schemas/masonry/maschi-murari-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useQueryClient } from '@tanstack/react-query'
import { Loader2Icon } from 'lucide-react'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useCallback, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'

type Props = {
  session: Session
  projectId: Project['id']
  moduleId: Module['id']
  defaultValues?: Partial<MaschiMurariMaterialPropertiesSchemaInputs>
  params: MaschiMurariParamsSchemaInputs
  setParams: (newParams: any) => void
  onSave: () => void
}

export const MaschiMurariMaterialPropertiesForm = ({
  session,
  projectId,
  moduleId,
  defaultValues,
  params,
  setParams,
  onSave,
}: Props) => {
  const t = useTranslations(
    'forms.project-params.maschi-murari.material-properties',
  )
  const tAction = useTranslations('actions.save-project-params.messages')
  const tCommon = useTranslations('actions.common')
  const queryClient = useQueryClient()

  const form = useForm<MaschiMurariMaterialPropertiesSchemaInputs>({
    resolver: zodResolver(maschiMurariMaterialPropertiesSchema),
    defaultValues: {
      structuralElementsNature:
        defaultValues?.structuralElementsNature ?? FACING_MATERIAL.BRICK,
      masonryType:
        defaultValues?.masonryType ??
        CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA,
      knowledgeLevel:
        defaultValues?.knowledgeLevel ?? moduleMaterialKnowledgeLevel.LC1,
      confidenceFactor: defaultValues?.confidenceFactor ?? 0,
      executionClass: defaultValues?.executionClass ?? executionClass.ONE,
      loadResistantCategory:
        defaultValues?.loadResistantCategory ??
        loadResistingCategory.MASONRY_CAT_I_AND_MORTAR_WITH_GUARANTEED_PERFORMANCE,
      masonrySafetyFactor: defaultValues?.masonrySafetyFactor ?? 0,
      characteristicCompressiveStrength:
        defaultValues?.characteristicCompressiveStrength ?? 0,
      designCompressiveStrength: defaultValues?.designCompressiveStrength ?? 0,
      characteristicShearStrength:
        defaultValues?.characteristicShearStrength ?? 0,
      designShearStrength: defaultValues?.designShearStrength ?? 0,
      elasticModulus: defaultValues?.elasticModulus ?? 0,
      shearModulus: defaultValues?.shearModulus ?? 0,
      ultimateCompressiveStrainLinearBehaviour:
        defaultValues?.ultimateCompressiveStrainLinearBehaviour ?? 0,
      ultimateCompressiveStrain:
        defaultValues?.ultimateCompressiveStrain ?? 0.0035,
      exposure: defaultValues?.exposure ?? moduleGeometryExposure.INTERNAL,
      conversionFactor: defaultValues?.conversionFactor ?? 0,
    },
  })

  const { mutate, isPending } = useSaveModuleParamsMutation(
    session.accessToken,
    {
      onSuccess: async () => {
        toast.success(tAction('edit.success'))
        await queryClient.invalidateQueries({
          queryKey: ['modules', { projectId, moduleId }],
        })
        onSave()
      },
      onError: error => {
        toast.error(tAction('edit.failure', { error: error.message }))
      },
    },
  )

  const handleFormSubmit = useCallback(
    (body: MaschiMurariMaterialPropertiesSchemaInputs) => {
      // construct body based on global schema:
      const maschiMurariParams: MaschiMurariParamsSchemaInputs = {
        ...params,
        materialProperties: body,
      }
      mutate({ projectId, moduleId, body: maschiMurariParams })
    },
    [mutate, projectId, moduleId, params],
  )

  const knowledgeMaterialLevel =
    form.watch('knowledgeLevel') ?? moduleMaterialKnowledgeLevel.LC1
  const executionClassFormValue =
    form.watch('executionClass') ?? executionClass.ONE
  const loadResistingCategoryFormValue =
    form.watch('loadResistantCategory') ??
    loadResistingCategory.MASONRY_CAT_I_AND_MORTAR_WITH_GUARANTEED_PERFORMANCE
  const masonryTypeValue =
    form.watch('masonryType') ??
    CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA
  const exposureValue =
    form.watch('exposure') ?? moduleGeometryExposure.INTERNAL

  useEffect(() => {
    const calculateGivenStrength = (strengthMinMax: {
      min: number
      max: number
    }) => {
      return knowledgeMaterialLevel === moduleMaterialKnowledgeLevel.LC1
        ? strengthMinMax.min
        : knowledgeMaterialLevel === moduleMaterialKnowledgeLevel.LC3
          ? strengthMinMax.max
          : (strengthMinMax.min + strengthMinMax.max) / 2
    }

    const confidenceFactor =
      moduleMaterialKnowledgeLevelValues[knowledgeMaterialLevel]
    form.setValue('confidenceFactor', confidenceFactor)

    const masonryStrengthSafetyFactor =
      masonryStrengthSafetyFactorMapping[loadResistingCategoryFormValue][
        executionClassFormValue
      ]
    form.setValue('masonrySafetyFactor', masonryStrengthSafetyFactor)

    const characteristicCompressiveStrengthMinMax =
      characteristicCompressiveStrengthValues[masonryTypeValue]

    const characteristicCompressiveStrength = calculateGivenStrength(
      characteristicCompressiveStrengthMinMax,
    )
    form.setValue(
      'characteristicCompressiveStrength',
      characteristicCompressiveStrength,
    )

    const designCompressiveStrength =
      characteristicCompressiveStrength /
      masonryStrengthSafetyFactor /
      confidenceFactor
    form.setValue('designCompressiveStrength', designCompressiveStrength)

    const characteristicsShearStrengthMinMax =
      characteristicShearStrengthValues[masonryTypeValue]
    const characteristicShearStrength = calculateGivenStrength(
      characteristicsShearStrengthMinMax,
    )
    form.setValue('characteristicShearStrength', characteristicShearStrength)

    const designShearStrength =
      characteristicShearStrength /
      confidenceFactor /
      masonryStrengthSafetyFactor
    form.setValue('designShearStrength', designShearStrength)

    const normalElasticityModulusMinMax =
      characteristicNormalElasticityModulusValues[masonryTypeValue]
    const normalElasticityModulus =
      (normalElasticityModulusMinMax.min + normalElasticityModulusMinMax.max) /
      2
    form.setValue('elasticModulus', normalElasticityModulus)

    const shearElasticityModulusMinMax =
      characteristicShearElasticityModulusValues[masonryTypeValue]
    const shearElasticityModulus =
      (shearElasticityModulusMinMax.min + shearElasticityModulusMinMax.max) / 2
    form.setValue('shearModulus', shearElasticityModulus)

    // =D19/D22
    const ultimateCompressiveStrainLinearBehaviour =
      designCompressiveStrength / normalElasticityModulus
    form.setValue(
      'ultimateCompressiveStrainLinearBehaviour',
      ultimateCompressiveStrainLinearBehaviour,
    )

    const ultimateCompressiveStrain = 0.0035
    form.setValue('ultimateCompressiveStrain', ultimateCompressiveStrain)

    const conversionFactor =
      exposureValue === moduleGeometryExposure.INTERNAL
        ? 0.9
        : exposureValue === moduleGeometryExposure.EXTERNAL
          ? 0.8
          : 0.7
    form.setValue('conversionFactor', conversionFactor)
  }, [
    form,
    knowledgeMaterialLevel,
    executionClassFormValue,
    loadResistingCategoryFormValue,
    masonryTypeValue,
    exposureValue,
  ])

  useEffect(() => {
    const subscription = form.watch(values => {
      setParams((prev: any) => ({
        ...prev,
        buildingCharacteristics: values,
      }))
    })
    return () => subscription.unsubscribe()
  }, [form, setParams])

  return (
    <Form {...form}>
      <form
        className="space-y-4 rounded-md border p-4"
        onSubmit={form.handleSubmit(handleFormSubmit)}
      >
        <SelectFormFixedInput
          control={form.control}
          name="structuralElementsNature"
          options={MODULE_FACING_MATERIAL}
          optionLabelFn={p => t(`structuralElementsNature.${p}`)}
          t={t}
        />
        <SelectFormFixedInput
          control={form.control}
          name="masonryType"
          options={MODULE_CRM_MASONRY_TYPE}
          optionLabelFn={p => t(`masonryType.${p}`)}
          t={t}
        />
        <SelectFormFixedInput
          control={form.control}
          name="knowledgeLevel"
          options={MODULE_MATERIAL_KNOWLEDGE_LEVEL}
          optionLabelFn={p => t(`knowledgeLevel.${p}`)}
          t={t}
        />
        <NumberFormInput
          control={form.control}
          name="confidenceFactor"
          t={t}
          disabled={true}
        />
        <SelectFormFixedInput
          control={form.control}
          name="executionClass"
          options={MODULE_EXECUTION_CLASS}
          optionLabelFn={p => t(`executionClass.${p}`)}
          t={t}
        />
        <SelectFormFixedInput
          control={form.control}
          name="loadResistantCategory"
          options={MODULE_LOAD_RESISTING_CATEGORY}
          optionLabelFn={p => t(`loadResistantCategory.${p}`)}
          t={t}
        />
        <NumberFormInput
          control={form.control}
          name="masonrySafetyFactor"
          t={t}
          disabled={true}
        />
        <NumberFormInput
          control={form.control}
          name="characteristicCompressiveStrength"
          t={t}
          disabled={true}
        />
        <NumberFormInput
          control={form.control}
          name="designCompressiveStrength"
          t={t}
          disabled={true}
        />
        <NumberFormInput
          control={form.control}
          name="characteristicShearStrength"
          t={t}
          disabled={true}
        />
        <NumberFormInput
          control={form.control}
          name="designShearStrength"
          t={t}
          disabled={true}
        />
        <NumberFormInput
          control={form.control}
          name="elasticModulus"
          t={t}
          disabled={true}
        />
        <NumberFormInput
          control={form.control}
          name="shearModulus"
          t={t}
          disabled={true}
        />
        <NumberFormInput
          control={form.control}
          name="ultimateCompressiveStrainLinearBehaviour"
          t={t}
          disabled={true}
        />
        <NumberFormInput
          control={form.control}
          name="ultimateCompressiveStrain"
          t={t}
          disabled={true}
        />
        <SelectFormFixedInput
          control={form.control}
          name="exposure"
          options={MODULE_GEOMETRY_EXPOSURE}
          optionLabelFn={p => t(`exposure.${p}`)}
          t={t}
        />
        <NumberFormInput
          control={form.control}
          name="conversionFactor"
          t={t}
          disabled={true}
        />
        <Button
          type="submit"
          className="w-full sm:w-auto"
          disabled={isPending}
          onClick={form.handleSubmit(handleFormSubmit)}
        >
          {isPending && <Loader2Icon className="animate-spin" />}
          {tCommon('save')}
        </Button>
      </form>
    </Form>
  )
}
