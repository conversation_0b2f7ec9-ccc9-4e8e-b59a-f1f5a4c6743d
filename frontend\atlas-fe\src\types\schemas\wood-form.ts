import {
  MODULE_GEOMETRY_EXPOSURE,
  MODULE_MATERIAL_KNOWLEDGE_LEVEL,
} from '@atlas/constants/module'
import {
  woodGeometryPropertiesSchema,
  woodMaterialPropertiesSchema,
  woodPreInterventionCheckSchema,
} from '@atlas/lib/api/modules/schemas/wood-params'
import { productFormSchema } from '@atlas/types/schemas/product-form-schema'
import { z } from 'zod'

export const woodGeneralSchema = z.object({
  initialDeformation: z.number(),
})

export const woodGeometrySchema = z.object({
  width: z.number().positive(),
  height: z.number().positive(),
  topConcreteCover: z.number().positive(),
  bottomConcreteCover: z.number().positive(),
  effectiveDepth: z.number(),
  exposure: z.enum(MODULE_GEOMETRY_EXPOSURE),
})

export const woodReinforcementSchema = z.object({
  top: z.object({
    diameter: z.number().positive(),
    quantity: z.number().positive(),
    area: z.number(),
  }),
  bottom: z.object({
    diameter: z.number().positive(),
    quantity: z.number().positive(),
    area: z.number(),
  }),
  transverse: z.object({
    diameter: z.number().positive(),
    legs: z.number().positive(),
    area: z.number(),
    stirrupSpacing: z.number().positive(),
    stirrupInclination: z.number().min(-360).max(360),
    cornerRadius: z.number().positive(),
  }),
})

export const woodMaterialSchema = z.object({
  knowledgeLevel: z.enum(MODULE_MATERIAL_KNOWLEDGE_LEVEL),
  confidenceFactor: z.number().optional(),
  concreteClass: z.object({
    id: z.string(),
    name: z.string(),
    cubeCompressiveStrength: z.number(),
    cylinderCompressiveStrength: z.number(),
    averageCompressiveStrength: z.number(),
    averageTensileStrength: z.number(),
    elasticModulus: z.number(),
    designCompressiveStrengthForBrittleMechanisms: z.number(),
    designCompressiveStrengthForDuctileMechanisms: z.number(),
  }),
  steelGrade: z.object({
    id: z.string(),
    name: z.string(),
    yieldStrength: z.number(),
    tensileStrength: z.number(),
    elongationPercentage: z.number(),
    elasticModulus: z.number(),
    designYieldStrengthForBrittleMechanisms: z.number(),
    designYieldStrengthForDuctileMechanisms: z.number(),
  }),
})

// Use the API schema directly for consistency
export const woodMaterialSchema1 = woodMaterialPropertiesSchema.extend({
  id: z.string().optional(), // Add optional id field for form usage
})

export const woodParamsCheckSchema = z.object({
  initialDeformation: z.number(),
  geometry: woodGeometryPropertiesSchema,
  materialProperties: woodMaterialSchema1, // Wood material properties are in materialProperties
  preIntervationCheck: woodPreInterventionCheckSchema.optional(),
})

// Schema for wood post-intervention check results
export const woodPostInterventionResultSchema = z.object({
  maximumBendingMoment: z.number(),
  maximumShearForce: z.number(),
  designBendingStress: z.number(),
  designBendingStrength: z.number(),
  designBendingCheck: z.number(),
  designShearStress: z.number(),
  designShearStrength: z.number(),
  designShearCheck: z.number(),
  permanentLoadPerLinearMeter: z.number(),
  imposedLoadPerLinearMeter: z.number(),
  instantaneousDeflectionPermanentLoad: z.number(),
  instantaneousDeflectionImposedLoad: z.number(),
  instantaneousDeflectionTotalLoads: z.number(),
  instantaneousDeflectionCheck: z.number(),
  combinationFactor: z.number(),
  finalDeflectionTotalLoads: z.number(),
  finalCheckResult: z.number(),
})

// Schema to check if wood post-intervention calculation has been completed
// Used to enable the report generation button
export const woodCalculationCheck = z.object({
  params: z.object({
    postIntervationCheck: z.object({
      resultOfPostIntervationCheck: woodPostInterventionResultSchema,
    }).optional(),
  }).optional(),
}).refine(
  data => data.params?.postIntervationCheck?.resultOfPostIntervationCheck !== undefined,
  {
    message: 'Post-intervention calculation must be completed to generate a report',
  },
)

export const woodFlexuralCalculationSchema = z.object({
  calculationType: z.literal('FLEXURAL_VERIFY'),
  input: z.object({
    stripWidth: z.number(),
    layersNumber: z.number(),
    bendingMoment: z.number(),
    product: productFormSchema,
  }),
})

export const woodShearCalculationSchema = z.object({
  calculationType: z.literal('SHEAR_VERIFY'),
  input: z.object({
    product: productFormSchema,
  }),
})

export type WoodGeneralFormInputs = z.infer<typeof woodGeneralSchema>
export type WoodGeometryFormInputs = z.infer<typeof woodMaterialSchema1>
export type WoodReinforcementFormInputs = z.infer<
  typeof woodReinforcementSchema
>
export type WoodMaterialFormInputs = z.infer<typeof woodMaterialSchema>
export type WoodFlexuralCalculationInput = z.infer<
  typeof woodFlexuralCalculationSchema
>
export type WoodShearCalculationInput = z.infer<
  typeof woodShearCalculationSchema
>
