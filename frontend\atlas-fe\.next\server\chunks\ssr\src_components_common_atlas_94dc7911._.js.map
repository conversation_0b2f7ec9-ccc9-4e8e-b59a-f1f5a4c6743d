{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/error-alert.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { Alert, AlertDescription, AlertTitle } from '@atlas/components/ui/alert'\r\nimport type { ApiError, ValidationError } from '@atlas/types'\r\nimport { AlertCircle } from 'lucide-react'\r\nimport { signIn } from 'next-auth/react'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  error: ApiError | ValidationError | Error\r\n}\r\n\r\nexport const ErrorAlert = ({ error }: Props) => {\r\n  const t = useTranslations()\r\n\r\n  if (error instanceof Error) {\r\n    return (\r\n      <Alert variant=\"destructive\" className=\"gap-3\">\r\n        <AlertCircle className=\"h-4 w-4\" />\r\n        <AlertTitle>{t('errors.generic.title')}</AlertTitle>\r\n        <AlertDescription>{t('errors.generic.description')}</AlertDescription>\r\n        {process.env.NODE_ENV === 'development' && (\r\n          <AlertDescription>{error.message}</AlertDescription>\r\n        )}\r\n      </Alert>\r\n    )\r\n  }\r\n\r\n  if (error.type === 'ValidationError') {\r\n    return (\r\n      <Alert variant=\"destructive\" className=\"gap-3\">\r\n        <AlertCircle className=\"h-4 w-4\" />\r\n        <AlertTitle>{t(`errors.validation.${error.code}.title`)}</AlertTitle>\r\n        <AlertDescription>\r\n          {t(`errors.validation.${error.code}.description`)}\r\n        </AlertDescription>\r\n        {process.env.NODE_ENV === 'development' && (\r\n          <AlertDescription>{error.message}</AlertDescription>\r\n        )}\r\n      </Alert>\r\n    )\r\n  }\r\n\r\n  if (error.code === '401_UNAUTHORIZED') {\r\n    return signIn()\r\n  }\r\n\r\n  return (\r\n    <Alert variant=\"destructive\" className=\"gap-3\">\r\n      <AlertCircle className=\"h-4 w-4\" />\r\n      <AlertTitle>{t(`errors.api.${error.code}.title`)}</AlertTitle>\r\n      {t.has(`errors.api.${error.code}.description`) && (\r\n        <AlertDescription>\r\n          {t(`errors.api.${error.code}.description`)}\r\n        </AlertDescription>\r\n      )}\r\n    </Alert>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;AACA;AANA;;;;;;AAYO,MAAM,aAAa,CAAC,EAAE,KAAK,EAAS;IACzC,MAAM,IAAI,IAAA,yNAAe;IAEzB,IAAI,iBAAiB,OAAO;QAC1B,qBACE,8OAAC,0IAAK;YAAC,SAAQ;YAAc,WAAU;;8BACrC,8OAAC,mOAAW;oBAAC,WAAU;;;;;;8BACvB,8OAAC,+IAAU;8BAAE,EAAE;;;;;;8BACf,8OAAC,qJAAgB;8BAAE,EAAE;;;;;;gBACpB,oDAAyB,+BACxB,8OAAC,qJAAgB;8BAAE,MAAM,OAAO;;;;;;;;;;;;IAIxC;IAEA,IAAI,MAAM,IAAI,KAAK,mBAAmB;QACpC,qBACE,8OAAC,0IAAK;YAAC,SAAQ;YAAc,WAAU;;8BACrC,8OAAC,mOAAW;oBAAC,WAAU;;;;;;8BACvB,8OAAC,+IAAU;8BAAE,EAAE,CAAC,kBAAkB,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC;;;;;;8BACtD,8OAAC,qJAAgB;8BACd,EAAE,CAAC,kBAAkB,EAAE,MAAM,IAAI,CAAC,YAAY,CAAC;;;;;;gBAEjD,oDAAyB,+BACxB,8OAAC,qJAAgB;8BAAE,MAAM,OAAO;;;;;;;;;;;;IAIxC;IAEA,IAAI,MAAM,IAAI,KAAK,oBAAoB;QACrC,OAAO,IAAA,+IAAM;IACf;IAEA,qBACE,8OAAC,0IAAK;QAAC,SAAQ;QAAc,WAAU;;0BACrC,8OAAC,mOAAW;gBAAC,WAAU;;;;;;0BACvB,8OAAC,+IAAU;0BAAE,EAAE,CAAC,WAAW,EAAE,MAAM,IAAI,CAAC,MAAM,CAAC;;;;;;YAC9C,EAAE,GAAG,CAAC,CAAC,WAAW,EAAE,MAAM,IAAI,CAAC,YAAY,CAAC,mBAC3C,8OAAC,qJAAgB;0BACd,EAAE,CAAC,WAAW,EAAE,MAAM,IAAI,CAAC,YAAY,CAAC;;;;;;;;;;;;AAKnD", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/header-skeleton.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { Skeleton } from '@atlas/components/ui/skeleton' // For loading state\r\n\r\nexport const HeaderSkeleton = () => (\r\n  <div className=\"mb-6 border-b pb-4\">\r\n    <Skeleton className=\"mb-2 h-8 w-3/4 md:w-1/2\" />\r\n    <div className=\"flex flex-wrap items-center gap-x-4 gap-y-1\">\r\n      <Skeleton className=\"h-5 w-20\" />\r\n      <Skeleton className=\"h-5 w-24\" />\r\n      <Skeleton className=\"h-5 w-32\" />\r\n      <Skeleton className=\"h-5 w-40\" />\r\n      <Skeleton className=\"h-5 w-28\" />\r\n    </div>\r\n  </div>\r\n)\r\n"], "names": [], "mappings": ";;;;;AAEA,uOAAyD,oBAAoB;AAF7E;;;AAIO,MAAM,iBAAiB,kBAC5B,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gJAAQ;gBAAC,WAAU;;;;;;0BACpB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gJAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC,gJAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC,gJAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC,gJAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC,gJAAQ;wBAAC,WAAU", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/product/custom-product-section.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { PRODUCT_FIBER_TYPE } from '@atlas/constants/product'\r\nimport type { RectangularBeamFlexuralCalculationInput } from '@atlas/types/schemas/rectangular-beam-from'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useFormContext } from 'react-hook-form'\r\n\r\nexport const CustomProductSection = () => {\r\n  const form = useFormContext<RectangularBeamFlexuralCalculationInput>()\r\n  const t = useTranslations('forms.calculations.rectangular-beam')\r\n\r\n  return (\r\n    <>\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"input.product.thickness\"\r\n        t={t}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"input.product.tensileStrength\"\r\n        t={t}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"input.product.elasticModulus\"\r\n        t={t}\r\n      />\r\n      <SelectFormFixedInput\r\n        control={form.control}\r\n        name=\"input.product.fiberType\"\r\n        options={PRODUCT_FIBER_TYPE}\r\n        optionLabelFn={p => t(`fiberType.${p}`)}\r\n        t={t}\r\n      />\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;AACA;;;;;;;AAEO,MAAM,uBAAuB;IAClC,MAAM,OAAO,IAAA,gLAAc;IAC3B,MAAM,IAAI,IAAA,yNAAe,EAAC;IAE1B,qBACE;;0BACE,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;;;;;;0BAEL,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;;;;;;0BAEL,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;;;;;;0BAEL,8OAAC,gMAAoB;gBACnB,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,SAAS,kJAAkB;gBAC3B,eAAe,CAAA,IAAK,EAAE,CAAC,UAAU,EAAE,GAAG;gBACtC,GAAG;;;;;;;;AAIX", "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/product/product-summary-card.tsx"], "sourcesContent": ["import { FACING_MATERIAL } from '@atlas/constants/module'\r\nimport type { Product } from '@atlas/lib/api/products/schemas/product'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ninterface Props {\r\n  product: Product\r\n  facingMaterial?: FACING_MATERIAL\r\n  isRectangularBeam?: boolean\r\n}\r\n\r\nexport const ProductSummaryCard = ({\r\n  product,\r\n  facingMaterial = undefined,\r\n  isRectangularBeam = true,\r\n}: Props) => {\r\n  const t = useTranslations('components.common.atlas.product')\r\n\r\n  const tForm = useTranslations(\r\n    `forms.calculations${isRectangularBeam ? '' : '.rectangular-beam'}`,\r\n  )\r\n\r\n  return (\r\n    <div className=\"rounded-md border p-2 my-2 bg-gray-50 text-gray-900 dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700 text-sm\">\r\n      <div className=\"font-semibold mb-1\">{t('summary')}</div>\r\n      <ul className=\"list-disc pl-4\">\r\n        {product.fiberType && (\r\n          <li>\r\n            {t('fiberType')}: {tForm(`fiberType.${product.fiberType}`)}\r\n          </li>\r\n        )}\r\n        {product.orientation && (\r\n          <li>\r\n            {t('orientation')}: {tForm(`orientation.${product.orientation}`)}\r\n          </li>\r\n        )}\r\n        {product.thickness && (\r\n          <li>\r\n            {t('thickness')}: {product.thickness}\r\n          </li>\r\n        )}\r\n        {product.availableWidths && (\r\n          <li>\r\n            {t('availableWidths')}: {product.availableWidths.join(', ')}\r\n          </li>\r\n        )}\r\n        {product.elasticModulus && (\r\n          <li>\r\n            {t('elasticModulus')}: {product.elasticModulus}\r\n          </li>\r\n        )}\r\n        {product.cylindricCompressiveStrength && (\r\n          <li>\r\n            {t('cylindricCompressiveStrength')}:{' '}\r\n            {product.cylindricCompressiveStrength}\r\n          </li>\r\n        )}\r\n        {facingMaterial &&\r\n          product.facingPerformance &&\r\n          product.facingPerformance[facingMaterial] && (\r\n            <li>\r\n              {t('facingPerformance')} ({t(`facingMaterial.${facingMaterial}`)}\r\n              ): {product.facingPerformance[facingMaterial]}\r\n            </li>\r\n          )}\r\n        {product.productType && (\r\n          <li>\r\n            {t('productType')}: {product.productType}\r\n          </li>\r\n        )}\r\n        {product.ultimateStrain && (\r\n          <li>\r\n            {t('ultimateStrain')}: {product.ultimateStrain}\r\n          </li>\r\n        )}\r\n        {product.designDeformation && (\r\n          <li>\r\n            {t('designDeformation')}: {product.designDeformation}\r\n          </li>\r\n        )}\r\n        {product.unitStrengthOfTheMesh && (\r\n          <li>\r\n            {t('unitStrengthOfTheMesh')}: {product.unitStrengthOfTheMesh}\r\n          </li>\r\n        )}\r\n        {product.reinforcementTensileStrength && (\r\n          <li>\r\n            {t('reinforcementTensileStrength')}:{' '}\r\n            {product.reinforcementTensileStrength}\r\n          </li>\r\n        )}\r\n        {product.reinforcementUltimateStrain && (\r\n          <li>\r\n            {t('reinforcementUltimateStrain')}:{' '}\r\n            {product.reinforcementUltimateStrain}\r\n          </li>\r\n        )}\r\n        {product.matrixBreachStress && (\r\n          <li>\r\n            {t('matrixBreachStress')}: {product.matrixBreachStress}\r\n          </li>\r\n        )}\r\n        {/* Calculated unitStrengthOfTheMesh */}\r\n        {product.productType === 'MESH' && (\r\n          <li>\r\n            {t('unitStrengthOfTheMesh')}:{' '}\r\n            {(product.name === 'KIMITECH_BS_ST_200' ||\r\n              product.name === 'KIMITECH_BS_ST_400') &&\r\n            facingMaterial\r\n              ? (() => {\r\n                  let value = 0\r\n                  if (product.name === 'KIMITECH_BS_ST_200') {\r\n                    if (facingMaterial === FACING_MATERIAL.BRICK) {\r\n                      value = 1182\r\n                    } else if (facingMaterial === FACING_MATERIAL.TUFF) {\r\n                      value = 1165\r\n                    } else {\r\n                      value = 1126\r\n                    }\r\n                  } else {\r\n                    if (facingMaterial === FACING_MATERIAL.BRICK) {\r\n                      value = 1107\r\n                    } else if (facingMaterial === FACING_MATERIAL.TUFF) {\r\n                      value = 1072\r\n                    } else {\r\n                      value = 1066\r\n                    }\r\n                  }\r\n                  return value\r\n                })()\r\n              : product.tensileStrength}\r\n          </li>\r\n        )}\r\n        {product.width && (\r\n          <li>\r\n            {t('width')}: {product.width}\r\n          </li>\r\n        )}\r\n        {product.density && (\r\n          <li>\r\n            {t('density')}: {product.density}\r\n          </li>\r\n        )}\r\n        {product.maxResistance && (\r\n          <li>\r\n            {t('maxResistance')}: {product.maxResistance}\r\n          </li>\r\n        )}\r\n        {product.weight && (\r\n          <li>\r\n            {t('weight')}: {product.weight}\r\n          </li>\r\n        )}\r\n        {product.crossSectionArea && (\r\n          <li>\r\n            {t('crossSectionArea')}: {product.crossSectionArea}\r\n          </li>\r\n        )}\r\n        {product.diameter && (\r\n          <li>\r\n            {t('diameter')}: {product.diameter}\r\n          </li>\r\n        )}\r\n        {product.pullOutResistance && (\r\n          <li>\r\n            {t('pullOutResistance')}: {product.pullOutResistance}\r\n          </li>\r\n        )}\r\n\r\n        {product.connectorsNumberAlongLength && (\r\n          <li>\r\n            {t('connectorsNumberAlongLength')}:{' '}\r\n            {product.connectorsNumberAlongLength}\r\n          </li>\r\n        )}\r\n        {product.designStrength && (\r\n          <li>\r\n            {t('designStrength')}: {product.designStrength}\r\n          </li>\r\n        )}\r\n        {typeof product.systemDeformation === 'number' &&\r\n          product.systemDeformation > 0 && (\r\n            <li>\r\n              {t('systemDeformation')}: {product.systemDeformation}\r\n            </li>\r\n          )}\r\n        {product.adhesionToConcrete && (\r\n          <li>\r\n            {t('adhesionToConcrete')}: {product.adhesionToConcrete}\r\n          </li>\r\n        )}\r\n        {product.shearStress && (\r\n          <li>\r\n            {t('shearStress')}: {product.shearStress}\r\n          </li>\r\n        )}\r\n      </ul>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;;AAQO,MAAM,qBAAqB,CAAC,EACjC,OAAO,EACP,iBAAiB,SAAS,EAC1B,oBAAoB,IAAI,EAClB;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAE1B,MAAM,QAAQ,IAAA,yNAAe,EAC3B,CAAC,kBAAkB,EAAE,oBAAoB,KAAK,qBAAqB;IAGrE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BAAsB,EAAE;;;;;;0BACvC,8OAAC;gBAAG,WAAU;;oBACX,QAAQ,SAAS,kBAChB,8OAAC;;4BACE,EAAE;4BAAa;4BAAG,MAAM,CAAC,UAAU,EAAE,QAAQ,SAAS,EAAE;;;;;;;oBAG5D,QAAQ,WAAW,kBAClB,8OAAC;;4BACE,EAAE;4BAAe;4BAAG,MAAM,CAAC,YAAY,EAAE,QAAQ,WAAW,EAAE;;;;;;;oBAGlE,QAAQ,SAAS,kBAChB,8OAAC;;4BACE,EAAE;4BAAa;4BAAG,QAAQ,SAAS;;;;;;;oBAGvC,QAAQ,eAAe,kBACtB,8OAAC;;4BACE,EAAE;4BAAmB;4BAAG,QAAQ,eAAe,CAAC,IAAI,CAAC;;;;;;;oBAGzD,QAAQ,cAAc,kBACrB,8OAAC;;4BACE,EAAE;4BAAkB;4BAAG,QAAQ,cAAc;;;;;;;oBAGjD,QAAQ,4BAA4B,kBACnC,8OAAC;;4BACE,EAAE;4BAAgC;4BAAE;4BACpC,QAAQ,4BAA4B;;;;;;;oBAGxC,kBACC,QAAQ,iBAAiB,IACzB,QAAQ,iBAAiB,CAAC,eAAe,kBACvC,8OAAC;;4BACE,EAAE;4BAAqB;4BAAG,EAAE,CAAC,eAAe,EAAE,gBAAgB;4BAAE;4BAC7D,QAAQ,iBAAiB,CAAC,eAAe;;;;;;;oBAGlD,QAAQ,WAAW,kBAClB,8OAAC;;4BACE,EAAE;4BAAe;4BAAG,QAAQ,WAAW;;;;;;;oBAG3C,QAAQ,cAAc,kBACrB,8OAAC;;4BACE,EAAE;4BAAkB;4BAAG,QAAQ,cAAc;;;;;;;oBAGjD,QAAQ,iBAAiB,kBACxB,8OAAC;;4BACE,EAAE;4BAAqB;4BAAG,QAAQ,iBAAiB;;;;;;;oBAGvD,QAAQ,qBAAqB,kBAC5B,8OAAC;;4BACE,EAAE;4BAAyB;4BAAG,QAAQ,qBAAqB;;;;;;;oBAG/D,QAAQ,4BAA4B,kBACnC,8OAAC;;4BACE,EAAE;4BAAgC;4BAAE;4BACpC,QAAQ,4BAA4B;;;;;;;oBAGxC,QAAQ,2BAA2B,kBAClC,8OAAC;;4BACE,EAAE;4BAA+B;4BAAE;4BACnC,QAAQ,2BAA2B;;;;;;;oBAGvC,QAAQ,kBAAkB,kBACzB,8OAAC;;4BACE,EAAE;4BAAsB;4BAAG,QAAQ,kBAAkB;;;;;;;oBAIzD,QAAQ,WAAW,KAAK,wBACvB,8OAAC;;4BACE,EAAE;4BAAyB;4BAAE;4BAC7B,CAAC,QAAQ,IAAI,KAAK,wBACjB,QAAQ,IAAI,KAAK,oBAAoB,KACvC,iBACI,CAAC;gCACC,IAAI,QAAQ;gCACZ,IAAI,QAAQ,IAAI,KAAK,sBAAsB;oCACzC,IAAI,mBAAmB,8IAAe,CAAC,KAAK,EAAE;wCAC5C,QAAQ;oCACV,OAAO,IAAI,mBAAmB,8IAAe,CAAC,IAAI,EAAE;wCAClD,QAAQ;oCACV,OAAO;wCACL,QAAQ;oCACV;gCACF,OAAO;oCACL,IAAI,mBAAmB,8IAAe,CAAC,KAAK,EAAE;wCAC5C,QAAQ;oCACV,OAAO,IAAI,mBAAmB,8IAAe,CAAC,IAAI,EAAE;wCAClD,QAAQ;oCACV,OAAO;wCACL,QAAQ;oCACV;gCACF;gCACA,OAAO;4BACT,CAAC,MACD,QAAQ,eAAe;;;;;;;oBAG9B,QAAQ,KAAK,kBACZ,8OAAC;;4BACE,EAAE;4BAAS;4BAAG,QAAQ,KAAK;;;;;;;oBAG/B,QAAQ,OAAO,kBACd,8OAAC;;4BACE,EAAE;4BAAW;4BAAG,QAAQ,OAAO;;;;;;;oBAGnC,QAAQ,aAAa,kBACpB,8OAAC;;4BACE,EAAE;4BAAiB;4BAAG,QAAQ,aAAa;;;;;;;oBAG/C,QAAQ,MAAM,kBACb,8OAAC;;4BACE,EAAE;4BAAU;4BAAG,QAAQ,MAAM;;;;;;;oBAGjC,QAAQ,gBAAgB,kBACvB,8OAAC;;4BACE,EAAE;4BAAoB;4BAAG,QAAQ,gBAAgB;;;;;;;oBAGrD,QAAQ,QAAQ,kBACf,8OAAC;;4BACE,EAAE;4BAAY;4BAAG,QAAQ,QAAQ;;;;;;;oBAGrC,QAAQ,iBAAiB,kBACxB,8OAAC;;4BACE,EAAE;4BAAqB;4BAAG,QAAQ,iBAAiB;;;;;;;oBAIvD,QAAQ,2BAA2B,kBAClC,8OAAC;;4BACE,EAAE;4BAA+B;4BAAE;4BACnC,QAAQ,2BAA2B;;;;;;;oBAGvC,QAAQ,cAAc,kBACrB,8OAAC;;4BACE,EAAE;4BAAkB;4BAAG,QAAQ,cAAc;;;;;;;oBAGjD,OAAO,QAAQ,iBAAiB,KAAK,YACpC,QAAQ,iBAAiB,GAAG,mBAC1B,8OAAC;;4BACE,EAAE;4BAAqB;4BAAG,QAAQ,iBAAiB;;;;;;;oBAGzD,QAAQ,kBAAkB,kBACzB,8OAAC;;4BACE,EAAE;4BAAsB;4BAAG,QAAQ,kBAAkB;;;;;;;oBAGzD,QAAQ,WAAW,kBAClB,8OAAC;;4BACE,EAAE;4BAAe;4BAAG,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;AAMpD", "debugId": null}}]}