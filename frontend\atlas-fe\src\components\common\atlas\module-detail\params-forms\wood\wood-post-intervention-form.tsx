import { WoodBeamGeometryForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form'
import { WoodCompositeGeometryForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-geometry-form'
import { WoodCompositePropertiesForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form'
import { WoodGeneralForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-general-form'
import { WoodGeometryForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-geometry-form'
import { WoodPostInterventionCheckForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form'

import {
  Accordion as AccordionComponent,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@atlas/components/ui/accordion'
import { Separator } from '@atlas/components/ui/separator'
import type {
  Module,
  ModuleWithParamsWood,
} from '@atlas/lib/api/modules/schemas/modules'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useCallback, useState } from 'react'

type Props = {
  session: Session
  projectId: Project['id']
  moduleId: Module['id']
  module: ModuleWithParamsWood
}

export const WoodPostInterventionForm = ({
  session,
  projectId,
  moduleId,
  module,
}: Props) => {
  const t = useTranslations('forms.project-params.wood')
  const { params } = module
  const [refreshTrigger, setRefreshTrigger] = useState(0)

  const [currentCompositeGeometry, setCurrentCompositeGeometry] = useState<any>(
    (params as any)?.postIntervationCheck?.compositeGeometry ||
      (params as any)?.compositeGeometry ||
      null,
  )

  const [currentCompositeProperties, setCurrentCompositeProperties] =
    useState<any>(
      (params as any)?.postIntervationCheck?.compositeProperties ||
        (params as any)?.compositeProperties ||
        null,
    )

  // Callback to handle composite geometry updates with change detection
  const handleCompositeGeometryChange = useCallback((newGeometry: any) => {
    // Only update if the geometry data has actually changed
    setCurrentCompositeGeometry((prev: any) => {
      if (
        !prev ||
        prev.productId !== newGeometry.productId ||
        prev.stripWidth !== newGeometry.stripWidth ||
        prev.layersNumber !== newGeometry.layersNumber ||
        prev.equivalentThickness !== newGeometry.equivalentThickness ||
        prev.expositionType !== newGeometry.expositionType ||
        prev.environmentalConversionFactor !==
          newGeometry.environmentalConversionFactor
      ) {
        return newGeometry
      }
      return prev
    })
  }, [])

  // Callback to handle composite properties updates with change detection
  const handleCompositePropertiesChange = useCallback((newProperties: any) => {
    // Only update if the properties data has actually changed
    setCurrentCompositeProperties((prev: any) => {
      if (
        !prev ||
        prev.sectionModulus !== newProperties.sectionModulus ||
        prev.momentOfInertiaAboutY !== newProperties.momentOfInertiaAboutY ||
        prev.frpElasticityModulus !== newProperties.frpElasticityModulus ||
        prev.frpDesignMaximumStrain !== newProperties.frpDesignMaximumStrain
      ) {
        return newProperties
      }
      return prev
    })
  }, [])

  // Create a dummy onSave function for read-only forms
  const dummyOnSave = () => {}

  return (
    <div className="space-y-6">
      <AccordionComponent type="single" collapsible className="w-full">
        <AccordionItem value="post-intervention">
          <AccordionTrigger className="text-lg font-normal">
            {t('postForm.Post-Intervention Analysis')}
          </AccordionTrigger>
          <AccordionContent className="space-y-6 p-4">
            {/* Section 1: Initial Deformation (Read-only) */}
            <div>
              <h3 className="text-md font-semibold mb-4">
                {t('postForm.Initial Deformation - Read Only')}
              </h3>
              <div className="opacity-60 [&_button[type='submit']]:hidden [&_input]:pointer-events-none [&_input]:cursor-not-allowed [&_[data-slot='select-trigger']]:pointer-events-auto [&_[data-slot='select-trigger']]:opacity-100 [&_[data-slot='select-trigger']]:cursor-pointer">
                <WoodGeneralForm
                  session={session}
                  projectId={projectId}
                  moduleId={moduleId}
                  defaultValues={{
                    initialDeformation: params?.initialDeformation || 0,
                  }}
                  onSave={dummyOnSave}
                />
              </div>
            </div>

            <Separator />

            {/* Section 2: Material Properties (Read-only) */}
            <div>
              <h3 className="text-md font-semibold mb-4">
                {t('postForm.Material Properties - Read Only')}
              </h3>
              <div className="opacity-60 [&_button[type='submit']]:hidden [&_input]:pointer-events-none [&_input]:cursor-not-allowed [&_[data-slot='select-trigger']]:pointer-events-auto [&_[data-slot='select-trigger']]:opacity-100 [&_[data-slot='select-trigger']]:cursor-pointer">
                <WoodGeometryForm
                  session={session}
                  projectId={projectId}
                  moduleId={moduleId}
                  defaultValues={params?.materialProperties as any}
                  woodName={params?.materialProperties?.woodName}
                  onSave={dummyOnSave}
                />
              </div>
            </div>

            <Separator />

            {/* Section 3: Geometry (Read-only) */}
            <div>
              <h3 className="text-md font-semibold mb-4">
                {t('postForm.Geometry - Read Only')}
              </h3>
              <div className="opacity-60 [&_button[type='submit']]:hidden [&_input]:pointer-events-none [&_input]:cursor-not-allowed [&_[data-slot='select-trigger']]:pointer-events-auto [&_[data-slot='select-trigger']]:opacity-100 [&_[data-slot='select-trigger']]:cursor-pointer">
                <WoodBeamGeometryForm
                  session={session}
                  projectId={projectId}
                  moduleId={moduleId}
                  defaultValues={params?.geometry as any}
                  materialProperties={params?.materialProperties as any}
                  onSave={dummyOnSave}
                />
              </div>
            </div>

            <Separator />

            {/* Section 4: Composite Geometry */}
            <div>
              <h3 className="text-md font-semibold mb-4">
                {t('compositeGeometry.title')}
              </h3>
              <WoodCompositeGeometryForm
                session={session}
                projectId={projectId}
                moduleId={moduleId}
                defaultValues={
                  (params as any)?.postIntervationCheck?.compositeGeometry &&
                  typeof (params as any).postIntervationCheck
                    .compositeGeometry === 'object'
                    ? (params as any).postIntervationCheck.compositeGeometry
                    : (params as any)?.compositeGeometry &&
                        typeof (params as any).compositeGeometry === 'object'
                      ? (params as any).compositeGeometry
                      : undefined
                }
                preInterventionData={params?.preIntervationCheck}
                materialProperties={params?.materialProperties}
                geometryProperties={params?.geometry}
                onSave={() => setRefreshTrigger(prev => prev + 1)}
                onGeometryChange={handleCompositeGeometryChange}
                initialDeformation={params?.initialDeformation}
              />
            </div>

            <Separator />

            {/* Section 5: Composite Properties */}
            <div>
              <h3 className="text-md font-semibold mb-4">
                {t('compositeProperties.title')}
              </h3>
              <WoodCompositePropertiesForm
                session={session}
                projectId={projectId}
                moduleId={moduleId}
                defaultValues={
                  (params as any)?.postIntervationCheck?.compositeProperties &&
                  typeof (params as any).postIntervationCheck
                    .compositeProperties === 'object'
                    ? (params as any).postIntervationCheck.compositeProperties
                    : (params as any)?.compositeProperties &&
                        typeof (params as any).compositeProperties === 'object'
                      ? (params as any).compositeProperties
                      : undefined
                }
                compositeGeometry={currentCompositeGeometry}
                preInterventionData={params?.preIntervationCheck}
                materialProperties={params?.materialProperties}
                geometryProperties={params?.geometry}
                onSave={() => setRefreshTrigger(prev => prev + 1)}
                onPropertiesChange={handleCompositePropertiesChange}
                initialDeformation={params?.initialDeformation}
              />
            </div>

            <Separator />

            {/* Section 6: Post-Intervention Check Results */}
            <div>
              <h3 className="text-md font-semibold mb-4">
                {t('resultOfPostIntervationCheck.title')}
              </h3>
              <WoodPostInterventionCheckForm
                key={`post-intervention-${refreshTrigger}-${currentCompositeProperties?.sectionModulus || 0}`}
                session={session}
                projectId={projectId}
                moduleId={moduleId}
                defaultValues={
                  (params as any)?.postIntervationCheck
                    ?.resultOfPostIntervationCheck &&
                  typeof (params as any).postIntervationCheck
                    .resultOfPostIntervationCheck === 'object'
                    ? (params as any).postIntervationCheck
                        .resultOfPostIntervationCheck
                    : (params as any)?.resultOfPostIntervationCheck &&
                        typeof (params as any).resultOfPostIntervationCheck ===
                          'object'
                      ? (params as any).resultOfPostIntervationCheck
                      : undefined
                }
                preInterventionData={params?.preIntervationCheck}
                materialProperties={params?.materialProperties}
                geometryProperties={params?.geometry}
                compositeGeometry={currentCompositeGeometry}
                compositeProperties={currentCompositeProperties}
                onSave={() => setRefreshTrigger(prev => prev + 1)}
                initialDeformation={params?.initialDeformation}
              />
            </div>
          </AccordionContent>
        </AccordionItem>
      </AccordionComponent>
    </div>
  )
}
