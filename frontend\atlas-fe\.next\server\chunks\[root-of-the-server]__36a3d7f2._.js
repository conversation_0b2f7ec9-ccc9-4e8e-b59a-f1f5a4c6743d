module.exports = [
"[project]/.next-internal/server/app/api/auth/[...nextauth]/route/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
"[externals]/util [external] (util, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}),
"[externals]/stream [external] (stream, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}),
"[externals]/path [external] (path, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("path", () => require("path"));

module.exports = mod;
}),
"[externals]/http [external] (http, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}),
"[externals]/https [external] (https, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}),
"[externals]/url [external] (url, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}),
"[externals]/fs [external] (fs, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}),
"[externals]/crypto [external] (crypto, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}),
"[externals]/assert [external] (assert, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("assert", () => require("assert"));

module.exports = mod;
}),
"[externals]/zlib [external] (zlib, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}),
"[externals]/events [external] (events, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}),
"[project]/src/lib/api/auth/doFinalSignOutHandshake.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "doFinalSignOutHandshake",
    ()=>doFinalSignOutHandshake
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-route] (ecmascript)");
;
const doFinalSignOutHandshake = async (jwt)=>{
    const { idToken } = jwt;
    try {
        // Add the id_token_hint to the query string
        const params = new URLSearchParams();
        params.append('id_token_hint', idToken);
        const { status, statusText } = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"].get(`${process.env.AUTH_KEYCLOAK_ISSUER}/protocol/openid-connect/logout?${params.toString()}`);
        // The response body should contain a confirmation that the user has been logged out
        console.debug('Completed post-logout handshake', status, statusText);
    } catch (e) {
        console.error('Unable to perform post-logout handshake', e?.code || e);
    }
};
}),
"[project]/src/lib/api/auth/refreshAccessToken.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "refreshAccessToken",
    ()=>refreshAccessToken
]);
const refreshAccessToken = async (token)=>{
    try {
        if (token.refreshTokenExpires && Date.now() >= token.refreshTokenExpires * 1000) {
            return {
                ...token,
                error: 'RefreshTokenExpired',
                errorDetails: 'The refresh token itself has expired.'
            };
        }
        if (!token.refreshToken) {
            throw new Error('MissingRefreshToken');
        }
        const clientId = process.env.AUTH_KEYCLOAK_ID;
        const clientSecret = process.env.AUTH_KEYCLOAK_SECRET;
        const tokenUrl = `${process.env.AUTH_KEYCLOAK_ISSUER}/protocol/openid-connect/token`;
        if (!(clientId && tokenUrl)) {
            throw new Error('MissingKeycloakConfig');
        }
        const params = new URLSearchParams();
        params.append('client_id', clientId);
        if (clientSecret) {
            params.append('client_secret', clientSecret);
        }
        params.append('grant_type', 'refresh_token');
        params.append('refresh_token', token.refreshToken);
        console.log('Try to refresh access token...');
        const response = await fetch(tokenUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            },
            body: params.toString()
        });
        const refreshedTokens = await response.json();
        if (!response.ok) {
            console.error('Error during refresh token:', refreshedTokens);
            if (refreshedTokens.error === 'invalid_grant') {
                return {
                    ...token,
                    error: 'RefreshTokenInvalidated',
                    errorDetails: refreshedTokens.error_description || 'Refresh token validation failed on server.'
                };
            }
            throw new Error(refreshedTokens.error || 'RefreshFailed');
        }
        const nowInSeconds = Math.floor(Date.now() / 1000);
        const newAccessTokenExpiresAt = nowInSeconds + refreshedTokens.expires_in;
        let newRefreshTokenExpiresAt = token.refreshTokenExpires;
        if (typeof refreshedTokens.refreshTokenExpires === 'number') {
            newRefreshTokenExpiresAt = nowInSeconds + refreshedTokens.refreshTokenExpires;
        }
        return {
            ...token,
            accessToken: refreshedTokens.access_token,
            idToken: refreshedTokens.id_token,
            accessTokenExpires: newAccessTokenExpiresAt,
            refreshToken: refreshedTokens.refresh_token ?? token.refreshToken,
            refreshTokenExpires: newRefreshTokenExpiresAt,
            error: undefined,
            errorDetails: undefined
        };
    } catch (error) {
        console.error('Exception during refresh token:', error);
        return {
            ...token,
            error: 'RefreshAccessTokenError',
            errorDetails: error.message || 'UnknownError'
        };
    }
};
}),
"[externals]/node:buffer [external] (node:buffer, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("node:buffer", () => require("node:buffer"));

module.exports = mod;
}),
"[externals]/node:crypto [external] (node:crypto, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("node:crypto", () => require("node:crypto"));

module.exports = mod;
}),
"[externals]/node:crypto [external] (node:crypto, cjs) <export randomFillSync as default>", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$crypto__$5b$external$5d$__$28$node$3a$crypto$2c$__cjs$29$__["randomFillSync"]
]);
var __TURBOPACK__imported__module__$5b$externals$5d2f$node$3a$crypto__$5b$external$5d$__$28$node$3a$crypto$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/node:crypto [external] (node:crypto, cjs)");
}),
"[externals]/node:util [external] (node:util, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("node:util", () => require("node:util"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}),
"[externals]/next/dist/server/app-render/action-async-storage.external.js [external] (next/dist/server/app-render/action-async-storage.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/server/app-render/action-async-storage.external.js", () => require("next/dist/server/app-render/action-async-storage.external.js"));

module.exports = mod;
}),
"[project]/src/auth.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "auth",
    ()=>auth,
    "handlers",
    ()=>handlers,
    "signIn",
    ()=>signIn,
    "signOut",
    ()=>signOut
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$auth$2f$doFinalSignOutHandshake$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/auth/doFinalSignOutHandshake.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$auth$2f$refreshAccessToken$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/auth/refreshAccessToken.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-auth/index.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$providers$2f$keycloak$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-auth/providers/keycloak.js [app-route] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$auth$2f$core$2f$providers$2f$keycloak$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@auth/core/providers/keycloak.js [app-route] (ecmascript)");
;
;
;
;
const ACCESS_TOKEN_BUFFER_SECONDS = 60;
const { handlers, signIn, signOut, auth } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$auth$2f$index$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"])({
    providers: [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$auth$2f$core$2f$providers$2f$keycloak$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["default"]
    ],
    callbacks: {
        /**
     * Controlla se l'utente è autorizzato ad accedere.
     * Utile per middleware o protezione server-side.
     *
     */ authorized ({ auth }) {
            return !!auth;
        },
        /**
     * Callback JWT: Gestisce la creazione e l'aggiornamento del token JWT.
     * Eseguito al login iniziale e ad ogni richiesta successiva che usa il JWT.
     */ async jwt ({ token, account, user }) {
            const now = Math.floor(Date.now() / 1000);
            if (account?.access_token && account?.id_token && user) {
                console.debug('[AUTH] jwt - Initial sign-in.');
                const accessTokenExpires = account.expires_at ?? now + (account.expires_in ?? 300);
                let refreshTokenExpires;
                if (typeof account.refresh_expires_in === 'number') {
                    refreshTokenExpires = now + account.refresh_expires_in;
                    console.debug(`[AUTH] jwt - Refresh token initial expiry: ${new Date(refreshTokenExpires * 1000).toISOString()}`);
                }
                return {
                    ...token,
                    accessToken: account.access_token,
                    idToken: account.id_token,
                    refreshToken: account.refresh_token,
                    accessTokenExpires: accessTokenExpires,
                    refreshTokenExpires: refreshTokenExpires,
                    user: user,
                    error: undefined
                };
            }
            if (token.refreshTokenExpires && now >= token.refreshTokenExpires) {
                console.debug('[AUTH] jwt - Refresh token expired. Invalidating session.');
                return null;
            }
            const isAccessTokenValid = token.accessTokenExpires && now < token.accessTokenExpires - ACCESS_TOKEN_BUFFER_SECONDS;
            if (isAccessTokenValid) {
                return token;
            }
            console.debug('[AUTH] jwt - Access token expired or requires refresh. Attempting refresh...');
            if (!token.refreshToken) {
                console.debug('[AUTH] jwt - Cannot refresh: Missing refresh token.');
                return null;
            }
            try {
                const refreshedToken = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$auth$2f$refreshAccessToken$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["refreshAccessToken"])(token);
                if (refreshedToken.error === 'RefreshTokenExpired' || refreshedToken.error === 'RefreshTokenInvalidated') {
                    console.debug(`[AUTH] jwt - Refresh failed (${refreshedToken.error}). Invalidating session.`);
                    return null;
                }
                return refreshedToken;
            } catch (error) {
                console.error('[AUTH] jwt - Exception during token refresh:', error);
                return null;
            }
        },
        /**
     * Callback Session: Gestisce l'oggetto sessione accessibile lato client.
     * Riceve il token JWT aggiornato dalla callback `jwt`.
     */ session ({ session, token }) {
            session.accessToken = token.accessToken;
            session.idToken = token.idToken;
            session.user = token.user;
            session.error = token.error;
            session.accessTokenExpires = token.accessTokenExpires;
            if (session.error) {
                console.warn(`[AUTH] session - Propagating token error to client session: ${session.error}`);
            }
            return session;
        }
    },
    session: {
        strategy: 'jwt'
    },
    events: {
        signOut: (message)=>{
            if ('token' in message && message.token) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$auth$2f$doFinalSignOutHandshake$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["doFinalSignOutHandshake"])(message.token);
            }
            return Promise.resolve();
        }
    }
});
}),
"[project]/src/app/api/auth/[...nextauth]/route.ts [app-route] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "GET",
    ()=>GET,
    "POST",
    ()=>POST
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/auth.ts [app-route] (ecmascript)"); // Referring to the auth.ts we just created
;
const { GET, POST } = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$auth$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["handlers"];
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__36a3d7f2._.js.map