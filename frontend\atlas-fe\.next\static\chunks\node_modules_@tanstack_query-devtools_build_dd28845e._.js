(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/6ELMOJL2.js [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_7f3ebca3._.js",
  "static/chunks/node_modules_@tanstack_query-devtools_build_DevtoolsComponent_6ELMOJL2_e1fcb7df.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@tanstack/query-devtools/build/DevtoolsComponent/6ELMOJL2.js [app-client] (ecmascript)");
    });
});
}),
"[project]/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/PULY4AJ7.js [app-client] (ecmascript, async loader)", ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_41ad3d38._.js",
  "static/chunks/d4b1c_modules_@tanstack_query-devtools_build_DevtoolsPanelComponent_PULY4AJ7_e1fcb7df.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/@tanstack/query-devtools/build/DevtoolsPanelComponent/PULY4AJ7.js [app-client] (ecmascript)");
    });
});
}),
]);