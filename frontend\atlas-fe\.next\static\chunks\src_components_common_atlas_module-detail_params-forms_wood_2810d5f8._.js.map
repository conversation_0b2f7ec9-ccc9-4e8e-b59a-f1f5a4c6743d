{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport { woodGeometryPropertiesSchema } from '@atlas/lib/api/modules/schemas/wood-params'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\n\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm, useWatch } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\nimport { z } from 'zod'\r\nconst formSchema = woodGeometryPropertiesSchema.extend({\r\n  id: z.string().optional(),\r\n})\r\n\r\ntype FormSchema = z.infer<typeof formSchema>\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FormSchema>\r\n  materialProperties?: {\r\n    characteristicBendingStrength?: number\r\n    characteristicShearStrength?: number\r\n    meanElasticityModulus?: number\r\n    partialMaterialFactor?: number\r\n  }\r\n  onSave: () => void\r\n}\r\n\r\nconst SERVICE_CLASS_OPTIONS = [\r\n  { value: 'SERVICE_CLASS_1', label: 'Classe di servizio 1' },\r\n  { value: 'SERVICE_CLASS_2', label: 'Classe di servizio 2' },\r\n  { value: 'SERVICE_CLASS_3', label: 'Classe di servizio 3' },\r\n]\r\n\r\nconst LOAD_DURATION_OPTIONS = [\r\n  { value: 'PERMANENTE', label: 'Permanente' },\r\n  { value: 'LUNGA', label: 'Lunga' },\r\n  { value: 'MEDIA', label: 'Media' },\r\n  { value: 'BREVE', label: 'Breve' },\r\n  { value: 'INSTANTANEA', label: 'Instantanea' },\r\n]\r\n\r\nconst KMOD_TABLE = {\r\n  SERVICE_CLASS_1: {\r\n    PERMANENTE: 0.6,\r\n    LUNGA: 0.7,\r\n    MEDIA: 0.8,\r\n    BREVE: 0.9,\r\n    INSTANTANEA: 1.1,\r\n  },\r\n  SERVICE_CLASS_2: {\r\n    PERMANENTE: 0.6,\r\n    LUNGA: 0.7,\r\n    MEDIA: 0.8,\r\n    BREVE: 0.9,\r\n    INSTANTANEA: 1.1,\r\n  },\r\n  SERVICE_CLASS_3: {\r\n    PERMANENTE: 0.5,\r\n    LUNGA: 0.55,\r\n    MEDIA: 0.65,\r\n    BREVE: 0.7,\r\n    INSTANTANEA: 0.9,\r\n  },\r\n}\r\n\r\nconst KDEF_VALUES = {\r\n  SERVICE_CLASS_1: 0.6,\r\n  SERVICE_CLASS_2: 0.8,\r\n  SERVICE_CLASS_3: 2.0,\r\n}\r\n\r\nexport const WoodBeamGeometryForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  materialProperties,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.wood.geometric_properties')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: () => {\r\n        toast.success(tAction('edit.success'))\r\n        onSave()\r\n      },\r\n      onError: () => {\r\n        toast.error(tAction('edit.error'))\r\n      },\r\n    },\r\n  )\r\n\r\n  const form = useForm<FormSchema>({\r\n    resolver: zodResolver(formSchema),\r\n    defaultValues: {\r\n      id: defaultValues?.id,\r\n      beamSectionWidth: defaultValues?.beamSectionWidth ?? 0,\r\n      beamSectionHeight: defaultValues?.beamSectionHeight ?? 0,\r\n      beamSpacing: defaultValues?.beamSpacing ?? 0,\r\n      beamSpan: defaultValues?.beamSpan ?? 0,\r\n      sectionModulus: defaultValues?.sectionModulus ?? 0,\r\n      inertiaMomentAboutY: defaultValues?.inertiaMomentAboutY ?? 0,\r\n      serviceClass: defaultValues?.serviceClass ?? 'SERVICE_CLASS_1',\r\n      loadDuration: defaultValues?.loadDuration ?? 'MEDIA',\r\n      correctionFactor: defaultValues?.correctionFactor ?? 0.8,\r\n      deformabilityFactor: defaultValues?.deformabilityFactor ?? 0.6,\r\n      designBendingStrength: defaultValues?.designBendingStrength ?? 0,\r\n      designShearStrength: defaultValues?.designShearStrength ?? 0,\r\n      elasticityInstantaneousModulus:\r\n        defaultValues?.elasticityInstantaneousModulus ?? 0,\r\n      longTermElasticityModulus: defaultValues?.longTermElasticityModulus ?? 0,\r\n    },\r\n    shouldUnregister: false,\r\n  })\r\n\r\n  const beamSectionWidth = useWatch({ control: form.control, name: 'beamSectionWidth' })\r\n  const beamSectionHeight = useWatch({ control: form.control, name: 'beamSectionHeight' })\r\n  const serviceClass = useWatch({ control: form.control, name: 'serviceClass' })\r\n  const loadDuration = useWatch({ control: form.control, name: 'loadDuration' })\r\n  const correctionFactor = useWatch({ control: form.control, name: 'correctionFactor' })\r\n  const deformabilityFactor = useWatch({ control: form.control, name: 'deformabilityFactor' })\r\n\r\n  useEffect(() => {\r\n    if (beamSectionWidth > 0 && beamSectionHeight > 0) {\r\n      const sectionModulus = (beamSectionWidth * Math.pow(beamSectionHeight, 2)) / 6\r\n      const inertiaMoment = (beamSectionWidth * Math.pow(beamSectionHeight, 3)) / 12\r\n      form.setValue('sectionModulus', sectionModulus)\r\n      form.setValue('inertiaMomentAboutY', inertiaMoment)\r\n    }\r\n  }, [beamSectionWidth, beamSectionHeight, form])\r\n\r\n  useEffect(() => {\r\n    if (serviceClass && loadDuration) {\r\n      const correction =\r\n        KMOD_TABLE[serviceClass as keyof typeof KMOD_TABLE]?.[\r\n          loadDuration as keyof (typeof KMOD_TABLE)['SERVICE_CLASS_1']\r\n        ] ?? 0.8\r\n      form.setValue('correctionFactor', correction)\r\n    }\r\n\r\n    if (serviceClass) {\r\n      const deformability = KDEF_VALUES[serviceClass as keyof typeof KDEF_VALUES] ?? 0.6\r\n      form.setValue('deformabilityFactor', deformability)\r\n    }\r\n  }, [serviceClass, loadDuration, form])\r\n\r\n  useEffect(() => {\r\n    const {\r\n      characteristicBendingStrength = 0,\r\n      characteristicShearStrength = 0,\r\n      partialMaterialFactor = 1.3,\r\n      meanElasticityModulus = 0,\r\n    } = materialProperties ?? {}\r\n\r\n    if (\r\n      correctionFactor > 0 &&\r\n      partialMaterialFactor > 0 &&\r\n      characteristicBendingStrength > 0 &&\r\n      characteristicShearStrength > 0\r\n    ) {\r\n      const designBendingStrength =\r\n        (characteristicBendingStrength * correctionFactor) / partialMaterialFactor\r\n      const designShearStrength =\r\n        (characteristicShearStrength * correctionFactor) / partialMaterialFactor\r\n\r\n      form.setValue('designBendingStrength', designBendingStrength)\r\n      form.setValue('designShearStrength', designShearStrength)\r\n    }\r\n\r\n    if (meanElasticityModulus > 0) {\r\n      form.setValue('elasticityInstantaneousModulus', meanElasticityModulus)\r\n      if (deformabilityFactor > 0) {\r\n        const longTermModulus = meanElasticityModulus / (1 + deformabilityFactor)\r\n        form.setValue('longTermElasticityModulus', longTermModulus)\r\n      }\r\n    }\r\n  }, [correctionFactor, deformabilityFactor, materialProperties, form])\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (data: FormSchema) => {\r\n      mutate({ projectId, moduleId, body: { geometry: data as any } })\r\n    },\r\n    [mutate, projectId, moduleId],\r\n  )\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4 grow\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        {/* User Input Fields */}\r\n        <NumberFormInput control={form.control} name=\"beamSectionWidth\" t={t} required />\r\n        <NumberFormInput control={form.control} name=\"beamSectionHeight\" t={t} required />\r\n        <NumberFormInput control={form.control} name=\"beamSpacing\" t={t} required />\r\n        <NumberFormInput control={form.control} name=\"beamSpan\" t={t} required />\r\n\r\n        {/* Calculated Fields */}\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"sectionModulus\"\r\n          t={t}\r\n          disabled\r\n          decimalPlaces={5}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"inertiaMomentAboutY\"\r\n          t={t}\r\n          disabled\r\n          decimalPlaces={5}\r\n        />\r\n\r\n        {/* Dropdown Fields */}\r\n        <SelectFormInput\r\n          control={form.control}\r\n          name=\"serviceClass\"\r\n          options={SERVICE_CLASS_OPTIONS}\r\n          t={t}\r\n          required\r\n        />\r\n        <SelectFormInput\r\n          control={form.control}\r\n          name=\"loadDuration\"\r\n          options={LOAD_DURATION_OPTIONS}\r\n          t={t}\r\n          required\r\n        />\r\n\r\n        {/* More Calculated Fields */}\r\n        <NumberFormInput control={form.control} name=\"correctionFactor\" t={t} disabled decimalPlaces={3} />\r\n        <NumberFormInput control={form.control} name=\"deformabilityFactor\" t={t} disabled decimalPlaces={3} />\r\n        <NumberFormInput control={form.control} name=\"designBendingStrength\" t={t} disabled decimalPlaces={3} />\r\n        <NumberFormInput control={form.control} name=\"designShearStrength\" t={t} disabled decimalPlaces={3} />\r\n        <NumberFormInput control={form.control} name=\"elasticityInstantaneousModulus\" t={t} disabled decimalPlaces={3} />\r\n        <NumberFormInput control={form.control} name=\"longTermElasticityModulus\" t={t} disabled decimalPlaces={3} />\r\n        <Button type=\"submit\" disabled={isPending}>\r\n          {isPending ? tCommon('saving') : tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAEA;AAEA;AACA;AAGA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;AACA,MAAM,aAAa,4LAA4B,CAAC,MAAM,CAAC;IACrD,IAAI,qLAAC,CAAC,MAAM,GAAG,QAAQ;AACzB;AAkBA,MAAM,wBAAwB;IAC5B;QAAE,OAAO;QAAmB,OAAO;IAAuB;IAC1D;QAAE,OAAO;QAAmB,OAAO;IAAuB;IAC1D;QAAE,OAAO;QAAmB,OAAO;IAAuB;CAC3D;AAED,MAAM,wBAAwB;IAC5B;QAAE,OAAO;QAAc,OAAO;IAAa;IAC3C;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAe,OAAO;IAAc;CAC9C;AAED,MAAM,aAAa;IACjB,iBAAiB;QACf,YAAY;QACZ,OAAO;QACP,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA,iBAAiB;QACf,YAAY;QACZ,OAAO;QACP,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA,iBAAiB;QACf,YAAY;QACZ,OAAO;QACP,OAAO;QACP,OAAO;QACP,aAAa;IACf;AACF;AAEA,MAAM,cAAc;IAClB,iBAAiB;IACjB,iBAAiB;IACjB,iBAAiB;AACnB;AAEO,MAAM,uBAAuB;QAAC,EACnC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,kBAAkB,EAClB,MAAM,EACA;;IACN,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAEhC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,mNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,SAAS;gEAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;gBACtB;YACF;;QACA,OAAO;gEAAE;gBACP,oJAAK,CAAC,KAAK,CAAC,QAAQ;YACtB;;IACF;QAOoB,iCACC,kCACN,4BACH,yBACM,+BACK,oCACP,6BACA,6BACI,iCACG,oCACE,sCACF,oCAEnB,+CACyB;IAlB/B,MAAM,OAAO,IAAA,4KAAO,EAAa;QAC/B,UAAU,IAAA,gLAAW,EAAC;QACtB,eAAe;YACb,EAAE,EAAE,0BAAA,oCAAA,cAAe,EAAE;YACrB,kBAAkB,CAAA,kCAAA,0BAAA,oCAAA,cAAe,gBAAgB,cAA/B,6CAAA,kCAAmC;YACrD,mBAAmB,CAAA,mCAAA,0BAAA,oCAAA,cAAe,iBAAiB,cAAhC,8CAAA,mCAAoC;YACvD,aAAa,CAAA,6BAAA,0BAAA,oCAAA,cAAe,WAAW,cAA1B,wCAAA,6BAA8B;YAC3C,UAAU,CAAA,0BAAA,0BAAA,oCAAA,cAAe,QAAQ,cAAvB,qCAAA,0BAA2B;YACrC,gBAAgB,CAAA,gCAAA,0BAAA,oCAAA,cAAe,cAAc,cAA7B,2CAAA,gCAAiC;YACjD,qBAAqB,CAAA,qCAAA,0BAAA,oCAAA,cAAe,mBAAmB,cAAlC,gDAAA,qCAAsC;YAC3D,cAAc,CAAA,8BAAA,0BAAA,oCAAA,cAAe,YAAY,cAA3B,yCAAA,8BAA+B;YAC7C,cAAc,CAAA,8BAAA,0BAAA,oCAAA,cAAe,YAAY,cAA3B,yCAAA,8BAA+B;YAC7C,kBAAkB,CAAA,kCAAA,0BAAA,oCAAA,cAAe,gBAAgB,cAA/B,6CAAA,kCAAmC;YACrD,qBAAqB,CAAA,qCAAA,0BAAA,oCAAA,cAAe,mBAAmB,cAAlC,gDAAA,qCAAsC;YAC3D,uBAAuB,CAAA,uCAAA,0BAAA,oCAAA,cAAe,qBAAqB,cAApC,kDAAA,uCAAwC;YAC/D,qBAAqB,CAAA,qCAAA,0BAAA,oCAAA,cAAe,mBAAmB,cAAlC,gDAAA,qCAAsC;YAC3D,gCACE,CAAA,gDAAA,0BAAA,oCAAA,cAAe,8BAA8B,cAA7C,2DAAA,gDAAiD;YACnD,2BAA2B,CAAA,2CAAA,0BAAA,oCAAA,cAAe,yBAAyB,cAAxC,sDAAA,2CAA4C;QACzE;QACA,kBAAkB;IACpB;IAEA,MAAM,mBAAmB,IAAA,6KAAQ,EAAC;QAAE,SAAS,KAAK,OAAO;QAAE,MAAM;IAAmB;IACpF,MAAM,oBAAoB,IAAA,6KAAQ,EAAC;QAAE,SAAS,KAAK,OAAO;QAAE,MAAM;IAAoB;IACtF,MAAM,eAAe,IAAA,6KAAQ,EAAC;QAAE,SAAS,KAAK,OAAO;QAAE,MAAM;IAAe;IAC5E,MAAM,eAAe,IAAA,6KAAQ,EAAC;QAAE,SAAS,KAAK,OAAO;QAAE,MAAM;IAAe;IAC5E,MAAM,mBAAmB,IAAA,6KAAQ,EAAC;QAAE,SAAS,KAAK,OAAO;QAAE,MAAM;IAAmB;IACpF,MAAM,sBAAsB,IAAA,6KAAQ,EAAC;QAAE,SAAS,KAAK,OAAO;QAAE,MAAM;IAAsB;IAE1F,IAAA,0KAAS;0CAAC;YACR,IAAI,mBAAmB,KAAK,oBAAoB,GAAG;gBACjD,MAAM,iBAAiB,AAAC,mBAAmB,KAAK,GAAG,CAAC,mBAAmB,KAAM;gBAC7E,MAAM,gBAAgB,AAAC,mBAAmB,KAAK,GAAG,CAAC,mBAAmB,KAAM;gBAC5E,KAAK,QAAQ,CAAC,kBAAkB;gBAChC,KAAK,QAAQ,CAAC,uBAAuB;YACvC;QACF;yCAAG;QAAC;QAAkB;QAAmB;KAAK;IAE9C,IAAA,0KAAS;0CAAC;YACR,IAAI,gBAAgB,cAAc;oBAE9B;oBAAA;gBADF,MAAM,aACJ,CAAA,yCAAA,2BAAA,UAAU,CAAC,aAAwC,cAAnD,+CAAA,wBAAqD,CACnD,aACD,cAFD,mDAAA,wCAEK;gBACP,KAAK,QAAQ,CAAC,oBAAoB;YACpC;YAEA,IAAI,cAAc;oBACM;gBAAtB,MAAM,gBAAgB,CAAA,4BAAA,WAAW,CAAC,aAAyC,cAArD,uCAAA,4BAAyD;gBAC/E,KAAK,QAAQ,CAAC,uBAAuB;YACvC;QACF;yCAAG;QAAC;QAAc;QAAc;KAAK;IAErC,IAAA,0KAAS;0CAAC;YACR,MAAM,EACJ,gCAAgC,CAAC,EACjC,8BAA8B,CAAC,EAC/B,wBAAwB,GAAG,EAC3B,wBAAwB,CAAC,EAC1B,GAAG,+BAAA,gCAAA,qBAAsB,CAAC;YAE3B,IACE,mBAAmB,KACnB,wBAAwB,KACxB,gCAAgC,KAChC,8BAA8B,GAC9B;gBACA,MAAM,wBACJ,AAAC,gCAAgC,mBAAoB;gBACvD,MAAM,sBACJ,AAAC,8BAA8B,mBAAoB;gBAErD,KAAK,QAAQ,CAAC,yBAAyB;gBACvC,KAAK,QAAQ,CAAC,uBAAuB;YACvC;YAEA,IAAI,wBAAwB,GAAG;gBAC7B,KAAK,QAAQ,CAAC,kCAAkC;gBAChD,IAAI,sBAAsB,GAAG;oBAC3B,MAAM,kBAAkB,wBAAwB,CAAC,IAAI,mBAAmB;oBACxE,KAAK,QAAQ,CAAC,6BAA6B;gBAC7C;YACF;QACF;yCAAG;QAAC;QAAkB;QAAqB;QAAoB;KAAK;IAEpE,MAAM,mBAAmB,IAAA,4KAAW;8DAClC,CAAC;YACC,OAAO;gBAAE;gBAAW;gBAAU,MAAM;oBAAE,UAAU;gBAAY;YAAE;QAChE;6DACA;QAAC;QAAQ;QAAW;KAAS;IAG/B,qBACE,6LAAC,2IAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,6LAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAG5B,6LAAC,qLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAmB,GAAG;oBAAG,QAAQ;;;;;;8BAC9E,6LAAC,qLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAoB,GAAG;oBAAG,QAAQ;;;;;;8BAC/E,6LAAC,qLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAc,GAAG;oBAAG,QAAQ;;;;;;8BACzE,6LAAC,qLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAW,GAAG;oBAAG,QAAQ;;;;;;8BAGtE,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,QAAQ;oBACR,eAAe;;;;;;8BAEjB,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,QAAQ;oBACR,eAAe;;;;;;8BAIjB,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS;oBACT,GAAG;oBACH,QAAQ;;;;;;8BAEV,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS;oBACT,GAAG;oBACH,QAAQ;;;;;;8BAIV,6LAAC,qLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAmB,GAAG;oBAAG,QAAQ;oBAAC,eAAe;;;;;;8BAC9F,6LAAC,qLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAsB,GAAG;oBAAG,QAAQ;oBAAC,eAAe;;;;;;8BACjG,6LAAC,qLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAwB,GAAG;oBAAG,QAAQ;oBAAC,eAAe;;;;;;8BACnG,6LAAC,qLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAsB,GAAG;oBAAG,QAAQ;oBAAC,eAAe;;;;;;8BACjG,6LAAC,qLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAiC,GAAG;oBAAG,QAAQ;oBAAC,eAAe;;;;;;8BAC5G,6LAAC,qLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAA4B,GAAG;oBAAG,QAAQ;oBAAC,eAAe;;;;;;8BACvG,6LAAC,+IAAM;oBAAC,MAAK;oBAAS,UAAU;8BAC7B,YAAY,QAAQ,YAAY,QAAQ;;;;;;;;;;;;;;;;;AAKnD;GA/Ka;;QAQD,4NAAe;QACT,4NAAe;QACf,4NAAe;QAED,mNAA2B;QAa5C,4KAAO;QAuBK,6KAAQ;QACP,6KAAQ;QACb,6KAAQ;QACR,6KAAQ;QACJ,6KAAQ;QACL,6KAAQ;;;KArDzB", "debugId": null}}, {"offset": {"line": 440, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-general-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type WoodGeneralFormInputs,\r\n  woodGeneralSchema,\r\n} from '@atlas/types/schemas/wood-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\n\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<WoodGeneralFormInputs>\r\n  onSave: () => void\r\n}\r\n\r\nexport const WoodGeneralForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.wood.general')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  const form = useForm<WoodGeneralFormInputs>({\r\n    resolver: zodResolver(woodGeneralSchema),\r\n    defaultValues: {\r\n      initialDeformation: defaultValues?.initialDeformation ?? 0,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: () => {\r\n        toast.success(tAction('edit.success'))\r\n        // Query invalidation is handled by the mutation hook itself\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: WoodGeneralFormInputs) => {\r\n      mutate({ projectId, moduleId, body })\r\n    },\r\n    [mutate, projectId, moduleId],\r\n  )\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"initialDeformation\"\r\n          t={t}\r\n          required={true}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAGA;AACA;AAIA;AAEA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;AAUO,MAAM,kBAAkB;QAAC,EAC9B,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACA;;IACN,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;QAKR;IAHxB,MAAM,OAAO,IAAA,4KAAO,EAAwB;QAC1C,UAAU,IAAA,gLAAW,EAAC,+JAAiB;QACvC,eAAe;YACb,oBAAoB,CAAA,oCAAA,0BAAA,oCAAA,cAAe,kBAAkB,cAAjC,+CAAA,oCAAqC;QAC3D;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,mNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,SAAS;2DAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;gBACtB,4DAA4D;gBAC5D;YACF;;QACA,OAAO;2DAAE,CAAA;gBACP,oJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAC7D;;IACF;IAGF,MAAM,mBAAmB,IAAA,4KAAW;yDAClC,CAAC;YACC,OAAO;gBAAE;gBAAW;gBAAU;YAAK;QACrC;wDACA;QAAC;QAAQ;QAAW;KAAS;IAG/B,qBACE,6LAAC,2IAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,6LAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,+IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,6LAAC,uOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB;GA/Da;;QAOD,4NAAe;QACT,4NAAe;QACf,4NAAe;QAElB,4KAAO;QAOU,mNAA2B;;;KAlB9C", "debugId": null}}, {"offset": {"line": 578, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-geometry-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport { useWoodMaterialByName } from '@atlas/lib/query/materials/use-wood-material-by-name'\r\nimport { useWoodNames } from '@atlas/lib/query/materials/use-wood-names-api'\r\nimport { woodMaterialSchema1 } from '@atlas/types/schemas/wood-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect, useState } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\nimport { z } from 'zod'\r\n\r\n// Partial Material Factor dropdown options\r\nconst PARTIAL_MATERIAL_FACTOR_OPTIONS = [\r\n  { value: '1', label: '1' },\r\n  { value: '5', label: '5' },\r\n  { value: '45', label: '45' },\r\n]\r\n\r\n// Custom form schema that extends the API schema but with string for dropdown\r\nconst woodGeometryFormSchema = woodMaterialSchema1.extend({\r\n  partialMaterialFactor: z.string(), // Override to string for dropdown\r\n})\r\n\r\ntype FormSchema = z.infer<typeof woodGeometryFormSchema>\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FormSchema>\r\n  woodName?: string\r\n  onSave: () => void\r\n}\r\n\r\nexport const WoodGeometryForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  woodName: _woodName,\r\n  onSave: _onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.wood.geometry')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const {\r\n    data: woodNames,\r\n    isError: woodNamesError,\r\n    isLoading: woodNamesLoading,\r\n  } = useWoodNames({ session })\r\n  const woodNamesOptions = [\r\n    ...(woodNames?.map(m => ({ value: m, label: m })) ?? []),\r\n  ]\r\n  const form = useForm<FormSchema>({\r\n    resolver: zodResolver(woodGeometryFormSchema),\r\n    defaultValues: {\r\n      // Material Properties fields from woodMaterialSchema1\r\n      id: defaultValues?.id,\r\n      category: defaultValues?.woodName ?? '',\r\n      partialMaterialFactor: defaultValues?.partialMaterialFactor\r\n        ? defaultValues.partialMaterialFactor.toString()\r\n        : '1',\r\n      characteristicBendingStrength:\r\n        defaultValues?.characteristicBendingStrength ?? 0,\r\n      characteristicShearStrength:\r\n        defaultValues?.characteristicShearStrength ?? 0,\r\n      characteristicTensileStrength:\r\n        defaultValues?.characteristicTensileStrength ?? 0,\r\n      characteristicCompressiveStrength:\r\n        defaultValues?.characteristicCompressiveStrength ?? 0,\r\n      meanDensity: defaultValues?.meanDensity ?? 0,\r\n      meanShearModulus: defaultValues?.meanShearModulus ?? 0,\r\n      elasticityModulusParallelToGrain:\r\n        defaultValues?.elasticityModulusParallelToGrain ?? 0,\r\n      meanElasticityModulus: defaultValues?.meanElasticityModulus ?? 0,\r\n    },\r\n    // Prevent form from resetting after successful submission\r\n    shouldUnregister: false,\r\n  })\r\n\r\n  // Reset form with new defaultValues when they change (component remount)\r\n  useEffect(() => {\r\n    if (defaultValues) {\r\n      form.reset({\r\n        id: defaultValues.id,\r\n        category: defaultValues.woodName ?? '',\r\n        partialMaterialFactor: defaultValues.partialMaterialFactor\r\n          ? defaultValues.partialMaterialFactor.toString()\r\n          : '1',\r\n        characteristicBendingStrength:\r\n          defaultValues.characteristicBendingStrength ?? 0,\r\n        characteristicShearStrength:\r\n          defaultValues.characteristicShearStrength ?? 0,\r\n        characteristicTensileStrength:\r\n          defaultValues.characteristicTensileStrength ?? 0,\r\n        characteristicCompressiveStrength:\r\n          defaultValues.characteristicCompressiveStrength ?? 0,\r\n        meanDensity: defaultValues.meanDensity ?? 0,\r\n        meanShearModulus: defaultValues.meanShearModulus ?? 0,\r\n        elasticityModulusParallelToGrain:\r\n          defaultValues.elasticityModulusParallelToGrain ?? 0,\r\n        meanElasticityModulus: defaultValues.meanElasticityModulus ?? 0,\r\n      })\r\n    }\r\n  }, [defaultValues, form])\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: () => {\r\n        toast.success(tAction('edit.success'))\r\n        // Query invalidation is handled by the mutation hook itself\r\n        // Don't call onSave() immediately to prevent form clearing\r\n        // The parent component should handle accordion state separately\r\n        // onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  // State to store category name from API - initialize from defaultValues if available\r\n  const [categoryName, setCategoryName] = useState<string>(\r\n    defaultValues?.woodName || '',\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (data: FormSchema) => {\r\n      // Include categoryName from API in the category field\r\n      const dataWithCategory = {\r\n        ...data,\r\n        category: categoryName || data.category,\r\n        woodName: data.category,\r\n        // Convert partialMaterialFactor from string to number for API\r\n        partialMaterialFactor: parseFloat(data.partialMaterialFactor),\r\n      }\r\n\r\n      // Send as materialProperties to match the API structure\r\n      mutate({\r\n        projectId,\r\n        moduleId,\r\n        body: { materialProperties: dataWithCategory as any },\r\n      })\r\n    },\r\n    [mutate, projectId, moduleId, categoryName],\r\n  )\r\n\r\n  // Watch for category changes to fetch wood material data\r\n  const selectedCategory = form.watch('category')\r\n\r\n  const { data: woodMaterialData } = useWoodMaterialByName(\r\n    session,\r\n    selectedCategory,\r\n    !!selectedCategory,\r\n  )\r\n\r\n  // Sync categoryName when defaultValues change (component remount or data refresh)\r\n  useEffect(() => {\r\n    if (defaultValues?.category && !categoryName) {\r\n      setCategoryName(defaultValues.category)\r\n    }\r\n  }, [defaultValues?.category, categoryName])\r\n\r\n  // Update form fields when wood material data is fetched\r\n  useEffect(() => {\r\n    if (woodMaterialData) {\r\n      // Store category name from API response\r\n      setCategoryName(woodMaterialData.category)\r\n\r\n      // Map API response to form fields\r\n      form.setValue(\r\n        'characteristicBendingStrength',\r\n        woodMaterialData.flexuralStrength,\r\n      )\r\n      form.setValue(\r\n        'characteristicShearStrength',\r\n        woodMaterialData.shearStrength,\r\n      )\r\n      form.setValue(\r\n        'characteristicTensileStrength',\r\n        woodMaterialData.tensileStrengthParallel,\r\n      )\r\n      form.setValue(\r\n        'characteristicCompressiveStrength',\r\n        woodMaterialData.compressiveStrengthParallel,\r\n      )\r\n      form.setValue('meanDensity', woodMaterialData.meanDensity)\r\n      form.setValue('meanShearModulus', woodMaterialData.shearModulusMean)\r\n      form.setValue(\r\n        'elasticityModulusParallelToGrain',\r\n        woodMaterialData.elasticModulusCharacteristicParallel,\r\n      )\r\n      form.setValue(\r\n        'meanElasticityModulus',\r\n        woodMaterialData.elasticModulusMeanParallel,\r\n      )\r\n    }\r\n  }, [woodMaterialData, form])\r\n\r\n  return (\r\n    <div className=\"flex flex-col 2xl:flex-row justify-center gap-2\">\r\n      <Form {...form}>\r\n        <form\r\n          className=\"space-y-4 rounded-md border p-4 grow\"\r\n          onSubmit={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          <SelectFormInput\r\n            control={form.control}\r\n            name=\"category\"\r\n            options={woodNamesOptions}\r\n            t={t}\r\n            loading={woodNamesLoading}\r\n            requestError={woodNamesError}\r\n            errorMessage={t('category.error')}\r\n            required={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"characteristicBendingStrength\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"characteristicShearStrength\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"characteristicTensileStrength\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"characteristicCompressiveStrength\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"meanDensity\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"meanShearModulus\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"elasticityModulusParallelToGrain\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"meanElasticityModulus\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <SelectFormInput\r\n            control={form.control}\r\n            name=\"partialMaterialFactor\"\r\n            options={PARTIAL_MATERIAL_FACTOR_OPTIONS}\r\n            t={t}\r\n            required={true}\r\n          />\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full sm:w-auto\"\r\n            disabled={isPending}\r\n            onClick={form.handleSubmit(handleFormSubmit)}\r\n          >\r\n            {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n            {tCommon('save')}\r\n          </Button>\r\n        </form>\r\n      </Form>\r\n      {/* <Image\r\n        src={'/assets/wood/wood_material_properties.jpg'}\r\n        alt=\"wood material properties\"\r\n        height={500}\r\n        width={250}\r\n        className=\"mx-auto rounded-md object-contain\"\r\n        priority\r\n      /> */}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;AAEA,2CAA2C;AAC3C,MAAM,kCAAkC;IACtC;QAAE,OAAO;QAAK,OAAO;IAAI;IACzB;QAAE,OAAO;QAAK,OAAO;IAAI;IACzB;QAAE,OAAO;QAAM,OAAO;IAAK;CAC5B;AAED,8EAA8E;AAC9E,MAAM,yBAAyB,iKAAmB,CAAC,MAAM,CAAC;IACxD,uBAAuB,qLAAC,CAAC,MAAM;AACjC;AAaO,MAAM,mBAAmB;QAAC,EAC/B,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,UAAU,SAAS,EACnB,QAAQ,OAAO,EACT;;IACN,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,EACJ,MAAM,SAAS,EACf,SAAS,cAAc,EACvB,WAAW,gBAAgB,EAC5B,GAAG,IAAA,kLAAY,EAAC;QAAE;IAAQ;QAErB;IADN,MAAM,mBAAmB;WACnB,CAAA,iBAAA,sBAAA,gCAAA,UAAW,GAAG,CAAC,CAAA,IAAK,CAAC;gBAAE,OAAO;gBAAG,OAAO;YAAE,CAAC,gBAA3C,4BAAA,iBAAiD,EAAE;KACxD;QAMa,yBAKR,8CAEA,4CAEA,8CAEA,kDACW,4BACK,iCAEhB,iDACqB;IArB3B,MAAM,OAAO,IAAA,4KAAO,EAAa;QAC/B,UAAU,IAAA,gLAAW,EAAC;QACtB,eAAe;YACb,sDAAsD;YACtD,EAAE,EAAE,0BAAA,oCAAA,cAAe,EAAE;YACrB,UAAU,CAAA,0BAAA,0BAAA,oCAAA,cAAe,QAAQ,cAAvB,qCAAA,0BAA2B;YACrC,uBAAuB,CAAA,0BAAA,oCAAA,cAAe,qBAAqB,IACvD,cAAc,qBAAqB,CAAC,QAAQ,KAC5C;YACJ,+BACE,CAAA,+CAAA,0BAAA,oCAAA,cAAe,6BAA6B,cAA5C,0DAAA,+CAAgD;YAClD,6BACE,CAAA,6CAAA,0BAAA,oCAAA,cAAe,2BAA2B,cAA1C,wDAAA,6CAA8C;YAChD,+BACE,CAAA,+CAAA,0BAAA,oCAAA,cAAe,6BAA6B,cAA5C,0DAAA,+CAAgD;YAClD,mCACE,CAAA,mDAAA,0BAAA,oCAAA,cAAe,iCAAiC,cAAhD,8DAAA,mDAAoD;YACtD,aAAa,CAAA,6BAAA,0BAAA,oCAAA,cAAe,WAAW,cAA1B,wCAAA,6BAA8B;YAC3C,kBAAkB,CAAA,kCAAA,0BAAA,oCAAA,cAAe,gBAAgB,cAA/B,6CAAA,kCAAmC;YACrD,kCACE,CAAA,kDAAA,0BAAA,oCAAA,cAAe,gCAAgC,cAA/C,6DAAA,kDAAmD;YACrD,uBAAuB,CAAA,uCAAA,0BAAA,oCAAA,cAAe,qBAAqB,cAApC,kDAAA,uCAAwC;QACjE;QACA,0DAA0D;QAC1D,kBAAkB;IACpB;IAEA,yEAAyE;IACzE,IAAA,0KAAS;sCAAC;YACR,IAAI,eAAe;oBAGL,yBAKR,8CAEA,4CAEA,8CAEA,kDACW,4BACK,iCAEhB,iDACqB;gBAlBzB,KAAK,KAAK,CAAC;oBACT,IAAI,cAAc,EAAE;oBACpB,UAAU,CAAA,0BAAA,cAAc,QAAQ,cAAtB,qCAAA,0BAA0B;oBACpC,uBAAuB,cAAc,qBAAqB,GACtD,cAAc,qBAAqB,CAAC,QAAQ,KAC5C;oBACJ,+BACE,CAAA,+CAAA,cAAc,6BAA6B,cAA3C,0DAAA,+CAA+C;oBACjD,6BACE,CAAA,6CAAA,cAAc,2BAA2B,cAAzC,wDAAA,6CAA6C;oBAC/C,+BACE,CAAA,+CAAA,cAAc,6BAA6B,cAA3C,0DAAA,+CAA+C;oBACjD,mCACE,CAAA,mDAAA,cAAc,iCAAiC,cAA/C,8DAAA,mDAAmD;oBACrD,aAAa,CAAA,6BAAA,cAAc,WAAW,cAAzB,wCAAA,6BAA6B;oBAC1C,kBAAkB,CAAA,kCAAA,cAAc,gBAAgB,cAA9B,6CAAA,kCAAkC;oBACpD,kCACE,CAAA,kDAAA,cAAc,gCAAgC,cAA9C,6DAAA,kDAAkD;oBACpD,uBAAuB,CAAA,uCAAA,cAAc,qBAAqB,cAAnC,kDAAA,uCAAuC;gBAChE;YACF;QACF;qCAAG;QAAC;QAAe;KAAK;IAExB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,mNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,SAAS;4DAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,4DAA4D;YAC5D,2DAA2D;YAC3D,gEAAgE;YAChE,WAAW;YACb;;QACA,OAAO;4DAAE,CAAA;gBACP,oJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAC7D;;IACF;IAGF,qFAAqF;IACrF,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAC9C,CAAA,0BAAA,oCAAA,cAAe,QAAQ,KAAI;IAG7B,MAAM,mBAAmB,IAAA,4KAAW;0DAClC,CAAC;YACC,sDAAsD;YACtD,MAAM,mBAAmB;gBACvB,GAAG,IAAI;gBACP,UAAU,gBAAgB,KAAK,QAAQ;gBACvC,UAAU,KAAK,QAAQ;gBACvB,8DAA8D;gBAC9D,uBAAuB,WAAW,KAAK,qBAAqB;YAC9D;YAEA,wDAAwD;YACxD,OAAO;gBACL;gBACA;gBACA,MAAM;oBAAE,oBAAoB;gBAAwB;YACtD;QACF;yDACA;QAAC;QAAQ;QAAW;QAAU;KAAa;IAG7C,yDAAyD;IACzD,MAAM,mBAAmB,KAAK,KAAK,CAAC;IAEpC,MAAM,EAAE,MAAM,gBAAgB,EAAE,GAAG,IAAA,qMAAqB,EACtD,SACA,kBACA,CAAC,CAAC;IAGJ,kFAAkF;IAClF,IAAA,0KAAS;sCAAC;YACR,IAAI,CAAA,0BAAA,oCAAA,cAAe,QAAQ,KAAI,CAAC,cAAc;gBAC5C,gBAAgB,cAAc,QAAQ;YACxC;QACF;qCAAG;QAAC,0BAAA,oCAAA,cAAe,QAAQ;QAAE;KAAa;IAE1C,wDAAwD;IACxD,IAAA,0KAAS;sCAAC;YACR,IAAI,kBAAkB;gBACpB,wCAAwC;gBACxC,gBAAgB,iBAAiB,QAAQ;gBAEzC,kCAAkC;gBAClC,KAAK,QAAQ,CACX,iCACA,iBAAiB,gBAAgB;gBAEnC,KAAK,QAAQ,CACX,+BACA,iBAAiB,aAAa;gBAEhC,KAAK,QAAQ,CACX,iCACA,iBAAiB,uBAAuB;gBAE1C,KAAK,QAAQ,CACX,qCACA,iBAAiB,2BAA2B;gBAE9C,KAAK,QAAQ,CAAC,eAAe,iBAAiB,WAAW;gBACzD,KAAK,QAAQ,CAAC,oBAAoB,iBAAiB,gBAAgB;gBACnE,KAAK,QAAQ,CACX,oCACA,iBAAiB,oCAAoC;gBAEvD,KAAK,QAAQ,CACX,yBACA,iBAAiB,0BAA0B;YAE/C;QACF;qCAAG;QAAC;QAAkB;KAAK;IAE3B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,2IAAI;YAAE,GAAG,IAAI;sBACZ,cAAA,6LAAC;gBACC,WAAU;gBACV,UAAU,KAAK,YAAY,CAAC;;kCAE5B,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,SAAS;wBACT,GAAG;wBACH,SAAS;wBACT,cAAc;wBACd,cAAc,EAAE;wBAChB,UAAU;;;;;;kCAEZ,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAEjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAEjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAEjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAEjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAEjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAEjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAEjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAEjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,SAAS;wBACT,GAAG;wBACH,UAAU;;;;;;kCAEZ,6LAAC,+IAAM;wBACL,MAAK;wBACL,WAAU;wBACV,UAAU;wBACV,SAAS,KAAK,YAAY,CAAC;;4BAE1B,2BAAa,6LAAC,uOAAW;gCAAC,WAAU;;;;;;4BACpC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;AAcrB;GA3Qa;;QAQD,4NAAe;QACT,4NAAe;QACf,4NAAe;QAK3B,kLAAY;QAIH,4KAAO;QAqDU,mNAA2B;QA6CtB,qMAAqB;;;KArH7C", "debugId": null}}, {"offset": {"line": 953, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { Badge } from '@atlas/components/ui/badge'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport { woodPreInterventionCheckSchema } from '@atlas/lib/api/modules/schemas/wood-params'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\nimport type { z } from 'zod'\r\n\r\n// Custom form schema that extends the API schema\r\nconst woodPreInterventionCheckFormSchema = woodPreInterventionCheckSchema\r\n\r\ntype FormSchema = z.infer<typeof woodPreInterventionCheckFormSchema>\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FormSchema>\r\n  geometryProperties?: any // Geometry properties from form 3\r\n  onSave: () => void\r\n  initialDeformation?: any\r\n}\r\n\r\nexport const WoodPreInterventionCheckForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  geometryProperties,\r\n  onSave,\r\n  initialDeformation,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.wood.preIntervationCheck')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  const form = useForm<FormSchema>({\r\n    resolver: zodResolver(woodPreInterventionCheckFormSchema),\r\n    defaultValues: {\r\n      maximumBendingMoment: defaultValues?.maximumBendingMoment ?? 0,\r\n      maximumShearForce: defaultValues?.maximumShearForce ?? 0,\r\n      designBendingStress: defaultValues?.designBendingStress ?? 0,\r\n      designBendingStrength: defaultValues?.designBendingStrength ?? 0,\r\n      bendingCheck: defaultValues?.bendingCheck ?? 0,\r\n      designShearStress: defaultValues?.designShearStress ?? 0,\r\n      designShearStrength: defaultValues?.designShearStrength ?? 0,\r\n      shearCheck: defaultValues?.shearCheck ?? 0,\r\n      permanentLoadPerLinearMeter:\r\n        defaultValues?.permanentLoadPerLinearMeter ?? 0,\r\n      imposedLoadPerLinearMeter: defaultValues?.imposedLoadPerLinearMeter ?? 0,\r\n      instantaneousDeflectionPermanentLoad:\r\n        defaultValues?.instantaneousDeflectionPermanentLoad ?? 0,\r\n      instantaneousDeflectionImposedLoad:\r\n        defaultValues?.instantaneousDeflectionImposedLoad ?? 0,\r\n      instantaneousDeflectionTotalLoads:\r\n        defaultValues?.instantaneousDeflectionTotalLoads ?? 0,\r\n      deformabilityCheck: defaultValues?.deformabilityCheck ?? 0,\r\n      combinationFactor: defaultValues?.combinationFactor ?? 0.3,\r\n      finalDeflectionTotalLoads: defaultValues?.finalDeflectionTotalLoads ?? 0,\r\n      finalCheckResult: defaultValues?.finalCheckResult ?? 0,\r\n    },\r\n  })\r\n\r\n  // Reset form when defaultValues change\r\n  useEffect(() => {\r\n    if (defaultValues) {\r\n      form.reset({\r\n        maximumBendingMoment: defaultValues.maximumBendingMoment ?? 0,\r\n        maximumShearForce: defaultValues.maximumShearForce ?? 0,\r\n        designBendingStress: defaultValues.designBendingStress ?? 0,\r\n        designBendingStrength: defaultValues.designBendingStrength ?? 0,\r\n        bendingCheck: defaultValues.bendingCheck ?? 0,\r\n        designShearStress: defaultValues.designShearStress ?? 0,\r\n        designShearStrength: defaultValues.designShearStrength ?? 0,\r\n        shearCheck: defaultValues.shearCheck ?? 0,\r\n        permanentLoadPerLinearMeter:\r\n          defaultValues.permanentLoadPerLinearMeter ?? 0,\r\n        imposedLoadPerLinearMeter: defaultValues.imposedLoadPerLinearMeter ?? 0,\r\n        instantaneousDeflectionPermanentLoad:\r\n          defaultValues.instantaneousDeflectionPermanentLoad ?? 0,\r\n        instantaneousDeflectionImposedLoad:\r\n          defaultValues.instantaneousDeflectionImposedLoad ?? 0,\r\n        instantaneousDeflectionTotalLoads:\r\n          defaultValues.instantaneousDeflectionTotalLoads ?? 0,\r\n        deformabilityCheck: defaultValues.deformabilityCheck ?? 0,\r\n        combinationFactor: defaultValues.combinationFactor ?? 0.3,\r\n        finalDeflectionTotalLoads: defaultValues.finalDeflectionTotalLoads ?? 0,\r\n        finalCheckResult: defaultValues.finalCheckResult ?? 0,\r\n      })\r\n    }\r\n  }, [defaultValues, form])\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: () => {\r\n        toast.success(tAction('edit.success'))\r\n        // Query invalidation is handled by the mutation hook itself\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (data: FormSchema) => {\r\n      // Send as preIntervationCheck to match the API structure\r\n      mutate({\r\n        projectId,\r\n        moduleId,\r\n        body: { preIntervationCheck: data } as any,\r\n      })\r\n    },\r\n    [mutate, projectId, moduleId],\r\n  )\r\n\r\n  // Watch form values for calculations\r\n  const [\r\n    maximumBendingMoment,\r\n    maximumShearForce,\r\n    permanentLoadPerLinearMeter,\r\n    imposedLoadPerLinearMeter,\r\n    combinationFactor,\r\n  ] = form.watch([\r\n    'maximumBendingMoment',\r\n    'maximumShearForce',\r\n    'permanentLoadPerLinearMeter',\r\n    'imposedLoadPerLinearMeter',\r\n    'combinationFactor',\r\n  ])\r\n\r\n  // Helper function to determine check result\r\n  const getCheckResult = (\r\n    value: number,\r\n    threshold: number,\r\n    isGreaterThan: boolean = true,\r\n  ) => {\r\n    if (isGreaterThan) {\r\n      return value > threshold ? 'Non-satisfy' : 'Satisfy'\r\n    }\r\n    return value < threshold ? 'Non-satisfy' : 'Satisfy'\r\n  }\r\n\r\n  // Helper function to get badge variant\r\n  const getBadgeVariant = (result: string) => {\r\n    return result === 'Satisfy' ? 'default' : 'destructive'\r\n  }\r\n\r\n  // Calculations based on form values and properties from other forms\r\n  useEffect(() => {\r\n    if (maximumBendingMoment && geometryProperties?.sectionModulus) {\r\n      const designBendingStress =\r\n        maximumBendingMoment / (geometryProperties.sectionModulus * 1000)\r\n      form.setValue('designBendingStress', designBendingStress)\r\n    }\r\n  }, [maximumBendingMoment, geometryProperties?.sectionModulus, form])\r\n\r\n  useEffect(() => {\r\n    if (geometryProperties?.designBendingStrength) {\r\n      form.setValue(\r\n        'designBendingStrength',\r\n        geometryProperties.designBendingStrength,\r\n      )\r\n    }\r\n  }, [geometryProperties?.designBendingStrength, form])\r\n\r\n  useEffect(() => {\r\n    const designBendingStress = form.getValues('designBendingStress')\r\n    const designBendingStrength = form.getValues('designBendingStrength')\r\n    if (designBendingStress && designBendingStrength) {\r\n      const bendingCheck = designBendingStress / designBendingStrength\r\n      form.setValue('bendingCheck', bendingCheck)\r\n    }\r\n  }, [form])\r\n\r\n  useEffect(() => {\r\n    if (\r\n      maximumShearForce &&\r\n      geometryProperties?.beamSectionHeight &&\r\n      geometryProperties?.beamSectionWidth\r\n    ) {\r\n      const designShearStress =\r\n        (3 * 1000 * maximumShearForce) /\r\n        (2 *\r\n          1000 *\r\n          geometryProperties.beamSectionHeight *\r\n          1000 *\r\n          geometryProperties.beamSectionWidth)\r\n      form.setValue('designShearStress', designShearStress)\r\n    }\r\n  }, [\r\n    maximumShearForce,\r\n    geometryProperties?.beamSectionHeight,\r\n    geometryProperties?.beamSectionWidth,\r\n    form,\r\n  ])\r\n\r\n  useEffect(() => {\r\n    if (geometryProperties?.designShearStrength) {\r\n      form.setValue(\r\n        'designShearStrength',\r\n        geometryProperties.designShearStrength,\r\n      )\r\n    }\r\n  }, [geometryProperties?.designShearStrength, form])\r\n\r\n  useEffect(() => {\r\n    const designShearStress = form.getValues('designShearStress')\r\n    const designShearStrength = form.getValues('designShearStrength')\r\n    if (designShearStress && designShearStrength) {\r\n      const shearCheck = designShearStress / designShearStrength\r\n      form.setValue('shearCheck', shearCheck)\r\n    }\r\n  }, [form])\r\n\r\n  // Deflection calculations\r\n  useEffect(() => {\r\n    if (\r\n      permanentLoadPerLinearMeter &&\r\n      geometryProperties?.beamSpan &&\r\n      geometryProperties?.elasticityInstantaneousModulus &&\r\n      geometryProperties?.inertiaMomentAboutY\r\n    ) {\r\n      const instantaneousDeflectionPermanentLoad =\r\n        (100 *\r\n          (5 *\r\n            permanentLoadPerLinearMeter *\r\n            geometryProperties.beamSpan ** 4)) /\r\n        (384 *\r\n          1000 *\r\n          geometryProperties.elasticityInstantaneousModulus *\r\n          geometryProperties.inertiaMomentAboutY)\r\n      form.setValue(\r\n        'instantaneousDeflectionPermanentLoad',\r\n        instantaneousDeflectionPermanentLoad,\r\n      )\r\n    }\r\n  }, [\r\n    permanentLoadPerLinearMeter,\r\n    geometryProperties?.beamSpan,\r\n    geometryProperties?.elasticityInstantaneousModulus,\r\n    geometryProperties?.inertiaMomentAboutY,\r\n    form,\r\n  ])\r\n\r\n  useEffect(() => {\r\n    if (\r\n      imposedLoadPerLinearMeter &&\r\n      geometryProperties?.beamSpan &&\r\n      geometryProperties?.elasticityInstantaneousModulus &&\r\n      geometryProperties?.inertiaMomentAboutY\r\n    ) {\r\n      const instantaneousDeflectionImposedLoad =\r\n        (100 *\r\n          (5 * imposedLoadPerLinearMeter * geometryProperties.beamSpan ** 4)) /\r\n        (384 *\r\n          1000 *\r\n          geometryProperties.elasticityInstantaneousModulus *\r\n          geometryProperties.inertiaMomentAboutY)\r\n      form.setValue(\r\n        'instantaneousDeflectionImposedLoad',\r\n        instantaneousDeflectionImposedLoad,\r\n      )\r\n    }\r\n  }, [\r\n    imposedLoadPerLinearMeter,\r\n    geometryProperties?.beamSpan,\r\n    geometryProperties?.elasticityInstantaneousModulus,\r\n    geometryProperties?.inertiaMomentAboutY,\r\n    form,\r\n  ])\r\n\r\n  useEffect(() => {\r\n    const instantaneousDeflectionPermanentLoad = form.getValues(\r\n      'instantaneousDeflectionPermanentLoad',\r\n    )\r\n    const instantaneousDeflectionImposedLoad = form.getValues(\r\n      'instantaneousDeflectionImposedLoad',\r\n    )\r\n    if (\r\n      instantaneousDeflectionPermanentLoad &&\r\n      instantaneousDeflectionImposedLoad\r\n    ) {\r\n      // Note: Adding 0 for the \"peli field\" as mentioned in requirements\r\n      const instantaneousDeflectionTotalLoads =\r\n        instantaneousDeflectionPermanentLoad +\r\n        instantaneousDeflectionImposedLoad +\r\n        initialDeformation\r\n      form.setValue(\r\n        'instantaneousDeflectionTotalLoads',\r\n        instantaneousDeflectionTotalLoads,\r\n      )\r\n    }\r\n  }, [form, initialDeformation])\r\n\r\n  useEffect(() => {\r\n    const instantaneousDeflectionTotalLoads = form.getValues(\r\n      'instantaneousDeflectionTotalLoads',\r\n    )\r\n    if (instantaneousDeflectionTotalLoads && geometryProperties?.beamSpan) {\r\n      const deformabilityCheck =\r\n        (100 * geometryProperties.beamSpan) / instantaneousDeflectionTotalLoads\r\n      form.setValue('deformabilityCheck', deformabilityCheck)\r\n    }\r\n  }, [geometryProperties?.beamSpan, form])\r\n\r\n  useEffect(() => {\r\n    const instantaneousDeflectionPermanentLoad = form.getValues(\r\n      'instantaneousDeflectionPermanentLoad',\r\n    )\r\n    const instantaneousDeflectionImposedLoad = form.getValues(\r\n      'instantaneousDeflectionImposedLoad',\r\n    )\r\n    if (\r\n      instantaneousDeflectionPermanentLoad &&\r\n      instantaneousDeflectionImposedLoad &&\r\n      combinationFactor &&\r\n      geometryProperties?.deformabilityFactor\r\n    ) {\r\n      // Note: Adding 0 for the \"peli field\" as mentioned in requirements\r\n      const finalDeflectionTotalLoads =\r\n        instantaneousDeflectionPermanentLoad *\r\n          (1 + geometryProperties.deformabilityFactor) +\r\n        instantaneousDeflectionImposedLoad *\r\n          (1 + combinationFactor * geometryProperties.deformabilityFactor) +\r\n        initialDeformation\r\n      form.setValue('finalDeflectionTotalLoads', finalDeflectionTotalLoads)\r\n    }\r\n  }, [\r\n    combinationFactor,\r\n    geometryProperties?.deformabilityFactor,\r\n    initialDeformation,\r\n    form,\r\n  ])\r\n\r\n  useEffect(() => {\r\n    const finalDeflectionTotalLoads = form.getValues(\r\n      'finalDeflectionTotalLoads',\r\n    )\r\n    if (finalDeflectionTotalLoads && geometryProperties?.beamSpan) {\r\n      const finalCheckResult =\r\n        (100 * geometryProperties.beamSpan) / finalDeflectionTotalLoads\r\n      form.setValue('finalCheckResult', finalCheckResult)\r\n    }\r\n  }, [geometryProperties?.beamSpan, form])\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        {/* User Input Fields */}\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"maximumBendingMoment\"\r\n          t={t}\r\n          required={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"maximumShearForce\"\r\n          t={t}\r\n          required={true}\r\n        />\r\n\r\n        {/* Calculated Bending Fields */}\r\n        <div className=\"space-y-4\">\r\n          <Separator />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"designBendingStress\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"designBendingStrength\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <div className=\"flex items-start gap-4 flex\">\r\n            <NumberFormInput\r\n              control={form.control}\r\n              name=\"bendingCheck\"\r\n              t={t}\r\n              disabled={true}\r\n              fieldContainerClassName=\"flex-1\"\r\n              decimalPlaces={3}\r\n            />\r\n            <Badge\r\n              variant={getBadgeVariant(\r\n                getCheckResult(form.watch('bendingCheck'), 1),\r\n              )}\r\n              className=\"mt-7\"\r\n            >\r\n              {getCheckResult(form.watch('bendingCheck'), 1)}\r\n            </Badge>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Calculated Shear Fields */}\r\n        <div className=\"space-y-4\">\r\n          <Separator />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"designShearStress\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"designShearStrength\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <div className=\"flex items-start gap-4 flex\">\r\n            <NumberFormInput\r\n              control={form.control}\r\n              name=\"shearCheck\"\r\n              t={t}\r\n              disabled={true}\r\n              fieldContainerClassName=\"flex-1\"\r\n              decimalPlaces={3}\r\n            />\r\n            <Badge\r\n              variant={getBadgeVariant(\r\n                getCheckResult(form.watch('shearCheck'), 1),\r\n              )}\r\n              className=\"mt-7\"\r\n            >\r\n              {getCheckResult(form.watch('shearCheck'), 1)}\r\n            </Badge>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Load Input Fields */}\r\n        <div className=\"space-y-4\">\r\n          <Separator />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"permanentLoadPerLinearMeter\"\r\n            t={t}\r\n            required={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"imposedLoadPerLinearMeter\"\r\n            t={t}\r\n            required={true}\r\n          />\r\n        </div>\r\n\r\n        {/* Deflection Analysis */}\r\n        <div className=\"space-y-4\">\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"instantaneousDeflectionPermanentLoad\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"instantaneousDeflectionImposedLoad\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"instantaneousDeflectionTotalLoads\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <div className=\"flex items-start gap-4 flex\">\r\n            <NumberFormInput\r\n              control={form.control}\r\n              name=\"deformabilityCheck\"\r\n              t={t}\r\n              disabled={true}\r\n              fieldContainerClassName=\"flex-1\"\r\n              decimalPlaces={3}\r\n            />\r\n            <Badge\r\n              variant={getBadgeVariant(\r\n                getCheckResult(form.watch('deformabilityCheck'), 300, false),\r\n              )}\r\n              className=\"mt-7\"\r\n            >\r\n              {getCheckResult(form.watch('deformabilityCheck'), 300, false)}\r\n            </Badge>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Final Analysis */}\r\n        <div className=\"space-y-4\">\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"combinationFactor\"\r\n            t={t}\r\n            required={false}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"finalDeflectionTotalLoads\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <div className=\"flex items-start gap-4 flex\">\r\n            <NumberFormInput\r\n              control={form.control}\r\n              name=\"finalCheckResult\"\r\n              t={t}\r\n              disabled={true}\r\n              fieldContainerClassName=\"flex-1\"\r\n              decimalPlaces={3}\r\n            />\r\n            <Badge\r\n              variant={getBadgeVariant(\r\n                getCheckResult(form.watch('finalCheckResult'), 200, false),\r\n              )}\r\n              className=\"mt-7\"\r\n            >\r\n              {getCheckResult(form.watch('finalCheckResult'), 200, false)}\r\n            </Badge>\r\n          </div>\r\n        </div>\r\n\r\n        <Button type=\"submit\" className=\"w-full sm:w-auto\" disabled={isPending}>\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AAGA,iDAAiD;AACjD,MAAM,qCAAqC,8LAA8B;AAclE,MAAM,+BAA+B;QAAC,EAC3C,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,kBAAkB,EAClB,MAAM,EACN,kBAAkB,EACZ;;IACN,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;QAKN,qCACH,kCACE,oCACE,sCACT,6BACK,kCACE,oCACT,2BAEV,4CACyB,0CAEzB,qDAEA,mDAEA,kDACkB,mCACD,kCACQ,0CACT;IAvBtB,MAAM,OAAO,IAAA,4KAAO,EAAa;QAC/B,UAAU,IAAA,gLAAW,EAAC;QACtB,eAAe;YACb,sBAAsB,CAAA,sCAAA,0BAAA,oCAAA,cAAe,oBAAoB,cAAnC,iDAAA,sCAAuC;YAC7D,mBAAmB,CAAA,mCAAA,0BAAA,oCAAA,cAAe,iBAAiB,cAAhC,8CAAA,mCAAoC;YACvD,qBAAqB,CAAA,qCAAA,0BAAA,oCAAA,cAAe,mBAAmB,cAAlC,gDAAA,qCAAsC;YAC3D,uBAAuB,CAAA,uCAAA,0BAAA,oCAAA,cAAe,qBAAqB,cAApC,kDAAA,uCAAwC;YAC/D,cAAc,CAAA,8BAAA,0BAAA,oCAAA,cAAe,YAAY,cAA3B,yCAAA,8BAA+B;YAC7C,mBAAmB,CAAA,mCAAA,0BAAA,oCAAA,cAAe,iBAAiB,cAAhC,8CAAA,mCAAoC;YACvD,qBAAqB,CAAA,qCAAA,0BAAA,oCAAA,cAAe,mBAAmB,cAAlC,gDAAA,qCAAsC;YAC3D,YAAY,CAAA,4BAAA,0BAAA,oCAAA,cAAe,UAAU,cAAzB,uCAAA,4BAA6B;YACzC,6BACE,CAAA,6CAAA,0BAAA,oCAAA,cAAe,2BAA2B,cAA1C,wDAAA,6CAA8C;YAChD,2BAA2B,CAAA,2CAAA,0BAAA,oCAAA,cAAe,yBAAyB,cAAxC,sDAAA,2CAA4C;YACvE,sCACE,CAAA,sDAAA,0BAAA,oCAAA,cAAe,oCAAoC,cAAnD,iEAAA,sDAAuD;YACzD,oCACE,CAAA,oDAAA,0BAAA,oCAAA,cAAe,kCAAkC,cAAjD,+DAAA,oDAAqD;YACvD,mCACE,CAAA,mDAAA,0BAAA,oCAAA,cAAe,iCAAiC,cAAhD,8DAAA,mDAAoD;YACtD,oBAAoB,CAAA,oCAAA,0BAAA,oCAAA,cAAe,kBAAkB,cAAjC,+CAAA,oCAAqC;YACzD,mBAAmB,CAAA,mCAAA,0BAAA,oCAAA,cAAe,iBAAiB,cAAhC,8CAAA,mCAAoC;YACvD,2BAA2B,CAAA,2CAAA,0BAAA,oCAAA,cAAe,yBAAyB,cAAxC,sDAAA,2CAA4C;YACvE,kBAAkB,CAAA,kCAAA,0BAAA,oCAAA,cAAe,gBAAgB,cAA/B,6CAAA,kCAAmC;QACvD;IACF;IAEA,uCAAuC;IACvC,IAAA,0KAAS;kDAAC;YACR,IAAI,eAAe;oBAEO,qCACH,kCACE,oCACE,sCACT,6BACK,kCACE,oCACT,2BAEV,4CACyB,0CAEzB,qDAEA,mDAEA,kDACkB,mCACD,kCACQ,0CACT;gBArBpB,KAAK,KAAK,CAAC;oBACT,sBAAsB,CAAA,sCAAA,cAAc,oBAAoB,cAAlC,iDAAA,sCAAsC;oBAC5D,mBAAmB,CAAA,mCAAA,cAAc,iBAAiB,cAA/B,8CAAA,mCAAmC;oBACtD,qBAAqB,CAAA,qCAAA,cAAc,mBAAmB,cAAjC,gDAAA,qCAAqC;oBAC1D,uBAAuB,CAAA,uCAAA,cAAc,qBAAqB,cAAnC,kDAAA,uCAAuC;oBAC9D,cAAc,CAAA,8BAAA,cAAc,YAAY,cAA1B,yCAAA,8BAA8B;oBAC5C,mBAAmB,CAAA,mCAAA,cAAc,iBAAiB,cAA/B,8CAAA,mCAAmC;oBACtD,qBAAqB,CAAA,qCAAA,cAAc,mBAAmB,cAAjC,gDAAA,qCAAqC;oBAC1D,YAAY,CAAA,4BAAA,cAAc,UAAU,cAAxB,uCAAA,4BAA4B;oBACxC,6BACE,CAAA,6CAAA,cAAc,2BAA2B,cAAzC,wDAAA,6CAA6C;oBAC/C,2BAA2B,CAAA,2CAAA,cAAc,yBAAyB,cAAvC,sDAAA,2CAA2C;oBACtE,sCACE,CAAA,sDAAA,cAAc,oCAAoC,cAAlD,iEAAA,sDAAsD;oBACxD,oCACE,CAAA,oDAAA,cAAc,kCAAkC,cAAhD,+DAAA,oDAAoD;oBACtD,mCACE,CAAA,mDAAA,cAAc,iCAAiC,cAA/C,8DAAA,mDAAmD;oBACrD,oBAAoB,CAAA,oCAAA,cAAc,kBAAkB,cAAhC,+CAAA,oCAAoC;oBACxD,mBAAmB,CAAA,mCAAA,cAAc,iBAAiB,cAA/B,8CAAA,mCAAmC;oBACtD,2BAA2B,CAAA,2CAAA,cAAc,yBAAyB,cAAvC,sDAAA,2CAA2C;oBACtE,kBAAkB,CAAA,kCAAA,cAAc,gBAAgB,cAA9B,6CAAA,kCAAkC;gBACtD;YACF;QACF;iDAAG;QAAC;QAAe;KAAK;IAExB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,mNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,SAAS;wEAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;gBACtB,4DAA4D;gBAC5D;YACF;;QACA,OAAO;wEAAE,CAAA;gBACP,oJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAC7D;;IACF;IAGF,MAAM,mBAAmB,IAAA,4KAAW;sEAClC,CAAC;YACC,yDAAyD;YACzD,OAAO;gBACL;gBACA;gBACA,MAAM;oBAAE,qBAAqB;gBAAK;YACpC;QACF;qEACA;QAAC;QAAQ;QAAW;KAAS;IAG/B,qCAAqC;IACrC,MAAM,CACJ,sBACA,mBACA,6BACA,2BACA,kBACD,GAAG,KAAK,KAAK,CAAC;QACb;QACA;QACA;QACA;QACA;KACD;IAED,4CAA4C;IAC5C,MAAM,iBAAiB,SACrB,OACA;YACA,iFAAyB;QAEzB,IAAI,eAAe;YACjB,OAAO,QAAQ,YAAY,gBAAgB;QAC7C;QACA,OAAO,QAAQ,YAAY,gBAAgB;IAC7C;IAEA,uCAAuC;IACvC,MAAM,kBAAkB,CAAC;QACvB,OAAO,WAAW,YAAY,YAAY;IAC5C;IAEA,oEAAoE;IACpE,IAAA,0KAAS;kDAAC;YACR,IAAI,yBAAwB,+BAAA,yCAAA,mBAAoB,cAAc,GAAE;gBAC9D,MAAM,sBACJ,uBAAuB,CAAC,mBAAmB,cAAc,GAAG,IAAI;gBAClE,KAAK,QAAQ,CAAC,uBAAuB;YACvC;QACF;iDAAG;QAAC;QAAsB,+BAAA,yCAAA,mBAAoB,cAAc;QAAE;KAAK;IAEnE,IAAA,0KAAS;kDAAC;YACR,IAAI,+BAAA,yCAAA,mBAAoB,qBAAqB,EAAE;gBAC7C,KAAK,QAAQ,CACX,yBACA,mBAAmB,qBAAqB;YAE5C;QACF;iDAAG;QAAC,+BAAA,yCAAA,mBAAoB,qBAAqB;QAAE;KAAK;IAEpD,IAAA,0KAAS;kDAAC;YACR,MAAM,sBAAsB,KAAK,SAAS,CAAC;YAC3C,MAAM,wBAAwB,KAAK,SAAS,CAAC;YAC7C,IAAI,uBAAuB,uBAAuB;gBAChD,MAAM,eAAe,sBAAsB;gBAC3C,KAAK,QAAQ,CAAC,gBAAgB;YAChC;QACF;iDAAG;QAAC;KAAK;IAET,IAAA,0KAAS;kDAAC;YACR,IACE,sBACA,+BAAA,yCAAA,mBAAoB,iBAAiB,MACrC,+BAAA,yCAAA,mBAAoB,gBAAgB,GACpC;gBACA,MAAM,oBACJ,AAAC,IAAI,OAAO,oBACZ,CAAC,IACC,OACA,mBAAmB,iBAAiB,GACpC,OACA,mBAAmB,gBAAgB;gBACvC,KAAK,QAAQ,CAAC,qBAAqB;YACrC;QACF;iDAAG;QACD;QACA,+BAAA,yCAAA,mBAAoB,iBAAiB;QACrC,+BAAA,yCAAA,mBAAoB,gBAAgB;QACpC;KACD;IAED,IAAA,0KAAS;kDAAC;YACR,IAAI,+BAAA,yCAAA,mBAAoB,mBAAmB,EAAE;gBAC3C,KAAK,QAAQ,CACX,uBACA,mBAAmB,mBAAmB;YAE1C;QACF;iDAAG;QAAC,+BAAA,yCAAA,mBAAoB,mBAAmB;QAAE;KAAK;IAElD,IAAA,0KAAS;kDAAC;YACR,MAAM,oBAAoB,KAAK,SAAS,CAAC;YACzC,MAAM,sBAAsB,KAAK,SAAS,CAAC;YAC3C,IAAI,qBAAqB,qBAAqB;gBAC5C,MAAM,aAAa,oBAAoB;gBACvC,KAAK,QAAQ,CAAC,cAAc;YAC9B;QACF;iDAAG;QAAC;KAAK;IAET,0BAA0B;IAC1B,IAAA,0KAAS;kDAAC;YACR,IACE,gCACA,+BAAA,yCAAA,mBAAoB,QAAQ,MAC5B,+BAAA,yCAAA,mBAAoB,8BAA8B,MAClD,+BAAA,yCAAA,mBAAoB,mBAAmB,GACvC;gBACA,MAAM,uCACJ,AAAC,MACC,CAAC,IACC,8BACA,mBAAmB,QAAQ,IAAI,CAAC,IACpC,CAAC,MACC,OACA,mBAAmB,8BAA8B,GACjD,mBAAmB,mBAAmB;gBAC1C,KAAK,QAAQ,CACX,wCACA;YAEJ;QACF;iDAAG;QACD;QACA,+BAAA,yCAAA,mBAAoB,QAAQ;QAC5B,+BAAA,yCAAA,mBAAoB,8BAA8B;QAClD,+BAAA,yCAAA,mBAAoB,mBAAmB;QACvC;KACD;IAED,IAAA,0KAAS;kDAAC;YACR,IACE,8BACA,+BAAA,yCAAA,mBAAoB,QAAQ,MAC5B,+BAAA,yCAAA,mBAAoB,8BAA8B,MAClD,+BAAA,yCAAA,mBAAoB,mBAAmB,GACvC;gBACA,MAAM,qCACJ,AAAC,MACC,CAAC,IAAI,4BAA4B,mBAAmB,QAAQ,IAAI,CAAC,IACnE,CAAC,MACC,OACA,mBAAmB,8BAA8B,GACjD,mBAAmB,mBAAmB;gBAC1C,KAAK,QAAQ,CACX,sCACA;YAEJ;QACF;iDAAG;QACD;QACA,+BAAA,yCAAA,mBAAoB,QAAQ;QAC5B,+BAAA,yCAAA,mBAAoB,8BAA8B;QAClD,+BAAA,yCAAA,mBAAoB,mBAAmB;QACvC;KACD;IAED,IAAA,0KAAS;kDAAC;YACR,MAAM,uCAAuC,KAAK,SAAS,CACzD;YAEF,MAAM,qCAAqC,KAAK,SAAS,CACvD;YAEF,IACE,wCACA,oCACA;gBACA,mEAAmE;gBACnE,MAAM,oCACJ,uCACA,qCACA;gBACF,KAAK,QAAQ,CACX,qCACA;YAEJ;QACF;iDAAG;QAAC;QAAM;KAAmB;IAE7B,IAAA,0KAAS;kDAAC;YACR,MAAM,oCAAoC,KAAK,SAAS,CACtD;YAEF,IAAI,sCAAqC,+BAAA,yCAAA,mBAAoB,QAAQ,GAAE;gBACrE,MAAM,qBACJ,AAAC,MAAM,mBAAmB,QAAQ,GAAI;gBACxC,KAAK,QAAQ,CAAC,sBAAsB;YACtC;QACF;iDAAG;QAAC,+BAAA,yCAAA,mBAAoB,QAAQ;QAAE;KAAK;IAEvC,IAAA,0KAAS;kDAAC;YACR,MAAM,uCAAuC,KAAK,SAAS,CACzD;YAEF,MAAM,qCAAqC,KAAK,SAAS,CACvD;YAEF,IACE,wCACA,sCACA,sBACA,+BAAA,yCAAA,mBAAoB,mBAAmB,GACvC;gBACA,mEAAmE;gBACnE,MAAM,4BACJ,uCACE,CAAC,IAAI,mBAAmB,mBAAmB,IAC7C,qCACE,CAAC,IAAI,oBAAoB,mBAAmB,mBAAmB,IACjE;gBACF,KAAK,QAAQ,CAAC,6BAA6B;YAC7C;QACF;iDAAG;QACD;QACA,+BAAA,yCAAA,mBAAoB,mBAAmB;QACvC;QACA;KACD;IAED,IAAA,0KAAS;kDAAC;YACR,MAAM,4BAA4B,KAAK,SAAS,CAC9C;YAEF,IAAI,8BAA6B,+BAAA,yCAAA,mBAAoB,QAAQ,GAAE;gBAC7D,MAAM,mBACJ,AAAC,MAAM,mBAAmB,QAAQ,GAAI;gBACxC,KAAK,QAAQ,CAAC,oBAAoB;YACpC;QACF;iDAAG;QAAC,+BAAA,yCAAA,mBAAoB,QAAQ;QAAE;KAAK;IAEvC,qBACE,6LAAC,2IAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,6LAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAG5B,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAIZ,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qJAAS;;;;;sCACV,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;4BACV,eAAe;;;;;;sCAEjB,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;4BACV,eAAe;;;;;;sCAEjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qLAAe;oCACd,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,GAAG;oCACH,UAAU;oCACV,yBAAwB;oCACxB,eAAe;;;;;;8CAEjB,6LAAC,6IAAK;oCACJ,SAAS,gBACP,eAAe,KAAK,KAAK,CAAC,iBAAiB;oCAE7C,WAAU;8CAET,eAAe,KAAK,KAAK,CAAC,iBAAiB;;;;;;;;;;;;;;;;;;8BAMlD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qJAAS;;;;;sCACV,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;4BACV,eAAe;;;;;;sCAEjB,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;4BACV,eAAe;;;;;;sCAEjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qLAAe;oCACd,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,GAAG;oCACH,UAAU;oCACV,yBAAwB;oCACxB,eAAe;;;;;;8CAEjB,6LAAC,6IAAK;oCACJ,SAAS,gBACP,eAAe,KAAK,KAAK,CAAC,eAAe;oCAE3C,WAAU;8CAET,eAAe,KAAK,KAAK,CAAC,eAAe;;;;;;;;;;;;;;;;;;8BAMhD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qJAAS;;;;;sCACV,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;;;;;;;8BAKd,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;4BACV,eAAe;;;;;;sCAEjB,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;4BACV,eAAe;;;;;;sCAEjB,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;4BACV,eAAe;;;;;;sCAEjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qLAAe;oCACd,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,GAAG;oCACH,UAAU;oCACV,yBAAwB;oCACxB,eAAe;;;;;;8CAEjB,6LAAC,6IAAK;oCACJ,SAAS,gBACP,eAAe,KAAK,KAAK,CAAC,uBAAuB,KAAK;oCAExD,WAAU;8CAET,eAAe,KAAK,KAAK,CAAC,uBAAuB,KAAK;;;;;;;;;;;;;;;;;;8BAM7D,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;4BACV,UAAU;;;;;;sCAEZ,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;4BACV,eAAe;;;;;;sCAEjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qLAAe;oCACd,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,GAAG;oCACH,UAAU;oCACV,yBAAwB;oCACxB,eAAe;;;;;;8CAEjB,6LAAC,6IAAK;oCACJ,SAAS,gBACP,eAAe,KAAK,KAAK,CAAC,qBAAqB,KAAK;oCAEtD,WAAU;8CAET,eAAe,KAAK,KAAK,CAAC,qBAAqB,KAAK;;;;;;;;;;;;;;;;;;8BAK3D,6LAAC,+IAAM;oBAAC,MAAK;oBAAS,WAAU;oBAAmB,UAAU;;wBAC1D,2BAAa,6LAAC,uOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB;GA1gBa;;QASD,4NAAe;QACT,4NAAe;QACf,4NAAe;QAElB,4KAAO;QAwDU,mNAA2B;;;KArE9C", "debugId": null}}, {"offset": {"line": 1627, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-geometry-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport { useProductsQuery } from '@atlas/lib/api/products/use-products-query'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect, useMemo } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { z } from 'zod'\r\n\r\nconst formSchema = z.object({\r\n  productId: z.string().min(1, 'Product is required'),\r\n  stripWidth: z.number().min(0.1, 'Strip width must be greater than 0'),\r\n  equivalentThickness: z\r\n    .number()\r\n    .min(0.001, 'Equivalent thickness must be greater than 0'),\r\n  layersNumber: z.number().min(1, 'Layers number must be at least 1'),\r\n  expositionType: z.enum(['INTERNAL', 'EXTERNAL', 'AGGRESSIVE']),\r\n  environmentalConversionFactor: z\r\n    .number()\r\n    .min(0, 'Environmental conversion factor must be positive'),\r\n})\r\n\r\ntype FormSchema = z.infer<typeof formSchema>\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FormSchema>\r\n  preInterventionData?: any\r\n  materialProperties?: any\r\n  geometryProperties?: any\r\n  onSave?: () => void\r\n  onGeometryChange?: (geometry: Partial<FormSchema>) => void\r\n  initialDeformation?: any\r\n}\r\n\r\nconst EXPOSITION_TYPE_OPTIONS = [\r\n  { value: 'INTERNAL', label: 'Internal' },\r\n  { value: 'EXTERNAL', label: 'External' },\r\n  { value: 'AGGRESSIVE', label: 'Aggressive' },\r\n]\r\n\r\nexport const WoodCompositeGeometryForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  preInterventionData: _preInterventionData,\r\n  materialProperties,\r\n  geometryProperties,\r\n  onSave,\r\n  onGeometryChange,\r\n  initialDeformation,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.wood.compositeGeometry')\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  const { data: productsData, isLoading: isLoadingProducts } = useProductsQuery(\r\n    'wood',\r\n    session.accessToken,\r\n  )\r\n\r\n  const productOptions = useMemo(() => {\r\n    if (!productsData?.content) {\r\n      return []\r\n    }\r\n    return productsData.content\r\n      .filter(product => product.categories.includes('wood'))\r\n      .map(product => ({\r\n        value: product.id,\r\n        label: product.name,\r\n      }))\r\n  }, [productsData])\r\n\r\n  const form = useForm<FormSchema>({\r\n    resolver: zodResolver(formSchema),\r\n    defaultValues: {\r\n      productId: defaultValues?.productId || '',\r\n      stripWidth: defaultValues?.stripWidth || 0,\r\n      equivalentThickness: defaultValues?.equivalentThickness || 0,\r\n      layersNumber: defaultValues?.layersNumber || 2.25,\r\n      expositionType: defaultValues?.expositionType || 'INTERNAL',\r\n      environmentalConversionFactor:\r\n        defaultValues?.environmentalConversionFactor || 0.95,\r\n    },\r\n  })\r\n\r\n  // Reset form when defaultValues change (for saved data after refresh)\r\n  useEffect(() => {\r\n    if (defaultValues) {\r\n      form.reset({\r\n        productId: defaultValues.productId ?? '',\r\n        stripWidth: defaultValues.stripWidth ?? 0,\r\n        equivalentThickness: defaultValues.equivalentThickness ?? 0,\r\n        layersNumber: defaultValues.layersNumber ?? 2.25,\r\n        expositionType: defaultValues.expositionType ?? 'INTERNAL',\r\n        environmentalConversionFactor:\r\n          defaultValues.environmentalConversionFactor ?? 0.95,\r\n      })\r\n    }\r\n  }, [defaultValues, form])\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(session.accessToken)\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (data: FormSchema) => {\r\n      // Save as part of postIntervationCheck structure with all inherited data\r\n      mutate({\r\n        projectId,\r\n        moduleId,\r\n        body: {\r\n          postIntervationCheck: {\r\n            // Include inherited data from pre-intervention\r\n            initialDeformation: initialDeformation || 0,\r\n            materialProperties: materialProperties || null,\r\n            geometry: geometryProperties || null,\r\n            // Include the current form data\r\n            compositeGeometry: data,\r\n          },\r\n        } as any,\r\n      })\r\n      onSave?.()\r\n    },\r\n    [\r\n      mutate,\r\n      projectId,\r\n      moduleId,\r\n      onSave,\r\n      materialProperties,\r\n      geometryProperties,\r\n      initialDeformation,\r\n    ],\r\n  )\r\n\r\n  // Watch for exposition type changes to update environmental conversion factor\r\n  const expositionType = form.watch('expositionType')\r\n  const selectedProductId = form.watch('productId')\r\n\r\n  useEffect(() => {\r\n    let factor = 0.85 // default for external and aggressive\r\n    if (expositionType === 'INTERNAL') {\r\n      factor = 0.95\r\n    }\r\n    form.setValue('environmentalConversionFactor', factor)\r\n  }, [expositionType, form])\r\n\r\n  // Update equivalent thickness when product changes\r\n  useEffect(() => {\r\n    if (selectedProductId && productsData?.content) {\r\n      const selectedProduct = productsData.content.find(\r\n        p => p.id === selectedProductId,\r\n      )\r\n      if (selectedProduct) {\r\n        form.setValue('equivalentThickness', selectedProduct.thickness)\r\n      }\r\n    }\r\n  }, [selectedProductId, productsData, form])\r\n\r\n  // Watch specific form values and notify parent component of changes\r\n  const stripWidth = form.watch('stripWidth')\r\n  const layersNumber = form.watch('layersNumber')\r\n  const equivalentThickness = form.watch('equivalentThickness')\r\n  const environmentalConversionFactor = form.watch(\r\n    'environmentalConversionFactor',\r\n  )\r\n\r\n  useEffect(() => {\r\n    // Only notify parent if we have a valid product selection and the callback exists\r\n    if (onGeometryChange && selectedProductId && stripWidth > 0) {\r\n      const geometryData = {\r\n        productId: selectedProductId,\r\n        stripWidth,\r\n        layersNumber,\r\n        equivalentThickness,\r\n        expositionType,\r\n        environmentalConversionFactor,\r\n      }\r\n      onGeometryChange(geometryData)\r\n    }\r\n  }, [\r\n    selectedProductId,\r\n    stripWidth,\r\n    layersNumber,\r\n    equivalentThickness,\r\n    expositionType,\r\n    environmentalConversionFactor,\r\n    onGeometryChange,\r\n  ])\r\n\r\n  return (\r\n    <div className=\"flex flex-col 2xl:flex-row justify-center gap-2\">\r\n      <Form {...form}>\r\n        <form\r\n          className=\"space-y-4 rounded-md border p-4 grow\"\r\n          onSubmit={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          <SelectFormInput\r\n            control={form.control}\r\n            name=\"productId\"\r\n            options={productOptions}\r\n            t={t}\r\n            loading={isLoadingProducts}\r\n            required={true}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"stripWidth\"\r\n            t={t}\r\n            required={true}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"equivalentThickness\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"layersNumber\"\r\n            t={t}\r\n            required={true}\r\n          />\r\n\r\n          <SelectFormInput\r\n            control={form.control}\r\n            name=\"expositionType\"\r\n            options={EXPOSITION_TYPE_OPTIONS}\r\n            t={t}\r\n            required={true}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"environmentalConversionFactor\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full sm:w-auto\"\r\n            disabled={isPending}\r\n          >\r\n            {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n            {tCommon('save')}\r\n          </Button>\r\n        </form>\r\n      </Form>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;AAEA,MAAM,aAAa,qLAAC,CAAC,MAAM,CAAC;IAC1B,WAAW,qLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,YAAY,qLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK;IAChC,qBAAqB,qLAAC,CACnB,MAAM,GACN,GAAG,CAAC,OAAO;IACd,cAAc,qLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAChC,gBAAgB,qLAAC,CAAC,IAAI,CAAC;QAAC;QAAY;QAAY;KAAa;IAC7D,+BAA+B,qLAAC,CAC7B,MAAM,GACN,GAAG,CAAC,GAAG;AACZ;AAiBA,MAAM,0BAA0B;IAC9B;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAc,OAAO;IAAa;CAC5C;AAEM,MAAM,4BAA4B;QAAC,EACxC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,qBAAqB,oBAAoB,EACzC,kBAAkB,EAClB,kBAAkB,EAClB,MAAM,EACN,gBAAgB,EAChB,kBAAkB,EACZ;;IACN,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,4NAAe,EAAC;IAEhC,MAAM,EAAE,MAAM,YAAY,EAAE,WAAW,iBAAiB,EAAE,GAAG,IAAA,gLAAgB,EAC3E,QACA,QAAQ,WAAW;IAGrB,MAAM,iBAAiB,IAAA,wKAAO;6DAAC;YAC7B,IAAI,EAAC,yBAAA,mCAAA,aAAc,OAAO,GAAE;gBAC1B,OAAO,EAAE;YACX;YACA,OAAO,aAAa,OAAO,CACxB,MAAM;qEAAC,CAAA,UAAW,QAAQ,UAAU,CAAC,QAAQ,CAAC;oEAC9C,GAAG;qEAAC,CAAA,UAAW,CAAC;wBACf,OAAO,QAAQ,EAAE;wBACjB,OAAO,QAAQ,IAAI;oBACrB,CAAC;;QACL;4DAAG;QAAC;KAAa;IAEjB,MAAM,OAAO,IAAA,4KAAO,EAAa;QAC/B,UAAU,IAAA,gLAAW,EAAC;QACtB,eAAe;YACb,WAAW,CAAA,0BAAA,oCAAA,cAAe,SAAS,KAAI;YACvC,YAAY,CAAA,0BAAA,oCAAA,cAAe,UAAU,KAAI;YACzC,qBAAqB,CAAA,0BAAA,oCAAA,cAAe,mBAAmB,KAAI;YAC3D,cAAc,CAAA,0BAAA,oCAAA,cAAe,YAAY,KAAI;YAC7C,gBAAgB,CAAA,0BAAA,oCAAA,cAAe,cAAc,KAAI;YACjD,+BACE,CAAA,0BAAA,oCAAA,cAAe,6BAA6B,KAAI;QACpD;IACF;IAEA,sEAAsE;IACtE,IAAA,0KAAS;+CAAC;YACR,IAAI,eAAe;oBAEJ,0BACC,2BACS,oCACP,6BACE,+BAEd;gBAPJ,KAAK,KAAK,CAAC;oBACT,WAAW,CAAA,2BAAA,cAAc,SAAS,cAAvB,sCAAA,2BAA2B;oBACtC,YAAY,CAAA,4BAAA,cAAc,UAAU,cAAxB,uCAAA,4BAA4B;oBACxC,qBAAqB,CAAA,qCAAA,cAAc,mBAAmB,cAAjC,gDAAA,qCAAqC;oBAC1D,cAAc,CAAA,8BAAA,cAAc,YAAY,cAA1B,yCAAA,8BAA8B;oBAC5C,gBAAgB,CAAA,gCAAA,cAAc,cAAc,cAA5B,2CAAA,gCAAgC;oBAChD,+BACE,CAAA,+CAAA,cAAc,6BAA6B,cAA3C,0DAAA,+CAA+C;gBACnD;YACF;QACF;8CAAG;QAAC;QAAe;KAAK;IAExB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,mNAA2B,EAAC,QAAQ,WAAW;IAE7E,MAAM,mBAAmB,IAAA,4KAAW;mEAClC,CAAC;YACC,yEAAyE;YACzE,OAAO;gBACL;gBACA;gBACA,MAAM;oBACJ,sBAAsB;wBACpB,+CAA+C;wBAC/C,oBAAoB,sBAAsB;wBAC1C,oBAAoB,sBAAsB;wBAC1C,UAAU,sBAAsB;wBAChC,gCAAgC;wBAChC,mBAAmB;oBACrB;gBACF;YACF;YACA,mBAAA,6BAAA;QACF;kEACA;QACE;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAGH,8EAA8E;IAC9E,MAAM,iBAAiB,KAAK,KAAK,CAAC;IAClC,MAAM,oBAAoB,KAAK,KAAK,CAAC;IAErC,IAAA,0KAAS;+CAAC;YACR,IAAI,SAAS,KAAK,sCAAsC;;YACxD,IAAI,mBAAmB,YAAY;gBACjC,SAAS;YACX;YACA,KAAK,QAAQ,CAAC,iCAAiC;QACjD;8CAAG;QAAC;QAAgB;KAAK;IAEzB,mDAAmD;IACnD,IAAA,0KAAS;+CAAC;YACR,IAAI,sBAAqB,yBAAA,mCAAA,aAAc,OAAO,GAAE;gBAC9C,MAAM,kBAAkB,aAAa,OAAO,CAAC,IAAI;2EAC/C,CAAA,IAAK,EAAE,EAAE,KAAK;;gBAEhB,IAAI,iBAAiB;oBACnB,KAAK,QAAQ,CAAC,uBAAuB,gBAAgB,SAAS;gBAChE;YACF;QACF;8CAAG;QAAC;QAAmB;QAAc;KAAK;IAE1C,oEAAoE;IACpE,MAAM,aAAa,KAAK,KAAK,CAAC;IAC9B,MAAM,eAAe,KAAK,KAAK,CAAC;IAChC,MAAM,sBAAsB,KAAK,KAAK,CAAC;IACvC,MAAM,gCAAgC,KAAK,KAAK,CAC9C;IAGF,IAAA,0KAAS;+CAAC;YACR,kFAAkF;YAClF,IAAI,oBAAoB,qBAAqB,aAAa,GAAG;gBAC3D,MAAM,eAAe;oBACnB,WAAW;oBACX;oBACA;oBACA;oBACA;oBACA;gBACF;gBACA,iBAAiB;YACnB;QACF;8CAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,2IAAI;YAAE,GAAG,IAAI;sBACZ,cAAA,6LAAC;gBACC,WAAU;gBACV,UAAU,KAAK,YAAY,CAAC;;kCAE5B,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,SAAS;wBACT,GAAG;wBACH,SAAS;wBACT,UAAU;;;;;;kCAGZ,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;;;;;;kCAGZ,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAGjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;;;;;;kCAGZ,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,SAAS;wBACT,GAAG;wBACH,UAAU;;;;;;kCAGZ,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAGjB,6LAAC,+IAAM;wBACL,MAAK;wBACL,WAAU;wBACV,UAAU;;4BAET,2BAAa,6LAAC,uOAAW;gCAAC,WAAU;;;;;;4BACpC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;AAMrB;GArNa;;QAYD,4NAAe;QACT,4NAAe;QAE8B,gLAAgB;QAiBhE,4KAAO;QA4BU,mNAA2B;;;KA5D9C", "debugId": null}}, {"offset": {"line": 1956, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport { useProductsQuery } from '@atlas/lib/api/products/use-products-query'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { z } from 'zod'\r\n\r\nconst formSchema = z.object({\r\n  frpElasticityModulus: z\r\n    .number()\r\n    .min(0, 'FRP elasticity modulus must be positive'),\r\n  frpDesignMaximumStrain: z\r\n    .number()\r\n    .min(0, 'FRP design maximum strain must be positive'),\r\n  frpCharacteristicStrain: z\r\n    .number()\r\n    .min(0, 'FRP characteristic strain must be positive'),\r\n  frpPartialFactorInUls: z\r\n    .number()\r\n    .min(0, 'FRP partial factor in ULS must be positive'),\r\n  frpMaximumStrainForDebonding: z\r\n    .number()\r\n    .min(0, 'FRP maximum strain for debonding must be positive'),\r\n  loadConditionFactor: z\r\n    .number()\r\n    .min(0, 'Load condition factor must be positive'),\r\n  frpPartialFactorInUlsForDebonding: z\r\n    .number()\r\n    .min(0, 'FRP partial factor in ULS for debonding must be positive'),\r\n  reinforcementToSectionWidthRatio: z\r\n    .number()\r\n    .min(0, 'Reinforcement to section width ratio must be positive'),\r\n  reinforcementToSectionWidthRatioUsefull: z\r\n    .number()\r\n    .min(0, 'Reinforcement to section width ratio useful must be positive'),\r\n  geometricCorrectionFactor: z\r\n    .number()\r\n    .min(0, 'Geometric correction factor must be positive'),\r\n  geometricCorrectionFactorUsefull: z\r\n    .number()\r\n    .min(0, 'Geometric correction factor useful must be positive'),\r\n  experimentalCorrectionFactor: z\r\n    .number()\r\n    .min(0, 'Experimental correction factor must be positive'),\r\n  confidenceFactor: z.number().min(0, 'Confidence factor must be positive'),\r\n  sectionModulus: z.number().min(0, 'Section modulus must be positive'),\r\n  momentOfInertiaAboutY: z\r\n    .number()\r\n    .min(0, 'Moment of inertia about Y must be positive'),\r\n})\r\n\r\ntype FormSchema = z.infer<typeof formSchema>\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FormSchema>\r\n  compositeGeometry?: any\r\n  preInterventionData?: any\r\n  materialProperties?: any\r\n  geometryProperties?: any\r\n  onSave?: () => void\r\n  onPropertiesChange?: (properties: any) => void\r\n  initialDeformation?: any\r\n}\r\n\r\nexport const WoodCompositePropertiesForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  compositeGeometry,\r\n  preInterventionData: _preInterventionData,\r\n  materialProperties,\r\n  geometryProperties,\r\n  onSave,\r\n  onPropertiesChange,\r\n  initialDeformation,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.wood.compositeProperties')\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  const { data: productsData } = useProductsQuery('wood', session.accessToken)\r\n\r\n  const form = useForm<FormSchema>({\r\n    resolver: zodResolver(formSchema),\r\n    defaultValues: {\r\n      frpElasticityModulus: defaultValues?.frpElasticityModulus || 0,\r\n      frpDesignMaximumStrain: defaultValues?.frpDesignMaximumStrain || 0,\r\n      frpCharacteristicStrain: defaultValues?.frpCharacteristicStrain || 0,\r\n      frpPartialFactorInUls: defaultValues?.frpPartialFactorInUls || 1.1,\r\n      frpMaximumStrainForDebonding:\r\n        defaultValues?.frpMaximumStrainForDebonding || 0,\r\n      loadConditionFactor: defaultValues?.loadConditionFactor || 1.25,\r\n      frpPartialFactorInUlsForDebonding:\r\n        defaultValues?.frpPartialFactorInUlsForDebonding || 1.2,\r\n      reinforcementToSectionWidthRatio:\r\n        defaultValues?.reinforcementToSectionWidthRatio || 0,\r\n      reinforcementToSectionWidthRatioUsefull:\r\n        defaultValues?.reinforcementToSectionWidthRatioUsefull || 0,\r\n      geometricCorrectionFactor: defaultValues?.geometricCorrectionFactor || 0,\r\n      geometricCorrectionFactorUsefull:\r\n        defaultValues?.geometricCorrectionFactorUsefull || 0,\r\n      experimentalCorrectionFactor:\r\n        defaultValues?.experimentalCorrectionFactor || 0.1,\r\n      confidenceFactor: defaultValues?.confidenceFactor || 1.35,\r\n      sectionModulus: defaultValues?.sectionModulus || 0,\r\n      momentOfInertiaAboutY: defaultValues?.momentOfInertiaAboutY || 0,\r\n    },\r\n  })\r\n\r\n  // Reset form when defaultValues change (for saved data after refresh)\r\n  useEffect(() => {\r\n    if (defaultValues) {\r\n      form.reset({\r\n        frpElasticityModulus: defaultValues.frpElasticityModulus ?? 0,\r\n        frpDesignMaximumStrain: defaultValues.frpDesignMaximumStrain ?? 0,\r\n        frpCharacteristicStrain: defaultValues.frpCharacteristicStrain ?? 0,\r\n        frpPartialFactorInUls: defaultValues.frpPartialFactorInUls ?? 1.1,\r\n        frpMaximumStrainForDebonding:\r\n          defaultValues.frpMaximumStrainForDebonding ?? 0,\r\n        loadConditionFactor: defaultValues.loadConditionFactor ?? 1.25,\r\n        frpPartialFactorInUlsForDebonding:\r\n          defaultValues.frpPartialFactorInUlsForDebonding ?? 1.2,\r\n        reinforcementToSectionWidthRatio:\r\n          defaultValues.reinforcementToSectionWidthRatio ?? 0,\r\n        reinforcementToSectionWidthRatioUsefull:\r\n          defaultValues.reinforcementToSectionWidthRatioUsefull ?? 0,\r\n        geometricCorrectionFactor: defaultValues.geometricCorrectionFactor ?? 0,\r\n        geometricCorrectionFactorUsefull:\r\n          defaultValues.geometricCorrectionFactorUsefull ?? 0,\r\n        experimentalCorrectionFactor:\r\n          defaultValues.experimentalCorrectionFactor ?? 0.1,\r\n        confidenceFactor: defaultValues.confidenceFactor ?? 1.35,\r\n        sectionModulus: defaultValues.sectionModulus ?? 0,\r\n        momentOfInertiaAboutY: defaultValues.momentOfInertiaAboutY ?? 0,\r\n      })\r\n    }\r\n  }, [defaultValues, form])\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(session.accessToken)\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (data: FormSchema) => {\r\n      // Save as part of postIntervationCheck structure with all inherited data\r\n      mutate({\r\n        projectId,\r\n        moduleId,\r\n        body: {\r\n          postIntervationCheck: {\r\n            // Include inherited data from pre-intervention\r\n            initialDeformation: initialDeformation || 0,\r\n            materialProperties: materialProperties || null,\r\n            geometry: geometryProperties || null,\r\n            // Include composite geometry if available\r\n            compositeGeometry: compositeGeometry || null,\r\n            // Include the current form data\r\n            compositeProperties: data,\r\n          },\r\n        } as any,\r\n      })\r\n      onSave?.()\r\n    },\r\n    [\r\n      mutate,\r\n      projectId,\r\n      moduleId,\r\n      onSave,\r\n      materialProperties,\r\n      geometryProperties,\r\n      compositeGeometry,\r\n      initialDeformation,\r\n    ],\r\n  )\r\n\r\n  // Calculate derived values when dependencies change\r\n  useEffect(() => {\r\n    if (\r\n      !compositeGeometry ||\r\n      !materialProperties ||\r\n      !geometryProperties ||\r\n      !productsData?.content\r\n    ) {\r\n      return\r\n    }\r\n\r\n    const selectedProduct = productsData.content.find(\r\n      p => p.id === compositeGeometry.productId,\r\n    )\r\n    if (!selectedProduct) {\r\n      return\r\n    }\r\n\r\n    // Set FRP elasticity modulus from selected product\r\n    form.setValue('frpElasticityModulus', selectedProduct.elasticModulus)\r\n\r\n    // Set FRP characteristic strain from selected product\r\n    form.setValue('frpCharacteristicStrain', selectedProduct.systemDeformation)\r\n\r\n    // Calculate reinforcement to section width ratio\r\n    // Formula: strip_width (mm) / beamSectionWidth (m) - convert stripWidth to meters\r\n\r\n    const reinforcementRatio =\r\n      compositeGeometry.stripWidth / 1000 / geometryProperties.beamSectionWidth\r\n    form.setValue('reinforcementToSectionWidthRatio', reinforcementRatio)\r\n\r\n    // Calculate reinforcement to section width ratio useful\r\n    // If ratio < 0.25, use 0.25; otherwise use the ratio (corrected logic)\r\n    const reinforcementRatioUseful =\r\n      reinforcementRatio < 0.25 ? 0.25 : reinforcementRatio\r\n    form.setValue(\r\n      'reinforcementToSectionWidthRatioUsefull',\r\n      reinforcementRatioUseful,\r\n    )\r\n\r\n    // Calculate geometric correction factor\r\n    const geometricFactor =\r\n      ((2 - reinforcementRatioUseful) / (1 + reinforcementRatioUseful)) ** 0.5\r\n    form.setValue('geometricCorrectionFactor', geometricFactor)\r\n\r\n    // Calculate geometric correction factor useful\r\n    // If factor > 1, use the factor; otherwise use 1 (as per specification)\r\n    const geometricFactorUseful = geometricFactor > 1 ? geometricFactor : 1\r\n    form.setValue('geometricCorrectionFactorUsefull', geometricFactorUseful)\r\n\r\n    // Calculate moment of inertia about Y\r\n    const momentOfInertia =\r\n      (geometryProperties.beamSectionWidth *\r\n        geometryProperties.beamSectionHeight ** 3) /\r\n        12 +\r\n      (((((((compositeGeometry.layersNumber *\r\n        compositeGeometry.equivalentThickness) /\r\n        1000) *\r\n        compositeGeometry.stripWidth) /\r\n        1000) *\r\n        selectedProduct.elasticModulus) /\r\n        materialProperties.meanElasticityModulus) *\r\n        geometryProperties.beamSectionHeight ** 2) /\r\n        2\r\n    form.setValue('momentOfInertiaAboutY', momentOfInertia)\r\n\r\n    // Calculate section modulus\r\n    const sectionMod =\r\n      momentOfInertia / (geometryProperties.beamSectionHeight / 2)\r\n    form.setValue('sectionModulus', sectionMod)\r\n\r\n    // Calculate FRP maximum strain for debonding\r\n    // Formula: load_condition_factor/(frp_elasticity_modulus*frp_partial_factor_in_uls_for_debonding)*√[(frp_elasticity_modulus*2*geometric_correction_factor_usefull*experimental_correction_factor)/(layers_number*equivalent_thickness*confidence_factor)*√(characteristicTensileStrength*characteristicCompressiveStrength)]\r\n    const loadConditionFactor = form.getValues('loadConditionFactor')\r\n    const frpPartialFactorInUlsForDebonding = form.getValues(\r\n      'frpPartialFactorInUlsForDebonding',\r\n    )\r\n    const experimentalCorrectionFactor = form.getValues(\r\n      'experimentalCorrectionFactor',\r\n    )\r\n    const confidenceFactor = form.getValues('confidenceFactor')\r\n\r\n    const maxStrainDebonding =\r\n      (loadConditionFactor /\r\n        (selectedProduct.elasticModulus * frpPartialFactorInUlsForDebonding)) *\r\n      Math.sqrt(\r\n        (selectedProduct.elasticModulus *\r\n          2 *\r\n          geometricFactor *\r\n          experimentalCorrectionFactor *\r\n          Math.sqrt(\r\n            materialProperties.characteristicTensileStrength *\r\n              materialProperties.characteristicCompressiveStrength,\r\n          )) /\r\n          (compositeGeometry.layersNumber *\r\n            compositeGeometry.equivalentThickness *\r\n            confidenceFactor),\r\n      )\r\n\r\n    form.setValue('frpMaximumStrainForDebonding', maxStrainDebonding)\r\n\r\n    // STEP 9: Calculate frp_design_maximum_strain\r\n    // Formula: MIN(environmental_conversion_factor * frp_characteristic_strain / frp_partial_factor_in_uls, frp_maximum_strain_for_debonding)\r\n\r\n    // Get the required values\r\n    const environmentalConversionFactor =\r\n      compositeGeometry.environmentalConversionFactor\r\n    const frpCharacteristicStrain = selectedProduct.systemDeformation // This is frp_characteristic_strain\r\n    const frpPartialFactorInUls = form.getValues('frpPartialFactorInUls')\r\n\r\n    // Calculate the first part of MIN function\r\n    const designStrainFirstPart =\r\n      (environmentalConversionFactor * frpCharacteristicStrain) /\r\n      frpPartialFactorInUls\r\n\r\n    // Calculate the MIN of both values\r\n    const designMaxStrain = Math.min(designStrainFirstPart, maxStrainDebonding)\r\n\r\n    form.setValue('frpDesignMaximumStrain', designMaxStrain)\r\n\r\n    // Notify parent component of the calculated properties\r\n    if (onPropertiesChange) {\r\n      const currentProperties = {\r\n        frpElasticityModulus: selectedProduct.elasticModulus,\r\n        frpDesignMaximumStrain: designMaxStrain,\r\n        frpCharacteristicStrain: frpCharacteristicStrain,\r\n        frpPartialFactorInUls: form.getValues('frpPartialFactorInUls'),\r\n        frpMaximumStrainForDebonding: maxStrainDebonding,\r\n        loadConditionFactor: form.getValues('loadConditionFactor'),\r\n        frpPartialFactorInUlsForDebonding: form.getValues(\r\n          'frpPartialFactorInUlsForDebonding',\r\n        ),\r\n        reinforcementToSectionWidthRatio: reinforcementRatio,\r\n        reinforcementToSectionWidthRatioUsefull: reinforcementRatioUseful,\r\n        geometricCorrectionFactor: geometricFactor,\r\n        geometricCorrectionFactorUsefull: geometricFactorUseful,\r\n        experimentalCorrectionFactor: form.getValues(\r\n          'experimentalCorrectionFactor',\r\n        ),\r\n        confidenceFactor: form.getValues('confidenceFactor'),\r\n        sectionModulus: sectionMod,\r\n        momentOfInertiaAboutY: momentOfInertia,\r\n      }\r\n      onPropertiesChange(currentProperties)\r\n    }\r\n  }, [\r\n    compositeGeometry,\r\n    materialProperties,\r\n    geometryProperties,\r\n    productsData,\r\n    form,\r\n    onPropertiesChange,\r\n  ])\r\n\r\n  // Watch for changes in user input fields and recalculate dependent values\r\n  const _watchedValues = form.watch([\r\n    'frpPartialFactorInUls',\r\n    'loadConditionFactor',\r\n    'frpPartialFactorInUlsForDebonding',\r\n    'experimentalCorrectionFactor',\r\n    'confidenceFactor',\r\n  ])\r\n\r\n  useEffect(() => {\r\n    if (\r\n      !compositeGeometry ||\r\n      !materialProperties ||\r\n      !geometryProperties ||\r\n      !productsData?.content\r\n    ) {\r\n      return\r\n    }\r\n\r\n    const selectedProduct = productsData.content.find(\r\n      p => p.id === compositeGeometry.productId,\r\n    )\r\n    if (!selectedProduct) {\r\n      return\r\n    }\r\n\r\n    // Get current values\r\n    const geometricFactor = form.getValues('geometricCorrectionFactor')\r\n    const loadConditionFactor = form.getValues('loadConditionFactor')\r\n    const frpPartialFactorInUlsForDebonding = form.getValues(\r\n      'frpPartialFactorInUlsForDebonding',\r\n    )\r\n    const experimentalCorrectionFactor = form.getValues(\r\n      'experimentalCorrectionFactor',\r\n    )\r\n    const confidenceFactor = form.getValues('confidenceFactor')\r\n    const frpPartialFactorInUls = form.getValues('frpPartialFactorInUls')\r\n\r\n    // Recalculate FRP maximum strain for debonding when user inputs change\r\n    // Using the EXACT formula: (K / (E * gamma)) * Math.sqrt((E * 2 * kappa * eta * Math.sqrt(ft * fc)) / (n * t_eq * Cf))\r\n    const maxStrainDebonding =\r\n      (loadConditionFactor /\r\n        (selectedProduct.elasticModulus * frpPartialFactorInUlsForDebonding)) *\r\n      Math.sqrt(\r\n        (selectedProduct.elasticModulus *\r\n          2 *\r\n          geometricFactor *\r\n          experimentalCorrectionFactor *\r\n          Math.sqrt(\r\n            materialProperties.characteristicTensileStrength *\r\n              materialProperties.characteristicCompressiveStrength,\r\n          )) /\r\n          (compositeGeometry.layersNumber *\r\n            compositeGeometry.equivalentThickness *\r\n            confidenceFactor),\r\n      )\r\n\r\n    form.setValue('frpMaximumStrainForDebonding', maxStrainDebonding)\r\n\r\n    // Recalculate FRP design maximum strain\r\n    const recalcDesignStrainFirstPart =\r\n      (compositeGeometry.environmentalConversionFactor *\r\n        selectedProduct.systemDeformation) /\r\n      frpPartialFactorInUls\r\n    const designMaxStrain = Math.min(\r\n      recalcDesignStrainFirstPart,\r\n      maxStrainDebonding,\r\n    )\r\n\r\n    form.setValue('frpDesignMaximumStrain', designMaxStrain)\r\n\r\n    // Notify parent component of the updated properties\r\n    if (onPropertiesChange) {\r\n      const currentProperties = {\r\n        frpElasticityModulus: selectedProduct.elasticModulus,\r\n        frpDesignMaximumStrain: designMaxStrain,\r\n        frpCharacteristicStrain: selectedProduct.systemDeformation,\r\n        frpPartialFactorInUls: frpPartialFactorInUls,\r\n        frpMaximumStrainForDebonding: maxStrainDebonding,\r\n        loadConditionFactor: loadConditionFactor,\r\n        frpPartialFactorInUlsForDebonding: frpPartialFactorInUlsForDebonding,\r\n        reinforcementToSectionWidthRatio: form.getValues(\r\n          'reinforcementToSectionWidthRatio',\r\n        ),\r\n        reinforcementToSectionWidthRatioUsefull: form.getValues(\r\n          'reinforcementToSectionWidthRatioUsefull',\r\n        ),\r\n        geometricCorrectionFactor: geometricFactor,\r\n        geometricCorrectionFactorUsefull: form.getValues(\r\n          'geometricCorrectionFactorUsefull',\r\n        ),\r\n        experimentalCorrectionFactor: experimentalCorrectionFactor,\r\n        confidenceFactor: confidenceFactor,\r\n        sectionModulus: form.getValues('sectionModulus'),\r\n        momentOfInertiaAboutY: form.getValues('momentOfInertiaAboutY'),\r\n      }\r\n      onPropertiesChange(currentProperties)\r\n    }\r\n  }, [\r\n    compositeGeometry,\r\n    materialProperties,\r\n    geometryProperties,\r\n    productsData,\r\n    form,\r\n    onPropertiesChange,\r\n  ])\r\n\r\n  return (\r\n    <div className=\"flex flex-col 2xl:flex-row justify-center gap-2\">\r\n      <Form {...form}>\r\n        <form\r\n          className=\"space-y-4 rounded-md border p-4 grow\"\r\n          onSubmit={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"frpElasticityModulus\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"frpDesignMaximumStrain\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"frpCharacteristicStrain\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"frpPartialFactorInUls\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"frpMaximumStrainForDebonding\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"loadConditionFactor\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"frpPartialFactorInUlsForDebonding\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"reinforcementToSectionWidthRatio\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"reinforcementToSectionWidthRatioUsefull\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"geometricCorrectionFactor\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"geometricCorrectionFactorUsefull\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"experimentalCorrectionFactor\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"confidenceFactor\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"sectionModulus\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={6}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"momentOfInertiaAboutY\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={6}\r\n          />\r\n\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full sm:w-auto\"\r\n            disabled={isPending}\r\n          >\r\n            {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n            {tCommon('save')}\r\n          </Button>\r\n        </form>\r\n      </Form>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;AAEA,MAAM,aAAa,qLAAC,CAAC,MAAM,CAAC;IAC1B,sBAAsB,qLAAC,CACpB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,wBAAwB,qLAAC,CACtB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,yBAAyB,qLAAC,CACvB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,uBAAuB,qLAAC,CACrB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,8BAA8B,qLAAC,CAC5B,MAAM,GACN,GAAG,CAAC,GAAG;IACV,qBAAqB,qLAAC,CACnB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,mCAAmC,qLAAC,CACjC,MAAM,GACN,GAAG,CAAC,GAAG;IACV,kCAAkC,qLAAC,CAChC,MAAM,GACN,GAAG,CAAC,GAAG;IACV,yCAAyC,qLAAC,CACvC,MAAM,GACN,GAAG,CAAC,GAAG;IACV,2BAA2B,qLAAC,CACzB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,kCAAkC,qLAAC,CAChC,MAAM,GACN,GAAG,CAAC,GAAG;IACV,8BAA8B,qLAAC,CAC5B,MAAM,GACN,GAAG,CAAC,GAAG;IACV,kBAAkB,qLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACpC,gBAAgB,qLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAClC,uBAAuB,qLAAC,CACrB,MAAM,GACN,GAAG,CAAC,GAAG;AACZ;AAkBO,MAAM,8BAA8B;QAAC,EAC1C,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,iBAAiB,EACjB,qBAAqB,oBAAoB,EACzC,kBAAkB,EAClB,kBAAkB,EAClB,MAAM,EACN,kBAAkB,EAClB,kBAAkB,EACZ;;IACN,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,4NAAe,EAAC;IAEhC,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,IAAA,gLAAgB,EAAC,QAAQ,QAAQ,WAAW;IAE3E,MAAM,OAAO,IAAA,4KAAO,EAAa;QAC/B,UAAU,IAAA,gLAAW,EAAC;QACtB,eAAe;YACb,sBAAsB,CAAA,0BAAA,oCAAA,cAAe,oBAAoB,KAAI;YAC7D,wBAAwB,CAAA,0BAAA,oCAAA,cAAe,sBAAsB,KAAI;YACjE,yBAAyB,CAAA,0BAAA,oCAAA,cAAe,uBAAuB,KAAI;YACnE,uBAAuB,CAAA,0BAAA,oCAAA,cAAe,qBAAqB,KAAI;YAC/D,8BACE,CAAA,0BAAA,oCAAA,cAAe,4BAA4B,KAAI;YACjD,qBAAqB,CAAA,0BAAA,oCAAA,cAAe,mBAAmB,KAAI;YAC3D,mCACE,CAAA,0BAAA,oCAAA,cAAe,iCAAiC,KAAI;YACtD,kCACE,CAAA,0BAAA,oCAAA,cAAe,gCAAgC,KAAI;YACrD,yCACE,CAAA,0BAAA,oCAAA,cAAe,uCAAuC,KAAI;YAC5D,2BAA2B,CAAA,0BAAA,oCAAA,cAAe,yBAAyB,KAAI;YACvE,kCACE,CAAA,0BAAA,oCAAA,cAAe,gCAAgC,KAAI;YACrD,8BACE,CAAA,0BAAA,oCAAA,cAAe,4BAA4B,KAAI;YACjD,kBAAkB,CAAA,0BAAA,oCAAA,cAAe,gBAAgB,KAAI;YACrD,gBAAgB,CAAA,0BAAA,oCAAA,cAAe,cAAc,KAAI;YACjD,uBAAuB,CAAA,0BAAA,oCAAA,cAAe,qBAAqB,KAAI;QACjE;IACF;IAEA,sEAAsE;IACtE,IAAA,0KAAS;iDAAC;YACR,IAAI,eAAe;oBAEO,qCACE,uCACC,wCACF,sCAErB,6CACmB,oCAEnB,kDAEA,iDAEA,wDACyB,0CAEzB,iDAEA,6CACgB,iCACF,+BACO;gBArBzB,KAAK,KAAK,CAAC;oBACT,sBAAsB,CAAA,sCAAA,cAAc,oBAAoB,cAAlC,iDAAA,sCAAsC;oBAC5D,wBAAwB,CAAA,wCAAA,cAAc,sBAAsB,cAApC,mDAAA,wCAAwC;oBAChE,yBAAyB,CAAA,yCAAA,cAAc,uBAAuB,cAArC,oDAAA,yCAAyC;oBAClE,uBAAuB,CAAA,uCAAA,cAAc,qBAAqB,cAAnC,kDAAA,uCAAuC;oBAC9D,8BACE,CAAA,8CAAA,cAAc,4BAA4B,cAA1C,yDAAA,8CAA8C;oBAChD,qBAAqB,CAAA,qCAAA,cAAc,mBAAmB,cAAjC,gDAAA,qCAAqC;oBAC1D,mCACE,CAAA,mDAAA,cAAc,iCAAiC,cAA/C,8DAAA,mDAAmD;oBACrD,kCACE,CAAA,kDAAA,cAAc,gCAAgC,cAA9C,6DAAA,kDAAkD;oBACpD,yCACE,CAAA,yDAAA,cAAc,uCAAuC,cAArD,oEAAA,yDAAyD;oBAC3D,2BAA2B,CAAA,2CAAA,cAAc,yBAAyB,cAAvC,sDAAA,2CAA2C;oBACtE,kCACE,CAAA,kDAAA,cAAc,gCAAgC,cAA9C,6DAAA,kDAAkD;oBACpD,8BACE,CAAA,8CAAA,cAAc,4BAA4B,cAA1C,yDAAA,8CAA8C;oBAChD,kBAAkB,CAAA,kCAAA,cAAc,gBAAgB,cAA9B,6CAAA,kCAAkC;oBACpD,gBAAgB,CAAA,gCAAA,cAAc,cAAc,cAA5B,2CAAA,gCAAgC;oBAChD,uBAAuB,CAAA,uCAAA,cAAc,qBAAqB,cAAnC,kDAAA,uCAAuC;gBAChE;YACF;QACF;gDAAG;QAAC;QAAe;KAAK;IAExB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,mNAA2B,EAAC,QAAQ,WAAW;IAE7E,MAAM,mBAAmB,IAAA,4KAAW;qEAClC,CAAC;YACC,yEAAyE;YACzE,OAAO;gBACL;gBACA;gBACA,MAAM;oBACJ,sBAAsB;wBACpB,+CAA+C;wBAC/C,oBAAoB,sBAAsB;wBAC1C,oBAAoB,sBAAsB;wBAC1C,UAAU,sBAAsB;wBAChC,0CAA0C;wBAC1C,mBAAmB,qBAAqB;wBACxC,gCAAgC;wBAChC,qBAAqB;oBACvB;gBACF;YACF;YACA,mBAAA,6BAAA;QACF;oEACA;QACE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAGH,oDAAoD;IACpD,IAAA,0KAAS;iDAAC;YACR,IACE,CAAC,qBACD,CAAC,sBACD,CAAC,sBACD,EAAC,yBAAA,mCAAA,aAAc,OAAO,GACtB;gBACA;YACF;YAEA,MAAM,kBAAkB,aAAa,OAAO,CAAC,IAAI;yEAC/C,CAAA,IAAK,EAAE,EAAE,KAAK,kBAAkB,SAAS;;YAE3C,IAAI,CAAC,iBAAiB;gBACpB;YACF;YAEA,mDAAmD;YACnD,KAAK,QAAQ,CAAC,wBAAwB,gBAAgB,cAAc;YAEpE,sDAAsD;YACtD,KAAK,QAAQ,CAAC,2BAA2B,gBAAgB,iBAAiB;YAE1E,iDAAiD;YACjD,kFAAkF;YAElF,MAAM,qBACJ,kBAAkB,UAAU,GAAG,OAAO,mBAAmB,gBAAgB;YAC3E,KAAK,QAAQ,CAAC,oCAAoC;YAElD,wDAAwD;YACxD,uEAAuE;YACvE,MAAM,2BACJ,qBAAqB,OAAO,OAAO;YACrC,KAAK,QAAQ,CACX,2CACA;YAGF,wCAAwC;YACxC,MAAM,kBACJ,CAAC,CAAC,IAAI,wBAAwB,IAAI,CAAC,IAAI,wBAAwB,CAAC,KAAK;YACvE,KAAK,QAAQ,CAAC,6BAA6B;YAE3C,+CAA+C;YAC/C,wEAAwE;YACxE,MAAM,wBAAwB,kBAAkB,IAAI,kBAAkB;YACtE,KAAK,QAAQ,CAAC,oCAAoC;YAElD,sCAAsC;YACtC,MAAM,kBACJ,AAAC,mBAAmB,gBAAgB,GAClC,mBAAmB,iBAAiB,IAAI,IACxC,KACF,AAAO,kBAAkB,YAAY,GACnC,kBAAkB,mBAAmB,GACrC,OACA,kBAAkB,UAAU,GAC5B,OACA,gBAAgB,cAAc,GAC9B,mBAAmB,qBAAqB,GACxC,mBAAmB,iBAAiB,IAAI,IACxC;YACJ,KAAK,QAAQ,CAAC,yBAAyB;YAEvC,4BAA4B;YAC5B,MAAM,aACJ,kBAAkB,CAAC,mBAAmB,iBAAiB,GAAG,CAAC;YAC7D,KAAK,QAAQ,CAAC,kBAAkB;YAEhC,6CAA6C;YAC7C,6TAA6T;YAC7T,MAAM,sBAAsB,KAAK,SAAS,CAAC;YAC3C,MAAM,oCAAoC,KAAK,SAAS,CACtD;YAEF,MAAM,+BAA+B,KAAK,SAAS,CACjD;YAEF,MAAM,mBAAmB,KAAK,SAAS,CAAC;YAExC,MAAM,qBACJ,AAAC,sBACC,CAAC,gBAAgB,cAAc,GAAG,iCAAiC,IACrE,KAAK,IAAI,CACP,AAAC,gBAAgB,cAAc,GAC7B,IACA,kBACA,+BACA,KAAK,IAAI,CACP,mBAAmB,6BAA6B,GAC9C,mBAAmB,iCAAiC,IAExD,CAAC,kBAAkB,YAAY,GAC7B,kBAAkB,mBAAmB,GACrC,gBAAgB;YAGxB,KAAK,QAAQ,CAAC,gCAAgC;YAE9C,8CAA8C;YAC9C,0IAA0I;YAE1I,0BAA0B;YAC1B,MAAM,gCACJ,kBAAkB,6BAA6B;YACjD,MAAM,0BAA0B,gBAAgB,iBAAiB,CAAC,oCAAoC;;YACtG,MAAM,wBAAwB,KAAK,SAAS,CAAC;YAE7C,2CAA2C;YAC3C,MAAM,wBACJ,AAAC,gCAAgC,0BACjC;YAEF,mCAAmC;YACnC,MAAM,kBAAkB,KAAK,GAAG,CAAC,uBAAuB;YAExD,KAAK,QAAQ,CAAC,0BAA0B;YAExC,uDAAuD;YACvD,IAAI,oBAAoB;gBACtB,MAAM,oBAAoB;oBACxB,sBAAsB,gBAAgB,cAAc;oBACpD,wBAAwB;oBACxB,yBAAyB;oBACzB,uBAAuB,KAAK,SAAS,CAAC;oBACtC,8BAA8B;oBAC9B,qBAAqB,KAAK,SAAS,CAAC;oBACpC,mCAAmC,KAAK,SAAS,CAC/C;oBAEF,kCAAkC;oBAClC,yCAAyC;oBACzC,2BAA2B;oBAC3B,kCAAkC;oBAClC,8BAA8B,KAAK,SAAS,CAC1C;oBAEF,kBAAkB,KAAK,SAAS,CAAC;oBACjC,gBAAgB;oBAChB,uBAAuB;gBACzB;gBACA,mBAAmB;YACrB;QACF;gDAAG;QACD;QACA;QACA;QACA;QACA;QACA;KACD;IAED,0EAA0E;IAC1E,MAAM,iBAAiB,KAAK,KAAK,CAAC;QAChC;QACA;QACA;QACA;QACA;KACD;IAED,IAAA,0KAAS;iDAAC;YACR,IACE,CAAC,qBACD,CAAC,sBACD,CAAC,sBACD,EAAC,yBAAA,mCAAA,aAAc,OAAO,GACtB;gBACA;YACF;YAEA,MAAM,kBAAkB,aAAa,OAAO,CAAC,IAAI;yEAC/C,CAAA,IAAK,EAAE,EAAE,KAAK,kBAAkB,SAAS;;YAE3C,IAAI,CAAC,iBAAiB;gBACpB;YACF;YAEA,qBAAqB;YACrB,MAAM,kBAAkB,KAAK,SAAS,CAAC;YACvC,MAAM,sBAAsB,KAAK,SAAS,CAAC;YAC3C,MAAM,oCAAoC,KAAK,SAAS,CACtD;YAEF,MAAM,+BAA+B,KAAK,SAAS,CACjD;YAEF,MAAM,mBAAmB,KAAK,SAAS,CAAC;YACxC,MAAM,wBAAwB,KAAK,SAAS,CAAC;YAE7C,uEAAuE;YACvE,uHAAuH;YACvH,MAAM,qBACJ,AAAC,sBACC,CAAC,gBAAgB,cAAc,GAAG,iCAAiC,IACrE,KAAK,IAAI,CACP,AAAC,gBAAgB,cAAc,GAC7B,IACA,kBACA,+BACA,KAAK,IAAI,CACP,mBAAmB,6BAA6B,GAC9C,mBAAmB,iCAAiC,IAExD,CAAC,kBAAkB,YAAY,GAC7B,kBAAkB,mBAAmB,GACrC,gBAAgB;YAGxB,KAAK,QAAQ,CAAC,gCAAgC;YAE9C,wCAAwC;YACxC,MAAM,8BACJ,AAAC,kBAAkB,6BAA6B,GAC9C,gBAAgB,iBAAiB,GACnC;YACF,MAAM,kBAAkB,KAAK,GAAG,CAC9B,6BACA;YAGF,KAAK,QAAQ,CAAC,0BAA0B;YAExC,oDAAoD;YACpD,IAAI,oBAAoB;gBACtB,MAAM,oBAAoB;oBACxB,sBAAsB,gBAAgB,cAAc;oBACpD,wBAAwB;oBACxB,yBAAyB,gBAAgB,iBAAiB;oBAC1D,uBAAuB;oBACvB,8BAA8B;oBAC9B,qBAAqB;oBACrB,mCAAmC;oBACnC,kCAAkC,KAAK,SAAS,CAC9C;oBAEF,yCAAyC,KAAK,SAAS,CACrD;oBAEF,2BAA2B;oBAC3B,kCAAkC,KAAK,SAAS,CAC9C;oBAEF,8BAA8B;oBAC9B,kBAAkB;oBAClB,gBAAgB,KAAK,SAAS,CAAC;oBAC/B,uBAAuB,KAAK,SAAS,CAAC;gBACxC;gBACA,mBAAmB;YACrB;QACF;gDAAG;QACD;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,2IAAI;YAAE,GAAG,IAAI;sBACZ,cAAA,6LAAC;gBACC,WAAU;gBACV,UAAU,KAAK,YAAY,CAAC;;kCAE5B,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAGjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAGjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAGjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;;;;;;kCAGZ,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAGjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;;;;;;kCAGZ,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;;;;;;kCAGZ,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAGjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAGjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAGjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAGjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;;;;;;kCAGZ,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;;;;;;kCAGZ,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAGjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAGjB,6LAAC,+IAAM;wBACL,MAAK;wBACL,WAAU;wBACV,UAAU;;4BAET,2BAAa,6LAAC,uOAAW;gCAAC,WAAU;;;;;;4BACpC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;AAMrB;GAxfa;;QAaD,4NAAe;QACT,4NAAe;QAEA,gLAAgB;QAElC,4KAAO;QAwDU,mNAA2B;;;KA1E9C", "debugId": null}}, {"offset": {"line": 2462, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { Badge } from '@atlas/components/ui/badge'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { z } from 'zod'\r\n\r\nconst formSchema = z.object({\r\n  maximumBendingMoment: z\r\n    .number()\r\n    .min(0, 'Maximum bending moment must be positive'),\r\n  maximumShearForce: z.number().min(0, 'Maximum shear force must be positive'),\r\n  designBendingStress: z\r\n    .number()\r\n    .min(0, 'Design bending stress must be positive'),\r\n  designBendingStrength: z\r\n    .number()\r\n    .min(0, 'Design bending strength must be positive'),\r\n  designBendingCheck: z\r\n    .number()\r\n    .min(0, 'Design bending check must be positive'),\r\n  designShearStress: z.number().min(0, 'Design shear stress must be positive'),\r\n  designShearStrength: z\r\n    .number()\r\n    .min(0, 'Design shear strength must be positive'),\r\n  designShearCheck: z.number().min(0, 'Design shear check must be positive'),\r\n  permanentLoadPerLinearMeter: z\r\n    .number()\r\n    .min(0, 'Permanent load per linear meter must be positive'),\r\n  imposedLoadPerLinearMeter: z\r\n    .number()\r\n    .min(0, 'Imposed load per linear meter must be positive'),\r\n  instantaneousDeflectionPermanentLoad: z\r\n    .number()\r\n    .min(0, 'Instantaneous deflection permanent load must be positive'),\r\n  instantaneousDeflectionImposedLoad: z\r\n    .number()\r\n    .min(0, 'Instantaneous deflection imposed load must be positive'),\r\n  instantaneousDeflectionTotalLoads: z\r\n    .number()\r\n    .min(0, 'Instantaneous deflection total loads must be positive'),\r\n  instantaneousDeflectionCheck: z\r\n    .number()\r\n    .min(0, 'Instantaneous deflection check must be positive'),\r\n  combinationFactor: z.number().min(0, 'Combination factor must be positive'),\r\n  finalDeflectionTotalLoads: z\r\n    .number()\r\n    .min(0, 'Final deflection total loads must be positive'),\r\n  finalCheckResult: z.number().min(0, 'Final check result must be positive'),\r\n})\r\n\r\ntype FormSchema = z.infer<typeof formSchema>\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FormSchema>\r\n  preInterventionData?: any\r\n  materialProperties?: any\r\n  geometryProperties?: any\r\n  compositeGeometry?: any\r\n  compositeProperties?: any\r\n  onSave?: () => void\r\n  initialDeformation?: any\r\n}\r\n\r\nexport const WoodPostInterventionCheckForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  preInterventionData,\r\n  materialProperties,\r\n  geometryProperties,\r\n  compositeGeometry,\r\n  compositeProperties,\r\n  onSave,\r\n  initialDeformation,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.project-params.wood.resultOfPostIntervationCheck',\r\n  )\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  const form = useForm<FormSchema>({\r\n    resolver: zodResolver(formSchema),\r\n    defaultValues: {\r\n      maximumBendingMoment:\r\n        defaultValues?.maximumBendingMoment ||\r\n        preInterventionData?.maximumBendingMoment ||\r\n        0,\r\n      maximumShearForce:\r\n        defaultValues?.maximumShearForce ||\r\n        preInterventionData?.maximumShearForce ||\r\n        0,\r\n      designBendingStress: defaultValues?.designBendingStress || 0,\r\n      designBendingStrength: defaultValues?.designBendingStrength || 0,\r\n      designBendingCheck: defaultValues?.designBendingCheck || 0,\r\n      designShearStress: defaultValues?.designShearStress || 0,\r\n      designShearStrength: defaultValues?.designShearStrength || 0,\r\n      designShearCheck: defaultValues?.designShearCheck || 0,\r\n      permanentLoadPerLinearMeter:\r\n        defaultValues?.permanentLoadPerLinearMeter ||\r\n        preInterventionData?.permanentLoadPerLinearMeter ||\r\n        0,\r\n      imposedLoadPerLinearMeter:\r\n        defaultValues?.imposedLoadPerLinearMeter ||\r\n        preInterventionData?.imposedLoadPerLinearMeter ||\r\n        0,\r\n      instantaneousDeflectionPermanentLoad:\r\n        defaultValues?.instantaneousDeflectionPermanentLoad || 0,\r\n      instantaneousDeflectionImposedLoad:\r\n        defaultValues?.instantaneousDeflectionImposedLoad || 0,\r\n      instantaneousDeflectionTotalLoads:\r\n        defaultValues?.instantaneousDeflectionTotalLoads || 0,\r\n      instantaneousDeflectionCheck:\r\n        defaultValues?.instantaneousDeflectionCheck || 0,\r\n      combinationFactor:\r\n        defaultValues?.combinationFactor ||\r\n        preInterventionData?.combinationFactor ||\r\n        0,\r\n      finalDeflectionTotalLoads: defaultValues?.finalDeflectionTotalLoads || 0,\r\n      finalCheckResult: defaultValues?.finalCheckResult || 0,\r\n    },\r\n  })\r\n\r\n  // Reset form when defaultValues change (for saved data after refresh)\r\n  useEffect(() => {\r\n    if (\r\n      defaultValues &&\r\n      typeof defaultValues === 'object' &&\r\n      Object.keys(defaultValues).length > 0\r\n    ) {\r\n      // Create a complete form data object with all fields\r\n      const formData = {\r\n        maximumBendingMoment:\r\n          defaultValues.maximumBendingMoment ??\r\n          preInterventionData?.maximumBendingMoment ??\r\n          0,\r\n        maximumShearForce:\r\n          defaultValues.maximumShearForce ??\r\n          preInterventionData?.maximumShearForce ??\r\n          0,\r\n        designBendingStress: defaultValues.designBendingStress ?? 0,\r\n        designBendingStrength: defaultValues.designBendingStrength ?? 0,\r\n        designBendingCheck: defaultValues.designBendingCheck ?? 0,\r\n        designShearStress: defaultValues.designShearStress ?? 0,\r\n        designShearStrength: defaultValues.designShearStrength ?? 0,\r\n        designShearCheck: defaultValues.designShearCheck ?? 0,\r\n        permanentLoadPerLinearMeter:\r\n          defaultValues.permanentLoadPerLinearMeter ??\r\n          preInterventionData?.permanentLoadPerLinearMeter ??\r\n          0,\r\n        imposedLoadPerLinearMeter:\r\n          defaultValues.imposedLoadPerLinearMeter ??\r\n          preInterventionData?.imposedLoadPerLinearMeter ??\r\n          0,\r\n        instantaneousDeflectionPermanentLoad:\r\n          defaultValues.instantaneousDeflectionPermanentLoad ?? 0,\r\n        instantaneousDeflectionImposedLoad:\r\n          defaultValues.instantaneousDeflectionImposedLoad ?? 0,\r\n        instantaneousDeflectionTotalLoads:\r\n          defaultValues.instantaneousDeflectionTotalLoads ?? 0,\r\n        instantaneousDeflectionCheck:\r\n          defaultValues.instantaneousDeflectionCheck ?? 0,\r\n        combinationFactor:\r\n          defaultValues.combinationFactor ??\r\n          preInterventionData?.combinationFactor ??\r\n          0,\r\n        finalDeflectionTotalLoads: defaultValues.finalDeflectionTotalLoads ?? 0,\r\n        finalCheckResult: defaultValues.finalCheckResult ?? 0,\r\n      }\r\n\r\n      form.reset(formData)\r\n    } else {\r\n    }\r\n  }, [defaultValues, preInterventionData, form])\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(session.accessToken)\r\n\r\n  // Helper function to determine check result\r\n  const getCheckResult = (\r\n    value: number,\r\n    threshold: number,\r\n    checkType: 'lessThanOrEqual' | 'greaterThanOrEqual' = 'lessThanOrEqual',\r\n  ) => {\r\n    if (checkType === 'lessThanOrEqual') {\r\n      // For bending and shear checks: <= threshold = \"Satisfy\", > threshold = \"Non-satisfy\"\r\n      return value <= threshold ? 'Satisfy' : 'Non-satisfy'\r\n    }\r\n    // For deflection checks: >= threshold = \"Satisfy\", < threshold = \"Non-satisfy\"\r\n    return value >= threshold ? 'Satisfy' : 'Non-satisfy'\r\n  }\r\n\r\n  // Helper function to get badge variant\r\n  const getBadgeVariant = (result: string) => {\r\n    return result === 'Satisfy' ? 'default' : 'destructive'\r\n  }\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (data: FormSchema) => {\r\n      // Save as part of postIntervationCheck structure with all inherited data\r\n      mutate({\r\n        projectId,\r\n        moduleId,\r\n        body: {\r\n          postIntervationCheck: {\r\n            // Include inherited data from pre-intervention\r\n            initialDeformation: initialDeformation || 0,\r\n            materialProperties: materialProperties || null,\r\n            geometry: geometryProperties || null,\r\n            // Include composite data from props\r\n            compositeGeometry: compositeGeometry || null,\r\n            compositeProperties: compositeProperties || null,\r\n            // Include the current form data (only 3 fields now)\r\n            resultOfPostIntervationCheck: data,\r\n          },\r\n        } as any,\r\n      })\r\n      onSave?.()\r\n    },\r\n    [\r\n      mutate,\r\n      projectId,\r\n      moduleId,\r\n      onSave,\r\n      materialProperties,\r\n      geometryProperties,\r\n      compositeGeometry,\r\n      compositeProperties,\r\n      initialDeformation,\r\n    ],\r\n  )\r\n\r\n  // Calculate derived values when dependencies change\r\n  useEffect(() => {\r\n    // Set values from pre-intervention data first (if available)\r\n    if (preInterventionData) {\r\n      const preIntervention = preInterventionData\r\n\r\n      // Set values from pre-intervention data (only if not already saved)\r\n      if (\r\n        !defaultValues?.maximumBendingMoment &&\r\n        preIntervention.maximumBendingMoment !== undefined\r\n      ) {\r\n        form.setValue(\r\n          'maximumBendingMoment',\r\n          preIntervention.maximumBendingMoment,\r\n        )\r\n      }\r\n      if (\r\n        !defaultValues?.maximumShearForce &&\r\n        preIntervention.maximumShearForce !== undefined\r\n      ) {\r\n        form.setValue('maximumShearForce', preIntervention.maximumShearForce)\r\n      }\r\n      if (\r\n        !defaultValues?.permanentLoadPerLinearMeter &&\r\n        preIntervention.permanentLoadPerLinearMeter !== undefined\r\n      ) {\r\n        form.setValue(\r\n          'permanentLoadPerLinearMeter',\r\n          preIntervention.permanentLoadPerLinearMeter,\r\n        )\r\n      }\r\n      if (\r\n        !defaultValues?.imposedLoadPerLinearMeter &&\r\n        preIntervention.imposedLoadPerLinearMeter !== undefined\r\n      ) {\r\n        form.setValue(\r\n          'imposedLoadPerLinearMeter',\r\n          preIntervention.imposedLoadPerLinearMeter,\r\n        )\r\n      }\r\n      if (\r\n        !defaultValues?.combinationFactor &&\r\n        preIntervention.combinationFactor !== undefined\r\n      ) {\r\n        form.setValue('combinationFactor', preIntervention.combinationFactor)\r\n      }\r\n    }\r\n\r\n    // Calculate design_bending_stress if we have the required data\r\n    if (preInterventionData) {\r\n      const preIntervention = preInterventionData\r\n      const maximumBendingMoment =\r\n        form.getValues('maximumBendingMoment') ||\r\n        preIntervention.maximumBendingMoment ||\r\n        0\r\n\r\n      // Try to get sectionModulus from composite properties (preferred)\r\n      if (\r\n        compositeProperties?.sectionModulus &&\r\n        compositeProperties.sectionModulus > 0\r\n      ) {\r\n        const designBendingStress =\r\n          maximumBendingMoment / (compositeProperties.sectionModulus * 1000)\r\n        form.setValue('designBendingStress', designBendingStress)\r\n      }\r\n      // Fallback: try to get sectionModulus from geometry properties (from pre-intervention forms)\r\n      else if (\r\n        geometryProperties?.sectionModulus &&\r\n        geometryProperties.sectionModulus > 0\r\n      ) {\r\n        const designBendingStress =\r\n          maximumBendingMoment / (geometryProperties.sectionModulus * 1000)\r\n        form.setValue('designBendingStress', designBendingStress)\r\n      } else {\r\n        // Set to 0 if we can't calculate (composite properties not ready yet)\r\n        form.setValue('designBendingStress', 0)\r\n      }\r\n    }\r\n\r\n    // Calculate design_bending_strength = MIN(pre ma \"designBendingStrength\", frp_design_maximum_strain*frp_elasticity_modulus)\r\n    if (materialProperties && compositeProperties) {\r\n      // Get designBendingStrength from previous forms (1,2,3,4)\r\n      const preDesignBendingStrength =\r\n        geometryProperties.designBendingStrength || 0\r\n\r\n      // Get frp_design_maximum_strain and frp_elasticity_modulus from 5th section (Composite Properties)\r\n      const frpDesignMaximumStrain =\r\n        compositeProperties.frpDesignMaximumStrain || 0\r\n      const frpElasticityModulus = compositeProperties.frpElasticityModulus || 0\r\n\r\n      // Calculate the MIN of both values\r\n      const designBendingStrength = Math.min(\r\n        preDesignBendingStrength,\r\n        frpDesignMaximumStrain * frpElasticityModulus,\r\n      )\r\n\r\n      form.setValue('designBendingStrength', designBendingStrength)\r\n\r\n      // Calculate designBendingCheck = design_bending_stress / design_bending_strength\r\n      const designBendingStress = form.getValues('designBendingStress') || 0\r\n      const designBendingCheck =\r\n        designBendingStrength > 0\r\n          ? designBendingStress / designBendingStrength\r\n          : 0\r\n      form.setValue('designBendingCheck', designBendingCheck)\r\n    } else {\r\n      // Set to 0 if we don't have the required data\r\n      form.setValue('designBendingStrength', 0)\r\n      form.setValue('designBendingCheck', 0)\r\n    }\r\n\r\n    // Calculate shear stress and strength\r\n    if (geometryProperties) {\r\n      const maximumShearForce =\r\n        form.getValues('maximumShearForce') ||\r\n        preInterventionData?.maximumShearForce ||\r\n        0\r\n\r\n      // Calculate design_shear_stress = 3*1000*maximum_shear_force/(2*1000*beamSectionHeight*1000*beamSectionWidth)\r\n      const beamSectionHeight = geometryProperties.beamSectionHeight || 0\r\n      const beamSectionWidth = geometryProperties.beamSectionWidth || 0\r\n\r\n      if (beamSectionHeight > 0 && beamSectionWidth > 0) {\r\n        const designShearStress =\r\n          (3 * 1000 * maximumShearForce) /\r\n          (2 * 1000 * beamSectionHeight * 1000 * beamSectionWidth)\r\n        form.setValue('designShearStress', designShearStress)\r\n\r\n        // design_shear_strength = Pre ma \"designShearStrength\"\r\n        const designShearStrength = geometryProperties.designShearStrength || 0\r\n        form.setValue('designShearStrength', designShearStrength)\r\n\r\n        // Calculate designShearCheck = design_shear_stress / design_shear_strength\r\n        const designShearCheck =\r\n          designShearStrength > 0 ? designShearStress / designShearStrength : 0\r\n        form.setValue('designShearCheck', designShearCheck)\r\n      } else {\r\n        form.setValue('designShearStress', 0)\r\n        form.setValue('designShearStrength', 0)\r\n        form.setValue('designShearCheck', 0)\r\n      }\r\n    } else {\r\n      form.setValue('designShearStress', 0)\r\n      form.setValue('designShearStrength', 0)\r\n      form.setValue('designShearCheck', 0)\r\n    }\r\n\r\n    // Calculate deflection values\r\n    if (geometryProperties && compositeProperties) {\r\n      const permanentLoadPerLinearMeter =\r\n        form.getValues('permanentLoadPerLinearMeter') ||\r\n        preInterventionData?.permanentLoadPerLinearMeter ||\r\n        0\r\n      const beamSpan = geometryProperties.beamSpan || 0\r\n      const elasticityInstantaneousModulus =\r\n        geometryProperties.elasticityInstantaneousModulus || 0\r\n\r\n      // Get moment of inertia from composite properties\r\n      const momentOfInertiaAboutY =\r\n        compositeProperties.momentOfInertiaAboutY || 0\r\n\r\n      if (\r\n        beamSpan > 0 &&\r\n        elasticityInstantaneousModulus > 0 &&\r\n        momentOfInertiaAboutY > 0\r\n      ) {\r\n        // instantaneous_deflection_permanent_load = 100*(5*permanent_load_per_linear_meter*beamSpan^4)/(384*1000*designShearStrength*moment_of_inertia_about_y)\r\n        const instantaneousDeflectionPermanentLoad =\r\n          100 *\r\n          ((5 * permanentLoadPerLinearMeter * beamSpan ** 4) /\r\n            (384 *\r\n              1000 *\r\n              elasticityInstantaneousModulus *\r\n              momentOfInertiaAboutY))\r\n        form.setValue(\r\n          'instantaneousDeflectionPermanentLoad',\r\n          instantaneousDeflectionPermanentLoad,\r\n        )\r\n\r\n        // instantaneous_deflection_imposed_load = 100*(5*imposed_load_per_linear_meter*beamSpan^4)/(384*1000*designShearStrength*moment_of_inertia_about_y)\r\n        const imposedLoadPerLinearMeter =\r\n          form.getValues('imposedLoadPerLinearMeter') ||\r\n          preInterventionData?.imposedLoadPerLinearMeter ||\r\n          0\r\n        const instantaneousDeflectionImposedLoad =\r\n          (100 * (5 * imposedLoadPerLinearMeter * beamSpan ** 4)) /\r\n          (384 * 1000 * elasticityInstantaneousModulus * momentOfInertiaAboutY)\r\n        form.setValue(\r\n          'instantaneousDeflectionImposedLoad',\r\n          instantaneousDeflectionImposedLoad,\r\n        )\r\n\r\n        // instantaneous_deflection_total_loads = instantaneous_deflection_permanent_load + instantaneous_deflection_imposed_load + initialDeformation\r\n        const finalInitialDeformation = initialDeformation || 0\r\n        const instantaneousDeflectionTotalLoads =\r\n          instantaneousDeflectionPermanentLoad +\r\n          instantaneousDeflectionImposedLoad +\r\n          finalInitialDeformation\r\n        form.setValue(\r\n          'instantaneousDeflectionTotalLoads',\r\n          instantaneousDeflectionTotalLoads,\r\n        )\r\n\r\n        // instantaneousDeflectionCheck = 100*beamSpan / instantaneous_deflection_total_loads (< 300 = \"Non-satisfy\", >= 300 = \"Satisfy\")\r\n        const instantaneousDeflectionCheck =\r\n          instantaneousDeflectionTotalLoads > 0\r\n            ? (100 * beamSpan) / instantaneousDeflectionTotalLoads\r\n            : 0\r\n        form.setValue(\r\n          'instantaneousDeflectionCheck',\r\n          instantaneousDeflectionCheck,\r\n        )\r\n\r\n        // final_deflection_total_loads = instantaneous_deflection_permanent_load*(1+deformabilityFactor)+instantaneous_deflection_imposed_load*(1+combination_factor*deformabilityFactor)+initialDeformation\r\n        const deformabilityFactor = geometryProperties.deformabilityFactor || 0\r\n        const combinationFactor =\r\n          form.getValues('combinationFactor') ||\r\n          preInterventionData?.combinationFactor ||\r\n          0\r\n        const finalDeflectionTotalLoads =\r\n          instantaneousDeflectionPermanentLoad * (1 + deformabilityFactor) +\r\n          instantaneousDeflectionImposedLoad *\r\n            (1 + combinationFactor * deformabilityFactor) +\r\n          finalInitialDeformation\r\n        form.setValue('finalDeflectionTotalLoads', finalDeflectionTotalLoads)\r\n\r\n        // finalCheckResult = 100*beamSpan/final_deflection_total_loads (>= 200 = \"Satisfy\", < 200 = \"Non-Satisfy\")\r\n        const finalCheckResult =\r\n          finalDeflectionTotalLoads > 0\r\n            ? (100 * beamSpan) / finalDeflectionTotalLoads\r\n            : 0\r\n        form.setValue('finalCheckResult', finalCheckResult)\r\n      } else {\r\n        form.setValue('instantaneousDeflectionPermanentLoad', 0)\r\n        form.setValue('instantaneousDeflectionImposedLoad', 0)\r\n        form.setValue('instantaneousDeflectionTotalLoads', 0)\r\n        form.setValue('instantaneousDeflectionCheck', 0)\r\n        form.setValue('finalDeflectionTotalLoads', 0)\r\n        form.setValue('finalCheckResult', 0)\r\n      }\r\n    } else {\r\n      form.setValue('instantaneousDeflectionPermanentLoad', 0)\r\n      form.setValue('instantaneousDeflectionImposedLoad', 0)\r\n      form.setValue('instantaneousDeflectionTotalLoads', 0)\r\n      form.setValue('instantaneousDeflectionCheck', 0)\r\n      form.setValue('finalDeflectionTotalLoads', 0)\r\n      form.setValue('finalCheckResult', 0)\r\n    }\r\n  }, [\r\n    preInterventionData,\r\n    compositeProperties,\r\n    geometryProperties,\r\n    materialProperties,\r\n    defaultValues,\r\n    form,\r\n    initialDeformation,\r\n  ])\r\n\r\n  return (\r\n    <div className=\"flex flex-col 2xl:flex-row justify-center gap-2\">\r\n      <Form {...form}>\r\n        <form\r\n          className=\"space-y-4 rounded-md border p-4 grow\"\r\n          onSubmit={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {/* 1. maximum_bending_moment = pre ma \"maximumBendingMoment\" */}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"maximumBendingMoment\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          {/* 2. maximum_shear_force = pre ma \"maximumShearForce\" */}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"maximumShearForce\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          {/* 3. design_bending_stress = maximum_bending_moment/(section_modulus*1000) */}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"designBendingStress\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          {/* 4. design_bending_strength = MIN(pre ma \"designBendingStrength\", frp_design_maximum_strain*frp_elasticity_modulus) */}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"designBendingStrength\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          {/* 5. designBendingCheck = design_bending_stress / design_bending_strength <= 1 \"Non-satisfy\" else \"satisfy\" */}\r\n          <div className=\"flex items-start gap-4\">\r\n            <NumberFormInput\r\n              control={form.control}\r\n              name=\"designBendingCheck\"\r\n              t={t}\r\n              disabled={true}\r\n              fieldContainerClassName=\"flex-1\"\r\n              decimalPlaces={3}\r\n            />\r\n            <Badge\r\n              variant={getBadgeVariant(\r\n                getCheckResult(form.watch('designBendingCheck'), 1),\r\n              )}\r\n              className=\"mt-7\"\r\n            >\r\n              {getCheckResult(form.watch('designBendingCheck'), 1)}\r\n            </Badge>\r\n          </div>\r\n\r\n          {/* 6. design_shear_stress = 3*1000*maximum_shear_force/(2*1000*beamSectionHeight*1000*beamSectionWidth) */}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"designShearStress\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          {/* 7. design_shear_strength = Pre ma \"designShearStrength\" */}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"designShearStrength\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          {/* 8. designShearCheck = design_shear_stress / design_shear_strength <= 1 \"satisfy\" else \"Non-satisfy\" */}\r\n          <div className=\"flex items-start gap-4\">\r\n            <NumberFormInput\r\n              control={form.control}\r\n              name=\"designShearCheck\"\r\n              t={t}\r\n              disabled={true}\r\n              fieldContainerClassName=\"flex-1\"\r\n              decimalPlaces={3}\r\n            />\r\n            <Badge\r\n              variant={getBadgeVariant(\r\n                getCheckResult(form.watch('designShearCheck'), 1),\r\n              )}\r\n              className=\"mt-7\"\r\n            >\r\n              {getCheckResult(form.watch('designShearCheck'), 1)}\r\n            </Badge>\r\n          </div>\r\n\r\n          {/* 9. permanent_load_per_linear_meter = Pre ma \"permanentLoadPerLinearMeter\" */}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"permanentLoadPerLinearMeter\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          {/* 10. imposed_load_per_linear_meter = pre ma \"imposedLoadPerLinearMeter\" */}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"imposedLoadPerLinearMeter\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          {/* 11. instantaneous_deflection_permanent_load = 100*(5*permanent_load_per_linear_meter*beamSpan^4)/(384*1000*designShearStrength*moment_of_inertia_about_y) */}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"instantaneousDeflectionPermanentLoad\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          {/* 12. instantaneous_deflection_imposed_load = 100*(5*imposed_load_per_linear_meter*beamSpan^4)/(384*1000*designShearStrength*moment_of_inertia_about_y) */}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"instantaneousDeflectionImposedLoad\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          {/* 13. instantaneous_deflection_total_loads = instantaneous_deflection_permanent_load + instantaneous_deflection_imposed_load + initialDeformation */}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"instantaneousDeflectionTotalLoads\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          {/* 14. instantaneousDeflectionCheck = 100*beamSpan / instantaneous_deflection_total_loads (< 300 = \"Non-satisfy\", >= 300 = \"Satisfy\") */}\r\n          <div className=\"flex items-start gap-4\">\r\n            <NumberFormInput\r\n              control={form.control}\r\n              name=\"instantaneousDeflectionCheck\"\r\n              t={t}\r\n              disabled={true}\r\n              fieldContainerClassName=\"flex-1\"\r\n              decimalPlaces={3}\r\n            />\r\n            <Badge\r\n              variant={getBadgeVariant(\r\n                getCheckResult(\r\n                  form.watch('instantaneousDeflectionCheck'),\r\n                  300,\r\n                  'greaterThanOrEqual',\r\n                ),\r\n              )}\r\n              className=\"mt-7\"\r\n            >\r\n              {getCheckResult(\r\n                form.watch('instantaneousDeflectionCheck'),\r\n                300,\r\n                'greaterThanOrEqual',\r\n              )}\r\n            </Badge>\r\n          </div>\r\n\r\n          {/* 15. combination_factor = pre ma \"combinationFactor\" */}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"combinationFactor\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          {/* 16. final_deflection_total_loads = instantaneous_deflection_permanent_load*(1+deformabilityFactor)+instantaneous_deflection_imposed_load*(1+combination_factor*deformabilityFactor)+initialDeformation */}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"finalDeflectionTotalLoads\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          {/* 17. finalCheckResult = 100*beamSpan/final_deflection_total_loads (>= 200 = \"Satisfy\", < 200 = \"Non-Satisfy\") */}\r\n          <div className=\"flex items-start gap-4\">\r\n            <NumberFormInput\r\n              control={form.control}\r\n              name=\"finalCheckResult\"\r\n              t={t}\r\n              disabled={true}\r\n              fieldContainerClassName=\"flex-1\"\r\n              decimalPlaces={3}\r\n            />\r\n            <Badge\r\n              variant={getBadgeVariant(\r\n                getCheckResult(\r\n                  form.watch('finalCheckResult'),\r\n                  200,\r\n                  'greaterThanOrEqual',\r\n                ),\r\n              )}\r\n              className=\"mt-7\"\r\n            >\r\n              {getCheckResult(\r\n                form.watch('finalCheckResult'),\r\n                200,\r\n                'greaterThanOrEqual',\r\n              )}\r\n            </Badge>\r\n          </div>\r\n\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full sm:w-auto\"\r\n            disabled={isPending}\r\n          >\r\n            {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n            {tCommon('save')}\r\n          </Button>\r\n        </form>\r\n      </Form>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAGA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;AAEA,MAAM,aAAa,qLAAC,CAAC,MAAM,CAAC;IAC1B,sBAAsB,qLAAC,CACpB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,mBAAmB,qLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACrC,qBAAqB,qLAAC,CACnB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,uBAAuB,qLAAC,CACrB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,oBAAoB,qLAAC,CAClB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,mBAAmB,qLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACrC,qBAAqB,qLAAC,CACnB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,kBAAkB,qLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACpC,6BAA6B,qLAAC,CAC3B,MAAM,GACN,GAAG,CAAC,GAAG;IACV,2BAA2B,qLAAC,CACzB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,sCAAsC,qLAAC,CACpC,MAAM,GACN,GAAG,CAAC,GAAG;IACV,oCAAoC,qLAAC,CAClC,MAAM,GACN,GAAG,CAAC,GAAG;IACV,mCAAmC,qLAAC,CACjC,MAAM,GACN,GAAG,CAAC,GAAG;IACV,8BAA8B,qLAAC,CAC5B,MAAM,GACN,GAAG,CAAC,GAAG;IACV,mBAAmB,qLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACrC,2BAA2B,qLAAC,CACzB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,kBAAkB,qLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AACtC;AAkBO,MAAM,gCAAgC;QAAC,EAC5C,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,mBAAmB,EACnB,kBAAkB,EAClB,kBAAkB,EAClB,iBAAiB,EACjB,mBAAmB,EACnB,MAAM,EACN,kBAAkB,EACZ;;IACN,MAAM,IAAI,IAAA,4NAAe,EACvB;IAEF,MAAM,UAAU,IAAA,4NAAe,EAAC;IAEhC,MAAM,OAAO,IAAA,4KAAO,EAAa;QAC/B,UAAU,IAAA,gLAAW,EAAC;QACtB,eAAe;YACb,sBACE,CAAA,0BAAA,oCAAA,cAAe,oBAAoB,MACnC,gCAAA,0CAAA,oBAAqB,oBAAoB,KACzC;YACF,mBACE,CAAA,0BAAA,oCAAA,cAAe,iBAAiB,MAChC,gCAAA,0CAAA,oBAAqB,iBAAiB,KACtC;YACF,qBAAqB,CAAA,0BAAA,oCAAA,cAAe,mBAAmB,KAAI;YAC3D,uBAAuB,CAAA,0BAAA,oCAAA,cAAe,qBAAqB,KAAI;YAC/D,oBAAoB,CAAA,0BAAA,oCAAA,cAAe,kBAAkB,KAAI;YACzD,mBAAmB,CAAA,0BAAA,oCAAA,cAAe,iBAAiB,KAAI;YACvD,qBAAqB,CAAA,0BAAA,oCAAA,cAAe,mBAAmB,KAAI;YAC3D,kBAAkB,CAAA,0BAAA,oCAAA,cAAe,gBAAgB,KAAI;YACrD,6BACE,CAAA,0BAAA,oCAAA,cAAe,2BAA2B,MAC1C,gCAAA,0CAAA,oBAAqB,2BAA2B,KAChD;YACF,2BACE,CAAA,0BAAA,oCAAA,cAAe,yBAAyB,MACxC,gCAAA,0CAAA,oBAAqB,yBAAyB,KAC9C;YACF,sCACE,CAAA,0BAAA,oCAAA,cAAe,oCAAoC,KAAI;YACzD,oCACE,CAAA,0BAAA,oCAAA,cAAe,kCAAkC,KAAI;YACvD,mCACE,CAAA,0BAAA,oCAAA,cAAe,iCAAiC,KAAI;YACtD,8BACE,CAAA,0BAAA,oCAAA,cAAe,4BAA4B,KAAI;YACjD,mBACE,CAAA,0BAAA,oCAAA,cAAe,iBAAiB,MAChC,gCAAA,0CAAA,oBAAqB,iBAAiB,KACtC;YACF,2BAA2B,CAAA,0BAAA,oCAAA,cAAe,yBAAyB,KAAI;YACvE,kBAAkB,CAAA,0BAAA,oCAAA,cAAe,gBAAgB,KAAI;QACvD;IACF;IAEA,sEAAsE;IACtE,IAAA,0KAAS;mDAAC;YACR,IACE,iBACA,OAAO,kBAAkB,YACzB,OAAO,IAAI,CAAC,eAAe,MAAM,GAAG,GACpC;oBAII,qCAAA,MAIA,kCAAA,OAGmB,oCACE,sCACH,mCACD,kCACE,oCACH,iCAEhB,4CAAA,OAIA,0CAAA,OAIA,qDAEA,mDAEA,kDAEA,6CAEA,kCAAA,OAGyB,0CACT;gBArCpB,qDAAqD;gBACrD,MAAM,WAAW;oBACf,sBACE,CAAA,OAAA,CAAA,sCAAA,cAAc,oBAAoB,cAAlC,iDAAA,sCACA,gCAAA,0CAAA,oBAAqB,oBAAoB,cADzC,kBAAA,OAEA;oBACF,mBACE,CAAA,QAAA,CAAA,mCAAA,cAAc,iBAAiB,cAA/B,8CAAA,mCACA,gCAAA,0CAAA,oBAAqB,iBAAiB,cADtC,mBAAA,QAEA;oBACF,qBAAqB,CAAA,qCAAA,cAAc,mBAAmB,cAAjC,gDAAA,qCAAqC;oBAC1D,uBAAuB,CAAA,uCAAA,cAAc,qBAAqB,cAAnC,kDAAA,uCAAuC;oBAC9D,oBAAoB,CAAA,oCAAA,cAAc,kBAAkB,cAAhC,+CAAA,oCAAoC;oBACxD,mBAAmB,CAAA,mCAAA,cAAc,iBAAiB,cAA/B,8CAAA,mCAAmC;oBACtD,qBAAqB,CAAA,qCAAA,cAAc,mBAAmB,cAAjC,gDAAA,qCAAqC;oBAC1D,kBAAkB,CAAA,kCAAA,cAAc,gBAAgB,cAA9B,6CAAA,kCAAkC;oBACpD,6BACE,CAAA,QAAA,CAAA,6CAAA,cAAc,2BAA2B,cAAzC,wDAAA,6CACA,gCAAA,0CAAA,oBAAqB,2BAA2B,cADhD,mBAAA,QAEA;oBACF,2BACE,CAAA,QAAA,CAAA,2CAAA,cAAc,yBAAyB,cAAvC,sDAAA,2CACA,gCAAA,0CAAA,oBAAqB,yBAAyB,cAD9C,mBAAA,QAEA;oBACF,sCACE,CAAA,sDAAA,cAAc,oCAAoC,cAAlD,iEAAA,sDAAsD;oBACxD,oCACE,CAAA,oDAAA,cAAc,kCAAkC,cAAhD,+DAAA,oDAAoD;oBACtD,mCACE,CAAA,mDAAA,cAAc,iCAAiC,cAA/C,8DAAA,mDAAmD;oBACrD,8BACE,CAAA,8CAAA,cAAc,4BAA4B,cAA1C,yDAAA,8CAA8C;oBAChD,mBACE,CAAA,QAAA,CAAA,mCAAA,cAAc,iBAAiB,cAA/B,8CAAA,mCACA,gCAAA,0CAAA,oBAAqB,iBAAiB,cADtC,mBAAA,QAEA;oBACF,2BAA2B,CAAA,2CAAA,cAAc,yBAAyB,cAAvC,sDAAA,2CAA2C;oBACtE,kBAAkB,CAAA,kCAAA,cAAc,gBAAgB,cAA9B,6CAAA,kCAAkC;gBACtD;gBAEA,KAAK,KAAK,CAAC;YACb,OAAO,CACP;QACF;kDAAG;QAAC;QAAe;QAAqB;KAAK;IAE7C,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,mNAA2B,EAAC,QAAQ,WAAW;IAE7E,4CAA4C;IAC5C,MAAM,iBAAiB,SACrB,OACA;YACA,6EAAsD;QAEtD,IAAI,cAAc,mBAAmB;YACnC,sFAAsF;YACtF,OAAO,SAAS,YAAY,YAAY;QAC1C;QACA,+EAA+E;QAC/E,OAAO,SAAS,YAAY,YAAY;IAC1C;IAEA,uCAAuC;IACvC,MAAM,kBAAkB,CAAC;QACvB,OAAO,WAAW,YAAY,YAAY;IAC5C;IAEA,MAAM,mBAAmB,IAAA,4KAAW;uEAClC,CAAC;YACC,yEAAyE;YACzE,OAAO;gBACL;gBACA;gBACA,MAAM;oBACJ,sBAAsB;wBACpB,+CAA+C;wBAC/C,oBAAoB,sBAAsB;wBAC1C,oBAAoB,sBAAsB;wBAC1C,UAAU,sBAAsB;wBAChC,oCAAoC;wBACpC,mBAAmB,qBAAqB;wBACxC,qBAAqB,uBAAuB;wBAC5C,oDAAoD;wBACpD,8BAA8B;oBAChC;gBACF;YACF;YACA,mBAAA,6BAAA;QACF;sEACA;QACE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAGH,oDAAoD;IACpD,IAAA,0KAAS;mDAAC;YACR,6DAA6D;YAC7D,IAAI,qBAAqB;gBACvB,MAAM,kBAAkB;gBAExB,oEAAoE;gBACpE,IACE,EAAC,0BAAA,oCAAA,cAAe,oBAAoB,KACpC,gBAAgB,oBAAoB,KAAK,WACzC;oBACA,KAAK,QAAQ,CACX,wBACA,gBAAgB,oBAAoB;gBAExC;gBACA,IACE,EAAC,0BAAA,oCAAA,cAAe,iBAAiB,KACjC,gBAAgB,iBAAiB,KAAK,WACtC;oBACA,KAAK,QAAQ,CAAC,qBAAqB,gBAAgB,iBAAiB;gBACtE;gBACA,IACE,EAAC,0BAAA,oCAAA,cAAe,2BAA2B,KAC3C,gBAAgB,2BAA2B,KAAK,WAChD;oBACA,KAAK,QAAQ,CACX,+BACA,gBAAgB,2BAA2B;gBAE/C;gBACA,IACE,EAAC,0BAAA,oCAAA,cAAe,yBAAyB,KACzC,gBAAgB,yBAAyB,KAAK,WAC9C;oBACA,KAAK,QAAQ,CACX,6BACA,gBAAgB,yBAAyB;gBAE7C;gBACA,IACE,EAAC,0BAAA,oCAAA,cAAe,iBAAiB,KACjC,gBAAgB,iBAAiB,KAAK,WACtC;oBACA,KAAK,QAAQ,CAAC,qBAAqB,gBAAgB,iBAAiB;gBACtE;YACF;YAEA,+DAA+D;YAC/D,IAAI,qBAAqB;gBACvB,MAAM,kBAAkB;gBACxB,MAAM,uBACJ,KAAK,SAAS,CAAC,2BACf,gBAAgB,oBAAoB,IACpC;gBAEF,kEAAkE;gBAClE,IACE,CAAA,gCAAA,0CAAA,oBAAqB,cAAc,KACnC,oBAAoB,cAAc,GAAG,GACrC;oBACA,MAAM,sBACJ,uBAAuB,CAAC,oBAAoB,cAAc,GAAG,IAAI;oBACnE,KAAK,QAAQ,CAAC,uBAAuB;gBACvC,OAEK,IACH,CAAA,+BAAA,yCAAA,mBAAoB,cAAc,KAClC,mBAAmB,cAAc,GAAG,GACpC;oBACA,MAAM,sBACJ,uBAAuB,CAAC,mBAAmB,cAAc,GAAG,IAAI;oBAClE,KAAK,QAAQ,CAAC,uBAAuB;gBACvC,OAAO;oBACL,sEAAsE;oBACtE,KAAK,QAAQ,CAAC,uBAAuB;gBACvC;YACF;YAEA,4HAA4H;YAC5H,IAAI,sBAAsB,qBAAqB;gBAC7C,0DAA0D;gBAC1D,MAAM,2BACJ,mBAAmB,qBAAqB,IAAI;gBAE9C,mGAAmG;gBACnG,MAAM,yBACJ,oBAAoB,sBAAsB,IAAI;gBAChD,MAAM,uBAAuB,oBAAoB,oBAAoB,IAAI;gBAEzE,mCAAmC;gBACnC,MAAM,wBAAwB,KAAK,GAAG,CACpC,0BACA,yBAAyB;gBAG3B,KAAK,QAAQ,CAAC,yBAAyB;gBAEvC,iFAAiF;gBACjF,MAAM,sBAAsB,KAAK,SAAS,CAAC,0BAA0B;gBACrE,MAAM,qBACJ,wBAAwB,IACpB,sBAAsB,wBACtB;gBACN,KAAK,QAAQ,CAAC,sBAAsB;YACtC,OAAO;gBACL,8CAA8C;gBAC9C,KAAK,QAAQ,CAAC,yBAAyB;gBACvC,KAAK,QAAQ,CAAC,sBAAsB;YACtC;YAEA,sCAAsC;YACtC,IAAI,oBAAoB;gBACtB,MAAM,oBACJ,KAAK,SAAS,CAAC,yBACf,gCAAA,0CAAA,oBAAqB,iBAAiB,KACtC;gBAEF,8GAA8G;gBAC9G,MAAM,oBAAoB,mBAAmB,iBAAiB,IAAI;gBAClE,MAAM,mBAAmB,mBAAmB,gBAAgB,IAAI;gBAEhE,IAAI,oBAAoB,KAAK,mBAAmB,GAAG;oBACjD,MAAM,oBACJ,AAAC,IAAI,OAAO,oBACZ,CAAC,IAAI,OAAO,oBAAoB,OAAO,gBAAgB;oBACzD,KAAK,QAAQ,CAAC,qBAAqB;oBAEnC,uDAAuD;oBACvD,MAAM,sBAAsB,mBAAmB,mBAAmB,IAAI;oBACtE,KAAK,QAAQ,CAAC,uBAAuB;oBAErC,2EAA2E;oBAC3E,MAAM,mBACJ,sBAAsB,IAAI,oBAAoB,sBAAsB;oBACtE,KAAK,QAAQ,CAAC,oBAAoB;gBACpC,OAAO;oBACL,KAAK,QAAQ,CAAC,qBAAqB;oBACnC,KAAK,QAAQ,CAAC,uBAAuB;oBACrC,KAAK,QAAQ,CAAC,oBAAoB;gBACpC;YACF,OAAO;gBACL,KAAK,QAAQ,CAAC,qBAAqB;gBACnC,KAAK,QAAQ,CAAC,uBAAuB;gBACrC,KAAK,QAAQ,CAAC,oBAAoB;YACpC;YAEA,8BAA8B;YAC9B,IAAI,sBAAsB,qBAAqB;gBAC7C,MAAM,8BACJ,KAAK,SAAS,CAAC,mCACf,gCAAA,0CAAA,oBAAqB,2BAA2B,KAChD;gBACF,MAAM,WAAW,mBAAmB,QAAQ,IAAI;gBAChD,MAAM,iCACJ,mBAAmB,8BAA8B,IAAI;gBAEvD,kDAAkD;gBAClD,MAAM,wBACJ,oBAAoB,qBAAqB,IAAI;gBAE/C,IACE,WAAW,KACX,iCAAiC,KACjC,wBAAwB,GACxB;oBACA,wJAAwJ;oBACxJ,MAAM,uCACJ,MACA,CAAC,AAAC,IAAI,8BAA8B,YAAY,IAC9C,CAAC,MACC,OACA,iCACA,qBAAqB,CAAC;oBAC5B,KAAK,QAAQ,CACX,wCACA;oBAGF,oJAAoJ;oBACpJ,MAAM,4BACJ,KAAK,SAAS,CAAC,iCACf,gCAAA,0CAAA,oBAAqB,yBAAyB,KAC9C;oBACF,MAAM,qCACJ,AAAC,MAAM,CAAC,IAAI,4BAA4B,YAAY,CAAC,IACrD,CAAC,MAAM,OAAO,iCAAiC,qBAAqB;oBACtE,KAAK,QAAQ,CACX,sCACA;oBAGF,8IAA8I;oBAC9I,MAAM,0BAA0B,sBAAsB;oBACtD,MAAM,oCACJ,uCACA,qCACA;oBACF,KAAK,QAAQ,CACX,qCACA;oBAGF,iIAAiI;oBACjI,MAAM,+BACJ,oCAAoC,IAChC,AAAC,MAAM,WAAY,oCACnB;oBACN,KAAK,QAAQ,CACX,gCACA;oBAGF,qMAAqM;oBACrM,MAAM,sBAAsB,mBAAmB,mBAAmB,IAAI;oBACtE,MAAM,oBACJ,KAAK,SAAS,CAAC,yBACf,gCAAA,0CAAA,oBAAqB,iBAAiB,KACtC;oBACF,MAAM,4BACJ,uCAAuC,CAAC,IAAI,mBAAmB,IAC/D,qCACE,CAAC,IAAI,oBAAoB,mBAAmB,IAC9C;oBACF,KAAK,QAAQ,CAAC,6BAA6B;oBAE3C,2GAA2G;oBAC3G,MAAM,mBACJ,4BAA4B,IACxB,AAAC,MAAM,WAAY,4BACnB;oBACN,KAAK,QAAQ,CAAC,oBAAoB;gBACpC,OAAO;oBACL,KAAK,QAAQ,CAAC,wCAAwC;oBACtD,KAAK,QAAQ,CAAC,sCAAsC;oBACpD,KAAK,QAAQ,CAAC,qCAAqC;oBACnD,KAAK,QAAQ,CAAC,gCAAgC;oBAC9C,KAAK,QAAQ,CAAC,6BAA6B;oBAC3C,KAAK,QAAQ,CAAC,oBAAoB;gBACpC;YACF,OAAO;gBACL,KAAK,QAAQ,CAAC,wCAAwC;gBACtD,KAAK,QAAQ,CAAC,sCAAsC;gBACpD,KAAK,QAAQ,CAAC,qCAAqC;gBACnD,KAAK,QAAQ,CAAC,gCAAgC;gBAC9C,KAAK,QAAQ,CAAC,6BAA6B;gBAC3C,KAAK,QAAQ,CAAC,oBAAoB;YACpC;QACF;kDAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,2IAAI;YAAE,GAAG,IAAI;sBACZ,cAAA,6LAAC;gBACC,WAAU;gBACV,UAAU,KAAK,YAAY,CAAC;;kCAG5B,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAIjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAIjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAIjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAIjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qLAAe;gCACd,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,GAAG;gCACH,UAAU;gCACV,yBAAwB;gCACxB,eAAe;;;;;;0CAEjB,6LAAC,6IAAK;gCACJ,SAAS,gBACP,eAAe,KAAK,KAAK,CAAC,uBAAuB;gCAEnD,WAAU;0CAET,eAAe,KAAK,KAAK,CAAC,uBAAuB;;;;;;;;;;;;kCAKtD,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAIjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAIjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qLAAe;gCACd,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,GAAG;gCACH,UAAU;gCACV,yBAAwB;gCACxB,eAAe;;;;;;0CAEjB,6LAAC,6IAAK;gCACJ,SAAS,gBACP,eAAe,KAAK,KAAK,CAAC,qBAAqB;gCAEjD,WAAU;0CAET,eAAe,KAAK,KAAK,CAAC,qBAAqB;;;;;;;;;;;;kCAKpD,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAIjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAIjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAIjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAIjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAIjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qLAAe;gCACd,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,GAAG;gCACH,UAAU;gCACV,yBAAwB;gCACxB,eAAe;;;;;;0CAEjB,6LAAC,6IAAK;gCACJ,SAAS,gBACP,eACE,KAAK,KAAK,CAAC,iCACX,KACA;gCAGJ,WAAU;0CAET,eACC,KAAK,KAAK,CAAC,iCACX,KACA;;;;;;;;;;;;kCAMN,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAIjB,6LAAC,qLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAIjB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qLAAe;gCACd,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,GAAG;gCACH,UAAU;gCACV,yBAAwB;gCACxB,eAAe;;;;;;0CAEjB,6LAAC,6IAAK;gCACJ,SAAS,gBACP,eACE,KAAK,KAAK,CAAC,qBACX,KACA;gCAGJ,WAAU;0CAET,eACC,KAAK,KAAK,CAAC,qBACX,KACA;;;;;;;;;;;;kCAKN,6LAAC,+IAAM;wBACL,MAAK;wBACL,WAAU;wBACV,UAAU;;4BAET,2BAAa,6LAAC,uOAAW;gCAAC,WAAU;;;;;;4BACpC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;AAMrB;GAlpBa;;QAaD,4NAAe;QAGT,4NAAe;QAElB,4KAAO;QA8FU,mNAA2B;;;KAhH9C", "debugId": null}}, {"offset": {"line": 3082, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx"], "sourcesContent": ["import { WoodBeamGeometryForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form'\r\nimport { WoodCompositeGeometryForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-geometry-form'\r\nimport { WoodCompositePropertiesForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form'\r\nimport { WoodGeneralForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-general-form'\r\nimport { WoodGeometryForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-geometry-form'\r\nimport { WoodPostInterventionCheckForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form'\r\n\r\nimport {\r\n  Accordion as AccordionComponent,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from '@atlas/components/ui/accordion'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport type {\r\n  Module,\r\n  ModuleWithParamsWood,\r\n} from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useState } from 'react'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  module: ModuleWithParamsWood\r\n}\r\n\r\nexport const WoodPostInterventionForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  module,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.wood')\r\n  const { params } = module\r\n  const [refreshTrigger, setRefreshTrigger] = useState(0)\r\n\r\n  const [currentCompositeGeometry, setCurrentCompositeGeometry] = useState<any>(\r\n    (params as any)?.postIntervationCheck?.compositeGeometry ||\r\n      (params as any)?.compositeGeometry ||\r\n      null,\r\n  )\r\n\r\n  const [currentCompositeProperties, setCurrentCompositeProperties] =\r\n    useState<any>(\r\n      (params as any)?.postIntervationCheck?.compositeProperties ||\r\n        (params as any)?.compositeProperties ||\r\n        null,\r\n    )\r\n\r\n  // Callback to handle composite geometry updates with change detection\r\n  const handleCompositeGeometryChange = useCallback((newGeometry: any) => {\r\n    // Only update if the geometry data has actually changed\r\n    setCurrentCompositeGeometry((prev: any) => {\r\n      if (\r\n        !prev ||\r\n        prev.productId !== newGeometry.productId ||\r\n        prev.stripWidth !== newGeometry.stripWidth ||\r\n        prev.layersNumber !== newGeometry.layersNumber ||\r\n        prev.equivalentThickness !== newGeometry.equivalentThickness ||\r\n        prev.expositionType !== newGeometry.expositionType ||\r\n        prev.environmentalConversionFactor !==\r\n          newGeometry.environmentalConversionFactor\r\n      ) {\r\n        return newGeometry\r\n      }\r\n      return prev\r\n    })\r\n  }, [])\r\n\r\n  // Callback to handle composite properties updates with change detection\r\n  const handleCompositePropertiesChange = useCallback((newProperties: any) => {\r\n    // Only update if the properties data has actually changed\r\n    setCurrentCompositeProperties((prev: any) => {\r\n      if (\r\n        !prev ||\r\n        prev.sectionModulus !== newProperties.sectionModulus ||\r\n        prev.momentOfInertiaAboutY !== newProperties.momentOfInertiaAboutY ||\r\n        prev.frpElasticityModulus !== newProperties.frpElasticityModulus ||\r\n        prev.frpDesignMaximumStrain !== newProperties.frpDesignMaximumStrain\r\n      ) {\r\n        return newProperties\r\n      }\r\n      return prev\r\n    })\r\n  }, [])\r\n\r\n  // Create a dummy onSave function for read-only forms\r\n  const dummyOnSave = () => {}\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <AccordionComponent type=\"single\" collapsible className=\"w-full\">\r\n        <AccordionItem value=\"post-intervention\">\r\n          <AccordionTrigger className=\"text-lg font-normal\">\r\n            {t('postForm.Post-Intervention Analysis')}\r\n          </AccordionTrigger>\r\n          <AccordionContent className=\"space-y-6 p-4\">\r\n            {/* Section 1: Initial Deformation (Read-only) */}\r\n            <div>\r\n              <h3 className=\"text-md font-semibold mb-4\">\r\n                {t('postForm.Initial Deformation - Read Only')}\r\n              </h3>\r\n              <div className=\"opacity-60 [&_button[type='submit']]:hidden [&_input]:pointer-events-none [&_input]:cursor-not-allowed [&_[data-slot='select-trigger']]:pointer-events-auto [&_[data-slot='select-trigger']]:opacity-100 [&_[data-slot='select-trigger']]:cursor-pointer\">\r\n                <WoodGeneralForm\r\n                  session={session}\r\n                  projectId={projectId}\r\n                  moduleId={moduleId}\r\n                  defaultValues={{\r\n                    initialDeformation: params?.initialDeformation || 0,\r\n                  }}\r\n                  onSave={dummyOnSave}\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <Separator />\r\n\r\n            {/* Section 2: Material Properties (Read-only) */}\r\n            <div>\r\n              <h3 className=\"text-md font-semibold mb-4\">\r\n                {t('postForm.Material Properties - Read Only')}\r\n              </h3>\r\n              <div className=\"opacity-60 [&_button[type='submit']]:hidden [&_input]:pointer-events-none [&_input]:cursor-not-allowed [&_[data-slot='select-trigger']]:pointer-events-auto [&_[data-slot='select-trigger']]:opacity-100 [&_[data-slot='select-trigger']]:cursor-pointer\">\r\n                <WoodGeometryForm\r\n                  session={session}\r\n                  projectId={projectId}\r\n                  moduleId={moduleId}\r\n                  defaultValues={params?.materialProperties as any}\r\n                  woodName={params?.materialProperties?.woodName}\r\n                  onSave={dummyOnSave}\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <Separator />\r\n\r\n            {/* Section 3: Geometry (Read-only) */}\r\n            <div>\r\n              <h3 className=\"text-md font-semibold mb-4\">\r\n                {t('postForm.Geometry - Read Only')}\r\n              </h3>\r\n              <div className=\"opacity-60 [&_button[type='submit']]:hidden [&_input]:pointer-events-none [&_input]:cursor-not-allowed [&_[data-slot='select-trigger']]:pointer-events-auto [&_[data-slot='select-trigger']]:opacity-100 [&_[data-slot='select-trigger']]:cursor-pointer\">\r\n                <WoodBeamGeometryForm\r\n                  session={session}\r\n                  projectId={projectId}\r\n                  moduleId={moduleId}\r\n                  defaultValues={params?.geometry as any}\r\n                  materialProperties={params?.materialProperties as any}\r\n                  onSave={dummyOnSave}\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <Separator />\r\n\r\n            {/* Section 4: Composite Geometry */}\r\n            <div>\r\n              <h3 className=\"text-md font-semibold mb-4\">\r\n                {t('compositeGeometry.title')}\r\n              </h3>\r\n              <WoodCompositeGeometryForm\r\n                session={session}\r\n                projectId={projectId}\r\n                moduleId={moduleId}\r\n                defaultValues={\r\n                  (params as any)?.postIntervationCheck?.compositeGeometry &&\r\n                  typeof (params as any).postIntervationCheck\r\n                    .compositeGeometry === 'object'\r\n                    ? (params as any).postIntervationCheck.compositeGeometry\r\n                    : (params as any)?.compositeGeometry &&\r\n                        typeof (params as any).compositeGeometry === 'object'\r\n                      ? (params as any).compositeGeometry\r\n                      : undefined\r\n                }\r\n                preInterventionData={params?.preIntervationCheck}\r\n                materialProperties={params?.materialProperties}\r\n                geometryProperties={params?.geometry}\r\n                onSave={() => setRefreshTrigger(prev => prev + 1)}\r\n                onGeometryChange={handleCompositeGeometryChange}\r\n                initialDeformation={params?.initialDeformation}\r\n              />\r\n            </div>\r\n\r\n            <Separator />\r\n\r\n            {/* Section 5: Composite Properties */}\r\n            <div>\r\n              <h3 className=\"text-md font-semibold mb-4\">\r\n                {t('compositeProperties.title')}\r\n              </h3>\r\n              <WoodCompositePropertiesForm\r\n                session={session}\r\n                projectId={projectId}\r\n                moduleId={moduleId}\r\n                defaultValues={\r\n                  (params as any)?.postIntervationCheck?.compositeProperties &&\r\n                  typeof (params as any).postIntervationCheck\r\n                    .compositeProperties === 'object'\r\n                    ? (params as any).postIntervationCheck.compositeProperties\r\n                    : (params as any)?.compositeProperties &&\r\n                        typeof (params as any).compositeProperties === 'object'\r\n                      ? (params as any).compositeProperties\r\n                      : undefined\r\n                }\r\n                compositeGeometry={currentCompositeGeometry}\r\n                preInterventionData={params?.preIntervationCheck}\r\n                materialProperties={params?.materialProperties}\r\n                geometryProperties={params?.geometry}\r\n                onSave={() => setRefreshTrigger(prev => prev + 1)}\r\n                onPropertiesChange={handleCompositePropertiesChange}\r\n                initialDeformation={params?.initialDeformation}\r\n              />\r\n            </div>\r\n\r\n            <Separator />\r\n\r\n            {/* Section 6: Post-Intervention Check Results */}\r\n            <div>\r\n              <h3 className=\"text-md font-semibold mb-4\">\r\n                {t('resultOfPostIntervationCheck.title')}\r\n              </h3>\r\n              <WoodPostInterventionCheckForm\r\n                key={`post-intervention-${refreshTrigger}-${currentCompositeProperties?.sectionModulus || 0}`}\r\n                session={session}\r\n                projectId={projectId}\r\n                moduleId={moduleId}\r\n                defaultValues={\r\n                  (params as any)?.postIntervationCheck\r\n                    ?.resultOfPostIntervationCheck &&\r\n                  typeof (params as any).postIntervationCheck\r\n                    .resultOfPostIntervationCheck === 'object'\r\n                    ? (params as any).postIntervationCheck\r\n                        .resultOfPostIntervationCheck\r\n                    : (params as any)?.resultOfPostIntervationCheck &&\r\n                        typeof (params as any).resultOfPostIntervationCheck ===\r\n                          'object'\r\n                      ? (params as any).resultOfPostIntervationCheck\r\n                      : undefined\r\n                }\r\n                preInterventionData={params?.preIntervationCheck}\r\n                materialProperties={params?.materialProperties}\r\n                geometryProperties={params?.geometry}\r\n                compositeGeometry={currentCompositeGeometry}\r\n                compositeProperties={currentCompositeProperties}\r\n                onSave={() => setRefreshTrigger(prev => prev + 1)}\r\n                initialDeformation={params?.initialDeformation}\r\n              />\r\n            </div>\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n      </AccordionComponent>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AAMA;AAOA;AACA;;;;;;;;;;;;;AASO,MAAM,2BAA2B;QAAC,EACvC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,MAAM,EACA;QAMJ,uBAAA,OACE,QAMA,wBAAA,QACE,QAmFoB,4BAqCV,wBAAA,QAII,QA0BJ,wBAAA,QAII,QA4BJ,wBAAA,QAMI;;IAzMpB,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,yKAAQ,EAAC;IAErD,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,IAAA,yKAAQ,EACtE,EAAA,QAAC,oBAAD,6BAAA,wBAAA,MAAiB,oBAAoB,cAArC,4CAAA,sBAAuC,iBAAiB,OACtD,SAAC,oBAAD,6BAAA,OAAiB,iBAAiB,KAClC;IAGJ,MAAM,CAAC,4BAA4B,8BAA8B,GAC/D,IAAA,yKAAQ,EACN,EAAA,SAAC,oBAAD,8BAAA,yBAAA,OAAiB,oBAAoB,cAArC,6CAAA,uBAAuC,mBAAmB,OACxD,SAAC,oBAAD,6BAAA,OAAiB,mBAAmB,KACpC;IAGN,sEAAsE;IACtE,MAAM,gCAAgC,IAAA,4KAAW;+EAAC,CAAC;YACjD,wDAAwD;YACxD;uFAA4B,CAAC;oBAC3B,IACE,CAAC,QACD,KAAK,SAAS,KAAK,YAAY,SAAS,IACxC,KAAK,UAAU,KAAK,YAAY,UAAU,IAC1C,KAAK,YAAY,KAAK,YAAY,YAAY,IAC9C,KAAK,mBAAmB,KAAK,YAAY,mBAAmB,IAC5D,KAAK,cAAc,KAAK,YAAY,cAAc,IAClD,KAAK,6BAA6B,KAChC,YAAY,6BAA6B,EAC3C;wBACA,OAAO;oBACT;oBACA,OAAO;gBACT;;QACF;8EAAG,EAAE;IAEL,wEAAwE;IACxE,MAAM,kCAAkC,IAAA,4KAAW;iFAAC,CAAC;YACnD,0DAA0D;YAC1D;yFAA8B,CAAC;oBAC7B,IACE,CAAC,QACD,KAAK,cAAc,KAAK,cAAc,cAAc,IACpD,KAAK,qBAAqB,KAAK,cAAc,qBAAqB,IAClE,KAAK,oBAAoB,KAAK,cAAc,oBAAoB,IAChE,KAAK,sBAAsB,KAAK,cAAc,sBAAsB,EACpE;wBACA,OAAO;oBACT;oBACA,OAAO;gBACT;;QACF;gFAAG,EAAE;IAEL,qDAAqD;IACrD,MAAM,cAAc,KAAO;IAE3B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,qJAAkB;YAAC,MAAK;YAAS,WAAW;YAAC,WAAU;sBACtD,cAAA,6LAAC,yJAAa;gBAAC,OAAM;;kCACnB,6LAAC,4JAAgB;wBAAC,WAAU;kCACzB,EAAE;;;;;;kCAEL,6LAAC,4JAAgB;wBAAC,WAAU;;0CAE1B,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAEL,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,iPAAe;4CACd,SAAS;4CACT,WAAW;4CACX,UAAU;4CACV,eAAe;gDACb,oBAAoB,CAAA,mBAAA,6BAAA,OAAQ,kBAAkB,KAAI;4CACpD;4CACA,QAAQ;;;;;;;;;;;;;;;;;0CAKd,6LAAC,qJAAS;;;;;0CAGV,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAEL,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,mPAAgB;4CACf,SAAS;4CACT,WAAW;4CACX,UAAU;4CACV,aAAa,EAAE,mBAAA,6BAAA,OAAQ,kBAAkB;4CACzC,QAAQ,EAAE,mBAAA,8BAAA,6BAAA,OAAQ,kBAAkB,cAA1B,iDAAA,2BAA4B,QAAQ;4CAC9C,QAAQ;;;;;;;;;;;;;;;;;0CAKd,6LAAC,qJAAS;;;;;0CAGV,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAEL,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,+PAAoB;4CACnB,SAAS;4CACT,WAAW;4CACX,UAAU;4CACV,aAAa,EAAE,mBAAA,6BAAA,OAAQ,QAAQ;4CAC/B,kBAAkB,EAAE,mBAAA,6BAAA,OAAQ,kBAAkB;4CAC9C,QAAQ;;;;;;;;;;;;;;;;;0CAKd,6LAAC,qJAAS;;;;;0CAGV,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAEL,6LAAC,yQAAyB;wCACxB,SAAS;wCACT,WAAW;wCACX,UAAU;wCACV,eACE,EAAA,SAAC,oBAAD,8BAAA,yBAAA,OAAiB,oBAAoB,cAArC,6CAAA,uBAAuC,iBAAiB,KACxD,OAAO,AAAC,OAAe,oBAAoB,CACxC,iBAAiB,KAAK,WACrB,AAAC,OAAe,oBAAoB,CAAC,iBAAiB,GACtD,EAAA,SAAC,oBAAD,6BAAA,OAAiB,iBAAiB,KAChC,OAAO,AAAC,OAAe,iBAAiB,KAAK,WAC7C,AAAC,OAAe,iBAAiB,GACjC;wCAER,mBAAmB,EAAE,mBAAA,6BAAA,OAAQ,mBAAmB;wCAChD,kBAAkB,EAAE,mBAAA,6BAAA,OAAQ,kBAAkB;wCAC9C,kBAAkB,EAAE,mBAAA,6BAAA,OAAQ,QAAQ;wCACpC,QAAQ,IAAM,kBAAkB,CAAA,OAAQ,OAAO;wCAC/C,kBAAkB;wCAClB,kBAAkB,EAAE,mBAAA,6BAAA,OAAQ,kBAAkB;;;;;;;;;;;;0CAIlD,6LAAC,qJAAS;;;;;0CAGV,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAEL,6LAAC,6QAA2B;wCAC1B,SAAS;wCACT,WAAW;wCACX,UAAU;wCACV,eACE,EAAA,SAAC,oBAAD,8BAAA,yBAAA,OAAiB,oBAAoB,cAArC,6CAAA,uBAAuC,mBAAmB,KAC1D,OAAO,AAAC,OAAe,oBAAoB,CACxC,mBAAmB,KAAK,WACvB,AAAC,OAAe,oBAAoB,CAAC,mBAAmB,GACxD,EAAA,SAAC,oBAAD,6BAAA,OAAiB,mBAAmB,KAClC,OAAO,AAAC,OAAe,mBAAmB,KAAK,WAC/C,AAAC,OAAe,mBAAmB,GACnC;wCAER,mBAAmB;wCACnB,mBAAmB,EAAE,mBAAA,6BAAA,OAAQ,mBAAmB;wCAChD,kBAAkB,EAAE,mBAAA,6BAAA,OAAQ,kBAAkB;wCAC9C,kBAAkB,EAAE,mBAAA,6BAAA,OAAQ,QAAQ;wCACpC,QAAQ,IAAM,kBAAkB,CAAA,OAAQ,OAAO;wCAC/C,oBAAoB;wCACpB,kBAAkB,EAAE,mBAAA,6BAAA,OAAQ,kBAAkB;;;;;;;;;;;;0CAIlD,6LAAC,qJAAS;;;;;0CAGV,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAEL,6LAAC,qRAA6B;wCAE5B,SAAS;wCACT,WAAW;wCACX,UAAU;wCACV,eACE,EAAA,SAAC,oBAAD,8BAAA,yBAAA,OAAiB,oBAAoB,cAArC,6CAAA,uBACI,4BAA4B,KAChC,OAAO,AAAC,OAAe,oBAAoB,CACxC,4BAA4B,KAAK,WAChC,AAAC,OAAe,oBAAoB,CACjC,4BAA4B,GAC/B,EAAA,SAAC,oBAAD,6BAAA,OAAiB,4BAA4B,KAC3C,OAAO,AAAC,OAAe,4BAA4B,KACjD,WACF,AAAC,OAAe,4BAA4B,GAC5C;wCAER,mBAAmB,EAAE,mBAAA,6BAAA,OAAQ,mBAAmB;wCAChD,kBAAkB,EAAE,mBAAA,6BAAA,OAAQ,kBAAkB;wCAC9C,kBAAkB,EAAE,mBAAA,6BAAA,OAAQ,QAAQ;wCACpC,mBAAmB;wCACnB,qBAAqB;wCACrB,QAAQ,IAAM,kBAAkB,CAAA,OAAQ,OAAO;wCAC/C,kBAAkB,EAAE,mBAAA,6BAAA,OAAQ,kBAAkB;uCAvBzC,AAAC,qBAAsC,OAAlB,gBAAe,KAAmD,OAAhD,CAAA,uCAAA,iDAAA,2BAA4B,cAAc,KAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+B1G;GAnOa;;QAMD,4NAAe;;;KANd", "debugId": null}}, {"offset": {"line": 3435, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx"], "sourcesContent": ["import { WoodBeamGeometryForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form'\r\nimport { WoodGeneralForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-general-form'\r\nimport { WoodGeometryForm as WoodMaterialPropertiesForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-geometry-form'\r\nimport { WoodPreInterventionCheckForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form'\r\nimport { WoodPostInterventionForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form'\r\n\r\nimport {\r\n  Accordion as AccordionComponent,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from '@atlas/components/ui/accordion'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport type {\r\n  Module,\r\n  ModuleWithParamsWood,\r\n} from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { woodParamsCheckSchema } from '@atlas/types/schemas/wood-form'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useMemo, useState } from 'react'\r\nimport { ModuleReportGenerationSection } from '../../module-report-generation-section'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  module: ModuleWithParamsWood\r\n}\r\n\r\nexport const WoodParamsForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  module,\r\n}: Props) => {\r\n  const { params } = module\r\n  const [openItems, setOpenItems] = useState(['0'])\r\n  const t = useTranslations('forms.project-params.wood')\r\n\r\n  const handleItemSaved = useCallback((id: string) => {\r\n    const nextId = String(Number(id) + 1)\r\n    setOpenItems(old => {\r\n      const temp = old.filter(v => v !== id)\r\n      return old.includes(nextId) ? temp : [...temp, nextId]\r\n    })\r\n  }, [])\r\n\r\n  const { success } = useMemo(\r\n    () => woodParamsCheckSchema.safeParse(params),\r\n    [params],\r\n  )\r\n\r\n  const { success: enableReport } = useMemo(\r\n      () => woodParamsCheckSchema.safeParse(module),\r\n      [module],\r\n    )\r\n\r\n  return (\r\n    <div>\r\n      <AccordionComponent\r\n        type=\"multiple\"\r\n        value={openItems}\r\n        onValueChange={setOpenItems}\r\n      >\r\n        <AccordionItem value=\"0\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">{t('general.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <WoodGeneralForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={{\r\n                initialDeformation: params?.initialDeformation,\r\n              }}\r\n              onSave={() => handleItemSaved('0')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"1\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('materialProperties.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <WoodMaterialPropertiesForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={\r\n                // Wood material properties are stored in materialProperties field\r\n                // Check if params.materialProperties has wood material properties structure\r\n                params?.materialProperties &&\r\n                typeof params.materialProperties === 'object' &&\r\n                'category' in params.materialProperties\r\n                  ? (params.materialProperties as any) // It's wood material properties\r\n                  : undefined // No wood material properties saved yet, start with empty form\r\n              }\r\n              onSave={() => handleItemSaved('1')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"2\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">{t('geometry.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <WoodBeamGeometryForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={\r\n                // Wood geometry properties are stored in geometry field\r\n                params?.geometry &&\r\n                typeof params.geometry === 'object' &&\r\n                'beamSectionWidth' in params.geometry\r\n                  ? (params.geometry as any) // It's wood geometry properties\r\n                  : undefined // No wood geometry properties saved yet, start with empty form\r\n              }\r\n              materialProperties={\r\n                // Pass material properties for calculations\r\n                params?.materialProperties &&\r\n                typeof params.materialProperties === 'object' &&\r\n                'category' in params.materialProperties\r\n                  ? (params.materialProperties as any) // Wood material properties\r\n                  : undefined // No material properties available yet\r\n              }\r\n              onSave={() => handleItemSaved('2')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"3\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('preIntervationCheck.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <WoodPreInterventionCheckForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              initialDeformation={params?.initialDeformation}\r\n              defaultValues={\r\n                // Wood pre-intervention check properties are stored in preIntervationCheck field\r\n                params?.preIntervationCheck &&\r\n                typeof params.preIntervationCheck === 'object'\r\n                  ? (params.preIntervationCheck as any) // It's wood pre-intervention check properties\r\n                  : undefined // No pre-intervention check properties saved yet, start with empty form\r\n              }\r\n              geometryProperties={\r\n                // Pass geometry properties for calculations\r\n                params?.geometry &&\r\n                typeof params.geometry === 'object' &&\r\n                'beamSectionWidth' in params.geometry\r\n                  ? (params.geometry as any) // Wood geometry properties\r\n                  : undefined // No geometry properties available yet\r\n              }\r\n              onSave={() => handleItemSaved('3')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n      </AccordionComponent>\r\n      {success && (\r\n        <>\r\n          <Separator />\r\n          <WoodPostInterventionForm\r\n            module={module}\r\n            session={session}\r\n            projectId={projectId}\r\n            moduleId={moduleId}\r\n          />\r\n          <div className=\"pt-4\">\r\n                      <ModuleReportGenerationSection\r\n                        moduleId={moduleId}\r\n                        enabled={enableReport}\r\n                        projectId={projectId}\r\n                        session={session}\r\n                      />\r\n                    </div>\r\n        </>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAEA;AAMA;AAMA;AAEA;AACA;AACA;;;;;;;;;;;;;;AASO,MAAM,iBAAiB;QAAC,EAC7B,OAAO,EACP,SAAS,EACT,QAAQ,EACR,MAAM,EACA;;IACN,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAC;QAAC;KAAI;IAChD,MAAM,IAAI,IAAA,4NAAe,EAAC;IAE1B,MAAM,kBAAkB,IAAA,4KAAW;uDAAC,CAAC;YACnC,MAAM,SAAS,OAAO,OAAO,MAAM;YACnC;+DAAa,CAAA;oBACX,MAAM,OAAO,IAAI,MAAM;4EAAC,CAAA,IAAK,MAAM;;oBACnC,OAAO,IAAI,QAAQ,CAAC,UAAU,OAAO;2BAAI;wBAAM;qBAAO;gBACxD;;QACF;sDAAG,EAAE;IAEL,MAAM,EAAE,OAAO,EAAE,GAAG,IAAA,wKAAO;kCACzB,IAAM,mKAAqB,CAAC,SAAS,CAAC;iCACtC;QAAC;KAAO;IAGV,MAAM,EAAE,SAAS,YAAY,EAAE,GAAG,IAAA,wKAAO;kCACrC,IAAM,mKAAqB,CAAC,SAAS,CAAC;iCACtC;QAAC;KAAO;IAGZ,qBACE,6LAAC;;0BACC,6LAAC,qJAAkB;gBACjB,MAAK;gBACL,OAAO;gBACP,eAAe;;kCAEf,6LAAC,yJAAa;wBAAC,OAAM;;0CACnB,6LAAC,4JAAgB;0CACf,cAAA,6LAAC;oCAAG,WAAU;8CAAuB,EAAE;;;;;;;;;;;0CAEzC,6LAAC,4JAAgB;0CACf,cAAA,6LAAC,iPAAe;oCACd,SAAS;oCACT,WAAW;oCACX,UAAU;oCACV,eAAe;wCACb,kBAAkB,EAAE,mBAAA,6BAAA,OAAQ,kBAAkB;oCAChD;oCACA,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;kCAIpC,6LAAC,yJAAa;wBAAC,OAAM;;0CACnB,6LAAC,4JAAgB;0CACf,cAAA,6LAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;;;;;;0CAGP,6LAAC,4JAAgB;0CACf,cAAA,6LAAC,mPAA0B;oCACzB,SAAS;oCACT,WAAW;oCACX,UAAU;oCACV,eACE,kEAAkE;oCAClE,4EAA4E;oCAC5E,CAAA,mBAAA,6BAAA,OAAQ,kBAAkB,KAC1B,OAAO,OAAO,kBAAkB,KAAK,YACrC,cAAc,OAAO,kBAAkB,GAClC,OAAO,kBAAkB,GAC1B,UAAU,+DAA+D;;oCAE/E,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;kCAIpC,6LAAC,yJAAa;wBAAC,OAAM;;0CACnB,6LAAC,4JAAgB;0CACf,cAAA,6LAAC;oCAAG,WAAU;8CAAuB,EAAE;;;;;;;;;;;0CAEzC,6LAAC,4JAAgB;0CACf,cAAA,6LAAC,+PAAoB;oCACnB,SAAS;oCACT,WAAW;oCACX,UAAU;oCACV,eACE,wDAAwD;oCACxD,CAAA,mBAAA,6BAAA,OAAQ,QAAQ,KAChB,OAAO,OAAO,QAAQ,KAAK,YAC3B,sBAAsB,OAAO,QAAQ,GAChC,OAAO,QAAQ,GAChB,UAAU,+DAA+D;;oCAE/E,oBACE,4CAA4C;oCAC5C,CAAA,mBAAA,6BAAA,OAAQ,kBAAkB,KAC1B,OAAO,OAAO,kBAAkB,KAAK,YACrC,cAAc,OAAO,kBAAkB,GAClC,OAAO,kBAAkB,GAC1B,UAAU,uCAAuC;;oCAEvD,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;kCAIpC,6LAAC,yJAAa;wBAAC,OAAM;;0CACnB,6LAAC,4JAAgB;0CACf,cAAA,6LAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;;;;;;0CAGP,6LAAC,4JAAgB;0CACf,cAAA,6LAAC,mRAA4B;oCAC3B,SAAS;oCACT,WAAW;oCACX,UAAU;oCACV,kBAAkB,EAAE,mBAAA,6BAAA,OAAQ,kBAAkB;oCAC9C,eACE,iFAAiF;oCACjF,CAAA,mBAAA,6BAAA,OAAQ,mBAAmB,KAC3B,OAAO,OAAO,mBAAmB,KAAK,WACjC,OAAO,mBAAmB,GAC3B,UAAU,wEAAwE;;oCAExF,oBACE,4CAA4C;oCAC5C,CAAA,mBAAA,6BAAA,OAAQ,QAAQ,KAChB,OAAO,OAAO,QAAQ,KAAK,YAC3B,sBAAsB,OAAO,QAAQ,GAChC,OAAO,QAAQ,GAChB,UAAU,uCAAuC;;oCAEvD,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;YAKrC,yBACC;;kCACE,6LAAC,qJAAS;;;;;kCACV,6LAAC,2PAAwB;wBACvB,QAAQ;wBACR,SAAS;wBACT,WAAW;wBACX,UAAU;;;;;;kCAEZ,6LAAC;wBAAI,WAAU;kCACH,cAAA,6LAAC,0OAA6B;4BAC5B,UAAU;4BACV,SAAS;4BACT,WAAW;4BACX,SAAS;;;;;;;;;;;;;;;;;;;AAOjC;GA7Ja;;QAQD,4NAAe;;;KARd", "debugId": null}}]}