{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/perimeter-and-widespread-intervention-calculation-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { PerimeterAndWidespreadInterventionCalculationResultSchemaInputs } from '@atlas/types/schemas/masonry/antioverturning-form'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  perimeterAndWidespreadInterventionCalculationResult: PerimeterAndWidespreadInterventionCalculationResultSchemaInputs\r\n}\r\n\r\nexport function MasonryAntiOverturningPerimeterAndWidespreadInterventionCalculationResultCard({\r\n  perimeterAndWidespreadInterventionCalculationResult,\r\n}: Props) {\r\n  const {\r\n    overturningMoment,\r\n    stabilizingMomentconnectors,\r\n    totalStabilizingMoment,\r\n    check,\r\n  } = perimeterAndWidespreadInterventionCalculationResult\r\n\r\n  const t = useTranslations(\r\n    'components.calculations.anti-overturning.postInterventionResult.perimeterAndWidespreadIntervention',\r\n  )\r\n  const locale = useLocale()\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <div>\r\n          <span className=\"font-medium\">{t('overturningMoment.label')}:</span>{' '}\r\n          <span>\r\n            {overturningMoment?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n            kNm\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('stabilizingMomentconnectors.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {stabilizingMomentconnectors?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n            kNm\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <div>\r\n            <span className=\"font-medium\">\r\n              {t('totalStabilizingMoment.label')}:\r\n            </span>{' '}\r\n            <span>\r\n              {totalStabilizingMoment?.toLocaleString(locale, {\r\n                maximumFractionDigits: 3,\r\n              })}{' '}\r\n              kNm\r\n            </span>\r\n          </div>\r\n          <span className=\"font-medium\">{t('check.label')}:</span>{' '}\r\n          <Badge\r\n            className={cn(\r\n              'text-base px-3 py-1',\r\n              check ? 'bg-green-600' : 'bg-red-600',\r\n            )}\r\n          >\r\n            {check ? t('check.satisfied') : t('check.notSatisfied')}\r\n          </Badge>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AAEA;AAAA;;;;;;;AAMO,SAAS,8EAA8E,KAEtF;QAFsF,EAC5F,mDAAmD,EAC7C,GAFsF;;IAG5F,MAAM,EACJ,iBAAiB,EACjB,2BAA2B,EAC3B,sBAAsB,EACtB,KAAK,EACN,GAAG;IAEJ,MAAM,IAAI,IAAA,4NAAe,EACvB;IAEF,MAAM,SAAS,IAAA,kLAAS;IAExB,qBACE,6LAAC,2IAAI;;0BACH,6LAAC,iJAAU;0BACT,cAAA,6LAAC,gJAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,6LAAC,kJAAW;gBAAC,WAAU;;kCACrB,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAA2B;;;;;;;4BAAS;0CACrE,6LAAC;;oCACE,8BAAA,wCAAA,kBAAmB,cAAc,CAAC,QAAQ;wCACzC,uBAAuB;oCACzB;oCAAI;oCAAI;;;;;;;;;;;;;kCAIZ,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAqC;;;;;;;4BAClC;0CACR,6LAAC;;oCACE,wCAAA,kDAAA,4BAA6B,cAAc,CAAC,QAAQ;wCACnD,uBAAuB;oCACzB;oCAAI;oCAAI;;;;;;;;;;;;;kCAIZ,6LAAC;;0CACC,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;;4CACb,EAAE;4CAAgC;;;;;;;oCAC7B;kDACR,6LAAC;;4CACE,mCAAA,6CAAA,uBAAwB,cAAc,CAAC,QAAQ;gDAC9C,uBAAuB;4CACzB;4CAAI;4CAAI;;;;;;;;;;;;;0CAIZ,6LAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAAe;;;;;;;4BAAS;0CACzD,6LAAC,6IAAK;gCACJ,WAAW,IAAA,4HAAE,EACX,uBACA,QAAQ,iBAAiB;0CAG1B,QAAQ,EAAE,qBAAqB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAM9C;GAlEgB;;QAUJ,4NAAe;QAGV,kLAAS;;;KAbV", "debugId": null}}, {"offset": {"line": 200, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/widespread-intervention-calculation-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { WidespreadInterventionCalculationResultSchemaInputs } from '@atlas/types/schemas/masonry/antioverturning-form'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  widespreadInterventionCalculationResult: WidespreadInterventionCalculationResultSchemaInputs\r\n}\r\n\r\nexport function MasonryAntiOverturningWidespreadInterventionCalculationResultCard({\r\n  widespreadInterventionCalculationResult,\r\n}: Props) {\r\n  const {\r\n    panelFundamentalPeriod,\r\n    maximumAcceleration,\r\n    overturningSeismicForce,\r\n    actingMoment,\r\n    midspanAxialStress,\r\n    normalStress,\r\n    unreinforcedSectionResistingMoment,\r\n    regionOneHypothesis,\r\n    regionTwoHypothesis,\r\n    specificResistingMoment,\r\n    check,\r\n  } = widespreadInterventionCalculationResult\r\n\r\n  const t = useTranslations(\r\n    'components.calculations.anti-overturning.postInterventionResult.widespreadIntervention',\r\n  )\r\n  const locale = useLocale()\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('panelFundamentalPeriod.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {panelFundamentalPeriod?.toLocaleString(locale, {\r\n              maximumFractionDigits: 4,\r\n            })}{' '}\r\n            s\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">{t('maximumAcceleration.label')}:</span>{' '}\r\n          <span>\r\n            {maximumAcceleration?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('overturningSeismicForce.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {overturningSeismicForce?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n            kN\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">{t('actingMoment.label')}:</span>{' '}\r\n          <span>\r\n            {actingMoment?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n            kNm\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">{t('midspanAxialStress.label')}:</span>{' '}\r\n          <span>\r\n            {midspanAxialStress?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n            kN\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">{t('normalStress.label')}:</span>{' '}\r\n          <span>\r\n            {normalStress?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n            MPa\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('unreinforcedSectionResistingMoment.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {unreinforcedSectionResistingMoment?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n            kNm/m\r\n          </span>\r\n        </div>\r\n        {regionTwoHypothesis && (\r\n          <div className=\"p-4 border rounded-lg\">\r\n            <h4 className=\"font-semibold mb-2\">\r\n              {t('regionHypothesis.regionTwoTitle')}\r\n            </h4>\r\n            <div>\r\n              <span className=\"font-medium\">\r\n                {t(\r\n                  'regionHypothesis.neutralAxisDistanceFromCompressedFlange.label',\r\n                )}\r\n                :\r\n              </span>{' '}\r\n              <span>\r\n                {regionTwoHypothesis?.neutralAxisDistanceFromCompressedFlange?.toLocaleString(\r\n                  locale,\r\n                  {\r\n                    maximumFractionDigits: 4,\r\n                  },\r\n                )}{' '}\r\n                mm\r\n              </span>\r\n            </div>\r\n            <div>\r\n              <span className=\"font-medium\">\r\n                {t('regionHypothesis.masonryDeformation.label')}:\r\n              </span>{' '}\r\n              <span>\r\n                {regionTwoHypothesis?.masonryDeformation?.toLocaleString(\r\n                  locale,\r\n                  {\r\n                    maximumFractionDigits: 3,\r\n                  },\r\n                )}{' '}\r\n              </span>\r\n            </div>\r\n            <div>\r\n              <span className=\"font-medium\">\r\n                {t(\r\n                  'regionHypothesis.masonryResultantCompressiveStresses.label',\r\n                )}\r\n                :\r\n              </span>{' '}\r\n              <span>\r\n                {regionTwoHypothesis?.masonryResultantCompressiveStresses?.toLocaleString(\r\n                  locale,\r\n                  {\r\n                    maximumFractionDigits: 3,\r\n                  },\r\n                )}{' '}\r\n                kN/m\r\n              </span>\r\n            </div>\r\n            <div>\r\n              <span className=\"font-medium\">\r\n                {t('regionHypothesis.frcmResultantTensileStresses.label')}:\r\n              </span>{' '}\r\n              <span>\r\n                {regionTwoHypothesis?.frcmResultantTensileStresses?.toLocaleString(\r\n                  locale,\r\n                  {\r\n                    maximumFractionDigits: 3,\r\n                  },\r\n                )}{' '}\r\n                kN/m\r\n              </span>\r\n            </div>\r\n            <div>\r\n              <span className=\"font-medium\">\r\n                {t(\r\n                  'regionHypothesis.designResistingMomentReinforcedSection.label',\r\n                )}\r\n                :\r\n              </span>{' '}\r\n              <span>\r\n                {regionTwoHypothesis?.designResistingMomentReinforcedSection?.toLocaleString(\r\n                  locale,\r\n                  {\r\n                    maximumFractionDigits: 3,\r\n                  },\r\n                )}{' '}\r\n                kNm/m\r\n              </span>\r\n            </div>\r\n            <div>\r\n              <span className=\"font-medium\">\r\n                {t('regionHypothesis.hypothesisCheck.label')}:\r\n              </span>{' '}\r\n              <span>{regionTwoHypothesis.hypothesisCheck ? 'ok' : 'no'}</span>\r\n            </div>\r\n          </div>\r\n        )}\r\n        {regionOneHypothesis && (\r\n          <div className=\"p-4 border rounded-lg\">\r\n            <h4 className=\"font-semibold mb-2\">\r\n              {t('regionHypothesis.regionOneTitle')}\r\n            </h4>\r\n            <div>\r\n              <span className=\"font-medium\">\r\n                {t(\r\n                  'regionHypothesis.neutralAxisDistanceFromCompressedFlange.label',\r\n                )}\r\n                :\r\n              </span>{' '}\r\n              <span>\r\n                {regionOneHypothesis?.neutralAxisDistanceFromCompressedFlange?.toLocaleString(\r\n                  locale,\r\n                  {\r\n                    maximumFractionDigits: 4,\r\n                  },\r\n                )}{' '}\r\n                mm\r\n              </span>\r\n            </div>\r\n            <div>\r\n              <span className=\"font-medium\">\r\n                {t('regionHypothesis.masonryDeformation.label')}:\r\n              </span>{' '}\r\n              <span>\r\n                {regionOneHypothesis?.masonryDeformation?.toLocaleString(\r\n                  locale,\r\n                  {\r\n                    maximumFractionDigits: 3,\r\n                  },\r\n                )}{' '}\r\n              </span>\r\n            </div>\r\n            <div>\r\n              <span className=\"font-medium\">\r\n                {t(\r\n                  'regionHypothesis.masonryResultantCompressiveStresses.label',\r\n                )}\r\n                :\r\n              </span>{' '}\r\n              <span>\r\n                {regionOneHypothesis?.masonryResultantCompressiveStresses?.toLocaleString(\r\n                  locale,\r\n                  {\r\n                    maximumFractionDigits: 3,\r\n                  },\r\n                )}{' '}\r\n                kN/m\r\n              </span>\r\n            </div>\r\n            <div>\r\n              <span className=\"font-medium\">\r\n                {t('regionHypothesis.frcmResultantTensileStresses.label')}:\r\n              </span>{' '}\r\n              <span>\r\n                {regionOneHypothesis?.frcmResultantTensileStresses?.toLocaleString(\r\n                  locale,\r\n                  {\r\n                    maximumFractionDigits: 3,\r\n                  },\r\n                )}{' '}\r\n                kN/m\r\n              </span>\r\n            </div>\r\n            <div>\r\n              <span className=\"font-medium\">\r\n                {t(\r\n                  'regionHypothesis.designResistingMomentReinforcedSection.label',\r\n                )}\r\n                :\r\n              </span>{' '}\r\n              <span>\r\n                {regionOneHypothesis?.designResistingMomentReinforcedSection?.toLocaleString(\r\n                  locale,\r\n                  {\r\n                    maximumFractionDigits: 3,\r\n                  },\r\n                )}{' '}\r\n                kNm/m\r\n              </span>\r\n            </div>\r\n            <div>\r\n              <span className=\"font-medium\">\r\n                {t('regionHypothesis.hypothesisCheck.label')}:\r\n              </span>{' '}\r\n              <span>{regionOneHypothesis.hypothesisCheck ? 'ok' : 'no'}</span>\r\n            </div>\r\n          </div>\r\n        )}\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('specificResistingMoment.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {specificResistingMoment?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n            kNm/m\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">{t('check.label')}:</span>{' '}\r\n          <Badge\r\n            className={cn(\r\n              'text-base px-3 py-1',\r\n              check ? 'bg-green-600' : 'bg-red-600',\r\n            )}\r\n          >\r\n            {check ? t('check.satisfied') : t('check.notSatisfied')}\r\n          </Badge>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AAEA;AAAA;;;;;;;AAMO,SAAS,kEAAkE,KAE1E;QAF0E,EAChF,uCAAuC,EACjC,GAF0E;QA6GjE,8DAcA,yCAgBA,0DAcA,mDAiBA,6DA8BA,8DAcA,yCAgBA,0DAcA,mDAiBA;;IAlQf,MAAM,EACJ,sBAAsB,EACtB,mBAAmB,EACnB,uBAAuB,EACvB,YAAY,EACZ,kBAAkB,EAClB,YAAY,EACZ,kCAAkC,EAClC,mBAAmB,EACnB,mBAAmB,EACnB,uBAAuB,EACvB,KAAK,EACN,GAAG;IAEJ,MAAM,IAAI,IAAA,4NAAe,EACvB;IAEF,MAAM,SAAS,IAAA,kLAAS;IAExB,qBACE,6LAAC,2IAAI;;0BACH,6LAAC,iJAAU;0BACT,cAAA,6LAAC,gJAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,6LAAC,kJAAW;gBAAC,WAAU;;kCACrB,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAgC;;;;;;;4BAC7B;0CACR,6LAAC;;oCACE,mCAAA,6CAAA,uBAAwB,cAAc,CAAC,QAAQ;wCAC9C,uBAAuB;oCACzB;oCAAI;oCAAI;;;;;;;;;;;;;kCAIZ,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAA6B;;;;;;;4BAAS;0CACvE,6LAAC;0CACE,gCAAA,0CAAA,oBAAqB,cAAc,CAAC,QAAQ;oCAC3C,uBAAuB;gCACzB;;;;;;;;;;;;kCAGJ,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAiC;;;;;;;4BAC9B;0CACR,6LAAC;;oCACE,oCAAA,8CAAA,wBAAyB,cAAc,CAAC,QAAQ;wCAC/C,uBAAuB;oCACzB;oCAAI;oCAAI;;;;;;;;;;;;;kCAIZ,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAAsB;;;;;;;4BAAS;0CAChE,6LAAC;;oCACE,yBAAA,mCAAA,aAAc,cAAc,CAAC,QAAQ;wCACpC,uBAAuB;oCACzB;oCAAI;oCAAI;;;;;;;;;;;;;kCAIZ,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAA4B;;;;;;;4BAAS;0CACtE,6LAAC;;oCACE,+BAAA,yCAAA,mBAAoB,cAAc,CAAC,QAAQ;wCAC1C,uBAAuB;oCACzB;oCAAI;oCAAI;;;;;;;;;;;;;kCAIZ,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAAsB;;;;;;;4BAAS;0CAChE,6LAAC;;oCACE,yBAAA,mCAAA,aAAc,cAAc,CAAC,QAAQ;wCACpC,uBAAuB;oCACzB;oCAAI;oCAAI;;;;;;;;;;;;;kCAIZ,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA4C;;;;;;;4BACzC;0CACR,6LAAC;;oCACE,+CAAA,yDAAA,mCAAoC,cAAc,CAAC,QAAQ;wCAC1D,uBAAuB;oCACzB;oCAAI;oCAAI;;;;;;;;;;;;;oBAIX,qCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;;4CACb,EACC;4CACA;;;;;;;oCAEI;kDACR,6LAAC;;4CACE,gCAAA,2CAAA,+DAAA,oBAAqB,uCAAuC,cAA5D,mFAAA,6DAA8D,cAAc,CAC3E,QACA;gDACE,uBAAuB;4CACzB;4CACC;4CAAI;;;;;;;;;;;;;0CAIX,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;;4CACb,EAAE;4CAA6C;;;;;;;oCAC1C;kDACR,6LAAC;;4CACE,gCAAA,2CAAA,0CAAA,oBAAqB,kBAAkB,cAAvC,8DAAA,wCAAyC,cAAc,CACtD,QACA;gDACE,uBAAuB;4CACzB;4CACC;;;;;;;;;;;;;0CAGP,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;;4CACb,EACC;4CACA;;;;;;;oCAEI;kDACR,6LAAC;;4CACE,gCAAA,2CAAA,2DAAA,oBAAqB,mCAAmC,cAAxD,+EAAA,yDAA0D,cAAc,CACvE,QACA;gDACE,uBAAuB;4CACzB;4CACC;4CAAI;;;;;;;;;;;;;0CAIX,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;;4CACb,EAAE;4CAAuD;;;;;;;oCACpD;kDACR,6LAAC;;4CACE,gCAAA,2CAAA,oDAAA,oBAAqB,4BAA4B,cAAjD,wEAAA,kDAAmD,cAAc,CAChE,QACA;gDACE,uBAAuB;4CACzB;4CACC;4CAAI;;;;;;;;;;;;;0CAIX,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;;4CACb,EACC;4CACA;;;;;;;oCAEI;kDACR,6LAAC;;4CACE,gCAAA,2CAAA,8DAAA,oBAAqB,sCAAsC,cAA3D,kFAAA,4DAA6D,cAAc,CAC1E,QACA;gDACE,uBAAuB;4CACzB;4CACC;4CAAI;;;;;;;;;;;;;0CAIX,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;;4CACb,EAAE;4CAA0C;;;;;;;oCACvC;kDACR,6LAAC;kDAAM,oBAAoB,eAAe,GAAG,OAAO;;;;;;;;;;;;;;;;;;oBAIzD,qCACC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;0CAEL,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;;4CACb,EACC;4CACA;;;;;;;oCAEI;kDACR,6LAAC;;4CACE,gCAAA,2CAAA,+DAAA,oBAAqB,uCAAuC,cAA5D,mFAAA,6DAA8D,cAAc,CAC3E,QACA;gDACE,uBAAuB;4CACzB;4CACC;4CAAI;;;;;;;;;;;;;0CAIX,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;;4CACb,EAAE;4CAA6C;;;;;;;oCAC1C;kDACR,6LAAC;;4CACE,gCAAA,2CAAA,0CAAA,oBAAqB,kBAAkB,cAAvC,8DAAA,wCAAyC,cAAc,CACtD,QACA;gDACE,uBAAuB;4CACzB;4CACC;;;;;;;;;;;;;0CAGP,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;;4CACb,EACC;4CACA;;;;;;;oCAEI;kDACR,6LAAC;;4CACE,gCAAA,2CAAA,2DAAA,oBAAqB,mCAAmC,cAAxD,+EAAA,yDAA0D,cAAc,CACvE,QACA;gDACE,uBAAuB;4CACzB;4CACC;4CAAI;;;;;;;;;;;;;0CAIX,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;;4CACb,EAAE;4CAAuD;;;;;;;oCACpD;kDACR,6LAAC;;4CACE,gCAAA,2CAAA,oDAAA,oBAAqB,4BAA4B,cAAjD,wEAAA,kDAAmD,cAAc,CAChE,QACA;gDACE,uBAAuB;4CACzB;4CACC;4CAAI;;;;;;;;;;;;;0CAIX,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;;4CACb,EACC;4CACA;;;;;;;oCAEI;kDACR,6LAAC;;4CACE,gCAAA,2CAAA,8DAAA,oBAAqB,sCAAsC,cAA3D,kFAAA,4DAA6D,cAAc,CAC1E,QACA;gDACE,uBAAuB;4CACzB;4CACC;4CAAI;;;;;;;;;;;;;0CAIX,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;;4CACb,EAAE;4CAA0C;;;;;;;oCACvC;kDACR,6LAAC;kDAAM,oBAAoB,eAAe,GAAG,OAAO;;;;;;;;;;;;;;;;;;kCAI1D,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAiC;;;;;;;4BAC9B;0CACR,6LAAC;;oCACE,oCAAA,8CAAA,wBAAyB,cAAc,CAAC,QAAQ;wCAC/C,uBAAuB;oCACzB;oCAAI;oCAAI;;;;;;;;;;;;;kCAIZ,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAAe;;;;;;;4BAAS;0CACzD,6LAAC,6IAAK;gCACJ,WAAW,IAAA,4HAAE,EACX,uBACA,QAAQ,iBAAiB;0CAG1B,QAAQ,EAAE,qBAAqB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAM9C;GA/SgB;;QAiBJ,4NAAe;QAGV,kLAAS;;;KApBV", "debugId": null}}, {"offset": {"line": 974, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-post-intervention-calculation-result.tsx"], "sourcesContent": ["import {\r\n  Tabs,\r\n  Ta<PERSON>Content,\r\n  Ta<PERSON>List,\r\n  TabsTrigger,\r\n} from '@atlas/components/ui/tabs'\r\nimport type { MasonryAntiOverturningPostInterventionCalculationResultSchemaInputs } from '@atlas/types/schemas/masonry/antioverturning-form'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\nimport { MasonryAntiOverturningPerimeterAndWidespreadInterventionCalculationResultCard } from './perimeter-and-widespread-intervention-calculation-result-card'\r\nimport { MasonryAntiOverturningWidespreadInterventionCalculationResultCard } from './widespread-intervention-calculation-result-card'\r\n\r\ntype Props = {\r\n  postInterventionCalculationResult: MasonryAntiOverturningPostInterventionCalculationResultSchemaInputs\r\n}\r\n\r\nexport function MasonryAntiOverturningPostInterventionCalculationResult({\r\n  postInterventionCalculationResult,\r\n}: Props) {\r\n  const t = useTranslations(\r\n    'components.calculations.anti-overturning.postInterventionResult',\r\n  )\r\n  const _locale = useLocale()\r\n\r\n  return (\r\n    <>\r\n      <h3 className=\"text-lg font-medium py-4\">{t('title')}</h3>\r\n      <Tabs defaultValue=\"perimeterAndWidespreadIntervention\">\r\n        <TabsList>\r\n          <TabsTrigger value=\"perimeterAndWidespreadIntervention\">\r\n            {t('perimeterAndWidespreadIntervention.label')}\r\n          </TabsTrigger>\r\n          <TabsTrigger value=\"widespreadIntervention\">\r\n            {t('widespreadIntervention.label')}\r\n          </TabsTrigger>\r\n        </TabsList>\r\n        <TabsContent value=\"perimeterAndWidespreadIntervention\">\r\n          <MasonryAntiOverturningPerimeterAndWidespreadInterventionCalculationResultCard\r\n            perimeterAndWidespreadInterventionCalculationResult={\r\n              postInterventionCalculationResult.perimeterAndWidespreadInterventionCalculationResult ??\r\n              {}\r\n            }\r\n          />\r\n        </TabsContent>\r\n        <TabsContent value=\"widespreadIntervention\">\r\n          <MasonryAntiOverturningWidespreadInterventionCalculationResultCard\r\n            widespreadInterventionCalculationResult={\r\n              postInterventionCalculationResult.widespreadInterventionCalculationResult ??\r\n              {}\r\n            }\r\n          />\r\n        </TabsContent>\r\n      </Tabs>\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAOA;AAAA;AACA;AACA;;;;;;;AAMO,SAAS,wDAAwD,KAEhE;QAFgE,EACtE,iCAAiC,EAC3B,GAFgE;;IAGtE,MAAM,IAAI,IAAA,4NAAe,EACvB;IAEF,MAAM,UAAU,IAAA,kLAAS;QAiBb,wFAQA;IAvBZ,qBACE;;0BACE,6LAAC;gBAAG,WAAU;0BAA4B,EAAE;;;;;;0BAC5C,6LAAC,2IAAI;gBAAC,cAAa;;kCACjB,6LAAC,+IAAQ;;0CACP,6LAAC,kJAAW;gCAAC,OAAM;0CAChB,EAAE;;;;;;0CAEL,6LAAC,kJAAW;gCAAC,OAAM;0CAChB,EAAE;;;;;;;;;;;;kCAGP,6LAAC,kJAAW;wBAAC,OAAM;kCACjB,cAAA,6LAAC,qYAA6E;4BAC5E,qDACE,CAAA,yFAAA,kCAAkC,mDAAmD,cAArF,oGAAA,yFACA,CAAC;;;;;;;;;;;kCAIP,6LAAC,kJAAW;wBAAC,OAAM;kCACjB,cAAA,6LAAC,qWAAiE;4BAChE,yCACE,CAAA,6EAAA,kCAAkC,uCAAuC,cAAzE,wFAAA,6EACA,CAAC;;;;;;;;;;;;;;;;;;;AAOf;GAvCgB;;QAGJ,4NAAe;QAGT,kLAAS;;;KANX", "debugId": null}}, {"offset": {"line": 1086, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-composite-reinforcement-system-calculation-form.tsx"], "sourcesContent": ["import { CustomProductSection } from '@atlas/components/common/atlas/product/custom-product-section'\r\nimport { ProductSummaryCard } from '@atlas/components/common/atlas/product/product-summary-card'\r\nimport { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { productFiberType } from '@atlas/constants/product'\r\nimport type { ModuleWithParamsAntiOverturning } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useModuleCalculation } from '@atlas/lib/mutation/modules/use-module-calculation'\r\nimport { useProductsByCategory } from '@atlas/lib/query/products/use-products-by-category'\r\nimport {\r\n  type MasonryAntiOverturningCompositeReinforcementSystemSchemaInputs,\r\n  masonryAntiOverturningCompositeReinforcementSystemSchema,\r\n} from '@atlas/types/schemas/masonry/antioverturning-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Separator } from '@radix-ui/react-dropdown-menu'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useMemo } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\nimport { MasonryAntiOverturningPostInterventionCalculationResult } from './anti-overturning-post-intervention-calculation-result'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  module: ModuleWithParamsAntiOverturning\r\n}\r\n\r\nexport const MasonryAntiOverturningCompositeReinforcementSystemCalculationForm =\r\n  ({ session, projectId, module }: Props) => {\r\n    const t = useTranslations(\r\n      'forms.calculations.anti-overturning.composite-reinforcement-system',\r\n    )\r\n    const tAction = useTranslations(\r\n      'actions.calculations.anti-overturning.composite-reinforcement-system',\r\n    )\r\n    const tCommon = useTranslations('actions.common')\r\n\r\n    const postInterventionCalculationResult =\r\n      module.postInterventionCalculationResult\r\n    const defaultValues = module?.compositeReinforcementSystemVerifyInput\r\n\r\n    const meshProduct =\r\n      module?.compositeReinforcementSystemVerifyInput?.meshInput?.meshProduct\r\n    const connectorProduct =\r\n      module?.compositeReinforcementSystemVerifyInput?.connectorInput\r\n        ?.connectorProduct\r\n    const matrixProduct =\r\n      module?.compositeReinforcementSystemVerifyInput?.matrixInput\r\n        ?.matrixProduct\r\n\r\n    const form =\r\n      useForm<MasonryAntiOverturningCompositeReinforcementSystemSchemaInputs>({\r\n        resolver: zodResolver(\r\n          masonryAntiOverturningCompositeReinforcementSystemSchema,\r\n        ),\r\n        defaultValues: {\r\n          calculationType: 'POST_INTERVENTION_VERIFY',\r\n          input: {\r\n            meshInput: {\r\n              // to be calculated: D65*D64*D69\r\n              // D65 : thickness, D64: width, D69: reinforced sides number\r\n              resistantAreaPerMeter:\r\n                defaultValues?.meshInput?.resistantAreaPerMeter ??\r\n                (meshProduct?.thickness ?? 0) *\r\n                  (meshProduct?.width ?? 0) *\r\n                  (defaultValues?.meshInput?.reinforcedSidesNumber ?? 0),\r\n              reinforcedSidesNumber:\r\n                defaultValues?.meshInput?.reinforcedSidesNumber ?? 0,\r\n              meshProduct: {\r\n                id: meshProduct?.id ?? '',\r\n                name: meshProduct?.name,\r\n                sourceType:\r\n                  meshProduct?.id === 'CUSTOM' ? 'CUSTOM' : 'DATABASE',\r\n                thickness: meshProduct?.thickness ?? 0,\r\n                tensileStrength: meshProduct?.tensileStrength ?? 0,\r\n                elasticModulus: meshProduct?.elasticModulus ?? 0,\r\n                fiberType: meshProduct?.fiberType ?? productFiberType.CARBON,\r\n              },\r\n            },\r\n            connectorInput: {\r\n              connectorSpacing:\r\n                defaultValues?.connectorInput?.connectorSpacing ?? 0,\r\n              connectorProduct: {\r\n                id: connectorProduct?.id ?? '',\r\n                name: connectorProduct?.name,\r\n                sourceType:\r\n                  connectorProduct?.id === 'custom' ? 'CUSTOM' : 'DATABASE',\r\n                thickness: connectorProduct?.thickness ?? 0,\r\n                tensileStrength: connectorProduct?.tensileStrength ?? 0,\r\n                elasticModulus: connectorProduct?.elasticModulus ?? 0,\r\n                fiberType:\r\n                  connectorProduct?.fiberType ?? productFiberType.CARBON,\r\n              },\r\n            },\r\n            matrixInput: {\r\n              compositeSystemThickness:\r\n                defaultValues?.matrixInput?.compositeSystemThickness ?? 0,\r\n              matrixProduct: {\r\n                id: matrixProduct?.id ?? '',\r\n                name: matrixProduct?.name ?? '',\r\n                sourceType:\r\n                  matrixProduct?.id === 'custom' ? 'CUSTOM' : 'DATABASE',\r\n              },\r\n            },\r\n          },\r\n        },\r\n      })\r\n\r\n    const { mutate, isPending } = useModuleCalculation(session.accessToken, {\r\n      onSuccess: () => {\r\n        toast.success(tAction('calculate.success'))\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('calculate.failure', { error: error.message }))\r\n      },\r\n    })\r\n\r\n    const handleFormSubmit = (\r\n      body: MasonryAntiOverturningCompositeReinforcementSystemSchemaInputs,\r\n    ) => {\r\n      mutate({ projectId, moduleId: module.id, body })\r\n    }\r\n\r\n    const {\r\n      data: productsAll,\r\n      isError: errorGettingProducts,\r\n      isLoading: isLoadingProducts,\r\n    } = useProductsByCategory(session, 'ANTI_OVERTURNING', 0, 100)\r\n\r\n    const productsMeshOptions = [\r\n      ...(productsAll?.content\r\n        .filter(m => m.productType === 'MESH')\r\n        .map(m => ({\r\n          value: m.id,\r\n          label: m.name ?? t('product.unnamed'),\r\n        })) ?? []),\r\n      // temporary disable\r\n      // { value: 'custom', label: t('product.custom') },\r\n    ]\r\n\r\n    const productsConnectorOptions = [\r\n      ...(productsAll?.content\r\n        .filter(m => m.productType === 'CONNECTOR')\r\n        .map(m => ({\r\n          value: m.id,\r\n          label: m.name ?? t('product.unnamed'),\r\n        })) ?? []),\r\n      // temporary disable\r\n      // { value: 'custom', label: t('product.custom') },\r\n    ]\r\n\r\n    const productsMatrixOptions = [\r\n      ...(productsAll?.content\r\n        .filter(m => m.productType === 'MATRIX')\r\n        .map(m => ({\r\n          value: m.id,\r\n          label: m.name ?? t('product.unnamed'),\r\n        })) ?? []),\r\n      // temporary disable\r\n      // { value: 'custom', label: t('product.custom') },\r\n    ]\r\n\r\n    const [productMeshId] = form.watch(['input.meshInput.meshProduct.id'])\r\n    const [productConnectorId] = form.watch([\r\n      'input.connectorInput.connectorProduct.id',\r\n    ])\r\n    const [productMatrixId] = form.watch(['input.matrixInput.matrixProduct.id'])\r\n\r\n    const selectedProductMesh = useMemo(\r\n      () =>\r\n        productsAll?.content\r\n          .filter(m => m.productType === 'MESH')\r\n          .find(p => p.id === productMeshId),\r\n      [productMeshId, productsAll],\r\n    )\r\n\r\n    const selectedProductConnector = useMemo(\r\n      () =>\r\n        productsAll?.content\r\n          .filter(m => m.productType === 'CONNECTOR')\r\n          .find(p => p.id === productConnectorId),\r\n      [productConnectorId, productsAll],\r\n    )\r\n\r\n    const selectedProductMatrix = useMemo(\r\n      () =>\r\n        productsAll?.content\r\n          .filter(m => m.productType === 'MATRIX')\r\n          .find(p => p.id === productMatrixId),\r\n      [productMatrixId, productsAll],\r\n    )\r\n\r\n    const reinforcedSidesNumberValue = form.watch(\r\n      'input.meshInput.reinforcedSidesNumber',\r\n    )\r\n\r\n    useEffect(() => {\r\n      if (productMeshId === 'custom') {\r\n        form.setValue('input.meshInput.meshProduct.sourceType', 'CUSTOM')\r\n      }\r\n      if (productConnectorId === 'custom') {\r\n        form.setValue(\r\n          'input.connectorInput.connectorProduct.sourceType',\r\n          'CUSTOM',\r\n        )\r\n      }\r\n      if (productMatrixId === 'custom') {\r\n        form.setValue('input.matrixInput.matrixProduct.sourceType', 'CUSTOM')\r\n      }\r\n\r\n      if (selectedProductMesh) {\r\n        form.setValue('input.meshInput.meshProduct', {\r\n          id: selectedProductMesh.id,\r\n          name: selectedProductMesh.name,\r\n          sourceType: 'DATABASE',\r\n        })\r\n\r\n        form.setValue(\r\n          'input.meshInput.resistantAreaPerMeter',\r\n          (selectedProductMesh?.thickness ?? 0) *\r\n            (selectedProductMesh?.width ??\r\n              selectedProductMesh.availableWidths?.[0] ??\r\n              0) *\r\n            (reinforcedSidesNumberValue ?? 0),\r\n        )\r\n      }\r\n\r\n      if (selectedProductConnector) {\r\n        form.setValue('input.connectorInput.connectorProduct', {\r\n          id: selectedProductConnector.id,\r\n          name: selectedProductConnector.name,\r\n          sourceType: 'DATABASE',\r\n        })\r\n      }\r\n\r\n      if (selectedProductMatrix) {\r\n        form.setValue('input.matrixInput.matrixProduct', {\r\n          id: selectedProductMatrix.id,\r\n          name: selectedProductMatrix.name,\r\n          sourceType: 'DATABASE',\r\n        })\r\n      }\r\n    }, [\r\n      form,\r\n      productMeshId,\r\n      selectedProductMesh,\r\n      selectedProductConnector,\r\n      selectedProductMatrix,\r\n      reinforcedSidesNumberValue,\r\n      productConnectorId,\r\n      productMatrixId,\r\n    ])\r\n\r\n    return (\r\n      <div className=\"flex flex-col justify-center gap-4\">\r\n        <Form {...form}>\r\n          <form\r\n            className=\"space-y-4 rounded-md border p-4\"\r\n            onSubmit={form.handleSubmit(handleFormSubmit)}\r\n          >\r\n            <Image\r\n              src=\"/assets/masonry-anti-overturning/antibaltamento-verify.jpg\"\r\n              alt=\"composite reinforcement system verify\"\r\n              height={250}\r\n              width={500}\r\n              className=\"mx-auto rounded-md object-contain\"\r\n              priority\r\n            />\r\n            {/* Mesh Section */}\r\n            <h1 className=\"text-xl font-bold\">{t('mesh-sub-heading')}</h1>\r\n            <SelectFormInput\r\n              control={form.control}\r\n              name=\"input.meshInput.meshProduct.id\"\r\n              options={productsMeshOptions}\r\n              t={t}\r\n              loading={isLoadingProducts}\r\n              requestError={errorGettingProducts}\r\n              // get the error message from the form state if any\r\n              errorMessage={\r\n                form.formState.errors.input?.meshInput?.meshProduct?.id?.message\r\n              }\r\n            />\r\n            {productMeshId === 'custom' && <CustomProductSection />}\r\n            {selectedProductMesh && (\r\n              <ProductSummaryCard\r\n                product={selectedProductMesh}\r\n                isRectangularBeam={false}\r\n              />\r\n            )}\r\n            <NumberFormInput\r\n              control={form.control}\r\n              name=\"input.meshInput.reinforcedSidesNumber\"\r\n              t={t}\r\n            />\r\n            <NumberFormInput\r\n              control={form.control}\r\n              name=\"input.meshInput.resistantAreaPerMeter\"\r\n              t={t}\r\n              disabled={true}\r\n            />\r\n            <Separator />\r\n            {/* Connector Section */}\r\n            <h1 className=\"text-xl font-bold\">{t('connector-sub-heading')}</h1>\r\n            <SelectFormInput\r\n              control={form.control}\r\n              name=\"input.connectorInput.connectorProduct.id\"\r\n              options={productsConnectorOptions}\r\n              t={t}\r\n              loading={isLoadingProducts}\r\n              requestError={errorGettingProducts}\r\n              errorMessage={\r\n                form.formState.errors.input?.connectorInput?.connectorProduct\r\n                  ?.id?.message\r\n              }\r\n            />\r\n            {productConnectorId === 'custom' && <CustomProductSection />}\r\n            {selectedProductConnector && (\r\n              <ProductSummaryCard\r\n                product={selectedProductConnector}\r\n                facingMaterial={\r\n                  module?.params?.materialProperties?.facingMaterial\r\n                }\r\n                isRectangularBeam={false}\r\n              />\r\n            )}\r\n            <NumberFormInput\r\n              control={form.control}\r\n              name=\"input.connectorInput.connectorSpacing\"\r\n              t={t}\r\n            />\r\n            <Separator />\r\n            {/* Matrix Section */}\r\n            <h1 className=\"text-xl font-bold\">{t('matrix-sub-heading')}</h1>\r\n            <SelectFormInput\r\n              control={form.control}\r\n              name=\"input.matrixInput.matrixProduct.id\"\r\n              options={productsMatrixOptions}\r\n              t={t}\r\n              loading={isLoadingProducts}\r\n              requestError={errorGettingProducts}\r\n              errorMessage={\r\n                form.formState.errors.input?.matrixInput?.matrixProduct?.id\r\n                  ?.message\r\n              }\r\n            />\r\n            {productMatrixId === 'custom' && <CustomProductSection />}\r\n            {selectedProductMatrix && (\r\n              <ProductSummaryCard\r\n                product={selectedProductMatrix}\r\n                isRectangularBeam={false}\r\n              />\r\n            )}\r\n            <NumberFormInput\r\n              control={form.control}\r\n              name=\"input.matrixInput.compositeSystemThickness\"\r\n              t={t}\r\n            />\r\n            <Button\r\n              type=\"submit\"\r\n              className=\"w-full sm:w-auto\"\r\n              disabled={isPending}\r\n            >\r\n              {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n              {tCommon('calculate')}\r\n            </Button>\r\n          </form>\r\n        </Form>\r\n        {postInterventionCalculationResult && (\r\n          <MasonryAntiOverturningPostInterventionCalculationResult\r\n            postInterventionCalculationResult={\r\n              postInterventionCalculationResult\r\n            }\r\n          />\r\n        )}\r\n      </div>\r\n    )\r\n  }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAIA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;AAQO,MAAM,oEACX;QAAC,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAS;QAclC,2DAAA,iDAEA,gEAAA,kDAGA,6DAAA,kDAeU,0BAGG,2BAEH,2BAcA,+BAeA,4BAuLA,uDAAA,oDAAA,wCAAA,8BAgCA,iEAAA,8DAAA,6CAAA,+BASE,mCAAA,gBAqBF,2DAAA,wDAAA,0CAAA;;IAxTZ,MAAM,IAAI,IAAA,4NAAe,EACvB;IAEF,MAAM,UAAU,IAAA,4NAAe,EAC7B;IAEF,MAAM,UAAU,IAAA,4NAAe,EAAC;IAEhC,MAAM,oCACJ,OAAO,iCAAiC;IAC1C,MAAM,gBAAgB,mBAAA,6BAAA,OAAQ,uCAAuC;IAErE,MAAM,cACJ,mBAAA,8BAAA,kDAAA,OAAQ,uCAAuC,cAA/C,uEAAA,4DAAA,gDAAiD,SAAS,cAA1D,gFAAA,0DAA4D,WAAW;IACzE,MAAM,mBACJ,mBAAA,8BAAA,mDAAA,OAAQ,uCAAuC,cAA/C,wEAAA,iEAAA,iDAAiD,cAAc,cAA/D,qFAAA,+DACI,gBAAgB;IACtB,MAAM,gBACJ,mBAAA,8BAAA,mDAAA,OAAQ,uCAAuC,cAA/C,wEAAA,8DAAA,iDAAiD,WAAW,cAA5D,kFAAA,4DACI,aAAa;QAeN,wBACE,oBACA,gDAHH,gDAKA,iDAEI,iBAIO,yBACM,8BACD,6BACL,wBAKX,gDAEI,sBAIO,6BACM,mCACD,kCAEd,6BAKF,qDAEI,mBACE;IAjDlB,MAAM,OACJ,IAAA,4KAAO,EAAiE;QACtE,UAAU,IAAA,gLAAW,EACnB,4NAAwD;QAE1D,eAAe;YACb,iBAAiB;YACjB,OAAO;gBACL,WAAW;oBACT,gCAAgC;oBAChC,4DAA4D;oBAC5D,uBACE,CAAA,iDAAA,0BAAA,qCAAA,2BAAA,cAAe,SAAS,cAAxB,+CAAA,yBAA0B,qBAAqB,cAA/C,4DAAA,iDACA,CAAC,CAAA,yBAAA,wBAAA,kCAAA,YAAa,SAAS,cAAtB,oCAAA,yBAA0B,CAAC,IAC1B,CAAC,CAAA,qBAAA,wBAAA,kCAAA,YAAa,KAAK,cAAlB,gCAAA,qBAAsB,CAAC,IACxB,CAAC,CAAA,iDAAA,0BAAA,qCAAA,4BAAA,cAAe,SAAS,cAAxB,gDAAA,0BAA0B,qBAAqB,cAA/C,4DAAA,iDAAmD,CAAC;oBACzD,uBACE,CAAA,kDAAA,0BAAA,qCAAA,4BAAA,cAAe,SAAS,cAAxB,gDAAA,0BAA0B,qBAAqB,cAA/C,6DAAA,kDAAmD;oBACrD,aAAa;wBACX,IAAI,CAAA,kBAAA,wBAAA,kCAAA,YAAa,EAAE,cAAf,6BAAA,kBAAmB;wBACvB,IAAI,EAAE,wBAAA,kCAAA,YAAa,IAAI;wBACvB,YACE,CAAA,wBAAA,kCAAA,YAAa,EAAE,MAAK,WAAW,WAAW;wBAC5C,WAAW,CAAA,0BAAA,wBAAA,kCAAA,YAAa,SAAS,cAAtB,qCAAA,0BAA0B;wBACrC,iBAAiB,CAAA,+BAAA,wBAAA,kCAAA,YAAa,eAAe,cAA5B,0CAAA,+BAAgC;wBACjD,gBAAgB,CAAA,8BAAA,wBAAA,kCAAA,YAAa,cAAc,cAA3B,yCAAA,8BAA+B;wBAC/C,WAAW,CAAA,yBAAA,wBAAA,kCAAA,YAAa,SAAS,cAAtB,oCAAA,yBAA0B,mJAAgB,CAAC,MAAM;oBAC9D;gBACF;gBACA,gBAAgB;oBACd,kBACE,CAAA,iDAAA,0BAAA,qCAAA,gCAAA,cAAe,cAAc,cAA7B,oDAAA,8BAA+B,gBAAgB,cAA/C,4DAAA,iDAAmD;oBACrD,kBAAkB;wBAChB,IAAI,CAAA,uBAAA,6BAAA,uCAAA,iBAAkB,EAAE,cAApB,kCAAA,uBAAwB;wBAC5B,IAAI,EAAE,6BAAA,uCAAA,iBAAkB,IAAI;wBAC5B,YACE,CAAA,6BAAA,uCAAA,iBAAkB,EAAE,MAAK,WAAW,WAAW;wBACjD,WAAW,CAAA,8BAAA,6BAAA,uCAAA,iBAAkB,SAAS,cAA3B,yCAAA,8BAA+B;wBAC1C,iBAAiB,CAAA,oCAAA,6BAAA,uCAAA,iBAAkB,eAAe,cAAjC,+CAAA,oCAAqC;wBACtD,gBAAgB,CAAA,mCAAA,6BAAA,uCAAA,iBAAkB,cAAc,cAAhC,8CAAA,mCAAoC;wBACpD,WACE,CAAA,8BAAA,6BAAA,uCAAA,iBAAkB,SAAS,cAA3B,yCAAA,8BAA+B,mJAAgB,CAAC,MAAM;oBAC1D;gBACF;gBACA,aAAa;oBACX,0BACE,CAAA,sDAAA,0BAAA,qCAAA,6BAAA,cAAe,WAAW,cAA1B,iDAAA,2BAA4B,wBAAwB,cAApD,iEAAA,sDAAwD;oBAC1D,eAAe;wBACb,IAAI,CAAA,oBAAA,0BAAA,oCAAA,cAAe,EAAE,cAAjB,+BAAA,oBAAqB;wBACzB,MAAM,CAAA,sBAAA,0BAAA,oCAAA,cAAe,IAAI,cAAnB,iCAAA,sBAAuB;wBAC7B,YACE,CAAA,0BAAA,oCAAA,cAAe,EAAE,MAAK,WAAW,WAAW;oBAChD;gBACF;YACF;QACF;IACF;IAEF,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,6LAAoB,EAAC,QAAQ,WAAW,EAAE;QACtE,SAAS;sGAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;YACxB;;QACA,OAAO;sGAAE,CAAA;gBACP,oJAAK,CAAC,KAAK,CAAC,QAAQ,qBAAqB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAClE;;IACF;IAEA,MAAM,mBAAmB,CACvB;QAEA,OAAO;YAAE;YAAW,UAAU,OAAO,EAAE;YAAE;QAAK;IAChD;IAEA,MAAM,EACJ,MAAM,WAAW,EACjB,SAAS,oBAAoB,EAC7B,WAAW,iBAAiB,EAC7B,GAAG,IAAA,iMAAqB,EAAC,SAAS,oBAAoB,GAAG;QAGpD;IADN,MAAM,sBAAsB;WACtB,CAAA,kCAAA,wBAAA,kCAAA,YAAa,OAAO,CACrB,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK,QAC9B,GAAG,CAAC,CAAA;gBAEI;mBAFE;gBACT,OAAO,EAAE,EAAE;gBACX,OAAO,CAAA,UAAA,EAAE,IAAI,cAAN,qBAAA,UAAU,EAAE;YACrB;wBALE,6CAAA,kCAKK,EAAE;KAGZ;QAGK;IADN,MAAM,2BAA2B;WAC3B,CAAA,mCAAA,wBAAA,kCAAA,YAAa,OAAO,CACrB,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK,aAC9B,GAAG,CAAC,CAAA;gBAEI;mBAFE;gBACT,OAAO,EAAE,EAAE;gBACX,OAAO,CAAA,UAAA,EAAE,IAAI,cAAN,qBAAA,UAAU,EAAE;YACrB;wBALE,8CAAA,mCAKK,EAAE;KAGZ;QAGK;IADN,MAAM,wBAAwB;WACxB,CAAA,mCAAA,wBAAA,kCAAA,YAAa,OAAO,CACrB,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK,UAC9B,GAAG,CAAC,CAAA;gBAEI;mBAFE;gBACT,OAAO,EAAE,EAAE;gBACX,OAAO,CAAA,UAAA,EAAE,IAAI,cAAN,qBAAA,UAAU,EAAE;YACrB;wBALE,8CAAA,mCAKK,EAAE;KAGZ;IAED,MAAM,CAAC,cAAc,GAAG,KAAK,KAAK,CAAC;QAAC;KAAiC;IACrE,MAAM,CAAC,mBAAmB,GAAG,KAAK,KAAK,CAAC;QACtC;KACD;IACD,MAAM,CAAC,gBAAgB,GAAG,KAAK,KAAK,CAAC;QAAC;KAAqC;IAE3E,MAAM,sBAAsB,IAAA,wKAAO;0GACjC,IACE,wBAAA,kCAAA,YAAa,OAAO,CACjB,MAAM;kHAAC,CAAA,IAAK,EAAE,WAAW,KAAK;iHAC9B,IAAI;kHAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;yGACxB;QAAC;QAAe;KAAY;IAG9B,MAAM,2BAA2B,IAAA,wKAAO;+GACtC,IACE,wBAAA,kCAAA,YAAa,OAAO,CACjB,MAAM;uHAAC,CAAA,IAAK,EAAE,WAAW,KAAK;sHAC9B,IAAI;uHAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;8GACxB;QAAC;QAAoB;KAAY;IAGnC,MAAM,wBAAwB,IAAA,wKAAO;4GACnC,IACE,wBAAA,kCAAA,YAAa,OAAO,CACjB,MAAM;oHAAC,CAAA,IAAK,EAAE,WAAW,KAAK;mHAC9B,IAAI;oHAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;2GACxB;QAAC;QAAiB;KAAY;IAGhC,MAAM,6BAA6B,KAAK,KAAK,CAC3C;IAGF,IAAA,0KAAS;uFAAC;YACR,IAAI,kBAAkB,UAAU;gBAC9B,KAAK,QAAQ,CAAC,0CAA0C;YAC1D;YACA,IAAI,uBAAuB,UAAU;gBACnC,KAAK,QAAQ,CACX,oDACA;YAEJ;YACA,IAAI,oBAAoB,UAAU;gBAChC,KAAK,QAAQ,CAAC,8CAA8C;YAC9D;YAEA,IAAI,qBAAqB;oBAWjB;gBAVN,KAAK,QAAQ,CAAC,+BAA+B;oBAC3C,IAAI,oBAAoB,EAAE;oBAC1B,MAAM,oBAAoB,IAAI;oBAC9B,YAAY;gBACd;oBAIG,gCACE,4BAAA;gBAHL,KAAK,QAAQ,CACX,yCACA,CAAC,CAAA,iCAAA,gCAAA,0CAAA,oBAAqB,SAAS,cAA9B,4CAAA,iCAAkC,CAAC,IAClC,CAAC,CAAA,OAAA,CAAA,6BAAA,gCAAA,0CAAA,oBAAqB,KAAK,cAA1B,wCAAA,8BACC,uCAAA,oBAAoB,eAAe,cAAnC,2DAAA,oCAAqC,CAAC,EAAE,cADzC,kBAAA,OAEC,CAAC,IACH,CAAC,uCAAA,wCAAA,6BAA8B,CAAC;YAEtC;YAEA,IAAI,0BAA0B;gBAC5B,KAAK,QAAQ,CAAC,yCAAyC;oBACrD,IAAI,yBAAyB,EAAE;oBAC/B,MAAM,yBAAyB,IAAI;oBACnC,YAAY;gBACd;YACF;YAEA,IAAI,uBAAuB;gBACzB,KAAK,QAAQ,CAAC,mCAAmC;oBAC/C,IAAI,sBAAsB,EAAE;oBAC5B,MAAM,sBAAsB,IAAI;oBAChC,YAAY;gBACd;YACF;QACF;sFAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,2IAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,6LAAC;oBACC,WAAU;oBACV,UAAU,KAAK,YAAY,CAAC;;sCAE5B,6LAAC,2IAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,QAAQ;4BACR,OAAO;4BACP,WAAU;4BACV,QAAQ;;;;;;sCAGV,6LAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS;4BACT,GAAG;4BACH,SAAS;4BACT,cAAc;4BACd,mDAAmD;4BACnD,YAAY,GACV,+BAAA,KAAK,SAAS,CAAC,MAAM,CAAC,KAAK,cAA3B,oDAAA,yCAAA,6BAA6B,SAAS,cAAtC,8DAAA,qDAAA,uCAAwC,WAAW,cAAnD,0EAAA,wDAAA,mDAAqD,EAAE,cAAvD,4EAAA,sDAAyD,OAAO;;;;;;wBAGnE,kBAAkB,0BAAY,6LAAC,2MAAoB;;;;;wBACnD,qCACC,6LAAC,uMAAkB;4BACjB,SAAS;4BACT,mBAAmB;;;;;;sCAGvB,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,4LAAS;;;;;sCAEV,6LAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS;4BACT,GAAG;4BACH,SAAS;4BACT,cAAc;4BACd,YAAY,GACV,gCAAA,KAAK,SAAS,CAAC,MAAM,CAAC,KAAK,cAA3B,qDAAA,8CAAA,8BAA6B,cAAc,cAA3C,mEAAA,+DAAA,4CAA6C,gBAAgB,cAA7D,oFAAA,kEAAA,6DACI,EAAE,cADN,sFAAA,gEACQ,OAAO;;;;;;wBAGlB,uBAAuB,0BAAY,6LAAC,2MAAoB;;;;;wBACxD,0CACC,6LAAC,uMAAkB;4BACjB,SAAS;4BACT,cAAc,EACZ,mBAAA,8BAAA,iBAAA,OAAQ,MAAM,cAAd,sCAAA,oCAAA,eAAgB,kBAAkB,cAAlC,wDAAA,kCAAoC,cAAc;4BAEpD,mBAAmB;;;;;;sCAGvB,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,6LAAC,4LAAS;;;;;sCAEV,6LAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS;4BACT,GAAG;4BACH,SAAS;4BACT,cAAc;4BACd,YAAY,GACV,gCAAA,KAAK,SAAS,CAAC,MAAM,CAAC,KAAK,cAA3B,qDAAA,2CAAA,8BAA6B,WAAW,cAAxC,gEAAA,yDAAA,yCAA0C,aAAa,cAAvD,8EAAA,4DAAA,uDAAyD,EAAE,cAA3D,gFAAA,0DACI,OAAO;;;;;;wBAGd,oBAAoB,0BAAY,6LAAC,2MAAoB;;;;;wBACrD,uCACC,6LAAC,uMAAkB;4BACjB,SAAS;4BACT,mBAAmB;;;;;;sCAGvB,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,6LAAC,+IAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU;;gCAET,2BAAa,6LAAC,uOAAW;oCAAC,WAAU;;;;;;gCACpC,QAAQ;;;;;;;;;;;;;;;;;;YAId,mDACC,6LAAC,oWAAuD;gBACtD,mCACE;;;;;;;;;;;;AAMZ;GA7VW;;QAEC,4NAAe;QAGT,4NAAe;QAGf,4NAAe;QAgB7B,4KAAO;QAyDqB,6LAAoB;QAmB9C,iMAAqB;;;KApGhB", "debugId": null}}, {"offset": {"line": 1561, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  checkResult: boolean\r\n}\r\n\r\nexport function MasonryAntiOverturningPreInterventionCalculationResultCard({\r\n  checkResult,\r\n}: Props) {\r\n  const t = useTranslations(\r\n    'forms.project-params.anti-overturning.pre-intervention',\r\n  )\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('infillOverturningCheck.title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <div>\r\n          <span className=\"font-medium\">{t('checkResult.label')}:</span>{' '}\r\n          <Badge\r\n            className={cn(\r\n              'text-base px-3 py-1',\r\n              checkResult ? 'bg-green-600' : 'bg-red-600',\r\n            )}\r\n          >\r\n            {checkResult\r\n              ? t('checkResult.satisfied')\r\n              : t('checkResult.notSatisfied')}\r\n          </Badge>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AACA;;;;;;;AAMO,SAAS,2DAA2D,KAEnE;QAFmE,EACzE,WAAW,EACL,GAFmE;;IAGzE,MAAM,IAAI,IAAA,4NAAe,EACvB;IAGF,qBACE,6LAAC,2IAAI;;0BACH,6LAAC,iJAAU;0BACT,cAAA,6LAAC,gJAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,6LAAC,kJAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;;sCACC,6LAAC;4BAAK,WAAU;;gCAAe,EAAE;gCAAqB;;;;;;;wBAAS;sCAC/D,6LAAC,6IAAK;4BACJ,WAAW,IAAA,4HAAE,EACX,uBACA,cAAc,iBAAiB;sCAGhC,cACG,EAAE,2BACF,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAMlB;GA7BgB;;QAGJ,4NAAe;;;KAHX", "debugId": null}}, {"offset": {"line": 1652, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-pre-intervention-calculation-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { masonryAntiOverturningPreInterventionCalculationResultSchema } from '@atlas/lib/api/modules/schemas/masonry-antioverturning-params'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { MasonryAntiOverturningPreInterventionCalculationResultSchemaInputs } from '@atlas/types/schemas/masonry/antioverturning-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useForm } from 'react-hook-form'\r\nimport { MasonryAntiOverturningPreInterventionCalculationResultCard } from './anti-overturning-pre-intervention-calculation-result-card'\r\n\r\ntype Props = {\r\n  preInterventionCalculationResult?: MasonryAntiOverturningPreInterventionCalculationResultSchemaInputs\r\n  onSave: () => void\r\n}\r\n\r\nexport const MasonryAntiOverturningPreInterventionCalculationResultsForm = ({\r\n  preInterventionCalculationResult,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.project-params.anti-overturning.pre-intervention',\r\n  )\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  const isPreInterventionCalculated = !!preInterventionCalculationResult\r\n\r\n  const form =\r\n    useForm<MasonryAntiOverturningPreInterventionCalculationResultSchemaInputs>(\r\n      {\r\n        resolver: zodResolver(\r\n          masonryAntiOverturningPreInterventionCalculationResultSchema,\r\n        ),\r\n        defaultValues: {\r\n          overturningMoment:\r\n            preInterventionCalculationResult?.overturningMoment ?? 0,\r\n          seismicRiskIndicator:\r\n            preInterventionCalculationResult?.seismicRiskIndicator ?? 0,\r\n          seismicAccelerationCorrespondingToRiskIndicator:\r\n            preInterventionCalculationResult?.seismicAccelerationCorrespondingToRiskIndicator ??\r\n            0,\r\n          stabilizingMomentExternalFacing:\r\n            preInterventionCalculationResult?.stabilizingMomentExternalFacing ??\r\n            0,\r\n          check: preInterventionCalculationResult?.check ?? false,\r\n        },\r\n      },\r\n    )\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className={`space-y-4 rounded-md border p-4 ${!isPreInterventionCalculated ? 'border-red-500' : ''}`}\r\n        onSubmit={form.handleSubmit(onSave)}\r\n      >\r\n        {!isPreInterventionCalculated && (\r\n          <div className=\"mb-4 p-2 rounded bg-red-100 text-red-700 border border-red-300\">\r\n            {t('notCalculated')}\r\n          </div>\r\n        )}\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"overturningMoment\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"stabilizingMomentExternalFacing\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <MasonryAntiOverturningPreInterventionCalculationResultCard\r\n          checkResult={preInterventionCalculationResult?.check || false}\r\n        />\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('seismicRiskIndicator.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {Math.round(\r\n              Number(preInterventionCalculationResult?.seismicRiskIndicator) *\r\n                1000,\r\n            ) / 1000}{' '}\r\n            %\r\n          </span>\r\n          <p\r\n            data-slot=\"form-description\"\r\n            id={'stabilizingMomentExternalFacingId'}\r\n            className={cn('text-muted-foreground text-sm')}\r\n          >\r\n            {t('seismicRiskIndicator.description')}\r\n          </p>\r\n        </div>\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"seismicAccelerationCorrespondingToRiskIndicator\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={!isPreInterventionCalculated}\r\n          onClick={form.handleSubmit(onSave)}\r\n        >\r\n          {tCommon('next')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;AAOO,MAAM,8DAA8D;QAAC,EAC1E,gCAAgC,EAChC,MAAM,EACA;;IACN,MAAM,IAAI,IAAA,4NAAe,EACvB;IAEF,MAAM,UAAU,IAAA,4NAAe,EAAC;IAEhC,MAAM,8BAA8B,CAAC,CAAC;QAU5B,qDAEA,wDAEA,mFAGA,mEAEK;IAjBf,MAAM,OACJ,IAAA,4KAAO,EACL;QACE,UAAU,IAAA,gLAAW,EACnB,kPAA4D;QAE9D,eAAe;YACb,mBACE,CAAA,sDAAA,6CAAA,uDAAA,iCAAkC,iBAAiB,cAAnD,iEAAA,sDAAuD;YACzD,sBACE,CAAA,yDAAA,6CAAA,uDAAA,iCAAkC,oBAAoB,cAAtD,oEAAA,yDAA0D;YAC5D,iDACE,CAAA,oFAAA,6CAAA,uDAAA,iCAAkC,+CAA+C,cAAjF,+FAAA,oFACA;YACF,iCACE,CAAA,oEAAA,6CAAA,uDAAA,iCAAkC,+BAA+B,cAAjE,+EAAA,oEACA;YACF,OAAO,CAAA,0CAAA,6CAAA,uDAAA,iCAAkC,KAAK,cAAvC,qDAAA,0CAA2C;QACpD;IACF;IAGJ,qBACE,6LAAC,2IAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,6LAAC;YACC,WAAW,AAAC,mCAAuF,OAArD,CAAC,8BAA8B,mBAAmB;YAChG,UAAU,KAAK,YAAY,CAAC;;gBAE3B,CAAC,6CACA,6LAAC;oBAAI,WAAU;8BACZ,EAAE;;;;;;8BAGP,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,8WAA0D;oBACzD,aAAa,CAAA,6CAAA,uDAAA,iCAAkC,KAAK,KAAI;;;;;;8BAE1D,6LAAC;;sCACC,6LAAC;4BAAK,WAAU;;gCACb,EAAE;gCAA8B;;;;;;;wBAC3B;sCACR,6LAAC;;gCACE,KAAK,KAAK,CACT,OAAO,6CAAA,uDAAA,iCAAkC,oBAAoB,IAC3D,QACA;gCAAM;gCAAI;;;;;;;sCAGhB,6LAAC;4BACC,aAAU;4BACV,IAAI;4BACJ,WAAW,IAAA,4HAAE,EAAC;sCAEb,EAAE;;;;;;;;;;;;8BAGP,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,+IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU,CAAC;oBACX,SAAS,KAAK,YAAY,CAAC;8BAE1B,QAAQ;;;;;;;;;;;;;;;;;AAKnB;GA/Fa;;QAID,4NAAe;QAGT,4NAAe;QAK7B,4KAAO;;;KAZE", "debugId": null}}, {"offset": {"line": 1826, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/calculations/anti-overturning-seismic-demand-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { masonryAntiOverturningSeismicDemandCalculationResultSchema } from '@atlas/lib/api/modules/schemas/masonry-antioverturning-params'\r\nimport type { MasonryAntiOverturningSeismicDemandCalculationResultSchemaInputs } from '@atlas/types/schemas/masonry/antioverturning-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useForm } from 'react-hook-form'\r\n\r\ntype Props = {\r\n  seismicDemandCalculationResult?: MasonryAntiOverturningSeismicDemandCalculationResultSchemaInputs\r\n  onSave: () => void\r\n}\r\n\r\nexport const MasonryAntiOverturningSeismicDemandCalculationResultsForm = ({\r\n  seismicDemandCalculationResult,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.project-params.anti-overturning.seismic-demand',\r\n  )\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  const isSeismicDemandCalculated = !!seismicDemandCalculationResult\r\n\r\n  const form =\r\n    useForm<MasonryAntiOverturningSeismicDemandCalculationResultSchemaInputs>({\r\n      resolver: zodResolver(\r\n        masonryAntiOverturningSeismicDemandCalculationResultSchema,\r\n      ),\r\n      defaultValues: {\r\n        overturningSeismicForce:\r\n          seismicDemandCalculationResult?.overturningSeismicForce ?? 0,\r\n        masonryAndPlasterWeight:\r\n          seismicDemandCalculationResult?.masonryAndPlasterWeight ?? 0,\r\n        maximumAcceleration:\r\n          seismicDemandCalculationResult?.maximumAcceleration ?? 0,\r\n      },\r\n    })\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className={`space-y-4 rounded-md border p-4 ${!isSeismicDemandCalculated ? 'border-red-500' : ''}`}\r\n        onSubmit={form.handleSubmit(onSave)}\r\n      >\r\n        {!isSeismicDemandCalculated && (\r\n          <div className=\"mb-4 p-2 rounded bg-red-100 text-red-700 border border-red-300\">\r\n            {t('notCalculated')}\r\n          </div>\r\n        )}\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"maximumAcceleration\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"masonryAndPlasterWeight\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"overturningSeismicForce\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={!isSeismicDemandCalculated}\r\n          onClick={form.handleSubmit(onSave)}\r\n        >\r\n          {tCommon('next')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;;;;;;;;;;AAOO,MAAM,4DAA4D;QAAC,EACxE,8BAA8B,EAC9B,MAAM,EACA;;IACN,MAAM,IAAI,IAAA,4NAAe,EACvB;IAEF,MAAM,UAAU,IAAA,4NAAe,EAAC;IAEhC,MAAM,4BAA4B,CAAC,CAAC;QAS5B,yDAEA,yDAEA;IAXR,MAAM,OACJ,IAAA,4KAAO,EAAmE;QACxE,UAAU,IAAA,gLAAW,EACnB,gPAA0D;QAE5D,eAAe;YACb,yBACE,CAAA,0DAAA,2CAAA,qDAAA,+BAAgC,uBAAuB,cAAvD,qEAAA,0DAA2D;YAC7D,yBACE,CAAA,0DAAA,2CAAA,qDAAA,+BAAgC,uBAAuB,cAAvD,qEAAA,0DAA2D;YAC7D,qBACE,CAAA,sDAAA,2CAAA,qDAAA,+BAAgC,mBAAmB,cAAnD,iEAAA,sDAAuD;QAC3D;IACF;IAEF,qBACE,6LAAC,2IAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,6LAAC;YACC,WAAW,AAAC,mCAAqF,OAAnD,CAAC,4BAA4B,mBAAmB;YAC9F,UAAU,KAAK,YAAY,CAAC;;gBAE3B,CAAC,2CACA,6LAAC;oBAAI,WAAU;8BACZ,EAAE;;;;;;8BAGP,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,+IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU,CAAC;oBACX,SAAS,KAAK,YAAY,CAAC;8BAE1B,QAAQ;;;;;;;;;;;;;;;;;AAKnB;GAlEa;;QAID,4NAAe;QAGT,4NAAe;QAK7B,4KAAO;;;KAZE", "debugId": null}}, {"offset": {"line": 1946, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-building-characteristics-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { BUILDING_TYPE, MODULE_BUILDING_TYPE } from '@atlas/constants/module'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type MasonryAntiOverturningBuildingCharacteristicsSchemaInputs,\r\n  type MasonryAntiOverturningParamsFormSchemaInputs,\r\n  masonryAntiOverturningBuildingCharacteristicsSchema,\r\n} from '@atlas/types/schemas/masonry/antioverturning-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<MasonryAntiOverturningBuildingCharacteristicsSchemaInputs>\r\n  params: MasonryAntiOverturningParamsFormSchemaInputs\r\n  setParams: (newParams: any) => void\r\n  onSave: () => void\r\n}\r\n\r\nexport const MasonryAntiOverturningBuildingCharacteristicsForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  params,\r\n  setParams,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.project-params.anti-overturning.building-characteristics',\r\n  )\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const buildingFundamentalPeriodCalculation = (\r\n    buildingType: BUILDING_TYPE,\r\n    totalBuildingHeight: number,\r\n  ) => {\r\n    return buildingType === BUILDING_TYPE.REINFORCED_CONCRETE\r\n      ? 0.075 * totalBuildingHeight ** 0.75\r\n      : 0.05 * totalBuildingHeight ** 0.75\r\n  }\r\n\r\n  const defaultBuildingFundamentalPeriod = buildingFundamentalPeriodCalculation(\r\n    defaultValues?.buildingType ?? BUILDING_TYPE.MASONRY,\r\n    defaultValues?.totalBuildingHeight ?? 1,\r\n  )\r\n\r\n  const form =\r\n    useForm<MasonryAntiOverturningBuildingCharacteristicsSchemaInputs>({\r\n      resolver: zodResolver(\r\n        masonryAntiOverturningBuildingCharacteristicsSchema,\r\n      ),\r\n      defaultValues: {\r\n        buildingType:\r\n          defaultValues?.buildingType ?? BUILDING_TYPE.REINFORCED_CONCRETE,\r\n        totalBuildingHeight: defaultValues?.totalBuildingHeight ?? 1,\r\n        buildingFundamentalPeriod: defaultBuildingFundamentalPeriod,\r\n        parameterA: defaultBuildingFundamentalPeriod < 0.5 ? 0.8 : 0.3,\r\n        parameterB:\r\n          defaultBuildingFundamentalPeriod < 0.5\r\n            ? 1.4\r\n            : defaultBuildingFundamentalPeriod > 1\r\n              ? 1\r\n              : 1.2,\r\n        parameterAp:\r\n          defaultBuildingFundamentalPeriod < 0.5\r\n            ? 5\r\n            : defaultBuildingFundamentalPeriod > 1\r\n              ? 2.5\r\n              : 4,\r\n      },\r\n    })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: MasonryAntiOverturningBuildingCharacteristicsSchemaInputs) => {\r\n      // construct body based on global schema:\r\n      const masonryAntiOverturningParams: MasonryAntiOverturningParamsFormSchemaInputs =\r\n        {\r\n          ...params,\r\n          buildingCharacteristics: body,\r\n        }\r\n      mutate({ projectId, moduleId, body: masonryAntiOverturningParams })\r\n    },\r\n    [mutate, projectId, moduleId, params],\r\n  )\r\n\r\n  const buildingType = form.watch('buildingType')\r\n  const totalBuildingHeight = form.watch('totalBuildingHeight')\r\n\r\n  useEffect(() => {\r\n    const buildingFundamentalPeriod =\r\n      buildingType === BUILDING_TYPE.REINFORCED_CONCRETE\r\n        ? 0.075 * totalBuildingHeight ** 0.75\r\n        : 0.05 * totalBuildingHeight ** 0.75\r\n\r\n    form.setValue('parameterA', buildingFundamentalPeriod < 0.5 ? 0.8 : 0.3)\r\n    form.setValue(\r\n      'parameterB',\r\n      buildingFundamentalPeriod < 0.5\r\n        ? 1.4\r\n        : buildingFundamentalPeriod > 1\r\n          ? 1\r\n          : 1.2,\r\n    )\r\n    form.setValue(\r\n      'parameterAp',\r\n      buildingFundamentalPeriod < 0.5\r\n        ? 5\r\n        : buildingFundamentalPeriod > 1\r\n          ? 2.5\r\n          : 4,\r\n    )\r\n    form.setValue('buildingFundamentalPeriod', buildingFundamentalPeriod)\r\n  }, [buildingType, totalBuildingHeight, form])\r\n\r\n  useEffect(() => {\r\n    const subscription = form.watch(values => {\r\n      setParams((prev: any) => ({\r\n        ...prev,\r\n        buildingCharacteristics: values,\r\n      }))\r\n    })\r\n    return () => subscription.unsubscribe()\r\n  }, [form, setParams])\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"buildingType\"\r\n          options={MODULE_BUILDING_TYPE}\r\n          optionLabelFn={p => t(`building.${p}`)}\r\n          t={t}\r\n          disabled\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"totalBuildingHeight\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"buildingFundamentalPeriod\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"parameterA\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"parameterB\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"parameterAp\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAGA;AACA;AAKA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AAYO,MAAM,oDAAoD;QAAC,EAChE,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACN,SAAS,EACT,MAAM,EACA;;IACN,MAAM,IAAI,IAAA,4NAAe,EACvB;IAEF,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,cAAc,IAAA,2MAAc;IAElC,MAAM,uCAAuC,CAC3C,cACA;QAEA,OAAO,iBAAiB,+IAAa,CAAC,mBAAmB,GACrD,QAAQ,uBAAuB,OAC/B,OAAO,uBAAuB;IACpC;QAGE,6BACA;IAFF,MAAM,mCAAmC,qCACvC,CAAA,8BAAA,0BAAA,oCAAA,cAAe,YAAY,cAA3B,yCAAA,8BAA+B,+IAAa,CAAC,OAAO,EACpD,CAAA,qCAAA,0BAAA,oCAAA,cAAe,mBAAmB,cAAlC,gDAAA,qCAAsC;QAUhC,8BACmB;IAR3B,MAAM,OACJ,IAAA,4KAAO,EAA4D;QACjE,UAAU,IAAA,gLAAW,EACnB,uNAAmD;QAErD,eAAe;YACb,cACE,CAAA,+BAAA,0BAAA,oCAAA,cAAe,YAAY,cAA3B,0CAAA,+BAA+B,+IAAa,CAAC,mBAAmB;YAClE,qBAAqB,CAAA,sCAAA,0BAAA,oCAAA,cAAe,mBAAmB,cAAlC,iDAAA,sCAAsC;YAC3D,2BAA2B;YAC3B,YAAY,mCAAmC,MAAM,MAAM;YAC3D,YACE,mCAAmC,MAC/B,MACA,mCAAmC,IACjC,IACA;YACR,aACE,mCAAmC,MAC/B,IACA,mCAAmC,IACjC,MACA;QACV;IACF;IAEF,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,mNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,SAAS;6FAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;gBACtB,MAAM,YAAY,iBAAiB,CAAC;oBAClC,UAAU;wBAAC;wBAAW;4BAAE;4BAAW;wBAAS;qBAAE;gBAChD;gBACA;YACF;;QACA,OAAO;6FAAE,CAAA;gBACP,oJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAC7D;;IACF;IAGF,MAAM,mBAAmB,IAAA,4KAAW;2FAClC,CAAC;YACC,yCAAyC;YACzC,MAAM,+BACJ;gBACE,GAAG,MAAM;gBACT,yBAAyB;YAC3B;YACF,OAAO;gBAAE;gBAAW;gBAAU,MAAM;YAA6B;QACnE;0FACA;QAAC;QAAQ;QAAW;QAAU;KAAO;IAGvC,MAAM,eAAe,KAAK,KAAK,CAAC;IAChC,MAAM,sBAAsB,KAAK,KAAK,CAAC;IAEvC,IAAA,0KAAS;uEAAC;YACR,MAAM,4BACJ,iBAAiB,+IAAa,CAAC,mBAAmB,GAC9C,QAAQ,uBAAuB,OAC/B,OAAO,uBAAuB;YAEpC,KAAK,QAAQ,CAAC,cAAc,4BAA4B,MAAM,MAAM;YACpE,KAAK,QAAQ,CACX,cACA,4BAA4B,MACxB,MACA,4BAA4B,IAC1B,IACA;YAER,KAAK,QAAQ,CACX,eACA,4BAA4B,MACxB,IACA,4BAA4B,IAC1B,MACA;YAER,KAAK,QAAQ,CAAC,6BAA6B;QAC7C;sEAAG;QAAC;QAAc;QAAqB;KAAK;IAE5C,IAAA,0KAAS;uEAAC;YACR,MAAM,eAAe,KAAK,KAAK;4FAAC,CAAA;oBAC9B;oGAAU,CAAC,OAAc,CAAC;gCACxB,GAAG,IAAI;gCACP,yBAAyB;4BAC3B,CAAC;;gBACH;;YACA;+EAAO,IAAM,aAAa,WAAW;;QACvC;sEAAG;QAAC;QAAM;KAAU;IAEpB,qBACE,6LAAC,2IAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,6LAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,sJAAoB;oBAC7B,eAAe,CAAA,IAAK,EAAE,AAAC,YAAa,OAAF;oBAClC,GAAG;oBACH,QAAQ;;;;;;8BAEV,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,+IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,6LAAC,uOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB;GAnLa;;QASD,4NAAe;QAGT,4NAAe;QACf,4NAAe;QACX,2MAAc;QAiBhC,4KAAO;QAyBqB,mNAA2B;;;KAxD9C", "debugId": null}}, {"offset": {"line": 2202, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-infill-geometry-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { PANEL_WIDTH_DEFAULT } from '@atlas/constants/module'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type MasonryAntiOverturningInfillGeometrySchemaInputs,\r\n  type MasonryAntiOverturningParamsFormSchemaInputs,\r\n  masonryAntiOverturningInfillGeometrySchema,\r\n} from '@atlas/types/schemas/masonry/antioverturning-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<MasonryAntiOverturningInfillGeometrySchemaInputs>\r\n  params: MasonryAntiOverturningParamsFormSchemaInputs\r\n  onSave: () => void\r\n}\r\n\r\nexport const MasonryAntiOverturningInfillGeometryForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  params,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.project-params.anti-overturning.infill-geometry',\r\n  )\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const form = useForm<MasonryAntiOverturningInfillGeometrySchemaInputs>({\r\n    resolver: zodResolver(masonryAntiOverturningInfillGeometrySchema),\r\n    defaultValues: {\r\n      panelWidth: PANEL_WIDTH_DEFAULT,\r\n      externalFacingThickness: defaultValues?.externalFacingThickness ?? 0,\r\n      internalFacingThickness: defaultValues?.internalFacingThickness ?? 0,\r\n      singleSidePlasterThickness:\r\n        defaultValues?.singleSidePlasterThickness ?? 0,\r\n      netPanelHeight: defaultValues?.netPanelHeight ?? 0,\r\n      panelHeightFromGroundLevel:\r\n        defaultValues?.panelHeightFromGroundLevel ?? 0,\r\n      panelCentroidFromGroundLevel:\r\n        defaultValues?.panelCentroidFromGroundLevel ?? 0,\r\n      fundamentalPeriodPanel: defaultValues?.fundamentalPeriodPanel ?? 0,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const totalBuildingHeight =\r\n    params?.buildingCharacteristics?.totalBuildingHeight ?? 1\r\n  const masonrySpecificWeightExternalFacing =\r\n    params?.materialProperties?.masonrySpecificWeightExternalFacing ?? 0\r\n  const masonrySpecificWeightInternalFacing =\r\n    params?.materialProperties?.masonrySpecificWeightInternalFacing ?? 0\r\n  const plasterSpecificWeight =\r\n    params?.materialProperties?.plasterSpecificWeight ?? 0\r\n  const infillWallElasticModulus =\r\n    params?.materialProperties?.infillWallElasticModulus ?? 0\r\n  const netPanelHeight = form.watch('netPanelHeight')\r\n  const panelWidth = form.watch('panelWidth') ?? PANEL_WIDTH_DEFAULT\r\n  const internalFacingThickness = form.watch('internalFacingThickness')\r\n  const externalFacingThickness = form.watch('externalFacingThickness')\r\n  const singleSidePlasterThickness = form.watch('singleSidePlasterThickness')\r\n\r\n  useEffect(() => {\r\n    const panelHeightFromGroundLevel = totalBuildingHeight * 1000\r\n    form.setValue('panelHeightFromGroundLevel', panelHeightFromGroundLevel)\r\n    form.setValue(\r\n      'panelCentroidFromGroundLevel',\r\n      panelHeightFromGroundLevel - netPanelHeight / 2,\r\n    )\r\n\r\n    // fundamental panel period calculation:\r\n    // D44 is netPanelHeight\r\n    // D40 is panelWidth\r\n    // D42 is internalFacingThickness\r\n    // D36 is materialProperties.masonrySpecificWeightInternalFacing\r\n    // D41 is externalFacingThickness\r\n    // D35 is materialProperties.masonrySpecificWeightExternalFacing\r\n    // D43 is singleSidePlasterThickness\r\n    // D37 is materialProperties.plasterSpecificWeight\r\n    // D33 is materialProperties.infillWallElasticModulus\r\n    // FORMULA : =(2*3.14*D44^2/3.5156)*SQRT((D40*((D42*D36)+(D41*D35)+(2*D43*D37))*1000/1000000000)/(D33*9.81*1000*D40*(D42+D41+2*D43)^3/12))\r\n    const subInnerPartOne =\r\n      (panelWidth *\r\n        (internalFacingThickness * masonrySpecificWeightInternalFacing +\r\n          externalFacingThickness * masonrySpecificWeightExternalFacing +\r\n          2 * singleSidePlasterThickness * plasterSpecificWeight) *\r\n        1000) /\r\n      1000000000\r\n    const subInnerPartTwo =\r\n      (infillWallElasticModulus *\r\n        9.81 *\r\n        1000 *\r\n        panelWidth *\r\n        (internalFacingThickness +\r\n          externalFacingThickness +\r\n          2 * singleSidePlasterThickness) **\r\n          3) /\r\n      12\r\n\r\n    const secondPart = Math.sqrt(subInnerPartOne / subInnerPartTwo)\r\n    const fundamentalPeriodPanel =\r\n      ((2 * 3.14 * netPanelHeight ** 2) / 3.5156) * secondPart\r\n\r\n    form.setValue('fundamentalPeriodPanel', fundamentalPeriodPanel)\r\n  }, [\r\n    totalBuildingHeight,\r\n    netPanelHeight,\r\n    panelWidth,\r\n    internalFacingThickness,\r\n    externalFacingThickness,\r\n    singleSidePlasterThickness,\r\n    infillWallElasticModulus,\r\n    form,\r\n    masonrySpecificWeightExternalFacing,\r\n    masonrySpecificWeightInternalFacing,\r\n    plasterSpecificWeight,\r\n  ])\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: MasonryAntiOverturningInfillGeometrySchemaInputs) => {\r\n      // construct body based on global schema:\r\n      const masonryAntiOverturningParams: MasonryAntiOverturningParamsFormSchemaInputs =\r\n        {\r\n          ...params,\r\n          infillGeometry: body,\r\n        }\r\n      mutate({ projectId, moduleId, body: masonryAntiOverturningParams })\r\n    },\r\n    [mutate, projectId, moduleId, params],\r\n  )\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"panelWidth\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"externalFacingThickness\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"internalFacingThickness\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"singleSidePlasterThickness\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput control={form.control} name=\"netPanelHeight\" t={t} />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"panelHeightFromGroundLevel\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"panelCentroidFromGroundLevel\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"fundamentalPeriodPanel\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAGA;AACA;AAKA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AAWO,MAAM,2CAA2C;QAAC,EACvD,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACN,MAAM,EACA;QA0CJ,iCAEA,4BAEA,6BAEA,6BAEA;;IAjDF,MAAM,IAAI,IAAA,4NAAe,EACvB;IAEF,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,cAAc,IAAA,2MAAc;QAML,wCACA,wCAEvB,2CACc,+BAEd,2CAEA,6CACsB;IAb5B,MAAM,OAAO,IAAA,4KAAO,EAAmD;QACrE,UAAU,IAAA,gLAAW,EAAC,8MAA0C;QAChE,eAAe;YACb,YAAY,qJAAmB;YAC/B,yBAAyB,CAAA,yCAAA,0BAAA,oCAAA,cAAe,uBAAuB,cAAtC,oDAAA,yCAA0C;YACnE,yBAAyB,CAAA,yCAAA,0BAAA,oCAAA,cAAe,uBAAuB,cAAtC,oDAAA,yCAA0C;YACnE,4BACE,CAAA,4CAAA,0BAAA,oCAAA,cAAe,0BAA0B,cAAzC,uDAAA,4CAA6C;YAC/C,gBAAgB,CAAA,gCAAA,0BAAA,oCAAA,cAAe,cAAc,cAA7B,2CAAA,gCAAiC;YACjD,4BACE,CAAA,4CAAA,0BAAA,oCAAA,cAAe,0BAA0B,cAAzC,uDAAA,4CAA6C;YAC/C,8BACE,CAAA,8CAAA,0BAAA,oCAAA,cAAe,4BAA4B,cAA3C,yDAAA,8CAA+C;YACjD,wBAAwB,CAAA,wCAAA,0BAAA,oCAAA,cAAe,sBAAsB,cAArC,mDAAA,wCAAyC;QACnE;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,mNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,SAAS;oFAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;gBACtB,MAAM,YAAY,iBAAiB,CAAC;oBAClC,UAAU;wBAAC;wBAAW;4BAAE;4BAAW;wBAAS;qBAAE;gBAChD;gBACA;YACF;;QACA,OAAO;oFAAE,CAAA;gBACP,oJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAC7D;;IACF;QAIA;IADF,MAAM,sBACJ,CAAA,sDAAA,mBAAA,8BAAA,kCAAA,OAAQ,uBAAuB,cAA/B,sDAAA,gCAAiC,mBAAmB,cAApD,iEAAA,sDAAwD;QAExD;IADF,MAAM,sCACJ,CAAA,iEAAA,mBAAA,8BAAA,6BAAA,OAAQ,kBAAkB,cAA1B,iDAAA,2BAA4B,mCAAmC,cAA/D,4EAAA,iEAAmE;QAEnE;IADF,MAAM,sCACJ,CAAA,iEAAA,mBAAA,8BAAA,8BAAA,OAAQ,kBAAkB,cAA1B,kDAAA,4BAA4B,mCAAmC,cAA/D,4EAAA,iEAAmE;QAEnE;IADF,MAAM,wBACJ,CAAA,mDAAA,mBAAA,8BAAA,8BAAA,OAAQ,kBAAkB,cAA1B,kDAAA,4BAA4B,qBAAqB,cAAjD,8DAAA,mDAAqD;QAErD;IADF,MAAM,2BACJ,CAAA,sDAAA,mBAAA,8BAAA,8BAAA,OAAQ,kBAAkB,cAA1B,kDAAA,4BAA4B,wBAAwB,cAApD,iEAAA,sDAAwD;IAC1D,MAAM,iBAAiB,KAAK,KAAK,CAAC;QACf;IAAnB,MAAM,aAAa,CAAA,cAAA,KAAK,KAAK,CAAC,2BAAX,yBAAA,cAA4B,qJAAmB;IAClE,MAAM,0BAA0B,KAAK,KAAK,CAAC;IAC3C,MAAM,0BAA0B,KAAK,KAAK,CAAC;IAC3C,MAAM,6BAA6B,KAAK,KAAK,CAAC;IAE9C,IAAA,0KAAS;8DAAC;YACR,MAAM,6BAA6B,sBAAsB;YACzD,KAAK,QAAQ,CAAC,8BAA8B;YAC5C,KAAK,QAAQ,CACX,gCACA,6BAA6B,iBAAiB;YAGhD,wCAAwC;YACxC,wBAAwB;YACxB,oBAAoB;YACpB,iCAAiC;YACjC,gEAAgE;YAChE,iCAAiC;YACjC,gEAAgE;YAChE,oCAAoC;YACpC,kDAAkD;YAClD,qDAAqD;YACrD,0IAA0I;YAC1I,MAAM,kBACJ,AAAC,aACC,CAAC,0BAA0B,sCACzB,0BAA0B,sCAC1B,IAAI,6BAA6B,qBAAqB,IACxD,OACF;YACF,MAAM,kBACJ,AAAC,2BACC,OACA,OACA,aACA,CAAC,0BACC,0BACA,IAAI,0BAA0B,KAC9B,IACJ;YAEF,MAAM,aAAa,KAAK,IAAI,CAAC,kBAAkB;YAC/C,MAAM,yBACJ,AAAE,IAAI,OAAO,kBAAkB,IAAK,SAAU;YAEhD,KAAK,QAAQ,CAAC,0BAA0B;QAC1C;6DAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,mBAAmB,IAAA,4KAAW;kFAClC,CAAC;YACC,yCAAyC;YACzC,MAAM,+BACJ;gBACE,GAAG,MAAM;gBACT,gBAAgB;YAClB;YACF,OAAO;gBAAE;gBAAW;gBAAU,MAAM;YAA6B;QACnE;iFACA;QAAC;QAAQ;QAAW;QAAU;KAAO;IAGvC,qBACE,6LAAC,2IAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,6LAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAiB,GAAG;;;;;;8BACjE,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,+IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,6LAAC,uOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB;GA/La;;QAQD,4NAAe;QAGT,4NAAe;QACf,4NAAe;QACX,2MAAc;QAErB,4KAAO;QAiBU,mNAA2B;;;KAhC9C", "debugId": null}}, {"offset": {"line": 2485, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-material-properties-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport {\r\n  executionClass,\r\n  FACING_MATERIAL,\r\n  INFILL_WALL_TOPOLOGY,\r\n  loadResistingCategory,\r\n  MODULE_EXECUTION_CLASS,\r\n  MODULE_FACING_MATERIAL,\r\n  MODULE_INFILL_WALL_TOPOLOGY,\r\n  MODULE_LOAD_RESISTING_CATEGORY,\r\n  MODULE_MATERIAL_KNOWLEDGE_LEVEL,\r\n  masonryStrengthSafetyFactorMapping,\r\n  moduleMaterialKnowledgeLevel,\r\n  moduleMaterialKnowledgeLevelValues,\r\n} from '@atlas/constants/module'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type MasonryAntiOverturningMaterialPropertiesSchemaInputs,\r\n  type MasonryAntiOverturningParamsFormSchemaInputs,\r\n  masonryAntiOverturningMaterialPropertiesSchema,\r\n} from '@atlas/types/schemas/masonry/antioverturning-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<MasonryAntiOverturningMaterialPropertiesSchemaInputs>\r\n  params: MasonryAntiOverturningParamsFormSchemaInputs\r\n  setParams: (newParams: any) => void\r\n  onSave: () => void\r\n}\r\n\r\nexport const MasonryAntiOverturningMaterialPropertiesForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  params,\r\n  setParams,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.project-params.anti-overturning.material-properties',\r\n  )\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const form = useForm<MasonryAntiOverturningMaterialPropertiesSchemaInputs>({\r\n    resolver: zodResolver(masonryAntiOverturningMaterialPropertiesSchema),\r\n    defaultValues: {\r\n      infillWallTypology:\r\n        defaultValues?.infillWallTypology ?? INFILL_WALL_TOPOLOGY.SINGLE,\r\n      facingMaterial: defaultValues?.facingMaterial ?? FACING_MATERIAL.BRICK,\r\n      knowledgeLevel:\r\n        defaultValues?.knowledgeLevel ?? moduleMaterialKnowledgeLevel.LC1,\r\n      confidenceFactor:\r\n        defaultValues?.confidenceFactor ??\r\n        moduleMaterialKnowledgeLevelValues.LC1,\r\n      executionClass: defaultValues?.executionClass ?? executionClass.ONE,\r\n      loadResistingCategory:\r\n        defaultValues?.loadResistingCategory ??\r\n        loadResistingCategory.MASONRY_CAT_I_AND_MORTAR_WITH_GUARANTEED_PERFORMANCE,\r\n      masonryStrengthSafetyFactor:\r\n        defaultValues?.masonryStrengthSafetyFactor ?? 0,\r\n      characteristicCompressiveStrength:\r\n        defaultValues?.characteristicCompressiveStrength ?? 0,\r\n      designCompressiveStrength: defaultValues?.designCompressiveStrength ?? 0,\r\n      infillWallElasticModulus: defaultValues?.infillWallElasticModulus ?? 0,\r\n      ultimateMasonryStrain: defaultValues?.ultimateMasonryStrain ?? 0.0035,\r\n      masonrySpecificWeightExternalFacing:\r\n        defaultValues?.masonrySpecificWeightExternalFacing ?? 0,\r\n      masonrySpecificWeightInternalFacing:\r\n        defaultValues?.infillWallTypology === INFILL_WALL_TOPOLOGY.SINGLE\r\n          ? 0\r\n          : (defaultValues?.masonrySpecificWeightInternalFacing ?? 0),\r\n      plasterSpecificWeight: defaultValues?.plasterSpecificWeight ?? 0,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const infillWallTypology = form.watch('infillWallTypology')\r\n  const knowledgeMaterialLevel = form.watch('knowledgeLevel')\r\n  const executionClassFormValue = form.watch('executionClass')\r\n  const loadResistingCategoryFormValue = form.watch('loadResistingCategory')\r\n  const characteristicCompressiveStrength = form.watch(\r\n    'characteristicCompressiveStrength',\r\n  )\r\n\r\n  useEffect(() => {\r\n    const confidenceFactor =\r\n      moduleMaterialKnowledgeLevelValues[knowledgeMaterialLevel]\r\n    form.setValue('confidenceFactor', confidenceFactor)\r\n    const masonryStrengthSafetyFactor =\r\n      masonryStrengthSafetyFactorMapping[loadResistingCategoryFormValue][\r\n        executionClassFormValue\r\n      ]\r\n    form.setValue('masonryStrengthSafetyFactor', masonryStrengthSafetyFactor)\r\n\r\n    form.setValue(\r\n      'designCompressiveStrength',\r\n      characteristicCompressiveStrength /\r\n        masonryStrengthSafetyFactor /\r\n        confidenceFactor,\r\n    )\r\n\r\n    if (infillWallTypology === INFILL_WALL_TOPOLOGY.SINGLE) {\r\n      form.setValue('masonrySpecificWeightInternalFacing', 0)\r\n    }\r\n  }, [\r\n    knowledgeMaterialLevel,\r\n    executionClassFormValue,\r\n    loadResistingCategoryFormValue,\r\n    infillWallTypology,\r\n    characteristicCompressiveStrength,\r\n    form,\r\n  ])\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: MasonryAntiOverturningMaterialPropertiesSchemaInputs) => {\r\n      // construct body based on global schema:\r\n      const masonryAntiOverturningParams: MasonryAntiOverturningParamsFormSchemaInputs =\r\n        {\r\n          ...params,\r\n          materialProperties: body,\r\n        }\r\n      mutate({ projectId, moduleId, body: masonryAntiOverturningParams })\r\n    },\r\n    [mutate, projectId, moduleId, params],\r\n  )\r\n\r\n  useEffect(() => {\r\n    const subscription = form.watch(values => {\r\n      setParams((prev: any) => ({\r\n        ...prev,\r\n        materialProperties: values,\r\n      }))\r\n    })\r\n    return () => subscription.unsubscribe()\r\n  }, [form, setParams])\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"infillWallTypology\"\r\n          options={MODULE_INFILL_WALL_TOPOLOGY}\r\n          optionLabelFn={p => t(`infillWall.${p}`)}\r\n          t={t}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"facingMaterial\"\r\n          options={MODULE_FACING_MATERIAL}\r\n          t={msg => t(`${infillWallTypology}.${msg}`)}\r\n          optionLabelFn={p => t(`facingMaterial.${p}`)}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"knowledgeLevel\"\r\n          options={MODULE_MATERIAL_KNOWLEDGE_LEVEL}\r\n          optionLabelFn={p => t(`materialKnowledge.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"confidenceFactor\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"executionClass\"\r\n          options={MODULE_EXECUTION_CLASS}\r\n          optionLabelFn={p => t(`executionClass.${p}`)}\r\n          t={t}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"loadResistingCategory\"\r\n          options={MODULE_LOAD_RESISTING_CATEGORY}\r\n          optionLabelFn={p => t(`loadResisting.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"masonryStrengthSafetyFactor\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"characteristicCompressiveStrength\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"designCompressiveStrength\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"infillWallElasticModulus\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"ultimateMasonryStrain\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"masonrySpecificWeightExternalFacing\"\r\n          t={msg => t(`${infillWallTypology}.${msg}`)}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"masonrySpecificWeightInternalFacing\"\r\n          t={msg => t(`${infillWallTypology}.${msg}`)}\r\n          disabled={infillWallTypology === INFILL_WALL_TOPOLOGY.SINGLE}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"plasterSpecificWeight\"\r\n          t={t}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAgBA;AACA;AAKA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AAYO,MAAM,+CAA+C;QAAC,EAC3D,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACN,SAAS,EACT,MAAM,EACA;;IACN,MAAM,IAAI,IAAA,4NAAe,EACvB;IAEF,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,cAAc,IAAA,2MAAc;QAM5B,mCACc,+BAEd,+BAEA,iCAEc,+BAEd,sCAGA,4CAEA,kDACyB,0CACD,yCACH,sCAErB,oDAIK,oDACgB;IA5B3B,MAAM,OAAO,IAAA,4KAAO,EAAuD;QACzE,UAAU,IAAA,gLAAW,EAAC,kNAA8C;QACpE,eAAe;YACb,oBACE,CAAA,oCAAA,0BAAA,oCAAA,cAAe,kBAAkB,cAAjC,+CAAA,oCAAqC,sJAAoB,CAAC,MAAM;YAClE,gBAAgB,CAAA,gCAAA,0BAAA,oCAAA,cAAe,cAAc,cAA7B,2CAAA,gCAAiC,iJAAe,CAAC,KAAK;YACtE,gBACE,CAAA,gCAAA,0BAAA,oCAAA,cAAe,cAAc,cAA7B,2CAAA,gCAAiC,8JAA4B,CAAC,GAAG;YACnE,kBACE,CAAA,kCAAA,0BAAA,oCAAA,cAAe,gBAAgB,cAA/B,6CAAA,kCACA,oKAAkC,CAAC,GAAG;YACxC,gBAAgB,CAAA,gCAAA,0BAAA,oCAAA,cAAe,cAAc,cAA7B,2CAAA,gCAAiC,gJAAc,CAAC,GAAG;YACnE,uBACE,CAAA,uCAAA,0BAAA,oCAAA,cAAe,qBAAqB,cAApC,kDAAA,uCACA,uJAAqB,CAAC,oDAAoD;YAC5E,6BACE,CAAA,6CAAA,0BAAA,oCAAA,cAAe,2BAA2B,cAA1C,wDAAA,6CAA8C;YAChD,mCACE,CAAA,mDAAA,0BAAA,oCAAA,cAAe,iCAAiC,cAAhD,8DAAA,mDAAoD;YACtD,2BAA2B,CAAA,2CAAA,0BAAA,oCAAA,cAAe,yBAAyB,cAAxC,sDAAA,2CAA4C;YACvE,0BAA0B,CAAA,0CAAA,0BAAA,oCAAA,cAAe,wBAAwB,cAAvC,qDAAA,0CAA2C;YACrE,uBAAuB,CAAA,uCAAA,0BAAA,oCAAA,cAAe,qBAAqB,cAApC,kDAAA,uCAAwC;YAC/D,qCACE,CAAA,qDAAA,0BAAA,oCAAA,cAAe,mCAAmC,cAAlD,gEAAA,qDAAsD;YACxD,qCACE,CAAA,0BAAA,oCAAA,cAAe,kBAAkB,MAAK,sJAAoB,CAAC,MAAM,GAC7D,IACC,CAAA,qDAAA,0BAAA,oCAAA,cAAe,mCAAmC,cAAlD,gEAAA,qDAAsD;YAC7D,uBAAuB,CAAA,uCAAA,0BAAA,oCAAA,cAAe,qBAAqB,cAApC,kDAAA,uCAAwC;QACjE;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,mNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,SAAS;wFAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;gBACtB,MAAM,YAAY,iBAAiB,CAAC;oBAClC,UAAU;wBAAC;wBAAW;4BAAE;4BAAW;wBAAS;qBAAE;gBAChD;gBACA;YACF;;QACA,OAAO;wFAAE,CAAA;gBACP,oJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAC7D;;IACF;IAGF,MAAM,qBAAqB,KAAK,KAAK,CAAC;IACtC,MAAM,yBAAyB,KAAK,KAAK,CAAC;IAC1C,MAAM,0BAA0B,KAAK,KAAK,CAAC;IAC3C,MAAM,iCAAiC,KAAK,KAAK,CAAC;IAClD,MAAM,oCAAoC,KAAK,KAAK,CAClD;IAGF,IAAA,0KAAS;kEAAC;YACR,MAAM,mBACJ,oKAAkC,CAAC,uBAAuB;YAC5D,KAAK,QAAQ,CAAC,oBAAoB;YAClC,MAAM,8BACJ,oKAAkC,CAAC,+BAA+B,CAChE,wBACD;YACH,KAAK,QAAQ,CAAC,+BAA+B;YAE7C,KAAK,QAAQ,CACX,6BACA,oCACE,8BACA;YAGJ,IAAI,uBAAuB,sJAAoB,CAAC,MAAM,EAAE;gBACtD,KAAK,QAAQ,CAAC,uCAAuC;YACvD;QACF;iEAAG;QACD;QACA;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,mBAAmB,IAAA,4KAAW;sFAClC,CAAC;YACC,yCAAyC;YACzC,MAAM,+BACJ;gBACE,GAAG,MAAM;gBACT,oBAAoB;YACtB;YACF,OAAO;gBAAE;gBAAW;gBAAU,MAAM;YAA6B;QACnE;qFACA;QAAC;QAAQ;QAAW;QAAU;KAAO;IAGvC,IAAA,0KAAS;kEAAC;YACR,MAAM,eAAe,KAAK,KAAK;uFAAC,CAAA;oBAC9B;+FAAU,CAAC,OAAc,CAAC;gCACxB,GAAG,IAAI;gCACP,oBAAoB;4BACtB,CAAC;;gBACH;;YACA;0EAAO,IAAM,aAAa,WAAW;;QACvC;iEAAG;QAAC;QAAM;KAAU;IACpB,qBACE,6LAAC,2IAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,6LAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,6JAA2B;oBACpC,eAAe,CAAA,IAAK,EAAE,AAAC,cAAe,OAAF;oBACpC,GAAG;;;;;;8BAEL,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,wJAAsB;oBAC/B,GAAG,CAAA,MAAO,EAAE,AAAC,GAAwB,OAAtB,oBAAmB,KAAO,OAAJ;oBACrC,eAAe,CAAA,IAAK,EAAE,AAAC,kBAAmB,OAAF;;;;;;8BAE1C,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,iKAA+B;oBACxC,eAAe,CAAA,IAAK,EAAE,AAAC,qBAAsB,OAAF;oBAC3C,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,wJAAsB;oBAC/B,eAAe,CAAA,IAAK,EAAE,AAAC,kBAAmB,OAAF;oBACxC,GAAG;;;;;;8BAEL,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,gKAA8B;oBACvC,eAAe,CAAA,IAAK,EAAE,AAAC,iBAAkB,OAAF;oBACvC,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG,CAAA,MAAO,EAAE,AAAC,GAAwB,OAAtB,oBAAmB,KAAO,OAAJ;;;;;;8BAEvC,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG,CAAA,MAAO,EAAE,AAAC,GAAwB,OAAtB,oBAAmB,KAAO,OAAJ;oBACrC,UAAU,uBAAuB,sJAAoB,CAAC,MAAM;;;;;;8BAE9D,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,6LAAC,+IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,6LAAC,uOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB;GAlOa;;QASD,4NAAe;QAGT,4NAAe;QACf,4NAAe;QACX,2MAAc;QAErB,4KAAO;QAgCU,mNAA2B;;;KAhD9C", "debugId": null}}, {"offset": {"line": 2833, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/sections/anti-overturning-site-characteristics-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport {\r\n  MODULE_SUBSOIL_CATEGORY,\r\n  MODULE_TOPOGRAPHIC_CATEGORY,\r\n  SUBSOIL_CATEGORY,\r\n  ssCoefficientValues,\r\n  TOPOGRAPHIC_CATEGORY,\r\n  topographicCoefficientValues,\r\n} from '@atlas/constants/module'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type MasonryAntiOverturningParamsFormSchemaInputs,\r\n  type MasonryAntiOverturningSiteCharacteristicsSchemaInputs,\r\n  masonryAntiOverturningSiteCharacteristicsSchema,\r\n} from '@atlas/types/schemas/masonry/antioverturning-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  params: MasonryAntiOverturningParamsFormSchemaInputs\r\n  defaultValues?: Partial<MasonryAntiOverturningSiteCharacteristicsSchemaInputs>\r\n  setParams: (newParams: any) => void\r\n  onSave: () => void\r\n}\r\n\r\nexport const MasonryAntiOverturningSiteCharacteristicsForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  params,\r\n  setParams,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.project-params.anti-overturning.site-characteristics',\r\n  )\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const form = useForm<MasonryAntiOverturningSiteCharacteristicsSchemaInputs>({\r\n    resolver: zodResolver(masonryAntiOverturningSiteCharacteristicsSchema),\r\n    defaultValues: {\r\n      seismicAccelerationAtSlv: defaultValues?.seismicAccelerationAtSlv ?? 0,\r\n      amplificationFactorAtSlv: defaultValues?.amplificationFactorAtSlv ?? 0,\r\n      subsoilCategory: defaultValues?.subsoilCategory ?? SUBSOIL_CATEGORY.A,\r\n      // use SubsoilCoefficient from key entry of subsoilCategory\r\n      ssCoefficient:\r\n        ssCoefficientValues[\r\n          defaultValues?.subsoilCategory ?? SUBSOIL_CATEGORY.A\r\n        ],\r\n      topographicCategory:\r\n        defaultValues?.topographicCategory ?? TOPOGRAPHIC_CATEGORY.T1,\r\n      stCoefficient:\r\n        topographicCoefficientValues[\r\n          defaultValues?.topographicCategory ?? TOPOGRAPHIC_CATEGORY.T1\r\n        ],\r\n      subsoilCoefficient:\r\n        (defaultValues?.ssCoefficient ?? 0) *\r\n        (defaultValues?.stCoefficient ?? 0),\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const subsoilCategory = form.watch('subsoilCategory') ?? SUBSOIL_CATEGORY.A\r\n  const topographicCategory =\r\n    form.watch('topographicCategory') ?? TOPOGRAPHIC_CATEGORY.T1\r\n\r\n  useEffect(() => {\r\n    form.setValue('ssCoefficient', ssCoefficientValues[subsoilCategory])\r\n    form.setValue(\r\n      'stCoefficient',\r\n      topographicCoefficientValues[topographicCategory],\r\n    )\r\n    form.setValue(\r\n      'subsoilCoefficient',\r\n      ssCoefficientValues[subsoilCategory] *\r\n        topographicCoefficientValues[topographicCategory],\r\n    )\r\n  }, [subsoilCategory, topographicCategory, form])\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: MasonryAntiOverturningSiteCharacteristicsSchemaInputs) => {\r\n      // construct body based on global schema:\r\n      const masonryAntiOverturningParams: MasonryAntiOverturningParamsFormSchemaInputs =\r\n        {\r\n          ...params,\r\n          siteCharacteristics: body,\r\n        }\r\n      mutate({ projectId, moduleId, body: masonryAntiOverturningParams })\r\n    },\r\n    [mutate, projectId, moduleId, params],\r\n  )\r\n\r\n  useEffect(() => {\r\n    const subscription = form.watch(values => {\r\n      setParams((prev: MasonryAntiOverturningParamsFormSchemaInputs) => ({\r\n        ...prev,\r\n        siteCharacteristics: values,\r\n      }))\r\n    })\r\n    return () => subscription.unsubscribe()\r\n  }, [form, setParams])\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <Image\r\n          src=\"/assets/masonry-anti-overturning/antibaltamento-geometria-armatura.jpg\"\r\n          alt=\"site characteristics\"\r\n          height={250}\r\n          width={500}\r\n          className=\"mx-auto rounded-md object-contain\"\r\n          priority\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"seismicAccelerationAtSlv\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"amplificationFactorAtSlv\"\r\n          t={t}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"subsoilCategory\"\r\n          options={MODULE_SUBSOIL_CATEGORY}\r\n          optionLabelFn={p => t(`subsoil.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"ssCoefficient\"\r\n          t={t}\r\n          disabled={true}\r\n          // value is set automatically based on subsoilCategory\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"topographicCategory\"\r\n          options={MODULE_TOPOGRAPHIC_CATEGORY}\r\n          optionLabelFn={p => t(`topographic.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"stCoefficient\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"subsoilCoefficient\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAUA;AACA;AAKA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;AAYO,MAAM,gDAAgD;QAAC,EAC5D,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACN,SAAS,EACT,MAAM,EACA;;IACN,MAAM,IAAI,IAAA,4NAAe,EACvB;IAEF,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,cAAc,IAAA,2MAAc;QAKJ,yCACA,yCACT,gCAIb,iCAGF,oCAGE,qCAGD,8BACA;IAnBP,MAAM,OAAO,IAAA,4KAAO,EAAwD;QAC1E,UAAU,IAAA,gLAAW,EAAC,mNAA+C;QACrE,eAAe;YACb,0BAA0B,CAAA,0CAAA,0BAAA,oCAAA,cAAe,wBAAwB,cAAvC,qDAAA,0CAA2C;YACrE,0BAA0B,CAAA,0CAAA,0BAAA,oCAAA,cAAe,wBAAwB,cAAvC,qDAAA,0CAA2C;YACrE,iBAAiB,CAAA,iCAAA,0BAAA,oCAAA,cAAe,eAAe,cAA9B,4CAAA,iCAAkC,kJAAgB,CAAC,CAAC;YACrE,2DAA2D;YAC3D,eACE,qJAAmB,CACjB,CAAA,kCAAA,0BAAA,oCAAA,cAAe,eAAe,cAA9B,6CAAA,kCAAkC,kJAAgB,CAAC,CAAC,CACrD;YACH,qBACE,CAAA,qCAAA,0BAAA,oCAAA,cAAe,mBAAmB,cAAlC,gDAAA,qCAAsC,sJAAoB,CAAC,EAAE;YAC/D,eACE,8JAA4B,CAC1B,CAAA,sCAAA,0BAAA,oCAAA,cAAe,mBAAmB,cAAlC,iDAAA,sCAAsC,sJAAoB,CAAC,EAAE,CAC9D;YACH,oBACE,CAAC,CAAA,+BAAA,0BAAA,oCAAA,cAAe,aAAa,cAA5B,0CAAA,+BAAgC,CAAC,IAClC,CAAC,CAAA,+BAAA,0BAAA,oCAAA,cAAe,aAAa,cAA5B,0CAAA,+BAAgC,CAAC;QACtC;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,mNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,SAAS;yFAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;gBACtB,MAAM,YAAY,iBAAiB,CAAC;oBAClC,UAAU;wBAAC;wBAAW;4BAAE;4BAAW;wBAAS;qBAAE;gBAChD;gBACA;YACF;;QACA,OAAO;yFAAE,CAAA;gBACP,oJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAC7D;;IACF;QAGsB;IAAxB,MAAM,kBAAkB,CAAA,cAAA,KAAK,KAAK,CAAC,gCAAX,yBAAA,cAAiC,kJAAgB,CAAC,CAAC;QAEzE;IADF,MAAM,sBACJ,CAAA,eAAA,KAAK,KAAK,CAAC,oCAAX,0BAAA,eAAqC,sJAAoB,CAAC,EAAE;IAE9D,IAAA,0KAAS;mEAAC;YACR,KAAK,QAAQ,CAAC,iBAAiB,qJAAmB,CAAC,gBAAgB;YACnE,KAAK,QAAQ,CACX,iBACA,8JAA4B,CAAC,oBAAoB;YAEnD,KAAK,QAAQ,CACX,sBACA,qJAAmB,CAAC,gBAAgB,GAClC,8JAA4B,CAAC,oBAAoB;QAEvD;kEAAG;QAAC;QAAiB;QAAqB;KAAK;IAE/C,MAAM,mBAAmB,IAAA,4KAAW;uFAClC,CAAC;YACC,yCAAyC;YACzC,MAAM,+BACJ;gBACE,GAAG,MAAM;gBACT,qBAAqB;YACvB;YACF,OAAO;gBAAE;gBAAW;gBAAU,MAAM;YAA6B;QACnE;sFACA;QAAC;QAAQ;QAAW;QAAU;KAAO;IAGvC,IAAA,0KAAS;mEAAC;YACR,MAAM,eAAe,KAAK,KAAK;wFAAC,CAAA;oBAC9B;gGAAU,CAAC,OAAuD,CAAC;gCACjE,GAAG,IAAI;gCACP,qBAAqB;4BACvB,CAAC;;gBACH;;YACA;2EAAO,IAAM,aAAa,WAAW;;QACvC;kEAAG;QAAC;QAAM;KAAU;IACpB,qBACE,6LAAC,2IAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,6LAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,6LAAC,2IAAK;oBACJ,KAAI;oBACJ,KAAI;oBACJ,QAAQ;oBACR,OAAO;oBACP,WAAU;oBACV,QAAQ;;;;;;8BAEV,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,yJAAuB;oBAChC,eAAe,CAAA,IAAK,EAAE,AAAC,WAAY,OAAF;oBACjC,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAGZ,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,6JAA2B;oBACpC,eAAe,CAAA,IAAK,EAAE,AAAC,eAAgB,OAAF;oBACrC,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,+IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,6LAAC,uOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB;GAnKa;;QASD,4NAAe;QAGT,4NAAe;QACf,4NAAe;QACX,2MAAc;QAErB,4KAAO;QAuBU,mNAA2B;;;KAvC9C", "debugId": null}}, {"offset": {"line": 3109, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/anti-overturning/anti-overturning-params-form.tsx"], "sourcesContent": ["import {\r\n  Accordion as AccordionComponent,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from '@atlas/components/ui/accordion'\r\nimport type {\r\n  Module,\r\n  ModuleWithParamsAntiOverturning,\r\n} from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport {\r\n  compositeReinforcementSystemInputSchema,\r\n  type MasonryAntiOverturningParamsFormSchemaInputs,\r\n} from '@atlas/types/schemas/masonry/antioverturning-form'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useState } from 'react'\r\nimport { MasonryAntiOverturningCompositeReinforcementSystemCalculationForm } from './calculations/anti-overturning-composite-reinforcement-system-calculation-form'\r\nimport { MasonryAntiOverturningPreInterventionCalculationResultsForm } from './calculations/anti-overturning-pre-intervention-calculation-form'\r\nimport { MasonryAntiOverturningSeismicDemandCalculationResultsForm } from './calculations/anti-overturning-seismic-demand-form'\r\nimport { MasonryAntiOverturningBuildingCharacteristicsForm } from './sections/anti-overturning-building-characteristics-form'\r\nimport { MasonryAntiOverturningInfillGeometryForm } from './sections/anti-overturning-infill-geometry-form'\r\nimport { MasonryAntiOverturningMaterialPropertiesForm } from './sections/anti-overturning-material-properties-form'\r\nimport { MasonryAntiOverturningSiteCharacteristicsForm } from './sections/anti-overturning-site-characteristics-form'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  module: ModuleWithParamsAntiOverturning\r\n}\r\n\r\nexport const MasonryAntiOverturningParamsForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  module,\r\n}: Props) => {\r\n  const [params, setParams] =\r\n    useState<MasonryAntiOverturningParamsFormSchemaInputs>(\r\n      module?.params ?? ({} as MasonryAntiOverturningParamsFormSchemaInputs),\r\n    )\r\n\r\n  const seismicDemandCalculationResult = module?.seismicDemandCalculationResult\r\n  const preInterventionCalculationResult =\r\n    module?.preInterventionCalculationResult\r\n\r\n  const _isCompositeReinforcementSystemDefined =\r\n    preInterventionCalculationResult && seismicDemandCalculationResult\r\n\r\n  const [openItems, setOpenItems] = useState(['0'])\r\n  const t = useTranslations('forms.project-params.anti-overturning')\r\n\r\n  const handleItemSaved = useCallback((id: string) => {\r\n    const nextId = String(Number(id) + 1)\r\n    setOpenItems(old => {\r\n      const temp = old.filter(v => v !== id)\r\n      return old.includes(nextId) ? temp : [...temp, nextId]\r\n    })\r\n  }, [])\r\n\r\n  return (\r\n    <div>\r\n      <AccordionComponent\r\n        type=\"multiple\"\r\n        value={openItems}\r\n        onValueChange={setOpenItems}\r\n      >\r\n        <AccordionItem value=\"0\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('site-characteristics.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <MasonryAntiOverturningSiteCharacteristicsForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={{\r\n                seismicAccelerationAtSlv:\r\n                  params?.siteCharacteristics?.seismicAccelerationAtSlv,\r\n                amplificationFactorAtSlv:\r\n                  params?.siteCharacteristics?.amplificationFactorAtSlv,\r\n                subsoilCategory: params?.siteCharacteristics?.subsoilCategory,\r\n                ssCoefficient: params?.siteCharacteristics?.ssCoefficient,\r\n                topographicCategory:\r\n                  params?.siteCharacteristics?.topographicCategory,\r\n                stCoefficient: params?.siteCharacteristics?.stCoefficient,\r\n                subsoilCoefficient:\r\n                  params?.siteCharacteristics?.subsoilCoefficient,\r\n              }}\r\n              setParams={setParams}\r\n              params={params}\r\n              onSave={() => handleItemSaved('0')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"1\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('building-characteristics.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <MasonryAntiOverturningBuildingCharacteristicsForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={{\r\n                buildingType: params?.buildingCharacteristics?.buildingType,\r\n                totalBuildingHeight:\r\n                  params?.buildingCharacteristics?.totalBuildingHeight,\r\n                buildingFundamentalPeriod:\r\n                  params?.buildingCharacteristics?.buildingFundamentalPeriod,\r\n                parameterA: params?.buildingCharacteristics?.parameterA,\r\n                parameterB: params?.buildingCharacteristics?.parameterB,\r\n                parameterAp: params?.buildingCharacteristics?.parameterAp,\r\n              }}\r\n              setParams={setParams}\r\n              params={params}\r\n              onSave={() => handleItemSaved('1')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"2\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('material-properties.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <MasonryAntiOverturningMaterialPropertiesForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={{\r\n                infillWallTypology:\r\n                  params?.materialProperties?.infillWallTypology,\r\n                facingMaterial: params?.materialProperties?.facingMaterial,\r\n                knowledgeLevel: params?.materialProperties?.knowledgeLevel,\r\n                executionClass: params?.materialProperties?.executionClass,\r\n                loadResistingCategory:\r\n                  params?.materialProperties?.loadResistingCategory,\r\n                characteristicCompressiveStrength:\r\n                  params?.materialProperties?.characteristicCompressiveStrength,\r\n                infillWallElasticModulus:\r\n                  params?.materialProperties?.infillWallElasticModulus,\r\n                masonrySpecificWeightExternalFacing:\r\n                  params?.materialProperties\r\n                    ?.masonrySpecificWeightExternalFacing,\r\n                masonrySpecificWeightInternalFacing:\r\n                  params?.materialProperties\r\n                    ?.masonrySpecificWeightInternalFacing,\r\n                plasterSpecificWeight:\r\n                  params?.materialProperties?.plasterSpecificWeight,\r\n                confidenceFactor: params?.materialProperties?.confidenceFactor,\r\n                masonryStrengthSafetyFactor:\r\n                  params?.materialProperties?.masonryStrengthSafetyFactor,\r\n                designCompressiveStrength:\r\n                  params?.materialProperties?.designCompressiveStrength,\r\n                ultimateMasonryStrain:\r\n                  params?.materialProperties?.ultimateMasonryStrain,\r\n              }}\r\n              params={params}\r\n              setParams={setParams}\r\n              onSave={() => handleItemSaved('2')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"3\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('infill-geometry.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <MasonryAntiOverturningInfillGeometryForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={{\r\n                panelWidth: params?.infillGeometry?.panelWidth,\r\n                externalFacingThickness:\r\n                  params?.infillGeometry?.externalFacingThickness,\r\n                internalFacingThickness:\r\n                  params?.infillGeometry?.internalFacingThickness,\r\n                singleSidePlasterThickness:\r\n                  params?.infillGeometry?.singleSidePlasterThickness,\r\n                netPanelHeight: params?.infillGeometry?.netPanelHeight,\r\n                panelHeightFromGroundLevel:\r\n                  params?.infillGeometry?.panelHeightFromGroundLevel,\r\n                panelCentroidFromGroundLevel:\r\n                  params?.infillGeometry?.panelCentroidFromGroundLevel,\r\n                fundamentalPeriodPanel:\r\n                  params?.infillGeometry?.fundamentalPeriodPanel,\r\n              }}\r\n              params={params}\r\n              onSave={() => handleItemSaved('3')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"4\">\r\n          <AccordionTrigger disabled={!seismicDemandCalculationResult}>\r\n            <h3 className=\"text-lg font-medium\">{t('seismic-demand.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <MasonryAntiOverturningSeismicDemandCalculationResultsForm\r\n              seismicDemandCalculationResult={seismicDemandCalculationResult}\r\n              onSave={() => handleItemSaved('4')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"5\">\r\n          <AccordionTrigger disabled={!preInterventionCalculationResult}>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('pre-intervention.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <MasonryAntiOverturningPreInterventionCalculationResultsForm\r\n              preInterventionCalculationResult={\r\n                preInterventionCalculationResult\r\n              }\r\n              onSave={() => handleItemSaved('5')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"6\">\r\n          <AccordionTrigger disabled={!compositeReinforcementSystemInputSchema}>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('composite-reinforcement-system.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <MasonryAntiOverturningCompositeReinforcementSystemCalculationForm\r\n              session={session}\r\n              projectId={projectId}\r\n              module={module}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n      </AccordionComponent>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAWA;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AASO,MAAM,mCAAmC;QAAC,EAC/C,OAAO,EACP,SAAS,EACT,QAAQ,EACR,MAAM,EACA;QA4CU,6BAEA,8BACe,8BACF,8BAEb,8BACa,8BAEb,8BAoBY,iCAEZ,kCAEA,kCACU,kCACA,kCACC,kCAqBX,4BACc,6BACA,6BACA,6BAEd,6BAEA,6BAEA,6BAEA,6BAGA,6BAGA,6BACgB,8BAEhB,8BAEA,8BAEA,8BAoBU,wBAEV,yBAEA,yBAEA,yBACc,yBAEd,yBAEA,yBAEA;;QA3JZ;IAFJ,MAAM,CAAC,QAAQ,UAAU,GACvB,IAAA,yKAAQ,EACN,CAAA,iBAAA,mBAAA,6BAAA,OAAQ,MAAM,cAAd,4BAAA,iBAAmB,CAAC;IAGxB,MAAM,iCAAiC,mBAAA,6BAAA,OAAQ,8BAA8B;IAC7E,MAAM,mCACJ,mBAAA,6BAAA,OAAQ,gCAAgC;IAE1C,MAAM,yCACJ,oCAAoC;IAEtC,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAC;QAAC;KAAI;IAChD,MAAM,IAAI,IAAA,4NAAe,EAAC;IAE1B,MAAM,kBAAkB,IAAA,4KAAW;yEAAC,CAAC;YACnC,MAAM,SAAS,OAAO,OAAO,MAAM;YACnC;iFAAa,CAAA;oBACX,MAAM,OAAO,IAAI,MAAM;8FAAC,CAAA,IAAK,MAAM;;oBACnC,OAAO,IAAI,QAAQ,CAAC,UAAU,OAAO;2BAAI;wBAAM;qBAAO;gBACxD;;QACF;wEAAG,EAAE;IAEL,qBACE,6LAAC;kBACC,cAAA,6LAAC,qJAAkB;YACjB,MAAK;YACL,OAAO;YACP,eAAe;;8BAEf,6LAAC,yJAAa;oBAAC,OAAM;;sCACnB,6LAAC,4JAAgB;sCACf,cAAA,6LAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;;;;;;sCAGP,6LAAC,4JAAgB;sCACf,cAAA,6LAAC,wUAA6C;gCAC5C,SAAS;gCACT,WAAW;gCACX,UAAU;gCACV,eAAe;oCACb,wBAAwB,EACtB,mBAAA,8BAAA,8BAAA,OAAQ,mBAAmB,cAA3B,kDAAA,4BAA6B,wBAAwB;oCACvD,wBAAwB,EACtB,mBAAA,8BAAA,+BAAA,OAAQ,mBAAmB,cAA3B,mDAAA,6BAA6B,wBAAwB;oCACvD,eAAe,EAAE,mBAAA,8BAAA,+BAAA,OAAQ,mBAAmB,cAA3B,mDAAA,6BAA6B,eAAe;oCAC7D,aAAa,EAAE,mBAAA,8BAAA,+BAAA,OAAQ,mBAAmB,cAA3B,mDAAA,6BAA6B,aAAa;oCACzD,mBAAmB,EACjB,mBAAA,8BAAA,+BAAA,OAAQ,mBAAmB,cAA3B,mDAAA,6BAA6B,mBAAmB;oCAClD,aAAa,EAAE,mBAAA,8BAAA,+BAAA,OAAQ,mBAAmB,cAA3B,mDAAA,6BAA6B,aAAa;oCACzD,kBAAkB,EAChB,mBAAA,8BAAA,+BAAA,OAAQ,mBAAmB,cAA3B,mDAAA,6BAA6B,kBAAkB;gCACnD;gCACA,WAAW;gCACX,QAAQ;gCACR,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,6LAAC,yJAAa;oBAAC,OAAM;;sCACnB,6LAAC,4JAAgB;sCACf,cAAA,6LAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;;;;;;sCAGP,6LAAC,4JAAgB;sCACf,cAAA,6LAAC,gVAAiD;gCAChD,SAAS;gCACT,WAAW;gCACX,UAAU;gCACV,eAAe;oCACb,YAAY,EAAE,mBAAA,8BAAA,kCAAA,OAAQ,uBAAuB,cAA/B,sDAAA,gCAAiC,YAAY;oCAC3D,mBAAmB,EACjB,mBAAA,8BAAA,mCAAA,OAAQ,uBAAuB,cAA/B,uDAAA,iCAAiC,mBAAmB;oCACtD,yBAAyB,EACvB,mBAAA,8BAAA,mCAAA,OAAQ,uBAAuB,cAA/B,uDAAA,iCAAiC,yBAAyB;oCAC5D,UAAU,EAAE,mBAAA,8BAAA,mCAAA,OAAQ,uBAAuB,cAA/B,uDAAA,iCAAiC,UAAU;oCACvD,UAAU,EAAE,mBAAA,8BAAA,mCAAA,OAAQ,uBAAuB,cAA/B,uDAAA,iCAAiC,UAAU;oCACvD,WAAW,EAAE,mBAAA,8BAAA,mCAAA,OAAQ,uBAAuB,cAA/B,uDAAA,iCAAiC,WAAW;gCAC3D;gCACA,WAAW;gCACX,QAAQ;gCACR,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,6LAAC,yJAAa;oBAAC,OAAM;;sCACnB,6LAAC,4JAAgB;sCACf,cAAA,6LAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;;;;;;sCAGP,6LAAC,4JAAgB;sCACf,cAAA,6LAAC,sUAA4C;gCAC3C,SAAS;gCACT,WAAW;gCACX,UAAU;gCACV,eAAe;oCACb,kBAAkB,EAChB,mBAAA,8BAAA,6BAAA,OAAQ,kBAAkB,cAA1B,iDAAA,2BAA4B,kBAAkB;oCAChD,cAAc,EAAE,mBAAA,8BAAA,8BAAA,OAAQ,kBAAkB,cAA1B,kDAAA,4BAA4B,cAAc;oCAC1D,cAAc,EAAE,mBAAA,8BAAA,8BAAA,OAAQ,kBAAkB,cAA1B,kDAAA,4BAA4B,cAAc;oCAC1D,cAAc,EAAE,mBAAA,8BAAA,8BAAA,OAAQ,kBAAkB,cAA1B,kDAAA,4BAA4B,cAAc;oCAC1D,qBAAqB,EACnB,mBAAA,8BAAA,8BAAA,OAAQ,kBAAkB,cAA1B,kDAAA,4BAA4B,qBAAqB;oCACnD,iCAAiC,EAC/B,mBAAA,8BAAA,8BAAA,OAAQ,kBAAkB,cAA1B,kDAAA,4BAA4B,iCAAiC;oCAC/D,wBAAwB,EACtB,mBAAA,8BAAA,8BAAA,OAAQ,kBAAkB,cAA1B,kDAAA,4BAA4B,wBAAwB;oCACtD,mCAAmC,EACjC,mBAAA,8BAAA,8BAAA,OAAQ,kBAAkB,cAA1B,kDAAA,4BACI,mCAAmC;oCACzC,mCAAmC,EACjC,mBAAA,8BAAA,8BAAA,OAAQ,kBAAkB,cAA1B,kDAAA,4BACI,mCAAmC;oCACzC,qBAAqB,EACnB,mBAAA,8BAAA,8BAAA,OAAQ,kBAAkB,cAA1B,kDAAA,4BAA4B,qBAAqB;oCACnD,gBAAgB,EAAE,mBAAA,8BAAA,+BAAA,OAAQ,kBAAkB,cAA1B,mDAAA,6BAA4B,gBAAgB;oCAC9D,2BAA2B,EACzB,mBAAA,8BAAA,+BAAA,OAAQ,kBAAkB,cAA1B,mDAAA,6BAA4B,2BAA2B;oCACzD,yBAAyB,EACvB,mBAAA,8BAAA,+BAAA,OAAQ,kBAAkB,cAA1B,mDAAA,6BAA4B,yBAAyB;oCACvD,qBAAqB,EACnB,mBAAA,8BAAA,+BAAA,OAAQ,kBAAkB,cAA1B,mDAAA,6BAA4B,qBAAqB;gCACrD;gCACA,QAAQ;gCACR,WAAW;gCACX,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,6LAAC,yJAAa;oBAAC,OAAM;;sCACnB,6LAAC,4JAAgB;sCACf,cAAA,6LAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;;;;;;sCAGP,6LAAC,4JAAgB;sCACf,cAAA,6LAAC,8TAAwC;gCACvC,SAAS;gCACT,WAAW;gCACX,UAAU;gCACV,eAAe;oCACb,UAAU,EAAE,mBAAA,8BAAA,yBAAA,OAAQ,cAAc,cAAtB,6CAAA,uBAAwB,UAAU;oCAC9C,uBAAuB,EACrB,mBAAA,8BAAA,0BAAA,OAAQ,cAAc,cAAtB,8CAAA,wBAAwB,uBAAuB;oCACjD,uBAAuB,EACrB,mBAAA,8BAAA,0BAAA,OAAQ,cAAc,cAAtB,8CAAA,wBAAwB,uBAAuB;oCACjD,0BAA0B,EACxB,mBAAA,8BAAA,0BAAA,OAAQ,cAAc,cAAtB,8CAAA,wBAAwB,0BAA0B;oCACpD,cAAc,EAAE,mBAAA,8BAAA,0BAAA,OAAQ,cAAc,cAAtB,8CAAA,wBAAwB,cAAc;oCACtD,0BAA0B,EACxB,mBAAA,8BAAA,0BAAA,OAAQ,cAAc,cAAtB,8CAAA,wBAAwB,0BAA0B;oCACpD,4BAA4B,EAC1B,mBAAA,8BAAA,0BAAA,OAAQ,cAAc,cAAtB,8CAAA,wBAAwB,4BAA4B;oCACtD,sBAAsB,EACpB,mBAAA,8BAAA,0BAAA,OAAQ,cAAc,cAAtB,8CAAA,wBAAwB,sBAAsB;gCAClD;gCACA,QAAQ;gCACR,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,6LAAC,yJAAa;oBAAC,OAAM;;sCACnB,6LAAC,4JAAgB;4BAAC,UAAU,CAAC;sCAC3B,cAAA,6LAAC;gCAAG,WAAU;0CAAuB,EAAE;;;;;;;;;;;sCAEzC,6LAAC,4JAAgB;sCACf,cAAA,6LAAC,kVAAyD;gCACxD,gCAAgC;gCAChC,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,6LAAC,yJAAa;oBAAC,OAAM;;sCACnB,6LAAC,4JAAgB;4BAAC,UAAU,CAAC;sCAC3B,cAAA,6LAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;;;;;;sCAGP,6LAAC,4JAAgB;sCACf,cAAA,6LAAC,qWAA2D;gCAC1D,kCACE;gCAEF,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,6LAAC,yJAAa;oBAAC,OAAM;;sCACnB,6LAAC,4JAAgB;4BAAC,UAAU,CAAC,2MAAuC;sCAClE,cAAA,6LAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;;;;;;sCAGP,6LAAC,4JAAgB;sCACf,cAAA,6LAAC,4XAAiE;gCAChE,SAAS;gCACT,WAAW;gCACX,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB;GArNa;;QAmBD,4NAAe;;;KAnBd", "debugId": null}}]}