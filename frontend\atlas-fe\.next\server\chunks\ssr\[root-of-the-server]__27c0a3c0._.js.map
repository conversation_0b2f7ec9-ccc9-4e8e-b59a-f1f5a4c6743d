{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 19, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/app/%5Blocale%5D/%5B...rest%5D/page.tsx"], "sourcesContent": ["import { notFound } from 'next/navigation'\r\n\r\nexport default function CatchAllPage() {\r\n  notFound()\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAEe,SAAS;IACtB,IAAA,iMAAQ;AACV", "debugId": null}}]}