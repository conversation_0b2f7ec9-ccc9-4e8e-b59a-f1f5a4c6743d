import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from '@atlas/components/ui/card'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON>List,
  TabsTrigger,
} from '@atlas/components/ui/tabs'
import type { MaschiMurariFlexuralReinforcementCalculationResultSchema } from '@atlas/types/schemas/masonry/maschi-murari-form'
import { useLocale, useTranslations } from 'next-intl'
import { ExtremityDetachementCheck } from './maschi-murari-extremity-detachement-check-result-card'
import { InPlaneFlexuralCheckResultCard } from './maschi-murari-flexural-in-plane-result-card'
import { OutOfPlaneFlexuralCheckResultCard } from './maschi-murari-flexural-out-of-plane-result-card'
import { ShearCheckResultCard } from './maschi-murari-shear-check-result-card'

type Props = {
  flexuralReinforcementCalculationResult: MaschiMurariFlexuralReinforcementCalculationResultSchema
}

export function MaschiMurariReinforcementFlexuralCalculationResult({
  flexuralReinforcementCalculationResult,
}: Props) {
  const t = useTranslations(
    'components.calculations.maschi-murari.flexuralReinforcementCalculationResult',
  )
  const _locale = useLocale()

  return (
    <>
      <h3 className="text-lg font-medium py-4">{t('title')}</h3>
      <Card>
        <CardHeader>
          <CardTitle>{t('title')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Tabs defaultValue="inPlaneFlexuralCheck">
            <TabsList>
              <TabsTrigger value="inPlaneFlexuralCheck">
                {t('inPlaneFlexuralCheck.label')}
              </TabsTrigger>
              <TabsTrigger value="outOfPlaneFlexuralCheck">
                {t('outOfPlaneFlexuralCheck.label')}
              </TabsTrigger>
              <TabsTrigger value="shearCheck">
                {t('shearCheck.label')}
              </TabsTrigger>
              <TabsTrigger value="extremityDetachementCheck">
                {t('extremityDetachementCheck.label')}
              </TabsTrigger>
            </TabsList>
            <TabsContent value="inPlaneFlexuralCheck">
              <InPlaneFlexuralCheckResultCard
                inPlaneFlexuralCheckSchema={
                  flexuralReinforcementCalculationResult?.inPlaneFlexuralCheck
                }
              />
            </TabsContent>
            <TabsContent value="outOfPlaneFlexuralCheck">
              <OutOfPlaneFlexuralCheckResultCard
                outOfPlaneFlexuralCheckSchema={
                  flexuralReinforcementCalculationResult?.outOfPlaneFlexuralCheck
                }
              />
            </TabsContent>
            <TabsContent value="shearCheck">
              <ShearCheckResultCard
                shearCheckResults={
                  flexuralReinforcementCalculationResult?.shearCheck
                }
              />
            </TabsContent>
            <TabsContent value="extremityDetachementCheck">
              <ExtremityDetachementCheck
                extremityDetachementCheck={
                  flexuralReinforcementCalculationResult?.extremityDetachementCheck
                }
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </>
  )
}
