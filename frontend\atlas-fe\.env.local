NEXTAUTH_URL=http://localhost:3000
AUTH_SECRET="xLtlfmv7SE/PXe92LSE+yFxBPGKLcK/rSOmdFXYo6G4=" # Added by `npx auth`. Read more: https://cli.authjs.dev
KEYCLOAK_BASE_URL=http://localhost:8080
KEYCLOAK_REALM=atlas
KEYCLOAK_ADMIN_USER=admin
K<PERSON>_ADMIN_PASSWORD=admin
AUTH_KEYCLOAK_ID=atlas
AUTH_KEYCLOAK_SECRET=c1b3b3b3-4b3b-4b3b-4b3b-4b3b3b3b3b3b
AUTH_KEYCLOAK_ISSUER=http://localhost:8080/realms/atlas
AUTH_KEYCLOAK_REDIRECT_URI=http://localhost:3000
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
NEXT_PUBLIC_AUTH_KEYCLOAK_ISSUER=http://localhost:8080/realms/atlas
NEXT_PUBLIC_AUTH_CLIENT_ID=atlas
NEXT_PUBLIC_AUTH_REDIRECT_URI=http://localhost:3000