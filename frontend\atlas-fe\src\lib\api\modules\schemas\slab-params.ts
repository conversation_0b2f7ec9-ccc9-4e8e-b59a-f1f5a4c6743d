import { z } from 'zod'

export const slabParamsSchema = z.object({
  geometry: z
    .object({
      joistFormwork: z
        .enum(['T_SHAPED', 'RECTANGULAR_WITHOUT_SLAB'])
        .optional(),
      joistBase: z.number().optional(),
      joistWebHeight: z.number().optional(),
      existingSlabHeight: z.number().optional(),
      joistSpacing: z.number().optional(),
      bottomRebarCover: z.number().optional(),
      topRebarCover: z.number().optional(),
      structuralScheme: z.enum(['SIMPLY_SUPPORTED', 'CANTILEVER']).optional(),
      totalSlabThickness: z.number().optional(),
      effectiveDepth: z.number().optional(),
    })
    .optional()
    .nullable(),

  slabRebar: z
    .object({
      spanBottomRebar: z
        .object({
          diameter: z.number().optional(),
          quantity: z.number().optional(),
          area: z.number().optional(),
        })
        .optional(),
      spanTopRebar: z
        .object({
          diameter: z.number().optional(),
          quantity: z.number().optional(),
          area: z.number().optional(),
        })
        .optional(),
      supportBottomRebar: z
        .object({
          diameter: z.number().optional(),
          quantity: z.number().optional(),
          area: z.number().optional(),
        })
        .optional(),
      supportTopRebar: z
        .object({
          diameter: z.number().optional(),
          quantity: z.number().optional(),
          area: z.number().optional(),
        })
        .optional(),
      additionalRebar: z
        .object({
          diameter: z.number().optional(),
          quantity: z.number().optional(),
          area: z.number().optional(),
        })
        .optional(),
      additionalSteelElasticModulus: z.number().optional(),
    })
    .optional()
    .nullable(),

  materialProperties: z
    .object({
      concreteClassKnowledgeLevel: z.enum(['LC1', 'LC2', 'LC3']).optional(),
      steelGradeKnowledgeLevel: z.enum(['LC1', 'LC2', 'LC3']).optional(),
      concreteMaterialClass: z.enum(['DUCTILE', 'BRITTLE']).optional(),
      steelMaterialClass: z.enum(['DUCTILE', 'BRITTLE']).optional(),
      concreteClass: z
        .object({
          id: z.string().optional(),
          name: z.string().optional(),
          cubeCompressiveStrength: z.number().optional(),
          cylinderCompressiveStrength: z.number().optional(),
          averageCompressiveStrength: z.number().optional(),
          averageTensileStrength: z.number().optional(),
          elasticModulus: z.number().optional(),
          designCompressiveStrengthForBrittleMechanisms: z.number().optional(),
          designTensileStrengthForBrittleMechanisms: z.number().optional(),
          designCompressiveStrengthForDuctileMechanisms: z.number().optional(),
        })
        .optional(),
      steelGrade: z
        .object({
          id: z.string().optional(),
          name: z.string().optional(),
          yieldStrength: z.number().optional(),
          tensileStrength: z.number().optional(),
          elongationPercentage: z.number().optional(),
          elasticModulus: z.number().optional(),
          designYieldStrengthForBrittleMechanisms: z.number().optional(),
          designYieldStrengthForDuctileMechanisms: z.number().optional(),
        })
        .optional(),
    })
    .optional()
    .nullable(),

  slabFrcReinforcement: z
    .object({
      frcReinforcementType: z.string().optional(),
      toughnessClassAndFibersType: z.string().optional(),
      frcSlabThickness: z.number().optional(),
      frcElasticModulus: z.number().optional(),
      characteristicCylindricalCompressiveStrengthFrcMatrix: z
        .number()
        .optional(),
      frcCharacteristicTensileStrength: z.number().optional(),
      specificWeight: z.number().optional(),
      supportAdditionalRebar: z
        .object({
          diameter: z.number().optional(),
          quantity: z.number().optional(),
          elasticModulus: z.number().optional(),
        })
        .optional(),
    })
    .optional()
    .nullable(),
})

export const slabFlexuralPositiveCalculationResultSchema = z.object({
  designMoment: z.number().optional().nullable(),
  resistantMoment: z.number().optional().nullable(),
  utilizationRatio: z.number().optional().nullable(),
  neutralAxisDepth: z.number().optional().nullable(),
  compressionZoneHeight: z.number().optional().nullable(),
  isVerified: z.boolean().optional().nullable(),
})

export const slabFlexuralNegativeCalculationResultSchema = z.object({
  designMoment: z.number().optional().nullable(),
  resistantMoment: z.number().optional().nullable(),
  utilizationRatio: z.number().optional().nullable(),
  neutralAxisDepth: z.number().optional().nullable(),
  compressionZoneHeight: z.number().optional().nullable(),
  isVerified: z.boolean().optional().nullable(),
})

export const slabShearCalculationResultSchema = z.object({
  // Common properties
  sectionFillType: z.string().optional().nullable(),
  inputShearForce: z.number().optional().nullable(),

  // Unreinforced section results
  unreinforcedSectionEffectiveDepth: z.number().optional().nullable(),
  unreinforcedTensionAreaMinimumWidth: z.number().optional().nullable(),
  unreinforcedSizeEffectFactor: z.number().optional().nullable(),
  unreinforcedCoefficient: z.number().optional().nullable(),
  unreinforcedTensileLongitudinalReinforcementArea: z
    .number()
    .optional()
    .nullable(),
  unreinforcedTensileReinforcementRatio: z.number().optional().nullable(),
  unreinforcedShearCapacity: z.number().optional().nullable(),

  // Reinforced section results
  reinforcedSectionEffectiveDepth: z.number().optional().nullable(),
  reinforcedTensionAreaMinimumWidth: z.number().optional().nullable(),
  reinforcedSizeEffectFactor: z.number().optional().nullable(),
  reinforcedMeanCharacteristicCompressiveStrength: z
    .number()
    .optional()
    .nullable(),
  reinforcedCoefficient: z.number().optional().nullable(),
  reinforcedTensileLongitudinalReinforcementArea: z
    .number()
    .optional()
    .nullable(),
  reinforcedTensileReinforcementRatio: z.number().optional().nullable(),
  reinforcedSectionShearResistance: z.number().optional().nullable(),

  // Verification result
  shearVerificationResult: z.string().optional().nullable(),
  isShearVerificationSatisfied: z.boolean().optional().nullable(),
})

export const slabNegativeMomentCalculationResultSchema = z.object({
  designMoment: z.number().optional().nullable(),
  resistantMoment: z.number().optional().nullable(),
  utilizationRatio: z.number().optional().nullable(),
  effectiveDepth: z.number().optional().nullable(),
  reinforcementArea: z.number().optional().nullable(),
  isVerified: z.boolean().optional().nullable(),
})

export const slabCalculationsResultSchema = z.object({
  flexuralPositive: slabFlexuralPositiveCalculationResultSchema.optional(),
  flexuralNegative: slabFlexuralNegativeCalculationResultSchema.optional(),
  shear: slabShearCalculationResultSchema.optional(),
  negativeMoment: slabNegativeMomentCalculationResultSchema.optional(),
})

export const slabFlexuralVerifyExecutionInputSchema = z
  .object({
    stripWidth: z.number().optional(),
    bendingMoment: z.number().optional(),
    spanVerification: z.boolean().optional(), // Backend uses 'spanVerification' not 'isSpanVerification'
    product: z
      .object({
        // Backend uses 'product' not 'productInput'
        id: z.string().optional(),
        name: z.string().optional(),
        sourceType: z.enum(['DATABASE', 'CUSTOM']).optional(),
        thickness: z.number().optional().nullable(), // Backend uses 'thickness' not 'frcSlabThickness'
        elasticModulus: z.number().optional(),
        cylindricCompressiveStrength: z.number().optional(),
        characteristicTensileStrength: z.number().optional(),
        specificWeight: z.number().optional().nullable(),
        adhesionToConcrete: z.number().optional(),
        fiberType: z.string().optional(),
        tensileStrength: z.number().optional(),
        orientation: z.string().optional().nullable(),
        systemDeformation: z.number().optional(),
      })
      .optional(),
  })
  .optional()

export const slabShearVerifyExecutionInputSchema = z
  .object({
    sectionFillType: z.string().optional(),
    shearForce: z.number().optional(),
    isCantilever: z.boolean().optional(),
    product: z
      .object({
        // Backend uses 'product' not 'productInput'
        id: z.string().optional(),
        name: z.string().optional(),
        sourceType: z.enum(['DATABASE', 'CUSTOM']).optional(),
        thickness: z.number().optional().nullable(), // Backend uses 'thickness' not 'frcSlabThickness'
        elasticModulus: z.number().optional(),
        cylindricCompressiveStrength: z.number().optional(),
        characteristicTensileStrength: z.number().optional(),
        specificWeight: z.number().optional().nullable(),
        adhesionToConcrete: z.number().optional(),
        fiberType: z.string().optional(),
        tensileStrength: z.number().optional(),
        orientation: z.string().optional().nullable(),
        systemDeformation: z.number().optional(),
      })
      .optional(),
  })
  .optional()

export const slabInterfaceSlipVerifyExecutionInputSchema = z
  .object({
    stripWidth: z.number().optional(),
    shearForce: z.number().optional(),
    frcBondStrength: z.number().optional(),
    product: z
      .object({
        // Backend uses 'product' not 'productInput'
        id: z.string().optional(),
        name: z.string().optional(),
        sourceType: z.enum(['DATABASE', 'CUSTOM']).optional(),
        thickness: z.number().optional().nullable(), // Backend uses 'thickness' not 'frcSlabThickness'
        elasticModulus: z.number().optional(),
        cylindricCompressiveStrength: z.number().optional(),
        characteristicTensileStrength: z.number().optional(),
        specificWeight: z.number().optional().nullable(),
        adhesionToConcrete: z.number().optional(),
        fiberType: z.string().optional(),
        tensileStrength: z.number().optional(),
        orientation: z.string().optional().nullable(),
        systemDeformation: z.number().optional(),
      })
      .optional(),
  })
  .optional()

export const slabFlexuralCalculationResultSchema = z.object({
  // Common properties
  sectionType: z.string().optional().nullable(),

  // Unreinforced section results
  unreinforcedBottomSteelStrain: z.number().optional().nullable(),
  unreinforcedTopSteelStrain: z.number().optional().nullable(),
  unreinforcedBottomSteelStress: z.number().optional().nullable(),
  unreinforcedTopSteelStress: z.number().optional().nullable(),
  unreinforcedNeutralAxisDistance: z.number().optional().nullable(),
  unreinforcedTranslationalEquilibrium: z.number().optional().nullable(),
  unreinforcedResistanceMoment: z.number().optional().nullable(),

  // Reinforced section results
  maximumBendingMoment: z.number().optional().nullable(),
  reinforcedBottomSteelStrain: z.number().optional().nullable(),
  reinforcedTopSteelStrain: z.number().optional().nullable(),
  reinforcedSupplementarySteelStrain: z.number().optional().nullable(),
  reinforcedBottomSteelStress: z.number().optional().nullable(),
  reinforcedTopSteelStress: z.number().optional().nullable(),
  reinforcedSupplementarySteelStress: z.number().optional().nullable(),
  reinforcedNeutralAxisDistance: z.number().optional().nullable(),
  reinforcedTranslationalEquilibrium: z.number().optional().nullable(),
  reinforcedSectionResistanceMoment: z.number().optional().nullable(),

  // Verification results
  checkResult: z.boolean().optional().nullable(),
  checkValue: z.number().optional().nullable(),
})

export const slabInterfaceSlipCalculationResultSchema = z.object({
  // Calculation results
  negativeMomentAreaHomogenizationCoefficient: z.number().optional().nullable(),
  neutralAxisCompressedFlangeDistance: z.number().optional().nullable(),
  translationalEquilibrium: z.number().optional().nullable(),
  staticMoment: z.number().optional().nullable(),
  neutralAxisInertiaMoment: z.number().optional().nullable(),
  interfaceShearStress: z.number().optional().nullable(),
  concreteToConcreteFrictionCoefficient: z.number().optional().nullable(),
  frcBondStrength: z.number().optional().nullable(),

  // Verification result
  checkResult: z.boolean().optional().nullable(),

  // Input values
  inputShearForce: z.number().optional().nullable(),
  inputStripWidth: z.number().optional().nullable(),
})

export type SlabParams = z.infer<typeof slabParamsSchema>
export type SlabFlexuralPositiveCalculationResult = z.infer<
  typeof slabFlexuralPositiveCalculationResultSchema
>
export type SlabFlexuralNegativeCalculationResult = z.infer<
  typeof slabFlexuralNegativeCalculationResultSchema
>
export type SlabShearCalculationResult = z.infer<
  typeof slabShearCalculationResultSchema
>
export type SlabInterfaceSlipCalculationResult = z.infer<
  typeof slabInterfaceSlipCalculationResultSchema
>
export type SlabNegativeMomentCalculationResult = z.infer<
  typeof slabNegativeMomentCalculationResultSchema
>
export type SlabCalculationsResult = z.infer<
  typeof slabCalculationsResultSchema
>

// Schema to check if at least one calculation has been completed
// Used to enable the report generation button
export const slabCalculationCheck = z
  .object({
    flexuralVerifyExecutionResultMPlus:
      slabFlexuralCalculationResultSchema.optional(),
    flexuralVerifyExecutionResultMMinus:
      slabFlexuralCalculationResultSchema.optional(),
    shearVerifyExecutionResult: slabShearCalculationResultSchema.optional(),
    interfaceSlipCalculationResult:
      slabInterfaceSlipCalculationResultSchema.optional(),
  })
  .refine(
    data =>
      data.flexuralVerifyExecutionResultMPlus !== undefined ||
      data.flexuralVerifyExecutionResultMMinus !== undefined ||
      data.shearVerifyExecutionResult !== undefined ||
      data.interfaceSlipCalculationResult !== undefined,
    {
      message:
        'At least one calculation must be completed to generate a report',
    },
  )
