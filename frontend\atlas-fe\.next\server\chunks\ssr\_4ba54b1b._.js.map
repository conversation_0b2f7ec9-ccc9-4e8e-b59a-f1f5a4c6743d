{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/app/%5Blocale%5D/not-found.tsx"], "sourcesContent": ["import { useTranslations } from 'next-intl'\r\n\r\nconst NotFoundPage = () => {\r\n  const t = useTranslations('pages.notFound')\r\n  return <h1>{t('title')}</h1>\r\n}\r\n\r\nexport default NotFoundPage\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAEA,MAAM,eAAe;IACnB,MAAM,IAAI,IAAA,iQAAe,EAAC;IAC1B,qBAAO,8OAAC;kBAAI,EAAE;;;;;;AAChB;uCAEe", "debugId": null}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/use-intl/dist/esm/development/core.js"], "sourcesContent": ["import { r as resolveNamespace, e as createBaseTranslator, f as defaultGetMessageFallback, b as createIntlFormatters, d as createCache, g as defaultOnError } from './initializeConfig-DPFnvsUO.js';\nexport { I as IntlError, a as IntlErrorCode, c as createFormatter, i as initializeConfig } from './initializeConfig-DPFnvsUO.js';\n\n\n\n\nfunction createTranslatorImpl({\n  messages,\n  namespace,\n  ...rest\n}, namespacePrefix) {\n  // The `namespacePrefix` is part of the type system.\n  // See the comment in the function invocation.\n  messages = messages[namespacePrefix];\n  namespace = resolveNamespace(namespace, namespacePrefix);\n  return createBaseTranslator({\n    ...rest,\n    messages,\n    namespace\n  });\n}\n\n// This type is slightly more loose than `AbstractIntlMessages`\n// in order to avoid a type error.\n\n/**\n * Translates messages from the given namespace by using the ICU syntax.\n * See https://formatjs.io/docs/core-concepts/icu-syntax.\n *\n * If no namespace is provided, all available messages are returned.\n * The namespace can also indicate nesting by using a dot\n * (e.g. `namespace.Component`).\n */\nfunction createTranslator({\n  _cache = createCache(),\n  _formatters = createIntlFormatters(_cache),\n  getMessageFallback = defaultGetMessageFallback,\n  messages,\n  namespace,\n  onError = defaultOnError,\n  ...rest\n}) {\n  // We have to wrap the actual function so the type inference for the optional\n  // namespace works correctly. See https://stackoverflow.com/a/71529575/343045\n  // The prefix (\"!\") is arbitrary.\n  // @ts-expect-error Use the explicit annotation instead\n  return createTranslatorImpl({\n    ...rest,\n    onError,\n    cache: _cache,\n    formatters: _formatters,\n    getMessageFallback,\n    // @ts-expect-error `messages` is allowed to be `undefined` here and will be handled internally\n    messages: {\n      '!': messages\n    },\n    namespace: namespace ? `!.${namespace}` : '!'\n  }, '!');\n}\n\n/**\n * Checks if a locale exists in a list of locales.\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale\n */\nfunction hasLocale(locales, candidate) {\n  return locales.includes(candidate);\n}\n\nexport { createCache as _createCache, createIntlFormatters as _createIntlFormatters, createTranslator, hasLocale };\n"], "names": [], "mappings": ";;;;;;AAAA;;;AAMA,SAAS,qBAAqB,EAC5B,QAAQ,EACR,SAAS,EACT,GAAG,MACJ,EAAE,eAAe;IAChB,oDAAoD;IACpD,8CAA8C;IAC9C,WAAW,QAAQ,CAAC,gBAAgB;IACpC,YAAY,IAAA,8LAAgB,EAAC,WAAW;IACxC,OAAO,IAAA,8LAAoB,EAAC;QAC1B,GAAG,IAAI;QACP;QACA;IACF;AACF;AAEA,+DAA+D;AAC/D,kCAAkC;AAElC;;;;;;;CAOC,GACD,SAAS,iBAAiB,EACxB,SAAS,IAAA,8LAAW,GAAE,EACtB,cAAc,IAAA,8LAAoB,EAAC,OAAO,EAC1C,qBAAqB,8LAAyB,EAC9C,QAAQ,EACR,SAAS,EACT,UAAU,8LAAc,EACxB,GAAG,MACJ;IACC,6EAA6E;IAC7E,6EAA6E;IAC7E,iCAAiC;IACjC,uDAAuD;IACvD,OAAO,qBAAqB;QAC1B,GAAG,IAAI;QACP;QACA,OAAO;QACP,YAAY;QACZ;QACA,+FAA+F;QAC/F,UAAU;YACR,KAAK;QACP;QACA,WAAW,YAAY,CAAC,EAAE,EAAE,WAAW,GAAG;IAC5C,GAAG;AACL;AAEA;;;;CAIC,GACD,SAAS,UAAU,OAAO,EAAE,SAAS;IACnC,OAAO,QAAQ,QAAQ,CAAC;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/next-intl/dist/esm/development/server/react-server/getServerTranslator.js"], "sourcesContent": ["import { cache } from 'react';\nimport { createTranslator } from 'use-intl/core';\n\nfunction getServerTranslatorImpl(config, namespace) {\n  return createTranslator({\n    ...config,\n    namespace\n  });\n}\nvar getServerTranslator = cache(getServerTranslatorImpl);\n\nexport { getServerTranslator as default };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,SAAS,wBAAwB,MAAM,EAAE,SAAS;IAChD,OAAO,IAAA,qMAAgB,EAAC;QACtB,GAAG,MAAM;QACT;IACF;AACF;AACA,IAAI,sBAAsB,IAAA,8MAAK,EAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/next-intl/dist/esm/development/react-server/useConfig.js"], "sourcesContent": ["import getConfig from '../server/react-server/getConfig.js';\nimport use from '../shared/use.js';\n\nfunction useHook(hookName, promise) {\n  try {\n    return use(promise);\n  } catch (error) {\n    if (error instanceof TypeError && error.message.includes(\"Cannot read properties of null (reading 'use')\")) {\n      throw new Error(`\\`${hookName}\\` is not callable within an async component. Please refer to https://next-intl.dev/docs/environments/server-client-components#async-components`, {\n        cause: error\n      });\n    } else {\n      throw error;\n    }\n  }\n}\nfunction useConfig(hookName) {\n  return useHook(hookName, getConfig());\n}\n\nexport { useConfig as default };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,SAAS,QAAQ,QAAQ,EAAE,OAAO;IAChC,IAAI;QACF,OAAO,IAAA,sLAAG,EAAC;IACb,EAAE,OAAO,OAAO;QACd,IAAI,iBAAiB,aAAa,MAAM,OAAO,CAAC,QAAQ,CAAC,mDAAmD;YAC1G,MAAM,IAAI,MAAM,CAAC,EAAE,EAAE,SAAS,+IAA+I,CAAC,EAAE;gBAC9K,OAAO;YACT;QACF,OAAO;YACL,MAAM;QACR;IACF;AACF;AACA,SAAS,UAAU,QAAQ;IACzB,OAAO,QAAQ,UAAU,IAAA,+MAAS;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/node_modules/next-intl/dist/esm/development/react-server/useTranslations.js"], "sourcesContent": ["import getServerTranslator from '../server/react-server/getServerTranslator.js';\nimport useConfig from './useConfig.js';\n\nfunction useTranslations(...[namespace]) {\n  const config = useConfig('useTranslations');\n  return getServerTranslator(config, namespace);\n}\n\nexport { useTranslations as default };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,SAAS,gBAAgB,GAAG,CAAC,UAAU;IACrC,MAAM,SAAS,IAAA,qMAAS,EAAC;IACzB,OAAO,IAAA,yNAAmB,EAAC,QAAQ;AACrC", "ignoreList": [0], "debugId": null}}]}