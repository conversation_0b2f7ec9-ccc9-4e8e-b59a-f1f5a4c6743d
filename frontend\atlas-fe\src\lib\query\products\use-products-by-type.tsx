import type { PaginatedProducts } from '@atlas/lib/api/products/schemas/product'
import { getProductsByTypeFunction } from '@atlas/lib/query/products/get-products-by-type'
import type { ApiError, ValidationError } from '@atlas/types'
import { useQuery } from '@tanstack/react-query'
import type { Session } from 'next-auth'

export const useProductsByType = (
  session: Session,
  productType: string,
  page = 0,
  pageSize = 100,
) => {
  const query = useQuery<PaginatedProducts, ApiError | ValidationError>({
    queryKey: ['products', 'type', productType, page, pageSize],
    queryFn: () =>
      getProductsByTypeFunction(session, productType, page, pageSize),
    retry: 1,
  })

  // Log errors
  if (query.error) {
    console.error('🔴 useProductsByType error:', query.error)
  }

  return query
}
