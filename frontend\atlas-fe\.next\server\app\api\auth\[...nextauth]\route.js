var R=require("../../../../chunks/[turbopack]_runtime.js")("server/app/api/auth/[...nextauth]/route.js")
R.c("server/chunks/node_modules_next_746dd77e._.js")
R.c("server/chunks/node_modules_axios_lib_965bc11c._.js")
R.c("server/chunks/node_modules_mime-db_f53cdd2e._.js")
R.c("server/chunks/node_modules_@auth_core_d5839b96._.js")
R.c("server/chunks/node_modules_jose_dist_node_esm_db23953b._.js")
R.c("server/chunks/node_modules_b8f314e5._.js")
R.c("server/chunks/[root-of-the-server]__36a3d7f2._.js")
R.m("[project]/.next-internal/server/app/api/auth/[...nextauth]/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/auth/[...nextauth]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/auth/[...nextauth]/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
