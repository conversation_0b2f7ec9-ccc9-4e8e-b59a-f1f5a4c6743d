import { FACING_MATERIAL } from '@atlas/constants/module'
import type { Product } from '@atlas/lib/api/products/schemas/product'
import { useTranslations } from 'next-intl'

interface Props {
  product: Product
  facingMaterial?: FACING_MATERIAL
  isRectangularBeam?: boolean
}

export const ProductSummaryCard = ({
  product,
  facingMaterial = undefined,
  isRectangularBeam = true,
}: Props) => {
  const t = useTranslations('components.common.atlas.product')

  const tForm = useTranslations(
    `forms.calculations${isRectangularBeam ? '' : '.rectangular-beam'}`,
  )

  return (
    <div className="rounded-md border p-2 my-2 bg-gray-50 text-gray-900 dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700 text-sm">
      <div className="font-semibold mb-1">{t('summary')}</div>
      <ul className="list-disc pl-4">
        {product.fiberType && (
          <li>
            {t('fiberType')}: {tForm(`fiberType.${product.fiberType}`)}
          </li>
        )}
        {product.orientation && (
          <li>
            {t('orientation')}: {tForm(`orientation.${product.orientation}`)}
          </li>
        )}
        {product.thickness && (
          <li>
            {t('thickness')}: {product.thickness}
          </li>
        )}
        {product.availableWidths && (
          <li>
            {t('availableWidths')}: {product.availableWidths.join(', ')}
          </li>
        )}
        {product.elasticModulus && (
          <li>
            {t('elasticModulus')}: {product.elasticModulus}
          </li>
        )}
        {product.cylindricCompressiveStrength && (
          <li>
            {t('cylindricCompressiveStrength')}:{' '}
            {product.cylindricCompressiveStrength}
          </li>
        )}
        {facingMaterial &&
          product.facingPerformance &&
          product.facingPerformance[facingMaterial] && (
            <li>
              {t('facingPerformance')} ({t(`facingMaterial.${facingMaterial}`)}
              ): {product.facingPerformance[facingMaterial]}
            </li>
          )}
        {product.productType && (
          <li>
            {t('productType')}: {product.productType}
          </li>
        )}
        {product.ultimateStrain && (
          <li>
            {t('ultimateStrain')}: {product.ultimateStrain}
          </li>
        )}
        {product.designDeformation && (
          <li>
            {t('designDeformation')}: {product.designDeformation}
          </li>
        )}
        {product.unitStrengthOfTheMesh && (
          <li>
            {t('unitStrengthOfTheMesh')}: {product.unitStrengthOfTheMesh}
          </li>
        )}
        {product.reinforcementTensileStrength && (
          <li>
            {t('reinforcementTensileStrength')}:{' '}
            {product.reinforcementTensileStrength}
          </li>
        )}
        {product.reinforcementUltimateStrain && (
          <li>
            {t('reinforcementUltimateStrain')}:{' '}
            {product.reinforcementUltimateStrain}
          </li>
        )}
        {product.matrixBreachStress && (
          <li>
            {t('matrixBreachStress')}: {product.matrixBreachStress}
          </li>
        )}
        {/* Calculated unitStrengthOfTheMesh */}
        {product.productType === 'MESH' && (
          <li>
            {t('unitStrengthOfTheMesh')}:{' '}
            {(product.name === 'KIMITECH_BS_ST_200' ||
              product.name === 'KIMITECH_BS_ST_400') &&
            facingMaterial
              ? (() => {
                  let value = 0
                  if (product.name === 'KIMITECH_BS_ST_200') {
                    if (facingMaterial === FACING_MATERIAL.BRICK) {
                      value = 1182
                    } else if (facingMaterial === FACING_MATERIAL.TUFF) {
                      value = 1165
                    } else {
                      value = 1126
                    }
                  } else {
                    if (facingMaterial === FACING_MATERIAL.BRICK) {
                      value = 1107
                    } else if (facingMaterial === FACING_MATERIAL.TUFF) {
                      value = 1072
                    } else {
                      value = 1066
                    }
                  }
                  return value
                })()
              : product.tensileStrength}
          </li>
        )}
        {product.width && (
          <li>
            {t('width')}: {product.width}
          </li>
        )}
        {product.density && (
          <li>
            {t('density')}: {product.density}
          </li>
        )}
        {product.maxResistance && (
          <li>
            {t('maxResistance')}: {product.maxResistance}
          </li>
        )}
        {product.weight && (
          <li>
            {t('weight')}: {product.weight}
          </li>
        )}
        {product.crossSectionArea && (
          <li>
            {t('crossSectionArea')}: {product.crossSectionArea}
          </li>
        )}
        {product.diameter && (
          <li>
            {t('diameter')}: {product.diameter}
          </li>
        )}
        {product.pullOutResistance && (
          <li>
            {t('pullOutResistance')}: {product.pullOutResistance}
          </li>
        )}

        {product.connectorsNumberAlongLength && (
          <li>
            {t('connectorsNumberAlongLength')}:{' '}
            {product.connectorsNumberAlongLength}
          </li>
        )}
        {product.designStrength && (
          <li>
            {t('designStrength')}: {product.designStrength}
          </li>
        )}
        {typeof product.systemDeformation === 'number' &&
          product.systemDeformation > 0 && (
            <li>
              {t('systemDeformation')}: {product.systemDeformation}
            </li>
          )}
        {product.adhesionToConcrete && (
          <li>
            {t('adhesionToConcrete')}: {product.adhesionToConcrete}
          </li>
        )}
        {product.shearStress && (
          <li>
            {t('shearStress')}: {product.shearStress}
          </li>
        )}
      </ul>
    </div>
  )
}
