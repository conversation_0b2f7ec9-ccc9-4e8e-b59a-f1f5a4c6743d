{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/header-skeleton.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { Skeleton } from '@atlas/components/ui/skeleton' // For loading state\r\n\r\nexport const HeaderSkeleton = () => (\r\n  <div className=\"mb-6 border-b pb-4\">\r\n    <Skeleton className=\"mb-2 h-8 w-3/4 md:w-1/2\" />\r\n    <div className=\"flex flex-wrap items-center gap-x-4 gap-y-1\">\r\n      <Skeleton className=\"h-5 w-20\" />\r\n      <Skeleton className=\"h-5 w-24\" />\r\n      <Skeleton className=\"h-5 w-32\" />\r\n      <Skeleton className=\"h-5 w-40\" />\r\n      <Skeleton className=\"h-5 w-28\" />\r\n    </div>\r\n  </div>\r\n)\r\n"], "names": [], "mappings": ";;;;;AAEA,6OAAyD,oBAAoB;AAF7E;;;AAIO,MAAM,iBAAiB,kBAC5B,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mJAAQ;gBAAC,WAAU;;;;;;0BACpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mJAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC,mJAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC,mJAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC,mJAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC,mJAAQ;wBAAC,WAAU;;;;;;;;;;;;;;;;;;KARb", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/select.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { cn } from '@atlas/lib/utils'\r\nimport * as SelectPrimitive from '@radix-ui/react-select'\r\nimport { CheckIcon, ChevronDownIcon, ChevronUpIcon } from 'lucide-react'\r\nimport type * as React from 'react'\r\n\r\nfunction Select({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\r\n}\r\n\r\nfunction SelectGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\r\n}\r\n\r\nfunction SelectValue({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = 'default',\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: 'sm' | 'default'\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  )\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = 'popper',\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md',\r\n          position === 'popper' &&\r\n            'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',\r\n          className,\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            'p-1',\r\n            position === 'popper' &&\r\n              'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1',\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction SelectLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn('text-muted-foreground px-2 py-1.5 text-xs', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  )\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn('bg-border pointer-events-none -mx-1 my-1 h-px', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn(\r\n        'flex cursor-default items-center justify-center py-1',\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  )\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn(\r\n        'flex cursor-default items-center justify-center py-1',\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  )\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAJA;;;;;AAOA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,6KAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,8KAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,8KAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC,gLAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,IAAA,4HAAE,EACX,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6KAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,8OAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,KAKgC;QALhC,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD,GALhC;IAMrB,qBACE,6LAAC,+KAAsB;kBACrB,cAAA,6LAAC,gLAAuB;YACtB,aAAU;YACV,WAAW,IAAA,4HAAE,EACX,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,iLAAwB;oBACvB,WAAW,IAAA,4HAAE,EACX,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,8KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,KAIgC;QAJhC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C,GAJhC;IAKlB,qBACE,6LAAC,6KAAoB;QACnB,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,sLAA6B;8BAC5B,cAAA,6LAAC,wNAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,iLAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,SAAS,EACT,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,kLAAyB;QACxB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,KAGgC;QAHhC,EAC5B,SAAS,EACT,GAAG,OACyD,GAHhC;IAI5B,qBACE,6LAAC,uLAA8B;QAC7B,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,wOAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,yLAAgC;QAC/B,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,8OAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 350, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/data-table/data-table-pagination.tsx"], "sourcesContent": ["import { Button } from '@atlas/components/ui/button'\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@atlas/components/ui/select'\r\nimport type { Table } from '@tanstack/react-table'\r\nimport {\r\n  ChevronLeft,\r\n  ChevronRight,\r\n  ChevronsLeft,\r\n  ChevronsRight,\r\n} from 'lucide-react'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ninterface DataTablePaginationProps<TData> {\r\n  table: Table<TData>\r\n}\r\n\r\nexport function DataTablePagination<TData>({\r\n  table,\r\n}: DataTablePaginationProps<TData>) {\r\n  const t = useTranslations('components.data-table.pagination')\r\n\r\n  return (\r\n    <div className=\"flex items-center justify-between px-2 gap-2\">\r\n      {table.getIsSomeRowsSelected() && (\r\n        <div className=\"flex-1 text-sm text-muted-foreground\">\r\n          {t('labels.selected-rows', {\r\n            count: table.getFilteredSelectedRowModel().rows.length,\r\n            total: table.getFilteredRowModel().rows.length,\r\n          })}\r\n        </div>\r\n      )}\r\n      <div className=\"flex items-center flex-col md:flex-row justify-between w-full gap-4\">\r\n        <div className=\"flex items-center gap-4 justify-center w-full md:w-auto flex-col md:flex-row\">\r\n          <p className=\"text-sm font-medium\">{t('labels.rows-per-page')}</p>\r\n          <Select\r\n            value={`${table.getState().pagination.pageSize}`}\r\n            onValueChange={value => {\r\n              table.setPageSize(Number(value))\r\n            }}\r\n          >\r\n            <SelectTrigger className=\"h-8 w-[70px]\">\r\n              <SelectValue placeholder={table.getState().pagination.pageSize} />\r\n            </SelectTrigger>\r\n            <SelectContent side=\"top\">\r\n              {[10, 20, 30, 40, 50].map(pageSize => (\r\n                <SelectItem key={pageSize} value={`${pageSize}`}>\r\n                  {pageSize}\r\n                </SelectItem>\r\n              ))}\r\n            </SelectContent>\r\n          </Select>\r\n        </div>\r\n        <div className=\"flex items-center gap-2 flex-row justify-center w-full md:w-auto\">\r\n          <div className=\"flex w-[100px] items-center justify-center text-sm font-medium\">\r\n            {t('labels.pages', {\r\n              current: table.getState().pagination.pageIndex + 1,\r\n              total: table.getPageCount(),\r\n            })}\r\n          </div>\r\n          <Button\r\n            variant=\"outline\"\r\n            className=\"hidden h-8 w-8 p-0 lg:flex\"\r\n            onClick={() => table.setPageIndex(0)}\r\n            disabled={!table.getCanPreviousPage()}\r\n          >\r\n            <span className=\"sr-only\">{t('labels.go-to-first-page')}</span>\r\n            <ChevronsLeft />\r\n          </Button>\r\n          <Button\r\n            variant=\"outline\"\r\n            className=\"h-8 w-8 p-0\"\r\n            onClick={() => table.previousPage()}\r\n            disabled={!table.getCanPreviousPage()}\r\n          >\r\n            <span className=\"sr-only\">{t('labels.go-to-previous-page')}</span>\r\n            <ChevronLeft />\r\n          </Button>\r\n          <Button\r\n            variant=\"outline\"\r\n            className=\"h-8 w-8 p-0\"\r\n            onClick={() => table.nextPage()}\r\n            disabled={!table.getCanNextPage()}\r\n          >\r\n            <span className=\"sr-only\">{t('labels.go-to-next-page')}</span>\r\n            <ChevronRight />\r\n          </Button>\r\n          <Button\r\n            variant=\"outline\"\r\n            className=\"hidden h-8 w-8 p-0 lg:flex\"\r\n            onClick={() => table.setPageIndex(table.getPageCount() - 1)}\r\n            disabled={!table.getCanNextPage()}\r\n          >\r\n            <span className=\"sr-only\">{t('labels.go-to-last-page')}</span>\r\n            <ChevronsRight />\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAQA;AAAA;AAAA;AAAA;AAMA;;;;;;;AAMO,SAAS,oBAA2B,KAET;QAFS,EACzC,KAAK,EAC2B,GAFS;;IAGzC,MAAM,IAAI,IAAA,4NAAe,EAAC;IAE1B,qBACE,6LAAC;QAAI,WAAU;;YACZ,MAAM,qBAAqB,oBAC1B,6LAAC;gBAAI,WAAU;0BACZ,EAAE,wBAAwB;oBACzB,OAAO,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM;oBACtD,OAAO,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;gBAChD;;;;;;0BAGJ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAuB,EAAE;;;;;;0CACtC,6LAAC,+IAAM;gCACL,OAAO,AAAC,GAAuC,OAArC,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ;gCAC9C,eAAe,CAAA;oCACb,MAAM,WAAW,CAAC,OAAO;gCAC3B;;kDAEA,6LAAC,sJAAa;wCAAC,WAAU;kDACvB,cAAA,6LAAC,oJAAW;4CAAC,aAAa,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ;;;;;;;;;;;kDAEhE,6LAAC,sJAAa;wCAAC,MAAK;kDACjB;4CAAC;4CAAI;4CAAI;4CAAI;4CAAI;yCAAG,CAAC,GAAG,CAAC,CAAA,yBACxB,6LAAC,mJAAU;gDAAgB,OAAO,AAAC,GAAW,OAAT;0DAClC;+CADc;;;;;;;;;;;;;;;;;;;;;;kCAOzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,EAAE,gBAAgB;oCACjB,SAAS,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG;oCACjD,OAAO,MAAM,YAAY;gCAC3B;;;;;;0CAEF,6LAAC,+IAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM,MAAM,YAAY,CAAC;gCAClC,UAAU,CAAC,MAAM,kBAAkB;;kDAEnC,6LAAC;wCAAK,WAAU;kDAAW,EAAE;;;;;;kDAC7B,6LAAC,yOAAY;;;;;;;;;;;0CAEf,6LAAC,+IAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM,MAAM,YAAY;gCACjC,UAAU,CAAC,MAAM,kBAAkB;;kDAEnC,6LAAC;wCAAK,WAAU;kDAAW,EAAE;;;;;;kDAC7B,6LAAC,sOAAW;;;;;;;;;;;0CAEd,6LAAC,+IAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM,MAAM,QAAQ;gCAC7B,UAAU,CAAC,MAAM,cAAc;;kDAE/B,6LAAC;wCAAK,WAAU;kDAAW,EAAE;;;;;;kDAC7B,6LAAC,yOAAY;;;;;;;;;;;0CAEf,6LAAC,+IAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM,MAAM,YAAY,CAAC,MAAM,YAAY,KAAK;gCACzD,UAAU,CAAC,MAAM,cAAc;;kDAE/B,6LAAC;wCAAK,WAAU;kDAAW,EAAE;;;;;;kDAC7B,6LAAC,4OAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1B;GAnFgB;;QAGJ,4NAAe;;;KAHX", "debugId": null}}, {"offset": {"line": 601, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/data-table/data-table-view-option.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { But<PERSON> } from '@atlas//components/ui/button'\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuContent,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n} from '@atlas//components/ui/dropdown-menu'\r\nimport { DropdownMenuTrigger } from '@radix-ui/react-dropdown-menu'\r\nimport type { Table } from '@tanstack/react-table'\r\nimport { Settings2 } from 'lucide-react'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ninterface DataTableViewOptionsProps<TData> {\r\n  table: Table<TData>\r\n}\r\n\r\nexport function DataTableViewOptions<TData>({\r\n  table,\r\n}: DataTableViewOptionsProps<TData>) {\r\n  const t = useTranslations()\r\n\r\n  return (\r\n    <DropdownMenu>\r\n      <DropdownMenuTrigger asChild>\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"sm\"\r\n          className=\"ml-auto hidden h-8 lg:flex\"\r\n        >\r\n          <Settings2 />\r\n          {t('components.data-table.view-options.buttons.options.label')}\r\n        </Button>\r\n      </DropdownMenuTrigger>\r\n      <DropdownMenuContent align=\"end\" className=\"w-[150px]\">\r\n        <DropdownMenuLabel>\r\n          {t('components.data-table.view-options.labels.toggle-columns')}\r\n        </DropdownMenuLabel>\r\n        <DropdownMenuSeparator />\r\n        {table\r\n          .getAllColumns()\r\n          .filter(\r\n            column =>\r\n              typeof column.accessorFn !== 'undefined' && column.getCanHide(),\r\n          )\r\n          .map(column => {\r\n            return (\r\n              <DropdownMenuCheckboxItem\r\n                key={column.id}\r\n                className=\"capitalize\"\r\n                checked={column.getIsVisible()}\r\n                onCheckedChange={value => column.toggleVisibility(!!value)}\r\n              >\r\n                {column.columnDef?.meta?.i18nLabel || column.id}\r\n              </DropdownMenuCheckboxItem>\r\n            )\r\n          })}\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAOA;AAEA;AACA;;;AAbA;;;;;;AAmBO,SAAS,qBAA4B,KAET;QAFS,EAC1C,KAAK,EAC4B,GAFS;;IAG1C,MAAM,IAAI,IAAA,4NAAe;IAEzB,qBACE,6LAAC,+JAAY;;0BACX,6LAAC,sMAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,+IAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;;sCAEV,6LAAC,gOAAS;;;;;wBACT,EAAE;;;;;;;;;;;;0BAGP,6LAAC,sKAAmB;gBAAC,OAAM;gBAAM,WAAU;;kCACzC,6LAAC,oKAAiB;kCACf,EAAE;;;;;;kCAEL,6LAAC,wKAAqB;;;;;oBACrB,MACE,aAAa,GACb,MAAM,CACL,CAAA,SACE,OAAO,OAAO,UAAU,KAAK,eAAe,OAAO,UAAU,IAEhE,GAAG,CAAC,CAAA;4BAQE,wBAAA;wBAPL,qBACE,6LAAC,2KAAwB;4BAEvB,WAAU;4BACV,SAAS,OAAO,YAAY;4BAC5B,iBAAiB,CAAA,QAAS,OAAO,gBAAgB,CAAC,CAAC,CAAC;sCAEnD,EAAA,oBAAA,OAAO,SAAS,cAAhB,yCAAA,yBAAA,kBAAkB,IAAI,cAAtB,6CAAA,uBAAwB,SAAS,KAAI,OAAO,EAAE;2BAL1C,OAAO,EAAE;;;;;oBAQpB;;;;;;;;;;;;;AAIV;GA3CgB;;QAGJ,4NAAe;;;KAHX", "debugId": null}}, {"offset": {"line": 706, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/table.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type * as React from 'react'\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<'table'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"table-container\"\r\n      className=\"relative w-full overflow-x-auto\"\r\n    >\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn('w-full caption-bottom text-sm', className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<'thead'>) {\r\n  return (\r\n    <thead\r\n      data-slot=\"table-header\"\r\n      className={cn('[&_tr]:border-b', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<'tbody'>) {\r\n  return (\r\n    <tbody\r\n      data-slot=\"table-body\"\r\n      className={cn('[&_tr:last-child]:border-0', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<'tfoot'>) {\r\n  return (\r\n    <tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn(\r\n        'bg-muted/50 border-t font-medium [&>tr]:last:border-b-0',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<'tr'>) {\r\n  return (\r\n    <tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        'hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<'th'>) {\r\n  return (\r\n    <th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        'text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<'td'>) {\r\n  return (\r\n    <td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        'p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCaption({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'caption'>) {\r\n  return (\r\n    <caption\r\n      data-slot=\"table-caption\"\r\n      className={cn('text-muted-foreground mt-4 text-sm', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAEA;AAFA;;;AAKA,SAAS,MAAM,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACb,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,IAAA,4HAAE,EAAC,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,KAGY;QAHZ,EACpB,SAAS,EACT,GAAG,OAC6B,GAHZ;IAIpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 858, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/data-table/data-table.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { DataTablePagination } from '@atlas/components/common/atlas/data-table/data-table-pagination'\r\nimport { DataTableViewOptions } from '@atlas/components/common/atlas/data-table/data-table-view-option'\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from '@atlas/components/ui/table'\r\nimport {\r\n  type ColumnDef,\r\n  flexRender,\r\n  getCoreRowModel,\r\n  getSortedRowModel,\r\n  type PaginationState,\r\n  type SortingState,\r\n  useReactTable,\r\n  type VisibilityState,\r\n} from '@tanstack/react-table'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useState } from 'react'\r\n\r\ninterface DataTableProps<TData, TValue> {\r\n  columns: ColumnDef<TData, TValue>[]\r\n  data: TData[]\r\n  onPaginationChange?: (pagination: { page: number; pageSize: number }) => void\r\n  page?: number\r\n  perPage?: number\r\n  rowCount: number\r\n  isLoading?: boolean\r\n  hidePagination?: boolean\r\n}\r\n\r\nexport function DataTable<TData, TValue>({\r\n  columns,\r\n  data,\r\n  onPaginationChange,\r\n  page = 0,\r\n  perPage = 10,\r\n  rowCount,\r\n  isLoading,\r\n  hidePagination = false,\r\n}: DataTableProps<TData, TValue>) {\r\n  const t = useTranslations('components.data-table')\r\n\r\n  const [pagination, setPagination] = useState<PaginationState>({\r\n    pageIndex: page,\r\n    pageSize: perPage,\r\n  })\r\n\r\n  useEffect(() => {\r\n    if (pagination.pageIndex !== page || pagination.pageSize !== perPage) {\r\n      onPaginationChange?.({\r\n        page: pagination.pageIndex + 1,\r\n        pageSize: pagination.pageSize,\r\n      })\r\n    }\r\n  }, [pagination, onPaginationChange, page, perPage])\r\n\r\n  const [sorting, setSorting] = useState<SortingState>([])\r\n  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})\r\n\r\n  const table = useReactTable({\r\n    columns,\r\n    data,\r\n    enableRowSelection: false,\r\n    getCoreRowModel: getCoreRowModel(),\r\n    getSortedRowModel: getSortedRowModel(),\r\n    manualPagination: true,\r\n    onPaginationChange: setPagination,\r\n    onColumnVisibilityChange: setColumnVisibility,\r\n    onSortingChange: setSorting,\r\n    rowCount,\r\n    state: {\r\n      columnVisibility,\r\n      pagination,\r\n      sorting,\r\n    },\r\n  })\r\n\r\n  return (\r\n    <div className=\"flex flex-col gap-2\">\r\n      <div className=\"flex flex-row justify-between\">\r\n        <DataTableViewOptions table={table} />\r\n      </div>\r\n      <div className=\"rounded-md border\">\r\n        <Table>\r\n          <TableHeader>\r\n            {table.getHeaderGroups().map(headerGroup => (\r\n              <TableRow key={headerGroup.id}>\r\n                {headerGroup.headers.map(header => {\r\n                  return (\r\n                    <TableHead key={header.id}>\r\n                      {header.isPlaceholder\r\n                        ? null\r\n                        : flexRender(\r\n                            header.column.columnDef.header,\r\n                            header.getContext(),\r\n                          )}\r\n                    </TableHead>\r\n                  )\r\n                })}\r\n              </TableRow>\r\n            ))}\r\n          </TableHeader>\r\n          <TableBody>\r\n            {isLoading ? (\r\n              Array.from({ length: perPage }).map((_, rowIndex) => (\r\n                <TableRow key={`skeleton-${rowIndex}`}>\r\n                  {Array.from({ length: columns.length }).map((_, colIndex) => (\r\n                    <TableCell key={`skeleton-cell-${colIndex}`}>\r\n                      <div className=\"h-4 w-full animate-pulse bg-gray-200 rounded\" />\r\n                    </TableCell>\r\n                  ))}\r\n                </TableRow>\r\n              ))\r\n            ) : table.getRowModel().rows?.length ? (\r\n              table.getRowModel().rows.map(row => (\r\n                <TableRow\r\n                  key={row.id}\r\n                  data-state={row.getIsSelected() && 'selected'}\r\n                >\r\n                  {row.getVisibleCells().map(cell => (\r\n                    <TableCell key={cell.id}>\r\n                      {flexRender(\r\n                        cell.column.columnDef.cell,\r\n                        cell.getContext(),\r\n                      )}\r\n                    </TableCell>\r\n                  ))}\r\n                </TableRow>\r\n              ))\r\n            ) : (\r\n              <TableRow>\r\n                <TableCell\r\n                  colSpan={columns.length}\r\n                  className=\"h-24 text-center\"\r\n                >\r\n                  {t('status.no-data')}\r\n                </TableCell>\r\n              </TableRow>\r\n            )}\r\n          </TableBody>\r\n        </Table>\r\n      </div>\r\n      {!hidePagination && <DataTablePagination table={table} />}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAQA;AAAA;AAUA;AACA;;;AAvBA;;;;;;;AAoCO,SAAS,UAAyB,KAST;QATS,EACvC,OAAO,EACP,IAAI,EACJ,kBAAkB,EAClB,OAAO,CAAC,EACR,UAAU,EAAE,EACZ,QAAQ,EACR,SAAS,EACT,iBAAiB,KAAK,EACQ,GATS;QAmFzB;;IAzEd,MAAM,IAAI,IAAA,4NAAe,EAAC;IAE1B,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAkB;QAC5D,WAAW;QACX,UAAU;IACZ;IAEA,IAAA,0KAAS;+BAAC;YACR,IAAI,WAAW,SAAS,KAAK,QAAQ,WAAW,QAAQ,KAAK,SAAS;gBACpE,+BAAA,yCAAA,mBAAqB;oBACnB,MAAM,WAAW,SAAS,GAAG;oBAC7B,UAAU,WAAW,QAAQ;gBAC/B;YACF;QACF;8BAAG;QAAC;QAAY;QAAoB;QAAM;KAAQ;IAElD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAe,EAAE;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,yKAAQ,EAAkB,CAAC;IAE3E,MAAM,QAAQ,IAAA,0MAAa,EAAC;QAC1B;QACA;QACA,oBAAoB;QACpB,iBAAiB,IAAA,2LAAe;QAChC,mBAAmB,IAAA,6LAAiB;QACpC,kBAAkB;QAClB,oBAAoB;QACpB,0BAA0B;QAC1B,iBAAiB;QACjB;QACA,OAAO;YACL;YACA;YACA;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,oNAAoB;oBAAC,OAAO;;;;;;;;;;;0BAE/B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6IAAK;;sCACJ,6LAAC,mJAAW;sCACT,MAAM,eAAe,GAAG,GAAG,CAAC,CAAA,4BAC3B,6LAAC,gJAAQ;8CACN,YAAY,OAAO,CAAC,GAAG,CAAC,CAAA;wCACvB,qBACE,6LAAC,iJAAS;sDACP,OAAO,aAAa,GACjB,OACA,IAAA,uMAAU,EACR,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;2CALT,OAAO,EAAE;;;;;oCAS7B;mCAZa,YAAY,EAAE;;;;;;;;;;sCAgBjC,6LAAC,iJAAS;sCACP,YACC,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAQ,GAAG,GAAG,CAAC,CAAC,GAAG,yBACtC,6LAAC,gJAAQ;8CACN,MAAM,IAAI,CAAC;wCAAE,QAAQ,QAAQ,MAAM;oCAAC,GAAG,GAAG,CAAC,CAAC,GAAG,yBAC9C,6LAAC,iJAAS;sDACR,cAAA,6LAAC;gDAAI,WAAU;;;;;;2CADD,AAAC,iBAAyB,OAAT;;;;;mCAFtB,AAAC,YAAoB,OAAT;;;;4CAQ3B,EAAA,0BAAA,MAAM,WAAW,GAAG,IAAI,cAAxB,8CAAA,wBAA0B,MAAM,IAClC,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA,oBAC3B,6LAAC,gJAAQ;oCAEP,cAAY,IAAI,aAAa,MAAM;8CAElC,IAAI,eAAe,GAAG,GAAG,CAAC,CAAA,qBACzB,6LAAC,iJAAS;sDACP,IAAA,uMAAU,EACT,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;2CAHH,KAAK,EAAE;;;;;mCAJpB,IAAI,EAAE;;;;0DAcf,6LAAC,gJAAQ;0CACP,cAAA,6LAAC,iJAAS;oCACR,SAAS,QAAQ,MAAM;oCACvB,WAAU;8CAET,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOd,CAAC,gCAAkB,6LAAC,+MAAmB;gBAAC,OAAO;;;;;;;;;;;;AAGtD;GAnHgB;;QAUJ,4NAAe;QAmBX,0MAAa;;;KA7Bb", "debugId": null}}, {"offset": {"line": 1061, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/label.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { cn } from '@atlas/lib/utils'\r\nimport * as LabelPrimitive from '@radix-ui/react-label'\r\nimport type * as React from 'react'\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        'flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAMA,SAAS,MAAM,KAGoC;QAHpC,EACb,SAAS,EACT,GAAG,OAC8C,GAHpC;IAIb,qBACE,6LAAC,4KAAmB;QAClB,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,uNACA;QAED,GAAG,KAAK;;;;;;AAGf;KAdS", "debugId": null}}, {"offset": {"line": 1095, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/form.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { Label } from '@atlas/components/ui/label'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type * as LabelPrimitive from '@radix-ui/react-label'\r\nimport { Slot } from '@radix-ui/react-slot'\r\nimport * as React from 'react'\r\nimport {\r\n  Controller,\r\n  type ControllerProps,\r\n  type FieldPath,\r\n  type FieldValues,\r\n  FormProvider,\r\n  useFormContext,\r\n  useFormState,\r\n} from 'react-hook-form'\r\n\r\nconst Form = FormProvider\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n> = {\r\n  name: TName\r\n}\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\r\n  {} as FormFieldContextValue,\r\n)\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  )\r\n}\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext)\r\n  const itemContext = React.useContext(FormItemContext)\r\n  const { getFieldState } = useFormContext()\r\n  const formState = useFormState({ name: fieldContext.name })\r\n  const fieldState = getFieldState(fieldContext.name, formState)\r\n\r\n  if (!fieldContext) {\r\n    throw new Error('useFormField should be used within <FormField>')\r\n  }\r\n\r\n  const { id } = itemContext\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  }\r\n}\r\n\r\ntype FormItemContextValue = {\r\n  id: string\r\n}\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>(\r\n  {} as FormItemContextValue,\r\n)\r\n\r\nfunction FormItem({ className, ...props }: React.ComponentProps<'div'>) {\r\n  const id = React.useId()\r\n\r\n  return (\r\n    <FormItemContext.Provider value={{ id }}>\r\n      <div\r\n        data-slot=\"form-item\"\r\n        className={cn('grid gap-2', className)}\r\n        {...props}\r\n      />\r\n    </FormItemContext.Provider>\r\n  )\r\n}\r\n\r\nfunction FormLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  const { error, formItemId } = useFormField()\r\n\r\n  return (\r\n    <Label\r\n      data-slot=\"form-label\"\r\n      data-error={!!error}\r\n      className={cn('data-[error=true]:text-destructive', className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\r\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\r\n\r\n  return (\r\n    <Slot\r\n      data-slot=\"form-control\"\r\n      id={formItemId}\r\n      aria-describedby={\r\n        !error\r\n          ? `${formDescriptionId}`\r\n          : `${formDescriptionId} ${formMessageId}`\r\n      }\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormDescription({ className, ...props }: React.ComponentProps<'p'>) {\r\n  const { formDescriptionId } = useFormField()\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-description\"\r\n      id={formDescriptionId}\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormMessage({ className, ...props }: React.ComponentProps<'p'>) {\r\n  const { error, formMessageId } = useFormField()\r\n  const body = error ? String(error?.message ?? '') : props.children\r\n\r\n  if (!body) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-message\"\r\n      id={formMessageId}\r\n      className={cn('text-destructive text-sm', className)}\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  )\r\n}\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAEA;AACA;AAEA;AACA;AACA;;;AAPA;;;;;;AAiBA,MAAM,OAAO,iLAAY;AASzB,MAAM,iCAAmB,8KAAmB,CAC1C,CAAC;AAGH,MAAM,YAAY;QAGhB,EACA,GAAG,OACkC;IACrC,qBACE,6LAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,6LAAC,+KAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;KAXM;AAaN,MAAM,eAAe;;IACnB,MAAM,eAAe,2KAAgB,CAAC;IACtC,MAAM,cAAc,2KAAgB,CAAC;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,IAAA,mLAAc;IACxC,MAAM,YAAY,IAAA,iLAAY,EAAC;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,AAAC,GAAK,OAAH,IAAG;QAClB,mBAAmB,AAAC,GAAK,OAAH,IAAG;QACzB,eAAe,AAAC,GAAK,OAAH,IAAG;QACrB,GAAG,UAAU;IACf;AACF;GArBM;;QAGsB,mLAAc;QACtB,iLAAY;;;AAuBhC,MAAM,gCAAkB,8KAAmB,CACzC,CAAC;AAGH,SAAS,SAAS,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;;IAChB,MAAM,KAAK,sKAAW;IAEtB,qBACE,6LAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,IAAA,4HAAE,EAAC,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;IAZS;MAAA;AAcT,SAAS,UAAU,KAGgC;QAHhC,EACjB,SAAS,EACT,GAAG,OAC8C,GAHhC;;IAIjB,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,6LAAC,6IAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,IAAA,4HAAE,EAAC,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;IAfS;;QAIuB;;;MAJvB;AAiBT,SAAS,YAAY,KAA+C;QAA/C,EAAE,GAAG,OAA0C,GAA/C;;IACnB,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,6LAAC,2KAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,AAAC,GAAoB,OAAlB,qBACH,AAAC,GAAuB,OAArB,mBAAkB,KAAiB,OAAd;QAE9B,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;IAhBS;;QACyD;;;MADzD;AAkBT,SAAS,gBAAgB,KAAkD;QAAlD,EAAE,SAAS,EAAE,GAAG,OAAkC,GAAlD;;IACvB,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,IAAA,4HAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;IAXS;;QACuB;;;MADvB;AAaT,SAAS,YAAY,KAAkD;QAAlD,EAAE,SAAS,EAAE,GAAG,OAAkC,GAAlD;;IACnB,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;QACL;IAA5B,MAAM,OAAO,QAAQ,OAAO,CAAA,iBAAA,kBAAA,4BAAA,MAAO,OAAO,cAAd,4BAAA,iBAAkB,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,IAAA,4HAAE,EAAC,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP;IAlBS;;QAC0B;;;MAD1B", "debugId": null}}, {"offset": {"line": 1310, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/projects/project-detail/modules/create-module-type-selector.tsx"], "sourcesContent": ["import {\r\n  FormControl,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n} from '@atlas/components/ui/form'\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@atlas/components/ui/select'\r\nimport { categorizedModuleTypes } from '@atlas/constants/module'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { NewModuleForm } from '@atlas/types/schemas/module-form'\r\nimport { useTranslations } from 'next-intl'\r\nimport type { Control } from 'react-hook-form'\r\n\r\ntype Props = { control: Control<NewModuleForm> }\r\n\r\nexport const CreateModuleTypeSelector = ({ control }: Props) => {\r\n  const t = useTranslations('forms.create-module.fields')\r\n  const tModuleType = useTranslations('enums.module-type')\r\n\r\n  return (\r\n    <FormField\r\n      control={control}\r\n      name=\"type\"\r\n      render={({ field, fieldState }) => (\r\n        <FormItem>\r\n          <FormLabel>\r\n            {t(`${field.name}.label`)}\r\n            <p className=\"text-red-600\">*</p>\r\n          </FormLabel>\r\n          <FormControl>\r\n            <Select {...field} onValueChange={field.onChange}>\r\n              <SelectTrigger className=\"w-full\">\r\n                <SelectValue placeholder={t(`${field.name}.placeholder`)} />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                {categorizedModuleTypes\r\n                  .filter(e => e.enabled)\r\n                  .map(cat => (\r\n                    <SelectGroup key={cat.category}>\r\n                      <SelectLabel>{t(`category.${cat.category}`)}</SelectLabel>\r\n                      {cat.types\r\n                        .filter(e => e.enabled)\r\n                        .map(({ name }) => (\r\n                          <SelectItem value={name} key={name}>\r\n                            {tModuleType(`${name}`)}\r\n                          </SelectItem>\r\n                        ))}\r\n                    </SelectGroup>\r\n                  ))}\r\n              </SelectContent>\r\n            </Select>\r\n          </FormControl>\r\n          {fieldState?.error?.message && (\r\n            <p className={cn('text-destructive text-sm')}>\r\n              {t(`error.${fieldState?.error?.message}`)}\r\n            </p>\r\n          )}\r\n        </FormItem>\r\n      )}\r\n    />\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAMA;AASA;AACA;AAEA;;;;;;;;AAKO,MAAM,2BAA2B;QAAC,EAAE,OAAO,EAAS;;IACzD,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,cAAc,IAAA,4NAAe,EAAC;IAEpC,qBACE,6LAAC,gJAAS;QACR,SAAS;QACT,MAAK;QACL,QAAQ;gBAAC,EAAE,KAAK,EAAE,UAAU,EAAE;gBA6BzB,mBAEe;iCA9BlB,6LAAC,+IAAQ;;kCACP,6LAAC,gJAAS;;4BACP,EAAE,AAAC,GAAa,OAAX,MAAM,IAAI,EAAC;0CACjB,6LAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;kCAE9B,6LAAC,kJAAW;kCACV,cAAA,6LAAC,+IAAM;4BAAE,GAAG,KAAK;4BAAE,eAAe,MAAM,QAAQ;;8CAC9C,6LAAC,sJAAa;oCAAC,WAAU;8CACvB,cAAA,6LAAC,oJAAW;wCAAC,aAAa,EAAE,AAAC,GAAa,OAAX,MAAM,IAAI,EAAC;;;;;;;;;;;8CAE5C,6LAAC,sJAAa;8CACX,wJAAsB,CACpB,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,EACrB,GAAG,CAAC,CAAA,oBACH,6LAAC,oJAAW;;8DACV,6LAAC,oJAAW;8DAAE,EAAE,AAAC,YAAwB,OAAb,IAAI,QAAQ;;;;;;gDACvC,IAAI,KAAK,CACP,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,EACrB,GAAG,CAAC;wDAAC,EAAE,IAAI,EAAE;yEACZ,6LAAC,mJAAU;wDAAC,OAAO;kEAChB,YAAY,AAAC,GAAO,OAAL;uDADY;;;;;;;2CALlB,IAAI,QAAQ;;;;;;;;;;;;;;;;;;;;;oBAcvC,CAAA,uBAAA,kCAAA,oBAAA,WAAY,KAAK,cAAjB,wCAAA,kBAAmB,OAAO,mBACzB,6LAAC;wBAAE,WAAW,IAAA,4HAAE,EAAC;kCACd,EAAE,AAAC,SAAmC,OAA3B,uBAAA,kCAAA,qBAAA,WAAY,KAAK,cAAjB,yCAAA,mBAAmB,OAAO;;;;;;;;;;;;;;;;;;AAOpD;GA9Ca;;QACD,4NAAe;QACL,4NAAe;;;KAFxB", "debugId": null}}, {"offset": {"line": 1456, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/form/string-form-input.tsx"], "sourcesContent": ["import {\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  useFormField,\r\n} from '@atlas/components/ui/form'\r\nimport { Input } from '@atlas/components/ui/input'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { Control, FieldPath, FieldValues } from 'react-hook-form'\r\n\r\ntype Props<T extends FieldValues> = {\r\n  control: Control<T>\r\n  description?: boolean\r\n  disabled?: boolean\r\n  name: FieldPath<T>\r\n  required?: boolean\r\n  t: (message: string) => string\r\n}\r\n\r\nexport const StringFormInput = <T extends FieldValues>({\r\n  control,\r\n  description = false,\r\n  disabled = false,\r\n  name,\r\n  required = false,\r\n  t,\r\n}: Props<T>) => {\r\n  const { error } = useFormField()\r\n\r\n  return (\r\n    <FormField\r\n      control={control}\r\n      name={name}\r\n      render={({ field }) => (\r\n        <FormItem>\r\n          <FormLabel>\r\n            {t(`${name}.label`)}\r\n            {required && <p className=\"text-red-600\">*</p>}\r\n          </FormLabel>\r\n          <FormControl>\r\n            <Input\r\n              placeholder={t(`${name}.placeholder`)}\r\n              disabled={disabled}\r\n              {...field}\r\n            />\r\n          </FormControl>\r\n          {description && (\r\n            <FormDescription>{t(`${name}.description`)}</FormDescription>\r\n          )}\r\n          {error?.message && (\r\n            <p className={cn('text-destructive text-sm')}>\r\n              {t(`error.${error}`)}\r\n            </p>\r\n          )}\r\n        </FormItem>\r\n      )}\r\n    />\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAQA;AACA;;;;;;AAYO,MAAM,kBAAkB;QAAwB,EACrD,OAAO,EACP,cAAc,KAAK,EACnB,WAAW,KAAK,EAChB,IAAI,EACJ,WAAW,KAAK,EAChB,CAAC,EACQ;;IACT,MAAM,EAAE,KAAK,EAAE,GAAG,IAAA,mJAAY;IAE9B,qBACE,6LAAC,gJAAS;QACR,SAAS;QACT,MAAM;QACN,QAAQ;gBAAC,EAAE,KAAK,EAAE;iCAChB,6LAAC,+IAAQ;;kCACP,6LAAC,gJAAS;;4BACP,EAAE,AAAC,GAAO,OAAL,MAAK;4BACV,0BAAY,6LAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;kCAE3C,6LAAC,kJAAW;kCACV,cAAA,6LAAC,6IAAK;4BACJ,aAAa,EAAE,AAAC,GAAO,OAAL,MAAK;4BACvB,UAAU;4BACT,GAAG,KAAK;;;;;;;;;;;oBAGZ,6BACC,6LAAC,sJAAe;kCAAE,EAAE,AAAC,GAAO,OAAL,MAAK;;;;;;oBAE7B,CAAA,kBAAA,4BAAA,MAAO,OAAO,mBACb,6LAAC;wBAAE,WAAW,IAAA,4HAAE,EAAC;kCACd,EAAE,AAAC,SAAc,OAAN;;;;;;;;;;;;;;;;;;AAO1B;GAvCa;;QAQO,mJAAY;;;KARnB", "debugId": null}}, {"offset": {"line": 1555, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { buttonVariants } from '@atlas/components/ui/button'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport * as AlertDialogPrimitive from '@radix-ui/react-alert-dialog'\r\nimport type * as React from 'react'\r\n\r\nfunction AlertDialog({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {\r\n  return <AlertDialogPrimitive.Root data-slot=\"alert-dialog\" {...props} />\r\n}\r\n\r\nfunction AlertDialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {\r\n  return (\r\n    <AlertDialogPrimitive.Trigger data-slot=\"alert-dialog-trigger\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {\r\n  return (\r\n    <AlertDialogPrimitive.Portal data-slot=\"alert-dialog-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {\r\n  return (\r\n    <AlertDialogPrimitive.Overlay\r\n      data-slot=\"alert-dialog-overlay\"\r\n      className={cn(\r\n        'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {\r\n  return (\r\n    <AlertDialogPortal>\r\n      <AlertDialogOverlay />\r\n      <AlertDialogPrimitive.Content\r\n        data-slot=\"alert-dialog-content\"\r\n        className={cn(\r\n          'bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg',\r\n          className,\r\n        )}\r\n        {...props}\r\n      />\r\n    </AlertDialogPortal>\r\n  )\r\n}\r\n\r\nfunction AlertDialogHeader({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-header\"\r\n      className={cn('flex flex-col gap-2 text-center sm:text-left', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogFooter({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-footer\"\r\n      className={cn(\r\n        'flex flex-col-reverse gap-2 sm:flex-row sm:justify-end',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {\r\n  return (\r\n    <AlertDialogPrimitive.Title\r\n      data-slot=\"alert-dialog-title\"\r\n      className={cn('text-lg font-semibold', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {\r\n  return (\r\n    <AlertDialogPrimitive.Description\r\n      data-slot=\"alert-dialog-description\"\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogAction({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {\r\n  return (\r\n    <AlertDialogPrimitive.Action\r\n      className={cn(buttonVariants(), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogCancel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {\r\n  return (\r\n    <AlertDialogPrimitive.Cancel\r\n      className={cn(buttonVariants({ variant: 'outline' }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAOA,SAAS,YAAY,KAEoC;QAFpC,EACnB,GAAG,OACoD,GAFpC;IAGnB,qBAAO,6LAAC,sLAAyB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACtE;KAJS;AAMT,SAAS,mBAAmB,KAEgC;QAFhC,EAC1B,GAAG,OACuD,GAFhC;IAG1B,qBACE,6LAAC,yLAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,kBAAkB,KAEgC;QAFhC,EACzB,GAAG,OACsD,GAFhC;IAGzB,qBACE,6LAAC,wLAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,mBAAmB,KAGgC;QAHhC,EAC1B,SAAS,EACT,GAAG,OACuD,GAHhC;IAI1B,qBACE,6LAAC,yLAA4B;QAC3B,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,mBAAmB,KAGgC;QAHhC,EAC1B,SAAS,EACT,GAAG,OACuD,GAHhC;IAI1B,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,yLAA4B;gBAC3B,aAAU;gBACV,WAAW,IAAA,4HAAE,EACX,+WACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;MAjBS;AAmBT,SAAS,kBAAkB,KAGG;QAHH,EACzB,SAAS,EACT,GAAG,OACyB,GAHH;IAIzB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,KAGG;QAHH,EACzB,SAAS,EACT,GAAG,OACyB,GAHH;IAIzB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,iBAAiB,KAGgC;QAHhC,EACxB,SAAS,EACT,GAAG,OACqD,GAHhC;IAIxB,qBACE,6LAAC,uLAA0B;QACzB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,6LAAgC;QAC/B,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,KAGgC;QAHhC,EACzB,SAAS,EACT,GAAG,OACsD,GAHhC;IAIzB,qBACE,6LAAC,wLAA2B;QAC1B,WAAW,IAAA,4HAAE,EAAC,IAAA,uJAAc,KAAI;QAC/B,GAAG,KAAK;;;;;;AAGf;MAVS;AAYT,SAAS,kBAAkB,KAGgC;QAHhC,EACzB,SAAS,EACT,GAAG,OACsD,GAHhC;IAIzB,qBACE,6LAAC,wLAA2B;QAC1B,WAAW,IAAA,4HAAE,EAAC,IAAA,uJAAc,EAAC;YAAE,SAAS;QAAU,IAAI;QACrD,GAAG,KAAK;;;;;;AAGf;OAVS", "debugId": null}}, {"offset": {"line": 1761, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/functions/forms/on-enter-key.ts"], "sourcesContent": ["import type { KeyboardEventHandler } from 'react'\r\n\r\nexport const onEnterKey =\r\n  (fn: () => void): KeyboardEventHandler<HTMLFormElement> =>\r\n  e => {\r\n    if (e.key === 'Enter' && !e.shiftKey && !(e as any).isComposing) {\r\n      e.preventDefault()\r\n      fn()\r\n    }\r\n  }\r\n"], "names": [], "mappings": ";;;;AAEO,MAAM,aACX,CAAC,KACD,CAAA;QACE,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,IAAI,CAAC,AAAC,EAAU,WAAW,EAAE;YAC/D,EAAE,cAAc;YAChB;QACF;IACF", "debugId": null}}, {"offset": {"line": 1778, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/api/modules/endpoints/post-module.ts"], "sourcesContent": ["import { handleApiError } from '@atlas/lib/api/apiErrors'\r\nimport {\r\n  type Module,\r\n  moduleSchema,\r\n} from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { ApiError, ValidationError } from '@atlas/types'\r\nimport type { NewModuleForm } from '@atlas/types/schemas/module-form'\r\nimport type { AxiosResponse } from 'axios'\r\nimport { pipe } from 'fp-ts/function'\r\nimport { map, type TaskEither, tryCatch } from 'fp-ts/TaskEither'\r\nimport { apiClient } from '../../httpClient'\r\n\r\nconst validate = (data: unknown): Module => {\r\n  const parse = moduleSchema.safeParse(data)\r\n  if (!parse.success) {\r\n    throw {\r\n      type: 'ValidationError',\r\n      code: 'MALFORMED_RESPONSE',\r\n      message: parse.error.message,\r\n    } as ValidationError\r\n  }\r\n  return parse.data\r\n}\r\n\r\nconst post = (\r\n  token: string,\r\n  projectId: Project['id'],\r\n  body: NewModuleForm,\r\n): TaskEither<ApiError, AxiosResponse> =>\r\n  tryCatch(\r\n    () =>\r\n      apiClient.post(`/api/v2/projects/${projectId}/modules`, body, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      }),\r\n    e => handleApiError(e),\r\n  )\r\n\r\nexport const postModule = (\r\n  token: string,\r\n  projectId: Project['id'],\r\n  body: NewModuleForm,\r\n): TaskEither<ApiError | ValidationError, Module> =>\r\n  pipe(\r\n    post(token, projectId, body),\r\n    map(a => a.data),\r\n    map(validate),\r\n  )\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAQA;AACA;AACA;;;;;;AAEA,MAAM,WAAW,CAAC;IAChB,MAAM,QAAQ,qKAAY,CAAC,SAAS,CAAC;IACrC,IAAI,CAAC,MAAM,OAAO,EAAE;QAClB,MAAM;YACJ,MAAM;YACN,MAAM;YACN,SAAS,MAAM,KAAK,CAAC,OAAO;QAC9B;IACF;IACA,OAAO,MAAM,IAAI;AACnB;AAEA,MAAM,OAAO,CACX,OACA,WACA,OAEA,IAAA,4JAAQ,EACN,IACE,+IAAS,CAAC,IAAI,CAAC,AAAC,oBAA6B,OAAV,WAAU,aAAW,MAAM;YAC5D,SAAS;gBAAE,eAAe,AAAC,UAAe,OAAN;YAAQ;QAC9C,IACF,CAAA,IAAK,IAAA,mJAAc,EAAC;AAGjB,MAAM,aAAa,CACxB,OACA,WACA,OAEA,IAAA,sJAAI,EACF,KAAK,OAAO,WAAW,OACvB,IAAA,uJAAG,EAAC,CAAA,IAAK,EAAE,IAAI,GACf,IAAA,uJAAG,EAAC", "debugId": null}}, {"offset": {"line": 1816, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/mutation/modules/use-new-module-mutation.tsx"], "sourcesContent": ["import { postModule } from '@atlas/lib/api/modules/endpoints/post-module'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { ApiError, ValidationError } from '@atlas/types'\r\nimport type { NewModuleForm } from '@atlas/types/schemas/module-form'\r\nimport {\r\n  type UseMutationOptions,\r\n  useMutation,\r\n  useQueryClient,\r\n} from '@tanstack/react-query'\r\nimport * as E from 'fp-ts/Either'\r\nimport { pipe } from 'fp-ts/function'\r\n\r\ntype PostModuleError = ApiError | ValidationError\r\ntype PostModuleData = Module\r\n\r\ntype PostModuleVariables = {\r\n  projectId: Project['id']\r\n  body: NewModuleForm\r\n}\r\n\r\nconst mutationFn =\r\n  (token: string) =>\r\n  async (variables: PostModuleVariables): Promise<PostModuleData> => {\r\n    const result = await postModule(\r\n      token,\r\n      variables.projectId,\r\n      variables.body,\r\n    )()\r\n\r\n    return pipe(\r\n      result,\r\n      E.match(\r\n        (error: PostModuleError) => {\r\n          throw error\r\n        },\r\n        (project: PostModuleData) => {\r\n          return project\r\n        },\r\n      ),\r\n    )\r\n  }\r\n\r\nexport const useNewModuleMutation = (\r\n  token: string,\r\n  options?: Omit<\r\n    UseMutationOptions<PostModuleData, PostModuleError, PostModuleVariables>,\r\n    'mutationFn'\r\n  >,\r\n) => {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation<PostModuleData, PostModuleError, PostModuleVariables>({\r\n    ...options,\r\n    mutationFn: mutationFn(token),\r\n    onSuccess: (data, variables, context) => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: ['modules', { projectId: variables.projectId }],\r\n      })\r\n      options?.onSuccess?.(data, variables, context)\r\n    },\r\n  })\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAKA;AAAA;AAKA;AACA;;;;;;AAUA,MAAM,aACJ,CAAC,QACD,OAAO;QACL,MAAM,SAAS,MAAM,IAAA,4KAAU,EAC7B,OACA,UAAU,SAAS,EACnB,UAAU,IAAI;QAGhB,OAAO,IAAA,sJAAI,EACT,QACA,qJAAO,CACL,CAAC;YACC,MAAM;QACR,GACA,CAAC;YACC,OAAO;QACT;IAGN;AAEK,MAAM,uBAAuB,CAClC,OACA;;IAKA,MAAM,cAAc,IAAA,2MAAc;IAElC,OAAO,IAAA,gMAAW,EAAuD;QACvE,GAAG,OAAO;QACV,YAAY,WAAW;QACvB,SAAS;gDAAE,CAAC,MAAM,WAAW;oBAI3B;gBAHA,YAAY,iBAAiB,CAAC;oBAC5B,UAAU;wBAAC;wBAAW;4BAAE,WAAW,UAAU,SAAS;wBAAC;qBAAE;gBAC3D;gBACA,oBAAA,+BAAA,qBAAA,QAAS,SAAS,cAAlB,yCAAA,wBAAA,SAAqB,MAAM,WAAW;YACxC;;IACF;AACF;GAnBa;;QAOS,2MAAc;QAE3B,gMAAW", "debugId": null}}, {"offset": {"line": 1873, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/types/schemas/module-form.ts"], "sourcesContent": ["import { MODULE_TYPES } from '@atlas/constants/module'\r\nimport { z } from 'zod'\r\n\r\nexport const newModuleFormSchema = z.object({\r\n  name: z.string().min(1),\r\n  description: z.string().optional(),\r\n  type: z.enum(MODULE_TYPES),\r\n})\r\n\r\nexport type NewModuleForm = z.infer<typeof newModuleFormSchema>\r\n\r\nexport const editModuleFormSchema = z.object({\r\n  name: z.string().min(1),\r\n  description: z.string().optional(),\r\n})\r\n\r\nexport type EditModuleForm = z.infer<typeof editModuleFormSchema>\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,MAAM,sBAAsB,qLAAC,CAAC,MAAM,CAAC;IAC1C,MAAM,qLAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACrB,aAAa,qLAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,MAAM,qLAAC,CAAC,IAAI,CAAC,8IAAY;AAC3B;AAIO,MAAM,uBAAuB,qLAAC,CAAC,MAAM,CAAC;IAC3C,MAAM,qLAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACrB,aAAa,qLAAC,CAAC,MAAM,GAAG,QAAQ;AAClC", "debugId": null}}, {"offset": {"line": 1899, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/projects/project-detail/modules/create-module-modal.tsx"], "sourcesContent": ["import { CreateModuleTypeSelector } from '@atlas/components/common/atlas/projects/project-detail/modules/create-module-type-selector'\r\nimport { createModuleModalOpenAtom } from '@atlas/components/common/atlas/projects/project-detail/modules/project-modules-section'\r\nimport { StringFormInput } from '@atlas/components/common/form/string-form-input'\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@atlas/components/ui/alert-dialog'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { onEnterKey } from '@atlas/functions/forms/on-enter-key'\r\nimport { useRouter } from '@atlas/i18n/routing'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useNewModuleMutation } from '@atlas/lib/mutation/modules/use-new-module-mutation'\r\nimport {\r\n  type NewModuleForm,\r\n  newModuleFormSchema,\r\n} from '@atlas/types/schemas/module-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useAtom } from 'jotai/index'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  project: Project\r\n  defaultValues?: Partial<NewModuleForm>\r\n}\r\n\r\nexport const CreateModuleModal = ({\r\n  session,\r\n  project,\r\n  defaultValues,\r\n}: Props) => {\r\n  const t = useTranslations('components.modules.create-modal')\r\n  const tAction = useTranslations('actions.create-module.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const tFields = useTranslations('forms.create-module.fields')\r\n  const [open, onOpenChange] = useAtom(createModuleModalOpenAtom)\r\n  const router = useRouter()\r\n  const form = useForm<NewModuleForm>({\r\n    resolver: zodResolver(newModuleFormSchema),\r\n    defaultValues: {\r\n      name: defaultValues?.name ?? '',\r\n      description: defaultValues?.description ?? '',\r\n      type: defaultValues?.type ?? 'RECTANGULAR_BEAM',\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useNewModuleMutation(session.accessToken, {\r\n    onSuccess: module => {\r\n      router.push(`/dashboard/projects/${project.id}/modules/${module.id}`)\r\n      toast.success(tAction('create.success'))\r\n    },\r\n    onError: error => {\r\n      toast.error(tAction('create.failure', { error: error.message }))\r\n    },\r\n  })\r\n\r\n  const onSubmit = (body: NewModuleForm) => {\r\n    mutate({ projectId: project.id, body })\r\n  }\r\n\r\n  return (\r\n    <AlertDialog open={open} onOpenChange={onOpenChange}>\r\n      <AlertDialogContent>\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>{t('title')}</AlertDialogTitle>\r\n          <AlertDialogDescription>\r\n            {t('description', {\r\n              projectName: project.constructionSiteName ?? '',\r\n            })}\r\n          </AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <Form {...form}>\r\n          <form\r\n            onSubmit={form.handleSubmit(onSubmit)}\r\n            onKeyDown={onEnterKey(form.handleSubmit(onSubmit))}\r\n            className=\"space-y-6\"\r\n          >\r\n            <StringFormInput\r\n              required\r\n              control={form.control}\r\n              name=\"name\"\r\n              t={tFields}\r\n            />\r\n            <StringFormInput\r\n              control={form.control}\r\n              name=\"description\"\r\n              t={tFields}\r\n            />\r\n            <CreateModuleTypeSelector control={form.control} />\r\n            <div className=\"flex justify-end gap-1\">\r\n              <Button\r\n                variant=\"secondary\"\r\n                className=\"w-30\"\r\n                onClick={() => onOpenChange(false)}\r\n              >\r\n                {tCommon('cancel')}\r\n              </Button>\r\n              <Button type=\"submit\" className=\"w-30\" disabled={isPending}>\r\n                {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n                {tCommon('create')}\r\n              </Button>\r\n            </div>\r\n          </form>\r\n        </Form>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AAEA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;;;;;;;;;;;;;;;;;;;AAQO,MAAM,oBAAoB;QAAC,EAChC,OAAO,EACP,OAAO,EACP,aAAa,EACP;;IACN,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,CAAC,MAAM,aAAa,GAAG,IAAA,oJAAO,EAAC,kPAAyB;IAC9D,MAAM,SAAS,IAAA,sIAAS;QAId,qBACO,4BACP;IALV,MAAM,OAAO,IAAA,4KAAO,EAAgB;QAClC,UAAU,IAAA,gLAAW,EAAC,mKAAmB;QACzC,eAAe;YACb,MAAM,CAAA,sBAAA,0BAAA,oCAAA,cAAe,IAAI,cAAnB,iCAAA,sBAAuB;YAC7B,aAAa,CAAA,6BAAA,0BAAA,oCAAA,cAAe,WAAW,cAA1B,wCAAA,6BAA8B;YAC3C,MAAM,CAAA,sBAAA,0BAAA,oCAAA,cAAe,IAAI,cAAnB,iCAAA,sBAAuB;QAC/B;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,iMAAoB,EAAC,QAAQ,WAAW,EAAE;QACtE,SAAS;sDAAE,CAAA;gBACT,OAAO,IAAI,CAAC,AAAC,uBAA4C,OAAtB,QAAQ,EAAE,EAAC,aAAqB,OAAV,OAAO,EAAE;gBAClE,oJAAK,CAAC,OAAO,CAAC,QAAQ;YACxB;;QACA,OAAO;sDAAE,CAAA;gBACP,oJAAK,CAAC,KAAK,CAAC,QAAQ,kBAAkB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAC/D;;IACF;IAEA,MAAM,WAAW,CAAC;QAChB,OAAO;YAAE,WAAW,QAAQ,EAAE;YAAE;QAAK;IACvC;QASyB;IAPzB,qBACE,6LAAC,6JAAW;QAAC,MAAM;QAAM,cAAc;kBACrC,cAAA,6LAAC,oKAAkB;;8BACjB,6LAAC,mKAAiB;;sCAChB,6LAAC,kKAAgB;sCAAE,EAAE;;;;;;sCACrB,6LAAC,wKAAsB;sCACpB,EAAE,eAAe;gCAChB,aAAa,CAAA,gCAAA,QAAQ,oBAAoB,cAA5B,2CAAA,gCAAgC;4BAC/C;;;;;;;;;;;;8BAGJ,6LAAC,2IAAI;oBAAE,GAAG,IAAI;8BACZ,cAAA,6LAAC;wBACC,UAAU,KAAK,YAAY,CAAC;wBAC5B,WAAW,IAAA,gKAAU,EAAC,KAAK,YAAY,CAAC;wBACxC,WAAU;;0CAEV,6LAAC,qLAAe;gCACd,QAAQ;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,GAAG;;;;;;0CAEL,6LAAC,qLAAe;gCACd,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,GAAG;;;;;;0CAEL,6LAAC,wPAAwB;gCAAC,SAAS,KAAK,OAAO;;;;;;0CAC/C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+IAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS,IAAM,aAAa;kDAE3B,QAAQ;;;;;;kDAEX,6LAAC,+IAAM;wCAAC,MAAK;wCAAS,WAAU;wCAAO,UAAU;;4CAC9C,2BAAa,6LAAC,uOAAW;gDAAC,WAAU;;;;;;4CACpC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzB;GAjFa;;QAKD,4NAAe;QACT,4NAAe;QACf,4NAAe;QACf,4NAAe;QACF,oJAAO;QACrB,sIAAS;QACX,4KAAO;QASU,iMAAoB;;;KApBvC", "debugId": null}}, {"offset": {"line": 2123, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/projects/project-detail/modules/use-module-pagination.tsx"], "sourcesContent": ["import type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport { useMemo, useState } from 'react'\r\n\r\ntype Props = {\r\n  modules: Module[]\r\n}\r\n\r\nexport const useModulePagination = ({ modules }: Props) => {\r\n  const [page, setPage] = useState(0)\r\n  const [pageSize, setPageSize] = useState(10)\r\n\r\n  const currentPageModules: Module[] = useMemo(() => {\r\n    const start = page * pageSize\r\n    return modules.slice(start, start + pageSize)\r\n  }, [modules, page, pageSize])\r\n\r\n  const onPaginationChange = ({\r\n    page,\r\n    pageSize,\r\n  }: {\r\n    page: number\r\n    pageSize: number\r\n  }) => {\r\n    setPage(page - 1)\r\n    setPageSize(pageSize)\r\n  }\r\n\r\n  return {\r\n    page,\r\n    pageSize,\r\n    onPaginationChange,\r\n    currentPageModules,\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;;;AAMO,MAAM,sBAAsB;QAAC,EAAE,OAAO,EAAS;;IACpD,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,yKAAQ,EAAC;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAC;IAEzC,MAAM,qBAA+B,IAAA,wKAAO;2DAAC;YAC3C,MAAM,QAAQ,OAAO;YACrB,OAAO,QAAQ,KAAK,CAAC,OAAO,QAAQ;QACtC;0DAAG;QAAC;QAAS;QAAM;KAAS;IAE5B,MAAM,qBAAqB;YAAC,EAC1B,IAAI,EACJ,QAAQ,EAIT;QACC,QAAQ,OAAO;QACf,YAAY;IACd;IAEA,OAAO;QACL;QACA;QACA;QACA;IACF;AACF;GA1Ba", "debugId": null}}, {"offset": {"line": 2165, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/data-table/data-table-column-header.tsx"], "sourcesContent": ["import {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from '@atlas//components/ui/dropdown-menu'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { Column } from '@tanstack/react-table'\r\nimport { ArrowDown, ArrowUp, ChevronsUpDown, EyeOff } from 'lucide-react'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ninterface DataTableColumnHeaderProps<TData, TValue>\r\n  extends React.HTMLAttributes<HTMLDivElement> {\r\n  column: Column<TData, TValue>\r\n  title: string\r\n}\r\n\r\nexport function DataTableColumnHeader<TData, TValue>({\r\n  column,\r\n  title,\r\n  className,\r\n}: DataTableColumnHeaderProps<TData, TValue>) {\r\n  const t = useTranslations('components.data-table.column-header')\r\n\r\n  if (!column.getCanSort()) {\r\n    return <div className={cn(className)}>{title}</div>\r\n  }\r\n\r\n  return (\r\n    <div className={cn('flex items-center space-x-2', className)}>\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            className=\"-ml-3 h-8 data-[state=open]:bg-accent\"\r\n          >\r\n            <span>{title}</span>\r\n            {column.getIsSorted() === 'desc' ? (\r\n              <ArrowDown />\r\n            ) : column.getIsSorted() === 'asc' ? (\r\n              <ArrowUp />\r\n            ) : (\r\n              <ChevronsUpDown />\r\n            )}\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent align=\"start\">\r\n          <DropdownMenuItem onClick={() => column.toggleSorting(false)}>\r\n            <ArrowUp className=\"h-3.5 w-3.5 text-muted-foreground/70\" />\r\n            {t('buttons.sort-ascending')}\r\n          </DropdownMenuItem>\r\n          <DropdownMenuItem onClick={() => column.toggleSorting(true)}>\r\n            <ArrowDown className=\"h-3.5 w-3.5 text-muted-foreground/70\" />\r\n            {t('buttons.sort-descending')}\r\n          </DropdownMenuItem>\r\n          <DropdownMenuSeparator />\r\n          <DropdownMenuItem onClick={() => column.toggleVisibility(false)}>\r\n            <EyeOff className=\"h-3.5 w-3.5 text-muted-foreground/70\" />\r\n            {t('buttons.hide-column')}\r\n          </DropdownMenuItem>\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAOA;AACA;AAEA;AAAA;AAAA;AAAA;AACA;;;;;;;;AAQO,SAAS,sBAAqC,KAIT;QAJS,EACnD,MAAM,EACN,KAAK,EACL,SAAS,EACiC,GAJS;;IAKnD,MAAM,IAAI,IAAA,4NAAe,EAAC;IAE1B,IAAI,CAAC,OAAO,UAAU,IAAI;QACxB,qBAAO,6LAAC;YAAI,WAAW,IAAA,4HAAE,EAAC;sBAAa;;;;;;IACzC;IAEA,qBACE,6LAAC;QAAI,WAAW,IAAA,4HAAE,EAAC,+BAA+B;kBAChD,cAAA,6LAAC,+JAAY;;8BACX,6LAAC,sKAAmB;oBAAC,OAAO;8BAC1B,cAAA,6LAAC,+IAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;;0CAEV,6LAAC;0CAAM;;;;;;4BACN,OAAO,WAAW,OAAO,uBACxB,6LAAC,gOAAS;;;;uCACR,OAAO,WAAW,OAAO,sBAC3B,6LAAC,0NAAO;;;;qDAER,6LAAC,mPAAc;;;;;;;;;;;;;;;;8BAIrB,6LAAC,sKAAmB;oBAAC,OAAM;;sCACzB,6LAAC,mKAAgB;4BAAC,SAAS,IAAM,OAAO,aAAa,CAAC;;8CACpD,6LAAC,0NAAO;oCAAC,WAAU;;;;;;gCAClB,EAAE;;;;;;;sCAEL,6LAAC,mKAAgB;4BAAC,SAAS,IAAM,OAAO,aAAa,CAAC;;8CACpD,6LAAC,gOAAS;oCAAC,WAAU;;;;;;gCACpB,EAAE;;;;;;;sCAEL,6LAAC,wKAAqB;;;;;sCACtB,6LAAC,mKAAgB;4BAAC,SAAS,IAAM,OAAO,gBAAgB,CAAC;;8CACvD,6LAAC,uNAAM;oCAAC,WAAU;;;;;;gCACjB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAMf;GAhDgB;;QAKJ,4NAAe;;;KALX", "debugId": null}}, {"offset": {"line": 2333, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/projects/project-detail/modules/use-modules-columns.tsx"], "sourcesContent": ["import { DataTableColumnHeader } from '@atlas/components/common/atlas/data-table/data-table-column-header'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { ColumnDef } from '@tanstack/react-table'\r\nimport Link from 'next/dist/client/link'\r\nimport { useFormatter, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  projectId: Project['id']\r\n}\r\n\r\nexport const useModulesColumns = ({ projectId }: Props) => {\r\n  const t = useTranslations('tables.modules.columns')\r\n  const tModuleType = useTranslations('enums.module-type')\r\n  const format = useFormatter()\r\n\r\n  return [\r\n    {\r\n      accessorKey: 'name',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title={t('name.header')} />\r\n      ),\r\n      meta: {\r\n        i18nLabel: t('name.header'),\r\n      },\r\n      cell: ({ row }) => {\r\n        const module = row.original\r\n        const name = module.name ?? t('name.unnamed')\r\n\r\n        return (\r\n          <Link\r\n            href={`/dashboard/projects/${projectId}/modules/${module.id}`}\r\n            className=\"font-medium text-primary underline-offset-4 hover:underline\"\r\n            title={name}\r\n            prefetch={false}\r\n          >\r\n            <span className=\"block max-w-[200px] truncate sm:max-w-[300px] md:max-w-[400px]\">\r\n              {name}\r\n            </span>\r\n          </Link>\r\n        )\r\n      },\r\n    },\r\n    {\r\n      accessorKey: 'type',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title={t('type.header')} />\r\n      ),\r\n      meta: {\r\n        i18nLabel: t('type.header'),\r\n      },\r\n      cell: ({ row }) => tModuleType(row.original.type),\r\n    },\r\n    {\r\n      accessorKey: 'description',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader\r\n          column={column}\r\n          title={t('description.header')}\r\n        />\r\n      ),\r\n      meta: {\r\n        i18nLabel: t('description.header'),\r\n      },\r\n    },\r\n    {\r\n      accessorKey: 'createdAt',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title={t('created-at.header')} />\r\n      ),\r\n      cell: ({ row }) => {\r\n        if (!row.original.createdAt) {\r\n          return '-'\r\n        }\r\n\r\n        return format.dateTime(row.original.createdAt, {\r\n          year: 'numeric',\r\n          month: 'long',\r\n          day: 'numeric',\r\n        })\r\n      },\r\n      meta: {\r\n        i18nLabel: t('created-at.header'),\r\n      },\r\n    },\r\n    {\r\n      accessorKey: 'lastModified',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader\r\n          column={column}\r\n          title={t('last-modified.header')}\r\n        />\r\n      ),\r\n      cell: ({ row }) => {\r\n        if (!row.original.lastModified) {\r\n          return '-'\r\n        }\r\n\r\n        return format.dateTime(row.original.lastModified, {\r\n          year: 'numeric',\r\n          month: 'long',\r\n          day: 'numeric',\r\n        })\r\n      },\r\n      meta: {\r\n        i18nLabel: t('last-modified.header'),\r\n      },\r\n    },\r\n  ] as ColumnDef<Module>[]\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAIA;AACA;;;;;;AAMO,MAAM,oBAAoB;QAAC,EAAE,SAAS,EAAS;;IACpD,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,cAAc,IAAA,4NAAe,EAAC;IACpC,MAAM,SAAS,IAAA,yNAAY;IAE3B,OAAO;QACL;YACE,aAAa;YACb,QAAQ;oBAAC,EAAE,MAAM,EAAE;qCACjB,6LAAC,uNAAqB;oBAAC,QAAQ;oBAAQ,OAAO,EAAE;;;;;;;YAElD,MAAM;gBACJ,WAAW,EAAE;YACf;YACA,MAAM;oBAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,SAAS,IAAI,QAAQ;oBACd;gBAAb,MAAM,OAAO,CAAA,eAAA,OAAO,IAAI,cAAX,0BAAA,eAAe,EAAE;gBAE9B,qBACE,6LAAC,4JAAI;oBACH,MAAM,AAAC,uBAA2C,OAArB,WAAU,aAAqB,OAAV,OAAO,EAAE;oBAC3D,WAAU;oBACV,OAAO;oBACP,UAAU;8BAEV,cAAA,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;YAIT;QACF;QACA;YACE,aAAa;YACb,QAAQ;oBAAC,EAAE,MAAM,EAAE;qCACjB,6LAAC,uNAAqB;oBAAC,QAAQ;oBAAQ,OAAO,EAAE;;;;;;;YAElD,MAAM;gBACJ,WAAW,EAAE;YACf;YACA,MAAM;oBAAC,EAAE,GAAG,EAAE;uBAAK,YAAY,IAAI,QAAQ,CAAC,IAAI;;QAClD;QACA;YACE,aAAa;YACb,QAAQ;oBAAC,EAAE,MAAM,EAAE;qCACjB,6LAAC,uNAAqB;oBACpB,QAAQ;oBACR,OAAO,EAAE;;;;;;;YAGb,MAAM;gBACJ,WAAW,EAAE;YACf;QACF;QACA;YACE,aAAa;YACb,QAAQ;oBAAC,EAAE,MAAM,EAAE;qCACjB,6LAAC,uNAAqB;oBAAC,QAAQ;oBAAQ,OAAO,EAAE;;;;;;;YAElD,MAAM;oBAAC,EAAE,GAAG,EAAE;gBACZ,IAAI,CAAC,IAAI,QAAQ,CAAC,SAAS,EAAE;oBAC3B,OAAO;gBACT;gBAEA,OAAO,OAAO,QAAQ,CAAC,IAAI,QAAQ,CAAC,SAAS,EAAE;oBAC7C,MAAM;oBACN,OAAO;oBACP,KAAK;gBACP;YACF;YACA,MAAM;gBACJ,WAAW,EAAE;YACf;QACF;QACA;YACE,aAAa;YACb,QAAQ;oBAAC,EAAE,MAAM,EAAE;qCACjB,6LAAC,uNAAqB;oBACpB,QAAQ;oBACR,OAAO,EAAE;;;;;;;YAGb,MAAM;oBAAC,EAAE,GAAG,EAAE;gBACZ,IAAI,CAAC,IAAI,QAAQ,CAAC,YAAY,EAAE;oBAC9B,OAAO;gBACT;gBAEA,OAAO,OAAO,QAAQ,CAAC,IAAI,QAAQ,CAAC,YAAY,EAAE;oBAChD,MAAM;oBACN,OAAO;oBACP,KAAK;gBACP;YACF;YACA,MAAM;gBACJ,WAAW,EAAE;YACf;QACF;KACD;AACH;GAlGa;;QACD,4NAAe;QACL,4NAAe;QACpB,yNAAY", "debugId": null}}, {"offset": {"line": 2504, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/projects/project-detail/modules/project-modules-section.tsx"], "sourcesContent": ["import { DataTable } from '@atlas/components/common/atlas/data-table/data-table'\r\nimport { CreateModuleModal } from '@atlas/components/common/atlas/projects/project-detail/modules/create-module-modal'\r\nimport { useModulePagination } from '@atlas/components/common/atlas/projects/project-detail/modules/use-module-pagination'\r\nimport { useModulesColumns } from '@atlas/components/common/atlas/projects/project-detail/modules/use-modules-columns'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Input } from '@atlas/components/ui/input'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useAtom } from 'jotai'\r\nimport { atom } from 'jotai/index'\r\nimport { PlusCircle } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useMemo, useState } from 'react'\r\n\r\ntype Props = {\r\n  project: Project\r\n  session: Session\r\n}\r\n\r\nexport const createModuleModalOpenAtom = atom<boolean>(false)\r\n\r\nexport const ProjectModulesSection = ({ project, session }: Props) => {\r\n  const t = useTranslations('components.modules')\r\n  const tActions = useTranslations('actions')\r\n  const columns = useModulesColumns({ projectId: project.id })\r\n  const [createModuleModalOpen, setCreateModuleModalOpen] = useAtom(\r\n    createModuleModalOpenAtom,\r\n  )\r\n  const [nameFilter, setNameFilter] = useState('')\r\n  const filteredModules = useMemo(\r\n    () =>\r\n      project.modules?.filter(m =>\r\n        m.name.toLowerCase().includes(nameFilter.toLowerCase()),\r\n      ) ?? [],\r\n    [project.modules, nameFilter],\r\n  )\r\n\r\n  const {\r\n    page,\r\n    pageSize,\r\n    onPaginationChange,\r\n    currentPageModules: filteredAndPaginatedModules,\r\n  } = useModulePagination({ modules: filteredModules })\r\n  return (\r\n    <>\r\n      {createModuleModalOpen && (\r\n        <CreateModuleModal project={project} session={session} />\r\n      )}\r\n      <div className=\"space-y-4 rounded-md border p-4\">\r\n        <h3 className=\"text-lg font-medium\">{t('title')}</h3>\r\n        <Button className=\"w-40\" onClick={() => setCreateModuleModalOpen(true)}>\r\n          <PlusCircle />\r\n          {tActions('create-module.label')}\r\n        </Button>\r\n        <Input\r\n          className=\"sm:w-1/3 xs:w-full\"\r\n          placeholder={t('name-filter.placeholder')}\r\n          value={nameFilter}\r\n          onChange={e => setNameFilter(e.target.value)}\r\n        />\r\n        {filteredAndPaginatedModules && (\r\n          <DataTable\r\n            page={page}\r\n            perPage={pageSize}\r\n            onPaginationChange={onPaginationChange}\r\n            columns={columns}\r\n            data={filteredAndPaginatedModules}\r\n            rowCount={filteredModules.length}\r\n          />\r\n        )}\r\n      </div>\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;;;;;;;;;;;;;;AAOO,MAAM,4BAA4B,IAAA,mJAAI,EAAU;AAEhD,MAAM,wBAAwB;QAAC,EAAE,OAAO,EAAE,OAAO,EAAS;;IAC/D,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,WAAW,IAAA,4NAAe,EAAC;IACjC,MAAM,UAAU,IAAA,sOAAiB,EAAC;QAAE,WAAW,QAAQ,EAAE;IAAC;IAC1D,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,IAAA,oJAAO,EAC/D;IAEF,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAC;IAC7C,MAAM,kBAAkB,IAAA,wKAAO;0DAC7B;gBACE;gBAAA;mBAAA,CAAA,2BAAA,mBAAA,QAAQ,OAAO,cAAf,uCAAA,iBAAiB,MAAM;kEAAC,CAAA,IACtB,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;8EADtD,qCAAA,0BAEK,EAAE;;yDACT;QAAC,QAAQ,OAAO;QAAE;KAAW;IAG/B,MAAM,EACJ,IAAI,EACJ,QAAQ,EACR,kBAAkB,EAClB,oBAAoB,2BAA2B,EAChD,GAAG,IAAA,0OAAmB,EAAC;QAAE,SAAS;IAAgB;IACnD,qBACE;;YACG,uCACC,6LAAC,sOAAiB;gBAAC,SAAS;gBAAS,SAAS;;;;;;0BAEhD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAuB,EAAE;;;;;;kCACvC,6LAAC,+IAAM;wBAAC,WAAU;wBAAO,SAAS,IAAM,yBAAyB;;0CAC/D,6LAAC,mOAAU;;;;;4BACV,SAAS;;;;;;;kCAEZ,6LAAC,6IAAK;wBACJ,WAAU;wBACV,aAAa,EAAE;wBACf,OAAO;wBACP,UAAU,CAAA,IAAK,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;oBAE5C,6CACC,6LAAC,uLAAS;wBACR,MAAM;wBACN,SAAS;wBACT,oBAAoB;wBACpB,SAAS;wBACT,MAAM;wBACN,UAAU,gBAAgB,MAAM;;;;;;;;;;;;;;AAM5C;GApDa;;QACD,4NAAe;QACR,4NAAe;QAChB,sOAAiB;QACyB,oJAAO;QAiB7D,0OAAmB;;;KArBZ", "debugId": null}}, {"offset": {"line": 2648, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/alert.tsx"], "sourcesContent": ["import { cn } from '@atlas/lib/utils'\r\nimport { cva, type VariantProps } from 'class-variance-authority'\r\nimport type * as React from 'react'\r\n\r\nconst alertVariants = cva(\r\n  'relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current',\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: 'bg-card text-card-foreground',\r\n        destructive:\r\n          'text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n    },\r\n  },\r\n)\r\n\r\nfunction Alert({\r\n  className,\r\n  variant,\r\n  ...props\r\n}: React.ComponentProps<'div'> & VariantProps<typeof alertVariants>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert\"\r\n      role=\"alert\"\r\n      className={cn(alertVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-title\"\r\n      className={cn(\r\n        'col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-description\"\r\n      className={cn(\r\n        'text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAGA,MAAM,gBAAgB,IAAA,0KAAG,EACvB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAIoD;QAJpD,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D,GAJpD;IAKb,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,IAAA,4HAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAbS;AAeT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,KAGI;QAHJ,EACxB,SAAS,EACT,GAAG,OACyB,GAHJ;IAIxB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 2727, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/projects/project-detail/project-detail-error-component.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { Alert, AlertDescription, AlertTitle } from '@atlas/components/ui/alert'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Link } from '@atlas/i18n/routing'\r\nimport type { ApiError, ValidationError } from '@atlas/types'\r\nimport { AlertTriangle } from 'lucide-react'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ninterface DetailErrorProps {\r\n  reset?: () => void\r\n  error?: ApiError | ValidationError\r\n}\r\n\r\nexport const ProjectDetailErrorComponent = ({ reset }: DetailErrorProps) => {\r\n  const t = useTranslations('pages.project-detail.error')\r\n\r\n  return (\r\n    <div className=\"flex items-center justify-center h-full p-4 md:p-8\">\r\n      <Alert variant=\"destructive\" className=\"max-w-md w-full\">\r\n        <AlertTriangle className=\"h-5 w-5\" />\r\n        <AlertTitle className=\"font-bold text-lg mb-1\">{t('title')}</AlertTitle>\r\n        <AlertDescription>\r\n          <p className=\"mb-4\">{t('description')}</p>\r\n          <div className=\"flex justify-end gap-3\">\r\n            {reset && (\r\n              <Button variant=\"outline\" size=\"sm\" onClick={() => reset()}>\r\n                {t('actions.retry.label')}\r\n              </Button>\r\n            )}\r\n            <Link href=\"/dashboard\">\r\n              <Button variant=\"outline\" size=\"sm\">\r\n                {t('actions.go-to-home.label')}\r\n              </Button>\r\n            </Link>\r\n          </div>\r\n        </AlertDescription>\r\n      </Alert>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AACA;;;AAPA;;;;;;AAcO,MAAM,8BAA8B;QAAC,EAAE,KAAK,EAAoB;;IACrE,MAAM,IAAI,IAAA,4NAAe,EAAC;IAE1B,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,6IAAK;YAAC,SAAQ;YAAc,WAAU;;8BACrC,6LAAC,4OAAa;oBAAC,WAAU;;;;;;8BACzB,6LAAC,kJAAU;oBAAC,WAAU;8BAA0B,EAAE;;;;;;8BAClD,6LAAC,wJAAgB;;sCACf,6LAAC;4BAAE,WAAU;sCAAQ,EAAE;;;;;;sCACvB,6LAAC;4BAAI,WAAU;;gCACZ,uBACC,6LAAC,+IAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,SAAS,IAAM;8CAChD,EAAE;;;;;;8CAGP,6LAAC,iIAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,+IAAM;wCAAC,SAAQ;wCAAU,MAAK;kDAC5B,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB;GA1Ba;;QACD,4NAAe;;;KADd", "debugId": null}}, {"offset": {"line": 2848, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/pages/dashboard/dashboard-content/projects/projects-delete-alert.tsx"], "sourcesContent": ["'use client'\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction, // <-- The component we are modifying\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@atlas/components/ui/alert-dialog'\r\nimport { buttonVariants } from '@atlas/components/ui/button'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  project: Project\r\n  open: boolean\r\n  onOpenChange: (open: boolean) => void\r\n  onConfirm: () => void\r\n}\r\n\r\nexport const ProjectDeleteAlert = ({\r\n  project,\r\n  open,\r\n  onOpenChange,\r\n  onConfirm,\r\n}: Props) => {\r\n  const t = useTranslations('components.projects.delete-alert')\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  const handleConfirm = () => {\r\n    onConfirm()\r\n    onOpenChange(false)\r\n  }\r\n\r\n  const handleCancel = () => {\r\n    onOpenChange(false)\r\n  }\r\n\r\n  return (\r\n    <AlertDialog open={open} onOpenChange={onOpenChange}>\r\n      <AlertDialogContent>\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>{t('title')}</AlertDialogTitle>\r\n          <AlertDialogDescription>\r\n            {t('description', {\r\n              projectName: project.constructionSiteName ?? '',\r\n            })}\r\n          </AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <AlertDialogFooter>\r\n          <AlertDialogCancel onClick={handleCancel}>\r\n            {tCommon('cancel')}\r\n          </AlertDialogCancel>\r\n          <AlertDialogAction\r\n            className={buttonVariants({ variant: 'destructive' })}\r\n            onClick={handleConfirm}\r\n          >\r\n            {tCommon('delete')}\r\n          </AlertDialogAction>\r\n        </AlertDialogFooter>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AAUA;AAEA;;;AAbA;;;;AAsBO,MAAM,qBAAqB;QAAC,EACjC,OAAO,EACP,IAAI,EACJ,YAAY,EACZ,SAAS,EACH;;IACN,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,4NAAe,EAAC;IAEhC,MAAM,gBAAgB;QACpB;QACA,aAAa;IACf;IAEA,MAAM,eAAe;QACnB,aAAa;IACf;QASyB;IAPzB,qBACE,6LAAC,6JAAW;QAAC,MAAM;QAAM,cAAc;kBACrC,cAAA,6LAAC,oKAAkB;;8BACjB,6LAAC,mKAAiB;;sCAChB,6LAAC,kKAAgB;sCAAE,EAAE;;;;;;sCACrB,6LAAC,wKAAsB;sCACpB,EAAE,eAAe;gCAChB,aAAa,CAAA,gCAAA,QAAQ,oBAAoB,cAA5B,2CAAA,gCAAgC;4BAC/C;;;;;;;;;;;;8BAGJ,6LAAC,mKAAiB;;sCAChB,6LAAC,mKAAiB;4BAAC,SAAS;sCACzB,QAAQ;;;;;;sCAEX,6LAAC,mKAAiB;4BAChB,WAAW,IAAA,uJAAc,EAAC;gCAAE,SAAS;4BAAc;4BACnD,SAAS;sCAER,QAAQ;;;;;;;;;;;;;;;;;;;;;;;AAMrB;GA3Ca;;QAMD,4NAAe;QACT,4NAAe;;;KAPpB", "debugId": null}}, {"offset": {"line": 2959, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/api/projects/endpoints/delete-project.ts"], "sourcesContent": ["import { handleApiError } from '@atlas/lib/api/apiErrors'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { ApiError } from '@atlas/types'\r\nimport type { AxiosResponse } from 'axios'\r\nimport { type TaskEither, tryCatch } from 'fp-ts/TaskEither'\r\nimport { apiClient } from '../../httpClient'\r\n\r\nexport const deleteProjectById = (\r\n  token: string,\r\n  id: Project['id'],\r\n): TaskEither<ApiError, AxiosResponse> =>\r\n  tryCatch(\r\n    () =>\r\n      apiClient.delete(`/api/v2/projects/${id}`, {\r\n        ...(token && {\r\n          headers: { Authorization: `Bearer ${token}` },\r\n        }),\r\n      }),\r\n    e => handleApiError(e),\r\n  )\r\n"], "names": [], "mappings": ";;;;AAAA;AAIA;AACA;;;;AAEO,MAAM,oBAAoB,CAC/B,OACA,KAEA,IAAA,4JAAQ,EACN,IACE,+IAAS,CAAC,MAAM,CAAC,AAAC,oBAAsB,OAAH,KAAM;YACzC,GAAI,SAAS;gBACX,SAAS;oBAAE,eAAe,AAAC,UAAe,OAAN;gBAAQ;YAC9C,CAAC;QACH,IACF,CAAA,IAAK,IAAA,mJAAc,EAAC", "debugId": null}}, {"offset": {"line": 2983, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/mutation/projects/use-delete-project-mutation.tsx"], "sourcesContent": ["import { deleteProjectById } from '@atlas/lib/api/projects/endpoints/delete-project'\r\nimport type { ApiError } from '@atlas/types'\r\nimport {\r\n  type UseMutationOptions,\r\n  useMutation,\r\n  useQueryClient,\r\n} from '@tanstack/react-query'\r\nimport * as E from 'fp-ts/Either'\r\nimport { pipe } from 'fp-ts/function'\r\n\r\ntype DeleteProjectError = ApiError\r\n\r\nconst mutationFn =\r\n  (token: string) =>\r\n  async (id: string): Promise<void> => {\r\n    const result = await deleteProjectById(token, id)()\r\n\r\n    return pipe(\r\n      result,\r\n      E.match(\r\n        (error: DeleteProjectError) => {\r\n          throw error\r\n        },\r\n        () => {\r\n          return\r\n        },\r\n      ),\r\n    )\r\n  }\r\n\r\nexport const useDeleteProjectMutation = (\r\n  token: string,\r\n  options?: Omit<\r\n    UseMutationOptions<void, DeleteProjectError, string>,\r\n    'mutationFn'\r\n  >,\r\n) => {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation<void, DeleteProjectError, string>({\r\n    ...options,\r\n    mutationFn: mutationFn(token),\r\n    onSuccess: (data, variables, context) => {\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n      options?.onSuccess?.(data, variables, context)\r\n    },\r\n  })\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AAAA;AAKA;AACA;;;;;;AAIA,MAAM,aACJ,CAAC,QACD,OAAO;QACL,MAAM,SAAS,MAAM,IAAA,uLAAiB,EAAC,OAAO;QAE9C,OAAO,IAAA,sJAAI,EACT,QACA,qJAAO,CACL,CAAC;YACC,MAAM;QACR,GACA;YACE;QACF;IAGN;AAEK,MAAM,2BAA2B,CACtC,OACA;;IAKA,MAAM,cAAc,IAAA,2MAAc;IAElC,OAAO,IAAA,gMAAW,EAAmC;QACnD,GAAG,OAAO;QACV,YAAY,WAAW;QACvB,SAAS;oDAAE,CAAC,MAAM,WAAW;oBAE3B;gBADA,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,oBAAA,+BAAA,qBAAA,QAAS,SAAS,cAAlB,yCAAA,wBAAA,SAAqB,MAAM,WAAW;YACxC;;IACF;AACF;GAjBa;;QAOS,2MAAc;QAE3B,gMAAW", "debugId": null}}, {"offset": {"line": 3037, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/api/projects/endpoints/get-project.ts"], "sourcesContent": ["import { handleApiError } from '@atlas/lib/api/apiErrors'\r\nimport {\r\n  type Project,\r\n  projectSchema,\r\n} from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { ApiError, ValidationError } from '@atlas/types'\r\nimport type { AxiosResponse } from 'axios'\r\nimport { pipe } from 'fp-ts/function'\r\nimport { map, type TaskEither, tryCatch } from 'fp-ts/TaskEither'\r\nimport { apiClient } from '../../httpClient'\r\n\r\nconst validate = (data: unknown): Project => {\r\n  const parse = projectSchema.safeParse(data)\r\n  if (!parse.success) {\r\n    throw {\r\n      type: 'ValidationError',\r\n      code: 'MALFORMED_RESPONSE',\r\n      message: parse.error.message,\r\n    } as ValidationError\r\n  }\r\n  return parse.data\r\n}\r\n\r\nexport const fetch = (\r\n  token: string,\r\n  id: Project['id'],\r\n): TaskEither<ApiError, AxiosResponse> =>\r\n  tryCatch(\r\n    () =>\r\n      apiClient.get(`/api/v2/projects/${id}`, {\r\n        ...(token && {\r\n          headers: { Authorization: `Bearer ${token}` },\r\n        }),\r\n      }),\r\n    e => handleApiError(e),\r\n  )\r\n\r\nexport const getProjectById = (\r\n  token: string,\r\n  projectId: Project['id'],\r\n): TaskEither<ApiError | ValidationError, Project> =>\r\n  pipe(\r\n    fetch(token, projectId),\r\n    map(a => a.data),\r\n    map(validate),\r\n  )\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAMA;AACA;AACA;;;;;;AAEA,MAAM,WAAW,CAAC;IAChB,MAAM,QAAQ,wKAAa,CAAC,SAAS,CAAC;IACtC,IAAI,CAAC,MAAM,OAAO,EAAE;QAClB,MAAM;YACJ,MAAM;YACN,MAAM;YACN,SAAS,MAAM,KAAK,CAAC,OAAO;QAC9B;IACF;IACA,OAAO,MAAM,IAAI;AACnB;AAEO,MAAM,QAAQ,CACnB,OACA,KAEA,IAAA,4JAAQ,EACN,IACE,+IAAS,CAAC,GAAG,CAAC,AAAC,oBAAsB,OAAH,KAAM;YACtC,GAAI,SAAS;gBACX,SAAS;oBAAE,eAAe,AAAC,UAAe,OAAN;gBAAQ;YAC9C,CAAC;QACH,IACF,CAAA,IAAK,IAAA,mJAAc,EAAC;AAGjB,MAAM,iBAAiB,CAC5B,OACA,YAEA,IAAA,sJAAI,EACF,MAAM,OAAO,YACb,IAAA,uJAAG,EAAC,CAAA,IAAK,EAAE,IAAI,GACf,IAAA,uJAAG,EAAC", "debugId": null}}, {"offset": {"line": 3079, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/query/projects/get-project.tsx"], "sourcesContent": ["import { getProjectById } from '@atlas/lib/api/projects/endpoints/get-project'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { fold } from 'fp-ts/Either'\r\nimport { pipe } from 'fp-ts/function'\r\nimport type { Session } from 'next-auth'\r\n\r\nexport const getProjectQuery = async (\r\n  session: Session,\r\n  projectId: Project['id'],\r\n): Promise<Project> => {\r\n  const result = await getProjectById(session.accessToken, projectId)()\r\n\r\n  return pipe(\r\n    result,\r\n    fold(\r\n      err => {\r\n        throw err\r\n      },\r\n      data => data,\r\n    ),\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;;;;AAGO,MAAM,kBAAkB,OAC7B,SACA;IAEA,MAAM,SAAS,MAAM,IAAA,iLAAc,EAAC,QAAQ,WAAW,EAAE;IAEzD,OAAO,IAAA,sJAAI,EACT,QACA,IAAA,oJAAI,EACF,CAAA;QACE,MAAM;IACR,GACA,CAAA,OAAQ;AAGd", "debugId": null}}, {"offset": {"line": 3102, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/query/projects/use-project.tsx"], "sourcesContent": ["import type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { getProjectQuery } from '@atlas/lib/query/projects/get-project'\r\nimport type { ApiError, ValidationError } from '@atlas/types'\r\nimport { useQuery } from '@tanstack/react-query'\r\nimport type { Session } from 'next-auth'\r\n\r\nexport const useProject = ({\r\n  session,\r\n  projectId,\r\n  initialData,\r\n}: {\r\n  session: Session\r\n  projectId: Project['id']\r\n  initialData?: Project\r\n}) =>\r\n  useQuery<Project, ApiError | ValidationError>({\r\n    queryKey: ['projects', projectId],\r\n    queryFn: () => {\r\n      return getProjectQuery(session, projectId)\r\n    },\r\n    retry: 1,\r\n    initialData,\r\n  })\r\n"], "names": [], "mappings": ";;;;AACA;AAEA;;;;AAGO,MAAM,aAAa;QAAC,EACzB,OAAO,EACP,SAAS,EACT,WAAW,EAKZ;;IACC,OAAA,IAAA,0LAAQ,EAAsC;QAC5C,UAAU;YAAC;YAAY;SAAU;QACjC,OAAO;mCAAE;gBACP,OAAO,IAAA,wKAAe,EAAC,SAAS;YAClC;;QACA,OAAO;QACP;IACF;AAAC;GAhBU;;QASX,0LAAQ", "debugId": null}}, {"offset": {"line": 3140, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/card.tsx"], "sourcesContent": ["import { cn } from '@atlas/lib/utils'\r\nimport type * as React from 'react'\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn('leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        'col-start-2 row-span-2 row-start-1 self-start justify-self-end',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn('px-6', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;;;AAGA,SAAS,KAAK,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACZ,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACvB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 3267, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/projects/project-detail/project-detail-header.tsx"], "sourcesContent": ["import { projectDetailToDelete<PERSON>tom } from '@atlas/components/common/atlas/projects/project-detail/project-detail-content'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Card, CardContent, CardHeader } from '@atlas/components/ui/card'\r\nimport { useRouter } from '@atlas/i18n/routing'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport { useSetAtom } from 'jotai'\r\nimport { useFormatter, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  project: Project\r\n  className?: string\r\n}\r\n\r\nexport const ProjectDetailHeader = ({ project, className }: Props) => {\r\n  const t = useTranslations('components.project-detail.header')\r\n  const format = useFormatter()\r\n  const setProjectToDelete = useSetAtom(projectDetailToDeleteAtom)\r\n  const router = useRouter()\r\n\r\n  const handleEditProjectClick = () => {\r\n    return router.push(`/dashboard/projects/${project.id}/edit`)\r\n  }\r\n\r\n  return (\r\n    <Card className={cn('top-6 z-10 mb-6', className)}>\r\n      <CardHeader className=\"flex flex-col sm:flex-row items-center justify-between pb-2 pt-6 pl-6 pr-6\">\r\n        <h1 className=\"text-2xl font-semibold tracking-tight md:text-3xl\">\r\n          {project.constructionSiteName ?? t('unnamed-project')}\r\n        </h1>\r\n        <div className={cn('flex flex-col sm:flex-row gap-2')}>\r\n          <Button size=\"sm\" onClick={handleEditProjectClick}>\r\n            {t('edit-project')}\r\n          </Button>\r\n          <Button\r\n            variant=\"destructive\"\r\n            size=\"sm\"\r\n            onClick={() => setProjectToDelete(project)}\r\n          >\r\n            {t('delete-project')}\r\n          </Button>\r\n        </div>\r\n      </CardHeader>\r\n      <CardContent className=\" flex flex-col gap-1 pb-6 pl-6 pr-6 pt-0\">\r\n        <div className=\"flex flex-wrap items-center gap-x-4 gap-y-1 text-sm text-muted-foreground\">\r\n          {project.company && (\r\n            <span>\r\n              {t('client-label')}: {project.company}\r\n            </span>\r\n          )}\r\n          {project.planner && (\r\n            <span>\r\n              {t('manager-label')}: {project.planner}\r\n            </span>\r\n          )}\r\n          {(project.address || project.address) && (\r\n            <span>\r\n              {t('address-label')}: {project.address}\r\n            </span>\r\n          )}\r\n          {project.createdAt && (\r\n            <span>\r\n              {t('created-label')}:{' '}\r\n              {format.dateTime(project.createdAt, {\r\n                year: 'numeric',\r\n                month: 'long',\r\n                day: 'numeric',\r\n                hour: '2-digit',\r\n                minute: '2-digit',\r\n              })}\r\n            </span>\r\n          )}\r\n        </div>\r\n        {project.processingType && (\r\n          <div className=\"flex flex-wrap items-center gap-x-4 gap-y-1 text-sm text-muted-foreground\">\r\n            <span>\r\n              {t('processing-type')}: {project.processingType}\r\n            </span>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;;;;;;;;;;AAOO,MAAM,sBAAsB;QAAC,EAAE,OAAO,EAAE,SAAS,EAAS;;IAC/D,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,SAAS,IAAA,yNAAY;IAC3B,MAAM,qBAAqB,IAAA,uJAAU,EAAC,sOAAyB;IAC/D,MAAM,SAAS,IAAA,sIAAS;IAExB,MAAM,yBAAyB;QAC7B,OAAO,OAAO,IAAI,CAAC,AAAC,uBAAiC,OAAX,QAAQ,EAAE,EAAC;IACvD;QAMS;IAJT,qBACE,6LAAC,2IAAI;QAAC,WAAW,IAAA,4HAAE,EAAC,mBAAmB;;0BACrC,6LAAC,iJAAU;gBAAC,WAAU;;kCACpB,6LAAC;wBAAG,WAAU;kCACX,CAAA,gCAAA,QAAQ,oBAAoB,cAA5B,2CAAA,gCAAgC,EAAE;;;;;;kCAErC,6LAAC;wBAAI,WAAW,IAAA,4HAAE,EAAC;;0CACjB,6LAAC,+IAAM;gCAAC,MAAK;gCAAK,SAAS;0CACxB,EAAE;;;;;;0CAEL,6LAAC,+IAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,mBAAmB;0CAEjC,EAAE;;;;;;;;;;;;;;;;;;0BAIT,6LAAC,kJAAW;gBAAC,WAAU;;kCACrB,6LAAC;wBAAI,WAAU;;4BACZ,QAAQ,OAAO,kBACd,6LAAC;;oCACE,EAAE;oCAAgB;oCAAG,QAAQ,OAAO;;;;;;;4BAGxC,QAAQ,OAAO,kBACd,6LAAC;;oCACE,EAAE;oCAAiB;oCAAG,QAAQ,OAAO;;;;;;;4BAGzC,CAAC,QAAQ,OAAO,IAAI,QAAQ,OAAO,mBAClC,6LAAC;;oCACE,EAAE;oCAAiB;oCAAG,QAAQ,OAAO;;;;;;;4BAGzC,QAAQ,SAAS,kBAChB,6LAAC;;oCACE,EAAE;oCAAiB;oCAAE;oCACrB,OAAO,QAAQ,CAAC,QAAQ,SAAS,EAAE;wCAClC,MAAM;wCACN,OAAO;wCACP,KAAK;wCACL,MAAM;wCACN,QAAQ;oCACV;;;;;;;;;;;;;oBAIL,QAAQ,cAAc,kBACrB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;;gCACE,EAAE;gCAAmB;gCAAG,QAAQ,cAAc;;;;;;;;;;;;;;;;;;;;;;;;AAO7D;GArEa;;QACD,4NAAe;QACV,yNAAY;QACA,uJAAU;QACtB,sIAAS;;;KAJb", "debugId": null}}, {"offset": {"line": 3459, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/projects/project-detail/project-detail-content.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { HeaderSkeleton } from '@atlas/components/common/atlas/header-skeleton'\r\nimport { ProjectModulesSection } from '@atlas/components/common/atlas/projects/project-detail/modules/project-modules-section'\r\nimport { ProjectDetailErrorComponent } from '@atlas/components/common/atlas/projects/project-detail/project-detail-error-component'\r\nimport { ProjectDeleteAlert } from '@atlas/components/pages/dashboard/dashboard-content/projects/projects-delete-alert'\r\nimport { Alert, AlertDescription, AlertTitle } from '@atlas/components/ui/alert' // For error state\r\nimport { Skeleton } from '@atlas/components/ui/skeleton' // For loading state\r\nimport { useRouter } from '@atlas/i18n/routing'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useDeleteProjectMutation } from '@atlas/lib/mutation/projects/use-delete-project-mutation'\r\nimport { useProject } from '@atlas/lib/query/projects/use-project'\r\nimport { atom, useAtom } from 'jotai'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { toast } from 'sonner'\r\nimport { ProjectDetailHeader } from './project-detail-header'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: string\r\n  initialDataProject?: Project\r\n}\r\n\r\nexport const projectDetailToDeleteAtom = atom<Project | undefined>()\r\n\r\nexport const ProjectDetailContent = ({\r\n  projectId,\r\n  initialDataProject,\r\n  session,\r\n}: Props) => {\r\n  const router = useRouter()\r\n\r\n  const tMessages = useTranslations('actions.delete-project.messages')\r\n  const tDetail = useTranslations('components.project-detail')\r\n\r\n  const {\r\n    data: project,\r\n    error,\r\n    isLoading,\r\n  } = useProject({\r\n    session,\r\n    projectId,\r\n    initialData: initialDataProject,\r\n  })\r\n  const [projectToDelete, setProjectToDelete] = useAtom<Project | undefined>(\r\n    projectDetailToDeleteAtom,\r\n  )\r\n\r\n  const { mutate: deleteProject, isPending: isDeleting } =\r\n    useDeleteProjectMutation(session.accessToken, {\r\n      onSuccess: () => {\r\n        toast.success(tMessages('delete.success'))\r\n        router.push('/dashboard')\r\n      },\r\n      onError: err => {\r\n        toast.error(\r\n          tMessages('delete.failure', {\r\n            error: err.message || 'Unknown error',\r\n          }),\r\n        )\r\n      },\r\n    })\r\n\r\n  const handleConfirmDelete = () => {\r\n    if (projectToDelete) {\r\n      deleteProject(projectToDelete.id)\r\n    }\r\n    setProjectToDelete(undefined)\r\n  }\r\n\r\n  const handleOpenChange = (open: boolean) => {\r\n    if (!open) {\r\n      setProjectToDelete(undefined)\r\n    }\r\n  }\r\n\r\n  if (isLoading || isDeleting) {\r\n    return (\r\n      <div className=\"p-4 md:p-6\">\r\n        <HeaderSkeleton />\r\n        <Skeleton className=\"mt-4 h-20 w-full\" />\r\n        <Skeleton className=\"mt-4 h-20 w-full\" />\r\n      </div>\r\n    )\r\n  }\r\n\r\n  if (error) {\r\n    return <ProjectDetailErrorComponent error={error} />\r\n  }\r\n\r\n  if (!project) {\r\n    return (\r\n      <div className=\"p-4 md:p-6\">\r\n        <Alert variant=\"destructive\">\r\n          {/* Usa tDetail per le traduzioni locali */}\r\n          <AlertTitle>{tDetail('not-found.title')}</AlertTitle>\r\n          <AlertDescription>\r\n            {tDetail('not-found.description')}\r\n          </AlertDescription>\r\n        </Alert>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className=\"p-4 md:p-6\">\r\n      <ProjectDetailHeader project={project} />\r\n      <ProjectModulesSection project={project} session={session} />\r\n      {projectToDelete && (\r\n        <ProjectDeleteAlert\r\n          project={projectToDelete}\r\n          open={!!projectToDelete}\r\n          onOpenChange={handleOpenChange}\r\n          onConfirm={handleConfirmDelete}\r\n        />\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AACA;AACA,uOAAiF,kBAAkB;AACnG,6OAAyD,oBAAoB;AAC7E;AAEA;AACA;AACA;AAAA;AAEA;AACA;AACA;;;AAhBA;;;;;;;;;;;;;;AAwBO,MAAM,4BAA4B,IAAA,mJAAI;AAEtC,MAAM,uBAAuB;QAAC,EACnC,SAAS,EACT,kBAAkB,EAClB,OAAO,EACD;;IACN,MAAM,SAAS,IAAA,sIAAS;IAExB,MAAM,YAAY,IAAA,4NAAe,EAAC;IAClC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAEhC,MAAM,EACJ,MAAM,OAAO,EACb,KAAK,EACL,SAAS,EACV,GAAG,IAAA,mKAAU,EAAC;QACb;QACA;QACA,aAAa;IACf;IACA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,oJAAO,EACnD;IAGF,MAAM,EAAE,QAAQ,aAAa,EAAE,WAAW,UAAU,EAAE,GACpD,IAAA,0MAAwB,EAAC,QAAQ,WAAW,EAAE;QAC5C,SAAS;6DAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,UAAU;gBACxB,OAAO,IAAI,CAAC;YACd;;QACA,OAAO;6DAAE,CAAA;gBACP,oJAAK,CAAC,KAAK,CACT,UAAU,kBAAkB;oBAC1B,OAAO,IAAI,OAAO,IAAI;gBACxB;YAEJ;;IACF;IAEF,MAAM,sBAAsB;QAC1B,IAAI,iBAAiB;YACnB,cAAc,gBAAgB,EAAE;QAClC;QACA,mBAAmB;IACrB;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,MAAM;YACT,mBAAmB;QACrB;IACF;IAEA,IAAI,aAAa,YAAY;QAC3B,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,gLAAc;;;;;8BACf,6LAAC,mJAAQ;oBAAC,WAAU;;;;;;8BACpB,6LAAC,mJAAQ;oBAAC,WAAU;;;;;;;;;;;;IAG1B;IAEA,IAAI,OAAO;QACT,qBAAO,6LAAC,mPAA2B;YAAC,OAAO;;;;;;IAC7C;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,6IAAK;gBAAC,SAAQ;;kCAEb,6LAAC,kJAAU;kCAAE,QAAQ;;;;;;kCACrB,6LAAC,wJAAgB;kCACd,QAAQ;;;;;;;;;;;;;;;;;IAKnB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,+NAAmB;gBAAC,SAAS;;;;;;0BAC9B,6LAAC,8OAAqB;gBAAC,SAAS;gBAAS,SAAS;;;;;;YACjD,iCACC,6LAAC,oOAAkB;gBACjB,SAAS;gBACT,MAAM,CAAC,CAAC;gBACR,cAAc;gBACd,WAAW;;;;;;;;;;;;AAKrB;GA7Fa;;QAKI,sIAAS;QAEN,4NAAe;QACjB,4NAAe;QAM3B,mKAAU;QAKgC,oJAAO;QAKnD,0MAAwB;;;KAxBf", "debugId": null}}]}