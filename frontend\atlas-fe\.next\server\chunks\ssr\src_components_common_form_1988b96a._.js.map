{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/form/string-form-input.tsx"], "sourcesContent": ["import {\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  useFormField,\r\n} from '@atlas/components/ui/form'\r\nimport { Input } from '@atlas/components/ui/input'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { Control, FieldPath, FieldValues } from 'react-hook-form'\r\n\r\ntype Props<T extends FieldValues> = {\r\n  control: Control<T>\r\n  description?: boolean\r\n  disabled?: boolean\r\n  name: FieldPath<T>\r\n  required?: boolean\r\n  t: (message: string) => string\r\n}\r\n\r\nexport const StringFormInput = <T extends FieldValues>({\r\n  control,\r\n  description = false,\r\n  disabled = false,\r\n  name,\r\n  required = false,\r\n  t,\r\n}: Props<T>) => {\r\n  const { error } = useFormField()\r\n\r\n  return (\r\n    <FormField\r\n      control={control}\r\n      name={name}\r\n      render={({ field }) => (\r\n        <FormItem>\r\n          <FormLabel>\r\n            {t(`${name}.label`)}\r\n            {required && <p className=\"text-red-600\">*</p>}\r\n          </FormLabel>\r\n          <FormControl>\r\n            <Input\r\n              placeholder={t(`${name}.placeholder`)}\r\n              disabled={disabled}\r\n              {...field}\r\n            />\r\n          </FormControl>\r\n          {description && (\r\n            <FormDescription>{t(`${name}.description`)}</FormDescription>\r\n          )}\r\n          {error?.message && (\r\n            <p className={cn('text-destructive text-sm')}>\r\n              {t(`error.${error}`)}\r\n            </p>\r\n          )}\r\n        </FormItem>\r\n      )}\r\n    />\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAQA;AACA;;;;;AAYO,MAAM,kBAAkB,CAAwB,EACrD,OAAO,EACP,cAAc,KAAK,EACnB,WAAW,KAAK,EAChB,IAAI,EACJ,WAAW,KAAK,EAChB,CAAC,EACQ;IACT,MAAM,EAAE,KAAK,EAAE,GAAG,IAAA,gJAAY;IAE9B,qBACE,8OAAC,6IAAS;QACR,SAAS;QACT,MAAM;QACN,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,4IAAQ;;kCACP,8OAAC,6IAAS;;4BACP,EAAE,GAAG,KAAK,MAAM,CAAC;4BACjB,0BAAY,8OAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;kCAE3C,8OAAC,+IAAW;kCACV,cAAA,8OAAC,0IAAK;4BACJ,aAAa,EAAE,GAAG,KAAK,YAAY,CAAC;4BACpC,UAAU;4BACT,GAAG,KAAK;;;;;;;;;;;oBAGZ,6BACC,8OAAC,mJAAe;kCAAE,EAAE,GAAG,KAAK,YAAY,CAAC;;;;;;oBAE1C,OAAO,yBACN,8OAAC;wBAAE,WAAW,IAAA,yHAAE,EAAC;kCACd,EAAE,CAAC,MAAM,EAAE,OAAO;;;;;;;;;;;;;;;;;AAOjC", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/form/number-form-input.tsx"], "sourcesContent": ["import {\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  useFormField,\r\n} from '@atlas/components/ui/form'\r\nimport { Input } from '@atlas/components/ui/input'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { Control, FieldPath, FieldValues } from 'react-hook-form'\r\n\r\ntype Props<T extends FieldValues> = {\r\n  control: Control<T>\r\n  disabled?: boolean\r\n  max?: number\r\n  min?: number\r\n  name: FieldPath<T>\r\n  required?: boolean\r\n  t: (message: string) => string\r\n  fieldContainerClassName?: string\r\n  decimalPlaces?: number // New prop for formatting display values\r\n}\r\n\r\nexport const NumberFormInput = <T extends FieldValues>({\r\n  control,\r\n  disabled = false,\r\n  max,\r\n  min,\r\n  name,\r\n  required = false,\r\n  t,\r\n  fieldContainerClassName = '',\r\n  decimalPlaces,\r\n}: Props<T>) => {\r\n  const { error } = useFormField()\r\n\r\n  return (\r\n    <FormField\r\n      control={control}\r\n      name={name}\r\n      render={({ field }) => (\r\n        <FormItem className={fieldContainerClassName}>\r\n          <FormLabel>\r\n            {t(`${name}.label`)}\r\n            {required && !disabled && (\r\n              <span className=\"text-red-600 ml-1\">*</span>\r\n            )}\r\n          </FormLabel>\r\n          <FormControl>\r\n            <Input\r\n              type=\"number\"\r\n              step=\"any\"\r\n              placeholder={t(`${name}.placeholder`)}\r\n              min={min}\r\n              max={max}\r\n              disabled={disabled}\r\n              {...field}\r\n              value={\r\n                disabled &&\r\n                decimalPlaces !== undefined &&\r\n                typeof field.value === 'number'\r\n                  ? field.value.toFixed(decimalPlaces)\r\n                  : field.value\r\n              }\r\n              onChange={e => field.onChange(e.target.valueAsNumber)}\r\n            />\r\n          </FormControl>\r\n          <FormDescription>{t(`${name}.description`)}</FormDescription>\r\n          {error?.message && (\r\n            <p className={cn('text-destructive text-sm')}>\r\n              {t(`error.${error}`)}\r\n            </p>\r\n          )}\r\n        </FormItem>\r\n      )}\r\n    />\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAQA;AACA;;;;;AAeO,MAAM,kBAAkB,CAAwB,EACrD,OAAO,EACP,WAAW,KAAK,EAChB,GAAG,EACH,GAAG,EACH,IAAI,EACJ,WAAW,KAAK,EAChB,CAAC,EACD,0BAA0B,EAAE,EAC5B,aAAa,EACJ;IACT,MAAM,EAAE,KAAK,EAAE,GAAG,IAAA,gJAAY;IAE9B,qBACE,8OAAC,6IAAS;QACR,SAAS;QACT,MAAM;QACN,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,4IAAQ;gBAAC,WAAW;;kCACnB,8OAAC,6IAAS;;4BACP,EAAE,GAAG,KAAK,MAAM,CAAC;4BACjB,YAAY,CAAC,0BACZ,8OAAC;gCAAK,WAAU;0CAAoB;;;;;;;;;;;;kCAGxC,8OAAC,+IAAW;kCACV,cAAA,8OAAC,0IAAK;4BACJ,MAAK;4BACL,MAAK;4BACL,aAAa,EAAE,GAAG,KAAK,YAAY,CAAC;4BACpC,KAAK;4BACL,KAAK;4BACL,UAAU;4BACT,GAAG,KAAK;4BACT,OACE,YACA,kBAAkB,aAClB,OAAO,MAAM,KAAK,KAAK,WACnB,MAAM,KAAK,CAAC,OAAO,CAAC,iBACpB,MAAM,KAAK;4BAEjB,UAAU,CAAA,IAAK,MAAM,QAAQ,CAAC,EAAE,MAAM,CAAC,aAAa;;;;;;;;;;;kCAGxD,8OAAC,mJAAe;kCAAE,EAAE,GAAG,KAAK,YAAY,CAAC;;;;;;oBACxC,OAAO,yBACN,8OAAC;wBAAE,WAAW,IAAA,yHAAE,EAAC;kCACd,EAAE,CAAC,MAAM,EAAE,OAAO;;;;;;;;;;;;;;;;;AAOjC", "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/form/select-form-fixed-input.tsx"], "sourcesContent": ["import {\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  useFormField,\r\n} from '@atlas/components/ui/form'\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@atlas/components/ui/select'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { Control, FieldPath, FieldValues } from 'react-hook-form'\r\n\r\ntype Props<T extends FieldValues, N extends FieldPath<T>> = {\r\n  control: Control<T>\r\n  description?: boolean\r\n  name: N\r\n  options: readonly string[]\r\n  optionLabelFn: (option: string) => string\r\n  required?: boolean\r\n  t: (message: string) => string\r\n  disabled?: boolean\r\n}\r\n\r\nexport const SelectFormFixedInput = <\r\n  T extends FieldValues,\r\n  N extends FieldPath<T>,\r\n>({\r\n  control,\r\n  description = false,\r\n  name,\r\n  options,\r\n  optionLabelFn,\r\n  required = false,\r\n  t,\r\n  disabled = false,\r\n}: Props<T, N>) => {\r\n  const { error } = useFormField()\r\n\r\n  return (\r\n    <FormField\r\n      control={control}\r\n      name={name}\r\n      render={({ field }) => (\r\n        <FormItem>\r\n          <FormLabel>\r\n            {t(`${name}.label`)}\r\n            {required && <p className=\"text-red-600\">*</p>}\r\n          </FormLabel>\r\n          <FormControl>\r\n            <Select\r\n              {...field}\r\n              onValueChange={field.onChange}\r\n              disabled={disabled}\r\n            >\r\n              <SelectTrigger className=\"w-full\">\r\n                <SelectValue placeholder={t(`${name}.placeholder`)} />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                {options.map(option => (\r\n                  <SelectItem value={option} key={option}>\r\n                    {optionLabelFn(option)}\r\n                  </SelectItem>\r\n                ))}\r\n              </SelectContent>\r\n            </Select>\r\n          </FormControl>\r\n          {description && (\r\n            <FormDescription>{t(`${name}.description`)}</FormDescription>\r\n          )}\r\n          {error?.message && (\r\n            <p className={cn('text-destructive text-sm')}>\r\n              {t(`error.${error}`)}\r\n            </p>\r\n          )}\r\n        </FormItem>\r\n      )}\r\n    />\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAQA;AAOA;;;;;AAcO,MAAM,uBAAuB,CAGlC,EACA,OAAO,EACP,cAAc,KAAK,EACnB,IAAI,EACJ,OAAO,EACP,aAAa,EACb,WAAW,KAAK,EAChB,CAAC,EACD,WAAW,KAAK,EACJ;IACZ,MAAM,EAAE,KAAK,EAAE,GAAG,IAAA,gJAAY;IAE9B,qBACE,8OAAC,6IAAS;QACR,SAAS;QACT,MAAM;QACN,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,4IAAQ;;kCACP,8OAAC,6IAAS;;4BACP,EAAE,GAAG,KAAK,MAAM,CAAC;4BACjB,0BAAY,8OAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;kCAE3C,8OAAC,+IAAW;kCACV,cAAA,8OAAC,4IAAM;4BACJ,GAAG,KAAK;4BACT,eAAe,MAAM,QAAQ;4BAC7B,UAAU;;8CAEV,8OAAC,mJAAa;oCAAC,WAAU;8CACvB,cAAA,8OAAC,iJAAW;wCAAC,aAAa,EAAE,GAAG,KAAK,YAAY,CAAC;;;;;;;;;;;8CAEnD,8OAAC,mJAAa;8CACX,QAAQ,GAAG,CAAC,CAAA,uBACX,8OAAC,gJAAU;4CAAC,OAAO;sDAChB,cAAc;2CADe;;;;;;;;;;;;;;;;;;;;;oBAOvC,6BACC,8OAAC,mJAAe;kCAAE,EAAE,GAAG,KAAK,YAAY,CAAC;;;;;;oBAE1C,OAAO,yBACN,8OAAC;wBAAE,WAAW,IAAA,yHAAE,EAAC;kCACd,EAAE,CAAC,MAAM,EAAE,OAAO;;;;;;;;;;;;;;;;;AAOjC", "debugId": null}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/form/select-form-input.tsx"], "sourcesContent": ["import { <PERSON>ert, AlertTitle } from '@atlas/components/ui/alert'\r\nimport {\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  useFormField,\r\n} from '@atlas/components/ui/form'\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@atlas/components/ui/select'\r\nimport { Skeleton } from '@atlas/components/ui/skeleton'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { Control, FieldPath, FieldValues } from 'react-hook-form'\r\n\r\nexport type SelectOption = {\r\n  label: string\r\n  value: string\r\n}\r\n\r\ntype Props<T extends FieldValues> = {\r\n  control: Control<T>\r\n  errorMessage?: string\r\n  loading?: boolean\r\n  name: FieldPath<T>\r\n  options: SelectOption[]\r\n  requestError?: boolean\r\n  required?: boolean\r\n  t: (message: string) => string\r\n}\r\n\r\nexport const SelectFormInput = <T extends FieldValues>({\r\n  control,\r\n  errorMessage,\r\n  loading = false,\r\n  name,\r\n  options,\r\n  requestError = false,\r\n  required = false,\r\n  t,\r\n}: Props<T>) => {\r\n  const { error } = useFormField()\r\n\r\n  return (\r\n    <FormField\r\n      control={control}\r\n      name={name}\r\n      render={({ field }) => (\r\n        <FormItem>\r\n          <FormLabel>\r\n            {t(`${name}.label`)}\r\n            {required && <p className=\"text-red-600\">*</p>}\r\n          </FormLabel>\r\n          {requestError && (\r\n            <Alert variant=\"destructive\">\r\n              <AlertTitle>{errorMessage ?? 'Error'}</AlertTitle>\r\n            </Alert>\r\n          )}\r\n          {loading && <Skeleton className=\"h-[30px] w-full\" />}\r\n          {!requestError && !loading && (\r\n            <FormControl>\r\n              <Select {...field} onValueChange={field.onChange}>\r\n                <SelectTrigger className=\"w-full\">\r\n                  <SelectValue placeholder={t(`${name}.placeholder`)} />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  {options.map(option => (\r\n                    <SelectItem value={option.value} key={option.value}>\r\n                      {option.label}\r\n                    </SelectItem>\r\n                  ))}\r\n                </SelectContent>\r\n              </Select>\r\n            </FormControl>\r\n          )}\r\n          <FormDescription>{t(`${name}.description`)}</FormDescription>\r\n          {error?.message && (\r\n            <p className={cn('text-destructive text-sm')}>\r\n              {t(`error.${error}`)}\r\n            </p>\r\n          )}\r\n        </FormItem>\r\n      )}\r\n    />\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAQA;AAOA;AACA;;;;;;;AAmBO,MAAM,kBAAkB,CAAwB,EACrD,OAAO,EACP,YAAY,EACZ,UAAU,KAAK,EACf,IAAI,EACJ,OAAO,EACP,eAAe,KAAK,EACpB,WAAW,KAAK,EAChB,CAAC,EACQ;IACT,MAAM,EAAE,KAAK,EAAE,GAAG,IAAA,gJAAY;IAE9B,qBACE,8OAAC,6IAAS;QACR,SAAS;QACT,MAAM;QACN,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,4IAAQ;;kCACP,8OAAC,6IAAS;;4BACP,EAAE,GAAG,KAAK,MAAM,CAAC;4BACjB,0BAAY,8OAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;oBAE1C,8BACC,8OAAC,0IAAK;wBAAC,SAAQ;kCACb,cAAA,8OAAC,+IAAU;sCAAE,gBAAgB;;;;;;;;;;;oBAGhC,yBAAW,8OAAC,gJAAQ;wBAAC,WAAU;;;;;;oBAC/B,CAAC,gBAAgB,CAAC,yBACjB,8OAAC,+IAAW;kCACV,cAAA,8OAAC,4IAAM;4BAAE,GAAG,KAAK;4BAAE,eAAe,MAAM,QAAQ;;8CAC9C,8OAAC,mJAAa;oCAAC,WAAU;8CACvB,cAAA,8OAAC,iJAAW;wCAAC,aAAa,EAAE,GAAG,KAAK,YAAY,CAAC;;;;;;;;;;;8CAEnD,8OAAC,mJAAa;8CACX,QAAQ,GAAG,CAAC,CAAA,uBACX,8OAAC,gJAAU;4CAAC,OAAO,OAAO,KAAK;sDAC5B,OAAO,KAAK;2CADuB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;kCAQ5D,8OAAC,mJAAe;kCAAE,EAAE,GAAG,KAAK,YAAY,CAAC;;;;;;oBACxC,OAAO,yBACN,8OAAC;wBAAE,WAAW,IAAA,yHAAE,EAAC;kCACd,EAAE,CAAC,MAAM,EAAE,OAAO;;;;;;;;;;;;;;;;;AAOjC", "debugId": null}}, {"offset": {"line": 423, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/form/polarity-aware-number-form-input.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport type { ModulePolarity } from '@atlas/constants/module'\r\nimport type { Control, FieldPath, FieldValues } from 'react-hook-form'\r\n\r\ntype Props<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TN<PERSON> extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n> = {\r\n  control: Control<TFieldValues>\r\n  name: TName\r\n  t: (key: string, options?: any) => string\r\n  polarity?: ModulePolarity\r\n  disabled?: boolean\r\n}\r\n\r\nexport function PolarityAwareNumberFormInput<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n>({ control, name, t, polarity, disabled }: Props<TFieldValues, TName>) {\r\n  // Get the base translations\r\n  const baseLabel = t(`${name}.label`)\r\n  const baseDescription = t(`${name}.description`)\r\n  const placeholder = t(`${name}.placeholder`)\r\n\r\n  // Get polarity-specific translations if polarity is provided\r\n  const polarityLabel = polarity\r\n    ? t(`${name}.labelPolarity.${polarity}`, { fallback: baseLabel })\r\n    : baseLabel\r\n\r\n  const polarityDescription = polarity\r\n    ? t(`${name}.descriptionPolarity.${polarity}`, {\r\n        fallback: baseDescription,\r\n      })\r\n    : baseDescription\r\n\r\n  // Create a custom t function that returns the polarity-aware values\r\n  const customT = (key: string) => {\r\n    if (key === `${name}.label`) {\r\n      return polarityLabel\r\n    }\r\n    if (key === `${name}.description`) {\r\n      return polarityDescription\r\n    }\r\n    if (key === `${name}.placeholder`) {\r\n      return placeholder\r\n    }\r\n    return t(key)\r\n  }\r\n\r\n  return (\r\n    <NumberFormInput\r\n      control={control}\r\n      name={name}\r\n      t={customT}\r\n      disabled={disabled}\r\n    />\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAeO,SAAS,6BAGd,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAA8B;IACpE,4BAA4B;IAC5B,MAAM,YAAY,EAAE,GAAG,KAAK,MAAM,CAAC;IACnC,MAAM,kBAAkB,EAAE,GAAG,KAAK,YAAY,CAAC;IAC/C,MAAM,cAAc,EAAE,GAAG,KAAK,YAAY,CAAC;IAE3C,6DAA6D;IAC7D,MAAM,gBAAgB,WAClB,EAAE,GAAG,KAAK,eAAe,EAAE,UAAU,EAAE;QAAE,UAAU;IAAU,KAC7D;IAEJ,MAAM,sBAAsB,WACxB,EAAE,GAAG,KAAK,qBAAqB,EAAE,UAAU,EAAE;QAC3C,UAAU;IACZ,KACA;IAEJ,oEAAoE;IACpE,MAAM,UAAU,CAAC;QACf,IAAI,QAAQ,GAAG,KAAK,MAAM,CAAC,EAAE;YAC3B,OAAO;QACT;QACA,IAAI,QAAQ,GAAG,KAAK,YAAY,CAAC,EAAE;YACjC,OAAO;QACT;QACA,IAAI,QAAQ,GAAG,KAAK,YAAY,CAAC,EAAE;YACjC,OAAO;QACT;QACA,OAAO,EAAE;IACX;IAEA,qBACE,8OAAC,kLAAe;QACd,SAAS;QACT,MAAM;QACN,GAAG;QACH,UAAU;;;;;;AAGhB", "debugId": null}}]}