{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/select.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { cn } from '@atlas/lib/utils'\r\nimport * as SelectPrimitive from '@radix-ui/react-select'\r\nimport { CheckIcon, ChevronDownIcon, ChevronUpIcon } from 'lucide-react'\r\nimport type * as React from 'react'\r\n\r\nfunction Select({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\r\n}\r\n\r\nfunction SelectGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\r\n}\r\n\r\nfunction SelectValue({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = 'default',\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: 'sm' | 'default'\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  )\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = 'popper',\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md',\r\n          position === 'popper' &&\r\n            'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',\r\n          className,\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            'p-1',\r\n            position === 'popper' &&\r\n              'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1',\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction SelectLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn('text-muted-foreground px-2 py-1.5 text-xs', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  )\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn('bg-border pointer-events-none -mx-1 my-1 h-px', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn(\r\n        'flex cursor-default items-center justify-center py-1',\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  )\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn(\r\n        'flex cursor-default items-center justify-center py-1',\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  )\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAJA;;;;;AAOA,SAAS,OAAO,KAEoC;QAFpC,EACd,GAAG,OAC+C,GAFpC;IAGd,qBAAO,6LAAC,6KAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;KAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,8KAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,YAAY,KAEgC;QAFhC,EACnB,GAAG,OACgD,GAFhC;IAGnB,qBAAO,6LAAC,8KAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;MAJS;AAMT,SAAS,cAAc,KAOtB;QAPsB,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ,GAPsB;IAQrB,qBACE,6LAAC,gLAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,IAAA,4HAAE,EACX,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,6KAAoB;gBAAC,OAAO;0BAC3B,cAAA,6LAAC,8OAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MAxBS;AA0BT,SAAS,cAAc,KAKgC;QALhC,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD,GALhC;IAMrB,qBACE,6LAAC,+KAAsB;kBACrB,cAAA,6LAAC,gLAAuB;YACtB,aAAU;YACV,WAAW,IAAA,4HAAE,EACX,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,6LAAC;;;;;8BACD,6LAAC,iLAAwB;oBACvB,WAAW,IAAA,4HAAE,EACX,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,6LAAC;;;;;;;;;;;;;;;;AAIT;MAjCS;AAmCT,SAAS,YAAY,KAGgC;QAHhC,EACnB,SAAS,EACT,GAAG,OACgD,GAHhC;IAInB,qBACE,6LAAC,8KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,WAAW,KAIgC;QAJhC,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C,GAJhC;IAKlB,qBACE,6LAAC,6KAAoB;QACnB,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,6aACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,sLAA6B;8BAC5B,cAAA,6LAAC,wNAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,6LAAC,iLAAwB;0BAAE;;;;;;;;;;;;AAGjC;MAtBS;AAwBT,SAAS,gBAAgB,KAGgC;QAHhC,EACvB,SAAS,EACT,GAAG,OACoD,GAHhC;IAIvB,qBACE,6LAAC,kLAAyB;QACxB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,qBAAqB,KAGgC;QAHhC,EAC5B,SAAS,EACT,GAAG,OACyD,GAHhC;IAI5B,qBACE,6LAAC,uLAA8B;QAC7B,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,wOAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;MAhBS;AAkBT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,yLAAgC;QAC/B,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,8OAAe;YAAC,WAAU;;;;;;;;;;;AAGjC;MAhBS", "debugId": null}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/data-table/data-table-pagination.tsx"], "sourcesContent": ["import { Button } from '@atlas/components/ui/button'\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@atlas/components/ui/select'\r\nimport type { Table } from '@tanstack/react-table'\r\nimport {\r\n  ChevronLeft,\r\n  ChevronRight,\r\n  ChevronsLeft,\r\n  ChevronsRight,\r\n} from 'lucide-react'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ninterface DataTablePaginationProps<TData> {\r\n  table: Table<TData>\r\n}\r\n\r\nexport function DataTablePagination<TData>({\r\n  table,\r\n}: DataTablePaginationProps<TData>) {\r\n  const t = useTranslations('components.data-table.pagination')\r\n\r\n  return (\r\n    <div className=\"flex items-center justify-between px-2 gap-2\">\r\n      {table.getIsSomeRowsSelected() && (\r\n        <div className=\"flex-1 text-sm text-muted-foreground\">\r\n          {t('labels.selected-rows', {\r\n            count: table.getFilteredSelectedRowModel().rows.length,\r\n            total: table.getFilteredRowModel().rows.length,\r\n          })}\r\n        </div>\r\n      )}\r\n      <div className=\"flex items-center flex-col md:flex-row justify-between w-full gap-4\">\r\n        <div className=\"flex items-center gap-4 justify-center w-full md:w-auto flex-col md:flex-row\">\r\n          <p className=\"text-sm font-medium\">{t('labels.rows-per-page')}</p>\r\n          <Select\r\n            value={`${table.getState().pagination.pageSize}`}\r\n            onValueChange={value => {\r\n              table.setPageSize(Number(value))\r\n            }}\r\n          >\r\n            <SelectTrigger className=\"h-8 w-[70px]\">\r\n              <SelectValue placeholder={table.getState().pagination.pageSize} />\r\n            </SelectTrigger>\r\n            <SelectContent side=\"top\">\r\n              {[10, 20, 30, 40, 50].map(pageSize => (\r\n                <SelectItem key={pageSize} value={`${pageSize}`}>\r\n                  {pageSize}\r\n                </SelectItem>\r\n              ))}\r\n            </SelectContent>\r\n          </Select>\r\n        </div>\r\n        <div className=\"flex items-center gap-2 flex-row justify-center w-full md:w-auto\">\r\n          <div className=\"flex w-[100px] items-center justify-center text-sm font-medium\">\r\n            {t('labels.pages', {\r\n              current: table.getState().pagination.pageIndex + 1,\r\n              total: table.getPageCount(),\r\n            })}\r\n          </div>\r\n          <Button\r\n            variant=\"outline\"\r\n            className=\"hidden h-8 w-8 p-0 lg:flex\"\r\n            onClick={() => table.setPageIndex(0)}\r\n            disabled={!table.getCanPreviousPage()}\r\n          >\r\n            <span className=\"sr-only\">{t('labels.go-to-first-page')}</span>\r\n            <ChevronsLeft />\r\n          </Button>\r\n          <Button\r\n            variant=\"outline\"\r\n            className=\"h-8 w-8 p-0\"\r\n            onClick={() => table.previousPage()}\r\n            disabled={!table.getCanPreviousPage()}\r\n          >\r\n            <span className=\"sr-only\">{t('labels.go-to-previous-page')}</span>\r\n            <ChevronLeft />\r\n          </Button>\r\n          <Button\r\n            variant=\"outline\"\r\n            className=\"h-8 w-8 p-0\"\r\n            onClick={() => table.nextPage()}\r\n            disabled={!table.getCanNextPage()}\r\n          >\r\n            <span className=\"sr-only\">{t('labels.go-to-next-page')}</span>\r\n            <ChevronRight />\r\n          </Button>\r\n          <Button\r\n            variant=\"outline\"\r\n            className=\"hidden h-8 w-8 p-0 lg:flex\"\r\n            onClick={() => table.setPageIndex(table.getPageCount() - 1)}\r\n            disabled={!table.getCanNextPage()}\r\n          >\r\n            <span className=\"sr-only\">{t('labels.go-to-last-page')}</span>\r\n            <ChevronsRight />\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAQA;AAAA;AAAA;AAAA;AAMA;;;;;;;AAMO,SAAS,oBAA2B,KAET;QAFS,EACzC,KAAK,EAC2B,GAFS;;IAGzC,MAAM,IAAI,IAAA,4NAAe,EAAC;IAE1B,qBACE,6LAAC;QAAI,WAAU;;YACZ,MAAM,qBAAqB,oBAC1B,6LAAC;gBAAI,WAAU;0BACZ,EAAE,wBAAwB;oBACzB,OAAO,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM;oBACtD,OAAO,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;gBAChD;;;;;;0BAGJ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAuB,EAAE;;;;;;0CACtC,6LAAC,+IAAM;gCACL,OAAO,AAAC,GAAuC,OAArC,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ;gCAC9C,eAAe,CAAA;oCACb,MAAM,WAAW,CAAC,OAAO;gCAC3B;;kDAEA,6LAAC,sJAAa;wCAAC,WAAU;kDACvB,cAAA,6LAAC,oJAAW;4CAAC,aAAa,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ;;;;;;;;;;;kDAEhE,6LAAC,sJAAa;wCAAC,MAAK;kDACjB;4CAAC;4CAAI;4CAAI;4CAAI;4CAAI;yCAAG,CAAC,GAAG,CAAC,CAAA,yBACxB,6LAAC,mJAAU;gDAAgB,OAAO,AAAC,GAAW,OAAT;0DAClC;+CADc;;;;;;;;;;;;;;;;;;;;;;kCAOzB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACZ,EAAE,gBAAgB;oCACjB,SAAS,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG;oCACjD,OAAO,MAAM,YAAY;gCAC3B;;;;;;0CAEF,6LAAC,+IAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM,MAAM,YAAY,CAAC;gCAClC,UAAU,CAAC,MAAM,kBAAkB;;kDAEnC,6LAAC;wCAAK,WAAU;kDAAW,EAAE;;;;;;kDAC7B,6LAAC,yOAAY;;;;;;;;;;;0CAEf,6LAAC,+IAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM,MAAM,YAAY;gCACjC,UAAU,CAAC,MAAM,kBAAkB;;kDAEnC,6LAAC;wCAAK,WAAU;kDAAW,EAAE;;;;;;kDAC7B,6LAAC,sOAAW;;;;;;;;;;;0CAEd,6LAAC,+IAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM,MAAM,QAAQ;gCAC7B,UAAU,CAAC,MAAM,cAAc;;kDAE/B,6LAAC;wCAAK,WAAU;kDAAW,EAAE;;;;;;kDAC7B,6LAAC,yOAAY;;;;;;;;;;;0CAEf,6LAAC,+IAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM,MAAM,YAAY,CAAC,MAAM,YAAY,KAAK;gCACzD,UAAU,CAAC,MAAM,cAAc;;kDAE/B,6LAAC;wCAAK,WAAU;kDAAW,EAAE;;;;;;kDAC7B,6LAAC,4OAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1B;GAnFgB;;QAGJ,4NAAe;;;KAHX", "debugId": null}}, {"offset": {"line": 522, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/data-table/data-table-view-option.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { But<PERSON> } from '@atlas//components/ui/button'\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuContent,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n} from '@atlas//components/ui/dropdown-menu'\r\nimport { DropdownMenuTrigger } from '@radix-ui/react-dropdown-menu'\r\nimport type { Table } from '@tanstack/react-table'\r\nimport { Settings2 } from 'lucide-react'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ninterface DataTableViewOptionsProps<TData> {\r\n  table: Table<TData>\r\n}\r\n\r\nexport function DataTableViewOptions<TData>({\r\n  table,\r\n}: DataTableViewOptionsProps<TData>) {\r\n  const t = useTranslations()\r\n\r\n  return (\r\n    <DropdownMenu>\r\n      <DropdownMenuTrigger asChild>\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"sm\"\r\n          className=\"ml-auto hidden h-8 lg:flex\"\r\n        >\r\n          <Settings2 />\r\n          {t('components.data-table.view-options.buttons.options.label')}\r\n        </Button>\r\n      </DropdownMenuTrigger>\r\n      <DropdownMenuContent align=\"end\" className=\"w-[150px]\">\r\n        <DropdownMenuLabel>\r\n          {t('components.data-table.view-options.labels.toggle-columns')}\r\n        </DropdownMenuLabel>\r\n        <DropdownMenuSeparator />\r\n        {table\r\n          .getAllColumns()\r\n          .filter(\r\n            column =>\r\n              typeof column.accessorFn !== 'undefined' && column.getCanHide(),\r\n          )\r\n          .map(column => {\r\n            return (\r\n              <DropdownMenuCheckboxItem\r\n                key={column.id}\r\n                className=\"capitalize\"\r\n                checked={column.getIsVisible()}\r\n                onCheckedChange={value => column.toggleVisibility(!!value)}\r\n              >\r\n                {column.columnDef?.meta?.i18nLabel || column.id}\r\n              </DropdownMenuCheckboxItem>\r\n            )\r\n          })}\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAOA;AAEA;AACA;;;AAbA;;;;;;AAmBO,SAAS,qBAA4B,KAET;QAFS,EAC1C,KAAK,EAC4B,GAFS;;IAG1C,MAAM,IAAI,IAAA,4NAAe;IAEzB,qBACE,6LAAC,+JAAY;;0BACX,6LAAC,sMAAmB;gBAAC,OAAO;0BAC1B,cAAA,6LAAC,+IAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;;sCAEV,6LAAC,gOAAS;;;;;wBACT,EAAE;;;;;;;;;;;;0BAGP,6LAAC,sKAAmB;gBAAC,OAAM;gBAAM,WAAU;;kCACzC,6LAAC,oKAAiB;kCACf,EAAE;;;;;;kCAEL,6LAAC,wKAAqB;;;;;oBACrB,MACE,aAAa,GACb,MAAM,CACL,CAAA,SACE,OAAO,OAAO,UAAU,KAAK,eAAe,OAAO,UAAU,IAEhE,GAAG,CAAC,CAAA;4BAQE,wBAAA;wBAPL,qBACE,6LAAC,2KAAwB;4BAEvB,WAAU;4BACV,SAAS,OAAO,YAAY;4BAC5B,iBAAiB,CAAA,QAAS,OAAO,gBAAgB,CAAC,CAAC,CAAC;sCAEnD,EAAA,oBAAA,OAAO,SAAS,cAAhB,yCAAA,yBAAA,kBAAkB,IAAI,cAAtB,6CAAA,uBAAwB,SAAS,KAAI,OAAO,EAAE;2BAL1C,OAAO,EAAE;;;;;oBAQpB;;;;;;;;;;;;;AAIV;GA3CgB;;QAGJ,4NAAe;;;KAHX", "debugId": null}}, {"offset": {"line": 627, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/table.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type * as React from 'react'\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<'table'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"table-container\"\r\n      className=\"relative w-full overflow-x-auto\"\r\n    >\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn('w-full caption-bottom text-sm', className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<'thead'>) {\r\n  return (\r\n    <thead\r\n      data-slot=\"table-header\"\r\n      className={cn('[&_tr]:border-b', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<'tbody'>) {\r\n  return (\r\n    <tbody\r\n      data-slot=\"table-body\"\r\n      className={cn('[&_tr:last-child]:border-0', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<'tfoot'>) {\r\n  return (\r\n    <tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn(\r\n        'bg-muted/50 border-t font-medium [&>tr]:last:border-b-0',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<'tr'>) {\r\n  return (\r\n    <tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        'hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<'th'>) {\r\n  return (\r\n    <th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        'text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<'td'>) {\r\n  return (\r\n    <td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        'p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCaption({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'caption'>) {\r\n  return (\r\n    <caption\r\n      data-slot=\"table-caption\"\r\n      className={cn('text-muted-foreground mt-4 text-sm', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAEA;AAFA;;;AAKA,SAAS,MAAM,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACb,qBACE,6LAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,6LAAC;YACC,aAAU;YACV,WAAW,IAAA,4HAAE,EAAC,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;KAbS;AAeT,SAAS,YAAY,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,UAAU,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,YAAY,KAAsD;QAAtD,EAAE,SAAS,EAAE,GAAG,OAAsC,GAAtD;IACnB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,SAAS,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IAChB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,KAAmD;QAAnD,EAAE,SAAS,EAAE,GAAG,OAAmC,GAAnD;IACjB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,aAAa,KAGY;QAHZ,EACpB,SAAS,EACT,GAAG,OAC6B,GAHZ;IAIpB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;MAXS", "debugId": null}}, {"offset": {"line": 779, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/data-table/data-table.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { DataTablePagination } from '@atlas/components/common/atlas/data-table/data-table-pagination'\r\nimport { DataTableViewOptions } from '@atlas/components/common/atlas/data-table/data-table-view-option'\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from '@atlas/components/ui/table'\r\nimport {\r\n  type ColumnDef,\r\n  flexRender,\r\n  getCoreRowModel,\r\n  getSortedRowModel,\r\n  type PaginationState,\r\n  type SortingState,\r\n  useReactTable,\r\n  type VisibilityState,\r\n} from '@tanstack/react-table'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useState } from 'react'\r\n\r\ninterface DataTableProps<TData, TValue> {\r\n  columns: ColumnDef<TData, TValue>[]\r\n  data: TData[]\r\n  onPaginationChange?: (pagination: { page: number; pageSize: number }) => void\r\n  page?: number\r\n  perPage?: number\r\n  rowCount: number\r\n  isLoading?: boolean\r\n  hidePagination?: boolean\r\n}\r\n\r\nexport function DataTable<TData, TValue>({\r\n  columns,\r\n  data,\r\n  onPaginationChange,\r\n  page = 0,\r\n  perPage = 10,\r\n  rowCount,\r\n  isLoading,\r\n  hidePagination = false,\r\n}: DataTableProps<TData, TValue>) {\r\n  const t = useTranslations('components.data-table')\r\n\r\n  const [pagination, setPagination] = useState<PaginationState>({\r\n    pageIndex: page,\r\n    pageSize: perPage,\r\n  })\r\n\r\n  useEffect(() => {\r\n    if (pagination.pageIndex !== page || pagination.pageSize !== perPage) {\r\n      onPaginationChange?.({\r\n        page: pagination.pageIndex + 1,\r\n        pageSize: pagination.pageSize,\r\n      })\r\n    }\r\n  }, [pagination, onPaginationChange, page, perPage])\r\n\r\n  const [sorting, setSorting] = useState<SortingState>([])\r\n  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})\r\n\r\n  const table = useReactTable({\r\n    columns,\r\n    data,\r\n    enableRowSelection: false,\r\n    getCoreRowModel: getCoreRowModel(),\r\n    getSortedRowModel: getSortedRowModel(),\r\n    manualPagination: true,\r\n    onPaginationChange: setPagination,\r\n    onColumnVisibilityChange: setColumnVisibility,\r\n    onSortingChange: setSorting,\r\n    rowCount,\r\n    state: {\r\n      columnVisibility,\r\n      pagination,\r\n      sorting,\r\n    },\r\n  })\r\n\r\n  return (\r\n    <div className=\"flex flex-col gap-2\">\r\n      <div className=\"flex flex-row justify-between\">\r\n        <DataTableViewOptions table={table} />\r\n      </div>\r\n      <div className=\"rounded-md border\">\r\n        <Table>\r\n          <TableHeader>\r\n            {table.getHeaderGroups().map(headerGroup => (\r\n              <TableRow key={headerGroup.id}>\r\n                {headerGroup.headers.map(header => {\r\n                  return (\r\n                    <TableHead key={header.id}>\r\n                      {header.isPlaceholder\r\n                        ? null\r\n                        : flexRender(\r\n                            header.column.columnDef.header,\r\n                            header.getContext(),\r\n                          )}\r\n                    </TableHead>\r\n                  )\r\n                })}\r\n              </TableRow>\r\n            ))}\r\n          </TableHeader>\r\n          <TableBody>\r\n            {isLoading ? (\r\n              Array.from({ length: perPage }).map((_, rowIndex) => (\r\n                <TableRow key={`skeleton-${rowIndex}`}>\r\n                  {Array.from({ length: columns.length }).map((_, colIndex) => (\r\n                    <TableCell key={`skeleton-cell-${colIndex}`}>\r\n                      <div className=\"h-4 w-full animate-pulse bg-gray-200 rounded\" />\r\n                    </TableCell>\r\n                  ))}\r\n                </TableRow>\r\n              ))\r\n            ) : table.getRowModel().rows?.length ? (\r\n              table.getRowModel().rows.map(row => (\r\n                <TableRow\r\n                  key={row.id}\r\n                  data-state={row.getIsSelected() && 'selected'}\r\n                >\r\n                  {row.getVisibleCells().map(cell => (\r\n                    <TableCell key={cell.id}>\r\n                      {flexRender(\r\n                        cell.column.columnDef.cell,\r\n                        cell.getContext(),\r\n                      )}\r\n                    </TableCell>\r\n                  ))}\r\n                </TableRow>\r\n              ))\r\n            ) : (\r\n              <TableRow>\r\n                <TableCell\r\n                  colSpan={columns.length}\r\n                  className=\"h-24 text-center\"\r\n                >\r\n                  {t('status.no-data')}\r\n                </TableCell>\r\n              </TableRow>\r\n            )}\r\n          </TableBody>\r\n        </Table>\r\n      </div>\r\n      {!hidePagination && <DataTablePagination table={table} />}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAQA;AAAA;AAUA;AACA;;;AAvBA;;;;;;;AAoCO,SAAS,UAAyB,KAST;QATS,EACvC,OAAO,EACP,IAAI,EACJ,kBAAkB,EAClB,OAAO,CAAC,EACR,UAAU,EAAE,EACZ,QAAQ,EACR,SAAS,EACT,iBAAiB,KAAK,EACQ,GATS;QAmFzB;;IAzEd,MAAM,IAAI,IAAA,4NAAe,EAAC;IAE1B,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAkB;QAC5D,WAAW;QACX,UAAU;IACZ;IAEA,IAAA,0KAAS;+BAAC;YACR,IAAI,WAAW,SAAS,KAAK,QAAQ,WAAW,QAAQ,KAAK,SAAS;gBACpE,+BAAA,yCAAA,mBAAqB;oBACnB,MAAM,WAAW,SAAS,GAAG;oBAC7B,UAAU,WAAW,QAAQ;gBAC/B;YACF;QACF;8BAAG;QAAC;QAAY;QAAoB;QAAM;KAAQ;IAElD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAe,EAAE;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,yKAAQ,EAAkB,CAAC;IAE3E,MAAM,QAAQ,IAAA,0MAAa,EAAC;QAC1B;QACA;QACA,oBAAoB;QACpB,iBAAiB,IAAA,2LAAe;QAChC,mBAAmB,IAAA,6LAAiB;QACpC,kBAAkB;QAClB,oBAAoB;QACpB,0BAA0B;QAC1B,iBAAiB;QACjB;QACA,OAAO;YACL;YACA;YACA;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,oNAAoB;oBAAC,OAAO;;;;;;;;;;;0BAE/B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6IAAK;;sCACJ,6LAAC,mJAAW;sCACT,MAAM,eAAe,GAAG,GAAG,CAAC,CAAA,4BAC3B,6LAAC,gJAAQ;8CACN,YAAY,OAAO,CAAC,GAAG,CAAC,CAAA;wCACvB,qBACE,6LAAC,iJAAS;sDACP,OAAO,aAAa,GACjB,OACA,IAAA,uMAAU,EACR,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;2CALT,OAAO,EAAE;;;;;oCAS7B;mCAZa,YAAY,EAAE;;;;;;;;;;sCAgBjC,6LAAC,iJAAS;sCACP,YACC,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAQ,GAAG,GAAG,CAAC,CAAC,GAAG,yBACtC,6LAAC,gJAAQ;8CACN,MAAM,IAAI,CAAC;wCAAE,QAAQ,QAAQ,MAAM;oCAAC,GAAG,GAAG,CAAC,CAAC,GAAG,yBAC9C,6LAAC,iJAAS;sDACR,cAAA,6LAAC;gDAAI,WAAU;;;;;;2CADD,AAAC,iBAAyB,OAAT;;;;;mCAFtB,AAAC,YAAoB,OAAT;;;;4CAQ3B,EAAA,0BAAA,MAAM,WAAW,GAAG,IAAI,cAAxB,8CAAA,wBAA0B,MAAM,IAClC,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA,oBAC3B,6LAAC,gJAAQ;oCAEP,cAAY,IAAI,aAAa,MAAM;8CAElC,IAAI,eAAe,GAAG,GAAG,CAAC,CAAA,qBACzB,6LAAC,iJAAS;sDACP,IAAA,uMAAU,EACT,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;2CAHH,KAAK,EAAE;;;;;mCAJpB,IAAI,EAAE;;;;0DAcf,6LAAC,gJAAQ;0CACP,cAAA,6LAAC,iJAAS;oCACR,SAAS,QAAQ,MAAM;oCACvB,WAAU;8CAET,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOd,CAAC,gCAAkB,6LAAC,+MAAmB;gBAAC,OAAO;;;;;;;;;;;;AAGtD;GAnHgB;;QAUJ,4NAAe;QAmBX,0MAAa;;;KA7Bb", "debugId": null}}, {"offset": {"line": 982, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/alert.tsx"], "sourcesContent": ["import { cn } from '@atlas/lib/utils'\r\nimport { cva, type VariantProps } from 'class-variance-authority'\r\nimport type * as React from 'react'\r\n\r\nconst alertVariants = cva(\r\n  'relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current',\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: 'bg-card text-card-foreground',\r\n        destructive:\r\n          'text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n    },\r\n  },\r\n)\r\n\r\nfunction Alert({\r\n  className,\r\n  variant,\r\n  ...props\r\n}: React.ComponentProps<'div'> & VariantProps<typeof alertVariants>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert\"\r\n      role=\"alert\"\r\n      className={cn(alertVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-title\"\r\n      className={cn(\r\n        'col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-description\"\r\n      className={cn(\r\n        'text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAGA,MAAM,gBAAgB,IAAA,0KAAG,EACvB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,KAIoD;QAJpD,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D,GAJpD;IAKb,qBACE,6LAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,IAAA,4HAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;KAbS;AAeT,SAAS,WAAW,KAAoD;QAApD,EAAE,SAAS,EAAE,GAAG,OAAoC,GAApD;IAClB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,KAGI;QAHJ,EACxB,SAAS,EACT,GAAG,OACyB,GAHJ;IAIxB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,kGACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS", "debugId": null}}, {"offset": {"line": 1061, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/error-alert.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { Alert, AlertDescription, AlertTitle } from '@atlas/components/ui/alert'\r\nimport type { ApiError, ValidationError } from '@atlas/types'\r\nimport { AlertCircle } from 'lucide-react'\r\nimport { signIn } from 'next-auth/react'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  error: ApiError | ValidationError | Error\r\n}\r\n\r\nexport const ErrorAlert = ({ error }: Props) => {\r\n  const t = useTranslations()\r\n\r\n  if (error instanceof Error) {\r\n    return (\r\n      <Alert variant=\"destructive\" className=\"gap-3\">\r\n        <AlertCircle className=\"h-4 w-4\" />\r\n        <AlertTitle>{t('errors.generic.title')}</AlertTitle>\r\n        <AlertDescription>{t('errors.generic.description')}</AlertDescription>\r\n        {process.env.NODE_ENV === 'development' && (\r\n          <AlertDescription>{error.message}</AlertDescription>\r\n        )}\r\n      </Alert>\r\n    )\r\n  }\r\n\r\n  if (error.type === 'ValidationError') {\r\n    return (\r\n      <Alert variant=\"destructive\" className=\"gap-3\">\r\n        <AlertCircle className=\"h-4 w-4\" />\r\n        <AlertTitle>{t(`errors.validation.${error.code}.title`)}</AlertTitle>\r\n        <AlertDescription>\r\n          {t(`errors.validation.${error.code}.description`)}\r\n        </AlertDescription>\r\n        {process.env.NODE_ENV === 'development' && (\r\n          <AlertDescription>{error.message}</AlertDescription>\r\n        )}\r\n      </Alert>\r\n    )\r\n  }\r\n\r\n  if (error.code === '401_UNAUTHORIZED') {\r\n    return signIn()\r\n  }\r\n\r\n  return (\r\n    <Alert variant=\"destructive\" className=\"gap-3\">\r\n      <AlertCircle className=\"h-4 w-4\" />\r\n      <AlertTitle>{t(`errors.api.${error.code}.title`)}</AlertTitle>\r\n      {t.has(`errors.api.${error.code}.description`) && (\r\n        <AlertDescription>\r\n          {t(`errors.api.${error.code}.description`)}\r\n        </AlertDescription>\r\n      )}\r\n    </Alert>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAqBS;;AAnBT;AAEA;AACA;AACA;;;AANA;;;;;AAYO,MAAM,aAAa;QAAC,EAAE,KAAK,EAAS;;IACzC,MAAM,IAAI,IAAA,4NAAe;IAEzB,IAAI,iBAAiB,OAAO;QAC1B,qBACE,6LAAC,6IAAK;YAAC,SAAQ;YAAc,WAAU;;8BACrC,6LAAC,sOAAW;oBAAC,WAAU;;;;;;8BACvB,6LAAC,kJAAU;8BAAE,EAAE;;;;;;8BACf,6LAAC,wJAAgB;8BAAE,EAAE;;;;;;gBACpB,oDAAyB,+BACxB,6LAAC,wJAAgB;8BAAE,MAAM,OAAO;;;;;;;;;;;;IAIxC;IAEA,IAAI,MAAM,IAAI,KAAK,mBAAmB;QACpC,qBACE,6LAAC,6IAAK;YAAC,SAAQ;YAAc,WAAU;;8BACrC,6LAAC,sOAAW;oBAAC,WAAU;;;;;;8BACvB,6LAAC,kJAAU;8BAAE,EAAE,AAAC,qBAA+B,OAAX,MAAM,IAAI,EAAC;;;;;;8BAC/C,6LAAC,wJAAgB;8BACd,EAAE,AAAC,qBAA+B,OAAX,MAAM,IAAI,EAAC;;;;;;gBAEpC,oDAAyB,+BACxB,6LAAC,wJAAgB;8BAAE,MAAM,OAAO;;;;;;;;;;;;IAIxC;IAEA,IAAI,MAAM,IAAI,KAAK,oBAAoB;QACrC,OAAO,IAAA,kJAAM;IACf;IAEA,qBACE,6LAAC,6IAAK;QAAC,SAAQ;QAAc,WAAU;;0BACrC,6LAAC,sOAAW;gBAAC,WAAU;;;;;;0BACvB,6LAAC,kJAAU;0BAAE,EAAE,AAAC,cAAwB,OAAX,MAAM,IAAI,EAAC;;;;;;YACvC,EAAE,GAAG,CAAC,AAAC,cAAwB,OAAX,MAAM,IAAI,EAAC,kCAC9B,6LAAC,wJAAgB;0BACd,EAAE,AAAC,cAAwB,OAAX,MAAM,IAAI,EAAC;;;;;;;;;;;;AAKtC;GA9Ca;;QACD,4NAAe;;;KADd", "debugId": null}}, {"offset": {"line": 1212, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { buttonVariants } from '@atlas/components/ui/button'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport * as AlertDialogPrimitive from '@radix-ui/react-alert-dialog'\r\nimport type * as React from 'react'\r\n\r\nfunction AlertDialog({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {\r\n  return <AlertDialogPrimitive.Root data-slot=\"alert-dialog\" {...props} />\r\n}\r\n\r\nfunction AlertDialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {\r\n  return (\r\n    <AlertDialogPrimitive.Trigger data-slot=\"alert-dialog-trigger\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {\r\n  return (\r\n    <AlertDialogPrimitive.Portal data-slot=\"alert-dialog-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {\r\n  return (\r\n    <AlertDialogPrimitive.Overlay\r\n      data-slot=\"alert-dialog-overlay\"\r\n      className={cn(\r\n        'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {\r\n  return (\r\n    <AlertDialogPortal>\r\n      <AlertDialogOverlay />\r\n      <AlertDialogPrimitive.Content\r\n        data-slot=\"alert-dialog-content\"\r\n        className={cn(\r\n          'bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg',\r\n          className,\r\n        )}\r\n        {...props}\r\n      />\r\n    </AlertDialogPortal>\r\n  )\r\n}\r\n\r\nfunction AlertDialogHeader({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-header\"\r\n      className={cn('flex flex-col gap-2 text-center sm:text-left', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogFooter({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-footer\"\r\n      className={cn(\r\n        'flex flex-col-reverse gap-2 sm:flex-row sm:justify-end',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {\r\n  return (\r\n    <AlertDialogPrimitive.Title\r\n      data-slot=\"alert-dialog-title\"\r\n      className={cn('text-lg font-semibold', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {\r\n  return (\r\n    <AlertDialogPrimitive.Description\r\n      data-slot=\"alert-dialog-description\"\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogAction({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {\r\n  return (\r\n    <AlertDialogPrimitive.Action\r\n      className={cn(buttonVariants(), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogCancel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {\r\n  return (\r\n    <AlertDialogPrimitive.Cancel\r\n      className={cn(buttonVariants({ variant: 'outline' }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAOA,SAAS,YAAY,KAEoC;QAFpC,EACnB,GAAG,OACoD,GAFpC;IAGnB,qBAAO,6LAAC,sLAAyB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACtE;KAJS;AAMT,SAAS,mBAAmB,KAEgC;QAFhC,EAC1B,GAAG,OACuD,GAFhC;IAG1B,qBACE,6LAAC,yLAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;MANS;AAQT,SAAS,kBAAkB,KAEgC;QAFhC,EACzB,GAAG,OACsD,GAFhC;IAGzB,qBACE,6LAAC,wLAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;MANS;AAQT,SAAS,mBAAmB,KAGgC;QAHhC,EAC1B,SAAS,EACT,GAAG,OACuD,GAHhC;IAI1B,qBACE,6LAAC,yLAA4B;QAC3B,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,mBAAmB,KAGgC;QAHhC,EAC1B,SAAS,EACT,GAAG,OACuD,GAHhC;IAI1B,qBACE,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,yLAA4B;gBAC3B,aAAU;gBACV,WAAW,IAAA,4HAAE,EACX,+WACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;MAjBS;AAmBT,SAAS,kBAAkB,KAGG;QAHH,EACzB,SAAS,EACT,GAAG,OACyB,GAHH;IAIzB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,KAGG;QAHH,EACzB,SAAS,EACT,GAAG,OACyB,GAHH;IAIzB,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,IAAA,4HAAE,EACX,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;MAdS;AAgBT,SAAS,iBAAiB,KAGgC;QAHhC,EACxB,SAAS,EACT,GAAG,OACqD,GAHhC;IAIxB,qBACE,6LAAC,uLAA0B;QACzB,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,uBAAuB,KAGgC;QAHhC,EAC9B,SAAS,EACT,GAAG,OAC2D,GAHhC;IAI9B,qBACE,6LAAC,6LAAgC;QAC/B,aAAU;QACV,WAAW,IAAA,4HAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,kBAAkB,KAGgC;QAHhC,EACzB,SAAS,EACT,GAAG,OACsD,GAHhC;IAIzB,qBACE,6LAAC,wLAA2B;QAC1B,WAAW,IAAA,4HAAE,EAAC,IAAA,uJAAc,KAAI;QAC/B,GAAG,KAAK;;;;;;AAGf;MAVS;AAYT,SAAS,kBAAkB,KAGgC;QAHhC,EACzB,SAAS,EACT,GAAG,OACsD,GAHhC;IAIzB,qBACE,6LAAC,wLAA2B;QAC1B,WAAW,IAAA,4HAAE,EAAC,IAAA,uJAAc,EAAC;YAAE,SAAS;QAAU,IAAI;QACrD,GAAG,KAAK;;;;;;AAGf;OAVS", "debugId": null}}, {"offset": {"line": 1418, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/pages/dashboard/dashboard-content/projects/projects-delete-alert.tsx"], "sourcesContent": ["'use client'\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction, // <-- The component we are modifying\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@atlas/components/ui/alert-dialog'\r\nimport { buttonVariants } from '@atlas/components/ui/button'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  project: Project\r\n  open: boolean\r\n  onOpenChange: (open: boolean) => void\r\n  onConfirm: () => void\r\n}\r\n\r\nexport const ProjectDeleteAlert = ({\r\n  project,\r\n  open,\r\n  onOpenChange,\r\n  onConfirm,\r\n}: Props) => {\r\n  const t = useTranslations('components.projects.delete-alert')\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  const handleConfirm = () => {\r\n    onConfirm()\r\n    onOpenChange(false)\r\n  }\r\n\r\n  const handleCancel = () => {\r\n    onOpenChange(false)\r\n  }\r\n\r\n  return (\r\n    <AlertDialog open={open} onOpenChange={onOpenChange}>\r\n      <AlertDialogContent>\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>{t('title')}</AlertDialogTitle>\r\n          <AlertDialogDescription>\r\n            {t('description', {\r\n              projectName: project.constructionSiteName ?? '',\r\n            })}\r\n          </AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <AlertDialogFooter>\r\n          <AlertDialogCancel onClick={handleCancel}>\r\n            {tCommon('cancel')}\r\n          </AlertDialogCancel>\r\n          <AlertDialogAction\r\n            className={buttonVariants({ variant: 'destructive' })}\r\n            onClick={handleConfirm}\r\n          >\r\n            {tCommon('delete')}\r\n          </AlertDialogAction>\r\n        </AlertDialogFooter>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AAUA;AAEA;;;AAbA;;;;AAsBO,MAAM,qBAAqB;QAAC,EACjC,OAAO,EACP,IAAI,EACJ,YAAY,EACZ,SAAS,EACH;;IACN,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,4NAAe,EAAC;IAEhC,MAAM,gBAAgB;QACpB;QACA,aAAa;IACf;IAEA,MAAM,eAAe;QACnB,aAAa;IACf;QASyB;IAPzB,qBACE,6LAAC,6JAAW;QAAC,MAAM;QAAM,cAAc;kBACrC,cAAA,6LAAC,oKAAkB;;8BACjB,6LAAC,mKAAiB;;sCAChB,6LAAC,kKAAgB;sCAAE,EAAE;;;;;;sCACrB,6LAAC,wKAAsB;sCACpB,EAAE,eAAe;gCAChB,aAAa,CAAA,gCAAA,QAAQ,oBAAoB,cAA5B,2CAAA,gCAAgC;4BAC/C;;;;;;;;;;;;8BAGJ,6LAAC,mKAAiB;;sCAChB,6LAAC,mKAAiB;4BAAC,SAAS;sCACzB,QAAQ;;;;;;sCAEX,6LAAC,mKAAiB;4BAChB,WAAW,IAAA,uJAAc,EAAC;gCAAE,SAAS;4BAAc;4BACnD,SAAS;sCAER,QAAQ;;;;;;;;;;;;;;;;;;;;;;;AAMrB;GA3Ca;;QAMD,4NAAe;QACT,4NAAe;;;KAPpB", "debugId": null}}, {"offset": {"line": 1529, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/data-table/data-table-column-header.tsx"], "sourcesContent": ["import {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from '@atlas//components/ui/dropdown-menu'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { Column } from '@tanstack/react-table'\r\nimport { ArrowDown, ArrowUp, ChevronsUpDown, EyeOff } from 'lucide-react'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ninterface DataTableColumnHeaderProps<TData, TValue>\r\n  extends React.HTMLAttributes<HTMLDivElement> {\r\n  column: Column<TData, TValue>\r\n  title: string\r\n}\r\n\r\nexport function DataTableColumnHeader<TData, TValue>({\r\n  column,\r\n  title,\r\n  className,\r\n}: DataTableColumnHeaderProps<TData, TValue>) {\r\n  const t = useTranslations('components.data-table.column-header')\r\n\r\n  if (!column.getCanSort()) {\r\n    return <div className={cn(className)}>{title}</div>\r\n  }\r\n\r\n  return (\r\n    <div className={cn('flex items-center space-x-2', className)}>\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            className=\"-ml-3 h-8 data-[state=open]:bg-accent\"\r\n          >\r\n            <span>{title}</span>\r\n            {column.getIsSorted() === 'desc' ? (\r\n              <ArrowDown />\r\n            ) : column.getIsSorted() === 'asc' ? (\r\n              <ArrowUp />\r\n            ) : (\r\n              <ChevronsUpDown />\r\n            )}\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent align=\"start\">\r\n          <DropdownMenuItem onClick={() => column.toggleSorting(false)}>\r\n            <ArrowUp className=\"h-3.5 w-3.5 text-muted-foreground/70\" />\r\n            {t('buttons.sort-ascending')}\r\n          </DropdownMenuItem>\r\n          <DropdownMenuItem onClick={() => column.toggleSorting(true)}>\r\n            <ArrowDown className=\"h-3.5 w-3.5 text-muted-foreground/70\" />\r\n            {t('buttons.sort-descending')}\r\n          </DropdownMenuItem>\r\n          <DropdownMenuSeparator />\r\n          <DropdownMenuItem onClick={() => column.toggleVisibility(false)}>\r\n            <EyeOff className=\"h-3.5 w-3.5 text-muted-foreground/70\" />\r\n            {t('buttons.hide-column')}\r\n          </DropdownMenuItem>\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAOA;AACA;AAEA;AAAA;AAAA;AAAA;AACA;;;;;;;;AAQO,SAAS,sBAAqC,KAIT;QAJS,EACnD,MAAM,EACN,KAAK,EACL,SAAS,EACiC,GAJS;;IAKnD,MAAM,IAAI,IAAA,4NAAe,EAAC;IAE1B,IAAI,CAAC,OAAO,UAAU,IAAI;QACxB,qBAAO,6LAAC;YAAI,WAAW,IAAA,4HAAE,EAAC;sBAAa;;;;;;IACzC;IAEA,qBACE,6LAAC;QAAI,WAAW,IAAA,4HAAE,EAAC,+BAA+B;kBAChD,cAAA,6LAAC,+JAAY;;8BACX,6LAAC,sKAAmB;oBAAC,OAAO;8BAC1B,cAAA,6LAAC,+IAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;;0CAEV,6LAAC;0CAAM;;;;;;4BACN,OAAO,WAAW,OAAO,uBACxB,6LAAC,gOAAS;;;;uCACR,OAAO,WAAW,OAAO,sBAC3B,6LAAC,0NAAO;;;;qDAER,6LAAC,mPAAc;;;;;;;;;;;;;;;;8BAIrB,6LAAC,sKAAmB;oBAAC,OAAM;;sCACzB,6LAAC,mKAAgB;4BAAC,SAAS,IAAM,OAAO,aAAa,CAAC;;8CACpD,6LAAC,0NAAO;oCAAC,WAAU;;;;;;gCAClB,EAAE;;;;;;;sCAEL,6LAAC,mKAAgB;4BAAC,SAAS,IAAM,OAAO,aAAa,CAAC;;8CACpD,6LAAC,gOAAS;oCAAC,WAAU;;;;;;gCACpB,EAAE;;;;;;;sCAEL,6LAAC,wKAAqB;;;;;sCACtB,6LAAC,mKAAgB;4BAAC,SAAS,IAAM,OAAO,gBAAgB,CAAC;;8CACvD,6LAAC,uNAAM;oCAAC,WAAU;;;;;;gCACjB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAMf;GAhDgB;;QAKJ,4NAAe;;;KALX", "debugId": null}}, {"offset": {"line": 1697, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/pages/dashboard/dashboard-content/projects/use-projects-columns.tsx"], "sourcesContent": ["import { DataTableColumnHeader } from '@atlas/components/common/atlas/data-table/data-table-column-header'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from '@atlas/components/ui/dropdown-menu'\r\nimport { useRouter } from '@atlas/i18n/routing'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { ColumnDef } from '@tanstack/react-table'\r\nimport { useSetAtom } from 'jotai'\r\nimport { MoreHorizontal } from 'lucide-react'\r\nimport Link from 'next/dist/client/link'\r\nimport { useFormatter, useTranslations } from 'next-intl'\r\nimport { projectToDeleteAtom } from './projects-table'\r\n\r\nexport const useProjectsColumns = () => {\r\n  const t = useTranslations('tables.projects.columns')\r\n  const tActions = useTranslations('tables.projects.actions')\r\n  const format = useFormatter()\r\n  const setProjectToDelete = useSetAtom(projectToDeleteAtom)\r\n  const router = useRouter()\r\n\r\n  return [\r\n    {\r\n      accessorKey: 'name',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title={t('name.header')} />\r\n      ),\r\n      meta: {\r\n        i18nLabel: t('name.header'),\r\n      },\r\n      cell: ({ row }) => {\r\n        const project = row.original\r\n        // 2. Handle potential null/undefined names with translation\r\n        const name = project.constructionSiteName ?? t('name.unnamed')\r\n        // 3. Define the link destination\r\n        const href = `/dashboard/projects/${project.id}`\r\n\r\n        return (\r\n          <Link\r\n            href={href}\r\n            className=\"font-medium text-primary underline-offset-4 hover:underline\"\r\n            title={name}\r\n            prefetch={false}\r\n          >\r\n            <span className=\"block max-w-[200px] truncate sm:max-w-[300px] md:max-w-[400px]\">\r\n              {name}\r\n            </span>\r\n          </Link>\r\n        )\r\n      },\r\n    },\r\n    {\r\n      accessorKey: 'company',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title={t('company.header')} />\r\n      ),\r\n      meta: {\r\n        i18nLabel: t('company.header'),\r\n      },\r\n    },\r\n    {\r\n      accessorKey: 'type',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title={t('type.header')} />\r\n      ),\r\n      meta: {\r\n        i18nLabel: t('type.header'),\r\n      },\r\n      cell: ({ row }) => {\r\n        const project = row.original\r\n\r\n        return project.processingType\r\n      },\r\n    },\r\n    {\r\n      accessorKey: 'createdAt',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title={t('created-at.header')} />\r\n      ),\r\n      cell: ({ row }) => {\r\n        if (!row.original.createdAt) {\r\n          return '-'\r\n        }\r\n\r\n        return format.dateTime(row.original.createdAt, {\r\n          year: 'numeric',\r\n          month: 'long',\r\n          day: 'numeric',\r\n        })\r\n      },\r\n      meta: {\r\n        i18nLabel: t('created-at.header'),\r\n      },\r\n    },\r\n    {\r\n      id: 'actions',\r\n      enableHiding: false,\r\n      cell: ({ row }) => {\r\n        const project = row.original\r\n\r\n        return (\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button variant=\"ghost\" className=\"h-8 w-8 p-0\">\r\n                <span className=\"sr-only\">{tActions('labels.title')}</span>\r\n                <MoreHorizontal />\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent align=\"end\">\r\n              <DropdownMenuLabel>{tActions('labels.title')}</DropdownMenuLabel>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem\r\n                onClick={() => navigator.clipboard.writeText(project.id)}\r\n              >\r\n                {tActions('labels.copy-id')}\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem onClick={() => setProjectToDelete(project)}>\r\n                {tActions('labels.delete')}\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem\r\n                onClick={() =>\r\n                  router.push(`/dashboard/projects/${project.id}/edit`)\r\n                }\r\n              >\r\n                {tActions('labels.edit')}\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        )\r\n      },\r\n    },\r\n  ] as ColumnDef<Project>[]\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAQA;AAGA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAEO,MAAM,qBAAqB;;IAChC,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,WAAW,IAAA,4NAAe,EAAC;IACjC,MAAM,SAAS,IAAA,yNAAY;IAC3B,MAAM,qBAAqB,IAAA,uJAAU,EAAC,2NAAmB;IACzD,MAAM,SAAS,IAAA,sIAAS;IAExB,OAAO;QACL;YACE,aAAa;YACb,QAAQ;oBAAC,EAAE,MAAM,EAAE;qCACjB,6LAAC,uNAAqB;oBAAC,QAAQ;oBAAQ,OAAO,EAAE;;;;;;;YAElD,MAAM;gBACJ,WAAW,EAAE;YACf;YACA,MAAM;oBAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,UAAU,IAAI,QAAQ;oBAEf;gBADb,4DAA4D;gBAC5D,MAAM,OAAO,CAAA,gCAAA,QAAQ,oBAAoB,cAA5B,2CAAA,gCAAgC,EAAE;gBAC/C,iCAAiC;gBACjC,MAAM,OAAO,AAAC,uBAAiC,OAAX,QAAQ,EAAE;gBAE9C,qBACE,6LAAC,4JAAI;oBACH,MAAM;oBACN,WAAU;oBACV,OAAO;oBACP,UAAU;8BAEV,cAAA,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;YAIT;QACF;QACA;YACE,aAAa;YACb,QAAQ;oBAAC,EAAE,MAAM,EAAE;qCACjB,6LAAC,uNAAqB;oBAAC,QAAQ;oBAAQ,OAAO,EAAE;;;;;;;YAElD,MAAM;gBACJ,WAAW,EAAE;YACf;QACF;QACA;YACE,aAAa;YACb,QAAQ;oBAAC,EAAE,MAAM,EAAE;qCACjB,6LAAC,uNAAqB;oBAAC,QAAQ;oBAAQ,OAAO,EAAE;;;;;;;YAElD,MAAM;gBACJ,WAAW,EAAE;YACf;YACA,MAAM;oBAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,UAAU,IAAI,QAAQ;gBAE5B,OAAO,QAAQ,cAAc;YAC/B;QACF;QACA;YACE,aAAa;YACb,QAAQ;oBAAC,EAAE,MAAM,EAAE;qCACjB,6LAAC,uNAAqB;oBAAC,QAAQ;oBAAQ,OAAO,EAAE;;;;;;;YAElD,MAAM;oBAAC,EAAE,GAAG,EAAE;gBACZ,IAAI,CAAC,IAAI,QAAQ,CAAC,SAAS,EAAE;oBAC3B,OAAO;gBACT;gBAEA,OAAO,OAAO,QAAQ,CAAC,IAAI,QAAQ,CAAC,SAAS,EAAE;oBAC7C,MAAM;oBACN,OAAO;oBACP,KAAK;gBACP;YACF;YACA,MAAM;gBACJ,WAAW,EAAE;YACf;QACF;QACA;YACE,IAAI;YACJ,cAAc;YACd,MAAM;oBAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,UAAU,IAAI,QAAQ;gBAE5B,qBACE,6LAAC,+JAAY;;sCACX,6LAAC,sKAAmB;4BAAC,OAAO;sCAC1B,cAAA,6LAAC,+IAAM;gCAAC,SAAQ;gCAAQ,WAAU;;kDAChC,6LAAC;wCAAK,WAAU;kDAAW,SAAS;;;;;;kDACpC,6LAAC,qOAAc;;;;;;;;;;;;;;;;sCAGnB,6LAAC,sKAAmB;4BAAC,OAAM;;8CACzB,6LAAC,oKAAiB;8CAAE,SAAS;;;;;;8CAC7B,6LAAC,wKAAqB;;;;;8CACtB,6LAAC,mKAAgB;oCACf,SAAS,IAAM,UAAU,SAAS,CAAC,SAAS,CAAC,QAAQ,EAAE;8CAEtD,SAAS;;;;;;8CAEZ,6LAAC,mKAAgB;oCAAC,SAAS,IAAM,mBAAmB;8CACjD,SAAS;;;;;;8CAEZ,6LAAC,mKAAgB;oCACf,SAAS,IACP,OAAO,IAAI,CAAC,AAAC,uBAAiC,OAAX,QAAQ,EAAE,EAAC;8CAG/C,SAAS;;;;;;;;;;;;;;;;;;YAKpB;QACF;KACD;AACH;GAtHa;;QACD,4NAAe;QACR,4NAAe;QACjB,yNAAY;QACA,uJAAU;QACtB,sIAAS", "debugId": null}}, {"offset": {"line": 1950, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/api/projects/endpoints/delete-project.ts"], "sourcesContent": ["import { handleApiError } from '@atlas/lib/api/apiErrors'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { ApiError } from '@atlas/types'\r\nimport type { AxiosResponse } from 'axios'\r\nimport { type TaskEither, tryCatch } from 'fp-ts/TaskEither'\r\nimport { apiClient } from '../../httpClient'\r\n\r\nexport const deleteProjectById = (\r\n  token: string,\r\n  id: Project['id'],\r\n): TaskEither<ApiError, AxiosResponse> =>\r\n  tryCatch(\r\n    () =>\r\n      apiClient.delete(`/api/v2/projects/${id}`, {\r\n        ...(token && {\r\n          headers: { Authorization: `Bearer ${token}` },\r\n        }),\r\n      }),\r\n    e => handleApiError(e),\r\n  )\r\n"], "names": [], "mappings": ";;;;AAAA;AAIA;AACA;;;;AAEO,MAAM,oBAAoB,CAC/B,OACA,KAEA,IAAA,4JAAQ,EACN,IACE,+IAAS,CAAC,MAAM,CAAC,AAAC,oBAAsB,OAAH,KAAM;YACzC,GAAI,SAAS;gBACX,SAAS;oBAAE,eAAe,AAAC,UAAe,OAAN;gBAAQ;YAC9C,CAAC;QACH,IACF,CAAA,IAAK,IAAA,mJAAc,EAAC", "debugId": null}}, {"offset": {"line": 1974, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/mutation/projects/use-delete-project-mutation.tsx"], "sourcesContent": ["import { deleteProjectById } from '@atlas/lib/api/projects/endpoints/delete-project'\r\nimport type { ApiError } from '@atlas/types'\r\nimport {\r\n  type UseMutationOptions,\r\n  useMutation,\r\n  useQueryClient,\r\n} from '@tanstack/react-query'\r\nimport * as E from 'fp-ts/Either'\r\nimport { pipe } from 'fp-ts/function'\r\n\r\ntype DeleteProjectError = ApiError\r\n\r\nconst mutationFn =\r\n  (token: string) =>\r\n  async (id: string): Promise<void> => {\r\n    const result = await deleteProjectById(token, id)()\r\n\r\n    return pipe(\r\n      result,\r\n      E.match(\r\n        (error: DeleteProjectError) => {\r\n          throw error\r\n        },\r\n        () => {\r\n          return\r\n        },\r\n      ),\r\n    )\r\n  }\r\n\r\nexport const useDeleteProjectMutation = (\r\n  token: string,\r\n  options?: Omit<\r\n    UseMutationOptions<void, DeleteProjectError, string>,\r\n    'mutationFn'\r\n  >,\r\n) => {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation<void, DeleteProjectError, string>({\r\n    ...options,\r\n    mutationFn: mutationFn(token),\r\n    onSuccess: (data, variables, context) => {\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n      options?.onSuccess?.(data, variables, context)\r\n    },\r\n  })\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AAAA;AAKA;AACA;;;;;;AAIA,MAAM,aACJ,CAAC,QACD,OAAO;QACL,MAAM,SAAS,MAAM,IAAA,uLAAiB,EAAC,OAAO;QAE9C,OAAO,IAAA,sJAAI,EACT,QACA,qJAAO,CACL,CAAC;YACC,MAAM;QACR,GACA;YACE;QACF;IAGN;AAEK,MAAM,2BAA2B,CACtC,OACA;;IAKA,MAAM,cAAc,IAAA,2MAAc;IAElC,OAAO,IAAA,gMAAW,EAAmC;QACnD,GAAG,OAAO;QACV,YAAY,WAAW;QACvB,SAAS;oDAAE,CAAC,MAAM,WAAW;oBAE3B;gBADA,YAAY,iBAAiB,CAAC;oBAAE,UAAU;wBAAC;qBAAW;gBAAC;gBACvD,oBAAA,+BAAA,qBAAA,QAAS,SAAS,cAAlB,yCAAA,wBAAA,SAAqB,MAAM,WAAW;YACxC;;IACF;AACF;GAjBa;;QAOS,2MAAc;QAE3B,gMAAW", "debugId": null}}, {"offset": {"line": 2028, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/pages/dashboard/dashboard-content/projects/projects-table.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { DataTable } from '@atlas/components/common/atlas/data-table/data-table'\r\nimport { ErrorAlert } from '@atlas/components/common/atlas/error-alert'\r\nimport { ProjectDeleteAlert } from '@atlas/components/pages/dashboard/dashboard-content/projects/projects-delete-alert'\r\nimport { useProjectsColumns } from '@atlas/components/pages/dashboard/dashboard-content/projects/use-projects-columns'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useDeleteProjectMutation } from '@atlas/lib/mutation/projects/use-delete-project-mutation'\r\nimport { useProjects } from '@atlas/lib/query/projects/use-projects'\r\nimport { atom, useAtom } from 'jotai'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useState } from 'react'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n}\r\n\r\nexport const projectToDeleteAtom = atom<Project | undefined>()\r\n\r\nexport default function ProjectsTable({ session }: Props) {\r\n  const [pagination, setPagination] = useState<{\r\n    page: number\r\n    pageSize: number\r\n  }>({ page: 0, pageSize: 10 })\r\n\r\n  const tMessages = useTranslations('actions.delete-project.messages')\r\n\r\n  const { data, error, isLoading, refetch } = useProjects(\r\n    session,\r\n    pagination.page,\r\n    pagination.pageSize,\r\n  )\r\n\r\n  const { mutate: deleteProject, isPending: isDeleting } =\r\n    useDeleteProjectMutation(session.accessToken, {\r\n      onSuccess: () => {\r\n        toast.success(tMessages('delete.success'))\r\n        refetch()\r\n      },\r\n      onError: err => {\r\n        toast.error(\r\n          tMessages('delete-failure', {\r\n            error: err.message || 'Unknown error',\r\n          }),\r\n        )\r\n      },\r\n    })\r\n\r\n  const columns = useProjectsColumns()\r\n\r\n  const [projectToDelete, setProjectToDelete] = useAtom<Project | undefined>(\r\n    projectToDeleteAtom,\r\n  )\r\n\r\n  const handleConfirmDelete = () => {\r\n    if (projectToDelete) {\r\n      deleteProject(projectToDelete.id)\r\n    }\r\n    setProjectToDelete(undefined)\r\n  }\r\n\r\n  const handleOpenChange = (open: boolean) => {\r\n    if (!open) {\r\n      setProjectToDelete(undefined)\r\n    }\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {error && <ErrorAlert error={error} />}\r\n      {projectToDelete && (\r\n        <ProjectDeleteAlert\r\n          project={projectToDelete}\r\n          open={!!projectToDelete}\r\n          onOpenChange={handleOpenChange}\r\n          onConfirm={handleConfirmDelete}\r\n        />\r\n      )}\r\n      <DataTable\r\n        columns={columns}\r\n        data={data?.content || []}\r\n        rowCount={data?.totalElements ?? 0}\r\n        page={pagination.page}\r\n        perPage={pagination.pageSize}\r\n        onPaginationChange={setPagination}\r\n        isLoading={isLoading || isDeleting}\r\n      />\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AACA;AAEA;AACA;AACA;AAAA;AAEA;AACA;AACA;;;AAbA;;;;;;;;;;;AAmBO,MAAM,sBAAsB,IAAA,mJAAI;AAExB,SAAS,cAAc,KAAkB;QAAlB,EAAE,OAAO,EAAS,GAAlB;;IACpC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAGzC;QAAE,MAAM;QAAG,UAAU;IAAG;IAE3B,MAAM,YAAY,IAAA,4NAAe,EAAC;IAElC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,IAAA,qKAAW,EACrD,SACA,WAAW,IAAI,EACf,WAAW,QAAQ;IAGrB,MAAM,EAAE,QAAQ,aAAa,EAAE,WAAW,UAAU,EAAE,GACpD,IAAA,0MAAwB,EAAC,QAAQ,WAAW,EAAE;QAC5C,SAAS;sDAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,UAAU;gBACxB;YACF;;QACA,OAAO;sDAAE,CAAA;gBACP,oJAAK,CAAC,KAAK,CACT,UAAU,kBAAkB;oBAC1B,OAAO,IAAI,OAAO,IAAI;gBACxB;YAEJ;;IACF;IAEF,MAAM,UAAU,IAAA,mOAAkB;IAElC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,oJAAO,EACnD;IAGF,MAAM,sBAAsB;QAC1B,IAAI,iBAAiB;YACnB,cAAc,gBAAgB,EAAE;QAClC;QACA,mBAAmB;IACrB;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,MAAM;YACT,mBAAmB;QACrB;IACF;QAgBgB;IAdhB,qBACE;;YACG,uBAAS,6LAAC,wKAAU;gBAAC,OAAO;;;;;;YAC5B,iCACC,6LAAC,oOAAkB;gBACjB,SAAS;gBACT,MAAM,CAAC,CAAC;gBACR,cAAc;gBACd,WAAW;;;;;;0BAGf,6LAAC,uLAAS;gBACR,SAAS;gBACT,MAAM,CAAA,iBAAA,2BAAA,KAAM,OAAO,KAAI,EAAE;gBACzB,UAAU,CAAA,sBAAA,iBAAA,2BAAA,KAAM,aAAa,cAAnB,iCAAA,sBAAuB;gBACjC,MAAM,WAAW,IAAI;gBACrB,SAAS,WAAW,QAAQ;gBAC5B,oBAAoB;gBACpB,WAAW,aAAa;;;;;;;;AAIhC;GAtEwB;;QAMJ,4NAAe;QAEW,qKAAW;QAOrD,0MAAwB;QAcV,mOAAkB;QAEY,oJAAO;;;KA/B/B", "debugId": null}}, {"offset": {"line": 2152, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/pages/dashboard/dashboard-content/dashboard-projects.tsx"], "sourcesContent": ["'use client'\r\nimport ProjectTable from '@atlas/components/pages/dashboard/dashboard-content/projects/projects-table'\r\nimport type { Session } from 'next-auth'\r\n\r\ntype Props = {\r\n  session: Session\r\n}\r\n\r\nexport const DashboardProjects = ({ session }: Props) => {\r\n  return <ProjectTable session={session} />\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AADA;;;AAQO,MAAM,oBAAoB;QAAC,EAAE,OAAO,EAAS;IAClD,qBAAO,6LAAC,+MAAY;QAAC,SAAS;;;;;;AAChC;KAFa", "debugId": null}}]}