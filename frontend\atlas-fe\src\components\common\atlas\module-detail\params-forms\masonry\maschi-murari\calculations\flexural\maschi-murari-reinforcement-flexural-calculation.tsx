import { CustomProductSection } from '@atlas/components/common/atlas/product/custom-product-section'
import { ProductSummaryCard } from '@atlas/components/common/atlas/product/product-summary-card'
import { NumberFormInput } from '@atlas/components/common/form/number-form-input'
import { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'
import { SelectFormInput } from '@atlas/components/common/form/select-form-input'
import { Button } from '@atlas/components/ui/button'
import { Form } from '@atlas/components/ui/form'
import { Separator } from '@atlas/components/ui/separator'
import {
  FACING_MATERIAL,
  MODULE_REINFORCEMENT_ARRANGEMENT,
  MODULE_REINFORCEMENT_FAILURE_MODE,
  REINFORCEMENT_ARRANGEMENT,
  REINFORCEMENT_FAILURE_MODE,
} from '@atlas/constants/module'
import type { ModuleWithParamsMaschiMurari } from '@atlas/lib/api/modules/schemas/modules'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import { useModuleCalculation } from '@atlas/lib/mutation/modules/use-module-calculation'
import { useProductsByCategory } from '@atlas/lib/query/products/use-products-by-category'
import {
  type MaschiMurariFlexuralReinforcementExecutionSchemaInput,
  maschiMurariFlexuralReinforcementExecutionSchema,
} from '@atlas/types/schemas/masonry/maschi-murari-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Loader2Icon } from 'lucide-react'
import Image from 'next/image'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useEffect, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { MaschiMurariReinforcementFlexuralCalculationResult } from './maschi-murari-reinforcement-flexural-calculation-result'

type Props = {
  module: ModuleWithParamsMaschiMurari
  session: Session
  projectId: Project['id']
}

// N.B: this function is temporary, until we remove the quotes in future seed
function getCleanedUpValue(
  recordToClean: Record<string, number> | undefined,
  nature: string,
) {
  // nature should be 'BRICK', 'TUFF', or 'STONE'
  if (!recordToClean) {
    return undefined
  }
  const key = `"${nature}"` // matches the API key format
  return recordToClean[key]
}

export const MaschiMurariFlexuralCalculation = ({
  session,
  module,
  projectId,
}: Props) => {
  const t = useTranslations('forms.calculations.maschi-murari.flexural')
  const tAction = useTranslations('actions.calculations.maschi-murari')
  const tCommon = useTranslations('actions.common')

  const {
    params,
    flexuralReinforcementVerifyExecutionInput,
    flexuralReinforcementCalculationResult,
  } = module

  const form = useForm<MaschiMurariFlexuralReinforcementExecutionSchemaInput>({
    resolver: zodResolver(maschiMurariFlexuralReinforcementExecutionSchema),
    defaultValues: {
      calculationType: 'FLEXURAL_VERIFY',
      input: {
        product: {
          id: flexuralReinforcementVerifyExecutionInput?.product.id,
          name: flexuralReinforcementVerifyExecutionInput?.product.name,
          sourceType:
            flexuralReinforcementVerifyExecutionInput?.product.id === 'custom'
              ? 'CUSTOM'
              : 'DATABASE',
        },
        sectionFailureMode:
          flexuralReinforcementVerifyExecutionInput?.sectionFailureMode ??
          REINFORCEMENT_FAILURE_MODE.DISTACCO_DI_ESTREMITA,
        designReinforcementStress:
          flexuralReinforcementVerifyExecutionInput?.designReinforcementStress ??
          0,
        designReinforcementStrain:
          flexuralReinforcementVerifyExecutionInput?.designReinforcementStrain ??
          0,
        reinforcedArrangement:
          flexuralReinforcementVerifyExecutionInput?.reinforcedArrangement ??
          REINFORCEMENT_ARRANGEMENT.CONTINUE,
        singleStripWidth:
          flexuralReinforcementVerifyExecutionInput?.singleStripWidth ?? 0,
        stripSpacing:
          flexuralReinforcementVerifyExecutionInput?.stripSpacing ?? 0,
        reinforcementTotalWidthAlongLength:
          flexuralReinforcementVerifyExecutionInput?.reinforcementTotalWidthAlongLength ??
          0,
        compressedEdgeReinforcementFiberDistance:
          flexuralReinforcementVerifyExecutionInput?.compressedEdgeReinforcementFiberDistance ??
          0,
        layersNumber:
          flexuralReinforcementVerifyExecutionInput?.layersNumber ?? 1,
        reinforcedSidesNumber:
          flexuralReinforcementVerifyExecutionInput?.reinforcedSidesNumber ?? 0,
        totalEquivalentThickness:
          flexuralReinforcementVerifyExecutionInput?.totalEquivalentThickness ??
          0,
        firstCoefficient:
          flexuralReinforcementVerifyExecutionInput?.firstCoefficient ?? 0,
        secondCoefficient:
          flexuralReinforcementVerifyExecutionInput?.secondCoefficient ?? 0,

        // Only for out-of-plane bending
        outOfPlanUnitWidthPanel:
          flexuralReinforcementVerifyExecutionInput?.outOfPlanUnitWidthPanel ??
          1000,
        outOfPlanAppliedDesignAxialStress:
          flexuralReinforcementVerifyExecutionInput?.outOfPlanAppliedDesignAxialStress ??
          0,
        outOfPlaneReinforcementFailureMode:
          flexuralReinforcementVerifyExecutionInput?.outOfPlaneReinforcementFailureMode ??
          REINFORCEMENT_FAILURE_MODE.DISTACCO_DI_ESTREMITA,
        outOfPlaneDesignReinforcementStrain:
          flexuralReinforcementVerifyExecutionInput?.outOfPlaneDesignReinforcementStrain ??
          0,
        outOfPlaneDesignReinforcementStress:
          flexuralReinforcementVerifyExecutionInput?.outOfPlaneDesignReinforcementStress ??
          0,

        outOfPlaneReinforcementTotalWidthAlongLength:
          flexuralReinforcementVerifyExecutionInput?.outOfPlaneReinforcementTotalWidthAlongLength ??
          0,
        outOfPlaneResistingArea:
          flexuralReinforcementVerifyExecutionInput?.outOfPlaneResistingArea ??
          0,
      },
    },
  })

  const { mutate, isPending } = useModuleCalculation(session.accessToken, {
    onSuccess: () => {
      toast.success(tAction('calculate.success'))
    },
    onError: error => {
      console.log('ERROR  ', error)
      toast.error(tAction('calculate.failure', { error: error.message }))
    },
  })

  const handleFormSubmit = (
    body: MaschiMurariFlexuralReinforcementExecutionSchemaInput,
  ) => {
    mutate({ projectId, moduleId: module.id, body })
  }

  const {
    data: products,
    isError: errorGettingProducts,
    isLoading: isLoadingProducts,
  } = useProductsByCategory(session, 'MASCHI_MURARI', 0, 100)

  const productsOptions = [
    ...(products?.content.map(m => ({
      value: m.id,
      label: m.name ?? t('product.unnamed'),
    })) ?? []),
  ]

  const [productId] = form.watch(['input.product.id'])

  const selectedProduct = useMemo(
    () => products?.content.find(p => p.id === productId),
    [productId, products],
  )

  const conventionalStrainLimit = selectedProduct?.conventionalStrainLimit
  const conversionFactor = params?.materialProperties?.conversionFactor ?? 0
  const sectionFailureModeValue =
    form.watch('input.sectionFailureMode') ??
    REINFORCEMENT_FAILURE_MODE.DISTACCO_DI_ESTREMITA

  const structuralElementsNatureValue =
    params?.materialProperties?.structuralElementsNature ??
    FACING_MATERIAL.BRICK

  const _panelGeometryHeight = params?.panelGeometry?.height ?? 0
  const panelGeometryWidth = params?.panelGeometry?.width ?? 0
  const singleStripWidthValue = form.watch('input.singleStripWidth') ?? 0
  const stripSpacingValue = form.watch('input.stripSpacing') ?? 0

  const conversionFactorValue =
    params?.materialProperties?.conversionFactor ?? 0
  const ultimateCompressiveStrainLinearBehaviourValue =
    params?.materialProperties?.ultimateCompressiveStrainLinearBehaviour ?? 0
  const ultimateCompressiveStrainValue =
    params?.materialProperties?.ultimateCompressiveStrain ?? 0

  const appliedNormalStressValue = params?.actions?.appliedNormalStress ?? 0

  const reinforcedArrangementValue =
    form.watch('input.reinforcedArrangement') ??
    REINFORCEMENT_ARRANGEMENT.CONTINUE

  const reinforcedSidesNumberValue =
    form.watch('input.reinforcedSidesNumber') ?? 0

  useEffect(() => {
    form.setValue('input.layersNumber', 1)
    form.setValue('input.outOfPlanUnitWidthPanel', 1000)
    form.setValue(
      'input.outOfPlaneReinforcementFailureMode',
      REINFORCEMENT_FAILURE_MODE.DISTACCO_INTERMEDIO,
    )
    // D61 = designReinforcementStrain =
    // =IF(D42="Distacco intermedio",(1.5*D41*D26/1.5),(D41*D26/1.5))
    // D42 = input.reinforcementFailureMode
    // D41 = product.conventionalStrainLimit
    // D26 = materialProperties.conversionFactor
    const conventionalStrainLimitValue =
      getCleanedUpValue(
        conventionalStrainLimit,
        structuralElementsNatureValue,
      ) ?? 0

    const designReinforcementStrain =
      sectionFailureModeValue === REINFORCEMENT_FAILURE_MODE.DISTACCO_INTERMEDIO
        ? (1.5 * conventionalStrainLimitValue * conversionFactor) / 1.5
        : (conventionalStrainLimitValue * conversionFactor) / 1.5

    form.setValue('input.designReinforcementStrain', designReinforcementStrain)

    // D62 = designReinforcementStress = =D43*D38
    // D43 = designReinforcementStrain
    // D38 = product.elasticModulus
    const elasticModulus = selectedProduct?.elasticModulus ?? 0
    const designReinforcementStress = designReinforcementStrain * elasticModulus
    form.setValue('input.designReinforcementStress', designReinforcementStress)

    // D66 reinforcementTotalWidthAlongLength =
    //  =IF(D63="Continuo",D9,D64*(D9/D65))
    // D63 = input.reinforcedArrangement
    // D64 = input.singleStripWidth
    // D65 = input.stripSpacing
    // D9 = width
    const reinforcementTotalWidthAlongLength =
      reinforcedArrangementValue === REINFORCEMENT_ARRANGEMENT.CONTINUE
        ? panelGeometryWidth
        : singleStripWidthValue * (panelGeometryWidth / stripSpacingValue)

    form.setValue(
      'input.reinforcementTotalWidthAlongLength',
      reinforcementTotalWidthAlongLength,
    )

    // totalEquivalentThickness
    // =IF(D63="Continuo",D69*D57,D69*D57*D64/D65)
    // D69 = reinforcedSidesNumber
    // D64 = input.singleStripWidth
    // D65 = input.stripSpacing
    // D57 = thickness of the product
    const thicknessOfTheProduct = selectedProduct?.thickness ?? 0
    const totalEquivalentThickness =
      reinforcedArrangementValue === REINFORCEMENT_ARRANGEMENT.CONTINUE
        ? reinforcedSidesNumberValue * singleStripWidthValue
        : (reinforcedSidesNumberValue *
            thicknessOfTheProduct *
            singleStripWidthValue) /
          stripSpacingValue
    form.setValue('input.totalEquivalentThickness', totalEquivalentThickness)

    // first Coefficient = D24/D25
    // D24: materialProperties.ultimateCompressiveStrainLinearBehaviour
    // D25: materialProperties.ultimateCompressiveStrain
    const firstCoefficient =
      ultimateCompressiveStrainLinearBehaviourValue /
      ultimateCompressiveStrainValue
    form.setValue('input.firstCoefficient', firstCoefficient)

    // second Coefficient = D24/D61
    // D61 = designReinforcementStrain
    const secondCoefficient =
      ultimateCompressiveStrainLinearBehaviourValue / designReinforcementStrain
    form.setValue('input.secondCoefficient', secondCoefficient)

    // OUT OF PLANE BENDING
    // D79 = outOfPlanAppliedDesignAxialStress=D29
    // D29 = actions.appliedNormalStress
    form.setValue(
      'input.outOfPlanAppliedDesignAxialStress',
      appliedNormalStressValue,
    )

    // D81 = outOfPlaneDesignReinforcementStrain
    // =  =(1.5*D59*D26/1.5)
    // D59 = product.conventionalStrainLimit
    // D26 = materialProperties.conversionFactor
    const outOfPlaneDesignReinforcementStrain =
      (1.5 * conventionalStrainLimitValue * conversionFactorValue) / 1.5
    form.setValue(
      'input.outOfPlaneDesignReinforcementStrain',
      outOfPlaneDesignReinforcementStrain,
    )

    // D82= outOfPlaneDesignReinforcementStrain=D81*D56
    // D56 = product.elasticModulus
    form.setValue(
      'input.outOfPlaneDesignReinforcementStress',
      outOfPlaneDesignReinforcementStrain * elasticModulus,
    )

    // D83 = outOfPlaneReinforcementTotalWidthAlongLength
    // =IF(D63="Continuo",D78,D64*(D78/D65))
    // D78 = width = outOfPlanUnitWidthPanel = 1000
    // D63 = input.reinforcedArrangement
    // D64 = input.singleStripWidth
    // D65 = input.stripSpacing
    const outOfPlaneReinforcementTotalWidthAlongLength =
      reinforcedArrangementValue === REINFORCEMENT_ARRANGEMENT.CONTINUE
        ? 1000
        : singleStripWidthValue * (1000 / stripSpacingValue)
    form.setValue(
      'input.outOfPlaneReinforcementTotalWidthAlongLength',
      outOfPlaneReinforcementTotalWidthAlongLength,
    )

    // D85 = outOfPlaneResistingArea
    // =D57*D68*D83
    // D57 = thickness of the product
    // D68 = reinforcedLayersNumber = 1
    form.setValue(
      'input.outOfPlaneResistingArea',
      thicknessOfTheProduct * 1 * outOfPlaneReinforcementTotalWidthAlongLength,
    )

    if (!productId) {
      return
    }
    if (productId === 'custom') {
      form.setValue('input.product.sourceType', 'CUSTOM')
    }

    if (selectedProduct) {
      form.setValue('input.product', {
        ...selectedProduct,
        sourceType: 'DATABASE',
      })
    }
  }, [
    form,
    productId,
    selectedProduct,
    conversionFactor,
    sectionFailureModeValue,
    conventionalStrainLimit,
    structuralElementsNatureValue,
    panelGeometryWidth,
    singleStripWidthValue,
    stripSpacingValue,
    reinforcedArrangementValue,
    reinforcedSidesNumberValue,
    ultimateCompressiveStrainLinearBehaviourValue,
    ultimateCompressiveStrainValue,
    appliedNormalStressValue,
    conversionFactorValue,
  ])

  return (
    <div className="flex flex-col justify-center gap-4">
      <Form {...form}>
        <form
          className="space-y-4 rounded-md border p-4"
          onSubmit={form.handleSubmit(handleFormSubmit)}
        >
          <h1 className="text-3xl font-bold">{t('heading')}</h1>
          <Image
            src="/assets/masonry_frcm/column/FRCM_COLONNE MURATURA.jpg"
            alt="flexural verify"
            height={250}
            width={500}
            className="mx-auto rounded-md object-contain"
            priority
          />
          <h1 className="text-xl font-bold">{t('inPlane.title')}</h1>
          <SelectFormInput
            control={form.control}
            name="input.product.id"
            options={productsOptions}
            t={t}
            loading={isLoadingProducts}
            requestError={errorGettingProducts}
            errorMessage={t('products.error')}
          />
          {productId === 'custom' && <CustomProductSection />}
          {selectedProduct && <ProductSummaryCard product={selectedProduct} />}
          <Separator />
          <SelectFormFixedInput
            control={form.control}
            name="input.sectionFailureMode"
            options={MODULE_REINFORCEMENT_FAILURE_MODE}
            optionLabelFn={p => t(`input.sectionFailureMode.${p}`)}
            t={t}
          />
          <NumberFormInput
            control={form.control}
            name="input.designReinforcementStrain"
            t={t}
            disabled={true}
          />
          <NumberFormInput
            control={form.control}
            name="input.designReinforcementStress"
            t={t}
            disabled={true}
          />
          <SelectFormFixedInput
            control={form.control}
            name="input.reinforcedArrangement"
            options={MODULE_REINFORCEMENT_ARRANGEMENT}
            optionLabelFn={p => t(`input.reinforcedArrangement.${p}`)}
            t={t}
          />
          <NumberFormInput
            control={form.control}
            name="input.singleStripWidth"
            t={t}
          />
          <NumberFormInput
            control={form.control}
            name="input.stripSpacing"
            t={t}
          />
          <NumberFormInput
            control={form.control}
            name="input.reinforcementTotalWidthAlongLength"
            t={t}
            disabled={true}
          />
          <NumberFormInput
            control={form.control}
            name="input.compressedEdgeReinforcementFiberDistance"
            t={t}
          />
          <NumberFormInput
            control={form.control}
            name="input.layersNumber"
            t={t}
            disabled={true}
          />
          <NumberFormInput
            control={form.control}
            name="input.reinforcedSidesNumber"
            t={t}
          />

          <NumberFormInput
            control={form.control}
            name="input.totalEquivalentThickness"
            t={t}
            disabled={true}
          />
          <NumberFormInput
            control={form.control}
            name="input.firstCoefficient"
            t={t}
            disabled={true}
          />
          <NumberFormInput
            control={form.control}
            name="input.secondCoefficient"
            t={t}
            disabled={true}
          />
          <Separator />
          <h1 className="text-xl font-bold">{t('outOfPlane.title')}</h1>
          <NumberFormInput
            control={form.control}
            name="input.outOfPlanUnitWidthPanel"
            t={t}
            disabled={true}
          />
          <NumberFormInput
            control={form.control}
            name="input.outOfPlanAppliedDesignAxialStress"
            t={t}
            disabled={true}
          />
          <SelectFormFixedInput
            control={form.control}
            name="input.outOfPlaneReinforcementFailureMode"
            options={MODULE_REINFORCEMENT_FAILURE_MODE}
            optionLabelFn={p =>
              t(`input.outOfPlaneReinforcementFailureMode.${p}`)
            }
            t={t}
            disabled={true}
          />
          <NumberFormInput
            control={form.control}
            name="input.outOfPlaneDesignReinforcementStrain"
            t={t}
            disabled={true}
          />
          <NumberFormInput
            control={form.control}
            name="input.outOfPlaneDesignReinforcementStress"
            t={t}
            disabled={true}
          />

          <NumberFormInput
            control={form.control}
            name="input.outOfPlaneReinforcementTotalWidthAlongLength"
            t={t}
            disabled={true}
          />
          <NumberFormInput
            control={form.control}
            name="input.outOfPlaneResistingArea"
            t={t}
            disabled={true}
          />
          <Button
            type="submit"
            className="w-full sm:w-auto"
            disabled={isPending}
          >
            {isPending && <Loader2Icon className="animate-spin" />}
            {tCommon('calculate')}
          </Button>
        </form>
      </Form>
      {flexuralReinforcementCalculationResult && (
        <MaschiMurariReinforcementFlexuralCalculationResult
          flexuralReinforcementCalculationResult={
            flexuralReinforcementCalculationResult
          }
        />
      )}
    </div>
  )
}
