{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-nonreinforced-section-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { NonReinforcedSectionSchema } from '@atlas/types/schemas/masonry/frcm-column-form'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  nonReinforcedSectionResults: NonReinforcedSectionSchema\r\n}\r\n\r\nexport function ConfinementNonReinforcedSectionResultCard({\r\n  nonReinforcedSectionResults,\r\n}: Props) {\r\n  const { normalStressStrength, designAxialResistance, check } =\r\n    nonReinforcedSectionResults\r\n\r\n  const t = useTranslations(\r\n    'components.calculations.frcm-column.confinementReinforcementCalculationResult.confinementNonReinforcedSectionResult',\r\n  )\r\n  const locale = useLocale()\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('normalStressStrength.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {normalStressStrength?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('designAxialResistance.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {designAxialResistance?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <span className=\"font-medium\">{t('check.label')}:</span>{' '}\r\n        <Badge\r\n          className={cn(\r\n            'text-base px-3 py-1',\r\n            check ? 'bg-green-600' : 'bg-red-600',\r\n          )}\r\n        >\r\n          {check ? t('check.satisfied') : t('check.notSatisfied')}\r\n        </Badge>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AAEA;AAAA;;;;;;;AAMO,SAAS,0CAA0C,KAElD;QAFkD,EACxD,2BAA2B,EACrB,GAFkD;;IAGxD,MAAM,EAAE,oBAAoB,EAAE,qBAAqB,EAAE,KAAK,EAAE,GAC1D;IAEF,MAAM,IAAI,IAAA,4NAAe,EACvB;IAEF,MAAM,SAAS,IAAA,kLAAS;IAExB,qBACE,6LAAC,2IAAI;;0BACH,6LAAC,iJAAU;0BACT,cAAA,6LAAC,gJAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,6LAAC,kJAAW;gBAAC,WAAU;;kCACrB,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA8B;;;;;;;4BAC3B;0CACR,6LAAC;;oCACE,iCAAA,2CAAA,qBAAsB,cAAc,CAAC,QAAQ;wCAC5C,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA+B;;;;;;;4BAC5B;0CACR,6LAAC;;oCACE,kCAAA,4CAAA,sBAAuB,cAAc,CAAC,QAAQ;wCAC7C,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,6LAAC;wBAAK,WAAU;;4BAAe,EAAE;4BAAe;;;;;;;oBAAS;kCACzD,6LAAC,6IAAK;wBACJ,WAAW,IAAA,4HAAE,EACX,uBACA,QAAQ,iBAAiB;kCAG1B,QAAQ,EAAE,qBAAqB,EAAE;;;;;;;;;;;;;;;;;;AAK5C;GAjDgB;;QAMJ,4NAAe;QAGV,kLAAS;;;KATV", "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-reinforced-section-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { ReinforcedSectionSchema } from '@atlas/types/schemas/masonry/frcm-column-form'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  reinforcedSectionResults: ReinforcedSectionSchema\r\n}\r\n\r\nexport function ConfinementReinforcedSectionResultCard({\r\n  reinforcedSectionResults,\r\n}: Props) {\r\n  const {\r\n    coefficientOfResistanceIncrease,\r\n    confinedColumnDesignResistanceWithFrcm,\r\n    designAxialResistanceOfTheConfinedColumnWithFrcm,\r\n    check,\r\n  } = reinforcedSectionResults\r\n\r\n  const t = useTranslations(\r\n    'components.calculations.frcm-column.confinementReinforcementCalculationResult.confinementReinforcedSectionResult',\r\n  )\r\n  const locale = useLocale()\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('coefficientOfResistanceIncrease.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {coefficientOfResistanceIncrease?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('confinedColumnDesignResistanceWithFrcm.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {confinedColumnDesignResistanceWithFrcm?.toLocaleString(locale, {\r\n              maximumFractionDigits: 3,\r\n            })}{' '}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <div>\r\n            <span className=\"font-medium\">\r\n              {t('designAxialResistanceOfTheConfinedColumnWithFrcm.label')}:\r\n            </span>{' '}\r\n            <span>\r\n              {designAxialResistanceOfTheConfinedColumnWithFrcm?.toLocaleString(\r\n                locale,\r\n                {\r\n                  maximumFractionDigits: 3,\r\n                },\r\n              )}{' '}\r\n            </span>\r\n          </div>\r\n          <span className=\"font-medium\">{t('check.label')}:</span>{' '}\r\n          <Badge\r\n            className={cn(\r\n              'text-base px-3 py-1',\r\n              check ? 'bg-green-600' : 'bg-red-600',\r\n            )}\r\n          >\r\n            {check ? t('check.satisfied') : t('check.notSatisfied')}\r\n          </Badge>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AAEA;AAAA;;;;;;;AAMO,SAAS,uCAAuC,KAE/C;QAF+C,EACrD,wBAAwB,EAClB,GAF+C;;IAGrD,MAAM,EACJ,+BAA+B,EAC/B,sCAAsC,EACtC,gDAAgD,EAChD,KAAK,EACN,GAAG;IAEJ,MAAM,IAAI,IAAA,4NAAe,EACvB;IAEF,MAAM,SAAS,IAAA,kLAAS;IAExB,qBACE,6LAAC,2IAAI;;0BACH,6LAAC,iJAAU;0BACT,cAAA,6LAAC,gJAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,6LAAC,kJAAW;gBAAC,WAAU;;kCACrB,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAyC;;;;;;;4BACtC;0CACR,6LAAC;;oCACE,4CAAA,sDAAA,gCAAiC,cAAc,CAAC,QAAQ;wCACvD,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAgD;;;;;;;4BAC7C;0CACR,6LAAC;;oCACE,mDAAA,6DAAA,uCAAwC,cAAc,CAAC,QAAQ;wCAC9D,uBAAuB;oCACzB;oCAAI;;;;;;;;;;;;;kCAGR,6LAAC;;0CACC,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;;4CACb,EAAE;4CAA0D;;;;;;;oCACvD;kDACR,6LAAC;;4CACE,6DAAA,uEAAA,iDAAkD,cAAc,CAC/D,QACA;gDACE,uBAAuB;4CACzB;4CACC;;;;;;;;;;;;;0CAGP,6LAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAAe;;;;;;;4BAAS;0CACzD,6LAAC,6IAAK;gCACJ,WAAW,IAAA,4HAAE,EACX,uBACA,QAAQ,iBAAiB;0CAG1B,QAAQ,EAAE,qBAAqB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAM9C;GApEgB;;QAUJ,4NAAe;QAGV,kLAAS;;;KAbV", "debugId": null}}, {"offset": {"line": 350, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx"], "sourcesContent": ["import {\r\n  <PERSON>,\r\n  CardContent,\r\n  <PERSON>Header,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>ontent,\r\n  <PERSON><PERSON><PERSON>ist,\r\n  TabsTrigger,\r\n} from '@atlas/components/ui/tabs'\r\nimport type { ConfinementReinforcementCalculationResultSchema } from '@atlas/types/schemas/masonry/frcm-column-form'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\nimport { ConfinementNonReinforcedSectionResultCard } from './confinement-nonreinforced-section-result-card'\r\nimport { ConfinementReinforcedSectionResultCard } from './confinement-reinforced-section-result-card'\r\n\r\ntype Props = {\r\n  confinementReinforcementCalculationResult: ConfinementReinforcementCalculationResultSchema\r\n}\r\n\r\nexport function FrcmColumnConfinementReinforcementCalculationResult({\r\n  confinementReinforcementCalculationResult,\r\n}: Props) {\r\n  const t = useTranslations(\r\n    'components.calculations.frcm-column.confinementReinforcementCalculationResult',\r\n  )\r\n  const locale = useLocale()\r\n\r\n  return (\r\n    <>\r\n      <h3 className=\"text-lg font-medium py-4\">{t('title')}</h3>\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle>{t('title')}</CardTitle>\r\n        </CardHeader>\r\n        <CardContent className=\"space-y-4\">\r\n          <div>\r\n            <span className=\"font-medium\">\r\n              {t('matrixGeometricPercentage.label')}:\r\n            </span>{' '}\r\n            <span>\r\n              {confinementReinforcementCalculationResult.matrixGeometricPercentage?.toLocaleString(\r\n                locale,\r\n                {\r\n                  maximumFractionDigits: 5,\r\n                },\r\n              )}{' '}\r\n              %\r\n            </span>\r\n          </div>\r\n          <div>\r\n            <span className=\"font-medium\">\r\n              {t('reinforcementGeometricPercentage.label')}:\r\n            </span>{' '}\r\n            <span>\r\n              {confinementReinforcementCalculationResult.reinforcementGeometricPercentage?.toLocaleString(\r\n                locale,\r\n                {\r\n                  maximumFractionDigits: 5,\r\n                },\r\n              )}{' '}\r\n              %\r\n            </span>\r\n          </div>\r\n          <div>\r\n            <span className=\"font-medium\">\r\n              {t('coefficicentOfHorizontalEfficiency.label')}:\r\n            </span>{' '}\r\n            <span>\r\n              {confinementReinforcementCalculationResult.coefficicentOfHorizontalEfficiency?.toLocaleString(\r\n                locale,\r\n                {\r\n                  maximumFractionDigits: 5,\r\n                },\r\n              )}{' '}\r\n            </span>\r\n          </div>\r\n          <div>\r\n            <span className=\"font-medium\">\r\n              {t('coefficicentOfVerticalEfficiency.label')}:\r\n            </span>{' '}\r\n            <span>\r\n              {confinementReinforcementCalculationResult.coefficicentOfVerticalEfficiency?.toLocaleString(\r\n                locale,\r\n                {\r\n                  maximumFractionDigits: 5,\r\n                },\r\n              )}{' '}\r\n            </span>\r\n          </div>\r\n\r\n          <div>\r\n            <span className=\"font-medium\">\r\n              {t('coefficientOfOverallEfficiency.label')}:\r\n            </span>{' '}\r\n            <span>\r\n              {confinementReinforcementCalculationResult.coefficientOfOverallEfficiency?.toLocaleString(\r\n                locale,\r\n                {\r\n                  maximumFractionDigits: 5,\r\n                },\r\n              )}{' '}\r\n            </span>\r\n          </div>\r\n          <div>\r\n            <span className=\"font-medium\">\r\n              {t('coefficientOfEffectivenessOfTheMatrix.label')}:\r\n            </span>{' '}\r\n            <span>\r\n              {confinementReinforcementCalculationResult.coefficientOfEffectivenessOfTheMatrix?.toLocaleString(\r\n                locale,\r\n                {\r\n                  maximumFractionDigits: 5,\r\n                },\r\n              )}{' '}\r\n            </span>\r\n          </div>\r\n          <div>\r\n            <span className=\"font-medium\">\r\n              {t('computationalStrainOfTheComposite.label')}:\r\n            </span>{' '}\r\n            <span>\r\n              {confinementReinforcementCalculationResult.computationalStrainOfTheComposite?.toLocaleString(\r\n                locale,\r\n                {\r\n                  maximumFractionDigits: 5,\r\n                },\r\n              )}{' '}\r\n            </span>\r\n          </div>\r\n          <div>\r\n            <span className=\"font-medium\">{t('confiningPressure.label')}:</span>{' '}\r\n            <span>\r\n              {confinementReinforcementCalculationResult.confiningPressure?.toLocaleString(\r\n                locale,\r\n                {\r\n                  maximumFractionDigits: 5,\r\n                },\r\n              )}{' '}\r\n            </span>\r\n          </div>\r\n          <div>\r\n            <span className=\"font-medium\">\r\n              {t('effectiveConfiningPressure.label')}:\r\n            </span>{' '}\r\n            <span>\r\n              {confinementReinforcementCalculationResult.effectiveConfiningPressure?.toLocaleString(\r\n                locale,\r\n                {\r\n                  maximumFractionDigits: 5,\r\n                },\r\n              )}{' '}\r\n            </span>\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n      <Tabs defaultValue=\"nonReinforcedSection\">\r\n        <TabsList>\r\n          <TabsTrigger value=\"nonReinforcedSection\">\r\n            {t('confinementNonReinforcedSectionResult.label')}\r\n          </TabsTrigger>\r\n          <TabsTrigger value=\"reinforcedSection\">\r\n            {t('confinementNonReinforcedSectionResult.label')}\r\n          </TabsTrigger>\r\n        </TabsList>\r\n        <TabsContent value=\"nonReinforcedSection\">\r\n          <ConfinementNonReinforcedSectionResultCard\r\n            nonReinforcedSectionResults={\r\n              confinementReinforcementCalculationResult?.confinementCheck\r\n                ?.nonReinforcedSection ?? {}\r\n            }\r\n          />\r\n        </TabsContent>\r\n        <TabsContent value=\"reinforcedSection\">\r\n          <ConfinementReinforcedSectionResultCard\r\n            reinforcedSectionResults={\r\n              confinementReinforcementCalculationResult?.confinementCheck\r\n                ?.reinforcedSection ?? {}\r\n            }\r\n          />\r\n        </TabsContent>\r\n      </Tabs>\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAMA;AAOA;AAAA;AACA;AACA;;;;;;;;AAMO,SAAS,oDAAoD,KAE5D;QAF4D,EAClE,yCAAyC,EACnC,GAF4D;QAqBrD,sEAcA,6EAcA,+EAaA,6EAcA,2EAaA,kFAaA,8EAWA,8DAaA,uEAsBD,6DAQA;;IAzJZ,MAAM,IAAI,IAAA,4NAAe,EACvB;IAEF,MAAM,SAAS,IAAA,kLAAS;QA8IZ,kFAQA;IApJZ,qBACE;;0BACE,6LAAC;gBAAG,WAAU;0BAA4B,EAAE;;;;;;0BAC5C,6LAAC,2IAAI;;kCACH,6LAAC,iJAAU;kCACT,cAAA,6LAAC,gJAAS;sCAAE,EAAE;;;;;;;;;;;kCAEhB,6LAAC,kJAAW;wBAAC,WAAU;;0CACrB,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;;4CACb,EAAE;4CAAmC;;;;;;;oCAChC;kDACR,6LAAC;;6CACE,uEAAA,0CAA0C,yBAAyB,cAAnE,2FAAA,qEAAqE,cAAc,CAClF,QACA;gDACE,uBAAuB;4CACzB;4CACC;4CAAI;;;;;;;;;;;;;0CAIX,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;;4CACb,EAAE;4CAA0C;;;;;;;oCACvC;kDACR,6LAAC;;6CACE,8EAAA,0CAA0C,gCAAgC,cAA1E,kGAAA,4EAA4E,cAAc,CACzF,QACA;gDACE,uBAAuB;4CACzB;4CACC;4CAAI;;;;;;;;;;;;;0CAIX,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;;4CACb,EAAE;4CAA4C;;;;;;;oCACzC;kDACR,6LAAC;;6CACE,gFAAA,0CAA0C,kCAAkC,cAA5E,oGAAA,8EAA8E,cAAc,CAC3F,QACA;gDACE,uBAAuB;4CACzB;4CACC;;;;;;;;;;;;;0CAGP,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;;4CACb,EAAE;4CAA0C;;;;;;;oCACvC;kDACR,6LAAC;;6CACE,8EAAA,0CAA0C,gCAAgC,cAA1E,kGAAA,4EAA4E,cAAc,CACzF,QACA;gDACE,uBAAuB;4CACzB;4CACC;;;;;;;;;;;;;0CAIP,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;;4CACb,EAAE;4CAAwC;;;;;;;oCACrC;kDACR,6LAAC;;6CACE,4EAAA,0CAA0C,8BAA8B,cAAxE,gGAAA,0EAA0E,cAAc,CACvF,QACA;gDACE,uBAAuB;4CACzB;4CACC;;;;;;;;;;;;;0CAGP,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;;4CACb,EAAE;4CAA+C;;;;;;;oCAC5C;kDACR,6LAAC;;6CACE,mFAAA,0CAA0C,qCAAqC,cAA/E,uGAAA,iFAAiF,cAAc,CAC9F,QACA;gDACE,uBAAuB;4CACzB;4CACC;;;;;;;;;;;;;0CAGP,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;;4CACb,EAAE;4CAA2C;;;;;;;oCACxC;kDACR,6LAAC;;6CACE,+EAAA,0CAA0C,iCAAiC,cAA3E,mGAAA,6EAA6E,cAAc,CAC1F,QACA;gDACE,uBAAuB;4CACzB;4CACC;;;;;;;;;;;;;0CAGP,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;;4CAAe,EAAE;4CAA2B;;;;;;;oCAAS;kDACrE,6LAAC;;6CACE,+DAAA,0CAA0C,iBAAiB,cAA3D,mFAAA,6DAA6D,cAAc,CAC1E,QACA;gDACE,uBAAuB;4CACzB;4CACC;;;;;;;;;;;;;0CAGP,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;;4CACb,EAAE;4CAAoC;;;;;;;oCACjC;kDACR,6LAAC;;6CACE,wEAAA,0CAA0C,0BAA0B,cAApE,4FAAA,sEAAsE,cAAc,CACnF,QACA;gDACE,uBAAuB;4CACzB;4CACC;;;;;;;;;;;;;;;;;;;;;;;;;0BAKX,6LAAC,2IAAI;gBAAC,cAAa;;kCACjB,6LAAC,+IAAQ;;0CACP,6LAAC,kJAAW;gCAAC,OAAM;0CAChB,EAAE;;;;;;0CAEL,6LAAC,kJAAW;gCAAC,OAAM;0CAChB,EAAE;;;;;;;;;;;;kCAGP,6LAAC,kJAAW;wBAAC,OAAM;kCACjB,cAAA,6LAAC,sUAAyC;4BACxC,6BACE,CAAA,mFAAA,sDAAA,iEAAA,8DAAA,0CAA2C,gBAAgB,cAA3D,kFAAA,4DACI,oBAAoB,cADxB,8FAAA,mFAC4B,CAAC;;;;;;;;;;;kCAInC,6LAAC,kJAAW;wBAAC,OAAM;kCACjB,cAAA,6LAAC,gUAAsC;4BACrC,0BACE,CAAA,gFAAA,sDAAA,iEAAA,+DAAA,0CAA2C,gBAAgB,cAA3D,mFAAA,6DACI,iBAAiB,cADrB,2FAAA,gFACyB,CAAC;;;;;;;;;;;;;;;;;;;AAOxC;GApKgB;;QAGJ,4NAAe;QAGV,kLAAS;;;KANV", "debugId": null}}, {"offset": {"line": 785, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation.tsx"], "sourcesContent": ["import { CustomProductSection } from '@atlas/components/common/atlas/product/custom-product-section'\r\nimport { ProductSummaryCard } from '@atlas/components/common/atlas/product/product-summary-card'\r\nimport { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport {\r\n  FRM_GEOMETRY_TOPOLOGY,\r\n  MODULE_REINFORCEMENT_ARRANGEMENT,\r\n  REINFORCEMENT_ARRANGEMENT,\r\n} from '@atlas/constants/module'\r\nimport type { ModuleWithParamsFrcmColumn } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useModuleCalculation } from '@atlas/lib/mutation/modules/use-module-calculation'\r\nimport { useProductsByCategory } from '@atlas/lib/query/products/use-products-by-category'\r\nimport {\r\n  frcmColumnConfinementReinforcementInput,\r\n  type frcmColumnConfinementReinforcementSchema,\r\n} from '@atlas/types/schemas/masonry/frcm-column-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useMemo } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\nimport { FrcmColumnConfinementReinforcementCalculationResult } from './frcm-column-confinement-reinforcement-calculation-result'\r\n\r\ntype Props = {\r\n  module: ModuleWithParamsFrcmColumn\r\n  session: Session\r\n  projectId: Project['id']\r\n}\r\n\r\nexport const FrcmColumnConfinementReinforcementCalculation = ({\r\n  session,\r\n  module,\r\n  projectId,\r\n}: Props) => {\r\n  const t = useTranslations('forms.calculations.frcm-column')\r\n  const tAction = useTranslations('actions.calculations.frcm-column')\r\n  const tCommon = useTranslations('actions.common')\r\n  const {\r\n    params,\r\n    confinementReinforcementVerifyInput,\r\n    confinementReinforcementCalculationResult,\r\n  } = module\r\n  const form = useForm<frcmColumnConfinementReinforcementSchema>({\r\n    resolver: zodResolver(frcmColumnConfinementReinforcementInput),\r\n    defaultValues: {\r\n      calculationType: 'CONFINEMENT_VERIFY',\r\n      input: {\r\n        product: {\r\n          id: confinementReinforcementVerifyInput?.product.id,\r\n          name: confinementReinforcementVerifyInput?.product.name,\r\n          sourceType:\r\n            confinementReinforcementVerifyInput?.product.id === 'custom'\r\n              ? 'CUSTOM'\r\n              : 'DATABASE',\r\n        },\r\n        reinforcedArrangement:\r\n          confinementReinforcementVerifyInput?.reinforcedArrangement ??\r\n          REINFORCEMENT_ARRANGEMENT.CONTINUE,\r\n        singleWidthBand:\r\n          confinementReinforcementVerifyInput?.singleWidthBand ?? 0,\r\n        stepsOfTheBand:\r\n          confinementReinforcementVerifyInput?.stepsOfTheBand ?? 0,\r\n        clearDistanceBetweenStripes:\r\n          confinementReinforcementVerifyInput?.clearDistanceBetweenStripes ?? 0,\r\n        minimalTransversalDimension:\r\n          confinementReinforcementVerifyInput?.minimalTransversalDimension ?? 0,\r\n        numberOfReinforcementLayers:\r\n          confinementReinforcementVerifyInput?.numberOfReinforcementLayers ?? 0,\r\n        matrixThicknessOfTheSingleLayer:\r\n          confinementReinforcementVerifyInput?.matrixThicknessOfTheSingleLayer ??\r\n          0,\r\n      },\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useModuleCalculation(session.accessToken, {\r\n    onSuccess: () => {\r\n      toast.success(tAction('calculate.success'))\r\n    },\r\n    onError: error => {\r\n      console.log('ERROR  ', error)\r\n      toast.error(tAction('calculate.failure', { error: error.message }))\r\n    },\r\n  })\r\n\r\n  const handleFormSubmit = (body: frcmColumnConfinementReinforcementSchema) => {\r\n    mutate({ projectId, moduleId: module.id, body })\r\n  }\r\n\r\n  const {\r\n    data: products,\r\n    isError: errorGettingProducts,\r\n    isLoading: isLoadingProducts,\r\n  } = useProductsByCategory(session, 'FRCM_COLUMN', 0, 100)\r\n\r\n  const productsOptions = [\r\n    ...(products?.content.map(m => ({\r\n      value: m.id,\r\n      label: m.name ?? t('product.unnamed'),\r\n    })) ?? []),\r\n  ]\r\n\r\n  const [productId] = form.watch(['input.product.id'])\r\n\r\n  const selectedProduct = useMemo(\r\n    () => products?.content.find(p => p.id === productId),\r\n    [productId, products],\r\n  )\r\n\r\n  const stepsOfTheBandValue = form.watch('input.stepsOfTheBand') ?? 0\r\n  const singleWidthBandValue = form.watch('input.singleWidthBand') ?? 0\r\n  const reinforcedArrangementValue =\r\n    form.watch('input.reinforcedArrangement') ??\r\n    REINFORCEMENT_ARRANGEMENT.CONTINUE\r\n\r\n  const lengthOrDiameterValue =\r\n    params?.geometry?.largerSizeOrColumnDiameter ?? 0\r\n  const smallerSize = params?.geometry?.smallerSize ?? 0\r\n  const topologyValue =\r\n    params?.geometry?.topology ?? FRM_GEOMETRY_TOPOLOGY.RECTANGULAR\r\n  useEffect(() => {\r\n    const clearDistanceBetweenStripes =\r\n      reinforcedArrangementValue === REINFORCEMENT_ARRANGEMENT.CONTINUE\r\n        ? 0\r\n        : stepsOfTheBandValue - singleWidthBandValue\r\n    form.setValue(\r\n      'input.clearDistanceBetweenStripes',\r\n      parseFloat(clearDistanceBetweenStripes.toFixed(2)),\r\n    )\r\n\r\n    // =IF(D41=\"Continuo\",\"-\",IF(C8=\"Rettangolare\",MIN(D9,D10),D9))\r\n    const minimalTransversalDimension =\r\n      reinforcedArrangementValue === REINFORCEMENT_ARRANGEMENT.CONTINUE\r\n        ? 0\r\n        : topologyValue === FRM_GEOMETRY_TOPOLOGY.RECTANGULAR\r\n          ? Math.min(lengthOrDiameterValue, smallerSize)\r\n          : lengthOrDiameterValue\r\n\r\n    form.setValue(\r\n      'input.minimalTransversalDimension',\r\n      parseFloat(minimalTransversalDimension.toFixed(2)),\r\n    )\r\n\r\n    if (!productId) {\r\n      return\r\n    }\r\n    if (productId === 'custom') {\r\n      form.setValue('input.product.sourceType', 'CUSTOM')\r\n    }\r\n\r\n    if (selectedProduct) {\r\n      form.setValue('input.product', {\r\n        ...selectedProduct,\r\n        sourceType: 'DATABASE',\r\n      })\r\n    }\r\n  }, [\r\n    form,\r\n    productId,\r\n    selectedProduct,\r\n    stepsOfTheBandValue,\r\n    singleWidthBandValue,\r\n    lengthOrDiameterValue,\r\n    smallerSize,\r\n    topologyValue,\r\n    reinforcedArrangementValue,\r\n  ])\r\n\r\n  return (\r\n    <div className=\"flex flex-col justify-center gap-4\">\r\n      <Form {...form}>\r\n        <form\r\n          className=\"space-y-4 rounded-md border p-4\"\r\n          onSubmit={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          <h1 className=\"text-3xl font-bold\">{t('heading')}</h1>\r\n          <Image\r\n            src=\"/assets/masonry_frcm/column/FRCM_COLONNE MURATURA.jpg\"\r\n            alt=\"flexural verify\"\r\n            height={250}\r\n            width={500}\r\n            className=\"mx-auto rounded-md object-contain\"\r\n            priority\r\n          />\r\n          <h1 className=\"text-xl font-bold\">{t('sub-heading')}</h1>\r\n          <SelectFormInput\r\n            control={form.control}\r\n            name=\"input.product.id\"\r\n            options={productsOptions}\r\n            t={t}\r\n            loading={isLoadingProducts}\r\n            requestError={errorGettingProducts}\r\n            errorMessage={t('products.error')}\r\n          />\r\n          {productId === 'custom' && <CustomProductSection />}\r\n          {selectedProduct && <ProductSummaryCard product={selectedProduct} />}\r\n          <Separator />\r\n          <SelectFormFixedInput\r\n            control={form.control}\r\n            name=\"input.reinforcedArrangement\"\r\n            options={MODULE_REINFORCEMENT_ARRANGEMENT}\r\n            optionLabelFn={p => t(`input.reinforcedArrangement.${p}`)}\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.singleWidthBand\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.stepsOfTheBand\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.clearDistanceBetweenStripes\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.minimalTransversalDimension\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.numberOfReinforcementLayers\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.matrixThicknessOfTheSingleLayer\"\r\n            t={t}\r\n          />\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full sm:w-auto\"\r\n            disabled={isPending}\r\n          >\r\n            {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n            {tCommon('calculate')}\r\n          </Button>\r\n        </form>\r\n      </Form>\r\n      {confinementReinforcementCalculationResult && (\r\n        <FrcmColumnConfinementReinforcementCalculationResult\r\n          confinementReinforcementCalculationResult={\r\n            confinementReinforcementCalculationResult\r\n          }\r\n        />\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;AAQO,MAAM,gDAAgD;QAAC,EAC5D,OAAO,EACP,MAAM,EACN,SAAS,EACH;QAmFJ,kBACkB,mBAElB;;IArFF,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,EACJ,MAAM,EACN,mCAAmC,EACnC,yCAAyC,EAC1C,GAAG;QAeI,4DAGA,sDAEA,qDAEA,kEAEA,kEAEA,kEAEA;IA3BR,MAAM,OAAO,IAAA,4KAAO,EAA2C;QAC7D,UAAU,IAAA,gLAAW,EAAC,0MAAuC;QAC7D,eAAe;YACb,iBAAiB;YACjB,OAAO;gBACL,SAAS;oBACP,EAAE,EAAE,gDAAA,0DAAA,oCAAqC,OAAO,CAAC,EAAE;oBACnD,IAAI,EAAE,gDAAA,0DAAA,oCAAqC,OAAO,CAAC,IAAI;oBACvD,YACE,CAAA,gDAAA,0DAAA,oCAAqC,OAAO,CAAC,EAAE,MAAK,WAChD,WACA;gBACR;gBACA,uBACE,CAAA,6DAAA,gDAAA,0DAAA,oCAAqC,qBAAqB,cAA1D,wEAAA,6DACA,2JAAyB,CAAC,QAAQ;gBACpC,iBACE,CAAA,uDAAA,gDAAA,0DAAA,oCAAqC,eAAe,cAApD,kEAAA,uDAAwD;gBAC1D,gBACE,CAAA,sDAAA,gDAAA,0DAAA,oCAAqC,cAAc,cAAnD,iEAAA,sDAAuD;gBACzD,6BACE,CAAA,mEAAA,gDAAA,0DAAA,oCAAqC,2BAA2B,cAAhE,8EAAA,mEAAoE;gBACtE,6BACE,CAAA,mEAAA,gDAAA,0DAAA,oCAAqC,2BAA2B,cAAhE,8EAAA,mEAAoE;gBACtE,6BACE,CAAA,mEAAA,gDAAA,0DAAA,oCAAqC,2BAA2B,cAAhE,8EAAA,mEAAoE;gBACtE,iCACE,CAAA,uEAAA,gDAAA,0DAAA,oCAAqC,+BAA+B,cAApE,kFAAA,uEACA;YACJ;QACF;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,6LAAoB,EAAC,QAAQ,WAAW,EAAE;QACtE,SAAS;kFAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;YACxB;;QACA,OAAO;kFAAE,CAAA;gBACP,QAAQ,GAAG,CAAC,WAAW;gBACvB,oJAAK,CAAC,KAAK,CAAC,QAAQ,qBAAqB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAClE;;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAO;YAAE;YAAW,UAAU,OAAO,EAAE;YAAE;QAAK;IAChD;IAEA,MAAM,EACJ,MAAM,QAAQ,EACd,SAAS,oBAAoB,EAC7B,WAAW,iBAAiB,EAC7B,GAAG,IAAA,iMAAqB,EAAC,SAAS,eAAe,GAAG;QAG/C;IADN,MAAM,kBAAkB;WAClB,CAAA,wBAAA,qBAAA,+BAAA,SAAU,OAAO,CAAC,GAAG,CAAC,CAAA;gBAEjB;mBAFuB;gBAC9B,OAAO,EAAE,EAAE;gBACX,OAAO,CAAA,UAAA,EAAE,IAAI,cAAN,qBAAA,UAAU,EAAE;YACrB;wBAHI,mCAAA,wBAGG,EAAE;KACV;IAED,MAAM,CAAC,UAAU,GAAG,KAAK,KAAK,CAAC;QAAC;KAAmB;IAEnD,MAAM,kBAAkB,IAAA,wKAAO;kFAC7B,IAAM,qBAAA,+BAAA,SAAU,OAAO,CAAC,IAAI;0FAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;iFAC3C;QAAC;QAAW;KAAS;QAGK;IAA5B,MAAM,sBAAsB,CAAA,cAAA,KAAK,KAAK,CAAC,qCAAX,yBAAA,cAAsC;QACrC;IAA7B,MAAM,uBAAuB,CAAA,eAAA,KAAK,KAAK,CAAC,sCAAX,0BAAA,eAAuC;QAElE;IADF,MAAM,6BACJ,CAAA,eAAA,KAAK,KAAK,CAAC,4CAAX,0BAAA,eACA,2JAAyB,CAAC,QAAQ;QAGlC;IADF,MAAM,wBACJ,CAAA,8CAAA,mBAAA,8BAAA,mBAAA,OAAQ,QAAQ,cAAhB,uCAAA,iBAAkB,0BAA0B,cAA5C,yDAAA,8CAAgD;QAC9B;IAApB,MAAM,cAAc,CAAA,+BAAA,mBAAA,8BAAA,oBAAA,OAAQ,QAAQ,cAAhB,wCAAA,kBAAkB,WAAW,cAA7B,0CAAA,+BAAiC;QAEnD;IADF,MAAM,gBACJ,CAAA,4BAAA,mBAAA,8BAAA,oBAAA,OAAQ,QAAQ,cAAhB,wCAAA,kBAAkB,QAAQ,cAA1B,uCAAA,4BAA8B,uJAAqB,CAAC,WAAW;IACjE,IAAA,0KAAS;mEAAC;YACR,MAAM,8BACJ,+BAA+B,2JAAyB,CAAC,QAAQ,GAC7D,IACA,sBAAsB;YAC5B,KAAK,QAAQ,CACX,qCACA,WAAW,4BAA4B,OAAO,CAAC;YAGjD,+DAA+D;YAC/D,MAAM,8BACJ,+BAA+B,2JAAyB,CAAC,QAAQ,GAC7D,IACA,kBAAkB,uJAAqB,CAAC,WAAW,GACjD,KAAK,GAAG,CAAC,uBAAuB,eAChC;YAER,KAAK,QAAQ,CACX,qCACA,WAAW,4BAA4B,OAAO,CAAC;YAGjD,IAAI,CAAC,WAAW;gBACd;YACF;YACA,IAAI,cAAc,UAAU;gBAC1B,KAAK,QAAQ,CAAC,4BAA4B;YAC5C;YAEA,IAAI,iBAAiB;gBACnB,KAAK,QAAQ,CAAC,iBAAiB;oBAC7B,GAAG,eAAe;oBAClB,YAAY;gBACd;YACF;QACF;kEAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,2IAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,6LAAC;oBACC,WAAU;oBACV,UAAU,KAAK,YAAY,CAAC;;sCAE5B,6LAAC;4BAAG,WAAU;sCAAsB,EAAE;;;;;;sCACtC,6LAAC,2IAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,QAAQ;4BACR,OAAO;4BACP,WAAU;4BACV,QAAQ;;;;;;sCAEV,6LAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS;4BACT,GAAG;4BACH,SAAS;4BACT,cAAc;4BACd,cAAc,EAAE;;;;;;wBAEjB,cAAc,0BAAY,6LAAC,2MAAoB;;;;;wBAC/C,iCAAmB,6LAAC,uMAAkB;4BAAC,SAAS;;;;;;sCACjD,6LAAC,qJAAS;;;;;sCACV,6LAAC,mMAAoB;4BACnB,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS,kKAAgC;4BACzC,eAAe,CAAA,IAAK,EAAE,AAAC,+BAAgC,OAAF;4BACrD,GAAG;;;;;;sCAEL,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,6LAAC,+IAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU;;gCAET,2BAAa,6LAAC,uOAAW;oCAAC,WAAU;;;;;;gCACpC,QAAQ;;;;;;;;;;;;;;;;;;YAId,2DACC,6LAAC,8VAAmD;gBAClD,2CACE;;;;;;;;;;;;AAMZ;GAlOa;;QAKD,4NAAe;QACT,4NAAe;QACf,4NAAe;QAMlB,4KAAO;QAiCU,6LAAoB;QAkB9C,iMAAqB;;;KAhEd", "debugId": null}}, {"offset": {"line": 1146, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-geometry-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport {\r\n  FRM_GEOMETRY_TOPOLOGY,\r\n  MODULE_FRM_GEOMETRY_TOPOLOGY,\r\n} from '@atlas/constants/module'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type FrcmColumnGeometryInput,\r\n  type FrcmColumnParamsSchemaInput,\r\n  frcmColumnGeometrySchema,\r\n} from '@atlas/types/schemas/masonry/frcm-column-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FrcmColumnGeometryInput>\r\n  params: FrcmColumnParamsSchemaInput\r\n  setParams: (newParams: any) => void\r\n  onSave: () => void\r\n}\r\n\r\nexport const FrcmColumnGeometryForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  params,\r\n  setParams,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.frcm-column.geometry')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const form = useForm<FrcmColumnGeometryInput>({\r\n    resolver: zodResolver(frcmColumnGeometrySchema),\r\n    defaultValues: {\r\n      topology: defaultValues?.topology ?? FRM_GEOMETRY_TOPOLOGY.RECTANGULAR,\r\n      largerSizeOrColumnDiameter:\r\n        defaultValues?.largerSizeOrColumnDiameter ?? 0,\r\n      smallerSize: defaultValues?.smallerSize ?? 0,\r\n      crossSectionArea: defaultValues?.crossSectionArea ?? 0,\r\n      crossSectionDiagonal: defaultValues?.crossSectionDiagonal ?? 0,\r\n      cornerRoundingRadius: defaultValues?.cornerRoundingRadius ?? 0,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: FrcmColumnGeometryInput) => {\r\n      // construct body based on global schema:\r\n      const frcmColumnParams: FrcmColumnParamsSchemaInput = {\r\n        ...params,\r\n        geometry: body,\r\n      }\r\n      mutate({ projectId, moduleId, body: frcmColumnParams })\r\n    },\r\n    [mutate, projectId, moduleId, params],\r\n  )\r\n\r\n  const topologyValue = form.watch('topology')\r\n  const smallerSizeValue = form.watch('smallerSize')\r\n  const largerSizeOrColumnDiameterValue = form.watch(\r\n    'largerSizeOrColumnDiameter',\r\n  )\r\n\r\n  useEffect(() => {\r\n    const smallerSize =\r\n      topologyValue === FRM_GEOMETRY_TOPOLOGY.CIRCULAR ? 0 : smallerSizeValue\r\n    form.setValue('smallerSize', parseFloat(smallerSize.toFixed(2)))\r\n\r\n    const crossSectionArea =\r\n      topologyValue === FRM_GEOMETRY_TOPOLOGY.CIRCULAR\r\n        ? Math.PI * (largerSizeOrColumnDiameterValue / 2) ** 2\r\n        : largerSizeOrColumnDiameterValue * smallerSizeValue\r\n    form.setValue('crossSectionArea', parseFloat(crossSectionArea.toFixed(2)))\r\n\r\n    const crossSectionDiagonal =\r\n      topologyValue === FRM_GEOMETRY_TOPOLOGY.CIRCULAR\r\n        ? 0\r\n        : Math.sqrt(\r\n            largerSizeOrColumnDiameterValue ** 2 + smallerSizeValue ** 2,\r\n          )\r\n    form.setValue(\r\n      'crossSectionDiagonal',\r\n      parseFloat(crossSectionDiagonal.toFixed(2)),\r\n    )\r\n  }, [topologyValue, smallerSizeValue, largerSizeOrColumnDiameterValue, form])\r\n\r\n  useEffect(() => {\r\n    const subscription = form.watch(values => {\r\n      setParams((prev: any) => ({\r\n        ...prev,\r\n        buildingCharacteristics: values,\r\n      }))\r\n    })\r\n    return () => subscription.unsubscribe()\r\n  }, [form, setParams])\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <Image\r\n          src=\"/assets/masonry_frcm/FRCM_Muratura.jpg\"\r\n          alt=\"site characteristics\"\r\n          height={250}\r\n          width={500}\r\n          className=\"mx-auto rounded-md object-contain\"\r\n          priority\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"topology\"\r\n          options={MODULE_FRM_GEOMETRY_TOPOLOGY}\r\n          optionLabelFn={p => t(`topology.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"largerSizeOrColumnDiameter\"\r\n          t={msg => t(`${topologyValue}.${msg}`)}\r\n          disabled={topologyValue === FRM_GEOMETRY_TOPOLOGY.CIRCULAR}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"smallerSize\"\r\n          t={msg => t(`${topologyValue}.${msg}`)}\r\n          disabled={topologyValue === FRM_GEOMETRY_TOPOLOGY.CIRCULAR}\r\n        />\r\n\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"crossSectionArea\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"crossSectionDiagonal\"\r\n          t={msg => t(`${topologyValue}.${msg}`)}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"cornerRoundingRadius\"\r\n          t={msg => t(`${topologyValue}.${msg}`)}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAMA;AACA;AAKA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;AAYO,MAAM,yBAAyB;QAAC,EACrC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACN,SAAS,EACT,MAAM,EACA;;IACN,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,cAAc,IAAA,2MAAc;QAKpB,yBAER,2CACW,4BACK,iCACI,qCACA;IAT1B,MAAM,OAAO,IAAA,4KAAO,EAA0B;QAC5C,UAAU,IAAA,gLAAW,EAAC,2LAAwB;QAC9C,eAAe;YACb,UAAU,CAAA,0BAAA,0BAAA,oCAAA,cAAe,QAAQ,cAAvB,qCAAA,0BAA2B,uJAAqB,CAAC,WAAW;YACtE,4BACE,CAAA,4CAAA,0BAAA,oCAAA,cAAe,0BAA0B,cAAzC,uDAAA,4CAA6C;YAC/C,aAAa,CAAA,6BAAA,0BAAA,oCAAA,cAAe,WAAW,cAA1B,wCAAA,6BAA8B;YAC3C,kBAAkB,CAAA,kCAAA,0BAAA,oCAAA,cAAe,gBAAgB,cAA/B,6CAAA,kCAAmC;YACrD,sBAAsB,CAAA,sCAAA,0BAAA,oCAAA,cAAe,oBAAoB,cAAnC,iDAAA,sCAAuC;YAC7D,sBAAsB,CAAA,sCAAA,0BAAA,oCAAA,cAAe,oBAAoB,cAAnC,iDAAA,sCAAuC;QAC/D;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,mNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,SAAS;kEAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;gBACtB,MAAM,YAAY,iBAAiB,CAAC;oBAClC,UAAU;wBAAC;wBAAW;4BAAE;4BAAW;wBAAS;qBAAE;gBAChD;gBACA;YACF;;QACA,OAAO;kEAAE,CAAA;gBACP,oJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAC7D;;IACF;IAGF,MAAM,mBAAmB,IAAA,4KAAW;gEAClC,CAAC;YACC,yCAAyC;YACzC,MAAM,mBAAgD;gBACpD,GAAG,MAAM;gBACT,UAAU;YACZ;YACA,OAAO;gBAAE;gBAAW;gBAAU,MAAM;YAAiB;QACvD;+DACA;QAAC;QAAQ;QAAW;QAAU;KAAO;IAGvC,MAAM,gBAAgB,KAAK,KAAK,CAAC;IACjC,MAAM,mBAAmB,KAAK,KAAK,CAAC;IACpC,MAAM,kCAAkC,KAAK,KAAK,CAChD;IAGF,IAAA,0KAAS;4CAAC;YACR,MAAM,cACJ,kBAAkB,uJAAqB,CAAC,QAAQ,GAAG,IAAI;YACzD,KAAK,QAAQ,CAAC,eAAe,WAAW,YAAY,OAAO,CAAC;YAE5D,MAAM,mBACJ,kBAAkB,uJAAqB,CAAC,QAAQ,GAC5C,KAAK,EAAE,GAAG,CAAC,kCAAkC,CAAC,KAAK,IACnD,kCAAkC;YACxC,KAAK,QAAQ,CAAC,oBAAoB,WAAW,iBAAiB,OAAO,CAAC;YAEtE,MAAM,uBACJ,kBAAkB,uJAAqB,CAAC,QAAQ,GAC5C,IACA,KAAK,IAAI,CACP,mCAAmC,IAAI,oBAAoB;YAEnE,KAAK,QAAQ,CACX,wBACA,WAAW,qBAAqB,OAAO,CAAC;QAE5C;2CAAG;QAAC;QAAe;QAAkB;QAAiC;KAAK;IAE3E,IAAA,0KAAS;4CAAC;YACR,MAAM,eAAe,KAAK,KAAK;iEAAC,CAAA;oBAC9B;yEAAU,CAAC,OAAc,CAAC;gCACxB,GAAG,IAAI;gCACP,yBAAyB;4BAC3B,CAAC;;gBACH;;YACA;oDAAO,IAAM,aAAa,WAAW;;QACvC;2CAAG;QAAC;QAAM;KAAU;IAEpB,qBACE,6LAAC,2IAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,6LAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,6LAAC,2IAAK;oBACJ,KAAI;oBACJ,KAAI;oBACJ,QAAQ;oBACR,OAAO;oBACP,WAAU;oBACV,QAAQ;;;;;;8BAEV,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,8JAA4B;oBACrC,eAAe,CAAA,IAAK,EAAE,AAAC,YAAa,OAAF;oBAClC,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG,CAAA,MAAO,EAAE,AAAC,GAAmB,OAAjB,eAAc,KAAO,OAAJ;oBAChC,UAAU,kBAAkB,uJAAqB,CAAC,QAAQ;;;;;;8BAE5D,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG,CAAA,MAAO,EAAE,AAAC,GAAmB,OAAjB,eAAc,KAAO,OAAJ;oBAChC,UAAU,kBAAkB,uJAAqB,CAAC,QAAQ;;;;;;8BAG5D,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG,CAAA,MAAO,EAAE,AAAC,GAAmB,OAAjB,eAAc,KAAO,OAAJ;oBAChC,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG,CAAA,MAAO,EAAE,AAAC,GAAmB,OAAjB,eAAc,KAAO,OAAJ;;;;;;8BAElC,6LAAC,+IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,6LAAC,uOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB;GA7Ja;;QASD,4NAAe;QACT,4NAAe;QACf,4NAAe;QACX,2MAAc;QAErB,4KAAO;QAaU,mNAA2B;;;KA3B9C", "debugId": null}}, {"offset": {"line": 1413, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-masonry-characteristics-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport {\r\n  CRM_MASONRY_TYPE,\r\n  characteristicCompressiveStrengthValues,\r\n  characteristicNormalElasticityModulusValues,\r\n  characteristicShearElasticityModulusValues,\r\n  characteristicShearStrengthValues,\r\n  executionClass,\r\n  FACING_MATERIAL,\r\n  loadResistingCategory,\r\n  MODULE_CRM_MASONRY_TYPE,\r\n  MODULE_EXECUTION_CLASS,\r\n  MODULE_FACING_MATERIAL,\r\n  MODULE_GEOMETRY_EXPOSURE,\r\n  MODULE_LOAD_RESISTING_CATEGORY,\r\n  MODULE_MATERIAL_KNOWLEDGE_LEVEL,\r\n  masonryDensityValues,\r\n  masonryStrengthSafetyFactorMapping,\r\n  moduleGeometryExposure,\r\n  moduleMaterialKnowledgeLevel,\r\n  moduleMaterialKnowledgeLevelValues,\r\n} from '@atlas/constants/module'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type FrcmColumnMasonryCharacteristicsInput,\r\n  type FrcmColumnParamsSchemaInput,\r\n  frcmColumnMasonryCharacteristicsSchema,\r\n} from '@atlas/types/schemas/masonry/frcm-column-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FrcmColumnMasonryCharacteristicsInput>\r\n  params: FrcmColumnParamsSchemaInput\r\n  setParams: (newParams: any) => void\r\n  onSave: () => void\r\n}\r\n\r\nexport const FrcmColumnMasonryCharacteristicsForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  params,\r\n  setParams,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.project-params.frcm-column.masonry-characteristics',\r\n  )\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const form = useForm<FrcmColumnMasonryCharacteristicsInput>({\r\n    resolver: zodResolver(frcmColumnMasonryCharacteristicsSchema),\r\n    defaultValues: {\r\n      material: defaultValues?.material ?? FACING_MATERIAL.BRICK,\r\n      enhancementCharacteristics:\r\n        defaultValues?.enhancementCharacteristics ??\r\n        CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA,\r\n      knowledgeLevel:\r\n        defaultValues?.knowledgeLevel ?? moduleMaterialKnowledgeLevel.LC1,\r\n      confidenceFactor: defaultValues?.confidenceFactor ?? 0,\r\n      executionClass: defaultValues?.executionClass ?? executionClass.ONE,\r\n      loadResistantCategory:\r\n        defaultValues?.loadResistantCategory ??\r\n        loadResistingCategory.MASONRY_CAT_I_AND_MORTAR_WITH_GUARANTEED_PERFORMANCE,\r\n      masonrySafetyFactor: defaultValues?.masonrySafetyFactor ?? 0,\r\n      characteristicCompressiveStrength:\r\n        defaultValues?.characteristicCompressiveStrength ?? 0,\r\n      designCompressiveStrength: defaultValues?.designCompressiveStrength ?? 0,\r\n      characteristicShearStrength:\r\n        defaultValues?.characteristicShearStrength ?? 0,\r\n      designShearStrength: defaultValues?.designShearStrength ?? 0,\r\n      normalElasticityModulus: defaultValues?.normalElasticityModulus ?? 0,\r\n      shearElasticityModulus: defaultValues?.shearElasticityModulus ?? 0,\r\n      masonryDensity: defaultValues?.masonryDensity ?? 0,\r\n      exposureType:\r\n        defaultValues?.exposureType ?? moduleGeometryExposure.INTERNAL,\r\n      conversionFactor: defaultValues?.conversionFactor ?? 0,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: FrcmColumnMasonryCharacteristicsInput) => {\r\n      // construct body based on global schema:\r\n      const frcmColumnParams: FrcmColumnParamsSchemaInput = {\r\n        ...params,\r\n        masonryCharacteristics: body,\r\n      }\r\n      mutate({ projectId, moduleId, body: frcmColumnParams })\r\n    },\r\n    [mutate, projectId, moduleId, params],\r\n  )\r\n\r\n  const knowledgeMaterialLevel =\r\n    form.watch('knowledgeLevel') ?? moduleMaterialKnowledgeLevel.LC1\r\n  const executionClassFormValue =\r\n    form.watch('executionClass') ?? executionClass.ONE\r\n  const loadResistingCategoryFormValue =\r\n    form.watch('loadResistantCategory') ??\r\n    loadResistingCategory.MASONRY_CAT_I_AND_MORTAR_WITH_GUARANTEED_PERFORMANCE\r\n  const enhancementCharacteristics =\r\n    form.watch('enhancementCharacteristics') ??\r\n    CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA\r\n  const exposureTypeValue =\r\n    form.watch('exposureType') ?? moduleGeometryExposure.INTERNAL\r\n\r\n  useEffect(() => {\r\n    const calculateGivenStrength = (strengthMinMax: {\r\n      min: number\r\n      max: number\r\n    }) => {\r\n      return knowledgeMaterialLevel === moduleMaterialKnowledgeLevel.LC1\r\n        ? strengthMinMax.min\r\n        : knowledgeMaterialLevel === moduleMaterialKnowledgeLevel.LC3\r\n          ? strengthMinMax.max\r\n          : (strengthMinMax.min + strengthMinMax.max) / 2\r\n    }\r\n\r\n    const confidenceFactor =\r\n      moduleMaterialKnowledgeLevelValues[knowledgeMaterialLevel]\r\n    form.setValue('confidenceFactor', confidenceFactor)\r\n\r\n    const masonryStrengthSafetyFactor =\r\n      masonryStrengthSafetyFactorMapping[loadResistingCategoryFormValue][\r\n        executionClassFormValue\r\n      ]\r\n    form.setValue('masonrySafetyFactor', masonryStrengthSafetyFactor)\r\n\r\n    const characteristicCompressiveStrengthMinMax =\r\n      characteristicCompressiveStrengthValues[enhancementCharacteristics]\r\n\r\n    const characteristicCompressiveStrength = calculateGivenStrength(\r\n      characteristicCompressiveStrengthMinMax,\r\n    )\r\n    form.setValue(\r\n      'characteristicCompressiveStrength',\r\n      characteristicCompressiveStrength,\r\n    )\r\n\r\n    const designCompressiveStrength =\r\n      characteristicCompressiveStrength /\r\n      masonryStrengthSafetyFactor /\r\n      confidenceFactor\r\n    form.setValue('designCompressiveStrength', designCompressiveStrength)\r\n\r\n    const characteristicsShearStrengthMinMax =\r\n      characteristicShearStrengthValues[enhancementCharacteristics]\r\n    const characteristicShearStrength = calculateGivenStrength(\r\n      characteristicsShearStrengthMinMax,\r\n    )\r\n    form.setValue('characteristicShearStrength', characteristicShearStrength)\r\n\r\n    const designShearStrength =\r\n      characteristicShearStrength /\r\n      confidenceFactor /\r\n      masonryStrengthSafetyFactor\r\n    form.setValue('designShearStrength', designShearStrength)\r\n\r\n    const normalElasticityModulusMinMax =\r\n      characteristicNormalElasticityModulusValues[enhancementCharacteristics]\r\n    const normalElasticityModulus =\r\n      (normalElasticityModulusMinMax.min + normalElasticityModulusMinMax.max) /\r\n      2\r\n    form.setValue('normalElasticityModulus', normalElasticityModulus)\r\n\r\n    const shearElasticityModulusMinMax =\r\n      characteristicShearElasticityModulusValues[enhancementCharacteristics]\r\n    const shearElasticityModulus =\r\n      (shearElasticityModulusMinMax.min + shearElasticityModulusMinMax.max) / 2\r\n    form.setValue('shearElasticityModulus', shearElasticityModulus)\r\n\r\n    const masonryDensity = masonryDensityValues[enhancementCharacteristics]\r\n    form.setValue('masonryDensity', masonryDensity)\r\n\r\n    const conversionFactor =\r\n      exposureTypeValue === moduleGeometryExposure.INTERNAL\r\n        ? 0.9\r\n        : exposureTypeValue === moduleGeometryExposure.EXTERNAL\r\n          ? 0.8\r\n          : 0.7\r\n    form.setValue('conversionFactor', conversionFactor)\r\n  }, [\r\n    form,\r\n    knowledgeMaterialLevel,\r\n    executionClassFormValue,\r\n    loadResistingCategoryFormValue,\r\n    enhancementCharacteristics,\r\n    exposureTypeValue,\r\n  ])\r\n\r\n  useEffect(() => {\r\n    const subscription = form.watch(values => {\r\n      setParams((prev: any) => ({\r\n        ...prev,\r\n        buildingCharacteristics: values,\r\n      }))\r\n    })\r\n    return () => subscription.unsubscribe()\r\n  }, [form, setParams])\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"material\"\r\n          options={MODULE_FACING_MATERIAL}\r\n          optionLabelFn={p => t(`material.${p}`)}\r\n          t={t}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"enhancementCharacteristics\"\r\n          options={MODULE_CRM_MASONRY_TYPE}\r\n          optionLabelFn={p => t(`enhancementCharacteristics.${p}`)}\r\n          t={t}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"knowledgeLevel\"\r\n          options={MODULE_MATERIAL_KNOWLEDGE_LEVEL}\r\n          optionLabelFn={p => t(`knowledgeLevel.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"confidenceFactor\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"executionClass\"\r\n          options={MODULE_EXECUTION_CLASS}\r\n          optionLabelFn={p => t(`executionClass.${p}`)}\r\n          t={t}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"loadResistantCategory\"\r\n          options={MODULE_LOAD_RESISTING_CATEGORY}\r\n          optionLabelFn={p => t(`loadResistantCategory.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"masonrySafetyFactor\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"characteristicCompressiveStrength\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"designCompressiveStrength\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"characteristicShearStrength\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"designShearStrength\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"normalElasticityModulus\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"shearElasticityModulus\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"masonryDensity\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"exposureType\"\r\n          options={MODULE_GEOMETRY_EXPOSURE}\r\n          optionLabelFn={p => t(`exposureType.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"conversionFactor\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAuBA;AACA;AAKA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AAYO,MAAM,uCAAuC;QAAC,EACnD,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACN,SAAS,EACT,MAAM,EACA;;IACN,MAAM,IAAI,IAAA,4NAAe,EACvB;IAEF,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,cAAc,IAAA,2MAAc;QAKpB,yBAER,2CAGA,+BACgB,iCACF,+BAEd,sCAEmB,oCAEnB,kDACyB,0CAEzB,4CACmB,oCACI,wCACD,uCACR,+BAEd,6BACgB;IA1BtB,MAAM,OAAO,IAAA,4KAAO,EAAwC;QAC1D,UAAU,IAAA,gLAAW,EAAC,yMAAsC;QAC5D,eAAe;YACb,UAAU,CAAA,0BAAA,0BAAA,oCAAA,cAAe,QAAQ,cAAvB,qCAAA,0BAA2B,iJAAe,CAAC,KAAK;YAC1D,4BACE,CAAA,4CAAA,0BAAA,oCAAA,cAAe,0BAA0B,cAAzC,uDAAA,4CACA,kJAAgB,CAAC,gCAAgC;YACnD,gBACE,CAAA,gCAAA,0BAAA,oCAAA,cAAe,cAAc,cAA7B,2CAAA,gCAAiC,8JAA4B,CAAC,GAAG;YACnE,kBAAkB,CAAA,kCAAA,0BAAA,oCAAA,cAAe,gBAAgB,cAA/B,6CAAA,kCAAmC;YACrD,gBAAgB,CAAA,gCAAA,0BAAA,oCAAA,cAAe,cAAc,cAA7B,2CAAA,gCAAiC,gJAAc,CAAC,GAAG;YACnE,uBACE,CAAA,uCAAA,0BAAA,oCAAA,cAAe,qBAAqB,cAApC,kDAAA,uCACA,uJAAqB,CAAC,oDAAoD;YAC5E,qBAAqB,CAAA,qCAAA,0BAAA,oCAAA,cAAe,mBAAmB,cAAlC,gDAAA,qCAAsC;YAC3D,mCACE,CAAA,mDAAA,0BAAA,oCAAA,cAAe,iCAAiC,cAAhD,8DAAA,mDAAoD;YACtD,2BAA2B,CAAA,2CAAA,0BAAA,oCAAA,cAAe,yBAAyB,cAAxC,sDAAA,2CAA4C;YACvE,6BACE,CAAA,6CAAA,0BAAA,oCAAA,cAAe,2BAA2B,cAA1C,wDAAA,6CAA8C;YAChD,qBAAqB,CAAA,qCAAA,0BAAA,oCAAA,cAAe,mBAAmB,cAAlC,gDAAA,qCAAsC;YAC3D,yBAAyB,CAAA,yCAAA,0BAAA,oCAAA,cAAe,uBAAuB,cAAtC,oDAAA,yCAA0C;YACnE,wBAAwB,CAAA,wCAAA,0BAAA,oCAAA,cAAe,sBAAsB,cAArC,mDAAA,wCAAyC;YACjE,gBAAgB,CAAA,gCAAA,0BAAA,oCAAA,cAAe,cAAc,cAA7B,2CAAA,gCAAiC;YACjD,cACE,CAAA,8BAAA,0BAAA,oCAAA,cAAe,YAAY,cAA3B,yCAAA,8BAA+B,wJAAsB,CAAC,QAAQ;YAChE,kBAAkB,CAAA,kCAAA,0BAAA,oCAAA,cAAe,gBAAgB,cAA/B,6CAAA,kCAAmC;QACvD;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,mNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,SAAS;gFAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;gBACtB,MAAM,YAAY,iBAAiB,CAAC;oBAClC,UAAU;wBAAC;wBAAW;4BAAE;4BAAW;wBAAS;qBAAE;gBAChD;gBACA;YACF;;QACA,OAAO;gFAAE,CAAA;gBACP,oJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAC7D;;IACF;IAGF,MAAM,mBAAmB,IAAA,4KAAW;8EAClC,CAAC;YACC,yCAAyC;YACzC,MAAM,mBAAgD;gBACpD,GAAG,MAAM;gBACT,wBAAwB;YAC1B;YACA,OAAO;gBAAE;gBAAW;gBAAU,MAAM;YAAiB;QACvD;6EACA;QAAC;QAAQ;QAAW;QAAU;KAAO;QAIrC;IADF,MAAM,yBACJ,CAAA,cAAA,KAAK,KAAK,CAAC,+BAAX,yBAAA,cAAgC,8JAA4B,CAAC,GAAG;QAEhE;IADF,MAAM,0BACJ,CAAA,eAAA,KAAK,KAAK,CAAC,+BAAX,0BAAA,eAAgC,gJAAc,CAAC,GAAG;QAElD;IADF,MAAM,iCACJ,CAAA,eAAA,KAAK,KAAK,CAAC,sCAAX,0BAAA,eACA,uJAAqB,CAAC,oDAAoD;QAE1E;IADF,MAAM,6BACJ,CAAA,eAAA,KAAK,KAAK,CAAC,2CAAX,0BAAA,eACA,kJAAgB,CAAC,gCAAgC;QAEjD;IADF,MAAM,oBACJ,CAAA,eAAA,KAAK,KAAK,CAAC,6BAAX,0BAAA,eAA8B,wJAAsB,CAAC,QAAQ;IAE/D,IAAA,0KAAS;0DAAC;YACR,MAAM;yFAAyB,CAAC;oBAI9B,OAAO,2BAA2B,8JAA4B,CAAC,GAAG,GAC9D,eAAe,GAAG,GAClB,2BAA2B,8JAA4B,CAAC,GAAG,GACzD,eAAe,GAAG,GAClB,CAAC,eAAe,GAAG,GAAG,eAAe,GAAG,IAAI;gBACpD;;YAEA,MAAM,mBACJ,oKAAkC,CAAC,uBAAuB;YAC5D,KAAK,QAAQ,CAAC,oBAAoB;YAElC,MAAM,8BACJ,oKAAkC,CAAC,+BAA+B,CAChE,wBACD;YACH,KAAK,QAAQ,CAAC,uBAAuB;YAErC,MAAM,0CACJ,yKAAuC,CAAC,2BAA2B;YAErE,MAAM,oCAAoC,uBACxC;YAEF,KAAK,QAAQ,CACX,qCACA;YAGF,MAAM,4BACJ,oCACA,8BACA;YACF,KAAK,QAAQ,CAAC,6BAA6B;YAE3C,MAAM,qCACJ,mKAAiC,CAAC,2BAA2B;YAC/D,MAAM,8BAA8B,uBAClC;YAEF,KAAK,QAAQ,CAAC,+BAA+B;YAE7C,MAAM,sBACJ,8BACA,mBACA;YACF,KAAK,QAAQ,CAAC,uBAAuB;YAErC,MAAM,gCACJ,6KAA2C,CAAC,2BAA2B;YACzE,MAAM,0BACJ,CAAC,8BAA8B,GAAG,GAAG,8BAA8B,GAAG,IACtE;YACF,KAAK,QAAQ,CAAC,2BAA2B;YAEzC,MAAM,+BACJ,4KAA0C,CAAC,2BAA2B;YACxE,MAAM,yBACJ,CAAC,6BAA6B,GAAG,GAAG,6BAA6B,GAAG,IAAI;YAC1E,KAAK,QAAQ,CAAC,0BAA0B;YAExC,MAAM,iBAAiB,sJAAoB,CAAC,2BAA2B;YACvE,KAAK,QAAQ,CAAC,kBAAkB;YAEhC,MAAM,mBACJ,sBAAsB,wJAAsB,CAAC,QAAQ,GACjD,MACA,sBAAsB,wJAAsB,CAAC,QAAQ,GACnD,MACA;YACR,KAAK,QAAQ,CAAC,oBAAoB;QACpC;yDAAG;QACD;QACA;QACA;QACA;QACA;QACA;KACD;IAED,IAAA,0KAAS;0DAAC;YACR,MAAM,eAAe,KAAK,KAAK;+EAAC,CAAA;oBAC9B;uFAAU,CAAC,OAAc,CAAC;gCACxB,GAAG,IAAI;gCACP,yBAAyB;4BAC3B,CAAC;;gBACH;;YACA;kEAAO,IAAM,aAAa,WAAW;;QACvC;yDAAG;QAAC;QAAM;KAAU;IAEpB,qBACE,6LAAC,2IAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,6LAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,wJAAsB;oBAC/B,eAAe,CAAA,IAAK,EAAE,AAAC,YAAa,OAAF;oBAClC,GAAG;;;;;;8BAEL,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,yJAAuB;oBAChC,eAAe,CAAA,IAAK,EAAE,AAAC,8BAA+B,OAAF;oBACpD,GAAG;;;;;;8BAEL,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,iKAA+B;oBACxC,eAAe,CAAA,IAAK,EAAE,AAAC,kBAAmB,OAAF;oBACxC,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,wJAAsB;oBAC/B,eAAe,CAAA,IAAK,EAAE,AAAC,kBAAmB,OAAF;oBACxC,GAAG;;;;;;8BAEL,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,gKAA8B;oBACvC,eAAe,CAAA,IAAK,EAAE,AAAC,yBAA0B,OAAF;oBAC/C,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,0JAAwB;oBACjC,eAAe,CAAA,IAAK,EAAE,AAAC,gBAAiB,OAAF;oBACtC,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,+IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,6LAAC,uOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB;GA7Sa;;QASD,4NAAe;QAGT,4NAAe;QACf,4NAAe;QACX,2MAAc;QAErB,4KAAO;QA8BU,mNAA2B;;;KA9C9C", "debugId": null}}, {"offset": {"line": 1814, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-stress-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type FrcmColumnParamsSchemaInput,\r\n  type FrcmColumnStressInput,\r\n  frcmColumnStressSchema,\r\n} from '@atlas/types/schemas/masonry/frcm-column-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FrcmColumnStressInput>\r\n  params: FrcmColumnParamsSchemaInput\r\n  setParams: (newParams: any) => void\r\n  onSave: () => void\r\n}\r\n\r\nexport const FrcmColumnStressForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  params,\r\n  setParams,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.frcm-column.stress')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const form = useForm<FrcmColumnStressInput>({\r\n    resolver: zodResolver(frcmColumnStressSchema),\r\n    defaultValues: {\r\n      normalStressCenteredStressing:\r\n        defaultValues?.normalStressCenteredStressing ?? 0,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: FrcmColumnStressInput) => {\r\n      // construct body based on global schema:\r\n      const frcmColumnParams: FrcmColumnParamsSchemaInput = {\r\n        ...params,\r\n        stress: body,\r\n      }\r\n      mutate({ projectId, moduleId, body: frcmColumnParams })\r\n    },\r\n    [mutate, projectId, moduleId, params],\r\n  )\r\n\r\n  useEffect(() => {\r\n    const subscription = form.watch(values => {\r\n      setParams((prev: any) => ({\r\n        ...prev,\r\n        buildingCharacteristics: values,\r\n      }))\r\n    })\r\n    return () => subscription.unsubscribe()\r\n  }, [form, setParams])\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"normalStressCenteredStressing\"\r\n          t={t}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAGA;AACA;AAKA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;AAYO,MAAM,uBAAuB;QAAC,EACnC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACN,SAAS,EACT,MAAM,EACA;;IACN,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,cAAc,IAAA,2MAAc;QAM5B;IAJN,MAAM,OAAO,IAAA,4KAAO,EAAwB;QAC1C,UAAU,IAAA,gLAAW,EAAC,yLAAsB;QAC5C,eAAe;YACb,+BACE,CAAA,+CAAA,0BAAA,oCAAA,cAAe,6BAA6B,cAA5C,0DAAA,+CAAgD;QACpD;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,mNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,SAAS;gEAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;gBACtB,MAAM,YAAY,iBAAiB,CAAC;oBAClC,UAAU;wBAAC;wBAAW;4BAAE;4BAAW;wBAAS;qBAAE;gBAChD;gBACA;YACF;;QACA,OAAO;gEAAE,CAAA;gBACP,oJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAC7D;;IACF;IAGF,MAAM,mBAAmB,IAAA,4KAAW;8DAClC,CAAC;YACC,yCAAyC;YACzC,MAAM,mBAAgD;gBACpD,GAAG,MAAM;gBACT,QAAQ;YACV;YACA,OAAO;gBAAE;gBAAW;gBAAU,MAAM;YAAiB;QACvD;6DACA;QAAC;QAAQ;QAAW;QAAU;KAAO;IAGvC,IAAA,0KAAS;0CAAC;YACR,MAAM,eAAe,KAAK,KAAK;+DAAC,CAAA;oBAC9B;uEAAU,CAAC,OAAc,CAAC;gCACxB,GAAG,IAAI;gCACP,yBAAyB;4BAC3B,CAAC;;gBACH;;YACA;kDAAO,IAAM,aAAa,WAAW;;QACvC;yCAAG;QAAC;QAAM;KAAU;IAEpB,qBACE,6LAAC,2IAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,6LAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,6LAAC,+IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,6LAAC,uOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB;GAnFa;;QASD,4NAAe;QACT,4NAAe;QACf,4NAAe;QACX,2MAAc;QAErB,4KAAO;QAQU,mNAA2B;;;KAtB9C", "debugId": null}}, {"offset": {"line": 1989, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/frcm-column-params-form.tsx"], "sourcesContent": ["import {\r\n  Accordion as AccordionComponent,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from '@atlas/components/ui/accordion'\r\nimport type {\r\n  Module,\r\n  ModuleWithParamsFrcmColumn,\r\n} from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { FrcmColumnParamsSchemaInput } from '@atlas/types/schemas/masonry/frcm-column-form'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useState } from 'react'\r\nimport { FrcmColumnConfinementReinforcementCalculation } from './calculations/frcm-column-confinement-reinforcement-calculation'\r\nimport { FrcmColumnGeometryForm } from './sections/frcm-column-geometry-form'\r\nimport { FrcmColumnMasonryCharacteristicsForm } from './sections/frcm-column-masonry-characteristics-form'\r\nimport { FrcmColumnStressForm } from './sections/frcm-column-stress-form'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  module: ModuleWithParamsFrcmColumn\r\n}\r\n\r\nexport const FrcmColumnParamsForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  module,\r\n}: Props) => {\r\n  const [params, setParams] = useState<FrcmColumnParamsSchemaInput>(\r\n    module?.params ?? ({} as FrcmColumnParamsSchemaInput),\r\n  )\r\n\r\n  const [openItems, setOpenItems] = useState(['0'])\r\n  const t = useTranslations('forms.project-params.frcm-column')\r\n\r\n  const handleItemSaved = useCallback((id: string) => {\r\n    const nextId = String(Number(id) + 1)\r\n    setOpenItems(old => {\r\n      const temp = old.filter(v => v !== id)\r\n      return old.includes(nextId) ? temp : [...temp, nextId]\r\n    })\r\n  }, [])\r\n\r\n  return (\r\n    <div>\r\n      <AccordionComponent\r\n        type=\"multiple\"\r\n        value={openItems}\r\n        onValueChange={setOpenItems}\r\n      >\r\n        <AccordionItem value=\"0\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">{t('geometry.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <FrcmColumnGeometryForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={params?.geometry}\r\n              setParams={setParams}\r\n              params={params}\r\n              onSave={() => handleItemSaved('0')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"1\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('masonry-characteristics.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <FrcmColumnMasonryCharacteristicsForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={params?.masonryCharacteristics}\r\n              setParams={setParams}\r\n              params={params}\r\n              onSave={() => handleItemSaved('1')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"2\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">{t('stress.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <FrcmColumnStressForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={params.stress}\r\n              params={params}\r\n              setParams={setParams}\r\n              onSave={() => handleItemSaved('2')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem\r\n          value=\"3\"\r\n          disabled={\r\n            !module?.params?.geometry ||\r\n            !module?.params?.masonryCharacteristics ||\r\n            !module?.params?.stress\r\n          }\r\n        >\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('confinement-reinforcement.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <FrcmColumnConfinementReinforcementCalculation\r\n              session={session}\r\n              projectId={projectId}\r\n              module={module}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n      </AccordionComponent>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAaA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AASO,MAAM,uBAAuB;QAAC,EACnC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,MAAM,EACA;QA4EK,gBACA,iBACA;;QA5ET;IADF,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAClC,CAAA,kBAAA,mBAAA,6BAAA,OAAQ,MAAM,cAAd,6BAAA,kBAAmB,CAAC;IAGtB,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAC;QAAC;KAAI;IAChD,MAAM,IAAI,IAAA,4NAAe,EAAC;IAE1B,MAAM,kBAAkB,IAAA,4KAAW;6DAAC,CAAC;YACnC,MAAM,SAAS,OAAO,OAAO,MAAM;YACnC;qEAAa,CAAA;oBACX,MAAM,OAAO,IAAI,MAAM;kFAAC,CAAA,IAAK,MAAM;;oBACnC,OAAO,IAAI,QAAQ,CAAC,UAAU,OAAO;2BAAI;wBAAM;qBAAO;gBACxD;;QACF;4DAAG,EAAE;IAEL,qBACE,6LAAC;kBACC,cAAA,6LAAC,qJAAkB;YACjB,MAAK;YACL,OAAO;YACP,eAAe;;8BAEf,6LAAC,yJAAa;oBAAC,OAAM;;sCACnB,6LAAC,4JAAgB;sCACf,cAAA,6LAAC;gCAAG,WAAU;0CAAuB,EAAE;;;;;;;;;;;sCAEzC,6LAAC,4JAAgB;sCACf,cAAA,6LAAC,wRAAsB;gCACrB,SAAS;gCACT,WAAW;gCACX,UAAU;gCACV,aAAa,EAAE,mBAAA,6BAAA,OAAQ,QAAQ;gCAC/B,WAAW;gCACX,QAAQ;gCACR,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,6LAAC,yJAAa;oBAAC,OAAM;;sCACnB,6LAAC,4JAAgB;sCACf,cAAA,6LAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;;;;;;sCAGP,6LAAC,4JAAgB;sCACf,cAAA,6LAAC,wTAAoC;gCACnC,SAAS;gCACT,WAAW;gCACX,UAAU;gCACV,aAAa,EAAE,mBAAA,6BAAA,OAAQ,sBAAsB;gCAC7C,WAAW;gCACX,QAAQ;gCACR,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,6LAAC,yJAAa;oBAAC,OAAM;;sCACnB,6LAAC,4JAAgB;sCACf,cAAA,6LAAC;gCAAG,WAAU;0CAAuB,EAAE;;;;;;;;;;;sCAEzC,6LAAC,4JAAgB;sCACf,cAAA,6LAAC,oRAAoB;gCACnB,SAAS;gCACT,WAAW;gCACX,UAAU;gCACV,eAAe,OAAO,MAAM;gCAC5B,QAAQ;gCACR,WAAW;gCACX,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,6LAAC,yJAAa;oBACZ,OAAM;oBACN,UACE,EAAC,mBAAA,8BAAA,iBAAA,OAAQ,MAAM,cAAd,qCAAA,eAAgB,QAAQ,KACzB,EAAC,mBAAA,8BAAA,kBAAA,OAAQ,MAAM,cAAd,sCAAA,gBAAgB,sBAAsB,KACvC,EAAC,mBAAA,8BAAA,kBAAA,OAAQ,MAAM,cAAd,sCAAA,gBAAgB,MAAM;;sCAGzB,6LAAC,4JAAgB;sCACf,cAAA,6LAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;;;;;;sCAGP,6LAAC,4JAAgB;sCACf,cAAA,6LAAC,8UAA6C;gCAC5C,SAAS;gCACT,WAAW;gCACX,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB;GAtGa;;QAWD,4NAAe;;;KAXd", "debugId": null}}]}