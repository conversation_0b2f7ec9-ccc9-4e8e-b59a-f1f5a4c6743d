export const PANEL_WIDTH_DEFAULT = 1000

export enum modulePolarity {
  POSITIVE = 'POSITIVE',
  NEGATIVE = 'NEGATIVE',
}

export const MODULE_POLARITY = Object.values(modulePolarity)

export type ModulePolarity =
  (typeof modulePolarity)[keyof typeof modulePolarity]

export const MODULE_TYPES = [
  'RECTANGULAR_BEAM',
  'PILLAR',
  'T_BEAM',
  'SLAB',
  'ANTI_OVERTURNING',
  'COLUMN',
  'MASONRY',
  'T_BEAM',
  'WOOD',
  'CRM',
  'FRCM_COLUMN',
  'MASCHI_MURARI',
] as const
export type ModuleType = (typeof MODULE_TYPES)[number]

export const MODULE_CATEGORY = [
  'REINFORCED_CONCRETE',
  'MASONRY',
  'WOOD',
  'FRCM_COLUMN',
  'MASCHI_MURARI',
] as const
export type ModuleCategory = (typeof MODULE_CATEGORY)[number]

export type ModuleCategoryType = {
  category: ModuleCategory
  types: { name: ModuleType; enabled: boolean }[]
  enabled: boolean
}

export const categorizedModuleTypes: ModuleCategoryType[] = [
  {
    category: 'REINFORCED_CONCRETE',
    types: [
      { name: 'RECTANGULAR_BEAM', enabled: true },
      { name: 'PILLAR', enabled: true },
      { name: 'T_BEAM', enabled: true },
      { name: 'SLAB', enabled: true },
    ],
    enabled: true,
  },
  {
    category: 'MASONRY',
    types: [
      { name: 'ANTI_OVERTURNING', enabled: true },
      { name: 'CRM', enabled: true },
      { name: 'FRCM_COLUMN', enabled: true },
      { name: 'MASCHI_MURARI', enabled: true },
    ],
    enabled: true,
  },
  {
    category: 'WOOD',
    types: [{ name: 'WOOD', enabled: true }],
    enabled: true,
  },
]

export const REINFORCEMENT_LAYOUTS = ['OPEN_STIRRUP', 'CLOSED_STIRRUP'] as const
export type ReinforcementLayout = (typeof REINFORCEMENT_LAYOUTS)[number]

export enum moduleGeometryExposure {
  INTERNAL = 'INTERNAL',
  EXTERNAL = 'EXTERNAL',
  AGGRESSIVE = 'AGGRESSIVE',
}

export const MODULE_GEOMETRY_EXPOSURE = Object.values(moduleGeometryExposure)

export enum moduleMaterialKnowledgeLevel {
  LC1 = 'LC1',
  LC2 = 'LC2',
  LC3 = 'LC3',
}

export type ModuleMaterialKnowledgeLevel =
  (typeof moduleMaterialKnowledgeLevel)[keyof typeof moduleMaterialKnowledgeLevel]

export const moduleMaterialKnowledgeLevelValues: Record<
  ModuleMaterialKnowledgeLevel,
  number
> = {
  LC1: 1.35,
  LC2: 1.2,
  LC3: 1,
}

export const MODULE_MATERIAL_KNOWLEDGE_LEVEL = Object.values(
  moduleMaterialKnowledgeLevel,
)

export enum moduleMaterialClass {
  DUCTILE = 'DUCTILE',
  BRITTLE = 'BRITTLE',
}

export type ModuleMaterialClass =
  (typeof moduleMaterialClass)[keyof typeof moduleMaterialClass]

export const MODULE_MATERIAL_CLASS = Object.values(moduleMaterialClass)

export enum SUBSOIL_CATEGORY {
  A = 'A',
  B = 'B',
  C = 'C',
  D = 'D',
  E = 'E',
}

// coefficient Ss
export const ssCoefficientValues: Record<SUBSOIL_CATEGORY, number> = {
  A: 1,
  B: 1.2,
  C: 1.41,
  D: 1.68,
  E: 1.47,
}

export const MODULE_SUBSOIL_CATEGORY = Object.values(SUBSOIL_CATEGORY)

export enum TOPOGRAPHIC_CATEGORY {
  T1 = 'T1',
  T2 = 'T2',
  T3 = 'T3',
  T4 = 'T4',
}

// coefficient St
export const topographicCoefficientValues: Record<
  TOPOGRAPHIC_CATEGORY,
  number
> = {
  T1: 1,
  T2: 1.2,
  T3: 1.2,
  T4: 1.4,
}
export const MODULE_TOPOGRAPHIC_CATEGORY = Object.values(TOPOGRAPHIC_CATEGORY)

export enum BUILDING_TYPE {
  MASONRY = 'MASONRY',
  REINFORCED_CONCRETE = 'REINFORCED_CONCRETE',
}

export const MODULE_BUILDING_TYPE = Object.values(BUILDING_TYPE)

export enum INFILL_WALL_TOPOLOGY {
  SINGLE = 'SINGLE',
  DOUBLE = 'DOUBLE',
}

export const MODULE_INFILL_WALL_TOPOLOGY = Object.values(INFILL_WALL_TOPOLOGY)

export enum FACING_MATERIAL {
  BRICK = 'BRICK',
  TUFF = 'TUFF',
  STONE = 'STONE',
}

export const MODULE_FACING_MATERIAL = Object.values(FACING_MATERIAL)

export enum executionClass {
  ONE = 'ONE',
  TWO = 'TWO',
}

export const MODULE_EXECUTION_CLASS = Object.values(executionClass)

export enum loadResistingCategory {
  MASONRY_CAT_I_AND_MORTAR_WITH_GUARANTEED_PERFORMANCE = 'MASONRY_CAT_I_AND_MORTAR_WITH_GUARANTEED_PERFORMANCE',
  MASONRY_CAT_I_AND_MORTAR_WITH_PRESCRIBED_PERFORMANCE = 'MASONRY_CAT_I_AND_MORTAR_WITH_PRESCRIBED_PERFORMANCE',
  MASONRY_CAT_II_AND_ANY_TYPE_OF_MORTAR = 'MASONRY_CAT_II_AND_ANY_TYPE_OF_MORTAR',
}

export const MODULE_LOAD_RESISTING_CATEGORY = Object.values(
  loadResistingCategory,
)

export const masonryStrengthSafetyFactorMapping: Record<
  string,
  Record<string, number>
> = {
  [loadResistingCategory.MASONRY_CAT_I_AND_MORTAR_WITH_GUARANTEED_PERFORMANCE]:
    {
      [executionClass.ONE]: 2,
      [executionClass.TWO]: 2.5,
    },
  [loadResistingCategory.MASONRY_CAT_I_AND_MORTAR_WITH_PRESCRIBED_PERFORMANCE]:
    {
      [executionClass.ONE]: 2.2,
      [executionClass.TWO]: 2.7,
    },
  [loadResistingCategory.MASONRY_CAT_II_AND_ANY_TYPE_OF_MORTAR]: {
    [executionClass.ONE]: 2.5,
    [executionClass.TWO]: 3,
  },
}

export enum CRM_MASONRY_TYPE {
  MURATURA_IN_PIETRAME_DISORDINATA = 'MURATURA_IN_PIETRAME_DISORDINATA',
  MURATURA_A_CONCI_SBOZZATI = 'MURATURA_A_CONCI_SBOZZATI',
  MURATURA_IN_PIETRA_A_SPACCO = 'MURATURA_IN_PIETRA_A_SPACCO',
  MURATURA_IRREGOLARE_DI_PIETRA_TENERA = 'MURATURA_IRREGOLARE_DI_PIETRA_TENERA',
  MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA = 'MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA',
  MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI = 'MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI',
  MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE = 'MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE',
  MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA = 'MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA',
}

export const MODULE_CRM_MASONRY_TYPE = Object.values(CRM_MASONRY_TYPE)

export enum ENHANCEMENT_CHARACTERISTICS {
  MALTA_BUONA = 'MALTA_BUONA',
  NON_PRESENTI = 'NON_PRESENTI',
}

export const MODULE_ENHANCEMENT_CHARACTERISTICS = Object.values(
  ENHANCEMENT_CHARACTERISTICS,
)

// corrective Coefficient, depend on enhancement characteristics + masonry type
// Coefficienti correttivi	Malta buona	Ricorsi o listature	Connessione trasversale	 - non presenti -
//
// Muratura in pietrame disordinata	1.5	1.3	1.5	1
// Muratura a conci sbozzati con paramenti di spessore disomodeneo	1.4	1.2	1.5	1
// Muratura in pietra a spacco con buona tessitura	1.3	1.1	1.3	1
// Muratura irregolare di pietra tenera	1.5	1.2	1.3	1
// Muratura a conci regolari di pietra tenera	1.6	0	1.2	1
// Muratura a blocchi lapidei squadrati	1.2	0	1.2	1
// Muratura in mattoni pieni e malta di calce	0	0	1.3	1
// Muratura in mattoni semipieni con malta cementizia	1.2	0	0	1 0
export const correctiveCoefficientValues: Record<
  ENHANCEMENT_CHARACTERISTICS,
  Record<CRM_MASONRY_TYPE, number>
> = {
  [ENHANCEMENT_CHARACTERISTICS.MALTA_BUONA]: {
    [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: 1.5,
    [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: 1.4,
    [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: 1.3,
    [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: 1.5,
    [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: 1.6,
    [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: 1.2,
    [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: 0,
    [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: 1.2,
  },
  [ENHANCEMENT_CHARACTERISTICS.NON_PRESENTI]: {
    [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: 1,
    [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: 1,
    [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: 1,
    [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: 1,
    [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: 1,
    [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: 1,
    [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: 1,
    [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: 1,
  },
}

// averageCompressiveStrength values in MPa, depend on masonry type + material knowledge level:
//
// 	Tipologia muratura	fm [MPa]                                    LC1	LC2	LC3
// Muratura in pietrame disordinata	                                1.00	1.50	2.00
// Muratura a conci sbozzati con paramenti di spessore disomodeneo	2.00	2.00	2.00
// Muratura in pietra a spacco con buona tessitura	                2.60	3.20	3.80
// Muratura irregolare di pietra tenera	                            1.40	1.80	2.20
// Muratura a conci regolari di pietra tenera	                      2.00	2.60	3.20
// Muratura a blocchi lapidei squadrati	                            5.80	7.00	8.20
// Muratura in mattoni pieni e malta di calce	                      2.60	3.45	4.30
// Muratura in mattoni semipieni con malta cementizia	              5.00	6.50	8.00
export const averageCompressiveStrengthValues: Record<
  CRM_MASONRY_TYPE,
  Record<ModuleMaterialKnowledgeLevel, number>
> = {
  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: {
    LC1: 1.0,
    LC2: 1.5,
    LC3: 2.0,
  },
  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: {
    LC1: 2.0,
    LC2: 2.0,
    LC3: 2.0,
  },
  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: {
    LC1: 2.6,
    LC2: 3.2,
    LC3: 3.8,
  },
  [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: {
    LC1: 1.4,
    LC2: 1.8,
    LC3: 2.2,
  },
  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: {
    LC1: 2.0,
    LC2: 2.6,
    LC3: 3.2,
  },
  [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: {
    LC1: 5.8,
    LC2: 7.0,
    LC3: 8.2,
  },
  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: {
    LC1: 2.6,
    LC2: 3.45,
    LC3: 4.3,
  },
  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: {
    LC1: 5.0,
    LC2: 6.5,
    LC3: 8.0,
  },
}

// averageShearStrengthRegularMasonry values in MPa, depend on masonry type + material knowledge level:
// Tipologia muratura	τ0 [MPa]		LC1	LC2	LC3
// Muratura in pietrame disordinata	0.018	0.025	0.032
// Muratura a conci sbozzati con paramenti di spessore disomodeneo	0.035	0.043	0.051
// Muratura in pietra a spacco con buona tessitura	0.056	0.065	0.074
// Muratura irregolare di pietra tenera	0.028	0.035	0.042
// Muratura a conci regolari di pietra tenera	0.040	0.060	0.080
// Muratura a blocchi lapidei squadrati	0.090	0.105	0.120
// Muratura in mattoni pieni e malta di calce	0.050	0.090	0.130
// Muratura in mattoni semipieni con malta cementizia	0.080	0.125	0.170
export const averageShearStrengthRegularMasonryValues: Record<
  CRM_MASONRY_TYPE,
  Record<ModuleMaterialKnowledgeLevel, number>
> = {
  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: {
    LC1: 0.018,
    LC2: 0.025,
    LC3: 0.032,
  },
  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: {
    LC1: 0.035,
    LC2: 0.043,
    LC3: 0.051,
  },
  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: {
    LC1: 0.056,
    LC2: 0.065,
    LC3: 0.074,
  },
  [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: {
    LC1: 0.028,
    LC2: 0.035,
    LC3: 0.042,
  },
  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: {
    LC1: 0.04,
    LC2: 0.06,
    LC3: 0.08,
  },
  [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: {
    LC1: 0.09,
    LC2: 0.105,
    LC3: 0.12,
  },
  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: {
    LC1: 0.05,
    LC2: 0.09,
    LC3: 0.13,
  },
  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: {
    LC1: 0.08,
    LC2: 0.125,
    LC3: 0.17,
  },
}

// averageShearStrengthIrregularMasonry values in MPa, depend on masonry type + material knowledge level:
//Tipologia muratura	fv0 [MPa]
// 	LC1	LC2	LC3
// Muratura in pietrame disordinata	0.000	0.000	0.000
// Muratura a conci sbozzati con paramenti di spessore disomodeneo	0.000	0.000	0.000
// Muratura in pietra a spacco con buona tessitura	0.000	0.000	0.000
// Muratura irregolare di pietra tenera	0.000	0.000	0.000
// Muratura a conci regolari di pietra tenera	0.100	0.145	0.190
// Muratura a blocchi lapidei squadrati	0.180	0.230	0.280
// Muratura in mattoni pieni e malta di calce	0.130	0.200	0.270
// Muratura in mattoni semipieni con malta cementizia	0.200	0.280	0.360
export const averageShearStrengthIrregularMasonryValues: Record<
  CRM_MASONRY_TYPE,
  Record<ModuleMaterialKnowledgeLevel, number>
> = {
  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: {
    LC1: 0.0,
    LC2: 0.0,
    LC3: 0.0,
  },
  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: {
    LC1: 0.0,
    LC2: 0.0,
    LC3: 0.0,
  },
  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: {
    LC1: 0.0,
    LC2: 0.0,
    LC3: 0.0,
  },
  [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: {
    LC1: 0.0,
    LC2: 0.0,
    LC3: 0.0,
  },
  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: {
    LC1: 0.1,
    LC2: 0.145,
    LC3: 0.19,
  },
  [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: {
    LC1: 0.18,
    LC2: 0.23,
    LC3: 0.28,
  },
  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: {
    LC1: 0.13,
    LC2: 0.2,
    LC3: 0.27,
  },
  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: {
    LC1: 0.2,
    LC2: 0.28,
    LC3: 0.36,
  },
}

// averageNormalElasticModulus values in MPa, depend on masonry type + material knowledge level:
// Tipologia muratura	E [MPa]
// 	LC1	LC2	LC3
// Muratura in pietrame disordinata	870	870	870
// Muratura a conci sbozzati con paramenti di spessore disomodeneo	1230	1230	1230
// Muratura in pietra a spacco con buona tessitura	1740	1740	1740
// Muratura irregolare di pietra tenera	1080	1080	1080
// Muratura a conci regolari di pietra tenera	1410	1410	1410
// Muratura a blocchi lapidei squadrati	2850	2850	2850
// Muratura in mattoni pieni e malta di calce	1500	1500	1500
// Muratura in mattoni semipieni con malta cementizia	4550	4550	4550
export const averageNormalElasticityModulusValues: Record<
  CRM_MASONRY_TYPE,
  Record<ModuleMaterialKnowledgeLevel, number>
> = {
  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: {
    LC1: 870,
    LC2: 870,
    LC3: 870,
  },
  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: {
    LC1: 1230,
    LC2: 1230,
    LC3: 1230,
  },
  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: {
    LC1: 1740,
    LC2: 1740,
    LC3: 1740,
  },
  [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: {
    LC1: 1080,
    LC2: 1080,
    LC3: 1080,
  },
  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: {
    LC1: 1410,
    LC2: 1410,
    LC3: 1410,
  },
  [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: {
    LC1: 2850,
    LC2: 2850,
    LC3: 2850,
  },
  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: {
    LC1: 1500,
    LC2: 1500,
    LC3: 1500,
  },
  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: {
    LC1: 4550,
    LC2: 4550,
    LC3: 4550,
  },
}

// averageShearElasticityModulus values in MPa, depend on masonry type + material knowledge level:
// Tipologia muratura	G [MPa]
// 	LC1	LC2	LC3
// Muratura in pietrame disordinata	290	290	290
// Muratura a conci sbozzati con paramenti di spessore disomodeneo	410	410	410
// Muratura in pietra a spacco con buona tessitura	580	580	580
// Muratura irregolare di pietra tenera	360	360	360
// Muratura a conci regolari di pietra tenera	450	450	450
// Muratura a blocchi lapidei squadrati	950	950	950
// Muratura in mattoni pieni e malta di calce	500	500	500
// Muratura in mattoni semipieni con malta cementizia	1137.5	1137.5	1137.5
export const averageShearElasticityModulusValues: Record<
  CRM_MASONRY_TYPE,
  Record<ModuleMaterialKnowledgeLevel, number>
> = {
  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: {
    LC1: 290,
    LC2: 290,
    LC3: 290,
  },
  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: {
    LC1: 410,
    LC2: 410,
    LC3: 410,
  },
  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: {
    LC1: 580,
    LC2: 580,
    LC3: 580,
  },
  [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: {
    LC1: 360,
    LC2: 360,
    LC3: 360,
  },
  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: {
    LC1: 450,
    LC2: 450,
    LC3: 450,
  },
  [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: {
    LC1: 950,
    LC2: 950,
    LC3: 950,
  },
  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: {
    LC1: 500,
    LC2: 500,
    LC3: 500,
  },
  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: {
    LC1: 1137.5,
    LC2: 1137.5,
    LC3: 1137.5,
  },
}

// postInterventionFirstSideReinforcedPlasterCoefficient values, depend on masonry type:
export const postInterventionFirstSideReinforcedPlasterCoefficientValues: Record<
  CRM_MASONRY_TYPE,
  number
> = {
  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: 2.5,
  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: 2,
  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: 1.5,
  [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: 1.7,
  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: 1.5,
  [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: 1.2,
  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: 1.5,
  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: 1.3,
}

// bindingCoefficient values, depend on masonry type:
export const bindingCoefficientValues: Record<CRM_MASONRY_TYPE, number> = {
  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: 2,
  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: 1.7,
  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: 1.5,
  [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: 1.4,
  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: 1.2,
  [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: 1.2,
  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: 1.2,
  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: 0,
}

// maxAmplficationCoefficient values, depend on masonry type:
export const maxAmplficationCoefficientValues: Record<
  CRM_MASONRY_TYPE,
  number
> = {
  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: 3.5,
  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: 3,
  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: 2.4,
  [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: 2,
  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: 1.8,
  [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: 1.4,
  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: 1.8,
  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: 1.3,
}

// specificWeight values in kN/m3, depend on masonry type:
// Tipologia muratura	γ [kN/m3]
// Muratura in pietrame disordinata	19
// Muratura a conci sbozzati con paramenti di spessore disomodeneo	20
// Muratura in pietra a spacco con buona tessitura	21
// Muratura irregolare di pietra tenera	14.5
// Muratura a conci regolari di pietra tenera	14.5
// Muratura a blocchi lapidei squadrati	22
// Muratura in mattoni pieni e malta di calce	18
// Muratura in mattoni semipieni con malta cementizia	15
export const specificWeightValues: Record<CRM_MASONRY_TYPE, number> = {
  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: 19,
  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: 20,
  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: 21,
  [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: 14.5,
  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: 14.5,
  [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: 22,
  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: 18,
  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: 15,
}

export enum REINFORCEMENT_APPLICATION_TYPE {
  APPLICAZIONE_SU_ENTRAMBI_I_LATI = 'APPLICAZIONE_SU_ENTRAMBI_I_LATI',
  APPLICAZIONE_SU_UN_SOLO_LATO = 'APPLICAZIONE_SU_UN_SOLO_LATO',
}

export const MODULE_REINFORCEMENT_APPLICATION_TYPE = Object.values(
  REINFORCEMENT_APPLICATION_TYPE,
)

export enum BINDER_MIXTURE_INJECTIONS {
  YES = 'YES',
  NO = 'NO',
}

export const MODULE_BINDER_MIXTURE_INJECTIONS = Object.values(
  BINDER_MIXTURE_INJECTIONS,
)

export enum FRM_GEOMETRY_TOPOLOGY {
  RECTANGULAR = 'RECTANGULAR',
  CIRCULAR = 'CIRCULAR',
}

export const MODULE_FRM_GEOMETRY_TOPOLOGY = Object.values(FRM_GEOMETRY_TOPOLOGY)

// characteristicCompressiveStrength values in MPa, depend on masonry type + custom (for user defined):
export const characteristicCompressiveStrengthValues: Record<
  CRM_MASONRY_TYPE | 'CUSTOM',
  { min: number; max: number }
> = {
  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: { min: 1, max: 2 },
  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: { min: 2, max: 2 },
  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: { min: 2.6, max: 3.8 },
  [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: {
    min: 1.4,
    max: 2.2,
  },
  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: {
    min: 2,
    max: 3.2,
  },
  [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: {
    min: 5.8,
    max: 8.2,
  },
  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: {
    min: 2.6,
    max: 4.3,
  },
  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: {
    min: 5,
    max: 8,
  },
  CUSTOM: { min: 5, max: 5 },
}

// characteristicShearStrength values in MPa, depend on masonry type + custom (for user defined):
export const characteristicShearStrengthValues: Record<
  CRM_MASONRY_TYPE | 'CUSTOM',
  { min: number; max: number }
> = {
  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: {
    min: 0.018,
    max: 0.032,
  },
  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: { min: 0.035, max: 0.051 },
  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: { min: 0.056, max: 0.074 },
  [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: {
    min: 0.028,
    max: 0.042,
  },
  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: {
    min: 0.04,
    max: 0.06,
  },
  [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: {
    min: 0.09,
    max: 0.12,
  },
  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: {
    min: 0.05,
    max: 0.13,
  },
  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: {
    min: 0.08,
    max: 0.17,
  },
  CUSTOM: { min: 0.05, max: 0.05 },
}

// characteristicNormalElasticModulus values in MPa, depend on masonry type + custom (for user defined):
// Em [MPa]
export const characteristicNormalElasticityModulusValues: Record<
  CRM_MASONRY_TYPE | 'CUSTOM',
  { min: number; max: number }
> = {
  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: { min: 690, max: 1050 },
  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: { min: 1020, max: 1440 },
  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: { min: 1500, max: 1980 },
  [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: {
    min: 900,
    max: 1260,
  },
  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: {
    min: 1200,
    max: 1620,
  },
  [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: {
    min: 2400,
    max: 3300,
  },
  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: {
    min: 1200,
    max: 1800,
  },
  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: {
    min: 3500,
    max: 5600,
  },
  CUSTOM: { min: 4550, max: 4550 },
}

// characteristicShearElasticModulus values in MPa, depend on masonry type + custom (for user defined):
// Gm [MPa]
export const characteristicShearElasticityModulusValues: Record<
  CRM_MASONRY_TYPE | 'CUSTOM',
  { min: number; max: number }
> = {
  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: { min: 230, max: 350 },
  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: { min: 340, max: 480 },
  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: { min: 500, max: 600 },
  [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: {
    min: 300,
    max: 420,
  },
  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: {
    min: 400,
    max: 500,
  },
  [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: {
    min: 800,
    max: 1100,
  },
  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: {
    min: 400,
    max: 600,
  },
  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: {
    min: 875,
    max: 1400,
  },
  CUSTOM: { min: 1137.5, max: 1137.5 },
}

// masonry density:
// w [kN/m3]
export const masonryDensityValues: Record<CRM_MASONRY_TYPE | 'CUSTOM', number> =
  {
    [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: 19,
    [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: 20,
    [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: 21,
    [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: 16,
    [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: 16,
    [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: 22,
    [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: 18,
    [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: 15,
    CUSTOM: 18,
  }

export enum REINFORCEMENT_ARRANGEMENT {
  CONTINUE = 'CONTINUE',
  DISCONTINUE = 'DISCONTINUE',
}

export const MODULE_REINFORCEMENT_ARRANGEMENT = Object.values(
  REINFORCEMENT_ARRANGEMENT,
)

export enum REINFORCEMENT_FAILURE_MODE {
  DISTACCO_INTERMEDIO = 'DISTACCO_INTERMEDIO',
  DISTACCO_DI_ESTREMITA = 'DISTACCO_DI_ESTREMITA',
}

export const MODULE_REINFORCEMENT_FAILURE_MODE = Object.values(
  REINFORCEMENT_FAILURE_MODE,
)
