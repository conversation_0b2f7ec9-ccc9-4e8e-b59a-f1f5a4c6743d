import { CustomProductSection } from '@atlas/components/common/atlas/product/custom-product-section'
import { ProductSummaryCard } from '@atlas/components/common/atlas/product/product-summary-card'
import { NumberFormInput } from '@atlas/components/common/form/number-form-input'
import { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'
import { SelectFormInput } from '@atlas/components/common/form/select-form-input'
import { Button } from '@atlas/components/ui/button'
import { Form } from '@atlas/components/ui/form'
import { Separator } from '@atlas/components/ui/separator'
import {
  FACING_MATERIAL,
  MODULE_REINFORCEMENT_ARRANGEMENT,
  MODULE_REINFORCEMENT_FAILURE_MODE,
  REINFORCEMENT_ARRANGEMENT,
  REINFORCEMENT_FAILURE_MODE,
} from '@atlas/constants/module'
import type { ModuleWithParamsMaschiMurari } from '@atlas/lib/api/modules/schemas/modules'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import { useModuleCalculation } from '@atlas/lib/mutation/modules/use-module-calculation'
import { useProductsByCategory } from '@atlas/lib/query/products/use-products-by-category'
import {
  type MaschiMurariShearReinforcementExecutionSchemaInput,
  maschiMurariShearReinforcementExecutionSchema,
} from '@atlas/types/schemas/masonry/maschi-murari-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Loader2Icon } from 'lucide-react'
import Image from 'next/image'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useEffect, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { MaschiMurariReinforcementShearCalculationResult } from './maschi-murari-reinforcement-shear-calculation-result'

type Props = {
  module: ModuleWithParamsMaschiMurari
  session: Session
  projectId: Project['id']
  onNext: () => void
}

// N.B: this function is temporary, until we remove the quotes in future seed
function getCleanedUpValue(
  recordToClean: Record<string, number> | undefined,
  nature: string,
) {
  // nature should be 'BRICK', 'TUFF', or 'STONE'
  if (!recordToClean) {
    return undefined
  }
  const key = `"${nature}"` // matches the API key format
  return recordToClean[key]
}

export const MaschiMurariShearCalculation = ({
  session,
  module,
  projectId,
  onNext,
}: Props) => {
  const t = useTranslations('forms.calculations.maschi-murari.shear')
  const tAction = useTranslations('actions.calculations.maschi-murari')
  const tCommon = useTranslations('actions.common')

  const {
    params,
    shearReinforcementVerifyExecutionInput,
    shearReinforcementCalculationResult,
  } = module

  const form = useForm<MaschiMurariShearReinforcementExecutionSchemaInput>({
    resolver: zodResolver(maschiMurariShearReinforcementExecutionSchema),
    defaultValues: {
      calculationType: 'SHEAR_VERIFY',
      input: {
        product: {
          id: shearReinforcementVerifyExecutionInput?.product.id,
          name: shearReinforcementVerifyExecutionInput?.product.name,
          sourceType:
            shearReinforcementVerifyExecutionInput?.product.id === 'custom'
              ? 'CUSTOM'
              : 'DATABASE',
        },
        reinforcementFailureMode:
          shearReinforcementVerifyExecutionInput?.reinforcementFailureMode ??
          REINFORCEMENT_FAILURE_MODE.DISTACCO_DI_ESTREMITA,
        designReinforcementStress:
          shearReinforcementVerifyExecutionInput?.designReinforcementStress ??
          0,
        designReinforcementStrain:
          shearReinforcementVerifyExecutionInput?.designReinforcementStrain ??
          0,
        reinforcedArrangement:
          shearReinforcementVerifyExecutionInput?.reinforcedArrangement ??
          REINFORCEMENT_ARRANGEMENT.CONTINUE,
        singleStripWidth:
          shearReinforcementVerifyExecutionInput?.singleStripWidth ?? 0,
        stripSpacing: shearReinforcementVerifyExecutionInput?.stripSpacing ?? 0,
        totalReinforcementWidthPerpendicularShearDirection:
          shearReinforcementVerifyExecutionInput?.totalReinforcementWidthPerpendicularShearDirection ??
          0,
        compressedEdgeReinforcementFiberDistance:
          shearReinforcementVerifyExecutionInput?.compressedEdgeReinforcementFiberDistance ??
          0,
        layersNumber: shearReinforcementVerifyExecutionInput?.layersNumber ?? 1,
        reinforcedSidesNumber:
          shearReinforcementVerifyExecutionInput?.reinforcedSidesNumber ?? 0,
      },
    },
  })

  const { mutate, isPending } = useModuleCalculation(session.accessToken, {
    onSuccess: () => {
      toast.success(tAction('calculate.success'))
    },
    onError: error => {
      console.log('ERROR  ', error)
      toast.error(tAction('calculate.failure', { error: error.message }))
    },
  })

  const handleFormSubmit = (
    body: MaschiMurariShearReinforcementExecutionSchemaInput,
  ) => {
    mutate({ projectId, moduleId: module.id, body })
  }

  const {
    data: products,
    isError: errorGettingProducts,
    isLoading: isLoadingProducts,
  } = useProductsByCategory(session, 'MASCHI_MURARI', 0, 100)

  const productsOptions = [
    ...(products?.content.map(m => ({
      value: m.id,
      label: m.name ?? t('product.unnamed'),
    })) ?? []),
  ]

  const [productId] = form.watch(['input.product.id'])

  const selectedProduct = useMemo(
    () => products?.content.find(p => p.id === productId),
    [productId, products],
  )

  const conventionalStrainLimit = selectedProduct?.conventionalStrainLimit
  const conversionFactor = params?.materialProperties?.conversionFactor ?? 0
  const reinforcementFailureModeValue = form.watch(
    'input.reinforcementFailureMode',
  )

  const structuralElementsNatureValue =
    params?.materialProperties?.structuralElementsNature ??
    FACING_MATERIAL.BRICK

  const panelGeometryHeight = params?.panelGeometry?.height ?? 0
  const panelGeometryWidth = params?.panelGeometry?.width ?? 0
  const singleStripWidthValue = form.watch('input.singleStripWidth') ?? 0
  const stripSpacingValue = form.watch('input.stripSpacing') ?? 0

  const reinforcedArrangementValue = form.watch('input.reinforcedArrangement')

  useEffect(() => {
    // D43 = designReinforcementStrain =
    // =IF(D42="Distacco intermedio",(1.5*D41*D26/1.5),(D41*D26/1.5))
    // D42 = input.reinforcementFailureMode
    // D41 = product.conventionalStrainLimit
    // D26 = materialProperties.conversionFactor
    const conventionalStrainLimitValue =
      getCleanedUpValue(
        conventionalStrainLimit,
        structuralElementsNatureValue,
      ) ?? 0

    const designReinforcementStrain =
      reinforcementFailureModeValue ===
      REINFORCEMENT_FAILURE_MODE.DISTACCO_INTERMEDIO
        ? (1.5 * conventionalStrainLimitValue * conversionFactor) / 1.5
        : (conventionalStrainLimitValue * conversionFactor) / 1.5

    form.setValue('input.designReinforcementStrain', designReinforcementStrain)

    // D62 = designReinforcementStress = =D43*D38
    // D43 = designReinforcementStrain
    // D38 = product.elasticModulus
    const elasticModulus = selectedProduct?.elasticModulus ?? 0
    const designReinforcementStress = designReinforcementStrain * elasticModulus
    form.setValue('input.designReinforcementStress', designReinforcementStress)

    // D48 totalReinforcementWidthPerpendicularShearDirection =
    //  =IF(D45="Continuo",MIN(D8,D9),MIN(D46*(D8/D47),D8,D9))
    // D45 = input.reinforcedArrangement
    // D8 = panelGeometry.height
    // D9 = panelGeometry.width
    // D46 = input.singleStripWidth
    // D47 = input.stripSpacing

    const totalReinforcementWidthPerpendicularShearDirection =
      reinforcedArrangementValue === REINFORCEMENT_ARRANGEMENT.CONTINUE
        ? Math.min(panelGeometryHeight, panelGeometryWidth)
        : Math.min(
            singleStripWidthValue * (panelGeometryHeight / stripSpacingValue),
            panelGeometryHeight,
            panelGeometryWidth,
          )
    form.setValue(
      'input.totalReinforcementWidthPerpendicularShearDirection',
      totalReinforcementWidthPerpendicularShearDirection,
    )

    form.setValue('input.layersNumber', 1)
    if (!productId) {
      return
    }
    if (productId === 'custom') {
      form.setValue('input.product.sourceType', 'CUSTOM')
    }

    if (selectedProduct) {
      form.setValue('input.product', {
        ...selectedProduct,
        sourceType: 'DATABASE',
      })
    }
  }, [
    form,
    productId,
    selectedProduct,
    conversionFactor,
    reinforcementFailureModeValue,
    conventionalStrainLimit,
    structuralElementsNatureValue,
    panelGeometryHeight,
    panelGeometryWidth,
    singleStripWidthValue,
    stripSpacingValue,
    reinforcedArrangementValue,
  ])

  return (
    <div className="flex flex-col justify-center gap-4">
      <Form {...form}>
        <form
          className="space-y-4 rounded-md border p-4"
          onSubmit={form.handleSubmit(handleFormSubmit)}
        >
          <h1 className="text-3xl font-bold">{t('heading')}</h1>
          <Image
            src="/assets/masonry_frcm/maschi-murari/FRCM_PANNELLI MURARI_Diffuso.jpg"
            alt="shear verify"
            height={250}
            width={500}
            className="mx-auto rounded-md object-contain"
            priority
          />
          <h1 className="text-xl font-bold">{t('sub-heading')}</h1>
          <SelectFormInput
            control={form.control}
            name="input.product.id"
            options={productsOptions}
            t={t}
            loading={isLoadingProducts}
            requestError={errorGettingProducts}
            errorMessage={t('products.error')}
          />
          {productId === 'custom' && <CustomProductSection />}
          {selectedProduct && <ProductSummaryCard product={selectedProduct} />}
          <Separator />
          <SelectFormFixedInput
            control={form.control}
            name="input.reinforcementFailureMode"
            options={MODULE_REINFORCEMENT_FAILURE_MODE}
            optionLabelFn={p => t(`input.reinforcementFailureMode.${p}`)}
            t={t}
          />
          <NumberFormInput
            control={form.control}
            name="input.designReinforcementStrain"
            t={t}
            disabled={true}
          />
          <NumberFormInput
            control={form.control}
            name="input.designReinforcementStress"
            t={t}
            disabled={true}
          />
          <SelectFormFixedInput
            control={form.control}
            name="input.reinforcedArrangement"
            options={MODULE_REINFORCEMENT_ARRANGEMENT}
            optionLabelFn={p => t(`input.reinforcedArrangement.${p}`)}
            t={t}
          />
          <NumberFormInput
            control={form.control}
            name="input.singleStripWidth"
            t={t}
          />
          <NumberFormInput
            control={form.control}
            name="input.stripSpacing"
            t={t}
          />
          <NumberFormInput
            control={form.control}
            name="input.totalReinforcementWidthPerpendicularShearDirection"
            t={t}
            disabled={true}
          />
          <NumberFormInput
            control={form.control}
            name="input.layersNumber"
            t={t}
            disabled={true}
          />
          <NumberFormInput
            control={form.control}
            name="input.reinforcedSidesNumber"
            t={t}
          />
          <NumberFormInput
            control={form.control}
            name="input.compressedEdgeReinforcementFiberDistance"
            t={t}
          />
          <Button
            type="submit"
            className="w-full sm:w-auto"
            disabled={isPending}
          >
            {isPending && <Loader2Icon className="animate-spin" />}
            {tCommon('calculate')}
          </Button>
        </form>
      </Form>
      {shearReinforcementCalculationResult && (
        <>
          <MaschiMurariReinforcementShearCalculationResult
            shearReinforcementCalculationResult={
              shearReinforcementCalculationResult
            }
          />
          <Button type="submit" className="w-full sm:w-auto" onClick={onNext}>
            {tCommon('next')}
          </Button>
        </>
      )}
    </div>
  )
}
