import { NumberFormInput } from '@atlas/components/common/form/number-form-input'
import { SelectFormInput } from '@atlas/components/common/form/select-form-input'
import { StringFormInput } from '@atlas/components/common/form/string-form-input'
import { Button } from '@atlas/components/ui/button'
import { Form } from '@atlas/components/ui/form'
import type { Module } from '@atlas/lib/api/modules/schemas/modules'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'
import { useProductsByType } from '@atlas/lib/query/products/use-products-by-type'
import {
  type SlabFrcReinforcementFormInputs as FormSchema,
  slabFrcReinforcementSchema,
} from '@atlas/types/schemas/slab-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useQueryClient } from '@tanstack/react-query'
import { Loader2Icon } from 'lucide-react'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useCallback, useEffect, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'

type Props = {
  session: Session
  projectId: Project['id']
  moduleId: Module['id']
  defaultValues?: Partial<FormSchema>
  onSave: () => void
}

export const SlabFrcReinforcementForm = ({
  session,
  projectId,
  moduleId,
  defaultValues,
  onSave,
}: Props) => {
  const t = useTranslations('forms.project-params.slab.slabFrcReinforcement')
  const tAction = useTranslations('actions.save-project-params.messages')
  const tCommon = useTranslations('actions.common')
  const tProduct = useTranslations('components.common.atlas.product')
  const queryClient = useQueryClient()

  const {
    data: products,
    isError: errorGettingProducts,
    isLoading: isLoadingProducts,
  } = useProductsByType(session, 'SLAB', 0, 100)

  const customOption = { value: 'custom', label: t('product.custom') }

  const productsOptions = [
    ...(products?.content.map(m => ({
      value: m.name ?? '',
      label: m.name ?? t('product.unnamed'),
    })) ?? []),
    customOption,
  ]

  const form = useForm<FormSchema>({
    resolver: zodResolver(slabFrcReinforcementSchema),
    defaultValues: {
      frcReinforcementType: defaultValues?.frcReinforcementType,
      toughnessClass: defaultValues?.toughnessClass,
      fiberType: defaultValues?.fiberType,
      frcSlabThickness: defaultValues?.frcSlabThickness,
      elasticModulus: defaultValues?.elasticModulus,
      characteristicCylindricalCompressiveStrengthFrcMatrix:
        defaultValues?.characteristicCylindricalCompressiveStrengthFrcMatrix,
      frcCharacteristicTensileStrength:
        defaultValues?.frcCharacteristicTensileStrength,
      tensileStrength: defaultValues?.tensileStrength,
      adhesionToConcrete: defaultValues?.adhesionToConcrete,
      specificWeight: defaultValues?.specificWeight,
    },
  })

  const [frcReinforcementType] = form.watch(['frcReinforcementType'])

  const isCustomSelected = frcReinforcementType === 'custom'

  const selectedProduct = useMemo(
    () => products?.content.find(p => p.name === frcReinforcementType),
    [frcReinforcementType, products],
  )

  // Auto-fill product properties when a product is selected
  useEffect(() => {
    if (isCustomSelected || !selectedProduct) {
      return
    }

    // Format toughness class and fiber type
    if (selectedProduct.toughnessClass || selectedProduct.fiberType) {
      const parts = []
      if (selectedProduct.toughnessClass) {
        parts.push(`${t('toughnessClass')}: ${selectedProduct.toughnessClass}`)
      }
      if (selectedProduct.fiberType) {
        const fiberTypeLabel = tProduct(selectedProduct.fiberType)
        parts.push(`${t('fiberType')}: ${fiberTypeLabel}`)
      }
      form.setValue('toughnessClass', selectedProduct.toughnessClass)
      form.setValue('fiberType', selectedProduct.fiberType)
      form.setValue('toughnessClassAndFibersType', parts.join(', '))
    }

    // Populate elastic modulus
    if (selectedProduct.elasticModulus !== undefined) {
      form.setValue('elasticModulus', selectedProduct.elasticModulus)
    }

    // Populate cylindric compressive strength
    if (selectedProduct.cylindricCompressiveStrength !== undefined) {
      form.setValue(
        'characteristicCylindricalCompressiveStrengthFrcMatrix',
        selectedProduct.cylindricCompressiveStrength,
      )
    }

    // Populate characteristic tensile strength
    if (selectedProduct.characteristicTensileStrength !== undefined) {
      form.setValue(
        'frcCharacteristicTensileStrength',
        selectedProduct.characteristicTensileStrength,
      )
    }

    // Populate tensile strength
    if (selectedProduct.tensileStrength !== undefined) {
      form.setValue('tensileStrength', selectedProduct.tensileStrength)
    }

    // Populate adhesion to concrete
    if (selectedProduct.adhesionToConcrete !== undefined) {
      form.setValue('adhesionToConcrete', selectedProduct.adhesionToConcrete)
    }

    // Populate characteristicCylindricalCompressiveStrengthFrcMatrix
    if (
      selectedProduct.characteristicCylindricalCompressiveStrengthFrcMatrix !==
      undefined
    ) {
      form.setValue(
        'characteristicCylindricalCompressiveStrengthFrcMatrix',
        selectedProduct.characteristicCylindricalCompressiveStrengthFrcMatrix,
      )
    }

    // Populate specific weight
    if (selectedProduct.specificWeight !== undefined) {
      form.setValue('specificWeight', selectedProduct.specificWeight)
    }
  }, [selectedProduct, form, t, tProduct, isCustomSelected])

  const { mutate, isPending } = useSaveModuleParamsMutation(
    session.accessToken,
    {
      onSuccess: async () => {
        toast.success(tAction('edit.success'))
        await queryClient.invalidateQueries({
          queryKey: ['modules', { projectId, moduleId }],
        })
        onSave()
      },
      onError: error => {
        toast.error(tAction('edit.failure', { error: error.message }))
      },
    },
  )

  const handleFormSubmit = useCallback(
    (slabFrcReinforcement: FormSchema) => {
      mutate({ projectId, moduleId, body: { slabFrcReinforcement } })
    },
    [mutate, projectId, moduleId],
  )

  return (
    <Form {...form}>
      <form
        className="space-y-4 rounded-md border p-4"
        onSubmit={form.handleSubmit(handleFormSubmit)}
      >
        <h1 className="text-lg font-bold">{t('frcProperties.title')}</h1>

        <SelectFormInput
          control={form.control}
          name="frcReinforcementType"
          options={productsOptions}
          t={t}
          loading={isLoadingProducts}
          requestError={errorGettingProducts}
          errorMessage={t('products.error')}
        />

        {frcReinforcementType && (
          <StringFormInput
            control={form.control}
            name="toughnessClassAndFibersType"
            t={t}
            disabled={true}
          />
        )}

        <NumberFormInput control={form.control} name="frcSlabThickness" t={t} />

        <NumberFormInput
          control={form.control}
          name="elasticModulus"
          t={t}
          disabled={!isCustomSelected}
        />

        <NumberFormInput
          control={form.control}
          name="characteristicCylindricalCompressiveStrengthFrcMatrix"
          t={t}
          disabled={!isCustomSelected}
        />

        <NumberFormInput
          control={form.control}
          name="frcCharacteristicTensileStrength"
          t={t}
          disabled={!isCustomSelected}
        />

        <NumberFormInput
          control={form.control}
          name="tensileStrength"
          t={t}
          disabled={!isCustomSelected}
        />

        <NumberFormInput
          control={form.control}
          name="adhesionToConcrete"
          t={t}
          disabled={!isCustomSelected}
        />

        <NumberFormInput
          control={form.control}
          name="specificWeight"
          t={t}
          disabled={!isCustomSelected}
        />

        <Button type="submit" className="w-full sm:w-auto" disabled={isPending}>
          {isPending && <Loader2Icon className="animate-spin" />}
          {tCommon('save')}
        </Button>
      </form>
    </Form>
  )
}
