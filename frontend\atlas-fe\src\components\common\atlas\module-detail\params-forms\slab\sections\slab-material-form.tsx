import { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'
import { Button } from '@atlas/components/ui/button'
import { Form } from '@atlas/components/ui/form'
import { Separator } from '@atlas/components/ui/separator'
import {
  MODULE_MATERIAL_CLASS,
  MODULE_MATERIAL_KNOWLEDGE_LEVEL,
  moduleMaterialClass,
  moduleMaterialKnowledgeLevel,
} from '@atlas/constants/module'
import type { Module } from '@atlas/lib/api/modules/schemas/modules'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'
import {
  type SlabMaterialFormInputs as FormSchema,
  slabMaterialSchema,
} from '@atlas/types/schemas/slab-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useQueryClient } from '@tanstack/react-query'
import { Loader2Icon } from 'lucide-react'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useCallback } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { SlabConcreteClassFormSection } from './slab-concrete-class-form-section'
import { SlabSteelGradeFormSection } from './slab-steel-grade-form-section'

type Props = {
  session: Session
  projectId: Project['id']
  moduleId: Module['id']
  defaultValues?: Partial<FormSchema>
  onSave: () => void
}

export const SlabMaterialForm = ({
  session,
  projectId,
  moduleId,
  defaultValues,
  onSave,
}: Props) => {
  const t = useTranslations('forms.project-params.slab.materialProperties')
  const tAction = useTranslations('actions.save-project-params.messages')
  const tCommon = useTranslations('actions.common')
  const queryClient = useQueryClient()

  const form = useForm<FormSchema>({
    resolver: zodResolver(slabMaterialSchema),
    defaultValues: {
      concreteClassKnowledgeLevel:
        defaultValues?.concreteClassKnowledgeLevel ??
        moduleMaterialKnowledgeLevel.LC1,
      steelGradeKnowledgeLevel:
        defaultValues?.steelGradeKnowledgeLevel ??
        moduleMaterialKnowledgeLevel.LC1,
      concreteMaterialClass:
        defaultValues?.concreteMaterialClass ?? moduleMaterialClass.DUCTILE,
      steelMaterialClass:
        defaultValues?.steelMaterialClass ?? moduleMaterialClass.DUCTILE,
      concreteClass: {
        id: defaultValues?.concreteClass?.id,
        name: defaultValues?.concreteClass?.name,
        cubeCompressiveStrength:
          defaultValues?.concreteClass?.cubeCompressiveStrength,
        cylinderCompressiveStrength:
          defaultValues?.concreteClass?.cylinderCompressiveStrength,
        averageCompressiveStrength:
          defaultValues?.concreteClass?.averageCompressiveStrength,
        averageTensileStrength:
          defaultValues?.concreteClass?.averageTensileStrength,
        elasticModulus: defaultValues?.concreteClass?.elasticModulus,
        designCompressiveStrengthForBrittleMechanisms:
          defaultValues?.concreteClass
            ?.designCompressiveStrengthForBrittleMechanisms,
        designTensileStrengthForBrittleMechanisms:
          defaultValues?.concreteClass
            ?.designTensileStrengthForBrittleMechanisms,
        designCompressiveStrengthForDuctileMechanisms:
          defaultValues?.concreteClass
            ?.designCompressiveStrengthForDuctileMechanisms,
      },
      steelGrade: {
        id: defaultValues?.steelGrade?.id,
        name: defaultValues?.steelGrade?.name,
        yieldStrength: defaultValues?.steelGrade?.yieldStrength,
        tensileStrength: defaultValues?.steelGrade?.tensileStrength,
        elongationPercentage: defaultValues?.steelGrade?.elongationPercentage,
        elasticModulus: defaultValues?.steelGrade?.elasticModulus,
        designYieldStrengthForBrittleMechanisms:
          defaultValues?.steelGrade?.designYieldStrengthForBrittleMechanisms,
        designYieldStrengthForDuctileMechanisms:
          defaultValues?.steelGrade?.designYieldStrengthForDuctileMechanisms,
      },
    },
  })

  const { mutate, isPending } = useSaveModuleParamsMutation(
    session.accessToken,
    {
      onSuccess: async () => {
        toast.success(tAction('edit.success'))
        await queryClient.invalidateQueries({
          queryKey: ['modules', { projectId, moduleId }],
        })
        onSave()
      },
      onError: error => {
        toast.error(tAction('edit.failure', { error: error.message }))
      },
    },
  )

  const handleFormSubmit = useCallback(
    (materialProperties: FormSchema) => {
      mutate({ projectId, moduleId, body: { materialProperties } })
    },
    [mutate, projectId, moduleId],
  )

  return (
    <Form {...form}>
      <form
        className="space-y-4 rounded-md border p-4"
        onSubmit={form.handleSubmit(handleFormSubmit)}
      >
        <SelectFormFixedInput
          control={form.control}
          name="concreteClassKnowledgeLevel"
          options={MODULE_MATERIAL_KNOWLEDGE_LEVEL}
          optionLabelFn={p => t(`concreteClassKnowledgeLevel.${p}`)}
          t={t}
        />

        <SelectFormFixedInput
          control={form.control}
          name="steelGradeKnowledgeLevel"
          options={MODULE_MATERIAL_KNOWLEDGE_LEVEL}
          optionLabelFn={p => t(`steelGradeKnowledgeLevel.${p}`)}
          t={t}
        />

        <SelectFormFixedInput
          control={form.control}
          name="concreteMaterialClass"
          options={MODULE_MATERIAL_CLASS}
          optionLabelFn={p => t(`concreteMaterialClass.${p}`)}
          t={t}
        />

        <SelectFormFixedInput
          control={form.control}
          name="steelMaterialClass"
          options={MODULE_MATERIAL_CLASS}
          optionLabelFn={p => t(`steelMaterialClass.${p}`)}
          t={t}
        />

        <Separator />

        <SlabConcreteClassFormSection session={session} />

        <Separator />

        <SlabSteelGradeFormSection session={session} />

        <Button type="submit" className="w-full sm:w-auto" disabled={isPending}>
          {isPending && <Loader2Icon className="animate-spin" />}
          {tCommon('save')}
        </Button>
      </form>
    </Form>
  )
}
