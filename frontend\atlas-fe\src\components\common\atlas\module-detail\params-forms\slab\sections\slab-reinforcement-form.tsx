import { NumberFormInput } from '@atlas/components/common/form/number-form-input'
import { Button } from '@atlas/components/ui/button'
import { Form } from '@atlas/components/ui/form'
import { Separator } from '@atlas/components/ui/separator'
import { calculateSlabRebarArea } from '@atlas/functions/forms/slab-form-calculations'
import type { Module } from '@atlas/lib/api/modules/schemas/modules'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'
import { cn } from '@atlas/lib/utils'
import {
  type SlabRebarFormInputs as FormSchema,
  slabRebarSchema,
} from '@atlas/types/schemas/slab-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useQueryClient } from '@tanstack/react-query'
import { Loader2Icon } from 'lucide-react'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useCallback, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'

type Props = {
  session: Session
  projectId: Project['id']
  moduleId: Module['id']
  defaultValues?: Partial<FormSchema>
  structuralScheme?: 'SIMPLY_SUPPORTED' | 'CANTILEVER'
  onSave: () => void
}

export const SlabReinforcementForm = ({
  session,
  projectId,
  moduleId,
  defaultValues,
  structuralScheme,
  onSave,
}: Props) => {
  const t = useTranslations('forms.project-params.slab.slabRebar')
  const tAction = useTranslations('actions.save-project-params.messages')
  const tCommon = useTranslations('actions.common')
  const queryClient = useQueryClient()

  const form = useForm<FormSchema>({
    resolver: zodResolver(slabRebarSchema),
    defaultValues: {
      spanBottomRebar: {
        diameter: defaultValues?.spanBottomRebar?.diameter,
        quantity: defaultValues?.spanBottomRebar?.quantity,
        area: defaultValues?.spanBottomRebar?.area,
      },
      spanTopRebar: {
        diameter: defaultValues?.spanTopRebar?.diameter,
        quantity: defaultValues?.spanTopRebar?.quantity,
        area: defaultValues?.spanTopRebar?.area,
      },
      supportBottomRebar: {
        diameter: defaultValues?.supportBottomRebar?.diameter,
        quantity: defaultValues?.supportBottomRebar?.quantity,
        area: defaultValues?.supportBottomRebar?.area,
      },
      supportTopRebar: {
        diameter: defaultValues?.supportTopRebar?.diameter,
        quantity: defaultValues?.supportTopRebar?.quantity,
        area: defaultValues?.supportTopRebar?.area,
      },
      additionalRebar: {
        diameter: defaultValues?.additionalRebar?.diameter,
        quantity: defaultValues?.additionalRebar?.quantity,
        area: defaultValues?.additionalRebar?.area,
      },
      additionalSteelElasticModulus:
        defaultValues?.additionalSteelElasticModulus,
    },
  })

  const { mutate, isPending } = useSaveModuleParamsMutation(
    session.accessToken,
    {
      onSuccess: async () => {
        toast.success(tAction('edit.success'))
        await queryClient.invalidateQueries({
          queryKey: ['modules', { projectId, moduleId }],
        })
        onSave()
      },
      onError: error => {
        toast.error(tAction('edit.failure', { error: error.message }))
      },
    },
  )

  const handleFormSubmit = useCallback(
    (slabRebar: FormSchema) => {
      mutate({ projectId, moduleId, body: { slabRebar } })
    },
    [mutate, projectId, moduleId],
  )

  const [
    spanBottomRebar,
    spanTopRebar,
    supportBottomRebar,
    supportTopRebar,
    additionalRebar,
  ] = form.watch([
    'spanBottomRebar',
    'spanTopRebar',
    'supportBottomRebar',
    'supportTopRebar',
    'additionalRebar',
  ])

  // Auto-calculate span bottom rebar area (D23)
  useEffect(() => {
    if (spanBottomRebar?.diameter && spanBottomRebar?.quantity) {
      const area = calculateSlabRebarArea(
        spanBottomRebar.diameter,
        spanBottomRebar.quantity,
      )
      form.setValue('spanBottomRebar.area', area, { shouldValidate: true })
    }
  }, [spanBottomRebar?.diameter, spanBottomRebar?.quantity, form])

  // Auto-calculate span top rebar area (D26)
  useEffect(() => {
    if (spanTopRebar?.diameter && spanTopRebar?.quantity) {
      const area = calculateSlabRebarArea(
        spanTopRebar.diameter,
        spanTopRebar.quantity,
      )
      form.setValue('spanTopRebar.area', area, { shouldValidate: true })
    }
  }, [spanTopRebar?.diameter, spanTopRebar?.quantity, form])

  // Auto-calculate support bottom rebar area (D30)
  useEffect(() => {
    if (supportBottomRebar?.diameter && supportBottomRebar?.quantity) {
      const area = calculateSlabRebarArea(
        supportBottomRebar.diameter,
        supportBottomRebar.quantity,
      )
      form.setValue('supportBottomRebar.area', area, { shouldValidate: true })
    }
  }, [supportBottomRebar?.diameter, supportBottomRebar?.quantity, form])

  // Auto-calculate support top rebar area (D33)
  useEffect(() => {
    if (supportTopRebar?.diameter && supportTopRebar?.quantity) {
      const area = calculateSlabRebarArea(
        supportTopRebar.diameter,
        supportTopRebar.quantity,
      )
      form.setValue('supportTopRebar.area', area, { shouldValidate: true })
    }
  }, [supportTopRebar?.diameter, supportTopRebar?.quantity, form])

  // Auto-calculate additional rebar area (D80)
  useEffect(() => {
    if (additionalRebar?.diameter && additionalRebar?.quantity) {
      const area = calculateSlabRebarArea(
        additionalRebar.diameter,
        additionalRebar.quantity,
      )
      form.setValue('additionalRebar.area', area, { shouldValidate: true })
    }
  }, [additionalRebar?.diameter, additionalRebar?.quantity, form])

  const showSpanSection = structuralScheme === 'SIMPLY_SUPPORTED'

  return (
    <Form {...form}>
      <form
        className="space-y-4 rounded-md border p-4"
        onSubmit={form.handleSubmit(handleFormSubmit)}
      >
        {showSpanSection && (
          <>
            <h1 className="text-md font-bold">{t('spanSection.title')}</h1>
            <p className={cn('text-muted-foreground text-sm')}>
              {t('spanSection.description')}
            </p>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              <NumberFormInput
                control={form.control}
                name="spanBottomRebar.diameter"
                t={t}
              />
              <NumberFormInput
                control={form.control}
                name="spanBottomRebar.quantity"
                t={t}
              />
              <NumberFormInput
                control={form.control}
                name="spanBottomRebar.area"
                t={t}
                disabled={true}
              />
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              <NumberFormInput
                control={form.control}
                name="spanTopRebar.diameter"
                t={t}
              />
              <NumberFormInput
                control={form.control}
                name="spanTopRebar.quantity"
                t={t}
              />
              <NumberFormInput
                control={form.control}
                name="spanTopRebar.area"
                t={t}
                disabled={true}
              />
            </div>
            <Separator />
          </>
        )}

        <h1 className="text-md font-bold">{t('supportSection.title')}</h1>
        <p className={cn('text-muted-foreground text-sm')}>
          {t('supportSection.description')}
        </p>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <NumberFormInput
            control={form.control}
            name="supportBottomRebar.diameter"
            t={t}
          />
          <NumberFormInput
            control={form.control}
            name="supportBottomRebar.quantity"
            t={t}
          />
          <NumberFormInput
            control={form.control}
            name="supportBottomRebar.area"
            t={t}
            disabled={true}
          />
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <NumberFormInput
            control={form.control}
            name="supportTopRebar.diameter"
            t={t}
          />
          <NumberFormInput
            control={form.control}
            name="supportTopRebar.quantity"
            t={t}
          />
          <NumberFormInput
            control={form.control}
            name="supportTopRebar.area"
            t={t}
            disabled={true}
          />
        </div>

        <Separator />

        <h1 className="text-md font-bold">{t('additionalRebar.title')}</h1>
        <p className={cn('text-muted-foreground text-sm')}>
          {t('additionalRebar.description')}
        </p>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <NumberFormInput
            control={form.control}
            name="additionalRebar.diameter"
            t={t}
          />
          <NumberFormInput
            control={form.control}
            name="additionalRebar.quantity"
            t={t}
          />
          <NumberFormInput
            control={form.control}
            name="additionalRebar.area"
            t={t}
            disabled={true}
          />
        </div>

        <NumberFormInput
          control={form.control}
          name="additionalSteelElasticModulus"
          t={t}
        />

        <Button type="submit" className="w-full sm:w-auto" disabled={isPending}>
          {isPending && <Loader2Icon className="animate-spin" />}
          {tCommon('save')}
        </Button>
      </form>
    </Form>
  )
}
