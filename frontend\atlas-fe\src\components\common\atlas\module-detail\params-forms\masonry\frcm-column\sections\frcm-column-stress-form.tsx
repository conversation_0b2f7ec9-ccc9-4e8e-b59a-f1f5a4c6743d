import { NumberFormInput } from '@atlas/components/common/form/number-form-input'
import { Button } from '@atlas/components/ui/button'
import { Form } from '@atlas/components/ui/form'
import type { Module } from '@atlas/lib/api/modules/schemas/modules'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'
import {
  type FrcmColumnParamsSchemaInput,
  type FrcmColumnStressInput,
  frcmColumnStressSchema,
} from '@atlas/types/schemas/masonry/frcm-column-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useQueryClient } from '@tanstack/react-query'
import { Loader2Icon } from 'lucide-react'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useCallback, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'

type Props = {
  session: Session
  projectId: Project['id']
  moduleId: Module['id']
  defaultValues?: Partial<FrcmColumnStressInput>
  params: FrcmColumnParamsSchemaInput
  setParams: (newParams: any) => void
  onSave: () => void
}

export const FrcmColumnStressForm = ({
  session,
  projectId,
  moduleId,
  defaultValues,
  params,
  setParams,
  onSave,
}: Props) => {
  const t = useTranslations('forms.project-params.frcm-column.stress')
  const tAction = useTranslations('actions.save-project-params.messages')
  const tCommon = useTranslations('actions.common')
  const queryClient = useQueryClient()

  const form = useForm<FrcmColumnStressInput>({
    resolver: zodResolver(frcmColumnStressSchema),
    defaultValues: {
      normalStressCenteredStressing:
        defaultValues?.normalStressCenteredStressing ?? 0,
    },
  })

  const { mutate, isPending } = useSaveModuleParamsMutation(
    session.accessToken,
    {
      onSuccess: async () => {
        toast.success(tAction('edit.success'))
        await queryClient.invalidateQueries({
          queryKey: ['modules', { projectId, moduleId }],
        })
        onSave()
      },
      onError: error => {
        toast.error(tAction('edit.failure', { error: error.message }))
      },
    },
  )

  const handleFormSubmit = useCallback(
    (body: FrcmColumnStressInput) => {
      // construct body based on global schema:
      const frcmColumnParams: FrcmColumnParamsSchemaInput = {
        ...params,
        stress: body,
      }
      mutate({ projectId, moduleId, body: frcmColumnParams })
    },
    [mutate, projectId, moduleId, params],
  )

  useEffect(() => {
    const subscription = form.watch(values => {
      setParams((prev: any) => ({
        ...prev,
        buildingCharacteristics: values,
      }))
    })
    return () => subscription.unsubscribe()
  }, [form, setParams])

  return (
    <Form {...form}>
      <form
        className="space-y-4 rounded-md border p-4"
        onSubmit={form.handleSubmit(handleFormSubmit)}
      >
        <NumberFormInput
          control={form.control}
          name="normalStressCenteredStressing"
          t={t}
        />
        <Button
          type="submit"
          className="w-full sm:w-auto"
          disabled={isPending}
          onClick={form.handleSubmit(handleFormSubmit)}
        >
          {isPending && <Loader2Icon className="animate-spin" />}
          {tCommon('save')}
        </Button>
      </form>
    </Form>
  )
}
