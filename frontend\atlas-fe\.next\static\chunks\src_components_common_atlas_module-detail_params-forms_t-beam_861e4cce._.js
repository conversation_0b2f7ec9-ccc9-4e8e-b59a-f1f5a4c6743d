(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-general-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "TBeamGeneralForm",
    ()=>TBeamGeneralForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/select-form-fixed-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-save-module-params-mutation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$t$2d$beam$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/t-beam-form.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const TBeamGeneralForm = (param)=>{
    let { session, projectId, moduleId, defaultValues, onSave } = param;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.t-beam.general');
    const tPolarity = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.common.polarity');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.save-project-params.messages');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    var _defaultValues_initialDeformation, _defaultValues_polarity;
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$t$2d$beam$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tBeamGeneralSchema"]),
        defaultValues: {
            initialDeformation: (_defaultValues_initialDeformation = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.initialDeformation) !== null && _defaultValues_initialDeformation !== void 0 ? _defaultValues_initialDeformation : 0,
            polarity: (_defaultValues_polarity = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.polarity) !== null && _defaultValues_polarity !== void 0 ? _defaultValues_polarity : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["modulePolarity"].POSITIVE
        }
    });
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"])(session.accessToken, {
        onSuccess: {
            "TBeamGeneralForm.useSaveModuleParamsMutation": async ()=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(tAction('edit.success'));
                await queryClient.invalidateQueries({
                    queryKey: [
                        'modules',
                        {
                            projectId,
                            moduleId
                        }
                    ]
                });
                onSave();
            }
        }["TBeamGeneralForm.useSaveModuleParamsMutation"],
        onError: {
            "TBeamGeneralForm.useSaveModuleParamsMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(tAction('edit.failure', {
                    error: error.message
                }));
            }
        }["TBeamGeneralForm.useSaveModuleParamsMutation"]
    });
    const handleFormSubmit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "TBeamGeneralForm.useCallback[handleFormSubmit]": (body)=>{
            mutate({
                projectId,
                moduleId,
                body
            });
        }
    }["TBeamGeneralForm.useCallback[handleFormSubmit]"], [
        mutate,
        projectId,
        moduleId
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Form"], {
        ...form,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            className: "space-y-4 rounded-md border p-4",
            onSubmit: form.handleSubmit(handleFormSubmit),
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "polarity",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_POLARITY"],
                    optionLabelFn: (p)=>tPolarity(p),
                    t: t,
                    description: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-general-form.tsx",
                    lineNumber: 79,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "initialDeformation",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-general-form.tsx",
                    lineNumber: 87,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    type: "submit",
                    className: "w-full sm:w-auto",
                    disabled: isPending,
                    onClick: form.handleSubmit(handleFormSubmit),
                    children: [
                        isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                            className: "animate-spin"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-general-form.tsx",
                            lineNumber: 98,
                            columnNumber: 25
                        }, ("TURBOPACK compile-time value", void 0)),
                        tCommon('save')
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-general-form.tsx",
                    lineNumber: 92,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-general-form.tsx",
            lineNumber: 75,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-general-form.tsx",
        lineNumber: 74,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(TBeamGeneralForm, "xvWyEUQhm7yDCADnyIHeY438aEE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"]
    ];
});
_c = TBeamGeneralForm;
var _c;
__turbopack_context__.k.register(_c, "TBeamGeneralForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-geometry-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "TBeamGeometryForm",
    ()=>TBeamGeometryForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$polarity$2d$aware$2d$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/polarity-aware-number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/select-form-fixed-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$forms$2f$t$2d$beam$2d$form$2d$calculations$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/functions/forms/t-beam-form-calculations.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-save-module-params-mutation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$t$2d$beam$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/t-beam-form.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const TBeamGeometryForm = (param)=>{
    let { session, projectId, moduleId, defaultValues, polarity, onSave } = param;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.t-beam.geometry');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.save-project-params.messages');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    var _defaultValues_exposure;
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$t$2d$beam$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tBeamGeometrySchema"]),
        defaultValues: {
            primaryHeight: defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.primaryHeight,
            primaryWidth: defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.primaryWidth,
            secondaryWidth: defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.secondaryWidth,
            secondaryHeight: defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.secondaryHeight,
            concreteCover1: defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.concreteCover1,
            concreteCover2: defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.concreteCover2,
            totalHeight: defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.totalHeight,
            effectiveDepth: defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.effectiveDepth,
            exposure: (_defaultValues_exposure = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.exposure) !== null && _defaultValues_exposure !== void 0 ? _defaultValues_exposure : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleGeometryExposure"].INTERNAL
        }
    });
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"])(session.accessToken, {
        onSuccess: {
            "TBeamGeometryForm.useSaveModuleParamsMutation": async ()=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(tAction('edit.success'));
                await queryClient.invalidateQueries({
                    queryKey: [
                        'modules',
                        {
                            projectId,
                            moduleId
                        }
                    ]
                });
                onSave();
            }
        }["TBeamGeometryForm.useSaveModuleParamsMutation"],
        onError: {
            "TBeamGeometryForm.useSaveModuleParamsMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(tAction('edit.failure', {
                    error: error.message
                }));
            }
        }["TBeamGeometryForm.useSaveModuleParamsMutation"]
    });
    const handleFormSubmit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "TBeamGeometryForm.useCallback[handleFormSubmit]": (geometry)=>{
            mutate({
                projectId,
                moduleId,
                body: {
                    geometry
                }
            });
        }
    }["TBeamGeometryForm.useCallback[handleFormSubmit]"], [
        mutate,
        projectId,
        moduleId
    ]);
    const [_primaryWidth, primaryHeight, secondaryHeight, _concreteCover2, concreteCover1] = form.watch([
        'primaryWidth',
        'primaryHeight',
        'secondaryHeight',
        'concreteCover2',
        'concreteCover1'
    ]);
    // Calculate total height when web height and flange thickness change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TBeamGeometryForm.useEffect": ()=>{
            if (primaryHeight && secondaryHeight) {
                form.setValue('totalHeight', (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$forms$2f$t$2d$beam$2d$form$2d$calculations$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calculateTBeamTotalHeight"])(primaryHeight, secondaryHeight));
            }
        }
    }["TBeamGeometryForm.useEffect"], [
        primaryHeight,
        secondaryHeight,
        form
    ]);
    // Calculate effective depth based on polarity
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TBeamGeometryForm.useEffect": ()=>{
            const totalHeight = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$forms$2f$t$2d$beam$2d$form$2d$calculations$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calculateTBeamTotalHeight"])(primaryHeight || 0, secondaryHeight || 0);
            if (totalHeight && concreteCover1) {
                form.setValue('effectiveDepth', (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$forms$2f$t$2d$beam$2d$form$2d$calculations$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calculateTBeamEffectiveDepth"])(totalHeight, concreteCover1));
            }
        }
    }["TBeamGeometryForm.useEffect"], [
        primaryHeight,
        secondaryHeight,
        concreteCover1,
        form
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col justify-center gap-2",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                src: polarity === 'NEGATIVE' ? '/assets/t_beam/T_beam_geometry_M-.png' : '/assets/t_beam/T_beam_geometry_M+.png',
                alt: "T-beam geometry",
                height: 500,
                width: 500,
                className: "mx-auto rounded-md object-contain",
                priority: true
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-geometry-form.tsx",
                lineNumber: 131,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Form"], {
                ...form,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                    className: "space-y-4 rounded-md border p-4 grow",
                    onSubmit: form.handleSubmit(handleFormSubmit),
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$polarity$2d$aware$2d$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PolarityAwareNumberFormInput"], {
                            control: form.control,
                            name: "primaryHeight",
                            t: t,
                            polarity: polarity
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-geometry-form.tsx",
                            lineNumber: 148,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$polarity$2d$aware$2d$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PolarityAwareNumberFormInput"], {
                            control: form.control,
                            name: "primaryWidth",
                            t: t,
                            polarity: polarity
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-geometry-form.tsx",
                            lineNumber: 154,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$polarity$2d$aware$2d$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PolarityAwareNumberFormInput"], {
                            control: form.control,
                            name: "secondaryWidth",
                            t: t,
                            polarity: polarity
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-geometry-form.tsx",
                            lineNumber: 160,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$polarity$2d$aware$2d$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PolarityAwareNumberFormInput"], {
                            control: form.control,
                            name: "secondaryHeight",
                            t: t,
                            polarity: polarity
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-geometry-form.tsx",
                            lineNumber: 166,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$polarity$2d$aware$2d$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PolarityAwareNumberFormInput"], {
                            control: form.control,
                            name: "concreteCover1",
                            t: t,
                            polarity: polarity
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-geometry-form.tsx",
                            lineNumber: 172,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$polarity$2d$aware$2d$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PolarityAwareNumberFormInput"], {
                            control: form.control,
                            name: "concreteCover2",
                            t: t,
                            polarity: polarity
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-geometry-form.tsx",
                            lineNumber: 178,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "totalHeight",
                            t: t,
                            disabled: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-geometry-form.tsx",
                            lineNumber: 184,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "effectiveDepth",
                            t: t,
                            disabled: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-geometry-form.tsx",
                            lineNumber: 190,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                            control: form.control,
                            name: "exposure",
                            options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_GEOMETRY_EXPOSURE"],
                            optionLabelFn: (p)=>t("exposure.".concat(p)),
                            t: t
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-geometry-form.tsx",
                            lineNumber: 196,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                            type: "submit",
                            className: "w-full sm:w-auto",
                            disabled: isPending,
                            onClick: form.handleSubmit(handleFormSubmit),
                            children: [
                                isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                                    className: "animate-spin"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-geometry-form.tsx",
                                    lineNumber: 209,
                                    columnNumber: 27
                                }, ("TURBOPACK compile-time value", void 0)),
                                tCommon('save')
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-geometry-form.tsx",
                            lineNumber: 203,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-geometry-form.tsx",
                    lineNumber: 144,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-geometry-form.tsx",
                lineNumber: 143,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-geometry-form.tsx",
        lineNumber: 130,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(TBeamGeometryForm, "q8qy4WSiQgq59By9bYU1Gd3awn8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"]
    ];
});
_c = TBeamGeometryForm;
var _c;
__turbopack_context__.k.register(_c, "TBeamGeometryForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-concrete-class-form-section.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "TBeamConcreteClassFormSection",
    ()=>TBeamConcreteClassFormSection
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/select-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$string$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/string-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$forms$2f$t$2d$beam$2d$form$2d$calculations$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/functions/forms/t-beam-form-calculations.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$materials$2f$use$2d$reinforced$2d$concrete$2d$materials$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/query/materials/use-reinforced-concrete-materials.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
function TBeamConcreteClassFormSection(param) {
    let { session } = param;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.t-beam.materialProperties');
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useFormContext"])();
    const { data: concreteMaterials, isError: concreteMaterialsError, isLoading: concreteMaterialsLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$materials$2f$use$2d$reinforced$2d$concrete$2d$materials$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useReinforcedConcreteMaterials"])({
        session,
        body: {
            size: 100
        }
    });
    const customOption = {
        value: 'custom',
        label: t('concreteClass.custom')
    };
    var _concreteMaterials_content_map;
    const concreteMaterialsOptions = [
        ...(_concreteMaterials_content_map = concreteMaterials === null || concreteMaterials === void 0 ? void 0 : concreteMaterials.content.map((m)=>({
                value: m.id,
                label: m.name
            }))) !== null && _concreteMaterials_content_map !== void 0 ? _concreteMaterials_content_map : [],
        customOption
    ];
    const knowledgeLevel = form.watch('knowledgeLevel');
    const concreteClassId = form.watch('concreteClass.id');
    const averageCompressiveStrength = form.watch('concreteClass.averageCompressiveStrength');
    const isCustomSelected = concreteClassId === 'custom';
    const selectedConcreteMaterial = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "TBeamConcreteClassFormSection.useMemo[selectedConcreteMaterial]": ()=>concreteMaterials === null || concreteMaterials === void 0 ? void 0 : concreteMaterials.content.find({
                "TBeamConcreteClassFormSection.useMemo[selectedConcreteMaterial]": (m)=>m.id === concreteClassId
            }["TBeamConcreteClassFormSection.useMemo[selectedConcreteMaterial]"])
    }["TBeamConcreteClassFormSection.useMemo[selectedConcreteMaterial]"], [
        concreteClassId,
        concreteMaterials
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TBeamConcreteClassFormSection.useEffect": ()=>{
            if (!selectedConcreteMaterial || isCustomSelected) {
                return;
            }
            const selected = selectedConcreteMaterial;
            if (form.getValues('concreteClass.name') !== selected.name) {
                form.setValue('concreteClass.name', selected.name);
            }
            if (form.getValues('concreteClass.cubeCompressiveStrength') !== selected.cubeCompressiveStrength) {
                form.setValue('concreteClass.cubeCompressiveStrength', selected.cubeCompressiveStrength);
            }
            if (form.getValues('concreteClass.cylinderCompressiveStrength') !== selected.cylinderCompressiveStrength) {
                form.setValue('concreteClass.cylinderCompressiveStrength', selected.cylinderCompressiveStrength);
            }
            if (form.getValues('concreteClass.averageCompressiveStrength') !== selected.averageCompressiveStrength) {
                form.setValue('concreteClass.averageCompressiveStrength', selected.averageCompressiveStrength);
            }
            if (form.getValues('concreteClass.averageTensileStrength') !== selected.averageTensileStrength) {
                form.setValue('concreteClass.averageTensileStrength', selected.averageTensileStrength);
            }
            if (form.getValues('concreteClass.elasticModulus') !== selected.elasticModulus) {
                form.setValue('concreteClass.elasticModulus', selected.elasticModulus);
            }
        }
    }["TBeamConcreteClassFormSection.useEffect"], [
        form,
        isCustomSelected,
        selectedConcreteMaterial
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TBeamConcreteClassFormSection.useEffect": ()=>{
            if (knowledgeLevel && averageCompressiveStrength !== undefined && averageCompressiveStrength !== null) {
                const confidenceFactor = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleMaterialKnowledgeLevelValues"][knowledgeLevel];
                form.setValue('concreteClass.designCompressiveStrengthForBrittleMechanisms', (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$forms$2f$t$2d$beam$2d$form$2d$calculations$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calculateTBeamDesignStrengthForBrittleMechanisms"])(averageCompressiveStrength, confidenceFactor));
                form.setValue('concreteClass.designCompressiveStrengthForDuctileMechanisms', (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$forms$2f$t$2d$beam$2d$form$2d$calculations$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calculateTBeamDesignStrengthForDuctileMechanisms"])(averageCompressiveStrength, confidenceFactor));
            }
        }
    }["TBeamConcreteClassFormSection.useEffect"], [
        knowledgeLevel,
        averageCompressiveStrength,
        form
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormInput"], {
                control: form.control,
                name: "concreteClass.id",
                options: concreteMaterialsOptions,
                t: t,
                loading: concreteMaterialsLoading,
                requestError: concreteMaterialsError,
                errorMessage: t('concreteClass.error')
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-concrete-class-form-section.tsx",
                lineNumber: 132,
                columnNumber: 7
            }, this),
            isCustomSelected && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$string$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["StringFormInput"], {
                control: form.control,
                name: "concreteClass.name",
                t: t
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-concrete-class-form-section.tsx",
                lineNumber: 142,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                control: form.control,
                name: "concreteClass.cubeCompressiveStrength",
                t: t,
                disabled: !isCustomSelected
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-concrete-class-form-section.tsx",
                lineNumber: 148,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                control: form.control,
                name: "concreteClass.cylinderCompressiveStrength",
                t: t,
                disabled: !isCustomSelected
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-concrete-class-form-section.tsx",
                lineNumber: 154,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                control: form.control,
                name: "concreteClass.averageCompressiveStrength",
                t: t,
                disabled: !isCustomSelected
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-concrete-class-form-section.tsx",
                lineNumber: 160,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                control: form.control,
                name: "concreteClass.averageTensileStrength",
                t: t,
                disabled: !isCustomSelected
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-concrete-class-form-section.tsx",
                lineNumber: 166,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                control: form.control,
                name: "concreteClass.elasticModulus",
                t: t,
                disabled: !isCustomSelected
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-concrete-class-form-section.tsx",
                lineNumber: 172,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                control: form.control,
                name: "concreteClass.designCompressiveStrengthForDuctileMechanisms",
                t: t,
                disabled: true
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-concrete-class-form-section.tsx",
                lineNumber: 178,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                control: form.control,
                name: "concreteClass.designCompressiveStrengthForBrittleMechanisms",
                t: t,
                disabled: true
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-concrete-class-form-section.tsx",
                lineNumber: 184,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
_s(TBeamConcreteClassFormSection, "MaJU4Cz0cgItNFOHAwIOta6wDf4=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useFormContext"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$materials$2f$use$2d$reinforced$2d$concrete$2d$materials$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useReinforcedConcreteMaterials"]
    ];
});
_c = TBeamConcreteClassFormSection;
var _c;
__turbopack_context__.k.register(_c, "TBeamConcreteClassFormSection");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-steel-grade-form-section.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "TBeamSteelGradeFormSection",
    ()=>TBeamSteelGradeFormSection
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/select-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$string$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/string-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$forms$2f$t$2d$beam$2d$form$2d$calculations$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/functions/forms/t-beam-form-calculations.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$materials$2f$use$2d$steel$2d$grades$2d$materials$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/query/materials/use-steel-grades-materials.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
function TBeamSteelGradeFormSection(param) {
    let { session } = param;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.t-beam.materialProperties');
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useFormContext"])();
    const { data: steelMaterials, isError: steelMaterialsError, isLoading: steelMaterialsLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$materials$2f$use$2d$steel$2d$grades$2d$materials$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSteelGradesMaterials"])({
        session,
        body: {
            size: 100
        }
    });
    const customOption = {
        value: 'custom',
        label: t('steelGrade.custom')
    };
    var _steelMaterials_content_map;
    const steelMaterialsOptions = [
        ...(_steelMaterials_content_map = steelMaterials === null || steelMaterials === void 0 ? void 0 : steelMaterials.content.map((m)=>({
                value: m.id,
                label: m.name
            }))) !== null && _steelMaterials_content_map !== void 0 ? _steelMaterials_content_map : [],
        customOption
    ];
    const knowledgeLevel = form.watch('knowledgeLevel');
    const steelGradeId = form.watch('steelGrade.id');
    const yieldStrength = form.watch('steelGrade.yieldStrength');
    const isCustomSelected = steelGradeId === 'custom';
    const selectedSteelMaterial = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "TBeamSteelGradeFormSection.useMemo[selectedSteelMaterial]": ()=>steelMaterials === null || steelMaterials === void 0 ? void 0 : steelMaterials.content.find({
                "TBeamSteelGradeFormSection.useMemo[selectedSteelMaterial]": (m)=>m.id === steelGradeId
            }["TBeamSteelGradeFormSection.useMemo[selectedSteelMaterial]"])
    }["TBeamSteelGradeFormSection.useMemo[selectedSteelMaterial]"], [
        steelGradeId,
        steelMaterials
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TBeamSteelGradeFormSection.useEffect": ()=>{
            if (!selectedSteelMaterial || isCustomSelected) {
                return;
            }
            const selected = selectedSteelMaterial;
            if (form.getValues('steelGrade.name') !== selected.name) {
                form.setValue('steelGrade.name', selected.name);
            }
            if (form.getValues('steelGrade.yieldStrength') !== selected.yieldStrength) {
                form.setValue('steelGrade.yieldStrength', selected.yieldStrength);
            }
            if (form.getValues('steelGrade.tensileStrength') !== selected.tensileStrength) {
                form.setValue('steelGrade.tensileStrength', selected.tensileStrength);
            }
            if (form.getValues('steelGrade.elongationPercentage') !== selected.elongationPercentage) {
                form.setValue('steelGrade.elongationPercentage', selected.elongationPercentage);
            }
            if (form.getValues('steelGrade.elasticModulus') !== selected.elasticModulus) {
                form.setValue('steelGrade.elasticModulus', selected.elasticModulus);
            }
        }
    }["TBeamSteelGradeFormSection.useEffect"], [
        form,
        isCustomSelected,
        selectedSteelMaterial
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TBeamSteelGradeFormSection.useEffect": ()=>{
            if (knowledgeLevel && yieldStrength !== undefined && yieldStrength !== null) {
                const confidenceFactor = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleMaterialKnowledgeLevelValues"][knowledgeLevel];
                form.setValue('steelGrade.designYieldStrengthForDuctileMechanisms', (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$forms$2f$t$2d$beam$2d$form$2d$calculations$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calculateTBeamDesignYieldStrengthForDuctileMechanisms"])(yieldStrength, confidenceFactor));
                form.setValue('steelGrade.designYieldStrengthForBrittleMechanisms', (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$forms$2f$t$2d$beam$2d$form$2d$calculations$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calculateTBeamDesignYieldStrengthForBrittleMechanisms"])(yieldStrength, confidenceFactor));
            }
        }
    }["TBeamSteelGradeFormSection.useEffect"], [
        knowledgeLevel,
        yieldStrength,
        form
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormInput"], {
                control: form.control,
                name: "steelGrade.id",
                options: steelMaterialsOptions,
                t: t,
                loading: steelMaterialsLoading,
                requestError: steelMaterialsError,
                errorMessage: t('steelGrade.error')
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-steel-grade-form-section.tsx",
                lineNumber: 103,
                columnNumber: 7
            }, this),
            isCustomSelected && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$string$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["StringFormInput"], {
                control: form.control,
                name: "steelGrade.name",
                t: t
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-steel-grade-form-section.tsx",
                lineNumber: 113,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                control: form.control,
                name: "steelGrade.yieldStrength",
                t: t,
                disabled: !isCustomSelected
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-steel-grade-form-section.tsx",
                lineNumber: 115,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                control: form.control,
                name: "steelGrade.elasticModulus",
                t: t,
                disabled: !isCustomSelected
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-steel-grade-form-section.tsx",
                lineNumber: 121,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                control: form.control,
                name: "steelGrade.designYieldStrengthForDuctileMechanisms",
                t: t,
                disabled: true
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-steel-grade-form-section.tsx",
                lineNumber: 127,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                control: form.control,
                name: "steelGrade.designYieldStrengthForBrittleMechanisms",
                t: t,
                disabled: true
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-steel-grade-form-section.tsx",
                lineNumber: 133,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
_s(TBeamSteelGradeFormSection, "wOVlSHiwxcJR/Aj/pbSestrueDU=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useFormContext"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$materials$2f$use$2d$steel$2d$grades$2d$materials$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSteelGradesMaterials"]
    ];
});
_c = TBeamSteelGradeFormSection;
var _c;
__turbopack_context__.k.register(_c, "TBeamSteelGradeFormSection");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-material-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "TBeamMaterialForm",
    ()=>TBeamMaterialForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/select-form-fixed-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/separator.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-save-module-params-mutation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$t$2d$beam$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/t-beam-form.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$t$2d$beam$2f$sections$2f$t$2d$beam$2d$concrete$2d$class$2d$form$2d$section$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-concrete-class-form-section.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$t$2d$beam$2f$sections$2f$t$2d$beam$2d$steel$2d$grade$2d$form$2d$section$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-steel-grade-form-section.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const TBeamMaterialForm = (param)=>{
    let { session, projectId, moduleId, defaultValues, onSave } = param;
    var _defaultValues_concreteClass, _defaultValues_concreteClass1, _defaultValues_concreteClass2, _defaultValues_concreteClass3, _defaultValues_concreteClass4, _defaultValues_concreteClass5, _defaultValues_concreteClass6, _defaultValues_concreteClass7, _defaultValues_concreteClass8, _defaultValues_steelGrade, _defaultValues_steelGrade1, _defaultValues_steelGrade2, _defaultValues_steelGrade3, _defaultValues_steelGrade4, _defaultValues_steelGrade5, _defaultValues_steelGrade6, _defaultValues_steelGrade7;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.t-beam.materialProperties');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.save-project-params.messages');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    var _defaultValues_knowledgeLevel;
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$t$2d$beam$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tBeamMaterialSchema"]),
        defaultValues: {
            knowledgeLevel: (_defaultValues_knowledgeLevel = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.knowledgeLevel) !== null && _defaultValues_knowledgeLevel !== void 0 ? _defaultValues_knowledgeLevel : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleMaterialKnowledgeLevel"].LC1,
            confidenceFactor: (defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.knowledgeLevel) ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleMaterialKnowledgeLevelValues"][defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.knowledgeLevel] : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleMaterialKnowledgeLevelValues"][__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleMaterialKnowledgeLevel"].LC1],
            concreteClass: {
                id: defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_concreteClass = defaultValues.concreteClass) === null || _defaultValues_concreteClass === void 0 ? void 0 : _defaultValues_concreteClass.id,
                name: defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_concreteClass1 = defaultValues.concreteClass) === null || _defaultValues_concreteClass1 === void 0 ? void 0 : _defaultValues_concreteClass1.name,
                cubeCompressiveStrength: defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_concreteClass2 = defaultValues.concreteClass) === null || _defaultValues_concreteClass2 === void 0 ? void 0 : _defaultValues_concreteClass2.cubeCompressiveStrength,
                cylinderCompressiveStrength: defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_concreteClass3 = defaultValues.concreteClass) === null || _defaultValues_concreteClass3 === void 0 ? void 0 : _defaultValues_concreteClass3.cylinderCompressiveStrength,
                averageCompressiveStrength: defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_concreteClass4 = defaultValues.concreteClass) === null || _defaultValues_concreteClass4 === void 0 ? void 0 : _defaultValues_concreteClass4.averageCompressiveStrength,
                averageTensileStrength: defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_concreteClass5 = defaultValues.concreteClass) === null || _defaultValues_concreteClass5 === void 0 ? void 0 : _defaultValues_concreteClass5.averageTensileStrength,
                elasticModulus: defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_concreteClass6 = defaultValues.concreteClass) === null || _defaultValues_concreteClass6 === void 0 ? void 0 : _defaultValues_concreteClass6.elasticModulus,
                designCompressiveStrengthForBrittleMechanisms: defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_concreteClass7 = defaultValues.concreteClass) === null || _defaultValues_concreteClass7 === void 0 ? void 0 : _defaultValues_concreteClass7.designCompressiveStrengthForBrittleMechanisms,
                designCompressiveStrengthForDuctileMechanisms: defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_concreteClass8 = defaultValues.concreteClass) === null || _defaultValues_concreteClass8 === void 0 ? void 0 : _defaultValues_concreteClass8.designCompressiveStrengthForDuctileMechanisms
            },
            steelGrade: {
                id: defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_steelGrade = defaultValues.steelGrade) === null || _defaultValues_steelGrade === void 0 ? void 0 : _defaultValues_steelGrade.id,
                name: defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_steelGrade1 = defaultValues.steelGrade) === null || _defaultValues_steelGrade1 === void 0 ? void 0 : _defaultValues_steelGrade1.name,
                yieldStrength: defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_steelGrade2 = defaultValues.steelGrade) === null || _defaultValues_steelGrade2 === void 0 ? void 0 : _defaultValues_steelGrade2.yieldStrength,
                tensileStrength: defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_steelGrade3 = defaultValues.steelGrade) === null || _defaultValues_steelGrade3 === void 0 ? void 0 : _defaultValues_steelGrade3.tensileStrength,
                elongationPercentage: defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_steelGrade4 = defaultValues.steelGrade) === null || _defaultValues_steelGrade4 === void 0 ? void 0 : _defaultValues_steelGrade4.elongationPercentage,
                elasticModulus: defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_steelGrade5 = defaultValues.steelGrade) === null || _defaultValues_steelGrade5 === void 0 ? void 0 : _defaultValues_steelGrade5.elasticModulus,
                designYieldStrengthForBrittleMechanisms: defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_steelGrade6 = defaultValues.steelGrade) === null || _defaultValues_steelGrade6 === void 0 ? void 0 : _defaultValues_steelGrade6.designYieldStrengthForBrittleMechanisms,
                designYieldStrengthForDuctileMechanisms: defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_steelGrade7 = defaultValues.steelGrade) === null || _defaultValues_steelGrade7 === void 0 ? void 0 : _defaultValues_steelGrade7.designYieldStrengthForDuctileMechanisms
            }
        }
    });
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"])(session.accessToken, {
        onSuccess: {
            "TBeamMaterialForm.useSaveModuleParamsMutation": async ()=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(tAction('edit.success'));
                await queryClient.invalidateQueries({
                    queryKey: [
                        'modules',
                        {
                            projectId,
                            moduleId
                        }
                    ]
                });
                onSave();
            }
        }["TBeamMaterialForm.useSaveModuleParamsMutation"],
        onError: {
            "TBeamMaterialForm.useSaveModuleParamsMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(tAction('edit.failure', {
                    error: error.message
                }));
            }
        }["TBeamMaterialForm.useSaveModuleParamsMutation"]
    });
    const handleFormSubmit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "TBeamMaterialForm.useCallback[handleFormSubmit]": (materialProperties)=>{
            mutate({
                projectId,
                moduleId,
                body: {
                    materialProperties
                }
            });
        }
    }["TBeamMaterialForm.useCallback[handleFormSubmit]"], [
        mutate,
        projectId,
        moduleId
    ]);
    const knowledgeLevel = form.watch('knowledgeLevel');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TBeamMaterialForm.useEffect": ()=>{
            if (knowledgeLevel) {
                form.setValue('confidenceFactor', __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleMaterialKnowledgeLevelValues"][knowledgeLevel]);
            }
        }
    }["TBeamMaterialForm.useEffect"], [
        knowledgeLevel,
        form
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Form"], {
        ...form,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            className: "space-y-4 rounded-md border p-4",
            onSubmit: form.handleSubmit(handleFormSubmit),
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "knowledgeLevel",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_MATERIAL_KNOWLEDGE_LEVEL"],
                    optionLabelFn: (p)=>t("knowledgeLevel.".concat(p)),
                    t: t,
                    description: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-material-form.tsx",
                    lineNumber: 130,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "confidenceFactor",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-material-form.tsx",
                    lineNumber: 138,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-material-form.tsx",
                    lineNumber: 144,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$t$2d$beam$2f$sections$2f$t$2d$beam$2d$concrete$2d$class$2d$form$2d$section$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TBeamConcreteClassFormSection"], {
                    session: session
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-material-form.tsx",
                    lineNumber: 145,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-material-form.tsx",
                    lineNumber: 146,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$t$2d$beam$2f$sections$2f$t$2d$beam$2d$steel$2d$grade$2d$form$2d$section$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TBeamSteelGradeFormSection"], {
                    session: session
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-material-form.tsx",
                    lineNumber: 147,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    type: "submit",
                    className: "w-full sm:w-auto",
                    disabled: isPending,
                    children: [
                        isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                            className: "animate-spin"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-material-form.tsx",
                            lineNumber: 149,
                            columnNumber: 25
                        }, ("TURBOPACK compile-time value", void 0)),
                        tCommon('save')
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-material-form.tsx",
                    lineNumber: 148,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-material-form.tsx",
            lineNumber: 126,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-material-form.tsx",
        lineNumber: 125,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(TBeamMaterialForm, "0GvcWzWtWEWdQUcgL4k4yYfwxkU=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"]
    ];
});
_c = TBeamMaterialForm;
var _c;
__turbopack_context__.k.register(_c, "TBeamMaterialForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-reinforcement-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "TBeamReinforcementForm",
    ()=>TBeamReinforcementForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$polarity$2d$aware$2d$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/polarity-aware-number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/separator.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$forms$2f$t$2d$beam$2d$form$2d$calculations$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/functions/forms/t-beam-form-calculations.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-save-module-params-mutation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$t$2d$beam$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/t-beam-form.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const TBeamReinforcementForm = (param)=>{
    let { session, projectId, moduleId, defaultValues, onSave, polarity } = param;
    var _defaultValues_compressionRebars, _defaultValues_compressionRebars1, _defaultValues_compressionRebars2, _defaultValues_tensionRebars, _defaultValues_tensionRebars1, _defaultValues_tensionRebars2;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.t-beam.tBeamRebar');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.save-project-params.messages');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$t$2d$beam$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tBeamRebarSchema"]),
        defaultValues: {
            compressionRebars: {
                diameter: defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_compressionRebars = defaultValues.compressionRebars) === null || _defaultValues_compressionRebars === void 0 ? void 0 : _defaultValues_compressionRebars.diameter,
                quantity: defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_compressionRebars1 = defaultValues.compressionRebars) === null || _defaultValues_compressionRebars1 === void 0 ? void 0 : _defaultValues_compressionRebars1.quantity,
                area: defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_compressionRebars2 = defaultValues.compressionRebars) === null || _defaultValues_compressionRebars2 === void 0 ? void 0 : _defaultValues_compressionRebars2.area
            },
            tensionRebars: {
                diameter: defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_tensionRebars = defaultValues.tensionRebars) === null || _defaultValues_tensionRebars === void 0 ? void 0 : _defaultValues_tensionRebars.diameter,
                quantity: defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_tensionRebars1 = defaultValues.tensionRebars) === null || _defaultValues_tensionRebars1 === void 0 ? void 0 : _defaultValues_tensionRebars1.quantity,
                area: defaultValues === null || defaultValues === void 0 ? void 0 : (_defaultValues_tensionRebars2 = defaultValues.tensionRebars) === null || _defaultValues_tensionRebars2 === void 0 ? void 0 : _defaultValues_tensionRebars2.area
            }
        }
    });
    const [compressionRebars, tensionRebars] = form.watch([
        'compressionRebars',
        'tensionRebars'
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TBeamReinforcementForm.useEffect": ()=>{
            if (compressionRebars.diameter && compressionRebars.quantity) {
                form.setValue('compressionRebars.area', (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$forms$2f$t$2d$beam$2d$form$2d$calculations$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calculateTBeamArmorArea"])(compressionRebars.diameter, compressionRebars.quantity));
            }
        }
    }["TBeamReinforcementForm.useEffect"], [
        compressionRebars.diameter,
        compressionRebars.quantity,
        form
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TBeamReinforcementForm.useEffect": ()=>{
            if (tensionRebars.diameter && tensionRebars.quantity) {
                form.setValue('tensionRebars.area', (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$forms$2f$t$2d$beam$2d$form$2d$calculations$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calculateTBeamArmorArea"])(tensionRebars.diameter, tensionRebars.quantity));
            }
        }
    }["TBeamReinforcementForm.useEffect"], [
        tensionRebars.diameter,
        tensionRebars.quantity,
        form
    ]);
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"])(session.accessToken, {
        onSuccess: {
            "TBeamReinforcementForm.useSaveModuleParamsMutation": async ()=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(tAction('edit.success'));
                await queryClient.invalidateQueries({
                    queryKey: [
                        'modules',
                        {
                            projectId,
                            moduleId
                        }
                    ]
                });
                onSave();
            }
        }["TBeamReinforcementForm.useSaveModuleParamsMutation"],
        onError: {
            "TBeamReinforcementForm.useSaveModuleParamsMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(tAction('edit.failure', {
                    error: error.message
                }));
            }
        }["TBeamReinforcementForm.useSaveModuleParamsMutation"]
    });
    const handleFormSubmit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "TBeamReinforcementForm.useCallback[handleFormSubmit]": (tBeamRebar)=>{
            mutate({
                projectId,
                moduleId,
                body: {
                    tBeamRebar
                }
            });
        }
    }["TBeamReinforcementForm.useCallback[handleFormSubmit]"], [
        mutate,
        projectId,
        moduleId
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Form"], {
        ...form,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            className: "space-y-4 rounded-md border p-4",
            onSubmit: form.handleSubmit(handleFormSubmit),
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                    className: "text-md font-bold",
                    children: t("compressionRebars.subtitlePolarity.".concat(polarity))
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-reinforcement-form.tsx",
                    lineNumber: 117,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-muted-foreground text-sm'),
                    children: t('compressionRebars.description')
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-reinforcement-form.tsx",
                    lineNumber: 120,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "compressionRebars.diameter",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-reinforcement-form.tsx",
                    lineNumber: 123,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "compressionRebars.quantity",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-reinforcement-form.tsx",
                    lineNumber: 128,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$polarity$2d$aware$2d$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PolarityAwareNumberFormInput"], {
                    control: form.control,
                    name: "compressionRebars.area",
                    t: t,
                    polarity: polarity,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-reinforcement-form.tsx",
                    lineNumber: 133,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-reinforcement-form.tsx",
                    lineNumber: 140,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                    className: "text-md font-bold",
                    children: t("tensionRebars.subtitlePolarity.".concat(polarity))
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-reinforcement-form.tsx",
                    lineNumber: 141,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-muted-foreground text-sm'),
                    children: t('tensionRebars.description')
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-reinforcement-form.tsx",
                    lineNumber: 144,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "tensionRebars.diameter",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-reinforcement-form.tsx",
                    lineNumber: 147,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "tensionRebars.quantity",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-reinforcement-form.tsx",
                    lineNumber: 152,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$polarity$2d$aware$2d$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PolarityAwareNumberFormInput"], {
                    control: form.control,
                    name: "tensionRebars.area",
                    t: t,
                    polarity: polarity,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-reinforcement-form.tsx",
                    lineNumber: 157,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    type: "submit",
                    className: "w-full sm:w-auto",
                    disabled: isPending,
                    onClick: form.handleSubmit(handleFormSubmit),
                    children: [
                        isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                            className: "animate-spin"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-reinforcement-form.tsx",
                            lineNumber: 170,
                            columnNumber: 25
                        }, ("TURBOPACK compile-time value", void 0)),
                        tCommon('save')
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-reinforcement-form.tsx",
                    lineNumber: 164,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-reinforcement-form.tsx",
            lineNumber: 113,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-reinforcement-form.tsx",
        lineNumber: 112,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(TBeamReinforcementForm, "Q3nePKqZTTyBzABNjrRRYJrZ/3E=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"]
    ];
});
_c = TBeamReinforcementForm;
var _c;
__turbopack_context__.k.register(_c, "TBeamReinforcementForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-result-card.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "TBeamFlexuralResultCard",
    ()=>TBeamFlexuralResultCard
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
;
var _s = __turbopack_context__.k.signature();
;
;
const TBeamFlexuralResultCard = (param)=>{
    let { results } = param;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.calculations.t-beam.flexural.results');
    const resultItems = [
        {
            key: 'momentCapacity',
            value: results.momentCapacity
        },
        {
            key: 'equilibrium',
            value: results.equilibrium
        },
        {
            key: 'checkResult',
            value: results.checkResult ? t('pass') : t('fail')
        }
    ];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                    children: t('title')
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-result-card.tsx",
                    lineNumber: 26,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-result-card.tsx",
                lineNumber: 25,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                className: "space-y-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",
                        children: resultItems.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-col space-y-1 p-3 bg-muted rounded-md",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-sm font-medium text-muted-foreground",
                                        children: t("".concat(item.key, ".label"))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-result-card.tsx",
                                        lineNumber: 35,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "text-lg font-semibold",
                                        children: [
                                            typeof item.value === 'number' ? item.value.toFixed(4) : item.value,
                                            typeof item.value === 'number' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-sm text-muted-foreground ml-1",
                                                children: t("".concat(item.key, ".unit"))
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-result-card.tsx",
                                                lineNumber: 43,
                                                columnNumber: 19
                                            }, ("TURBOPACK compile-time value", void 0))
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-result-card.tsx",
                                        lineNumber: 38,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, item.key, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-result-card.tsx",
                                lineNumber: 31,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)))
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-result-card.tsx",
                        lineNumber: 29,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "p-4 rounded-md border-2 ".concat(results.checkResult ? 'border-green-500 bg-green-50 text-green-800' : 'border-red-500 bg-red-50 text-red-800'),
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "font-semibold",
                            children: results.checkResult ? t('verification.pass') : t('verification.fail')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-result-card.tsx",
                            lineNumber: 58,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-result-card.tsx",
                        lineNumber: 51,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-result-card.tsx",
                lineNumber: 28,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-result-card.tsx",
        lineNumber: 24,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(TBeamFlexuralResultCard, "h6+q2O3NJKPY5uL0BIJGLIanww8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"]
    ];
});
_c = TBeamFlexuralResultCard;
var _c;
__turbopack_context__.k.register(_c, "TBeamFlexuralResultCard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-calculation.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "TBeamFlexuralCalculation",
    ()=>TBeamFlexuralCalculation
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$t$2d$beam$2f$calculations$2f$t$2d$beam$2d$flexural$2d$result$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-result-card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$product$2f$custom$2d$product$2d$section$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/product/custom-product-section.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$product$2f$product$2d$summary$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/product/product-summary-card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/select-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$product$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/product.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$module$2d$calculation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-module-calculation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$products$2f$use$2d$products$2d$by$2d$category$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/query/products/use-products-by-category.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$t$2d$beam$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/t-beam-form.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const TBeamFlexuralCalculation = (param)=>{
    let { session, module, projectId } = param;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.calculations.t-beam.flexural');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.calculations.t-beam.flexural');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const { flexuralVerifyExecutionInput, flexuralVerifyExecutionResult } = module;
    var _flexuralVerifyExecutionInput_product_fiberType, _flexuralVerifyExecutionInput_stripWidth, _flexuralVerifyExecutionInput_layersNumber, _flexuralVerifyExecutionInput_bendingMoment;
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$t$2d$beam$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tBeamFlexuralCalculationSchema"]),
        defaultValues: {
            calculationType: 'FLEXURAL_VERIFY',
            input: {
                product: {
                    id: flexuralVerifyExecutionInput === null || flexuralVerifyExecutionInput === void 0 ? void 0 : flexuralVerifyExecutionInput.product.id,
                    name: flexuralVerifyExecutionInput === null || flexuralVerifyExecutionInput === void 0 ? void 0 : flexuralVerifyExecutionInput.product.name,
                    sourceType: (flexuralVerifyExecutionInput === null || flexuralVerifyExecutionInput === void 0 ? void 0 : flexuralVerifyExecutionInput.product.id) === 'custom' ? 'CUSTOM' : 'DATABASE',
                    thickness: flexuralVerifyExecutionInput === null || flexuralVerifyExecutionInput === void 0 ? void 0 : flexuralVerifyExecutionInput.product.thickness,
                    tensileStrength: flexuralVerifyExecutionInput === null || flexuralVerifyExecutionInput === void 0 ? void 0 : flexuralVerifyExecutionInput.product.tensileStrength,
                    elasticModulus: flexuralVerifyExecutionInput === null || flexuralVerifyExecutionInput === void 0 ? void 0 : flexuralVerifyExecutionInput.product.elasticModulus,
                    fiberType: (_flexuralVerifyExecutionInput_product_fiberType = flexuralVerifyExecutionInput === null || flexuralVerifyExecutionInput === void 0 ? void 0 : flexuralVerifyExecutionInput.product.fiberType) !== null && _flexuralVerifyExecutionInput_product_fiberType !== void 0 ? _flexuralVerifyExecutionInput_product_fiberType : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$product$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["productFiberType"].CARBON
                },
                stripWidth: (_flexuralVerifyExecutionInput_stripWidth = flexuralVerifyExecutionInput === null || flexuralVerifyExecutionInput === void 0 ? void 0 : flexuralVerifyExecutionInput.stripWidth) !== null && _flexuralVerifyExecutionInput_stripWidth !== void 0 ? _flexuralVerifyExecutionInput_stripWidth : 1,
                layersNumber: (_flexuralVerifyExecutionInput_layersNumber = flexuralVerifyExecutionInput === null || flexuralVerifyExecutionInput === void 0 ? void 0 : flexuralVerifyExecutionInput.layersNumber) !== null && _flexuralVerifyExecutionInput_layersNumber !== void 0 ? _flexuralVerifyExecutionInput_layersNumber : 2,
                bendingMoment: (_flexuralVerifyExecutionInput_bendingMoment = flexuralVerifyExecutionInput === null || flexuralVerifyExecutionInput === void 0 ? void 0 : flexuralVerifyExecutionInput.bendingMoment) !== null && _flexuralVerifyExecutionInput_bendingMoment !== void 0 ? _flexuralVerifyExecutionInput_bendingMoment : 10
            }
        }
    });
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$module$2d$calculation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useModuleCalculation"])(session.accessToken, {
        onSuccess: {
            "TBeamFlexuralCalculation.useModuleCalculation": ()=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(tAction('calculate.success'));
            }
        }["TBeamFlexuralCalculation.useModuleCalculation"],
        onError: {
            "TBeamFlexuralCalculation.useModuleCalculation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(tAction('calculate.failure', {
                    error: error.message
                }));
            }
        }["TBeamFlexuralCalculation.useModuleCalculation"]
    });
    const handleFormSubmit = (body)=>{
        const res = mutate({
            projectId,
            moduleId: module.id,
            body
        });
        console.log(res);
    };
    const { data: products, isError: errorGettingProducts, isLoading: isLoadingProducts } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$products$2f$use$2d$products$2d$by$2d$category$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useProductsByCategory"])(session, 'T_BEAM', 0, 100);
    var _products_content_map;
    const productsOptions = [
        ...(_products_content_map = products === null || products === void 0 ? void 0 : products.content.map((m)=>{
            var _m_name;
            return {
                value: m.id,
                label: (_m_name = m.name) !== null && _m_name !== void 0 ? _m_name : t('product.unnamed')
            };
        })) !== null && _products_content_map !== void 0 ? _products_content_map : [],
        {
            value: 'custom',
            label: t('product.custom')
        }
    ];
    const [productId] = form.watch([
        'input.product.id'
    ]);
    const selectedProduct = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "TBeamFlexuralCalculation.useMemo[selectedProduct]": ()=>products === null || products === void 0 ? void 0 : products.content.find({
                "TBeamFlexuralCalculation.useMemo[selectedProduct]": (p)=>p.id === productId
            }["TBeamFlexuralCalculation.useMemo[selectedProduct]"])
    }["TBeamFlexuralCalculation.useMemo[selectedProduct]"], [
        productId,
        products
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TBeamFlexuralCalculation.useEffect": ()=>{
            if (!productId) {
                return;
            }
            if (productId === 'custom') {
                form.setValue('input.product.sourceType', 'CUSTOM');
            }
            if (selectedProduct) {
                form.setValue('input.product', {
                    ...selectedProduct,
                    sourceType: 'DATABASE'
                });
            }
        }
    }["TBeamFlexuralCalculation.useEffect"], [
        form,
        productId,
        selectedProduct
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col justify-center gap-4",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Form"], {
                ...form,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                    className: "space-y-4 rounded-md border p-4",
                    onSubmit: form.handleSubmit(handleFormSubmit),
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-3xl font-bold",
                            children: t('heading')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-calculation.tsx",
                            lineNumber: 125,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            src: "/assets/t_beam/T_beam_flexural_verify.jpg",
                            alt: "flexural verify",
                            height: 250,
                            width: 500,
                            className: "mx-auto rounded-md object-contain",
                            priority: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-calculation.tsx",
                            lineNumber: 126,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-xl font-bold",
                            children: t('sub-heading')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-calculation.tsx",
                            lineNumber: 134,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormInput"], {
                            control: form.control,
                            name: "input.product.id",
                            options: productsOptions,
                            t: t,
                            loading: isLoadingProducts,
                            requestError: errorGettingProducts,
                            errorMessage: t('products.error')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-calculation.tsx",
                            lineNumber: 135,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        productId === 'custom' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$product$2f$custom$2d$product$2d$section$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CustomProductSection"], {}, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-calculation.tsx",
                            lineNumber: 144,
                            columnNumber: 38
                        }, ("TURBOPACK compile-time value", void 0)),
                        selectedProduct && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$product$2f$product$2d$summary$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ProductSummaryCard"], {
                            product: selectedProduct
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-calculation.tsx",
                            lineNumber: 145,
                            columnNumber: 31
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.stripWidth",
                            t: t
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-calculation.tsx",
                            lineNumber: 146,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.layersNumber",
                            t: t
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-calculation.tsx",
                            lineNumber: 151,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.bendingMoment",
                            t: t
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-calculation.tsx",
                            lineNumber: 156,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                            type: "submit",
                            className: "w-full sm:w-auto",
                            disabled: isPending,
                            children: [
                                isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                                    className: "animate-spin"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-calculation.tsx",
                                    lineNumber: 166,
                                    columnNumber: 27
                                }, ("TURBOPACK compile-time value", void 0)),
                                tCommon('calculate')
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-calculation.tsx",
                            lineNumber: 161,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-calculation.tsx",
                    lineNumber: 121,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-calculation.tsx",
                lineNumber: 120,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            flexuralVerifyExecutionResult && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$t$2d$beam$2f$calculations$2f$t$2d$beam$2d$flexural$2d$result$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TBeamFlexuralResultCard"], {
                results: flexuralVerifyExecutionResult
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-calculation.tsx",
                lineNumber: 172,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-calculation.tsx",
        lineNumber: 119,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(TBeamFlexuralCalculation, "PASt7Pj3XMYeJU6OPHzcN/Djfww=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$module$2d$calculation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useModuleCalculation"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$products$2f$use$2d$products$2d$by$2d$category$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useProductsByCategory"]
    ];
});
_c = TBeamFlexuralCalculation;
var _c;
__turbopack_context__.k.register(_c, "TBeamFlexuralCalculation");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-calculations.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "TBeamCalculations",
    ()=>TBeamCalculations
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$t$2d$beam$2f$calculations$2f$t$2d$beam$2d$flexural$2d$calculation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-calculation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/tabs.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
;
var _s = __turbopack_context__.k.signature();
;
;
;
const TBeamCalculations = (param)=>{
    let { session, module, projectId } = param;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.calculations.t-beam');
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                className: "text-lg font-medium py-4",
                children: t('title')
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-calculations.tsx",
                lineNumber: 24,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tabs"], {
                defaultValue: "flexural",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsList"], {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                            value: "flexural",
                            children: t('flexural.label')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-calculations.tsx",
                            lineNumber: 27,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-calculations.tsx",
                        lineNumber: 26,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                        value: "flexural",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$t$2d$beam$2f$calculations$2f$t$2d$beam$2d$flexural$2d$calculation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TBeamFlexuralCalculation"], {
                            session: session,
                            module: module,
                            projectId: projectId
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-calculations.tsx",
                            lineNumber: 30,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-calculations.tsx",
                        lineNumber: 29,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-calculations.tsx",
                lineNumber: 25,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true);
};
_s(TBeamCalculations, "h6+q2O3NJKPY5uL0BIJGLIanww8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"]
    ];
});
_c = TBeamCalculations;
var _c;
__turbopack_context__.k.register(_c, "TBeamCalculations");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "TBeamParamsForm",
    ()=>TBeamParamsForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$module$2d$report$2d$generation$2d$section$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/module-report-generation-section.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$t$2d$beam$2f$sections$2f$t$2d$beam$2d$general$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-general-form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$t$2d$beam$2f$sections$2f$t$2d$beam$2d$geometry$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-geometry-form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$t$2d$beam$2f$sections$2f$t$2d$beam$2d$material$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-material-form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$t$2d$beam$2f$sections$2f$t$2d$beam$2d$reinforcement$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-reinforcement-form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$t$2d$beam$2f$t$2d$beam$2d$calculations$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-calculations.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/accordion.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/separator.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$t$2d$beam$2d$params$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/modules/schemas/t-beam-params.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$t$2d$beam$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/t-beam-form.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
const TBeamParamsForm = (param)=>{
    let { session, projectId, moduleId, module } = param;
    _s();
    const { params } = module;
    const [openItems, setOpenItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([
        '0'
    ]);
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.t-beam');
    const handleItemSaved = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "TBeamParamsForm.useCallback[handleItemSaved]": (id)=>{
            const nextId = String(Number(id) + 1);
            setOpenItems({
                "TBeamParamsForm.useCallback[handleItemSaved]": (old)=>{
                    const temp = old.filter({
                        "TBeamParamsForm.useCallback[handleItemSaved].temp": (v)=>v !== id
                    }["TBeamParamsForm.useCallback[handleItemSaved].temp"]);
                    return old.includes(nextId) ? temp : [
                        ...temp,
                        nextId
                    ];
                }
            }["TBeamParamsForm.useCallback[handleItemSaved]"]);
        }
    }["TBeamParamsForm.useCallback[handleItemSaved]"], []);
    const { success } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "TBeamParamsForm.useMemo": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$t$2d$beam$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tBeamParamsCheckSchema"].safeParse(params)
    }["TBeamParamsForm.useMemo"], [
        params
    ]);
    const enableReport = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "TBeamParamsForm.useMemo[enableReport]": ()=>{
            const result = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$t$2d$beam$2d$params$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tBeamCalculationCheck"].safeParse(module);
            return result.success;
        }
    }["TBeamParamsForm.useMemo[enableReport]"], [
        module
    ]);
    var _params_polarity, _params_polarity1;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Accordion"], {
                type: "multiple",
                value: openItems,
                onValueChange: setOpenItems,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionItem"], {
                        value: "0",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-lg font-medium",
                                    children: t('general.title')
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx",
                                    lineNumber: 70,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx",
                                lineNumber: 69,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionContent"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$t$2d$beam$2f$sections$2f$t$2d$beam$2d$general$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TBeamGeneralForm"], {
                                    session: session,
                                    projectId: projectId,
                                    moduleId: moduleId,
                                    defaultValues: {
                                        initialDeformation: params === null || params === void 0 ? void 0 : params.initialDeformation,
                                        polarity: params === null || params === void 0 ? void 0 : params.polarity
                                    },
                                    onSave: ()=>handleItemSaved('0')
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx",
                                    lineNumber: 73,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx",
                                lineNumber: 72,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx",
                        lineNumber: 68,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionItem"], {
                        value: "1",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-lg font-medium",
                                    children: t('geometry.title')
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx",
                                    lineNumber: 87,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx",
                                lineNumber: 86,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionContent"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$t$2d$beam$2f$sections$2f$t$2d$beam$2d$geometry$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TBeamGeometryForm"], {
                                    session: session,
                                    projectId: projectId,
                                    moduleId: moduleId,
                                    defaultValues: params === null || params === void 0 ? void 0 : params.geometry,
                                    polarity: (_params_polarity = params.polarity) !== null && _params_polarity !== void 0 ? _params_polarity : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["modulePolarity"].POSITIVE,
                                    onSave: ()=>handleItemSaved('1')
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx",
                                    lineNumber: 90,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx",
                                lineNumber: 89,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx",
                        lineNumber: 85,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionItem"], {
                        value: "2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-lg font-medium",
                                    children: t('tBeamRebar.title')
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx",
                                    lineNumber: 102,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx",
                                lineNumber: 101,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionContent"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$t$2d$beam$2f$sections$2f$t$2d$beam$2d$reinforcement$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TBeamReinforcementForm"], {
                                    session: session,
                                    projectId: projectId,
                                    moduleId: moduleId,
                                    defaultValues: params === null || params === void 0 ? void 0 : params.tBeamRebar,
                                    polarity: (_params_polarity1 = params.polarity) !== null && _params_polarity1 !== void 0 ? _params_polarity1 : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["modulePolarity"].POSITIVE,
                                    onSave: ()=>handleItemSaved('2')
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx",
                                    lineNumber: 105,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx",
                                lineNumber: 104,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx",
                        lineNumber: 100,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionItem"], {
                        value: "3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-lg font-medium",
                                    children: t('materialProperties.title')
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx",
                                    lineNumber: 117,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx",
                                lineNumber: 116,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionContent"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$t$2d$beam$2f$sections$2f$t$2d$beam$2d$material$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TBeamMaterialForm"], {
                                    session: session,
                                    projectId: projectId,
                                    moduleId: moduleId,
                                    defaultValues: params === null || params === void 0 ? void 0 : params.materialProperties,
                                    onSave: ()=>handleItemSaved('3')
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx",
                                    lineNumber: 122,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx",
                                lineNumber: 121,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx",
                        lineNumber: 115,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx",
                lineNumber: 63,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            success && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx",
                        lineNumber: 135,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$t$2d$beam$2f$t$2d$beam$2d$calculations$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TBeamCalculations"], {
                        module: module,
                        session: session,
                        projectId: projectId
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx",
                        lineNumber: 136,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true),
            enableReport && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx",
                        lineNumber: 146,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$module$2d$report$2d$generation$2d$section$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ModuleReportGenerationSection"], {
                        session: session,
                        projectId: projectId,
                        moduleId: moduleId,
                        enabled: enableReport
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx",
                        lineNumber: 147,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx",
        lineNumber: 62,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(TBeamParamsForm, "9AUt9IAh3CTqNDOPvB9r16T42vY=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"]
    ];
});
_c = TBeamParamsForm;
var _c;
__turbopack_context__.k.register(_c, "TBeamParamsForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
]);

//# sourceMappingURL=src_components_common_atlas_module-detail_params-forms_t-beam_861e4cce._.js.map