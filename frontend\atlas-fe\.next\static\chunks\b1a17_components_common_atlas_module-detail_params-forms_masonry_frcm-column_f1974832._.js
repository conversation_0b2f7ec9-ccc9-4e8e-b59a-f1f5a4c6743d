(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-nonreinforced-section-result-card.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "ConfinementNonReinforcedSectionResultCard",
    ()=>ConfinementNonReinforcedSectionResultCard
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/use-intl/dist/esm/development/react.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
function ConfinementNonReinforcedSectionResultCard(param) {
    let { nonReinforcedSectionResults } = param;
    _s();
    const { normalStressStrength, designAxialResistance, check } = nonReinforcedSectionResults;
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('components.calculations.frcm-column.confinementReinforcementCalculationResult.confinementNonReinforcedSectionResult');
    const locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                    children: t('title')
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-nonreinforced-section-result-card.tsx",
                    lineNumber: 30,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-nonreinforced-section-result-card.tsx",
                lineNumber: 29,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                className: "space-y-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('normalStressStrength.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-nonreinforced-section-result-card.tsx",
                                lineNumber: 34,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    normalStressStrength === null || normalStressStrength === void 0 ? void 0 : normalStressStrength.toLocaleString(locale, {
                                        maximumFractionDigits: 3
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-nonreinforced-section-result-card.tsx",
                                lineNumber: 37,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-nonreinforced-section-result-card.tsx",
                        lineNumber: 33,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('designAxialResistance.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-nonreinforced-section-result-card.tsx",
                                lineNumber: 44,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    designAxialResistance === null || designAxialResistance === void 0 ? void 0 : designAxialResistance.toLocaleString(locale, {
                                        maximumFractionDigits: 3
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-nonreinforced-section-result-card.tsx",
                                lineNumber: 47,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-nonreinforced-section-result-card.tsx",
                        lineNumber: 43,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "font-medium",
                        children: [
                            t('check.label'),
                            ":"
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-nonreinforced-section-result-card.tsx",
                        lineNumber: 53,
                        columnNumber: 9
                    }, this),
                    ' ',
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-base px-3 py-1', check ? 'bg-green-600' : 'bg-red-600'),
                        children: check ? t('check.satisfied') : t('check.notSatisfied')
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-nonreinforced-section-result-card.tsx",
                        lineNumber: 54,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-nonreinforced-section-result-card.tsx",
                lineNumber: 32,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-nonreinforced-section-result-card.tsx",
        lineNumber: 28,
        columnNumber: 5
    }, this);
}
_s(ConfinementNonReinforcedSectionResultCard, "ibHvjDxkLpek5QWjIfNYywjof5I=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"]
    ];
});
_c = ConfinementNonReinforcedSectionResultCard;
var _c;
__turbopack_context__.k.register(_c, "ConfinementNonReinforcedSectionResultCard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-reinforced-section-result-card.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "ConfinementReinforcedSectionResultCard",
    ()=>ConfinementReinforcedSectionResultCard
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/use-intl/dist/esm/development/react.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
function ConfinementReinforcedSectionResultCard(param) {
    let { reinforcedSectionResults } = param;
    _s();
    const { coefficientOfResistanceIncrease, confinedColumnDesignResistanceWithFrcm, designAxialResistanceOfTheConfinedColumnWithFrcm, check } = reinforcedSectionResults;
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('components.calculations.frcm-column.confinementReinforcementCalculationResult.confinementReinforcedSectionResult');
    const locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"])();
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                    children: t('title')
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-reinforced-section-result-card.tsx",
                    lineNumber: 34,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-reinforced-section-result-card.tsx",
                lineNumber: 33,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                className: "space-y-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('coefficientOfResistanceIncrease.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-reinforced-section-result-card.tsx",
                                lineNumber: 38,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    coefficientOfResistanceIncrease === null || coefficientOfResistanceIncrease === void 0 ? void 0 : coefficientOfResistanceIncrease.toLocaleString(locale, {
                                        maximumFractionDigits: 3
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-reinforced-section-result-card.tsx",
                                lineNumber: 41,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-reinforced-section-result-card.tsx",
                        lineNumber: 37,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('confinedColumnDesignResistanceWithFrcm.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-reinforced-section-result-card.tsx",
                                lineNumber: 48,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: [
                                    confinedColumnDesignResistanceWithFrcm === null || confinedColumnDesignResistanceWithFrcm === void 0 ? void 0 : confinedColumnDesignResistanceWithFrcm.toLocaleString(locale, {
                                        maximumFractionDigits: 3
                                    }),
                                    ' '
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-reinforced-section-result-card.tsx",
                                lineNumber: 51,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-reinforced-section-result-card.tsx",
                        lineNumber: 47,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: [
                                            t('designAxialResistanceOfTheConfinedColumnWithFrcm.label'),
                                            ":"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-reinforced-section-result-card.tsx",
                                        lineNumber: 59,
                                        columnNumber: 13
                                    }, this),
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            designAxialResistanceOfTheConfinedColumnWithFrcm === null || designAxialResistanceOfTheConfinedColumnWithFrcm === void 0 ? void 0 : designAxialResistanceOfTheConfinedColumnWithFrcm.toLocaleString(locale, {
                                                maximumFractionDigits: 3
                                            }),
                                            ' '
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-reinforced-section-result-card.tsx",
                                        lineNumber: 62,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-reinforced-section-result-card.tsx",
                                lineNumber: 58,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('check.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-reinforced-section-result-card.tsx",
                                lineNumber: 71,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Badge"], {
                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-base px-3 py-1', check ? 'bg-green-600' : 'bg-red-600'),
                                children: check ? t('check.satisfied') : t('check.notSatisfied')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-reinforced-section-result-card.tsx",
                                lineNumber: 72,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-reinforced-section-result-card.tsx",
                        lineNumber: 57,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-reinforced-section-result-card.tsx",
                lineNumber: 36,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-reinforced-section-result-card.tsx",
        lineNumber: 32,
        columnNumber: 5
    }, this);
}
_s(ConfinementReinforcedSectionResultCard, "ibHvjDxkLpek5QWjIfNYywjof5I=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"]
    ];
});
_c = ConfinementReinforcedSectionResultCard;
var _c;
__turbopack_context__.k.register(_c, "ConfinementReinforcedSectionResultCard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "FrcmColumnConfinementReinforcementCalculationResult",
    ()=>FrcmColumnConfinementReinforcementCalculationResult
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/tabs.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/use-intl/dist/esm/development/react.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$frcm$2d$column$2f$calculations$2f$confinement$2d$nonreinforced$2d$section$2d$result$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-nonreinforced-section-result-card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$frcm$2d$column$2f$calculations$2f$confinement$2d$reinforced$2d$section$2d$result$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/confinement-reinforced-section-result-card.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
function FrcmColumnConfinementReinforcementCalculationResult(param) {
    let { confinementReinforcementCalculationResult } = param;
    var _confinementReinforcementCalculationResult_matrixGeometricPercentage, _confinementReinforcementCalculationResult_reinforcementGeometricPercentage, _confinementReinforcementCalculationResult_coefficicentOfHorizontalEfficiency, _confinementReinforcementCalculationResult_coefficicentOfVerticalEfficiency, _confinementReinforcementCalculationResult_coefficientOfOverallEfficiency, _confinementReinforcementCalculationResult_coefficientOfEffectivenessOfTheMatrix, _confinementReinforcementCalculationResult_computationalStrainOfTheComposite, _confinementReinforcementCalculationResult_confiningPressure, _confinementReinforcementCalculationResult_effectiveConfiningPressure, _confinementReinforcementCalculationResult_confinementCheck, _confinementReinforcementCalculationResult_confinementCheck1;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('components.calculations.frcm-column.confinementReinforcementCalculationResult');
    const locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"])();
    var _confinementReinforcementCalculationResult_confinementCheck_nonReinforcedSection, _confinementReinforcementCalculationResult_confinementCheck_reinforcedSection;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                className: "text-lg font-medium py-4",
                children: t('title')
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                lineNumber: 32,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                            children: t('title')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                            lineNumber: 35,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                        lineNumber: 34,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                        className: "space-y-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: [
                                            t('matrixGeometricPercentage.label'),
                                            ":"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                        lineNumber: 39,
                                        columnNumber: 13
                                    }, this),
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            (_confinementReinforcementCalculationResult_matrixGeometricPercentage = confinementReinforcementCalculationResult.matrixGeometricPercentage) === null || _confinementReinforcementCalculationResult_matrixGeometricPercentage === void 0 ? void 0 : _confinementReinforcementCalculationResult_matrixGeometricPercentage.toLocaleString(locale, {
                                                maximumFractionDigits: 5
                                            }),
                                            ' ',
                                            "%"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                        lineNumber: 42,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                lineNumber: 38,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: [
                                            t('reinforcementGeometricPercentage.label'),
                                            ":"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                        lineNumber: 53,
                                        columnNumber: 13
                                    }, this),
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            (_confinementReinforcementCalculationResult_reinforcementGeometricPercentage = confinementReinforcementCalculationResult.reinforcementGeometricPercentage) === null || _confinementReinforcementCalculationResult_reinforcementGeometricPercentage === void 0 ? void 0 : _confinementReinforcementCalculationResult_reinforcementGeometricPercentage.toLocaleString(locale, {
                                                maximumFractionDigits: 5
                                            }),
                                            ' ',
                                            "%"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                        lineNumber: 56,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                lineNumber: 52,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: [
                                            t('coefficicentOfHorizontalEfficiency.label'),
                                            ":"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                        lineNumber: 67,
                                        columnNumber: 13
                                    }, this),
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            (_confinementReinforcementCalculationResult_coefficicentOfHorizontalEfficiency = confinementReinforcementCalculationResult.coefficicentOfHorizontalEfficiency) === null || _confinementReinforcementCalculationResult_coefficicentOfHorizontalEfficiency === void 0 ? void 0 : _confinementReinforcementCalculationResult_coefficicentOfHorizontalEfficiency.toLocaleString(locale, {
                                                maximumFractionDigits: 5
                                            }),
                                            ' '
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                        lineNumber: 70,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                lineNumber: 66,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: [
                                            t('coefficicentOfVerticalEfficiency.label'),
                                            ":"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                        lineNumber: 80,
                                        columnNumber: 13
                                    }, this),
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            (_confinementReinforcementCalculationResult_coefficicentOfVerticalEfficiency = confinementReinforcementCalculationResult.coefficicentOfVerticalEfficiency) === null || _confinementReinforcementCalculationResult_coefficicentOfVerticalEfficiency === void 0 ? void 0 : _confinementReinforcementCalculationResult_coefficicentOfVerticalEfficiency.toLocaleString(locale, {
                                                maximumFractionDigits: 5
                                            }),
                                            ' '
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                        lineNumber: 83,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                lineNumber: 79,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: [
                                            t('coefficientOfOverallEfficiency.label'),
                                            ":"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                        lineNumber: 94,
                                        columnNumber: 13
                                    }, this),
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            (_confinementReinforcementCalculationResult_coefficientOfOverallEfficiency = confinementReinforcementCalculationResult.coefficientOfOverallEfficiency) === null || _confinementReinforcementCalculationResult_coefficientOfOverallEfficiency === void 0 ? void 0 : _confinementReinforcementCalculationResult_coefficientOfOverallEfficiency.toLocaleString(locale, {
                                                maximumFractionDigits: 5
                                            }),
                                            ' '
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                        lineNumber: 97,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                lineNumber: 93,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: [
                                            t('coefficientOfEffectivenessOfTheMatrix.label'),
                                            ":"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                        lineNumber: 107,
                                        columnNumber: 13
                                    }, this),
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            (_confinementReinforcementCalculationResult_coefficientOfEffectivenessOfTheMatrix = confinementReinforcementCalculationResult.coefficientOfEffectivenessOfTheMatrix) === null || _confinementReinforcementCalculationResult_coefficientOfEffectivenessOfTheMatrix === void 0 ? void 0 : _confinementReinforcementCalculationResult_coefficientOfEffectivenessOfTheMatrix.toLocaleString(locale, {
                                                maximumFractionDigits: 5
                                            }),
                                            ' '
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                        lineNumber: 110,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                lineNumber: 106,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: [
                                            t('computationalStrainOfTheComposite.label'),
                                            ":"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                        lineNumber: 120,
                                        columnNumber: 13
                                    }, this),
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            (_confinementReinforcementCalculationResult_computationalStrainOfTheComposite = confinementReinforcementCalculationResult.computationalStrainOfTheComposite) === null || _confinementReinforcementCalculationResult_computationalStrainOfTheComposite === void 0 ? void 0 : _confinementReinforcementCalculationResult_computationalStrainOfTheComposite.toLocaleString(locale, {
                                                maximumFractionDigits: 5
                                            }),
                                            ' '
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                        lineNumber: 123,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                lineNumber: 119,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: [
                                            t('confiningPressure.label'),
                                            ":"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                        lineNumber: 133,
                                        columnNumber: 13
                                    }, this),
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            (_confinementReinforcementCalculationResult_confiningPressure = confinementReinforcementCalculationResult.confiningPressure) === null || _confinementReinforcementCalculationResult_confiningPressure === void 0 ? void 0 : _confinementReinforcementCalculationResult_confiningPressure.toLocaleString(locale, {
                                                maximumFractionDigits: 5
                                            }),
                                            ' '
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                        lineNumber: 134,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                lineNumber: 132,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium",
                                        children: [
                                            t('effectiveConfiningPressure.label'),
                                            ":"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                        lineNumber: 144,
                                        columnNumber: 13
                                    }, this),
                                    ' ',
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        children: [
                                            (_confinementReinforcementCalculationResult_effectiveConfiningPressure = confinementReinforcementCalculationResult.effectiveConfiningPressure) === null || _confinementReinforcementCalculationResult_effectiveConfiningPressure === void 0 ? void 0 : _confinementReinforcementCalculationResult_effectiveConfiningPressure.toLocaleString(locale, {
                                                maximumFractionDigits: 5
                                            }),
                                            ' '
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                        lineNumber: 147,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                lineNumber: 143,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                        lineNumber: 37,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                lineNumber: 33,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tabs"], {
                defaultValue: "nonReinforcedSection",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsList"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                value: "nonReinforcedSection",
                                children: t('confinementNonReinforcedSectionResult.label')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                lineNumber: 160,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                value: "reinforcedSection",
                                children: t('confinementNonReinforcedSectionResult.label')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                                lineNumber: 163,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                        lineNumber: 159,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                        value: "nonReinforcedSection",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$frcm$2d$column$2f$calculations$2f$confinement$2d$nonreinforced$2d$section$2d$result$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfinementNonReinforcedSectionResultCard"], {
                            nonReinforcedSectionResults: (_confinementReinforcementCalculationResult_confinementCheck_nonReinforcedSection = confinementReinforcementCalculationResult === null || confinementReinforcementCalculationResult === void 0 ? void 0 : (_confinementReinforcementCalculationResult_confinementCheck = confinementReinforcementCalculationResult.confinementCheck) === null || _confinementReinforcementCalculationResult_confinementCheck === void 0 ? void 0 : _confinementReinforcementCalculationResult_confinementCheck.nonReinforcedSection) !== null && _confinementReinforcementCalculationResult_confinementCheck_nonReinforcedSection !== void 0 ? _confinementReinforcementCalculationResult_confinementCheck_nonReinforcedSection : {}
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                            lineNumber: 168,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                        lineNumber: 167,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                        value: "reinforcedSection",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$frcm$2d$column$2f$calculations$2f$confinement$2d$reinforced$2d$section$2d$result$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ConfinementReinforcedSectionResultCard"], {
                            reinforcedSectionResults: (_confinementReinforcementCalculationResult_confinementCheck_reinforcedSection = confinementReinforcementCalculationResult === null || confinementReinforcementCalculationResult === void 0 ? void 0 : (_confinementReinforcementCalculationResult_confinementCheck1 = confinementReinforcementCalculationResult.confinementCheck) === null || _confinementReinforcementCalculationResult_confinementCheck1 === void 0 ? void 0 : _confinementReinforcementCalculationResult_confinementCheck1.reinforcedSection) !== null && _confinementReinforcementCalculationResult_confinementCheck_reinforcedSection !== void 0 ? _confinementReinforcementCalculationResult_confinementCheck_reinforcedSection : {}
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                            lineNumber: 176,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                        lineNumber: 175,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx",
                lineNumber: 158,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
}
_s(FrcmColumnConfinementReinforcementCalculationResult, "ibHvjDxkLpek5QWjIfNYywjof5I=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"]
    ];
});
_c = FrcmColumnConfinementReinforcementCalculationResult;
var _c;
__turbopack_context__.k.register(_c, "FrcmColumnConfinementReinforcementCalculationResult");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "FrcmColumnConfinementReinforcementCalculation",
    ()=>FrcmColumnConfinementReinforcementCalculation
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$product$2f$custom$2d$product$2d$section$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/product/custom-product-section.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$product$2f$product$2d$summary$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/product/product-summary-card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/select-form-fixed-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/select-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/separator.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$module$2d$calculation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-module-calculation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$products$2f$use$2d$products$2d$by$2d$category$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/query/products/use-products-by-category.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$frcm$2d$column$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/masonry/frcm-column-form.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$frcm$2d$column$2f$calculations$2f$frcm$2d$column$2d$confinement$2d$reinforcement$2d$calculation$2d$result$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation-result.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const FrcmColumnConfinementReinforcementCalculation = (param)=>{
    let { session, module, projectId } = param;
    var _params_geometry, _params_geometry1, _params_geometry2;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.calculations.frcm-column');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.calculations.frcm-column');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const { params, confinementReinforcementVerifyInput, confinementReinforcementCalculationResult } = module;
    var _confinementReinforcementVerifyInput_reinforcedArrangement, _confinementReinforcementVerifyInput_singleWidthBand, _confinementReinforcementVerifyInput_stepsOfTheBand, _confinementReinforcementVerifyInput_clearDistanceBetweenStripes, _confinementReinforcementVerifyInput_minimalTransversalDimension, _confinementReinforcementVerifyInput_numberOfReinforcementLayers, _confinementReinforcementVerifyInput_matrixThicknessOfTheSingleLayer;
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$frcm$2d$column$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["frcmColumnConfinementReinforcementInput"]),
        defaultValues: {
            calculationType: 'CONFINEMENT_VERIFY',
            input: {
                product: {
                    id: confinementReinforcementVerifyInput === null || confinementReinforcementVerifyInput === void 0 ? void 0 : confinementReinforcementVerifyInput.product.id,
                    name: confinementReinforcementVerifyInput === null || confinementReinforcementVerifyInput === void 0 ? void 0 : confinementReinforcementVerifyInput.product.name,
                    sourceType: (confinementReinforcementVerifyInput === null || confinementReinforcementVerifyInput === void 0 ? void 0 : confinementReinforcementVerifyInput.product.id) === 'custom' ? 'CUSTOM' : 'DATABASE'
                },
                reinforcedArrangement: (_confinementReinforcementVerifyInput_reinforcedArrangement = confinementReinforcementVerifyInput === null || confinementReinforcementVerifyInput === void 0 ? void 0 : confinementReinforcementVerifyInput.reinforcedArrangement) !== null && _confinementReinforcementVerifyInput_reinforcedArrangement !== void 0 ? _confinementReinforcementVerifyInput_reinforcedArrangement : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REINFORCEMENT_ARRANGEMENT"].CONTINUE,
                singleWidthBand: (_confinementReinforcementVerifyInput_singleWidthBand = confinementReinforcementVerifyInput === null || confinementReinforcementVerifyInput === void 0 ? void 0 : confinementReinforcementVerifyInput.singleWidthBand) !== null && _confinementReinforcementVerifyInput_singleWidthBand !== void 0 ? _confinementReinforcementVerifyInput_singleWidthBand : 0,
                stepsOfTheBand: (_confinementReinforcementVerifyInput_stepsOfTheBand = confinementReinforcementVerifyInput === null || confinementReinforcementVerifyInput === void 0 ? void 0 : confinementReinforcementVerifyInput.stepsOfTheBand) !== null && _confinementReinforcementVerifyInput_stepsOfTheBand !== void 0 ? _confinementReinforcementVerifyInput_stepsOfTheBand : 0,
                clearDistanceBetweenStripes: (_confinementReinforcementVerifyInput_clearDistanceBetweenStripes = confinementReinforcementVerifyInput === null || confinementReinforcementVerifyInput === void 0 ? void 0 : confinementReinforcementVerifyInput.clearDistanceBetweenStripes) !== null && _confinementReinforcementVerifyInput_clearDistanceBetweenStripes !== void 0 ? _confinementReinforcementVerifyInput_clearDistanceBetweenStripes : 0,
                minimalTransversalDimension: (_confinementReinforcementVerifyInput_minimalTransversalDimension = confinementReinforcementVerifyInput === null || confinementReinforcementVerifyInput === void 0 ? void 0 : confinementReinforcementVerifyInput.minimalTransversalDimension) !== null && _confinementReinforcementVerifyInput_minimalTransversalDimension !== void 0 ? _confinementReinforcementVerifyInput_minimalTransversalDimension : 0,
                numberOfReinforcementLayers: (_confinementReinforcementVerifyInput_numberOfReinforcementLayers = confinementReinforcementVerifyInput === null || confinementReinforcementVerifyInput === void 0 ? void 0 : confinementReinforcementVerifyInput.numberOfReinforcementLayers) !== null && _confinementReinforcementVerifyInput_numberOfReinforcementLayers !== void 0 ? _confinementReinforcementVerifyInput_numberOfReinforcementLayers : 0,
                matrixThicknessOfTheSingleLayer: (_confinementReinforcementVerifyInput_matrixThicknessOfTheSingleLayer = confinementReinforcementVerifyInput === null || confinementReinforcementVerifyInput === void 0 ? void 0 : confinementReinforcementVerifyInput.matrixThicknessOfTheSingleLayer) !== null && _confinementReinforcementVerifyInput_matrixThicknessOfTheSingleLayer !== void 0 ? _confinementReinforcementVerifyInput_matrixThicknessOfTheSingleLayer : 0
            }
        }
    });
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$module$2d$calculation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useModuleCalculation"])(session.accessToken, {
        onSuccess: {
            "FrcmColumnConfinementReinforcementCalculation.useModuleCalculation": ()=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(tAction('calculate.success'));
            }
        }["FrcmColumnConfinementReinforcementCalculation.useModuleCalculation"],
        onError: {
            "FrcmColumnConfinementReinforcementCalculation.useModuleCalculation": (error)=>{
                console.log('ERROR  ', error);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(tAction('calculate.failure', {
                    error: error.message
                }));
            }
        }["FrcmColumnConfinementReinforcementCalculation.useModuleCalculation"]
    });
    const handleFormSubmit = (body)=>{
        mutate({
            projectId,
            moduleId: module.id,
            body
        });
    };
    const { data: products, isError: errorGettingProducts, isLoading: isLoadingProducts } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$products$2f$use$2d$products$2d$by$2d$category$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useProductsByCategory"])(session, 'FRCM_COLUMN', 0, 100);
    var _products_content_map;
    const productsOptions = [
        ...(_products_content_map = products === null || products === void 0 ? void 0 : products.content.map((m)=>{
            var _m_name;
            return {
                value: m.id,
                label: (_m_name = m.name) !== null && _m_name !== void 0 ? _m_name : t('product.unnamed')
            };
        })) !== null && _products_content_map !== void 0 ? _products_content_map : []
    ];
    const [productId] = form.watch([
        'input.product.id'
    ]);
    const selectedProduct = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "FrcmColumnConfinementReinforcementCalculation.useMemo[selectedProduct]": ()=>products === null || products === void 0 ? void 0 : products.content.find({
                "FrcmColumnConfinementReinforcementCalculation.useMemo[selectedProduct]": (p)=>p.id === productId
            }["FrcmColumnConfinementReinforcementCalculation.useMemo[selectedProduct]"])
    }["FrcmColumnConfinementReinforcementCalculation.useMemo[selectedProduct]"], [
        productId,
        products
    ]);
    var _form_watch;
    const stepsOfTheBandValue = (_form_watch = form.watch('input.stepsOfTheBand')) !== null && _form_watch !== void 0 ? _form_watch : 0;
    var _form_watch1;
    const singleWidthBandValue = (_form_watch1 = form.watch('input.singleWidthBand')) !== null && _form_watch1 !== void 0 ? _form_watch1 : 0;
    var _form_watch2;
    const reinforcedArrangementValue = (_form_watch2 = form.watch('input.reinforcedArrangement')) !== null && _form_watch2 !== void 0 ? _form_watch2 : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REINFORCEMENT_ARRANGEMENT"].CONTINUE;
    var _params_geometry_largerSizeOrColumnDiameter;
    const lengthOrDiameterValue = (_params_geometry_largerSizeOrColumnDiameter = params === null || params === void 0 ? void 0 : (_params_geometry = params.geometry) === null || _params_geometry === void 0 ? void 0 : _params_geometry.largerSizeOrColumnDiameter) !== null && _params_geometry_largerSizeOrColumnDiameter !== void 0 ? _params_geometry_largerSizeOrColumnDiameter : 0;
    var _params_geometry_smallerSize;
    const smallerSize = (_params_geometry_smallerSize = params === null || params === void 0 ? void 0 : (_params_geometry1 = params.geometry) === null || _params_geometry1 === void 0 ? void 0 : _params_geometry1.smallerSize) !== null && _params_geometry_smallerSize !== void 0 ? _params_geometry_smallerSize : 0;
    var _params_geometry_topology;
    const topologyValue = (_params_geometry_topology = params === null || params === void 0 ? void 0 : (_params_geometry2 = params.geometry) === null || _params_geometry2 === void 0 ? void 0 : _params_geometry2.topology) !== null && _params_geometry_topology !== void 0 ? _params_geometry_topology : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FRM_GEOMETRY_TOPOLOGY"].RECTANGULAR;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "FrcmColumnConfinementReinforcementCalculation.useEffect": ()=>{
            const clearDistanceBetweenStripes = reinforcedArrangementValue === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REINFORCEMENT_ARRANGEMENT"].CONTINUE ? 0 : stepsOfTheBandValue - singleWidthBandValue;
            form.setValue('input.clearDistanceBetweenStripes', parseFloat(clearDistanceBetweenStripes.toFixed(2)));
            // =IF(D41="Continuo","-",IF(C8="Rettangolare",MIN(D9,D10),D9))
            const minimalTransversalDimension = reinforcedArrangementValue === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REINFORCEMENT_ARRANGEMENT"].CONTINUE ? 0 : topologyValue === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FRM_GEOMETRY_TOPOLOGY"].RECTANGULAR ? Math.min(lengthOrDiameterValue, smallerSize) : lengthOrDiameterValue;
            form.setValue('input.minimalTransversalDimension', parseFloat(minimalTransversalDimension.toFixed(2)));
            if (!productId) {
                return;
            }
            if (productId === 'custom') {
                form.setValue('input.product.sourceType', 'CUSTOM');
            }
            if (selectedProduct) {
                form.setValue('input.product', {
                    ...selectedProduct,
                    sourceType: 'DATABASE'
                });
            }
        }
    }["FrcmColumnConfinementReinforcementCalculation.useEffect"], [
        form,
        productId,
        selectedProduct,
        stepsOfTheBandValue,
        singleWidthBandValue,
        lengthOrDiameterValue,
        smallerSize,
        topologyValue,
        reinforcedArrangementValue
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col justify-center gap-4",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Form"], {
                ...form,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                    className: "space-y-4 rounded-md border p-4",
                    onSubmit: form.handleSubmit(handleFormSubmit),
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-3xl font-bold",
                            children: t('heading')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation.tsx",
                            lineNumber: 184,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            src: "/assets/masonry_frcm/column/FRCM_COLONNE MURATURA.jpg",
                            alt: "flexural verify",
                            height: 250,
                            width: 500,
                            className: "mx-auto rounded-md object-contain",
                            priority: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation.tsx",
                            lineNumber: 185,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-xl font-bold",
                            children: t('sub-heading')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation.tsx",
                            lineNumber: 193,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormInput"], {
                            control: form.control,
                            name: "input.product.id",
                            options: productsOptions,
                            t: t,
                            loading: isLoadingProducts,
                            requestError: errorGettingProducts,
                            errorMessage: t('products.error')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation.tsx",
                            lineNumber: 194,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        productId === 'custom' && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$product$2f$custom$2d$product$2d$section$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CustomProductSection"], {}, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation.tsx",
                            lineNumber: 203,
                            columnNumber: 38
                        }, ("TURBOPACK compile-time value", void 0)),
                        selectedProduct && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$product$2f$product$2d$summary$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ProductSummaryCard"], {
                            product: selectedProduct
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation.tsx",
                            lineNumber: 204,
                            columnNumber: 31
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation.tsx",
                            lineNumber: 205,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                            control: form.control,
                            name: "input.reinforcedArrangement",
                            options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_REINFORCEMENT_ARRANGEMENT"],
                            optionLabelFn: (p)=>t("input.reinforcedArrangement.".concat(p)),
                            t: t
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation.tsx",
                            lineNumber: 206,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.singleWidthBand",
                            t: t
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation.tsx",
                            lineNumber: 213,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.stepsOfTheBand",
                            t: t
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation.tsx",
                            lineNumber: 218,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.clearDistanceBetweenStripes",
                            t: t,
                            disabled: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation.tsx",
                            lineNumber: 223,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.minimalTransversalDimension",
                            t: t,
                            disabled: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation.tsx",
                            lineNumber: 229,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.numberOfReinforcementLayers",
                            t: t
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation.tsx",
                            lineNumber: 235,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.matrixThicknessOfTheSingleLayer",
                            t: t
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation.tsx",
                            lineNumber: 240,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                            type: "submit",
                            className: "w-full sm:w-auto",
                            disabled: isPending,
                            children: [
                                isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                                    className: "animate-spin"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation.tsx",
                                    lineNumber: 250,
                                    columnNumber: 27
                                }, ("TURBOPACK compile-time value", void 0)),
                                tCommon('calculate')
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation.tsx",
                            lineNumber: 245,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation.tsx",
                    lineNumber: 180,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation.tsx",
                lineNumber: 179,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            confinementReinforcementCalculationResult && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$frcm$2d$column$2f$calculations$2f$frcm$2d$column$2d$confinement$2d$reinforcement$2d$calculation$2d$result$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FrcmColumnConfinementReinforcementCalculationResult"], {
                confinementReinforcementCalculationResult: confinementReinforcementCalculationResult
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation.tsx",
                lineNumber: 256,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation.tsx",
        lineNumber: 178,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(FrcmColumnConfinementReinforcementCalculation, "PASt7Pj3XMYeJU6OPHzcN/Djfww=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$module$2d$calculation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useModuleCalculation"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$products$2f$use$2d$products$2d$by$2d$category$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useProductsByCategory"]
    ];
});
_c = FrcmColumnConfinementReinforcementCalculation;
var _c;
__turbopack_context__.k.register(_c, "FrcmColumnConfinementReinforcementCalculation");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-geometry-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "FrcmColumnGeometryForm",
    ()=>FrcmColumnGeometryForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/select-form-fixed-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-save-module-params-mutation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$frcm$2d$column$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/masonry/frcm-column-form.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const FrcmColumnGeometryForm = (param)=>{
    let { session, projectId, moduleId, defaultValues, params, setParams, onSave } = param;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.frcm-column.geometry');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.save-project-params.messages');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    var _defaultValues_topology, _defaultValues_largerSizeOrColumnDiameter, _defaultValues_smallerSize, _defaultValues_crossSectionArea, _defaultValues_crossSectionDiagonal, _defaultValues_cornerRoundingRadius;
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$frcm$2d$column$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["frcmColumnGeometrySchema"]),
        defaultValues: {
            topology: (_defaultValues_topology = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.topology) !== null && _defaultValues_topology !== void 0 ? _defaultValues_topology : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FRM_GEOMETRY_TOPOLOGY"].RECTANGULAR,
            largerSizeOrColumnDiameter: (_defaultValues_largerSizeOrColumnDiameter = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.largerSizeOrColumnDiameter) !== null && _defaultValues_largerSizeOrColumnDiameter !== void 0 ? _defaultValues_largerSizeOrColumnDiameter : 0,
            smallerSize: (_defaultValues_smallerSize = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.smallerSize) !== null && _defaultValues_smallerSize !== void 0 ? _defaultValues_smallerSize : 0,
            crossSectionArea: (_defaultValues_crossSectionArea = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.crossSectionArea) !== null && _defaultValues_crossSectionArea !== void 0 ? _defaultValues_crossSectionArea : 0,
            crossSectionDiagonal: (_defaultValues_crossSectionDiagonal = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.crossSectionDiagonal) !== null && _defaultValues_crossSectionDiagonal !== void 0 ? _defaultValues_crossSectionDiagonal : 0,
            cornerRoundingRadius: (_defaultValues_cornerRoundingRadius = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.cornerRoundingRadius) !== null && _defaultValues_cornerRoundingRadius !== void 0 ? _defaultValues_cornerRoundingRadius : 0
        }
    });
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"])(session.accessToken, {
        onSuccess: {
            "FrcmColumnGeometryForm.useSaveModuleParamsMutation": async ()=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(tAction('edit.success'));
                await queryClient.invalidateQueries({
                    queryKey: [
                        'modules',
                        {
                            projectId,
                            moduleId
                        }
                    ]
                });
                onSave();
            }
        }["FrcmColumnGeometryForm.useSaveModuleParamsMutation"],
        onError: {
            "FrcmColumnGeometryForm.useSaveModuleParamsMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(tAction('edit.failure', {
                    error: error.message
                }));
            }
        }["FrcmColumnGeometryForm.useSaveModuleParamsMutation"]
    });
    const handleFormSubmit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FrcmColumnGeometryForm.useCallback[handleFormSubmit]": (body)=>{
            // construct body based on global schema:
            const frcmColumnParams = {
                ...params,
                geometry: body
            };
            mutate({
                projectId,
                moduleId,
                body: frcmColumnParams
            });
        }
    }["FrcmColumnGeometryForm.useCallback[handleFormSubmit]"], [
        mutate,
        projectId,
        moduleId,
        params
    ]);
    const topologyValue = form.watch('topology');
    const smallerSizeValue = form.watch('smallerSize');
    const largerSizeOrColumnDiameterValue = form.watch('largerSizeOrColumnDiameter');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "FrcmColumnGeometryForm.useEffect": ()=>{
            const smallerSize = topologyValue === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FRM_GEOMETRY_TOPOLOGY"].CIRCULAR ? 0 : smallerSizeValue;
            form.setValue('smallerSize', parseFloat(smallerSize.toFixed(2)));
            const crossSectionArea = topologyValue === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FRM_GEOMETRY_TOPOLOGY"].CIRCULAR ? Math.PI * (largerSizeOrColumnDiameterValue / 2) ** 2 : largerSizeOrColumnDiameterValue * smallerSizeValue;
            form.setValue('crossSectionArea', parseFloat(crossSectionArea.toFixed(2)));
            const crossSectionDiagonal = topologyValue === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FRM_GEOMETRY_TOPOLOGY"].CIRCULAR ? 0 : Math.sqrt(largerSizeOrColumnDiameterValue ** 2 + smallerSizeValue ** 2);
            form.setValue('crossSectionDiagonal', parseFloat(crossSectionDiagonal.toFixed(2)));
        }
    }["FrcmColumnGeometryForm.useEffect"], [
        topologyValue,
        smallerSizeValue,
        largerSizeOrColumnDiameterValue,
        form
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "FrcmColumnGeometryForm.useEffect": ()=>{
            const subscription = form.watch({
                "FrcmColumnGeometryForm.useEffect.subscription": (values)=>{
                    setParams({
                        "FrcmColumnGeometryForm.useEffect.subscription": (prev)=>({
                                ...prev,
                                buildingCharacteristics: values
                            })
                    }["FrcmColumnGeometryForm.useEffect.subscription"]);
                }
            }["FrcmColumnGeometryForm.useEffect.subscription"]);
            return ({
                "FrcmColumnGeometryForm.useEffect": ()=>subscription.unsubscribe()
            })["FrcmColumnGeometryForm.useEffect"];
        }
    }["FrcmColumnGeometryForm.useEffect"], [
        form,
        setParams
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Form"], {
        ...form,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            className: "space-y-4 rounded-md border p-4",
            onSubmit: form.handleSubmit(handleFormSubmit),
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    src: "/assets/masonry_frcm/FRCM_Muratura.jpg",
                    alt: "site characteristics",
                    height: 250,
                    width: 500,
                    className: "mx-auto rounded-md object-contain",
                    priority: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-geometry-form.tsx",
                    lineNumber: 137,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "topology",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_FRM_GEOMETRY_TOPOLOGY"],
                    optionLabelFn: (p)=>t("topology.".concat(p)),
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-geometry-form.tsx",
                    lineNumber: 145,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "largerSizeOrColumnDiameter",
                    t: (msg)=>t("".concat(topologyValue, ".").concat(msg)),
                    disabled: topologyValue === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FRM_GEOMETRY_TOPOLOGY"].CIRCULAR
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-geometry-form.tsx",
                    lineNumber: 152,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "smallerSize",
                    t: (msg)=>t("".concat(topologyValue, ".").concat(msg)),
                    disabled: topologyValue === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FRM_GEOMETRY_TOPOLOGY"].CIRCULAR
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-geometry-form.tsx",
                    lineNumber: 158,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "crossSectionArea",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-geometry-form.tsx",
                    lineNumber: 165,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "crossSectionDiagonal",
                    t: (msg)=>t("".concat(topologyValue, ".").concat(msg)),
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-geometry-form.tsx",
                    lineNumber: 171,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "cornerRoundingRadius",
                    t: (msg)=>t("".concat(topologyValue, ".").concat(msg))
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-geometry-form.tsx",
                    lineNumber: 177,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    type: "submit",
                    className: "w-full sm:w-auto",
                    disabled: isPending,
                    onClick: form.handleSubmit(handleFormSubmit),
                    children: [
                        isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                            className: "animate-spin"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-geometry-form.tsx",
                            lineNumber: 188,
                            columnNumber: 25
                        }, ("TURBOPACK compile-time value", void 0)),
                        tCommon('save')
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-geometry-form.tsx",
                    lineNumber: 182,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-geometry-form.tsx",
            lineNumber: 133,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-geometry-form.tsx",
        lineNumber: 132,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(FrcmColumnGeometryForm, "q8qy4WSiQgq59By9bYU1Gd3awn8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"]
    ];
});
_c = FrcmColumnGeometryForm;
var _c;
__turbopack_context__.k.register(_c, "FrcmColumnGeometryForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-masonry-characteristics-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "FrcmColumnMasonryCharacteristicsForm",
    ()=>FrcmColumnMasonryCharacteristicsForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/select-form-fixed-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-save-module-params-mutation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$frcm$2d$column$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/masonry/frcm-column-form.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const FrcmColumnMasonryCharacteristicsForm = (param)=>{
    let { session, projectId, moduleId, defaultValues, params, setParams, onSave } = param;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.frcm-column.masonry-characteristics');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.save-project-params.messages');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    var _defaultValues_material, _defaultValues_enhancementCharacteristics, _defaultValues_knowledgeLevel, _defaultValues_confidenceFactor, _defaultValues_executionClass, _defaultValues_loadResistantCategory, _defaultValues_masonrySafetyFactor, _defaultValues_characteristicCompressiveStrength, _defaultValues_designCompressiveStrength, _defaultValues_characteristicShearStrength, _defaultValues_designShearStrength, _defaultValues_normalElasticityModulus, _defaultValues_shearElasticityModulus, _defaultValues_masonryDensity, _defaultValues_exposureType, _defaultValues_conversionFactor;
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$frcm$2d$column$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["frcmColumnMasonryCharacteristicsSchema"]),
        defaultValues: {
            material: (_defaultValues_material = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.material) !== null && _defaultValues_material !== void 0 ? _defaultValues_material : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FACING_MATERIAL"].BRICK,
            enhancementCharacteristics: (_defaultValues_enhancementCharacteristics = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.enhancementCharacteristics) !== null && _defaultValues_enhancementCharacteristics !== void 0 ? _defaultValues_enhancementCharacteristics : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CRM_MASONRY_TYPE"].MURATURA_IN_PIETRAME_DISORDINATA,
            knowledgeLevel: (_defaultValues_knowledgeLevel = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.knowledgeLevel) !== null && _defaultValues_knowledgeLevel !== void 0 ? _defaultValues_knowledgeLevel : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleMaterialKnowledgeLevel"].LC1,
            confidenceFactor: (_defaultValues_confidenceFactor = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.confidenceFactor) !== null && _defaultValues_confidenceFactor !== void 0 ? _defaultValues_confidenceFactor : 0,
            executionClass: (_defaultValues_executionClass = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.executionClass) !== null && _defaultValues_executionClass !== void 0 ? _defaultValues_executionClass : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["executionClass"].ONE,
            loadResistantCategory: (_defaultValues_loadResistantCategory = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.loadResistantCategory) !== null && _defaultValues_loadResistantCategory !== void 0 ? _defaultValues_loadResistantCategory : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["loadResistingCategory"].MASONRY_CAT_I_AND_MORTAR_WITH_GUARANTEED_PERFORMANCE,
            masonrySafetyFactor: (_defaultValues_masonrySafetyFactor = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.masonrySafetyFactor) !== null && _defaultValues_masonrySafetyFactor !== void 0 ? _defaultValues_masonrySafetyFactor : 0,
            characteristicCompressiveStrength: (_defaultValues_characteristicCompressiveStrength = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.characteristicCompressiveStrength) !== null && _defaultValues_characteristicCompressiveStrength !== void 0 ? _defaultValues_characteristicCompressiveStrength : 0,
            designCompressiveStrength: (_defaultValues_designCompressiveStrength = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.designCompressiveStrength) !== null && _defaultValues_designCompressiveStrength !== void 0 ? _defaultValues_designCompressiveStrength : 0,
            characteristicShearStrength: (_defaultValues_characteristicShearStrength = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.characteristicShearStrength) !== null && _defaultValues_characteristicShearStrength !== void 0 ? _defaultValues_characteristicShearStrength : 0,
            designShearStrength: (_defaultValues_designShearStrength = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.designShearStrength) !== null && _defaultValues_designShearStrength !== void 0 ? _defaultValues_designShearStrength : 0,
            normalElasticityModulus: (_defaultValues_normalElasticityModulus = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.normalElasticityModulus) !== null && _defaultValues_normalElasticityModulus !== void 0 ? _defaultValues_normalElasticityModulus : 0,
            shearElasticityModulus: (_defaultValues_shearElasticityModulus = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.shearElasticityModulus) !== null && _defaultValues_shearElasticityModulus !== void 0 ? _defaultValues_shearElasticityModulus : 0,
            masonryDensity: (_defaultValues_masonryDensity = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.masonryDensity) !== null && _defaultValues_masonryDensity !== void 0 ? _defaultValues_masonryDensity : 0,
            exposureType: (_defaultValues_exposureType = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.exposureType) !== null && _defaultValues_exposureType !== void 0 ? _defaultValues_exposureType : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleGeometryExposure"].INTERNAL,
            conversionFactor: (_defaultValues_conversionFactor = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.conversionFactor) !== null && _defaultValues_conversionFactor !== void 0 ? _defaultValues_conversionFactor : 0
        }
    });
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"])(session.accessToken, {
        onSuccess: {
            "FrcmColumnMasonryCharacteristicsForm.useSaveModuleParamsMutation": async ()=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(tAction('edit.success'));
                await queryClient.invalidateQueries({
                    queryKey: [
                        'modules',
                        {
                            projectId,
                            moduleId
                        }
                    ]
                });
                onSave();
            }
        }["FrcmColumnMasonryCharacteristicsForm.useSaveModuleParamsMutation"],
        onError: {
            "FrcmColumnMasonryCharacteristicsForm.useSaveModuleParamsMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(tAction('edit.failure', {
                    error: error.message
                }));
            }
        }["FrcmColumnMasonryCharacteristicsForm.useSaveModuleParamsMutation"]
    });
    const handleFormSubmit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FrcmColumnMasonryCharacteristicsForm.useCallback[handleFormSubmit]": (body)=>{
            // construct body based on global schema:
            const frcmColumnParams = {
                ...params,
                masonryCharacteristics: body
            };
            mutate({
                projectId,
                moduleId,
                body: frcmColumnParams
            });
        }
    }["FrcmColumnMasonryCharacteristicsForm.useCallback[handleFormSubmit]"], [
        mutate,
        projectId,
        moduleId,
        params
    ]);
    var _form_watch;
    const knowledgeMaterialLevel = (_form_watch = form.watch('knowledgeLevel')) !== null && _form_watch !== void 0 ? _form_watch : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleMaterialKnowledgeLevel"].LC1;
    var _form_watch1;
    const executionClassFormValue = (_form_watch1 = form.watch('executionClass')) !== null && _form_watch1 !== void 0 ? _form_watch1 : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["executionClass"].ONE;
    var _form_watch2;
    const loadResistingCategoryFormValue = (_form_watch2 = form.watch('loadResistantCategory')) !== null && _form_watch2 !== void 0 ? _form_watch2 : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["loadResistingCategory"].MASONRY_CAT_I_AND_MORTAR_WITH_GUARANTEED_PERFORMANCE;
    var _form_watch3;
    const enhancementCharacteristics = (_form_watch3 = form.watch('enhancementCharacteristics')) !== null && _form_watch3 !== void 0 ? _form_watch3 : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CRM_MASONRY_TYPE"].MURATURA_IN_PIETRAME_DISORDINATA;
    var _form_watch4;
    const exposureTypeValue = (_form_watch4 = form.watch('exposureType')) !== null && _form_watch4 !== void 0 ? _form_watch4 : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleGeometryExposure"].INTERNAL;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "FrcmColumnMasonryCharacteristicsForm.useEffect": ()=>{
            const calculateGivenStrength = {
                "FrcmColumnMasonryCharacteristicsForm.useEffect.calculateGivenStrength": (strengthMinMax)=>{
                    return knowledgeMaterialLevel === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleMaterialKnowledgeLevel"].LC1 ? strengthMinMax.min : knowledgeMaterialLevel === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleMaterialKnowledgeLevel"].LC3 ? strengthMinMax.max : (strengthMinMax.min + strengthMinMax.max) / 2;
                }
            }["FrcmColumnMasonryCharacteristicsForm.useEffect.calculateGivenStrength"];
            const confidenceFactor = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleMaterialKnowledgeLevelValues"][knowledgeMaterialLevel];
            form.setValue('confidenceFactor', confidenceFactor);
            const masonryStrengthSafetyFactor = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["masonryStrengthSafetyFactorMapping"][loadResistingCategoryFormValue][executionClassFormValue];
            form.setValue('masonrySafetyFactor', masonryStrengthSafetyFactor);
            const characteristicCompressiveStrengthMinMax = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["characteristicCompressiveStrengthValues"][enhancementCharacteristics];
            const characteristicCompressiveStrength = calculateGivenStrength(characteristicCompressiveStrengthMinMax);
            form.setValue('characteristicCompressiveStrength', characteristicCompressiveStrength);
            const designCompressiveStrength = characteristicCompressiveStrength / masonryStrengthSafetyFactor / confidenceFactor;
            form.setValue('designCompressiveStrength', designCompressiveStrength);
            const characteristicsShearStrengthMinMax = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["characteristicShearStrengthValues"][enhancementCharacteristics];
            const characteristicShearStrength = calculateGivenStrength(characteristicsShearStrengthMinMax);
            form.setValue('characteristicShearStrength', characteristicShearStrength);
            const designShearStrength = characteristicShearStrength / confidenceFactor / masonryStrengthSafetyFactor;
            form.setValue('designShearStrength', designShearStrength);
            const normalElasticityModulusMinMax = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["characteristicNormalElasticityModulusValues"][enhancementCharacteristics];
            const normalElasticityModulus = (normalElasticityModulusMinMax.min + normalElasticityModulusMinMax.max) / 2;
            form.setValue('normalElasticityModulus', normalElasticityModulus);
            const shearElasticityModulusMinMax = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["characteristicShearElasticityModulusValues"][enhancementCharacteristics];
            const shearElasticityModulus = (shearElasticityModulusMinMax.min + shearElasticityModulusMinMax.max) / 2;
            form.setValue('shearElasticityModulus', shearElasticityModulus);
            const masonryDensity = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["masonryDensityValues"][enhancementCharacteristics];
            form.setValue('masonryDensity', masonryDensity);
            const conversionFactor = exposureTypeValue === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleGeometryExposure"].INTERNAL ? 0.9 : exposureTypeValue === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleGeometryExposure"].EXTERNAL ? 0.8 : 0.7;
            form.setValue('conversionFactor', conversionFactor);
        }
    }["FrcmColumnMasonryCharacteristicsForm.useEffect"], [
        form,
        knowledgeMaterialLevel,
        executionClassFormValue,
        loadResistingCategoryFormValue,
        enhancementCharacteristics,
        exposureTypeValue
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "FrcmColumnMasonryCharacteristicsForm.useEffect": ()=>{
            const subscription = form.watch({
                "FrcmColumnMasonryCharacteristicsForm.useEffect.subscription": (values)=>{
                    setParams({
                        "FrcmColumnMasonryCharacteristicsForm.useEffect.subscription": (prev)=>({
                                ...prev,
                                buildingCharacteristics: values
                            })
                    }["FrcmColumnMasonryCharacteristicsForm.useEffect.subscription"]);
                }
            }["FrcmColumnMasonryCharacteristicsForm.useEffect.subscription"]);
            return ({
                "FrcmColumnMasonryCharacteristicsForm.useEffect": ()=>subscription.unsubscribe()
            })["FrcmColumnMasonryCharacteristicsForm.useEffect"];
        }
    }["FrcmColumnMasonryCharacteristicsForm.useEffect"], [
        form,
        setParams
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Form"], {
        ...form,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            className: "space-y-4 rounded-md border p-4",
            onSubmit: form.handleSubmit(handleFormSubmit),
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "material",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_FACING_MATERIAL"],
                    optionLabelFn: (p)=>t("material.".concat(p)),
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-masonry-characteristics-form.tsx",
                    lineNumber: 240,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "enhancementCharacteristics",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_CRM_MASONRY_TYPE"],
                    optionLabelFn: (p)=>t("enhancementCharacteristics.".concat(p)),
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-masonry-characteristics-form.tsx",
                    lineNumber: 247,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "knowledgeLevel",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_MATERIAL_KNOWLEDGE_LEVEL"],
                    optionLabelFn: (p)=>t("knowledgeLevel.".concat(p)),
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-masonry-characteristics-form.tsx",
                    lineNumber: 254,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "confidenceFactor",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-masonry-characteristics-form.tsx",
                    lineNumber: 261,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "executionClass",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_EXECUTION_CLASS"],
                    optionLabelFn: (p)=>t("executionClass.".concat(p)),
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-masonry-characteristics-form.tsx",
                    lineNumber: 267,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "loadResistantCategory",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_LOAD_RESISTING_CATEGORY"],
                    optionLabelFn: (p)=>t("loadResistantCategory.".concat(p)),
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-masonry-characteristics-form.tsx",
                    lineNumber: 274,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "masonrySafetyFactor",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-masonry-characteristics-form.tsx",
                    lineNumber: 281,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "characteristicCompressiveStrength",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-masonry-characteristics-form.tsx",
                    lineNumber: 287,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "designCompressiveStrength",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-masonry-characteristics-form.tsx",
                    lineNumber: 293,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "characteristicShearStrength",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-masonry-characteristics-form.tsx",
                    lineNumber: 299,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "designShearStrength",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-masonry-characteristics-form.tsx",
                    lineNumber: 305,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "normalElasticityModulus",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-masonry-characteristics-form.tsx",
                    lineNumber: 311,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "shearElasticityModulus",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-masonry-characteristics-form.tsx",
                    lineNumber: 317,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "masonryDensity",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-masonry-characteristics-form.tsx",
                    lineNumber: 323,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "exposureType",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_GEOMETRY_EXPOSURE"],
                    optionLabelFn: (p)=>t("exposureType.".concat(p)),
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-masonry-characteristics-form.tsx",
                    lineNumber: 329,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "conversionFactor",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-masonry-characteristics-form.tsx",
                    lineNumber: 336,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    type: "submit",
                    className: "w-full sm:w-auto",
                    disabled: isPending,
                    onClick: form.handleSubmit(handleFormSubmit),
                    children: [
                        isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                            className: "animate-spin"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-masonry-characteristics-form.tsx",
                            lineNumber: 348,
                            columnNumber: 25
                        }, ("TURBOPACK compile-time value", void 0)),
                        tCommon('save')
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-masonry-characteristics-form.tsx",
                    lineNumber: 342,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-masonry-characteristics-form.tsx",
            lineNumber: 236,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-masonry-characteristics-form.tsx",
        lineNumber: 235,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(FrcmColumnMasonryCharacteristicsForm, "q8qy4WSiQgq59By9bYU1Gd3awn8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"]
    ];
});
_c = FrcmColumnMasonryCharacteristicsForm;
var _c;
__turbopack_context__.k.register(_c, "FrcmColumnMasonryCharacteristicsForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-stress-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "FrcmColumnStressForm",
    ()=>FrcmColumnStressForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-save-module-params-mutation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$frcm$2d$column$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/masonry/frcm-column-form.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
const FrcmColumnStressForm = (param)=>{
    let { session, projectId, moduleId, defaultValues, params, setParams, onSave } = param;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.frcm-column.stress');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.save-project-params.messages');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    var _defaultValues_normalStressCenteredStressing;
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$frcm$2d$column$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["frcmColumnStressSchema"]),
        defaultValues: {
            normalStressCenteredStressing: (_defaultValues_normalStressCenteredStressing = defaultValues === null || defaultValues === void 0 ? void 0 : defaultValues.normalStressCenteredStressing) !== null && _defaultValues_normalStressCenteredStressing !== void 0 ? _defaultValues_normalStressCenteredStressing : 0
        }
    });
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"])(session.accessToken, {
        onSuccess: {
            "FrcmColumnStressForm.useSaveModuleParamsMutation": async ()=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(tAction('edit.success'));
                await queryClient.invalidateQueries({
                    queryKey: [
                        'modules',
                        {
                            projectId,
                            moduleId
                        }
                    ]
                });
                onSave();
            }
        }["FrcmColumnStressForm.useSaveModuleParamsMutation"],
        onError: {
            "FrcmColumnStressForm.useSaveModuleParamsMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(tAction('edit.failure', {
                    error: error.message
                }));
            }
        }["FrcmColumnStressForm.useSaveModuleParamsMutation"]
    });
    const handleFormSubmit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FrcmColumnStressForm.useCallback[handleFormSubmit]": (body)=>{
            // construct body based on global schema:
            const frcmColumnParams = {
                ...params,
                stress: body
            };
            mutate({
                projectId,
                moduleId,
                body: frcmColumnParams
            });
        }
    }["FrcmColumnStressForm.useCallback[handleFormSubmit]"], [
        mutate,
        projectId,
        moduleId,
        params
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "FrcmColumnStressForm.useEffect": ()=>{
            const subscription = form.watch({
                "FrcmColumnStressForm.useEffect.subscription": (values)=>{
                    setParams({
                        "FrcmColumnStressForm.useEffect.subscription": (prev)=>({
                                ...prev,
                                buildingCharacteristics: values
                            })
                    }["FrcmColumnStressForm.useEffect.subscription"]);
                }
            }["FrcmColumnStressForm.useEffect.subscription"]);
            return ({
                "FrcmColumnStressForm.useEffect": ()=>subscription.unsubscribe()
            })["FrcmColumnStressForm.useEffect"];
        }
    }["FrcmColumnStressForm.useEffect"], [
        form,
        setParams
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Form"], {
        ...form,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            className: "space-y-4 rounded-md border p-4",
            onSubmit: form.handleSubmit(handleFormSubmit),
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "normalStressCenteredStressing",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-stress-form.tsx",
                    lineNumber: 97,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    type: "submit",
                    className: "w-full sm:w-auto",
                    disabled: isPending,
                    onClick: form.handleSubmit(handleFormSubmit),
                    children: [
                        isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                            className: "animate-spin"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-stress-form.tsx",
                            lineNumber: 108,
                            columnNumber: 25
                        }, ("TURBOPACK compile-time value", void 0)),
                        tCommon('save')
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-stress-form.tsx",
                    lineNumber: 102,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-stress-form.tsx",
            lineNumber: 93,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-stress-form.tsx",
        lineNumber: 92,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(FrcmColumnStressForm, "0GvcWzWtWEWdQUcgL4k4yYfwxkU=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"]
    ];
});
_c = FrcmColumnStressForm;
var _c;
__turbopack_context__.k.register(_c, "FrcmColumnStressForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/frcm-column-params-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "FrcmColumnParamsForm",
    ()=>FrcmColumnParamsForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/accordion.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$frcm$2d$column$2f$calculations$2f$frcm$2d$column$2d$confinement$2d$reinforcement$2d$calculation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/calculations/frcm-column-confinement-reinforcement-calculation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$frcm$2d$column$2f$sections$2f$frcm$2d$column$2d$geometry$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-geometry-form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$frcm$2d$column$2f$sections$2f$frcm$2d$column$2d$masonry$2d$characteristics$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-masonry-characteristics-form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$frcm$2d$column$2f$sections$2f$frcm$2d$column$2d$stress$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/sections/frcm-column-stress-form.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
const FrcmColumnParamsForm = (param)=>{
    let { session, projectId, moduleId, module } = param;
    var _module_params, _module_params1, _module_params2;
    _s();
    var _module_params3;
    const [params, setParams] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])((_module_params3 = module === null || module === void 0 ? void 0 : module.params) !== null && _module_params3 !== void 0 ? _module_params3 : {});
    const [openItems, setOpenItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([
        '0'
    ]);
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.frcm-column');
    const handleItemSaved = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "FrcmColumnParamsForm.useCallback[handleItemSaved]": (id)=>{
            const nextId = String(Number(id) + 1);
            setOpenItems({
                "FrcmColumnParamsForm.useCallback[handleItemSaved]": (old)=>{
                    const temp = old.filter({
                        "FrcmColumnParamsForm.useCallback[handleItemSaved].temp": (v)=>v !== id
                    }["FrcmColumnParamsForm.useCallback[handleItemSaved].temp"]);
                    return old.includes(nextId) ? temp : [
                        ...temp,
                        nextId
                    ];
                }
            }["FrcmColumnParamsForm.useCallback[handleItemSaved]"]);
        }
    }["FrcmColumnParamsForm.useCallback[handleItemSaved]"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Accordion"], {
            type: "multiple",
            value: openItems,
            onValueChange: setOpenItems,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionItem"], {
                    value: "0",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-medium",
                                children: t('geometry.title')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/frcm-column-params-form.tsx",
                                lineNumber: 58,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/frcm-column-params-form.tsx",
                            lineNumber: 57,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionContent"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$frcm$2d$column$2f$sections$2f$frcm$2d$column$2d$geometry$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FrcmColumnGeometryForm"], {
                                session: session,
                                projectId: projectId,
                                moduleId: moduleId,
                                defaultValues: params === null || params === void 0 ? void 0 : params.geometry,
                                setParams: setParams,
                                params: params,
                                onSave: ()=>handleItemSaved('0')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/frcm-column-params-form.tsx",
                                lineNumber: 61,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/frcm-column-params-form.tsx",
                            lineNumber: 60,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/frcm-column-params-form.tsx",
                    lineNumber: 56,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionItem"], {
                    value: "1",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-medium",
                                children: t('masonry-characteristics.title')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/frcm-column-params-form.tsx",
                                lineNumber: 74,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/frcm-column-params-form.tsx",
                            lineNumber: 73,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionContent"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$frcm$2d$column$2f$sections$2f$frcm$2d$column$2d$masonry$2d$characteristics$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FrcmColumnMasonryCharacteristicsForm"], {
                                session: session,
                                projectId: projectId,
                                moduleId: moduleId,
                                defaultValues: params === null || params === void 0 ? void 0 : params.masonryCharacteristics,
                                setParams: setParams,
                                params: params,
                                onSave: ()=>handleItemSaved('1')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/frcm-column-params-form.tsx",
                                lineNumber: 79,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/frcm-column-params-form.tsx",
                            lineNumber: 78,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/frcm-column-params-form.tsx",
                    lineNumber: 72,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionItem"], {
                    value: "2",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-medium",
                                children: t('stress.title')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/frcm-column-params-form.tsx",
                                lineNumber: 92,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/frcm-column-params-form.tsx",
                            lineNumber: 91,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionContent"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$frcm$2d$column$2f$sections$2f$frcm$2d$column$2d$stress$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FrcmColumnStressForm"], {
                                session: session,
                                projectId: projectId,
                                moduleId: moduleId,
                                defaultValues: params.stress,
                                params: params,
                                setParams: setParams,
                                onSave: ()=>handleItemSaved('2')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/frcm-column-params-form.tsx",
                                lineNumber: 95,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/frcm-column-params-form.tsx",
                            lineNumber: 94,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/frcm-column-params-form.tsx",
                    lineNumber: 90,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionItem"], {
                    value: "3",
                    disabled: !(module === null || module === void 0 ? void 0 : (_module_params = module.params) === null || _module_params === void 0 ? void 0 : _module_params.geometry) || !(module === null || module === void 0 ? void 0 : (_module_params1 = module.params) === null || _module_params1 === void 0 ? void 0 : _module_params1.masonryCharacteristics) || !(module === null || module === void 0 ? void 0 : (_module_params2 = module.params) === null || _module_params2 === void 0 ? void 0 : _module_params2.stress),
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-medium",
                                children: t('confinement-reinforcement.title')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/frcm-column-params-form.tsx",
                                lineNumber: 115,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/frcm-column-params-form.tsx",
                            lineNumber: 114,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionContent"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$frcm$2d$column$2f$calculations$2f$frcm$2d$column$2d$confinement$2d$reinforcement$2d$calculation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["FrcmColumnConfinementReinforcementCalculation"], {
                                session: session,
                                projectId: projectId,
                                module: module
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/frcm-column-params-form.tsx",
                                lineNumber: 120,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/frcm-column-params-form.tsx",
                            lineNumber: 119,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/frcm-column-params-form.tsx",
                    lineNumber: 106,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/frcm-column-params-form.tsx",
            lineNumber: 51,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/frcm-column/frcm-column-params-form.tsx",
        lineNumber: 50,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(FrcmColumnParamsForm, "V5k61vZDgCHqNQ+jf9H1PSOyQy0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"]
    ];
});
_c = FrcmColumnParamsForm;
var _c;
__turbopack_context__.k.register(_c, "FrcmColumnParamsForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
]);

//# sourceMappingURL=b1a17_components_common_atlas_module-detail_params-forms_masonry_frcm-column_f1974832._.js.map