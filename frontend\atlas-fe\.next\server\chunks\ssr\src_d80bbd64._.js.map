{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/header-skeleton.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { Skeleton } from '@atlas/components/ui/skeleton' // For loading state\r\n\r\nexport const HeaderSkeleton = () => (\r\n  <div className=\"mb-6 border-b pb-4\">\r\n    <Skeleton className=\"mb-2 h-8 w-3/4 md:w-1/2\" />\r\n    <div className=\"flex flex-wrap items-center gap-x-4 gap-y-1\">\r\n      <Skeleton className=\"h-5 w-20\" />\r\n      <Skeleton className=\"h-5 w-24\" />\r\n      <Skeleton className=\"h-5 w-32\" />\r\n      <Skeleton className=\"h-5 w-40\" />\r\n      <Skeleton className=\"h-5 w-28\" />\r\n    </div>\r\n  </div>\r\n)\r\n"], "names": [], "mappings": ";;;;;AAEA,uOAAyD,oBAAoB;AAF7E;;;AAIO,MAAM,iBAAiB,kBAC5B,8OAAC;QAAI,WAAU;;0BACb,8OAAC,gJAAQ;gBAAC,WAAU;;;;;;0BACpB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gJAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC,gJAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC,gJAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC,gJAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC,gJAAQ;wBAAC,WAAU", "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/select.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { cn } from '@atlas/lib/utils'\r\nimport * as SelectPrimitive from '@radix-ui/react-select'\r\nimport { CheckIcon, ChevronDownIcon, ChevronUpIcon } from 'lucide-react'\r\nimport type * as React from 'react'\r\n\r\nfunction Select({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\r\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\r\n}\r\n\r\nfunction SelectGroup({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\r\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\r\n}\r\n\r\nfunction SelectValue({\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\r\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\r\n}\r\n\r\nfunction SelectTrigger({\r\n  className,\r\n  size = 'default',\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\r\n  size?: 'sm' | 'default'\r\n}) {\r\n  return (\r\n    <SelectPrimitive.Trigger\r\n      data-slot=\"select-trigger\"\r\n      data-size={size}\r\n      className={cn(\r\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      {children}\r\n      <SelectPrimitive.Icon asChild>\r\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\r\n      </SelectPrimitive.Icon>\r\n    </SelectPrimitive.Trigger>\r\n  )\r\n}\r\n\r\nfunction SelectContent({\r\n  className,\r\n  children,\r\n  position = 'popper',\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\r\n  return (\r\n    <SelectPrimitive.Portal>\r\n      <SelectPrimitive.Content\r\n        data-slot=\"select-content\"\r\n        className={cn(\r\n          'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md',\r\n          position === 'popper' &&\r\n            'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',\r\n          className,\r\n        )}\r\n        position={position}\r\n        {...props}\r\n      >\r\n        <SelectScrollUpButton />\r\n        <SelectPrimitive.Viewport\r\n          className={cn(\r\n            'p-1',\r\n            position === 'popper' &&\r\n              'h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1',\r\n          )}\r\n        >\r\n          {children}\r\n        </SelectPrimitive.Viewport>\r\n        <SelectScrollDownButton />\r\n      </SelectPrimitive.Content>\r\n    </SelectPrimitive.Portal>\r\n  )\r\n}\r\n\r\nfunction SelectLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\r\n  return (\r\n    <SelectPrimitive.Label\r\n      data-slot=\"select-label\"\r\n      className={cn('text-muted-foreground px-2 py-1.5 text-xs', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectItem({\r\n  className,\r\n  children,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\r\n  return (\r\n    <SelectPrimitive.Item\r\n      data-slot=\"select-item\"\r\n      className={cn(\r\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\r\n        <SelectPrimitive.ItemIndicator>\r\n          <CheckIcon className=\"size-4\" />\r\n        </SelectPrimitive.ItemIndicator>\r\n      </span>\r\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\r\n    </SelectPrimitive.Item>\r\n  )\r\n}\r\n\r\nfunction SelectSeparator({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\r\n  return (\r\n    <SelectPrimitive.Separator\r\n      data-slot=\"select-separator\"\r\n      className={cn('bg-border pointer-events-none -mx-1 my-1 h-px', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction SelectScrollUpButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollUpButton\r\n      data-slot=\"select-scroll-up-button\"\r\n      className={cn(\r\n        'flex cursor-default items-center justify-center py-1',\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronUpIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollUpButton>\r\n  )\r\n}\r\n\r\nfunction SelectScrollDownButton({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\r\n  return (\r\n    <SelectPrimitive.ScrollDownButton\r\n      data-slot=\"select-scroll-down-button\"\r\n      className={cn(\r\n        'flex cursor-default items-center justify-center py-1',\r\n        className,\r\n      )}\r\n      {...props}\r\n    >\r\n      <ChevronDownIcon className=\"size-4\" />\r\n    </SelectPrimitive.ScrollDownButton>\r\n  )\r\n}\r\n\r\nexport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectScrollDownButton,\r\n  SelectScrollUpButton,\r\n  SelectSeparator,\r\n  SelectTrigger,\r\n  SelectValue,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAJA;;;;;AAOA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,0KAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,2KAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,2KAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,6KAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,IAAA,yHAAE,EACX,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0KAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,2OAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,4KAAsB;kBACrB,cAAA,8OAAC,6KAAuB;YACtB,aAAU;YACV,WAAW,IAAA,yHAAE,EACX,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,8KAAwB;oBACvB,WAAW,IAAA,yHAAE,EACX,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,2KAAqB;QACpB,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,0KAAoB;QACnB,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,mLAA6B;8BAC5B,cAAA,8OAAC,qNAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,8KAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,+KAAyB;QACxB,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,oLAA8B;QAC7B,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,qOAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,sLAAgC;QAC/B,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,2OAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 310, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/data-table/data-table-pagination.tsx"], "sourcesContent": ["import { Button } from '@atlas/components/ui/button'\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@atlas/components/ui/select'\r\nimport type { Table } from '@tanstack/react-table'\r\nimport {\r\n  ChevronLeft,\r\n  ChevronRight,\r\n  ChevronsLeft,\r\n  ChevronsRight,\r\n} from 'lucide-react'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ninterface DataTablePaginationProps<TData> {\r\n  table: Table<TData>\r\n}\r\n\r\nexport function DataTablePagination<TData>({\r\n  table,\r\n}: DataTablePaginationProps<TData>) {\r\n  const t = useTranslations('components.data-table.pagination')\r\n\r\n  return (\r\n    <div className=\"flex items-center justify-between px-2 gap-2\">\r\n      {table.getIsSomeRowsSelected() && (\r\n        <div className=\"flex-1 text-sm text-muted-foreground\">\r\n          {t('labels.selected-rows', {\r\n            count: table.getFilteredSelectedRowModel().rows.length,\r\n            total: table.getFilteredRowModel().rows.length,\r\n          })}\r\n        </div>\r\n      )}\r\n      <div className=\"flex items-center flex-col md:flex-row justify-between w-full gap-4\">\r\n        <div className=\"flex items-center gap-4 justify-center w-full md:w-auto flex-col md:flex-row\">\r\n          <p className=\"text-sm font-medium\">{t('labels.rows-per-page')}</p>\r\n          <Select\r\n            value={`${table.getState().pagination.pageSize}`}\r\n            onValueChange={value => {\r\n              table.setPageSize(Number(value))\r\n            }}\r\n          >\r\n            <SelectTrigger className=\"h-8 w-[70px]\">\r\n              <SelectValue placeholder={table.getState().pagination.pageSize} />\r\n            </SelectTrigger>\r\n            <SelectContent side=\"top\">\r\n              {[10, 20, 30, 40, 50].map(pageSize => (\r\n                <SelectItem key={pageSize} value={`${pageSize}`}>\r\n                  {pageSize}\r\n                </SelectItem>\r\n              ))}\r\n            </SelectContent>\r\n          </Select>\r\n        </div>\r\n        <div className=\"flex items-center gap-2 flex-row justify-center w-full md:w-auto\">\r\n          <div className=\"flex w-[100px] items-center justify-center text-sm font-medium\">\r\n            {t('labels.pages', {\r\n              current: table.getState().pagination.pageIndex + 1,\r\n              total: table.getPageCount(),\r\n            })}\r\n          </div>\r\n          <Button\r\n            variant=\"outline\"\r\n            className=\"hidden h-8 w-8 p-0 lg:flex\"\r\n            onClick={() => table.setPageIndex(0)}\r\n            disabled={!table.getCanPreviousPage()}\r\n          >\r\n            <span className=\"sr-only\">{t('labels.go-to-first-page')}</span>\r\n            <ChevronsLeft />\r\n          </Button>\r\n          <Button\r\n            variant=\"outline\"\r\n            className=\"h-8 w-8 p-0\"\r\n            onClick={() => table.previousPage()}\r\n            disabled={!table.getCanPreviousPage()}\r\n          >\r\n            <span className=\"sr-only\">{t('labels.go-to-previous-page')}</span>\r\n            <ChevronLeft />\r\n          </Button>\r\n          <Button\r\n            variant=\"outline\"\r\n            className=\"h-8 w-8 p-0\"\r\n            onClick={() => table.nextPage()}\r\n            disabled={!table.getCanNextPage()}\r\n          >\r\n            <span className=\"sr-only\">{t('labels.go-to-next-page')}</span>\r\n            <ChevronRight />\r\n          </Button>\r\n          <Button\r\n            variant=\"outline\"\r\n            className=\"hidden h-8 w-8 p-0 lg:flex\"\r\n            onClick={() => table.setPageIndex(table.getPageCount() - 1)}\r\n            disabled={!table.getCanNextPage()}\r\n          >\r\n            <span className=\"sr-only\">{t('labels.go-to-last-page')}</span>\r\n            <ChevronsRight />\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAQA;AAAA;AAAA;AAAA;AAMA;;;;;;AAMO,SAAS,oBAA2B,EACzC,KAAK,EAC2B;IAChC,MAAM,IAAI,IAAA,yNAAe,EAAC;IAE1B,qBACE,8OAAC;QAAI,WAAU;;YACZ,MAAM,qBAAqB,oBAC1B,8OAAC;gBAAI,WAAU;0BACZ,EAAE,wBAAwB;oBACzB,OAAO,MAAM,2BAA2B,GAAG,IAAI,CAAC,MAAM;oBACtD,OAAO,MAAM,mBAAmB,GAAG,IAAI,CAAC,MAAM;gBAChD;;;;;;0BAGJ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAuB,EAAE;;;;;;0CACtC,8OAAC,4IAAM;gCACL,OAAO,GAAG,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,EAAE;gCAChD,eAAe,CAAA;oCACb,MAAM,WAAW,CAAC,OAAO;gCAC3B;;kDAEA,8OAAC,mJAAa;wCAAC,WAAU;kDACvB,cAAA,8OAAC,iJAAW;4CAAC,aAAa,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ;;;;;;;;;;;kDAEhE,8OAAC,mJAAa;wCAAC,MAAK;kDACjB;4CAAC;4CAAI;4CAAI;4CAAI;4CAAI;yCAAG,CAAC,GAAG,CAAC,CAAA,yBACxB,8OAAC,gJAAU;gDAAgB,OAAO,GAAG,UAAU;0DAC5C;+CADc;;;;;;;;;;;;;;;;;;;;;;kCAOzB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACZ,EAAE,gBAAgB;oCACjB,SAAS,MAAM,QAAQ,GAAG,UAAU,CAAC,SAAS,GAAG;oCACjD,OAAO,MAAM,YAAY;gCAC3B;;;;;;0CAEF,8OAAC,4IAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM,MAAM,YAAY,CAAC;gCAClC,UAAU,CAAC,MAAM,kBAAkB;;kDAEnC,8OAAC;wCAAK,WAAU;kDAAW,EAAE;;;;;;kDAC7B,8OAAC,sOAAY;;;;;;;;;;;0CAEf,8OAAC,4IAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM,MAAM,YAAY;gCACjC,UAAU,CAAC,MAAM,kBAAkB;;kDAEnC,8OAAC;wCAAK,WAAU;kDAAW,EAAE;;;;;;kDAC7B,8OAAC,mOAAW;;;;;;;;;;;0CAEd,8OAAC,4IAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM,MAAM,QAAQ;gCAC7B,UAAU,CAAC,MAAM,cAAc;;kDAE/B,8OAAC;wCAAK,WAAU;kDAAW,EAAE;;;;;;kDAC7B,8OAAC,sOAAY;;;;;;;;;;;0CAEf,8OAAC,4IAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM,MAAM,YAAY,CAAC,MAAM,YAAY,KAAK;gCACzD,UAAU,CAAC,MAAM,cAAc;;kDAE/B,8OAAC;wCAAK,WAAU;kDAAW,EAAE;;;;;;kDAC7B,8OAAC,yOAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1B", "debugId": null}}, {"offset": {"line": 547, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/data-table/data-table-view-option.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { But<PERSON> } from '@atlas//components/ui/button'\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuContent,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n} from '@atlas//components/ui/dropdown-menu'\r\nimport { DropdownMenuTrigger } from '@radix-ui/react-dropdown-menu'\r\nimport type { Table } from '@tanstack/react-table'\r\nimport { Settings2 } from 'lucide-react'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ninterface DataTableViewOptionsProps<TData> {\r\n  table: Table<TData>\r\n}\r\n\r\nexport function DataTableViewOptions<TData>({\r\n  table,\r\n}: DataTableViewOptionsProps<TData>) {\r\n  const t = useTranslations()\r\n\r\n  return (\r\n    <DropdownMenu>\r\n      <DropdownMenuTrigger asChild>\r\n        <Button\r\n          variant=\"outline\"\r\n          size=\"sm\"\r\n          className=\"ml-auto hidden h-8 lg:flex\"\r\n        >\r\n          <Settings2 />\r\n          {t('components.data-table.view-options.buttons.options.label')}\r\n        </Button>\r\n      </DropdownMenuTrigger>\r\n      <DropdownMenuContent align=\"end\" className=\"w-[150px]\">\r\n        <DropdownMenuLabel>\r\n          {t('components.data-table.view-options.labels.toggle-columns')}\r\n        </DropdownMenuLabel>\r\n        <DropdownMenuSeparator />\r\n        {table\r\n          .getAllColumns()\r\n          .filter(\r\n            column =>\r\n              typeof column.accessorFn !== 'undefined' && column.getCanHide(),\r\n          )\r\n          .map(column => {\r\n            return (\r\n              <DropdownMenuCheckboxItem\r\n                key={column.id}\r\n                className=\"capitalize\"\r\n                checked={column.getIsVisible()}\r\n                onCheckedChange={value => column.toggleVisibility(!!value)}\r\n              >\r\n                {column.columnDef?.meta?.i18nLabel || column.id}\r\n              </DropdownMenuCheckboxItem>\r\n            )\r\n          })}\r\n      </DropdownMenuContent>\r\n    </DropdownMenu>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAOA;AAEA;AACA;AAbA;;;;;;;AAmBO,SAAS,qBAA4B,EAC1C,KAAK,EAC4B;IACjC,MAAM,IAAI,IAAA,yNAAe;IAEzB,qBACE,8OAAC,4JAAY;;0BACX,8OAAC,mMAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,4IAAM;oBACL,SAAQ;oBACR,MAAK;oBACL,WAAU;;sCAEV,8OAAC,6NAAS;;;;;wBACT,EAAE;;;;;;;;;;;;0BAGP,8OAAC,mKAAmB;gBAAC,OAAM;gBAAM,WAAU;;kCACzC,8OAAC,iKAAiB;kCACf,EAAE;;;;;;kCAEL,8OAAC,qKAAqB;;;;;oBACrB,MACE,aAAa,GACb,MAAM,CACL,CAAA,SACE,OAAO,OAAO,UAAU,KAAK,eAAe,OAAO,UAAU,IAEhE,GAAG,CAAC,CAAA;wBACH,qBACE,8OAAC,wKAAwB;4BAEvB,WAAU;4BACV,SAAS,OAAO,YAAY;4BAC5B,iBAAiB,CAAA,QAAS,OAAO,gBAAgB,CAAC,CAAC,CAAC;sCAEnD,OAAO,SAAS,EAAE,MAAM,aAAa,OAAO,EAAE;2BAL1C,OAAO,EAAE;;;;;oBAQpB;;;;;;;;;;;;;AAIV", "debugId": null}}, {"offset": {"line": 637, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/table.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type * as React from 'react'\r\n\r\nfunction Table({ className, ...props }: React.ComponentProps<'table'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"table-container\"\r\n      className=\"relative w-full overflow-x-auto\"\r\n    >\r\n      <table\r\n        data-slot=\"table\"\r\n        className={cn('w-full caption-bottom text-sm', className)}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n}\r\n\r\nfunction TableHeader({ className, ...props }: React.ComponentProps<'thead'>) {\r\n  return (\r\n    <thead\r\n      data-slot=\"table-header\"\r\n      className={cn('[&_tr]:border-b', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableBody({ className, ...props }: React.ComponentProps<'tbody'>) {\r\n  return (\r\n    <tbody\r\n      data-slot=\"table-body\"\r\n      className={cn('[&_tr:last-child]:border-0', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableFooter({ className, ...props }: React.ComponentProps<'tfoot'>) {\r\n  return (\r\n    <tfoot\r\n      data-slot=\"table-footer\"\r\n      className={cn(\r\n        'bg-muted/50 border-t font-medium [&>tr]:last:border-b-0',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableRow({ className, ...props }: React.ComponentProps<'tr'>) {\r\n  return (\r\n    <tr\r\n      data-slot=\"table-row\"\r\n      className={cn(\r\n        'hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableHead({ className, ...props }: React.ComponentProps<'th'>) {\r\n  return (\r\n    <th\r\n      data-slot=\"table-head\"\r\n      className={cn(\r\n        'text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCell({ className, ...props }: React.ComponentProps<'td'>) {\r\n  return (\r\n    <td\r\n      data-slot=\"table-cell\"\r\n      className={cn(\r\n        'p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction TableCaption({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'caption'>) {\r\n  return (\r\n    <caption\r\n      data-slot=\"table-caption\"\r\n      className={cn('text-muted-foreground mt-4 text-sm', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Table,\r\n  TableHeader,\r\n  TableBody,\r\n  TableFooter,\r\n  TableHead,\r\n  TableRow,\r\n  TableCell,\r\n  TableCaption,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAEA;AAFA;;;AAKA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,IAAA,yHAAE,EAAC,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 761, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/data-table/data-table.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { DataTablePagination } from '@atlas/components/common/atlas/data-table/data-table-pagination'\r\nimport { DataTableViewOptions } from '@atlas/components/common/atlas/data-table/data-table-view-option'\r\nimport {\r\n  Table,\r\n  TableBody,\r\n  TableCell,\r\n  TableHead,\r\n  TableHeader,\r\n  TableRow,\r\n} from '@atlas/components/ui/table'\r\nimport {\r\n  type ColumnDef,\r\n  flexRender,\r\n  getCoreRowModel,\r\n  getSortedRowModel,\r\n  type PaginationState,\r\n  type SortingState,\r\n  useReactTable,\r\n  type VisibilityState,\r\n} from '@tanstack/react-table'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useState } from 'react'\r\n\r\ninterface DataTableProps<TData, TValue> {\r\n  columns: ColumnDef<TData, TValue>[]\r\n  data: TData[]\r\n  onPaginationChange?: (pagination: { page: number; pageSize: number }) => void\r\n  page?: number\r\n  perPage?: number\r\n  rowCount: number\r\n  isLoading?: boolean\r\n  hidePagination?: boolean\r\n}\r\n\r\nexport function DataTable<TData, TValue>({\r\n  columns,\r\n  data,\r\n  onPaginationChange,\r\n  page = 0,\r\n  perPage = 10,\r\n  rowCount,\r\n  isLoading,\r\n  hidePagination = false,\r\n}: DataTableProps<TData, TValue>) {\r\n  const t = useTranslations('components.data-table')\r\n\r\n  const [pagination, setPagination] = useState<PaginationState>({\r\n    pageIndex: page,\r\n    pageSize: perPage,\r\n  })\r\n\r\n  useEffect(() => {\r\n    if (pagination.pageIndex !== page || pagination.pageSize !== perPage) {\r\n      onPaginationChange?.({\r\n        page: pagination.pageIndex + 1,\r\n        pageSize: pagination.pageSize,\r\n      })\r\n    }\r\n  }, [pagination, onPaginationChange, page, perPage])\r\n\r\n  const [sorting, setSorting] = useState<SortingState>([])\r\n  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({})\r\n\r\n  const table = useReactTable({\r\n    columns,\r\n    data,\r\n    enableRowSelection: false,\r\n    getCoreRowModel: getCoreRowModel(),\r\n    getSortedRowModel: getSortedRowModel(),\r\n    manualPagination: true,\r\n    onPaginationChange: setPagination,\r\n    onColumnVisibilityChange: setColumnVisibility,\r\n    onSortingChange: setSorting,\r\n    rowCount,\r\n    state: {\r\n      columnVisibility,\r\n      pagination,\r\n      sorting,\r\n    },\r\n  })\r\n\r\n  return (\r\n    <div className=\"flex flex-col gap-2\">\r\n      <div className=\"flex flex-row justify-between\">\r\n        <DataTableViewOptions table={table} />\r\n      </div>\r\n      <div className=\"rounded-md border\">\r\n        <Table>\r\n          <TableHeader>\r\n            {table.getHeaderGroups().map(headerGroup => (\r\n              <TableRow key={headerGroup.id}>\r\n                {headerGroup.headers.map(header => {\r\n                  return (\r\n                    <TableHead key={header.id}>\r\n                      {header.isPlaceholder\r\n                        ? null\r\n                        : flexRender(\r\n                            header.column.columnDef.header,\r\n                            header.getContext(),\r\n                          )}\r\n                    </TableHead>\r\n                  )\r\n                })}\r\n              </TableRow>\r\n            ))}\r\n          </TableHeader>\r\n          <TableBody>\r\n            {isLoading ? (\r\n              Array.from({ length: perPage }).map((_, rowIndex) => (\r\n                <TableRow key={`skeleton-${rowIndex}`}>\r\n                  {Array.from({ length: columns.length }).map((_, colIndex) => (\r\n                    <TableCell key={`skeleton-cell-${colIndex}`}>\r\n                      <div className=\"h-4 w-full animate-pulse bg-gray-200 rounded\" />\r\n                    </TableCell>\r\n                  ))}\r\n                </TableRow>\r\n              ))\r\n            ) : table.getRowModel().rows?.length ? (\r\n              table.getRowModel().rows.map(row => (\r\n                <TableRow\r\n                  key={row.id}\r\n                  data-state={row.getIsSelected() && 'selected'}\r\n                >\r\n                  {row.getVisibleCells().map(cell => (\r\n                    <TableCell key={cell.id}>\r\n                      {flexRender(\r\n                        cell.column.columnDef.cell,\r\n                        cell.getContext(),\r\n                      )}\r\n                    </TableCell>\r\n                  ))}\r\n                </TableRow>\r\n              ))\r\n            ) : (\r\n              <TableRow>\r\n                <TableCell\r\n                  colSpan={columns.length}\r\n                  className=\"h-24 text-center\"\r\n                >\r\n                  {t('status.no-data')}\r\n                </TableCell>\r\n              </TableRow>\r\n            )}\r\n          </TableBody>\r\n        </Table>\r\n      </div>\r\n      {!hidePagination && <DataTablePagination table={table} />}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAQA;AAAA;AAUA;AACA;AAvBA;;;;;;;;AAoCO,SAAS,UAAyB,EACvC,OAAO,EACP,IAAI,EACJ,kBAAkB,EAClB,OAAO,CAAC,EACR,UAAU,EAAE,EACZ,QAAQ,EACR,SAAS,EACT,iBAAiB,KAAK,EACQ;IAC9B,MAAM,IAAI,IAAA,yNAAe,EAAC;IAE1B,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAkB;QAC5D,WAAW;QACX,UAAU;IACZ;IAEA,IAAA,kNAAS,EAAC;QACR,IAAI,WAAW,SAAS,KAAK,QAAQ,WAAW,QAAQ,KAAK,SAAS;YACpE,qBAAqB;gBACnB,MAAM,WAAW,SAAS,GAAG;gBAC7B,UAAU,WAAW,QAAQ;YAC/B;QACF;IACF,GAAG;QAAC;QAAY;QAAoB;QAAM;KAAQ;IAElD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAe,EAAE;IACvD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,IAAA,iNAAQ,EAAkB,CAAC;IAE3E,MAAM,QAAQ,IAAA,uMAAa,EAAC;QAC1B;QACA;QACA,oBAAoB;QACpB,iBAAiB,IAAA,wLAAe;QAChC,mBAAmB,IAAA,0LAAiB;QACpC,kBAAkB;QAClB,oBAAoB;QACpB,0BAA0B;QAC1B,iBAAiB;QACjB;QACA,OAAO;YACL;YACA;YACA;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,iNAAoB;oBAAC,OAAO;;;;;;;;;;;0BAE/B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0IAAK;;sCACJ,8OAAC,gJAAW;sCACT,MAAM,eAAe,GAAG,GAAG,CAAC,CAAA,4BAC3B,8OAAC,6IAAQ;8CACN,YAAY,OAAO,CAAC,GAAG,CAAC,CAAA;wCACvB,qBACE,8OAAC,8IAAS;sDACP,OAAO,aAAa,GACjB,OACA,IAAA,oMAAU,EACR,OAAO,MAAM,CAAC,SAAS,CAAC,MAAM,EAC9B,OAAO,UAAU;2CALT,OAAO,EAAE;;;;;oCAS7B;mCAZa,YAAY,EAAE;;;;;;;;;;sCAgBjC,8OAAC,8IAAS;sCACP,YACC,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAQ,GAAG,GAAG,CAAC,CAAC,GAAG,yBACtC,8OAAC,6IAAQ;8CACN,MAAM,IAAI,CAAC;wCAAE,QAAQ,QAAQ,MAAM;oCAAC,GAAG,GAAG,CAAC,CAAC,GAAG,yBAC9C,8OAAC,8IAAS;sDACR,cAAA,8OAAC;gDAAI,WAAU;;;;;;2CADD,CAAC,cAAc,EAAE,UAAU;;;;;mCAFhC,CAAC,SAAS,EAAE,UAAU;;;;4CAQrC,MAAM,WAAW,GAAG,IAAI,EAAE,SAC5B,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAA,oBAC3B,8OAAC,6IAAQ;oCAEP,cAAY,IAAI,aAAa,MAAM;8CAElC,IAAI,eAAe,GAAG,GAAG,CAAC,CAAA,qBACzB,8OAAC,8IAAS;sDACP,IAAA,oMAAU,EACT,KAAK,MAAM,CAAC,SAAS,CAAC,IAAI,EAC1B,KAAK,UAAU;2CAHH,KAAK,EAAE;;;;;mCAJpB,IAAI,EAAE;;;;0DAcf,8OAAC,6IAAQ;0CACP,cAAA,8OAAC,8IAAS;oCACR,SAAS,QAAQ,MAAM;oCACvB,WAAU;8CAET,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOd,CAAC,gCAAkB,8OAAC,4MAAmB;gBAAC,OAAO;;;;;;;;;;;;AAGtD", "debugId": null}}, {"offset": {"line": 946, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/label.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { cn } from '@atlas/lib/utils'\r\nimport * as LabelPrimitive from '@radix-ui/react-label'\r\nimport type * as React from 'react'\r\n\r\nfunction Label({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  return (\r\n    <LabelPrimitive.Root\r\n      data-slot=\"label\"\r\n      className={cn(\r\n        'flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAMA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,yKAAmB;QAClB,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 973, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/form.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { Label } from '@atlas/components/ui/label'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type * as LabelPrimitive from '@radix-ui/react-label'\r\nimport { Slot } from '@radix-ui/react-slot'\r\nimport * as React from 'react'\r\nimport {\r\n  Controller,\r\n  type ControllerProps,\r\n  type FieldPath,\r\n  type FieldValues,\r\n  FormProvider,\r\n  useFormContext,\r\n  useFormState,\r\n} from 'react-hook-form'\r\n\r\nconst Form = FormProvider\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n> = {\r\n  name: TName\r\n}\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\r\n  {} as FormFieldContextValue,\r\n)\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  )\r\n}\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext)\r\n  const itemContext = React.useContext(FormItemContext)\r\n  const { getFieldState } = useFormContext()\r\n  const formState = useFormState({ name: fieldContext.name })\r\n  const fieldState = getFieldState(fieldContext.name, formState)\r\n\r\n  if (!fieldContext) {\r\n    throw new Error('useFormField should be used within <FormField>')\r\n  }\r\n\r\n  const { id } = itemContext\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  }\r\n}\r\n\r\ntype FormItemContextValue = {\r\n  id: string\r\n}\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>(\r\n  {} as FormItemContextValue,\r\n)\r\n\r\nfunction FormItem({ className, ...props }: React.ComponentProps<'div'>) {\r\n  const id = React.useId()\r\n\r\n  return (\r\n    <FormItemContext.Provider value={{ id }}>\r\n      <div\r\n        data-slot=\"form-item\"\r\n        className={cn('grid gap-2', className)}\r\n        {...props}\r\n      />\r\n    </FormItemContext.Provider>\r\n  )\r\n}\r\n\r\nfunction FormLabel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\r\n  const { error, formItemId } = useFormField()\r\n\r\n  return (\r\n    <Label\r\n      data-slot=\"form-label\"\r\n      data-error={!!error}\r\n      className={cn('data-[error=true]:text-destructive', className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormControl({ ...props }: React.ComponentProps<typeof Slot>) {\r\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\r\n\r\n  return (\r\n    <Slot\r\n      data-slot=\"form-control\"\r\n      id={formItemId}\r\n      aria-describedby={\r\n        !error\r\n          ? `${formDescriptionId}`\r\n          : `${formDescriptionId} ${formMessageId}`\r\n      }\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormDescription({ className, ...props }: React.ComponentProps<'p'>) {\r\n  const { formDescriptionId } = useFormField()\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-description\"\r\n      id={formDescriptionId}\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction FormMessage({ className, ...props }: React.ComponentProps<'p'>) {\r\n  const { error, formMessageId } = useFormField()\r\n  const body = error ? String(error?.message ?? '') : props.children\r\n\r\n  if (!body) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <p\r\n      data-slot=\"form-message\"\r\n      id={formMessageId}\r\n      className={cn('text-destructive text-sm', className)}\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  )\r\n}\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAEA;AACA;AAEA;AACA;AACA;AAPA;;;;;;;AAiBA,MAAM,OAAO,8KAAY;AASzB,MAAM,iCAAmB,sNAAmB,CAC1C,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,8OAAC,4KAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,mNAAgB,CAAC;IACtC,MAAM,cAAc,mNAAgB,CAAC;IACrC,MAAM,EAAE,aAAa,EAAE,GAAG,IAAA,gLAAc;IACxC,MAAM,YAAY,IAAA,8KAAY,EAAC;QAAE,MAAM,aAAa,IAAI;IAAC;IACzD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,sNAAmB,CACzC,CAAC;AAGH,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAoC;IACpE,MAAM,KAAK,8MAAW;IAEtB,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,IAAA,yHAAE,EAAC,cAAc;YAC3B,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OAC8C;IACjD,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,8OAAC,0IAAK;QACJ,aAAU;QACV,cAAY,CAAC,CAAC;QACd,WAAW,IAAA,yHAAE,EAAC,sCAAsC;QACpD,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,GAAG,OAA0C;IAClE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,8OAAC,wKAAI;QACH,aAAU;QACV,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAkC;IACzE,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,IAAA,yHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAkC;IACrE,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM,MAAM,QAAQ;IAElE,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,aAAU;QACV,IAAI;QACJ,WAAW,IAAA,yHAAE,EAAC,4BAA4B;QACzC,GAAG,KAAK;kBAER;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 1131, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/projects/project-detail/modules/create-module-type-selector.tsx"], "sourcesContent": ["import {\r\n  FormControl,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n} from '@atlas/components/ui/form'\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectGroup,\r\n  SelectItem,\r\n  SelectLabel,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@atlas/components/ui/select'\r\nimport { categorizedModuleTypes } from '@atlas/constants/module'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { NewModuleForm } from '@atlas/types/schemas/module-form'\r\nimport { useTranslations } from 'next-intl'\r\nimport type { Control } from 'react-hook-form'\r\n\r\ntype Props = { control: Control<NewModuleForm> }\r\n\r\nexport const CreateModuleTypeSelector = ({ control }: Props) => {\r\n  const t = useTranslations('forms.create-module.fields')\r\n  const tModuleType = useTranslations('enums.module-type')\r\n\r\n  return (\r\n    <FormField\r\n      control={control}\r\n      name=\"type\"\r\n      render={({ field, fieldState }) => (\r\n        <FormItem>\r\n          <FormLabel>\r\n            {t(`${field.name}.label`)}\r\n            <p className=\"text-red-600\">*</p>\r\n          </FormLabel>\r\n          <FormControl>\r\n            <Select {...field} onValueChange={field.onChange}>\r\n              <SelectTrigger className=\"w-full\">\r\n                <SelectValue placeholder={t(`${field.name}.placeholder`)} />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                {categorizedModuleTypes\r\n                  .filter(e => e.enabled)\r\n                  .map(cat => (\r\n                    <SelectGroup key={cat.category}>\r\n                      <SelectLabel>{t(`category.${cat.category}`)}</SelectLabel>\r\n                      {cat.types\r\n                        .filter(e => e.enabled)\r\n                        .map(({ name }) => (\r\n                          <SelectItem value={name} key={name}>\r\n                            {tModuleType(`${name}`)}\r\n                          </SelectItem>\r\n                        ))}\r\n                    </SelectGroup>\r\n                  ))}\r\n              </SelectContent>\r\n            </Select>\r\n          </FormControl>\r\n          {fieldState?.error?.message && (\r\n            <p className={cn('text-destructive text-sm')}>\r\n              {t(`error.${fieldState?.error?.message}`)}\r\n            </p>\r\n          )}\r\n        </FormItem>\r\n      )}\r\n    />\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAMA;AASA;AACA;AAEA;;;;;;;AAKO,MAAM,2BAA2B,CAAC,EAAE,OAAO,EAAS;IACzD,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,cAAc,IAAA,yNAAe,EAAC;IAEpC,qBACE,8OAAC,6IAAS;QACR,SAAS;QACT,MAAK;QACL,QAAQ,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,iBAC5B,8OAAC,4IAAQ;;kCACP,8OAAC,6IAAS;;4BACP,EAAE,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC;0CACxB,8OAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;kCAE9B,8OAAC,+IAAW;kCACV,cAAA,8OAAC,4IAAM;4BAAE,GAAG,KAAK;4BAAE,eAAe,MAAM,QAAQ;;8CAC9C,8OAAC,mJAAa;oCAAC,WAAU;8CACvB,cAAA,8OAAC,iJAAW;wCAAC,aAAa,EAAE,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC;;;;;;;;;;;8CAEzD,8OAAC,mJAAa;8CACX,qJAAsB,CACpB,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,EACrB,GAAG,CAAC,CAAA,oBACH,8OAAC,iJAAW;;8DACV,8OAAC,iJAAW;8DAAE,EAAE,CAAC,SAAS,EAAE,IAAI,QAAQ,EAAE;;;;;;gDACzC,IAAI,KAAK,CACP,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,EACrB,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,iBACZ,8OAAC,gJAAU;wDAAC,OAAO;kEAChB,YAAY,GAAG,MAAM;uDADM;;;;;;2CALlB,IAAI,QAAQ;;;;;;;;;;;;;;;;;;;;;oBAcvC,YAAY,OAAO,yBAClB,8OAAC;wBAAE,WAAW,IAAA,yHAAE,EAAC;kCACd,EAAE,CAAC,MAAM,EAAE,YAAY,OAAO,SAAS;;;;;;;;;;;;;;;;;AAOtD", "debugId": null}}, {"offset": {"line": 1255, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/form/string-form-input.tsx"], "sourcesContent": ["import {\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  useFormField,\r\n} from '@atlas/components/ui/form'\r\nimport { Input } from '@atlas/components/ui/input'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { Control, FieldPath, FieldValues } from 'react-hook-form'\r\n\r\ntype Props<T extends FieldValues> = {\r\n  control: Control<T>\r\n  description?: boolean\r\n  disabled?: boolean\r\n  name: FieldPath<T>\r\n  required?: boolean\r\n  t: (message: string) => string\r\n}\r\n\r\nexport const StringFormInput = <T extends FieldValues>({\r\n  control,\r\n  description = false,\r\n  disabled = false,\r\n  name,\r\n  required = false,\r\n  t,\r\n}: Props<T>) => {\r\n  const { error } = useFormField()\r\n\r\n  return (\r\n    <FormField\r\n      control={control}\r\n      name={name}\r\n      render={({ field }) => (\r\n        <FormItem>\r\n          <FormLabel>\r\n            {t(`${name}.label`)}\r\n            {required && <p className=\"text-red-600\">*</p>}\r\n          </FormLabel>\r\n          <FormControl>\r\n            <Input\r\n              placeholder={t(`${name}.placeholder`)}\r\n              disabled={disabled}\r\n              {...field}\r\n            />\r\n          </FormControl>\r\n          {description && (\r\n            <FormDescription>{t(`${name}.description`)}</FormDescription>\r\n          )}\r\n          {error?.message && (\r\n            <p className={cn('text-destructive text-sm')}>\r\n              {t(`error.${error}`)}\r\n            </p>\r\n          )}\r\n        </FormItem>\r\n      )}\r\n    />\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAQA;AACA;;;;;AAYO,MAAM,kBAAkB,CAAwB,EACrD,OAAO,EACP,cAAc,KAAK,EACnB,WAAW,KAAK,EAChB,IAAI,EACJ,WAAW,KAAK,EAChB,CAAC,EACQ;IACT,MAAM,EAAE,KAAK,EAAE,GAAG,IAAA,gJAAY;IAE9B,qBACE,8OAAC,6IAAS;QACR,SAAS;QACT,MAAM;QACN,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,4IAAQ;;kCACP,8OAAC,6IAAS;;4BACP,EAAE,GAAG,KAAK,MAAM,CAAC;4BACjB,0BAAY,8OAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;kCAE3C,8OAAC,+IAAW;kCACV,cAAA,8OAAC,0IAAK;4BACJ,aAAa,EAAE,GAAG,KAAK,YAAY,CAAC;4BACpC,UAAU;4BACT,GAAG,KAAK;;;;;;;;;;;oBAGZ,6BACC,8OAAC,mJAAe;kCAAE,EAAE,GAAG,KAAK,YAAY,CAAC;;;;;;oBAE1C,OAAO,yBACN,8OAAC;wBAAE,WAAW,IAAA,yHAAE,EAAC;kCACd,EAAE,CAAC,MAAM,EAAE,OAAO;;;;;;;;;;;;;;;;;AAOjC", "debugId": null}}, {"offset": {"line": 1337, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { buttonVariants } from '@atlas/components/ui/button'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport * as AlertDialogPrimitive from '@radix-ui/react-alert-dialog'\r\nimport type * as React from 'react'\r\n\r\nfunction AlertDialog({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Root>) {\r\n  return <AlertDialogPrimitive.Root data-slot=\"alert-dialog\" {...props} />\r\n}\r\n\r\nfunction AlertDialogTrigger({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Trigger>) {\r\n  return (\r\n    <AlertDialogPrimitive.Trigger data-slot=\"alert-dialog-trigger\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogPortal({\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Portal>) {\r\n  return (\r\n    <AlertDialogPrimitive.Portal data-slot=\"alert-dialog-portal\" {...props} />\r\n  )\r\n}\r\n\r\nfunction AlertDialogOverlay({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Overlay>) {\r\n  return (\r\n    <AlertDialogPrimitive.Overlay\r\n      data-slot=\"alert-dialog-overlay\"\r\n      className={cn(\r\n        'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogContent({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Content>) {\r\n  return (\r\n    <AlertDialogPortal>\r\n      <AlertDialogOverlay />\r\n      <AlertDialogPrimitive.Content\r\n        data-slot=\"alert-dialog-content\"\r\n        className={cn(\r\n          'bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg',\r\n          className,\r\n        )}\r\n        {...props}\r\n      />\r\n    </AlertDialogPortal>\r\n  )\r\n}\r\n\r\nfunction AlertDialogHeader({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-header\"\r\n      className={cn('flex flex-col gap-2 text-center sm:text-left', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogFooter({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-dialog-footer\"\r\n      className={cn(\r\n        'flex flex-col-reverse gap-2 sm:flex-row sm:justify-end',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogTitle({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Title>) {\r\n  return (\r\n    <AlertDialogPrimitive.Title\r\n      data-slot=\"alert-dialog-title\"\r\n      className={cn('text-lg font-semibold', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Description>) {\r\n  return (\r\n    <AlertDialogPrimitive.Description\r\n      data-slot=\"alert-dialog-description\"\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogAction({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Action>) {\r\n  return (\r\n    <AlertDialogPrimitive.Action\r\n      className={cn(buttonVariants(), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDialogCancel({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<typeof AlertDialogPrimitive.Cancel>) {\r\n  return (\r\n    <AlertDialogPrimitive.Cancel\r\n      className={cn(buttonVariants({ variant: 'outline' }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  AlertDialog,\r\n  AlertDialogPortal,\r\n  AlertDialogOverlay,\r\n  AlertDialogTrigger,\r\n  AlertDialogContent,\r\n  AlertDialogHeader,\r\n  AlertDialogFooter,\r\n  AlertDialogTitle,\r\n  AlertDialogDescription,\r\n  AlertDialogAction,\r\n  AlertDialogCancel,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAOA,SAAS,YAAY,EACnB,GAAG,OACoD;IACvD,qBAAO,8OAAC,mLAAyB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,sLAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,qLAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC,sLAA4B;QAC3B,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,sLAA4B;gBAC3B,aAAU;gBACV,WAAW,IAAA,yHAAE,EACX,+WACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,oLAA0B;QACzB,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,0LAAgC;QAC/B,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,qLAA2B;QAC1B,WAAW,IAAA,yHAAE,EAAC,IAAA,oJAAc,KAAI;QAC/B,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,8OAAC,qLAA2B;QAC1B,WAAW,IAAA,yHAAE,EAAC,IAAA,oJAAc,EAAC;YAAE,SAAS;QAAU,IAAI;QACrD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1506, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/functions/forms/on-enter-key.ts"], "sourcesContent": ["import type { KeyboardEventHandler } from 'react'\r\n\r\nexport const onEnterKey =\r\n  (fn: () => void): KeyboardEventHandler<HTMLFormElement> =>\r\n  e => {\r\n    if (e.key === 'Enter' && !e.shiftKey && !(e as any).isComposing) {\r\n      e.preventDefault()\r\n      fn()\r\n    }\r\n  }\r\n"], "names": [], "mappings": ";;;;AAEO,MAAM,aACX,CAAC,KACD,CAAA;QACE,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,IAAI,CAAC,AAAC,EAAU,WAAW,EAAE;YAC/D,EAAE,cAAc;YAChB;QACF;IACF", "debugId": null}}, {"offset": {"line": 1520, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/api/modules/endpoints/post-module.ts"], "sourcesContent": ["import { handleApiError } from '@atlas/lib/api/apiErrors'\r\nimport {\r\n  type Module,\r\n  moduleSchema,\r\n} from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { ApiError, ValidationError } from '@atlas/types'\r\nimport type { NewModuleForm } from '@atlas/types/schemas/module-form'\r\nimport type { AxiosResponse } from 'axios'\r\nimport { pipe } from 'fp-ts/function'\r\nimport { map, type TaskEither, tryCatch } from 'fp-ts/TaskEither'\r\nimport { apiClient } from '../../httpClient'\r\n\r\nconst validate = (data: unknown): Module => {\r\n  const parse = moduleSchema.safeParse(data)\r\n  if (!parse.success) {\r\n    throw {\r\n      type: 'ValidationError',\r\n      code: 'MALFORMED_RESPONSE',\r\n      message: parse.error.message,\r\n    } as ValidationError\r\n  }\r\n  return parse.data\r\n}\r\n\r\nconst post = (\r\n  token: string,\r\n  projectId: Project['id'],\r\n  body: NewModuleForm,\r\n): TaskEither<ApiError, AxiosResponse> =>\r\n  tryCatch(\r\n    () =>\r\n      apiClient.post(`/api/v2/projects/${projectId}/modules`, body, {\r\n        headers: { Authorization: `Bearer ${token}` },\r\n      }),\r\n    e => handleApiError(e),\r\n  )\r\n\r\nexport const postModule = (\r\n  token: string,\r\n  projectId: Project['id'],\r\n  body: NewModuleForm,\r\n): TaskEither<ApiError | ValidationError, Module> =>\r\n  pipe(\r\n    post(token, projectId, body),\r\n    map(a => a.data),\r\n    map(validate),\r\n  )\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAQA;AACA;AACA;;;;;;AAEA,MAAM,WAAW,CAAC;IAChB,MAAM,QAAQ,kKAAY,CAAC,SAAS,CAAC;IACrC,IAAI,CAAC,MAAM,OAAO,EAAE;QAClB,MAAM;YACJ,MAAM;YACN,MAAM;YACN,SAAS,MAAM,KAAK,CAAC,OAAO;QAC9B;IACF;IACA,OAAO,MAAM,IAAI;AACnB;AAEA,MAAM,OAAO,CACX,OACA,WACA,OAEA,IAAA,yJAAQ,EACN,IACE,4IAAS,CAAC,IAAI,CAAC,CAAC,iBAAiB,EAAE,UAAU,QAAQ,CAAC,EAAE,MAAM;YAC5D,SAAS;gBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;YAAC;QAC9C,IACF,CAAA,IAAK,IAAA,gJAAc,EAAC;AAGjB,MAAM,aAAa,CACxB,OACA,WACA,OAEA,IAAA,mJAAI,EACF,KAAK,OAAO,WAAW,OACvB,IAAA,oJAAG,EAAC,CAAA,IAAK,EAAE,IAAI,GACf,IAAA,oJAAG,EAAC", "debugId": null}}, {"offset": {"line": 1555, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/mutation/modules/use-new-module-mutation.tsx"], "sourcesContent": ["import { postModule } from '@atlas/lib/api/modules/endpoints/post-module'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { ApiError, ValidationError } from '@atlas/types'\r\nimport type { NewModuleForm } from '@atlas/types/schemas/module-form'\r\nimport {\r\n  type UseMutationOptions,\r\n  useMutation,\r\n  useQueryClient,\r\n} from '@tanstack/react-query'\r\nimport * as E from 'fp-ts/Either'\r\nimport { pipe } from 'fp-ts/function'\r\n\r\ntype PostModuleError = ApiError | ValidationError\r\ntype PostModuleData = Module\r\n\r\ntype PostModuleVariables = {\r\n  projectId: Project['id']\r\n  body: NewModuleForm\r\n}\r\n\r\nconst mutationFn =\r\n  (token: string) =>\r\n  async (variables: PostModuleVariables): Promise<PostModuleData> => {\r\n    const result = await postModule(\r\n      token,\r\n      variables.projectId,\r\n      variables.body,\r\n    )()\r\n\r\n    return pipe(\r\n      result,\r\n      E.match(\r\n        (error: PostModuleError) => {\r\n          throw error\r\n        },\r\n        (project: PostModuleData) => {\r\n          return project\r\n        },\r\n      ),\r\n    )\r\n  }\r\n\r\nexport const useNewModuleMutation = (\r\n  token: string,\r\n  options?: Omit<\r\n    UseMutationOptions<PostModuleData, PostModuleError, PostModuleVariables>,\r\n    'mutationFn'\r\n  >,\r\n) => {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation<PostModuleData, PostModuleError, PostModuleVariables>({\r\n    ...options,\r\n    mutationFn: mutationFn(token),\r\n    onSuccess: (data, variables, context) => {\r\n      queryClient.invalidateQueries({\r\n        queryKey: ['modules', { projectId: variables.projectId }],\r\n      })\r\n      options?.onSuccess?.(data, variables, context)\r\n    },\r\n  })\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAKA;AAAA;AAKA;AACA;;;;;AAUA,MAAM,aACJ,CAAC,QACD,OAAO;QACL,MAAM,SAAS,MAAM,IAAA,yKAAU,EAC7B,OACA,UAAU,SAAS,EACnB,UAAU,IAAI;QAGhB,OAAO,IAAA,mJAAI,EACT,QACA,kJAAO,CACL,CAAC;YACC,MAAM;QACR,GACA,CAAC;YACC,OAAO;QACT;IAGN;AAEK,MAAM,uBAAuB,CAClC,OACA;IAKA,MAAM,cAAc,IAAA,wMAAc;IAElC,OAAO,IAAA,6LAAW,EAAuD;QACvE,GAAG,OAAO;QACV,YAAY,WAAW;QACvB,WAAW,CAAC,MAAM,WAAW;YAC3B,YAAY,iBAAiB,CAAC;gBAC5B,UAAU;oBAAC;oBAAW;wBAAE,WAAW,UAAU,SAAS;oBAAC;iBAAE;YAC3D;YACA,SAAS,YAAY,MAAM,WAAW;QACxC;IACF;AACF", "debugId": null}}, {"offset": {"line": 1598, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/types/schemas/module-form.ts"], "sourcesContent": ["import { MODULE_TYPES } from '@atlas/constants/module'\r\nimport { z } from 'zod'\r\n\r\nexport const newModuleFormSchema = z.object({\r\n  name: z.string().min(1),\r\n  description: z.string().optional(),\r\n  type: z.enum(MODULE_TYPES),\r\n})\r\n\r\nexport type NewModuleForm = z.infer<typeof newModuleFormSchema>\r\n\r\nexport const editModuleFormSchema = z.object({\r\n  name: z.string().min(1),\r\n  description: z.string().optional(),\r\n})\r\n\r\nexport type EditModuleForm = z.infer<typeof editModuleFormSchema>\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,MAAM,sBAAsB,kLAAC,CAAC,MAAM,CAAC;IAC1C,MAAM,kLAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACrB,aAAa,kLAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,MAAM,kLAAC,CAAC,IAAI,CAAC,2IAAY;AAC3B;AAIO,MAAM,uBAAuB,kLAAC,CAAC,MAAM,CAAC;IAC3C,MAAM,kLAAC,CAAC,MAAM,GAAG,GAAG,CAAC;IACrB,aAAa,kLAAC,CAAC,MAAM,GAAG,QAAQ;AAClC", "debugId": null}}, {"offset": {"line": 1621, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/projects/project-detail/modules/create-module-modal.tsx"], "sourcesContent": ["import { CreateModuleTypeSelector } from '@atlas/components/common/atlas/projects/project-detail/modules/create-module-type-selector'\r\nimport { createModuleModalOpenAtom } from '@atlas/components/common/atlas/projects/project-detail/modules/project-modules-section'\r\nimport { StringFormInput } from '@atlas/components/common/form/string-form-input'\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@atlas/components/ui/alert-dialog'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { onEnterKey } from '@atlas/functions/forms/on-enter-key'\r\nimport { useRouter } from '@atlas/i18n/routing'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useNewModuleMutation } from '@atlas/lib/mutation/modules/use-new-module-mutation'\r\nimport {\r\n  type NewModuleForm,\r\n  newModuleFormSchema,\r\n} from '@atlas/types/schemas/module-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useAtom } from 'jotai/index'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  project: Project\r\n  defaultValues?: Partial<NewModuleForm>\r\n}\r\n\r\nexport const CreateModuleModal = ({\r\n  session,\r\n  project,\r\n  defaultValues,\r\n}: Props) => {\r\n  const t = useTranslations('components.modules.create-modal')\r\n  const tAction = useTranslations('actions.create-module.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const tFields = useTranslations('forms.create-module.fields')\r\n  const [open, onOpenChange] = useAtom(createModuleModalOpenAtom)\r\n  const router = useRouter()\r\n  const form = useForm<NewModuleForm>({\r\n    resolver: zodResolver(newModuleFormSchema),\r\n    defaultValues: {\r\n      name: defaultValues?.name ?? '',\r\n      description: defaultValues?.description ?? '',\r\n      type: defaultValues?.type ?? 'RECTANGULAR_BEAM',\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useNewModuleMutation(session.accessToken, {\r\n    onSuccess: module => {\r\n      router.push(`/dashboard/projects/${project.id}/modules/${module.id}`)\r\n      toast.success(tAction('create.success'))\r\n    },\r\n    onError: error => {\r\n      toast.error(tAction('create.failure', { error: error.message }))\r\n    },\r\n  })\r\n\r\n  const onSubmit = (body: NewModuleForm) => {\r\n    mutate({ projectId: project.id, body })\r\n  }\r\n\r\n  return (\r\n    <AlertDialog open={open} onOpenChange={onOpenChange}>\r\n      <AlertDialogContent>\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>{t('title')}</AlertDialogTitle>\r\n          <AlertDialogDescription>\r\n            {t('description', {\r\n              projectName: project.constructionSiteName ?? '',\r\n            })}\r\n          </AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <Form {...form}>\r\n          <form\r\n            onSubmit={form.handleSubmit(onSubmit)}\r\n            onKeyDown={onEnterKey(form.handleSubmit(onSubmit))}\r\n            className=\"space-y-6\"\r\n          >\r\n            <StringFormInput\r\n              required\r\n              control={form.control}\r\n              name=\"name\"\r\n              t={tFields}\r\n            />\r\n            <StringFormInput\r\n              control={form.control}\r\n              name=\"description\"\r\n              t={tFields}\r\n            />\r\n            <CreateModuleTypeSelector control={form.control} />\r\n            <div className=\"flex justify-end gap-1\">\r\n              <Button\r\n                variant=\"secondary\"\r\n                className=\"w-30\"\r\n                onClick={() => onOpenChange(false)}\r\n              >\r\n                {tCommon('cancel')}\r\n              </Button>\r\n              <Button type=\"submit\" className=\"w-30\" disabled={isPending}>\r\n                {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n                {tCommon('create')}\r\n              </Button>\r\n            </div>\r\n          </form>\r\n        </Form>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AAEA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;;;;;;;;;;;;;;;;;;AAQO,MAAM,oBAAoB,CAAC,EAChC,OAAO,EACP,OAAO,EACP,aAAa,EACP;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,CAAC,MAAM,aAAa,GAAG,IAAA,iJAAO,EAAC,+OAAyB;IAC9D,MAAM,SAAS,IAAA,mIAAS;IACxB,MAAM,OAAO,IAAA,yKAAO,EAAgB;QAClC,UAAU,IAAA,6KAAW,EAAC,gKAAmB;QACzC,eAAe;YACb,MAAM,eAAe,QAAQ;YAC7B,aAAa,eAAe,eAAe;YAC3C,MAAM,eAAe,QAAQ;QAC/B;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,8LAAoB,EAAC,QAAQ,WAAW,EAAE;QACtE,WAAW,CAAA;YACT,OAAO,IAAI,CAAC,CAAC,oBAAoB,EAAE,QAAQ,EAAE,CAAC,SAAS,EAAE,OAAO,EAAE,EAAE;YACpE,iJAAK,CAAC,OAAO,CAAC,QAAQ;QACxB;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,kBAAkB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC/D;IACF;IAEA,MAAM,WAAW,CAAC;QAChB,OAAO;YAAE,WAAW,QAAQ,EAAE;YAAE;QAAK;IACvC;IAEA,qBACE,8OAAC,0JAAW;QAAC,MAAM;QAAM,cAAc;kBACrC,cAAA,8OAAC,iKAAkB;;8BACjB,8OAAC,gKAAiB;;sCAChB,8OAAC,+JAAgB;sCAAE,EAAE;;;;;;sCACrB,8OAAC,qKAAsB;sCACpB,EAAE,eAAe;gCAChB,aAAa,QAAQ,oBAAoB,IAAI;4BAC/C;;;;;;;;;;;;8BAGJ,8OAAC,wIAAI;oBAAE,GAAG,IAAI;8BACZ,cAAA,8OAAC;wBACC,UAAU,KAAK,YAAY,CAAC;wBAC5B,WAAW,IAAA,6JAAU,EAAC,KAAK,YAAY,CAAC;wBACxC,WAAU;;0CAEV,8OAAC,kLAAe;gCACd,QAAQ;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,GAAG;;;;;;0CAEL,8OAAC,kLAAe;gCACd,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,GAAG;;;;;;0CAEL,8OAAC,qPAAwB;gCAAC,SAAS,KAAK,OAAO;;;;;;0CAC/C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4IAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS,IAAM,aAAa;kDAE3B,QAAQ;;;;;;kDAEX,8OAAC,4IAAM;wCAAC,MAAK;wCAAS,WAAU;wCAAO,UAAU;;4CAC9C,2BAAa,8OAAC,oOAAW;gDAAC,WAAU;;;;;;4CACpC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzB", "debugId": null}}, {"offset": {"line": 1818, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/projects/project-detail/modules/use-module-pagination.tsx"], "sourcesContent": ["import type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport { useMemo, useState } from 'react'\r\n\r\ntype Props = {\r\n  modules: Module[]\r\n}\r\n\r\nexport const useModulePagination = ({ modules }: Props) => {\r\n  const [page, setPage] = useState(0)\r\n  const [pageSize, setPageSize] = useState(10)\r\n\r\n  const currentPageModules: Module[] = useMemo(() => {\r\n    const start = page * pageSize\r\n    return modules.slice(start, start + pageSize)\r\n  }, [modules, page, pageSize])\r\n\r\n  const onPaginationChange = ({\r\n    page,\r\n    pageSize,\r\n  }: {\r\n    page: number\r\n    pageSize: number\r\n  }) => {\r\n    setPage(page - 1)\r\n    setPageSize(pageSize)\r\n  }\r\n\r\n  return {\r\n    page,\r\n    pageSize,\r\n    onPaginationChange,\r\n    currentPageModules,\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;;AAMO,MAAM,sBAAsB,CAAC,EAAE,OAAO,EAAS;IACpD,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,iNAAQ,EAAC;IACjC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAC;IAEzC,MAAM,qBAA+B,IAAA,gNAAO,EAAC;QAC3C,MAAM,QAAQ,OAAO;QACrB,OAAO,QAAQ,KAAK,CAAC,OAAO,QAAQ;IACtC,GAAG;QAAC;QAAS;QAAM;KAAS;IAE5B,MAAM,qBAAqB,CAAC,EAC1B,IAAI,EACJ,QAAQ,EAIT;QACC,QAAQ,OAAO;QACf,YAAY;IACd;IAEA,OAAO;QACL;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1850, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/data-table/data-table-column-header.tsx"], "sourcesContent": ["import {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from '@atlas//components/ui/dropdown-menu'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { Column } from '@tanstack/react-table'\r\nimport { ArrowDown, ArrowUp, ChevronsUpDown, EyeOff } from 'lucide-react'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ninterface DataTableColumnHeaderProps<TData, TValue>\r\n  extends React.HTMLAttributes<HTMLDivElement> {\r\n  column: Column<TData, TValue>\r\n  title: string\r\n}\r\n\r\nexport function DataTableColumnHeader<TData, TValue>({\r\n  column,\r\n  title,\r\n  className,\r\n}: DataTableColumnHeaderProps<TData, TValue>) {\r\n  const t = useTranslations('components.data-table.column-header')\r\n\r\n  if (!column.getCanSort()) {\r\n    return <div className={cn(className)}>{title}</div>\r\n  }\r\n\r\n  return (\r\n    <div className={cn('flex items-center space-x-2', className)}>\r\n      <DropdownMenu>\r\n        <DropdownMenuTrigger asChild>\r\n          <Button\r\n            variant=\"ghost\"\r\n            size=\"sm\"\r\n            className=\"-ml-3 h-8 data-[state=open]:bg-accent\"\r\n          >\r\n            <span>{title}</span>\r\n            {column.getIsSorted() === 'desc' ? (\r\n              <ArrowDown />\r\n            ) : column.getIsSorted() === 'asc' ? (\r\n              <ArrowUp />\r\n            ) : (\r\n              <ChevronsUpDown />\r\n            )}\r\n          </Button>\r\n        </DropdownMenuTrigger>\r\n        <DropdownMenuContent align=\"start\">\r\n          <DropdownMenuItem onClick={() => column.toggleSorting(false)}>\r\n            <ArrowUp className=\"h-3.5 w-3.5 text-muted-foreground/70\" />\r\n            {t('buttons.sort-ascending')}\r\n          </DropdownMenuItem>\r\n          <DropdownMenuItem onClick={() => column.toggleSorting(true)}>\r\n            <ArrowDown className=\"h-3.5 w-3.5 text-muted-foreground/70\" />\r\n            {t('buttons.sort-descending')}\r\n          </DropdownMenuItem>\r\n          <DropdownMenuSeparator />\r\n          <DropdownMenuItem onClick={() => column.toggleVisibility(false)}>\r\n            <EyeOff className=\"h-3.5 w-3.5 text-muted-foreground/70\" />\r\n            {t('buttons.hide-column')}\r\n          </DropdownMenuItem>\r\n        </DropdownMenuContent>\r\n      </DropdownMenu>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAOA;AACA;AAEA;AAAA;AAAA;AAAA;AACA;;;;;;;AAQO,SAAS,sBAAqC,EACnD,MAAM,EACN,KAAK,EACL,SAAS,EACiC;IAC1C,MAAM,IAAI,IAAA,yNAAe,EAAC;IAE1B,IAAI,CAAC,OAAO,UAAU,IAAI;QACxB,qBAAO,8OAAC;YAAI,WAAW,IAAA,yHAAE,EAAC;sBAAa;;;;;;IACzC;IAEA,qBACE,8OAAC;QAAI,WAAW,IAAA,yHAAE,EAAC,+BAA+B;kBAChD,cAAA,8OAAC,4JAAY;;8BACX,8OAAC,mKAAmB;oBAAC,OAAO;8BAC1B,cAAA,8OAAC,4IAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;;0CAEV,8OAAC;0CAAM;;;;;;4BACN,OAAO,WAAW,OAAO,uBACxB,8OAAC,6NAAS;;;;uCACR,OAAO,WAAW,OAAO,sBAC3B,8OAAC,uNAAO;;;;qDAER,8OAAC,gPAAc;;;;;;;;;;;;;;;;8BAIrB,8OAAC,mKAAmB;oBAAC,OAAM;;sCACzB,8OAAC,gKAAgB;4BAAC,SAAS,IAAM,OAAO,aAAa,CAAC;;8CACpD,8OAAC,uNAAO;oCAAC,WAAU;;;;;;gCAClB,EAAE;;;;;;;sCAEL,8OAAC,gKAAgB;4BAAC,SAAS,IAAM,OAAO,aAAa,CAAC;;8CACpD,8OAAC,6NAAS;oCAAC,WAAU;;;;;;gCACpB,EAAE;;;;;;;sCAEL,8OAAC,qKAAqB;;;;;sCACtB,8OAAC,gKAAgB;4BAAC,SAAS,IAAM,OAAO,gBAAgB,CAAC;;8CACvD,8OAAC,oNAAM;oCAAC,WAAU;;;;;;gCACjB,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAMf", "debugId": null}}, {"offset": {"line": 2004, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/projects/project-detail/modules/use-modules-columns.tsx"], "sourcesContent": ["import { DataTableColumnHeader } from '@atlas/components/common/atlas/data-table/data-table-column-header'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { ColumnDef } from '@tanstack/react-table'\r\nimport Link from 'next/dist/client/link'\r\nimport { useFormatter, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  projectId: Project['id']\r\n}\r\n\r\nexport const useModulesColumns = ({ projectId }: Props) => {\r\n  const t = useTranslations('tables.modules.columns')\r\n  const tModuleType = useTranslations('enums.module-type')\r\n  const format = useFormatter()\r\n\r\n  return [\r\n    {\r\n      accessorKey: 'name',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title={t('name.header')} />\r\n      ),\r\n      meta: {\r\n        i18nLabel: t('name.header'),\r\n      },\r\n      cell: ({ row }) => {\r\n        const module = row.original\r\n        const name = module.name ?? t('name.unnamed')\r\n\r\n        return (\r\n          <Link\r\n            href={`/dashboard/projects/${projectId}/modules/${module.id}`}\r\n            className=\"font-medium text-primary underline-offset-4 hover:underline\"\r\n            title={name}\r\n            prefetch={false}\r\n          >\r\n            <span className=\"block max-w-[200px] truncate sm:max-w-[300px] md:max-w-[400px]\">\r\n              {name}\r\n            </span>\r\n          </Link>\r\n        )\r\n      },\r\n    },\r\n    {\r\n      accessorKey: 'type',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title={t('type.header')} />\r\n      ),\r\n      meta: {\r\n        i18nLabel: t('type.header'),\r\n      },\r\n      cell: ({ row }) => tModuleType(row.original.type),\r\n    },\r\n    {\r\n      accessorKey: 'description',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader\r\n          column={column}\r\n          title={t('description.header')}\r\n        />\r\n      ),\r\n      meta: {\r\n        i18nLabel: t('description.header'),\r\n      },\r\n    },\r\n    {\r\n      accessorKey: 'createdAt',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader column={column} title={t('created-at.header')} />\r\n      ),\r\n      cell: ({ row }) => {\r\n        if (!row.original.createdAt) {\r\n          return '-'\r\n        }\r\n\r\n        return format.dateTime(row.original.createdAt, {\r\n          year: 'numeric',\r\n          month: 'long',\r\n          day: 'numeric',\r\n        })\r\n      },\r\n      meta: {\r\n        i18nLabel: t('created-at.header'),\r\n      },\r\n    },\r\n    {\r\n      accessorKey: 'lastModified',\r\n      header: ({ column }) => (\r\n        <DataTableColumnHeader\r\n          column={column}\r\n          title={t('last-modified.header')}\r\n        />\r\n      ),\r\n      cell: ({ row }) => {\r\n        if (!row.original.lastModified) {\r\n          return '-'\r\n        }\r\n\r\n        return format.dateTime(row.original.lastModified, {\r\n          year: 'numeric',\r\n          month: 'long',\r\n          day: 'numeric',\r\n        })\r\n      },\r\n      meta: {\r\n        i18nLabel: t('last-modified.header'),\r\n      },\r\n    },\r\n  ] as ColumnDef<Module>[]\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAIA;AACA;;;;;AAMO,MAAM,oBAAoB,CAAC,EAAE,SAAS,EAAS;IACpD,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,cAAc,IAAA,yNAAe,EAAC;IACpC,MAAM,SAAS,IAAA,sNAAY;IAE3B,OAAO;QACL;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,8OAAC,oNAAqB;oBAAC,QAAQ;oBAAQ,OAAO,EAAE;;;;;;YAElD,MAAM;gBACJ,WAAW,EAAE;YACf;YACA,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,MAAM,SAAS,IAAI,QAAQ;gBAC3B,MAAM,OAAO,OAAO,IAAI,IAAI,EAAE;gBAE9B,qBACE,8OAAC,yJAAI;oBACH,MAAM,CAAC,oBAAoB,EAAE,UAAU,SAAS,EAAE,OAAO,EAAE,EAAE;oBAC7D,WAAU;oBACV,OAAO;oBACP,UAAU;8BAEV,cAAA,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;YAIT;QACF;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,8OAAC,oNAAqB;oBAAC,QAAQ;oBAAQ,OAAO,EAAE;;;;;;YAElD,MAAM;gBACJ,WAAW,EAAE;YACf;YACA,MAAM,CAAC,EAAE,GAAG,EAAE,GAAK,YAAY,IAAI,QAAQ,CAAC,IAAI;QAClD;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,8OAAC,oNAAqB;oBACpB,QAAQ;oBACR,OAAO,EAAE;;;;;;YAGb,MAAM;gBACJ,WAAW,EAAE;YACf;QACF;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,8OAAC,oNAAqB;oBAAC,QAAQ;oBAAQ,OAAO,EAAE;;;;;;YAElD,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,IAAI,CAAC,IAAI,QAAQ,CAAC,SAAS,EAAE;oBAC3B,OAAO;gBACT;gBAEA,OAAO,OAAO,QAAQ,CAAC,IAAI,QAAQ,CAAC,SAAS,EAAE;oBAC7C,MAAM;oBACN,OAAO;oBACP,KAAK;gBACP;YACF;YACA,MAAM;gBACJ,WAAW,EAAE;YACf;QACF;QACA;YACE,aAAa;YACb,QAAQ,CAAC,EAAE,MAAM,EAAE,iBACjB,8OAAC,oNAAqB;oBACpB,QAAQ;oBACR,OAAO,EAAE;;;;;;YAGb,MAAM,CAAC,EAAE,GAAG,EAAE;gBACZ,IAAI,CAAC,IAAI,QAAQ,CAAC,YAAY,EAAE;oBAC9B,OAAO;gBACT;gBAEA,OAAO,OAAO,QAAQ,CAAC,IAAI,QAAQ,CAAC,YAAY,EAAE;oBAChD,MAAM;oBACN,OAAO;oBACP,KAAK;gBACP;YACF;YACA,MAAM;gBACJ,WAAW,EAAE;YACf;QACF;KACD;AACH", "debugId": null}}, {"offset": {"line": 2140, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/projects/project-detail/modules/project-modules-section.tsx"], "sourcesContent": ["import { DataTable } from '@atlas/components/common/atlas/data-table/data-table'\r\nimport { CreateModuleModal } from '@atlas/components/common/atlas/projects/project-detail/modules/create-module-modal'\r\nimport { useModulePagination } from '@atlas/components/common/atlas/projects/project-detail/modules/use-module-pagination'\r\nimport { useModulesColumns } from '@atlas/components/common/atlas/projects/project-detail/modules/use-modules-columns'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Input } from '@atlas/components/ui/input'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useAtom } from 'jotai'\r\nimport { atom } from 'jotai/index'\r\nimport { PlusCircle } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useMemo, useState } from 'react'\r\n\r\ntype Props = {\r\n  project: Project\r\n  session: Session\r\n}\r\n\r\nexport const createModuleModalOpenAtom = atom<boolean>(false)\r\n\r\nexport const ProjectModulesSection = ({ project, session }: Props) => {\r\n  const t = useTranslations('components.modules')\r\n  const tActions = useTranslations('actions')\r\n  const columns = useModulesColumns({ projectId: project.id })\r\n  const [createModuleModalOpen, setCreateModuleModalOpen] = useAtom(\r\n    createModuleModalOpenAtom,\r\n  )\r\n  const [nameFilter, setNameFilter] = useState('')\r\n  const filteredModules = useMemo(\r\n    () =>\r\n      project.modules?.filter(m =>\r\n        m.name.toLowerCase().includes(nameFilter.toLowerCase()),\r\n      ) ?? [],\r\n    [project.modules, nameFilter],\r\n  )\r\n\r\n  const {\r\n    page,\r\n    pageSize,\r\n    onPaginationChange,\r\n    currentPageModules: filteredAndPaginatedModules,\r\n  } = useModulePagination({ modules: filteredModules })\r\n  return (\r\n    <>\r\n      {createModuleModalOpen && (\r\n        <CreateModuleModal project={project} session={session} />\r\n      )}\r\n      <div className=\"space-y-4 rounded-md border p-4\">\r\n        <h3 className=\"text-lg font-medium\">{t('title')}</h3>\r\n        <Button className=\"w-40\" onClick={() => setCreateModuleModalOpen(true)}>\r\n          <PlusCircle />\r\n          {tActions('create-module.label')}\r\n        </Button>\r\n        <Input\r\n          className=\"sm:w-1/3 xs:w-full\"\r\n          placeholder={t('name-filter.placeholder')}\r\n          value={nameFilter}\r\n          onChange={e => setNameFilter(e.target.value)}\r\n        />\r\n        {filteredAndPaginatedModules && (\r\n          <DataTable\r\n            page={page}\r\n            perPage={pageSize}\r\n            onPaginationChange={onPaginationChange}\r\n            columns={columns}\r\n            data={filteredAndPaginatedModules}\r\n            rowCount={filteredModules.length}\r\n          />\r\n        )}\r\n      </div>\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;;;;;;;;;;;;;AAOO,MAAM,4BAA4B,IAAA,gJAAI,EAAU;AAEhD,MAAM,wBAAwB,CAAC,EAAE,OAAO,EAAE,OAAO,EAAS;IAC/D,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,WAAW,IAAA,yNAAe,EAAC;IACjC,MAAM,UAAU,IAAA,mOAAiB,EAAC;QAAE,WAAW,QAAQ,EAAE;IAAC;IAC1D,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,IAAA,iJAAO,EAC/D;IAEF,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAC;IAC7C,MAAM,kBAAkB,IAAA,gNAAO,EAC7B,IACE,QAAQ,OAAO,EAAE,OAAO,CAAA,IACtB,EAAE,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,QACjD,EAAE,EACT;QAAC,QAAQ,OAAO;QAAE;KAAW;IAG/B,MAAM,EACJ,IAAI,EACJ,QAAQ,EACR,kBAAkB,EAClB,oBAAoB,2BAA2B,EAChD,GAAG,IAAA,uOAAmB,EAAC;QAAE,SAAS;IAAgB;IACnD,qBACE;;YACG,uCACC,8OAAC,mOAAiB;gBAAC,SAAS;gBAAS,SAAS;;;;;;0BAEhD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuB,EAAE;;;;;;kCACvC,8OAAC,4IAAM;wBAAC,WAAU;wBAAO,SAAS,IAAM,yBAAyB;;0CAC/D,8OAAC,gOAAU;;;;;4BACV,SAAS;;;;;;;kCAEZ,8OAAC,0IAAK;wBACJ,WAAU;wBACV,aAAa,EAAE;wBACf,OAAO;wBACP,UAAU,CAAA,IAAK,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;oBAE5C,6CACC,8OAAC,oLAAS;wBACR,MAAM;wBACN,SAAS;wBACT,oBAAoB;wBACpB,SAAS;wBACT,MAAM;wBACN,UAAU,gBAAgB,MAAM;;;;;;;;;;;;;;AAM5C", "debugId": null}}, {"offset": {"line": 2258, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/alert.tsx"], "sourcesContent": ["import { cn } from '@atlas/lib/utils'\r\nimport { cva, type VariantProps } from 'class-variance-authority'\r\nimport type * as React from 'react'\r\n\r\nconst alertVariants = cva(\r\n  'relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current',\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: 'bg-card text-card-foreground',\r\n        destructive:\r\n          'text-destructive bg-card [&>svg]:text-current *:data-[slot=alert-description]:text-destructive/90',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n    },\r\n  },\r\n)\r\n\r\nfunction Alert({\r\n  className,\r\n  variant,\r\n  ...props\r\n}: React.ComponentProps<'div'> & VariantProps<typeof alertVariants>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert\"\r\n      role=\"alert\"\r\n      className={cn(alertVariants({ variant }), className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-title\"\r\n      className={cn(\r\n        'col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction AlertDescription({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"alert-description\"\r\n      className={cn(\r\n        'text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAGA,MAAM,gBAAgB,IAAA,uKAAG,EACvB,qOACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,GAAG,OAC8D;IACjE,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,WAAW,IAAA,yHAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,+DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACyB;IAC5B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,kGACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2324, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/projects/project-detail/project-detail-error-component.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { Alert, AlertDescription, AlertTitle } from '@atlas/components/ui/alert'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Link } from '@atlas/i18n/routing'\r\nimport type { ApiError, ValidationError } from '@atlas/types'\r\nimport { AlertTriangle } from 'lucide-react'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ninterface DetailErrorProps {\r\n  reset?: () => void\r\n  error?: ApiError | ValidationError\r\n}\r\n\r\nexport const ProjectDetailErrorComponent = ({ reset }: DetailErrorProps) => {\r\n  const t = useTranslations('pages.project-detail.error')\r\n\r\n  return (\r\n    <div className=\"flex items-center justify-center h-full p-4 md:p-8\">\r\n      <Alert variant=\"destructive\" className=\"max-w-md w-full\">\r\n        <AlertTriangle className=\"h-5 w-5\" />\r\n        <AlertTitle className=\"font-bold text-lg mb-1\">{t('title')}</AlertTitle>\r\n        <AlertDescription>\r\n          <p className=\"mb-4\">{t('description')}</p>\r\n          <div className=\"flex justify-end gap-3\">\r\n            {reset && (\r\n              <Button variant=\"outline\" size=\"sm\" onClick={() => reset()}>\r\n                {t('actions.retry.label')}\r\n              </Button>\r\n            )}\r\n            <Link href=\"/dashboard\">\r\n              <Button variant=\"outline\" size=\"sm\">\r\n                {t('actions.go-to-home.label')}\r\n              </Button>\r\n            </Link>\r\n          </div>\r\n        </AlertDescription>\r\n      </Alert>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AACA;AAPA;;;;;;;AAcO,MAAM,8BAA8B,CAAC,EAAE,KAAK,EAAoB;IACrE,MAAM,IAAI,IAAA,yNAAe,EAAC;IAE1B,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,0IAAK;YAAC,SAAQ;YAAc,WAAU;;8BACrC,8OAAC,yOAAa;oBAAC,WAAU;;;;;;8BACzB,8OAAC,+IAAU;oBAAC,WAAU;8BAA0B,EAAE;;;;;;8BAClD,8OAAC,qJAAgB;;sCACf,8OAAC;4BAAE,WAAU;sCAAQ,EAAE;;;;;;sCACvB,8OAAC;4BAAI,WAAU;;gCACZ,uBACC,8OAAC,4IAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,SAAS,IAAM;8CAChD,EAAE;;;;;;8CAGP,8OAAC,8HAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,4IAAM;wCAAC,SAAQ;wCAAU,MAAK;kDAC5B,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB", "debugId": null}}, {"offset": {"line": 2431, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/pages/dashboard/dashboard-content/projects/projects-delete-alert.tsx"], "sourcesContent": ["'use client'\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction, // <-- The component we are modifying\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@atlas/components/ui/alert-dialog'\r\nimport { buttonVariants } from '@atlas/components/ui/button'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  project: Project\r\n  open: boolean\r\n  onOpenChange: (open: boolean) => void\r\n  onConfirm: () => void\r\n}\r\n\r\nexport const ProjectDeleteAlert = ({\r\n  project,\r\n  open,\r\n  onOpenChange,\r\n  onConfirm,\r\n}: Props) => {\r\n  const t = useTranslations('components.projects.delete-alert')\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  const handleConfirm = () => {\r\n    onConfirm()\r\n    onOpenChange(false)\r\n  }\r\n\r\n  const handleCancel = () => {\r\n    onOpenChange(false)\r\n  }\r\n\r\n  return (\r\n    <AlertDialog open={open} onOpenChange={onOpenChange}>\r\n      <AlertDialogContent>\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>{t('title')}</AlertDialogTitle>\r\n          <AlertDialogDescription>\r\n            {t('description', {\r\n              projectName: project.constructionSiteName ?? '',\r\n            })}\r\n          </AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <AlertDialogFooter>\r\n          <AlertDialogCancel onClick={handleCancel}>\r\n            {tCommon('cancel')}\r\n          </AlertDialogCancel>\r\n          <AlertDialogAction\r\n            className={buttonVariants({ variant: 'destructive' })}\r\n            onClick={handleConfirm}\r\n          >\r\n            {tCommon('delete')}\r\n          </AlertDialogAction>\r\n        </AlertDialogFooter>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AAUA;AAEA;AAbA;;;;;AAsBO,MAAM,qBAAqB,CAAC,EACjC,OAAO,EACP,IAAI,EACJ,YAAY,EACZ,SAAS,EACH;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAEhC,MAAM,gBAAgB;QACpB;QACA,aAAa;IACf;IAEA,MAAM,eAAe;QACnB,aAAa;IACf;IAEA,qBACE,8OAAC,0JAAW;QAAC,MAAM;QAAM,cAAc;kBACrC,cAAA,8OAAC,iKAAkB;;8BACjB,8OAAC,gKAAiB;;sCAChB,8OAAC,+JAAgB;sCAAE,EAAE;;;;;;sCACrB,8OAAC,qKAAsB;sCACpB,EAAE,eAAe;gCAChB,aAAa,QAAQ,oBAAoB,IAAI;4BAC/C;;;;;;;;;;;;8BAGJ,8OAAC,gKAAiB;;sCAChB,8OAAC,gKAAiB;4BAAC,SAAS;sCACzB,QAAQ;;;;;;sCAEX,8OAAC,gKAAiB;4BAChB,WAAW,IAAA,oJAAc,EAAC;gCAAE,SAAS;4BAAc;4BACnD,SAAS;sCAER,QAAQ;;;;;;;;;;;;;;;;;;;;;;;AAMrB", "debugId": null}}, {"offset": {"line": 2526, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/api/projects/endpoints/delete-project.ts"], "sourcesContent": ["import { handleApiError } from '@atlas/lib/api/apiErrors'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { ApiError } from '@atlas/types'\r\nimport type { AxiosResponse } from 'axios'\r\nimport { type TaskEither, tryCatch } from 'fp-ts/TaskEither'\r\nimport { apiClient } from '../../httpClient'\r\n\r\nexport const deleteProjectById = (\r\n  token: string,\r\n  id: Project['id'],\r\n): TaskEither<ApiError, AxiosResponse> =>\r\n  tryCatch(\r\n    () =>\r\n      apiClient.delete(`/api/v2/projects/${id}`, {\r\n        ...(token && {\r\n          headers: { Authorization: `Bearer ${token}` },\r\n        }),\r\n      }),\r\n    e => handleApiError(e),\r\n  )\r\n"], "names": [], "mappings": ";;;;AAAA;AAIA;AACA;;;;AAEO,MAAM,oBAAoB,CAC/B,OACA,KAEA,IAAA,yJAAQ,EACN,IACE,4IAAS,CAAC,MAAM,CAAC,CAAC,iBAAiB,EAAE,IAAI,EAAE;YACzC,GAAI,SAAS;gBACX,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C,CAAC;QACH,IACF,CAAA,IAAK,IAAA,gJAAc,EAAC", "debugId": null}}, {"offset": {"line": 2547, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/mutation/projects/use-delete-project-mutation.tsx"], "sourcesContent": ["import { deleteProjectById } from '@atlas/lib/api/projects/endpoints/delete-project'\r\nimport type { ApiError } from '@atlas/types'\r\nimport {\r\n  type UseMutationOptions,\r\n  useMutation,\r\n  useQueryClient,\r\n} from '@tanstack/react-query'\r\nimport * as E from 'fp-ts/Either'\r\nimport { pipe } from 'fp-ts/function'\r\n\r\ntype DeleteProjectError = ApiError\r\n\r\nconst mutationFn =\r\n  (token: string) =>\r\n  async (id: string): Promise<void> => {\r\n    const result = await deleteProjectById(token, id)()\r\n\r\n    return pipe(\r\n      result,\r\n      E.match(\r\n        (error: DeleteProjectError) => {\r\n          throw error\r\n        },\r\n        () => {\r\n          return\r\n        },\r\n      ),\r\n    )\r\n  }\r\n\r\nexport const useDeleteProjectMutation = (\r\n  token: string,\r\n  options?: Omit<\r\n    UseMutationOptions<void, DeleteProjectError, string>,\r\n    'mutationFn'\r\n  >,\r\n) => {\r\n  const queryClient = useQueryClient()\r\n\r\n  return useMutation<void, DeleteProjectError, string>({\r\n    ...options,\r\n    mutationFn: mutationFn(token),\r\n    onSuccess: (data, variables, context) => {\r\n      queryClient.invalidateQueries({ queryKey: ['projects'] })\r\n      options?.onSuccess?.(data, variables, context)\r\n    },\r\n  })\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AAAA;AAKA;AACA;;;;;AAIA,MAAM,aACJ,CAAC,QACD,OAAO;QACL,MAAM,SAAS,MAAM,IAAA,oLAAiB,EAAC,OAAO;QAE9C,OAAO,IAAA,mJAAI,EACT,QACA,kJAAO,CACL,CAAC;YACC,MAAM;QACR,GACA;YACE;QACF;IAGN;AAEK,MAAM,2BAA2B,CACtC,OACA;IAKA,MAAM,cAAc,IAAA,wMAAc;IAElC,OAAO,IAAA,6LAAW,EAAmC;QACnD,GAAG,OAAO;QACV,YAAY,WAAW;QACvB,WAAW,CAAC,MAAM,WAAW;YAC3B,YAAY,iBAAiB,CAAC;gBAAE,UAAU;oBAAC;iBAAW;YAAC;YACvD,SAAS,YAAY,MAAM,WAAW;QACxC;IACF;AACF", "debugId": null}}, {"offset": {"line": 2587, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/api/projects/endpoints/get-project.ts"], "sourcesContent": ["import { handleApiError } from '@atlas/lib/api/apiErrors'\r\nimport {\r\n  type Project,\r\n  projectSchema,\r\n} from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { ApiError, ValidationError } from '@atlas/types'\r\nimport type { AxiosResponse } from 'axios'\r\nimport { pipe } from 'fp-ts/function'\r\nimport { map, type TaskEither, tryCatch } from 'fp-ts/TaskEither'\r\nimport { apiClient } from '../../httpClient'\r\n\r\nconst validate = (data: unknown): Project => {\r\n  const parse = projectSchema.safeParse(data)\r\n  if (!parse.success) {\r\n    throw {\r\n      type: 'ValidationError',\r\n      code: 'MALFORMED_RESPONSE',\r\n      message: parse.error.message,\r\n    } as ValidationError\r\n  }\r\n  return parse.data\r\n}\r\n\r\nexport const fetch = (\r\n  token: string,\r\n  id: Project['id'],\r\n): TaskEither<ApiError, AxiosResponse> =>\r\n  tryCatch(\r\n    () =>\r\n      apiClient.get(`/api/v2/projects/${id}`, {\r\n        ...(token && {\r\n          headers: { Authorization: `Bearer ${token}` },\r\n        }),\r\n      }),\r\n    e => handleApiError(e),\r\n  )\r\n\r\nexport const getProjectById = (\r\n  token: string,\r\n  projectId: Project['id'],\r\n): TaskEither<ApiError | ValidationError, Project> =>\r\n  pipe(\r\n    fetch(token, projectId),\r\n    map(a => a.data),\r\n    map(validate),\r\n  )\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAMA;AACA;AACA;;;;;;AAEA,MAAM,WAAW,CAAC;IAChB,MAAM,QAAQ,qKAAa,CAAC,SAAS,CAAC;IACtC,IAAI,CAAC,MAAM,OAAO,EAAE;QAClB,MAAM;YACJ,MAAM;YACN,MAAM;YACN,SAAS,MAAM,KAAK,CAAC,OAAO;QAC9B;IACF;IACA,OAAO,MAAM,IAAI;AACnB;AAEO,MAAM,QAAQ,CACnB,OACA,KAEA,IAAA,yJAAQ,EACN,IACE,4IAAS,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,IAAI,EAAE;YACtC,GAAI,SAAS;gBACX,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C,CAAC;QACH,IACF,CAAA,IAAK,IAAA,gJAAc,EAAC;AAGjB,MAAM,iBAAiB,CAC5B,OACA,YAEA,IAAA,mJAAI,EACF,MAAM,OAAO,YACb,IAAA,oJAAG,EAAC,CAAA,IAAK,EAAE,IAAI,GACf,IAAA,oJAAG,EAAC", "debugId": null}}, {"offset": {"line": 2626, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/query/projects/get-project.tsx"], "sourcesContent": ["import { getProjectById } from '@atlas/lib/api/projects/endpoints/get-project'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { fold } from 'fp-ts/Either'\r\nimport { pipe } from 'fp-ts/function'\r\nimport type { Session } from 'next-auth'\r\n\r\nexport const getProjectQuery = async (\r\n  session: Session,\r\n  projectId: Project['id'],\r\n): Promise<Project> => {\r\n  const result = await getProjectById(session.accessToken, projectId)()\r\n\r\n  return pipe(\r\n    result,\r\n    fold(\r\n      err => {\r\n        throw err\r\n      },\r\n      data => data,\r\n    ),\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;;;;AAGO,MAAM,kBAAkB,OAC7B,SACA;IAEA,MAAM,SAAS,MAAM,IAAA,8KAAc,EAAC,QAAQ,WAAW,EAAE;IAEzD,OAAO,IAAA,mJAAI,EACT,QACA,IAAA,iJAAI,EACF,CAAA;QACE,MAAM;IACR,GACA,CAAA,OAAQ;AAGd", "debugId": null}}, {"offset": {"line": 2646, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/query/projects/use-project.tsx"], "sourcesContent": ["import type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { getProjectQuery } from '@atlas/lib/query/projects/get-project'\r\nimport type { ApiError, ValidationError } from '@atlas/types'\r\nimport { useQuery } from '@tanstack/react-query'\r\nimport type { Session } from 'next-auth'\r\n\r\nexport const useProject = ({\r\n  session,\r\n  projectId,\r\n  initialData,\r\n}: {\r\n  session: Session\r\n  projectId: Project['id']\r\n  initialData?: Project\r\n}) =>\r\n  useQuery<Project, ApiError | ValidationError>({\r\n    queryKey: ['projects', projectId],\r\n    queryFn: () => {\r\n      return getProjectQuery(session, projectId)\r\n    },\r\n    retry: 1,\r\n    initialData,\r\n  })\r\n"], "names": [], "mappings": ";;;;AACA;AAEA;;;AAGO,MAAM,aAAa,CAAC,EACzB,OAAO,EACP,SAAS,EACT,WAAW,EAKZ,GACC,IAAA,uLAAQ,EAAsC;QAC5C,UAAU;YAAC;YAAY;SAAU;QACjC,SAAS;YACP,OAAO,IAAA,qKAAe,EAAC,SAAS;QAClC;QACA,OAAO;QACP;IACF", "debugId": null}}, {"offset": {"line": 2669, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/card.tsx"], "sourcesContent": ["import { cn } from '@atlas/lib/utils'\r\nimport type * as React from 'react'\r\n\r\nfunction Card({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        'bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardHeader({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        '@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardTitle({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn('leading-none font-semibold', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardDescription({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn('text-muted-foreground text-sm', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardAction({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        'col-start-2 row-span-2 row-start-1 self-start justify-self-end',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardContent({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-content\"\r\n      className={cn('px-6', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction CardFooter({ className, ...props }: React.ComponentProps<'div'>) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn('flex items-center px-6 [.border-t]:pt-6', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;;;AAGA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2771, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/projects/project-detail/project-detail-header.tsx"], "sourcesContent": ["import { projectDetailToDelete<PERSON>tom } from '@atlas/components/common/atlas/projects/project-detail/project-detail-content'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Card, CardContent, CardHeader } from '@atlas/components/ui/card'\r\nimport { useRouter } from '@atlas/i18n/routing'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport { useSetAtom } from 'jotai'\r\nimport { useFormatter, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  project: Project\r\n  className?: string\r\n}\r\n\r\nexport const ProjectDetailHeader = ({ project, className }: Props) => {\r\n  const t = useTranslations('components.project-detail.header')\r\n  const format = useFormatter()\r\n  const setProjectToDelete = useSetAtom(projectDetailToDeleteAtom)\r\n  const router = useRouter()\r\n\r\n  const handleEditProjectClick = () => {\r\n    return router.push(`/dashboard/projects/${project.id}/edit`)\r\n  }\r\n\r\n  return (\r\n    <Card className={cn('top-6 z-10 mb-6', className)}>\r\n      <CardHeader className=\"flex flex-col sm:flex-row items-center justify-between pb-2 pt-6 pl-6 pr-6\">\r\n        <h1 className=\"text-2xl font-semibold tracking-tight md:text-3xl\">\r\n          {project.constructionSiteName ?? t('unnamed-project')}\r\n        </h1>\r\n        <div className={cn('flex flex-col sm:flex-row gap-2')}>\r\n          <Button size=\"sm\" onClick={handleEditProjectClick}>\r\n            {t('edit-project')}\r\n          </Button>\r\n          <Button\r\n            variant=\"destructive\"\r\n            size=\"sm\"\r\n            onClick={() => setProjectToDelete(project)}\r\n          >\r\n            {t('delete-project')}\r\n          </Button>\r\n        </div>\r\n      </CardHeader>\r\n      <CardContent className=\" flex flex-col gap-1 pb-6 pl-6 pr-6 pt-0\">\r\n        <div className=\"flex flex-wrap items-center gap-x-4 gap-y-1 text-sm text-muted-foreground\">\r\n          {project.company && (\r\n            <span>\r\n              {t('client-label')}: {project.company}\r\n            </span>\r\n          )}\r\n          {project.planner && (\r\n            <span>\r\n              {t('manager-label')}: {project.planner}\r\n            </span>\r\n          )}\r\n          {(project.address || project.address) && (\r\n            <span>\r\n              {t('address-label')}: {project.address}\r\n            </span>\r\n          )}\r\n          {project.createdAt && (\r\n            <span>\r\n              {t('created-label')}:{' '}\r\n              {format.dateTime(project.createdAt, {\r\n                year: 'numeric',\r\n                month: 'long',\r\n                day: 'numeric',\r\n                hour: '2-digit',\r\n                minute: '2-digit',\r\n              })}\r\n            </span>\r\n          )}\r\n        </div>\r\n        {project.processingType && (\r\n          <div className=\"flex flex-wrap items-center gap-x-4 gap-y-1 text-sm text-muted-foreground\">\r\n            <span>\r\n              {t('processing-type')}: {project.processingType}\r\n            </span>\r\n          </div>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAEA;AACA;AACA;;;;;;;;;AAOO,MAAM,sBAAsB,CAAC,EAAE,OAAO,EAAE,SAAS,EAAS;IAC/D,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,SAAS,IAAA,sNAAY;IAC3B,MAAM,qBAAqB,IAAA,oJAAU,EAAC,mOAAyB;IAC/D,MAAM,SAAS,IAAA,mIAAS;IAExB,MAAM,yBAAyB;QAC7B,OAAO,OAAO,IAAI,CAAC,CAAC,oBAAoB,EAAE,QAAQ,EAAE,CAAC,KAAK,CAAC;IAC7D;IAEA,qBACE,8OAAC,wIAAI;QAAC,WAAW,IAAA,yHAAE,EAAC,mBAAmB;;0BACrC,8OAAC,8IAAU;gBAAC,WAAU;;kCACpB,8OAAC;wBAAG,WAAU;kCACX,QAAQ,oBAAoB,IAAI,EAAE;;;;;;kCAErC,8OAAC;wBAAI,WAAW,IAAA,yHAAE,EAAC;;0CACjB,8OAAC,4IAAM;gCAAC,MAAK;gCAAK,SAAS;0CACxB,EAAE;;;;;;0CAEL,8OAAC,4IAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,mBAAmB;0CAEjC,EAAE;;;;;;;;;;;;;;;;;;0BAIT,8OAAC,+IAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;;4BACZ,QAAQ,OAAO,kBACd,8OAAC;;oCACE,EAAE;oCAAgB;oCAAG,QAAQ,OAAO;;;;;;;4BAGxC,QAAQ,OAAO,kBACd,8OAAC;;oCACE,EAAE;oCAAiB;oCAAG,QAAQ,OAAO;;;;;;;4BAGzC,CAAC,QAAQ,OAAO,IAAI,QAAQ,OAAO,mBAClC,8OAAC;;oCACE,EAAE;oCAAiB;oCAAG,QAAQ,OAAO;;;;;;;4BAGzC,QAAQ,SAAS,kBAChB,8OAAC;;oCACE,EAAE;oCAAiB;oCAAE;oCACrB,OAAO,QAAQ,CAAC,QAAQ,SAAS,EAAE;wCAClC,MAAM;wCACN,OAAO;wCACP,KAAK;wCACL,MAAM;wCACN,QAAQ;oCACV;;;;;;;;;;;;;oBAIL,QAAQ,cAAc,kBACrB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;;gCACE,EAAE;gCAAmB;gCAAG,QAAQ,cAAc;;;;;;;;;;;;;;;;;;;;;;;;AAO7D", "debugId": null}}, {"offset": {"line": 2945, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/projects/project-detail/project-detail-content.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { HeaderSkeleton } from '@atlas/components/common/atlas/header-skeleton'\r\nimport { ProjectModulesSection } from '@atlas/components/common/atlas/projects/project-detail/modules/project-modules-section'\r\nimport { ProjectDetailErrorComponent } from '@atlas/components/common/atlas/projects/project-detail/project-detail-error-component'\r\nimport { ProjectDeleteAlert } from '@atlas/components/pages/dashboard/dashboard-content/projects/projects-delete-alert'\r\nimport { Alert, AlertDescription, AlertTitle } from '@atlas/components/ui/alert' // For error state\r\nimport { Skeleton } from '@atlas/components/ui/skeleton' // For loading state\r\nimport { useRouter } from '@atlas/i18n/routing'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useDeleteProjectMutation } from '@atlas/lib/mutation/projects/use-delete-project-mutation'\r\nimport { useProject } from '@atlas/lib/query/projects/use-project'\r\nimport { atom, useAtom } from 'jotai'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { toast } from 'sonner'\r\nimport { ProjectDetailHeader } from './project-detail-header'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: string\r\n  initialDataProject?: Project\r\n}\r\n\r\nexport const projectDetailToDeleteAtom = atom<Project | undefined>()\r\n\r\nexport const ProjectDetailContent = ({\r\n  projectId,\r\n  initialDataProject,\r\n  session,\r\n}: Props) => {\r\n  const router = useRouter()\r\n\r\n  const tMessages = useTranslations('actions.delete-project.messages')\r\n  const tDetail = useTranslations('components.project-detail')\r\n\r\n  const {\r\n    data: project,\r\n    error,\r\n    isLoading,\r\n  } = useProject({\r\n    session,\r\n    projectId,\r\n    initialData: initialDataProject,\r\n  })\r\n  const [projectToDelete, setProjectToDelete] = useAtom<Project | undefined>(\r\n    projectDetailToDeleteAtom,\r\n  )\r\n\r\n  const { mutate: deleteProject, isPending: isDeleting } =\r\n    useDeleteProjectMutation(session.accessToken, {\r\n      onSuccess: () => {\r\n        toast.success(tMessages('delete.success'))\r\n        router.push('/dashboard')\r\n      },\r\n      onError: err => {\r\n        toast.error(\r\n          tMessages('delete.failure', {\r\n            error: err.message || 'Unknown error',\r\n          }),\r\n        )\r\n      },\r\n    })\r\n\r\n  const handleConfirmDelete = () => {\r\n    if (projectToDelete) {\r\n      deleteProject(projectToDelete.id)\r\n    }\r\n    setProjectToDelete(undefined)\r\n  }\r\n\r\n  const handleOpenChange = (open: boolean) => {\r\n    if (!open) {\r\n      setProjectToDelete(undefined)\r\n    }\r\n  }\r\n\r\n  if (isLoading || isDeleting) {\r\n    return (\r\n      <div className=\"p-4 md:p-6\">\r\n        <HeaderSkeleton />\r\n        <Skeleton className=\"mt-4 h-20 w-full\" />\r\n        <Skeleton className=\"mt-4 h-20 w-full\" />\r\n      </div>\r\n    )\r\n  }\r\n\r\n  if (error) {\r\n    return <ProjectDetailErrorComponent error={error} />\r\n  }\r\n\r\n  if (!project) {\r\n    return (\r\n      <div className=\"p-4 md:p-6\">\r\n        <Alert variant=\"destructive\">\r\n          {/* Usa tDetail per le traduzioni locali */}\r\n          <AlertTitle>{tDetail('not-found.title')}</AlertTitle>\r\n          <AlertDescription>\r\n            {tDetail('not-found.description')}\r\n          </AlertDescription>\r\n        </Alert>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className=\"p-4 md:p-6\">\r\n      <ProjectDetailHeader project={project} />\r\n      <ProjectModulesSection project={project} session={session} />\r\n      {projectToDelete && (\r\n        <ProjectDeleteAlert\r\n          project={projectToDelete}\r\n          open={!!projectToDelete}\r\n          onOpenChange={handleOpenChange}\r\n          onConfirm={handleConfirmDelete}\r\n        />\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AACA;AACA,iOAAiF,kBAAkB;AACnG,uOAAyD,oBAAoB;AAC7E;AAEA;AACA;AACA;AAAA;AAEA;AACA;AACA;AAhBA;;;;;;;;;;;;;;;AAwBO,MAAM,4BAA4B,IAAA,gJAAI;AAEtC,MAAM,uBAAuB,CAAC,EACnC,SAAS,EACT,kBAAkB,EAClB,OAAO,EACD;IACN,MAAM,SAAS,IAAA,mIAAS;IAExB,MAAM,YAAY,IAAA,yNAAe,EAAC;IAClC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAEhC,MAAM,EACJ,MAAM,OAAO,EACb,KAAK,EACL,SAAS,EACV,GAAG,IAAA,gKAAU,EAAC;QACb;QACA;QACA,aAAa;IACf;IACA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,iJAAO,EACnD;IAGF,MAAM,EAAE,QAAQ,aAAa,EAAE,WAAW,UAAU,EAAE,GACpD,IAAA,uMAAwB,EAAC,QAAQ,WAAW,EAAE;QAC5C,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,UAAU;YACxB,OAAO,IAAI,CAAC;QACd;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CACT,UAAU,kBAAkB;gBAC1B,OAAO,IAAI,OAAO,IAAI;YACxB;QAEJ;IACF;IAEF,MAAM,sBAAsB;QAC1B,IAAI,iBAAiB;YACnB,cAAc,gBAAgB,EAAE;QAClC;QACA,mBAAmB;IACrB;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,MAAM;YACT,mBAAmB;QACrB;IACF;IAEA,IAAI,aAAa,YAAY;QAC3B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,6KAAc;;;;;8BACf,8OAAC,gJAAQ;oBAAC,WAAU;;;;;;8BACpB,8OAAC,gJAAQ;oBAAC,WAAU;;;;;;;;;;;;IAG1B;IAEA,IAAI,OAAO;QACT,qBAAO,8OAAC,gPAA2B;YAAC,OAAO;;;;;;IAC7C;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0IAAK;gBAAC,SAAQ;;kCAEb,8OAAC,+IAAU;kCAAE,QAAQ;;;;;;kCACrB,8OAAC,qJAAgB;kCACd,QAAQ;;;;;;;;;;;;;;;;;IAKnB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4NAAmB;gBAAC,SAAS;;;;;;0BAC9B,8OAAC,2OAAqB;gBAAC,SAAS;gBAAS,SAAS;;;;;;YACjD,iCACC,8OAAC,iOAAkB;gBACjB,SAAS;gBACT,MAAM,CAAC,CAAC;gBACR,cAAc;gBACd,WAAW;;;;;;;;;;;;AAKrB", "debugId": null}}]}