import { handleApiError } from '@atlas/lib/api/apiErrors'
import {
  type PaginatedProducts,
  paginatedProductSchema,
} from '@atlas/lib/api/products/schemas/product'
import type { ApiError, ValidationError } from '@atlas/types'
import type { AxiosResponse } from 'axios'
import { pipe } from 'fp-ts/function'
import { map, type TaskEither, tryCatch } from 'fp-ts/TaskEither'
import { apiClient } from '../../httpClient'

const validate = (data: unknown): PaginatedProducts => {
  console.log('🔍 Validating products data:', JSON.stringify(data, null, 2))
  const parse = paginatedProductSchema.safeParse(data)
  if (!parse.success) {
    console.error('❌ Validation failed:', parse.error)
    console.error(
      '❌ Validation errors:',
      JSON.stringify(parse.error.issues, null, 2),
    )
    throw {
      type: 'ValidationError',
      code: 'MALFORMED_RESPONSE',
      message: parse.error.message,
    } as ValidationError
  }
  console.log('✅ Validation successful')
  return parse.data
}

export const fetch = (
  token: string,
  productType: string,
  page: number = 0,
  pageSize = 10,
): TaskEither<ApiError, AxiosResponse> =>
  tryCatch(
    () =>
      apiClient.get(
        `/api/v2/products?productType=${productType}&page=${page}&size=${pageSize}`,
        {
          ...(token && {
            headers: { Authorization: `Bearer ${token}` },
          }),
        },
      ),
    e => handleApiError(e),
  )

export const getProductsByType = (
  token: string,
  productType: string,
  page: number,
  pageSize = 10,
): TaskEither<ApiError | ValidationError, PaginatedProducts> =>
  pipe(
    fetch(token, productType, page, pageSize),
    map(a => a.data),
    map(validate),
  )
