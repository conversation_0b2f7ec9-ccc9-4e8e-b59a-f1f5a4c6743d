{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/pillar/calculations/pillar-flexural-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  checkResult: boolean\r\n  resistantMoment: number\r\n  equilibrium: number\r\n}\r\n\r\nexport function PillarFlexuralResultCard({\r\n  checkResult,\r\n  resistantMoment,\r\n  equilibrium,\r\n}: Props) {\r\n  const t = useTranslations('components.calculations')\r\n  const locale = useLocale()\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('flexuralResult.title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('flexuralResult.resistantMoment')}:\r\n          </span>{' '}\r\n          <span>\r\n            {resistantMoment.toLocaleString(locale, {\r\n              maximumFractionDigits: 2,\r\n            })}{' '}\r\n            kNm\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('flexuralResult.equilibrium')}:\r\n          </span>{' '}\r\n          <span>\r\n            {equilibrium.toLocaleString(locale, { maximumFractionDigits: 2 })}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('flexuralResult.checkResult.label')}:\r\n          </span>{' '}\r\n          <Badge\r\n            className={cn(\r\n              'text-base px-3 py-1',\r\n              checkResult ? 'bg-green-600' : 'bg-red-600',\r\n            )}\r\n          >\r\n            {checkResult\r\n              ? t('flexuralResult.checkResult.satisfied')\r\n              : t('flexuralResult.checkResult.notSatisfied')}\r\n          </Badge>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AACA;AAAA;;;;;;AAQO,SAAS,yBAAyB,EACvC,WAAW,EACX,eAAe,EACf,WAAW,EACL;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,SAAS,IAAA,+KAAS;IAExB,qBACE,8OAAC,wIAAI;;0BACH,8OAAC,8IAAU;0BACT,cAAA,8OAAC,6IAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,8OAAC,+IAAW;gBAAC,WAAU;;kCACrB,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAkC;;;;;;;4BAC/B;0CACR,8OAAC;;oCACE,gBAAgB,cAAc,CAAC,QAAQ;wCACtC,uBAAuB;oCACzB;oCAAI;oCAAI;;;;;;;;;;;;;kCAIZ,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA8B;;;;;;;4BAC3B;0CACR,8OAAC;0CACE,YAAY,cAAc,CAAC,QAAQ;oCAAE,uBAAuB;gCAAE;;;;;;;;;;;;kCAGnE,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAoC;;;;;;;4BACjC;0CACR,8OAAC,0IAAK;gCACJ,WAAW,IAAA,yHAAE,EACX,uBACA,cAAc,iBAAiB;0CAGhC,cACG,EAAE,0CACF,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAMlB", "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/pillar/calculations/pillar-flexural-calculation.tsx"], "sourcesContent": ["import { PillarFlexuralResultCard } from '@atlas/components/common/atlas/module-detail/params-forms/pillar/calculations/pillar-flexural-result-card'\r\nimport { CustomProductSection } from '@atlas/components/common/atlas/product/custom-product-section'\r\nimport { ProductSummaryCard } from '@atlas/components/common/atlas/product/product-summary-card'\r\nimport { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport { productFiberType } from '@atlas/constants/product'\r\nimport type { ModuleWithParamsPillar } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useModuleCalculation } from '@atlas/lib/mutation/modules/use-module-calculation'\r\nimport { useProductsByCategory } from '@atlas/lib/query/products/use-products-by-category'\r\nimport {\r\n  type PillarFlexuralCalculationInput,\r\n  pillarFlexuralCalculationSchema,\r\n} from '@atlas/types/schemas/pillar-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useMemo } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  module: ModuleWithParamsPillar\r\n  session: Session\r\n  projectId: Project['id']\r\n}\r\n\r\nexport const PillarFlexuralCalculation = ({\r\n  session,\r\n  module,\r\n  projectId,\r\n}: Props) => {\r\n  const t = useTranslations('forms.calculations.pillar.flexural')\r\n  const tAction = useTranslations('actions.calculations.pillar.flexural')\r\n  const tCommon = useTranslations('actions.common')\r\n  const { flexuralVerifyExecutionInput, flexuralCalculationResult } = module\r\n  const form = useForm<PillarFlexuralCalculationInput>({\r\n    resolver: zodResolver(pillarFlexuralCalculationSchema),\r\n    defaultValues: {\r\n      calculationType: 'FLEXURAL_VERIFY',\r\n      input: {\r\n        product: {\r\n          id: flexuralVerifyExecutionInput?.product.id,\r\n          name: flexuralVerifyExecutionInput?.product.name,\r\n          sourceType:\r\n            flexuralVerifyExecutionInput?.product.id === 'custom'\r\n              ? 'CUSTOM'\r\n              : 'DATABASE',\r\n          thickness: flexuralVerifyExecutionInput?.product.thickness,\r\n          tensileStrength:\r\n            flexuralVerifyExecutionInput?.product.tensileStrength,\r\n          elasticModulus: flexuralVerifyExecutionInput?.product.elasticModulus,\r\n          fiberType:\r\n            flexuralVerifyExecutionInput?.product.fiberType ??\r\n            productFiberType.CARBON,\r\n        },\r\n        stripWidth: flexuralVerifyExecutionInput?.stripWidth ?? 1,\r\n        layersNumber: flexuralVerifyExecutionInput?.layersNumber ?? 2,\r\n        bendingMoment: flexuralVerifyExecutionInput?.bendingMoment ?? 10,\r\n      },\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useModuleCalculation(session.accessToken, {\r\n    onSuccess: () => {\r\n      toast.success(tAction('calculate.success'))\r\n    },\r\n    onError: error => {\r\n      toast.error(tAction('calculate.failure', { error: error.message }))\r\n    },\r\n  })\r\n\r\n  const handleFormSubmit = (body: PillarFlexuralCalculationInput) => {\r\n    mutate({ projectId, moduleId: module.id, body })\r\n  }\r\n\r\n  const {\r\n    data: products,\r\n    isError: errorGettingProducts,\r\n    isLoading: isLoadingProducts,\r\n  } = useProductsByCategory(session, 'PILLAR', 0, 100)\r\n\r\n  const productsOptions = [\r\n    ...(products?.content.map(m => ({\r\n      value: m.id,\r\n      label: m.name ?? t('product.unnamed'),\r\n    })) ?? []),\r\n    { value: 'custom', label: t('product.custom') },\r\n  ]\r\n\r\n  const [productId] = form.watch(['input.product.id'])\r\n\r\n  const selectedProduct = useMemo(\r\n    () => products?.content.find(p => p.id === productId),\r\n    [productId, products],\r\n  )\r\n\r\n  useEffect(() => {\r\n    if (!productId) {\r\n      return\r\n    }\r\n    if (productId === 'custom') {\r\n      form.setValue('input.product.sourceType', 'CUSTOM')\r\n    }\r\n\r\n    if (selectedProduct) {\r\n      form.setValue('input.product', {\r\n        ...selectedProduct,\r\n        sourceType: 'DATABASE',\r\n      })\r\n    }\r\n  }, [form, productId, selectedProduct])\r\n\r\n  return (\r\n    <div className=\"flex flex-col justify-center gap-4\">\r\n      <Form {...form}>\r\n        <form\r\n          className=\"space-y-4 rounded-md border p-4\"\r\n          onSubmit={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          <h1 className=\"text-3xl font-bold\">{t('heading')}</h1>\r\n          <Image\r\n            src=\"/assets/pillar/FRP_rectangular_pillar_verify.jpg\"\r\n            alt=\"flexural verify\"\r\n            height={250}\r\n            width={500}\r\n            className=\"mx-auto rounded-md object-contain\"\r\n            priority\r\n          />\r\n          <h1 className=\"text-xl font-bold\">{t('sub-heading')}</h1>\r\n          <SelectFormInput\r\n            control={form.control}\r\n            name=\"input.product.id\"\r\n            options={productsOptions}\r\n            t={t}\r\n            loading={isLoadingProducts}\r\n            requestError={errorGettingProducts}\r\n            errorMessage={t('products.error')}\r\n          />\r\n          {productId === 'custom' && <CustomProductSection />}\r\n          {selectedProduct && <ProductSummaryCard product={selectedProduct} />}\r\n          <Separator />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.stripWidth\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.layersNumber\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.bendingMoment\"\r\n            t={t}\r\n          />\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full sm:w-auto\"\r\n            disabled={isPending}\r\n          >\r\n            {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n            {tCommon('calculate')}\r\n          </Button>\r\n        </form>\r\n      </Form>\r\n      {flexuralCalculationResult && (\r\n        <PillarFlexuralResultCard\r\n          resistantMoment={flexuralCalculationResult.resistantMoment}\r\n          equilibrium={flexuralCalculationResult.equilibrium}\r\n          checkResult={flexuralCalculationResult.checkResult}\r\n        />\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;AAQO,MAAM,4BAA4B,CAAC,EACxC,OAAO,EACP,MAAM,EACN,SAAS,EACH;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,EAAE,4BAA4B,EAAE,yBAAyB,EAAE,GAAG;IACpE,MAAM,OAAO,IAAA,yKAAO,EAAiC;QACnD,UAAU,IAAA,6KAAW,EAAC,4KAA+B;QACrD,eAAe;YACb,iBAAiB;YACjB,OAAO;gBACL,SAAS;oBACP,IAAI,8BAA8B,QAAQ;oBAC1C,MAAM,8BAA8B,QAAQ;oBAC5C,YACE,8BAA8B,QAAQ,OAAO,WACzC,WACA;oBACN,WAAW,8BAA8B,QAAQ;oBACjD,iBACE,8BAA8B,QAAQ;oBACxC,gBAAgB,8BAA8B,QAAQ;oBACtD,WACE,8BAA8B,QAAQ,aACtC,gJAAgB,CAAC,MAAM;gBAC3B;gBACA,YAAY,8BAA8B,cAAc;gBACxD,cAAc,8BAA8B,gBAAgB;gBAC5D,eAAe,8BAA8B,iBAAiB;YAChE;QACF;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,0LAAoB,EAAC,QAAQ,WAAW,EAAE;QACtE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;QACxB;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,qBAAqB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAClE;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAO;YAAE;YAAW,UAAU,OAAO,EAAE;YAAE;QAAK;IAChD;IAEA,MAAM,EACJ,MAAM,QAAQ,EACd,SAAS,oBAAoB,EAC7B,WAAW,iBAAiB,EAC7B,GAAG,IAAA,8LAAqB,EAAC,SAAS,UAAU,GAAG;IAEhD,MAAM,kBAAkB;WAClB,UAAU,QAAQ,IAAI,CAAA,IAAK,CAAC;gBAC9B,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,IAAI,IAAI,EAAE;YACrB,CAAC,MAAM,EAAE;QACT;YAAE,OAAO;YAAU,OAAO,EAAE;QAAkB;KAC/C;IAED,MAAM,CAAC,UAAU,GAAG,KAAK,KAAK,CAAC;QAAC;KAAmB;IAEnD,MAAM,kBAAkB,IAAA,gNAAO,EAC7B,IAAM,UAAU,QAAQ,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK,YAC3C;QAAC;QAAW;KAAS;IAGvB,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,WAAW;YACd;QACF;QACA,IAAI,cAAc,UAAU;YAC1B,KAAK,QAAQ,CAAC,4BAA4B;QAC5C;QAEA,IAAI,iBAAiB;YACnB,KAAK,QAAQ,CAAC,iBAAiB;gBAC7B,GAAG,eAAe;gBAClB,YAAY;YACd;QACF;IACF,GAAG;QAAC;QAAM;QAAW;KAAgB;IAErC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,wIAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,8OAAC;oBACC,WAAU;oBACV,UAAU,KAAK,YAAY,CAAC;;sCAE5B,8OAAC;4BAAG,WAAU;sCAAsB,EAAE;;;;;;sCACtC,8OAAC,wIAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,QAAQ;4BACR,OAAO;4BACP,WAAU;4BACV,QAAQ;;;;;;sCAEV,8OAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS;4BACT,GAAG;4BACH,SAAS;4BACT,cAAc;4BACd,cAAc,EAAE;;;;;;wBAEjB,cAAc,0BAAY,8OAAC,wMAAoB;;;;;wBAC/C,iCAAmB,8OAAC,oMAAkB;4BAAC,SAAS;;;;;;sCACjD,8OAAC,kJAAS;;;;;sCACV,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,4IAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU;;gCAET,2BAAa,8OAAC,oOAAW;oCAAC,WAAU;;;;;;gCACpC,QAAQ;;;;;;;;;;;;;;;;;;YAId,2CACC,8OAAC,0QAAwB;gBACvB,iBAAiB,0BAA0B,eAAe;gBAC1D,aAAa,0BAA0B,WAAW;gBAClD,aAAa,0BAA0B,WAAW;;;;;;;;;;;;AAK5D", "debugId": null}}, {"offset": {"line": 413, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/pillar/calculations/pillar-shear-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  checkResult: boolean\r\n  frpShearContribution: number\r\n  shearCapacity: number\r\n}\r\n\r\nexport function PillarFlexuralResultCard({\r\n  checkResult,\r\n  frpShearContribution,\r\n  shearCapacity,\r\n}: Props) {\r\n  const t = useTranslations('components.calculations')\r\n  const locale = useLocale()\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('shearResult.title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('shearResult.frpShearContribution')}:\r\n          </span>{' '}\r\n          <span>\r\n            {frpShearContribution.toLocaleString(locale, {\r\n              maximumFractionDigits: 2,\r\n            })}{' '}\r\n            kNm\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">{t('shearResult.shearCapacity')}:</span>{' '}\r\n          <span>\r\n            {shearCapacity.toLocaleString(locale, { maximumFractionDigits: 2 })}\r\n          </span>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('shearResult.checkResult.label')}:\r\n          </span>{' '}\r\n          <Badge\r\n            className={cn(\r\n              'text-base px-3 py-1',\r\n              checkResult ? 'bg-green-600' : 'bg-red-600',\r\n            )}\r\n          >\r\n            {checkResult\r\n              ? t('shear-result.checkResult.satisfied')\r\n              : t('shear-reesult.checkResult.notSatisfied')}\r\n          </Badge>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AACA;AAAA;;;;;;AAQO,SAAS,yBAAyB,EACvC,WAAW,EACX,oBAAoB,EACpB,aAAa,EACP;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,SAAS,IAAA,+KAAS;IAExB,qBACE,8OAAC,wIAAI;;0BACH,8OAAC,8IAAU;0BACT,cAAA,8OAAC,6IAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,8OAAC,+IAAW;gBAAC,WAAU;;kCACrB,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAoC;;;;;;;4BACjC;0CACR,8OAAC;;oCACE,qBAAqB,cAAc,CAAC,QAAQ;wCAC3C,uBAAuB;oCACzB;oCAAI;oCAAI;;;;;;;;;;;;;kCAIZ,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAA6B;;;;;;;4BAAS;0CACvE,8OAAC;0CACE,cAAc,cAAc,CAAC,QAAQ;oCAAE,uBAAuB;gCAAE;;;;;;;;;;;;kCAGrE,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAiC;;;;;;;4BAC9B;0CACR,8OAAC,0IAAK;gCACJ,WAAW,IAAA,yHAAE,EACX,uBACA,cAAc,iBAAiB;0CAGhC,cACG,EAAE,wCACF,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAMlB", "debugId": null}}, {"offset": {"line": 556, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/pillar/calculations/pillar-shear-calculation.tsx"], "sourcesContent": ["import { PillarFlexuralResultCard } from '@atlas/components/common/atlas/module-detail/params-forms/pillar/calculations/pillar-shear-result-card'\r\nimport { CustomProductSection } from '@atlas/components/common/atlas/product/custom-product-section'\r\nimport { ProductSummaryCard } from '@atlas/components/common/atlas/product/product-summary-card'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { productFiberType } from '@atlas/constants/product'\r\nimport type { ModuleWithParamsPillar } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useModuleCalculation } from '@atlas/lib/mutation/modules/use-module-calculation'\r\nimport { useProductsByCategory } from '@atlas/lib/query/products/use-products-by-category'\r\nimport {\r\n  type PillarShearCalculationInput,\r\n  pillarShearCalculationSchema,\r\n} from '@atlas/types/schemas/pillar-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useMemo } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  module: ModuleWithParamsPillar\r\n  session: Session\r\n  projectId: Project['id']\r\n}\r\n\r\nexport const PillarShearCalculation = ({\r\n  module,\r\n  session,\r\n  projectId,\r\n}: Props) => {\r\n  const t = useTranslations('forms.calculations.pillar.shear')\r\n  const tAction = useTranslations('actions.calculations.pillar.shear')\r\n  const tCommon = useTranslations('actions.common')\r\n  const { shearVerifyExecutionInput, shearCalculationResult } = module\r\n  const form = useForm<PillarShearCalculationInput>({\r\n    resolver: zodResolver(pillarShearCalculationSchema),\r\n    defaultValues: {\r\n      calculationType: 'SHEAR_VERIFY',\r\n      input: {\r\n        product: {\r\n          id: shearVerifyExecutionInput?.product.id,\r\n          name: shearVerifyExecutionInput?.product.name,\r\n          sourceType:\r\n            shearVerifyExecutionInput?.product.id === 'custom'\r\n              ? 'CUSTOM'\r\n              : 'DATABASE',\r\n          thickness: shearVerifyExecutionInput?.product.thickness,\r\n          tensileStrength: shearVerifyExecutionInput?.product.tensileStrength,\r\n          elasticModulus: shearVerifyExecutionInput?.product.elasticModulus,\r\n          fiberType:\r\n            shearVerifyExecutionInput?.product.fiberType ??\r\n            productFiberType.CARBON,\r\n        },\r\n      },\r\n    },\r\n  })\r\n\r\n  const {\r\n    data: products,\r\n    isError: errorGettingProducts,\r\n    isLoading: isLoadingProducts,\r\n  } = useProductsByCategory(session, 'PILLAR', 0, 100)\r\n\r\n  const productsOptions = [\r\n    ...(products?.content.map(m => ({\r\n      value: m.id,\r\n      label: m.name ?? t('product.unnamed'),\r\n    })) ?? []),\r\n    { value: 'custom', label: t('product.custom') },\r\n  ]\r\n\r\n  const [productId] = form.watch(['input.product.id'])\r\n  const selectedProduct = useMemo(\r\n    () => products?.content.find(p => p.id === productId),\r\n    [productId, products],\r\n  )\r\n\r\n  useEffect(() => {\r\n    if (!productId) {\r\n      return\r\n    }\r\n    if (productId === 'custom') {\r\n      form.setValue('input.product.sourceType', 'CUSTOM')\r\n    }\r\n\r\n    if (selectedProduct) {\r\n      form.setValue('input.product', {\r\n        ...selectedProduct,\r\n        sourceType: 'DATABASE',\r\n      })\r\n    }\r\n  }, [form, productId, selectedProduct])\r\n\r\n  const { mutate, isPending } = useModuleCalculation(session.accessToken, {\r\n    onSuccess: () => {\r\n      toast.success(tAction('calculate.success'))\r\n    },\r\n    onError: error => {\r\n      toast.error(tAction('calculate.failure', { error: error.message }))\r\n    },\r\n  })\r\n\r\n  const handleFormSubmit = (body: PillarShearCalculationInput) => {\r\n    mutate({ projectId, moduleId: module.id, body })\r\n  }\r\n\r\n  return (\r\n    <div className=\"flex flex-col justify-center gap-4\">\r\n      <Form {...form}>\r\n        <form\r\n          className=\"space-y-4 rounded-md border p-4\"\r\n          onSubmit={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          <h1 className=\"text-3xl font-bold\">{t('heading')}</h1>\r\n          <Image\r\n            src=\"/assets/pillar/FRP_pillar_shear_verify.jpg\"\r\n            alt=\"flexural verify\"\r\n            height={250}\r\n            width={500}\r\n            className=\"mx-auto rounded-md object-contain\"\r\n            priority\r\n          />\r\n          <h1 className=\"text-xl font-bold\">{t('sub-heading')}</h1>\r\n          <SelectFormInput\r\n            control={form.control}\r\n            name=\"input.product.id\"\r\n            options={productsOptions}\r\n            t={t}\r\n            loading={isLoadingProducts}\r\n            requestError={errorGettingProducts}\r\n            errorMessage={t('products.error')}\r\n          />\r\n          {productId === 'custom' && <CustomProductSection />}\r\n          {selectedProduct && <ProductSummaryCard product={selectedProduct} />}\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full sm:w-auto\"\r\n            disabled={isPending}\r\n            onClick={form.handleSubmit(handleFormSubmit)}\r\n          >\r\n            {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n            {tCommon('save')}\r\n          </Button>\r\n        </form>\r\n      </Form>\r\n      {shearCalculationResult && (\r\n        <PillarFlexuralResultCard\r\n          frpShearContribution={shearCalculationResult.frpShearContribution}\r\n          shearCapacity={shearCalculationResult.shearCapacity}\r\n          checkResult={shearCalculationResult.checkResult}\r\n        />\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AAQO,MAAM,yBAAyB,CAAC,EACrC,MAAM,EACN,OAAO,EACP,SAAS,EACH;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,EAAE,yBAAyB,EAAE,sBAAsB,EAAE,GAAG;IAC9D,MAAM,OAAO,IAAA,yKAAO,EAA8B;QAChD,UAAU,IAAA,6KAAW,EAAC,yKAA4B;QAClD,eAAe;YACb,iBAAiB;YACjB,OAAO;gBACL,SAAS;oBACP,IAAI,2BAA2B,QAAQ;oBACvC,MAAM,2BAA2B,QAAQ;oBACzC,YACE,2BAA2B,QAAQ,OAAO,WACtC,WACA;oBACN,WAAW,2BAA2B,QAAQ;oBAC9C,iBAAiB,2BAA2B,QAAQ;oBACpD,gBAAgB,2BAA2B,QAAQ;oBACnD,WACE,2BAA2B,QAAQ,aACnC,gJAAgB,CAAC,MAAM;gBAC3B;YACF;QACF;IACF;IAEA,MAAM,EACJ,MAAM,QAAQ,EACd,SAAS,oBAAoB,EAC7B,WAAW,iBAAiB,EAC7B,GAAG,IAAA,8LAAqB,EAAC,SAAS,UAAU,GAAG;IAEhD,MAAM,kBAAkB;WAClB,UAAU,QAAQ,IAAI,CAAA,IAAK,CAAC;gBAC9B,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,IAAI,IAAI,EAAE;YACrB,CAAC,MAAM,EAAE;QACT;YAAE,OAAO;YAAU,OAAO,EAAE;QAAkB;KAC/C;IAED,MAAM,CAAC,UAAU,GAAG,KAAK,KAAK,CAAC;QAAC;KAAmB;IACnD,MAAM,kBAAkB,IAAA,gNAAO,EAC7B,IAAM,UAAU,QAAQ,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK,YAC3C;QAAC;QAAW;KAAS;IAGvB,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,WAAW;YACd;QACF;QACA,IAAI,cAAc,UAAU;YAC1B,KAAK,QAAQ,CAAC,4BAA4B;QAC5C;QAEA,IAAI,iBAAiB;YACnB,KAAK,QAAQ,CAAC,iBAAiB;gBAC7B,GAAG,eAAe;gBAClB,YAAY;YACd;QACF;IACF,GAAG;QAAC;QAAM;QAAW;KAAgB;IAErC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,0LAAoB,EAAC,QAAQ,WAAW,EAAE;QACtE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;QACxB;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,qBAAqB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAClE;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAO;YAAE;YAAW,UAAU,OAAO,EAAE;YAAE;QAAK;IAChD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,wIAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,8OAAC;oBACC,WAAU;oBACV,UAAU,KAAK,YAAY,CAAC;;sCAE5B,8OAAC;4BAAG,WAAU;sCAAsB,EAAE;;;;;;sCACtC,8OAAC,wIAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,QAAQ;4BACR,OAAO;4BACP,WAAU;4BACV,QAAQ;;;;;;sCAEV,8OAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS;4BACT,GAAG;4BACH,SAAS;4BACT,cAAc;4BACd,cAAc,EAAE;;;;;;wBAEjB,cAAc,0BAAY,8OAAC,wMAAoB;;;;;wBAC/C,iCAAmB,8OAAC,oMAAkB;4BAAC,SAAS;;;;;;sCACjD,8OAAC,4IAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU;4BACV,SAAS,KAAK,YAAY,CAAC;;gCAE1B,2BAAa,8OAAC,oOAAW;oCAAC,WAAU;;;;;;gCACpC,QAAQ;;;;;;;;;;;;;;;;;;YAId,wCACC,8OAAC,uQAAwB;gBACvB,sBAAsB,uBAAuB,oBAAoB;gBACjE,eAAe,uBAAuB,aAAa;gBACnD,aAAa,uBAAuB,WAAW;;;;;;;;;;;;AAKzD", "debugId": null}}, {"offset": {"line": 784, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/pillar/pillar-calculations.tsx"], "sourcesContent": ["import { PillarFlexuralCalculation } from '@atlas/components/common/atlas/module-detail/params-forms/pillar/calculations/pillar-flexural-calculation'\r\nimport { PillarShearCalculation } from '@atlas/components/common/atlas/module-detail/params-forms/pillar/calculations/pillar-shear-calculation'\r\nimport {\r\n  Ta<PERSON>,\r\n  <PERSON>bs<PERSON>ontent,\r\n  Tabs<PERSON>ist,\r\n  TabsTrigger,\r\n} from '@atlas/components/ui/tabs'\r\nimport type { ModuleWithParamsPillar } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  module: ModuleWithParamsPillar\r\n  session: Session\r\n  projectId: string\r\n}\r\n\r\nexport const PillarCalculations = ({ module, session, projectId }: Props) => {\r\n  const t = useTranslations('forms.calculations.pillar')\r\n\r\n  return (\r\n    <>\r\n      <h3 className=\"text-lg font-medium py-4\">{t('title')}</h3>\r\n      <Tabs defaultValue=\"flexural\">\r\n        <TabsList>\r\n          <TabsTrigger value=\"flexural\">{t('flexural.label')}</TabsTrigger>\r\n          <TabsTrigger value=\"shear\">{t('shear.label')}</TabsTrigger>\r\n          <TabsTrigger value=\"confinement\">{t('confinemet.label')}</TabsTrigger>\r\n        </TabsList>\r\n        <TabsContent value=\"flexural\">\r\n          <PillarFlexuralCalculation\r\n            session={session}\r\n            module={module}\r\n            projectId={projectId}\r\n          />\r\n        </TabsContent>\r\n        <TabsContent value=\"shear\">\r\n          <PillarShearCalculation\r\n            session={session}\r\n            module={module}\r\n            projectId={projectId}\r\n          />\r\n        </TabsContent>\r\n        <TabsContent value=\"confinement\" />\r\n      </Tabs>\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAQA;;;;;;AAQO,MAAM,qBAAqB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAS;IACtE,MAAM,IAAI,IAAA,yNAAe,EAAC;IAE1B,qBACE;;0BACE,8OAAC;gBAAG,WAAU;0BAA4B,EAAE;;;;;;0BAC5C,8OAAC,wIAAI;gBAAC,cAAa;;kCACjB,8OAAC,4IAAQ;;0CACP,8OAAC,+IAAW;gCAAC,OAAM;0CAAY,EAAE;;;;;;0CACjC,8OAAC,+IAAW;gCAAC,OAAM;0CAAS,EAAE;;;;;;0CAC9B,8OAAC,+IAAW;gCAAC,OAAM;0CAAe,EAAE;;;;;;;;;;;;kCAEtC,8OAAC,+IAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,wQAAyB;4BACxB,SAAS;4BACT,QAAQ;4BACR,WAAW;;;;;;;;;;;kCAGf,8OAAC,+IAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,kQAAsB;4BACrB,SAAS;4BACT,QAAQ;4BACR,WAAW;;;;;;;;;;;kCAGf,8OAAC,+IAAW;wBAAC,OAAM;;;;;;;;;;;;;;AAI3B", "debugId": null}}, {"offset": {"line": 897, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/pillar/sections/pillar-general-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type PillarGeneralFormInputs,\r\n  pillarGeneralSchema,\r\n} from '@atlas/types/schemas/pillar-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<PillarGeneralFormInputs>\r\n  onSave: () => void\r\n}\r\n\r\nexport const PillarGeneralForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.pillar.general')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const form = useForm<PillarGeneralFormInputs>({\r\n    resolver: zodResolver(pillarGeneralSchema),\r\n    defaultValues: {\r\n      initialDeformation: defaultValues?.initialDeformation ?? 0,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: PillarGeneralFormInputs) => {\r\n      mutate({ projectId, moduleId, body })\r\n    },\r\n    [mutate, projectId, moduleId],\r\n  )\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"initialDeformation\"\r\n          t={t}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAGA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;AAUO,MAAM,oBAAoB,CAAC,EAChC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,OAAO,IAAA,yKAAO,EAA0B;QAC5C,UAAU,IAAA,6KAAW,EAAC,gKAAmB;QACzC,eAAe;YACb,oBAAoB,eAAe,sBAAsB;QAC3D;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAW;wBAAE;wBAAW;oBAAS;iBAAE;YAChD;YACA;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,OAAO;YAAE;YAAW;YAAU;QAAK;IACrC,GACA;QAAC;QAAQ;QAAW;KAAS;IAG/B,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,8OAAC,4IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,8OAAC,oOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 1020, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/pillar/sections/pillar-geometry-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport {\r\n  MODULE_GEOMETRY_EXPOSURE,\r\n  moduleGeometryExposure,\r\n} from '@atlas/constants/module'\r\nimport { calculateEffectiveDepth } from '@atlas/functions/forms/rectangular-beam-form-calculations'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type PillarGeometryFormInputs as FormSchema,\r\n  pillarGeometrySchema,\r\n} from '@atlas/types/schemas/pillar-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FormSchema>\r\n  onSave: () => void\r\n}\r\n\r\nexport const PillarGeometryForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.pillar.geometry')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n  const form = useForm<FormSchema>({\r\n    resolver: zodResolver(pillarGeometrySchema),\r\n    defaultValues: {\r\n      width: defaultValues?.width,\r\n      height: defaultValues?.height,\r\n      topConcreteCover: defaultValues?.topConcreteCover,\r\n      bottomConcreteCover: defaultValues?.bottomConcreteCover,\r\n      effectiveDepth: defaultValues?.effectiveDepth,\r\n      exposure: defaultValues?.exposure ?? moduleGeometryExposure.INTERNAL,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (geometry: FormSchema) => {\r\n      mutate({ projectId, moduleId, body: { geometry } })\r\n    },\r\n    [mutate, projectId, moduleId],\r\n  )\r\n\r\n  const [height, bottomConcreteCover] = form.watch([\r\n    'height',\r\n    'bottomConcreteCover',\r\n  ])\r\n\r\n  useEffect(() => {\r\n    if (height && bottomConcreteCover) {\r\n      form.setValue(\r\n        'effectiveDepth',\r\n        calculateEffectiveDepth(height, bottomConcreteCover),\r\n      )\r\n    }\r\n  }, [height, bottomConcreteCover, form])\r\n\r\n  return (\r\n    <div className=\"flex flex-col 2xl:flex-row justify-center gap-2\">\r\n      <Form {...form}>\r\n        <form\r\n          className=\"space-y-4 rounded-md border p-4 grow\"\r\n          onSubmit={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          <NumberFormInput control={form.control} name=\"width\" t={t} />\r\n          <NumberFormInput control={form.control} name=\"height\" t={t} />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"topConcreteCover\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"bottomConcreteCover\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"effectiveDepth\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <SelectFormFixedInput\r\n            control={form.control}\r\n            name=\"exposure\"\r\n            options={MODULE_GEOMETRY_EXPOSURE}\r\n            optionLabelFn={p => t(`exposure.${p}`)}\r\n            t={t}\r\n          />\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full sm:w-auto\"\r\n            disabled={isPending}\r\n            onClick={form.handleSubmit(handleFormSubmit)}\r\n          >\r\n            {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n            {tCommon('save')}\r\n          </Button>\r\n        </form>\r\n      </Form>\r\n      <Image\r\n        src={'/assets/pillar/FRP_pillar_geometry.jpg'}\r\n        alt=\"rectangual beam geometry\"\r\n        height={500}\r\n        width={250}\r\n        className=\"mx-auto rounded-md object-contain\"\r\n        priority\r\n      />\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAIA;AAGA;AACA;AAIA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;AAUO,MAAM,qBAAqB,CAAC,EACjC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,cAAc,IAAA,wMAAc;IAClC,MAAM,OAAO,IAAA,yKAAO,EAAa;QAC/B,UAAU,IAAA,6KAAW,EAAC,iKAAoB;QAC1C,eAAe;YACb,OAAO,eAAe;YACtB,QAAQ,eAAe;YACvB,kBAAkB,eAAe;YACjC,qBAAqB,eAAe;YACpC,gBAAgB,eAAe;YAC/B,UAAU,eAAe,YAAY,qJAAsB,CAAC,QAAQ;QACtE;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAW;wBAAE;wBAAW;oBAAS;iBAAE;YAChD;YACA;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,OAAO;YAAE;YAAW;YAAU,MAAM;gBAAE;YAAS;QAAE;IACnD,GACA;QAAC;QAAQ;QAAW;KAAS;IAG/B,MAAM,CAAC,QAAQ,oBAAoB,GAAG,KAAK,KAAK,CAAC;QAC/C;QACA;KACD;IAED,IAAA,kNAAS,EAAC;QACR,IAAI,UAAU,qBAAqB;YACjC,KAAK,QAAQ,CACX,kBACA,IAAA,mMAAuB,EAAC,QAAQ;QAEpC;IACF,GAAG;QAAC;QAAQ;QAAqB;KAAK;IAEtC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,wIAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,8OAAC;oBACC,WAAU;oBACV,UAAU,KAAK,YAAY,CAAC;;sCAE5B,8OAAC,kLAAe;4BAAC,SAAS,KAAK,OAAO;4BAAE,MAAK;4BAAQ,GAAG;;;;;;sCACxD,8OAAC,kLAAe;4BAAC,SAAS,KAAK,OAAO;4BAAE,MAAK;4BAAS,GAAG;;;;;;sCACzD,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,gMAAoB;4BACnB,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS,uJAAwB;4BACjC,eAAe,CAAA,IAAK,EAAE,CAAC,SAAS,EAAE,GAAG;4BACrC,GAAG;;;;;;sCAEL,8OAAC,4IAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU;4BACV,SAAS,KAAK,YAAY,CAAC;;gCAE1B,2BAAa,8OAAC,oOAAW;oCAAC,WAAU;;;;;;gCACpC,QAAQ;;;;;;;;;;;;;;;;;;0BAIf,8OAAC,wIAAK;gBACJ,KAAK;gBACL,KAAI;gBACJ,QAAQ;gBACR,OAAO;gBACP,WAAU;gBACV,QAAQ;;;;;;;;;;;;AAIhB", "debugId": null}}, {"offset": {"line": 1240, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/pillar/sections/pillar-concrete-class-form-section.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { StringFormInput } from '@atlas/components/common/form/string-form-input'\r\nimport { moduleMaterialKnowledgeLevelValues } from '@atlas/constants/module'\r\nimport {\r\n  calculateDesignStrengthForBrittleMechanisms,\r\n  calculateDesignStrengthForDuctileMechanisms,\r\n} from '@atlas/functions/forms/rectangular-beam-form-calculations'\r\nimport { useReinforcedConcreteMaterials } from '@atlas/lib/query/materials/use-reinforced-concrete-materials'\r\nimport type { PillarMaterialFormInputs } from '@atlas/types/schemas/pillar-form'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect } from 'react'\r\nimport { useFormContext } from 'react-hook-form'\r\n\r\ntype Props = {\r\n  session: Session\r\n}\r\n\r\nexport function PillarConcreteClassFormSection({ session }: Props) {\r\n  const t = useTranslations('forms.project-params.pillar.materialProperties')\r\n\r\n  const form = useFormContext<PillarMaterialFormInputs>()\r\n\r\n  const {\r\n    data: concreteMaterials,\r\n    isError: concreteMaterialsError,\r\n    isLoading: concreteMaterialsLoading,\r\n  } = useReinforcedConcreteMaterials({ session, body: { size: 100 } })\r\n\r\n  const customOption = { value: 'custom', label: t('concreteClass.custom') }\r\n\r\n  const concreteMaterialsOptions = [\r\n    ...(concreteMaterials?.content.map(m => ({\r\n      value: m.id,\r\n      label: m.name,\r\n    })) ?? []),\r\n    customOption,\r\n  ]\r\n\r\n  const knowledgeLevel = form.watch('knowledgeLevel')\r\n  const concreteClassId = form.watch('concreteClass.id')\r\n  const averageCompressiveStrength = form.watch(\r\n    'concreteClass.averageCompressiveStrength',\r\n  )\r\n  const isCustomSelected = concreteClassId === 'custom'\r\n\r\n  useEffect(() => {\r\n    if (!concreteClassId || !concreteMaterials || isCustomSelected) {\r\n      return\r\n    }\r\n    const selected = concreteMaterials.content.find(\r\n      (m: any) => m.id === concreteClassId,\r\n    )\r\n    if (!selected) {\r\n      return\r\n    }\r\n    if (form.getValues('concreteClass.name') !== selected.name) {\r\n      form.setValue('concreteClass.name', selected.name)\r\n    }\r\n\r\n    if (\r\n      form.getValues('concreteClass.cubeCompressiveStrength') !==\r\n      selected.cubeCompressiveStrength\r\n    ) {\r\n      form.setValue(\r\n        'concreteClass.cubeCompressiveStrength',\r\n        selected.cubeCompressiveStrength,\r\n      )\r\n    }\r\n    if (\r\n      form.getValues('concreteClass.cylinderCompressiveStrength') !==\r\n      selected.cylinderCompressiveStrength\r\n    ) {\r\n      form.setValue(\r\n        'concreteClass.cylinderCompressiveStrength',\r\n        selected.cylinderCompressiveStrength,\r\n      )\r\n    }\r\n    if (\r\n      form.getValues('concreteClass.averageCompressiveStrength') !==\r\n      selected.averageCompressiveStrength\r\n    ) {\r\n      form.setValue(\r\n        'concreteClass.averageCompressiveStrength',\r\n        selected.averageCompressiveStrength,\r\n      )\r\n    }\r\n    if (\r\n      form.getValues('concreteClass.averageTensileStrength') !==\r\n      selected.averageTensileStrength\r\n    ) {\r\n      form.setValue(\r\n        'concreteClass.averageTensileStrength',\r\n        selected.averageTensileStrength,\r\n      )\r\n    }\r\n    if (\r\n      form.getValues('concreteClass.elasticModulus') !== selected.elasticModulus\r\n    ) {\r\n      form.setValue('concreteClass.elasticModulus', selected.elasticModulus)\r\n    }\r\n  }, [concreteClassId, concreteMaterials, form, isCustomSelected])\r\n\r\n  useEffect(() => {\r\n    if (\r\n      knowledgeLevel &&\r\n      averageCompressiveStrength !== undefined &&\r\n      averageCompressiveStrength !== null\r\n    ) {\r\n      const confidenceFactor =\r\n        moduleMaterialKnowledgeLevelValues[knowledgeLevel]\r\n      form.setValue(\r\n        'concreteClass.designCompressiveStrengthForBrittleMechanisms',\r\n        calculateDesignStrengthForBrittleMechanisms(\r\n          averageCompressiveStrength,\r\n          confidenceFactor,\r\n        ),\r\n      )\r\n      form.setValue(\r\n        'concreteClass.designCompressiveStrengthForDuctileMechanisms',\r\n        calculateDesignStrengthForDuctileMechanisms(\r\n          averageCompressiveStrength,\r\n          confidenceFactor,\r\n        ),\r\n      )\r\n    }\r\n  }, [knowledgeLevel, averageCompressiveStrength, form])\r\n\r\n  return (\r\n    <>\r\n      <SelectFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.id\"\r\n        options={concreteMaterialsOptions}\r\n        t={t}\r\n        loading={concreteMaterialsLoading}\r\n        requestError={concreteMaterialsError}\r\n        errorMessage={t('concreteClass.error')}\r\n      />\r\n      {isCustomSelected && (\r\n        <StringFormInput\r\n          control={form.control}\r\n          name=\"concreteClass.name\"\r\n          t={t}\r\n        />\r\n      )}\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.cubeCompressiveStrength\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.cylinderCompressiveStrength\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.averageCompressiveStrength\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.averageTensileStrength\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.elasticModulus\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.designCompressiveStrengthForDuctileMechanisms\"\r\n        t={t}\r\n        disabled={true}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.designCompressiveStrengthForBrittleMechanisms\"\r\n        t={t}\r\n        disabled={true}\r\n      />\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAIA;AAGA;AACA;AACA;;;;;;;;;;;AAMO,SAAS,+BAA+B,EAAE,OAAO,EAAS;IAC/D,MAAM,IAAI,IAAA,yNAAe,EAAC;IAE1B,MAAM,OAAO,IAAA,gLAAc;IAE3B,MAAM,EACJ,MAAM,iBAAiB,EACvB,SAAS,sBAAsB,EAC/B,WAAW,wBAAwB,EACpC,GAAG,IAAA,gNAA8B,EAAC;QAAE;QAAS,MAAM;YAAE,MAAM;QAAI;IAAE;IAElE,MAAM,eAAe;QAAE,OAAO;QAAU,OAAO,EAAE;IAAwB;IAEzE,MAAM,2BAA2B;WAC3B,mBAAmB,QAAQ,IAAI,CAAA,IAAK,CAAC;gBACvC,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,IAAI;YACf,CAAC,MAAM,EAAE;QACT;KACD;IAED,MAAM,iBAAiB,KAAK,KAAK,CAAC;IAClC,MAAM,kBAAkB,KAAK,KAAK,CAAC;IACnC,MAAM,6BAA6B,KAAK,KAAK,CAC3C;IAEF,MAAM,mBAAmB,oBAAoB;IAE7C,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,mBAAmB,CAAC,qBAAqB,kBAAkB;YAC9D;QACF;QACA,MAAM,WAAW,kBAAkB,OAAO,CAAC,IAAI,CAC7C,CAAC,IAAW,EAAE,EAAE,KAAK;QAEvB,IAAI,CAAC,UAAU;YACb;QACF;QACA,IAAI,KAAK,SAAS,CAAC,0BAA0B,SAAS,IAAI,EAAE;YAC1D,KAAK,QAAQ,CAAC,sBAAsB,SAAS,IAAI;QACnD;QAEA,IACE,KAAK,SAAS,CAAC,6CACf,SAAS,uBAAuB,EAChC;YACA,KAAK,QAAQ,CACX,yCACA,SAAS,uBAAuB;QAEpC;QACA,IACE,KAAK,SAAS,CAAC,iDACf,SAAS,2BAA2B,EACpC;YACA,KAAK,QAAQ,CACX,6CACA,SAAS,2BAA2B;QAExC;QACA,IACE,KAAK,SAAS,CAAC,gDACf,SAAS,0BAA0B,EACnC;YACA,KAAK,QAAQ,CACX,4CACA,SAAS,0BAA0B;QAEvC;QACA,IACE,KAAK,SAAS,CAAC,4CACf,SAAS,sBAAsB,EAC/B;YACA,KAAK,QAAQ,CACX,wCACA,SAAS,sBAAsB;QAEnC;QACA,IACE,KAAK,SAAS,CAAC,oCAAoC,SAAS,cAAc,EAC1E;YACA,KAAK,QAAQ,CAAC,gCAAgC,SAAS,cAAc;QACvE;IACF,GAAG;QAAC;QAAiB;QAAmB;QAAM;KAAiB;IAE/D,IAAA,kNAAS,EAAC;QACR,IACE,kBACA,+BAA+B,aAC/B,+BAA+B,MAC/B;YACA,MAAM,mBACJ,iKAAkC,CAAC,eAAe;YACpD,KAAK,QAAQ,CACX,+DACA,IAAA,uNAA2C,EACzC,4BACA;YAGJ,KAAK,QAAQ,CACX,+DACA,IAAA,uNAA2C,EACzC,4BACA;QAGN;IACF,GAAG;QAAC;QAAgB;QAA4B;KAAK;IAErD,qBACE;;0BACE,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,SAAS;gBACT,GAAG;gBACH,SAAS;gBACT,cAAc;gBACd,cAAc,EAAE;;;;;;YAEjB,kCACC,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;;;;;;0BAGP,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU;;;;;;0BAEZ,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU;;;;;;;;AAIlB", "debugId": null}}, {"offset": {"line": 1432, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/pillar/sections/pillar-steel-grade-form-section.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { StringFormInput } from '@atlas/components/common/form/string-form-input'\r\nimport { moduleMaterialKnowledgeLevelValues } from '@atlas/constants/module'\r\nimport {\r\n  calculateDesignYieldStrengthForBrittleMechanisms,\r\n  calculateDesignYieldStrengthForDuctileMechanisms,\r\n} from '@atlas/functions/forms/rectangular-beam-form-calculations'\r\nimport { useSteelGradesMaterials } from '@atlas/lib/query/materials/use-steel-grades-materials'\r\nimport type { PillarMaterialFormInputs } from '@atlas/types/schemas/pillar-form'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect } from 'react'\r\nimport { useFormContext } from 'react-hook-form'\r\n\r\nexport function PillarSteelGradeFormSection({ session }: { session: Session }) {\r\n  const t = useTranslations('forms.project-params.pillar.materialProperties')\r\n  const form = useFormContext<PillarMaterialFormInputs>()\r\n\r\n  const {\r\n    data: steelMaterials,\r\n    isError: steelMaterialsError,\r\n    isLoading: steelMaterialsLoading,\r\n  } = useSteelGradesMaterials({ session, body: { size: 100 } })\r\n\r\n  const customOption = { value: 'custom', label: t('steelGrade.custom') }\r\n  const steelMaterialsOptions = [\r\n    ...(steelMaterials?.content.map(m => ({ value: m.id, label: m.name })) ??\r\n      []),\r\n    customOption,\r\n  ]\r\n\r\n  const knowledgeLevel = form.watch('knowledgeLevel')\r\n  const steelGradeId = form.watch('steelGrade.id')\r\n  const yieldStrength = form.watch('steelGrade.yieldStrength')\r\n  const isCustomSelected = steelGradeId === 'custom'\r\n\r\n  useEffect(() => {\r\n    if (!steelGradeId || !steelMaterials || isCustomSelected) {\r\n      return\r\n    }\r\n    const selected = steelMaterials.content.find(\r\n      (m: any) => m.id === steelGradeId,\r\n    )\r\n    if (!selected) {\r\n      return\r\n    }\r\n    if (form.getValues('steelGrade.name') !== selected.name) {\r\n      form.setValue('steelGrade.name', selected.name)\r\n    }\r\n    if (form.getValues('steelGrade.yieldStrength') !== selected.yieldStrength) {\r\n      form.setValue('steelGrade.yieldStrength', selected.yieldStrength)\r\n    }\r\n    if (\r\n      form.getValues('steelGrade.tensileStrength') !== selected.tensileStrength\r\n    ) {\r\n      form.setValue('steelGrade.tensileStrength', selected.tensileStrength)\r\n    }\r\n    if (\r\n      form.getValues('steelGrade.elongationPercentage') !==\r\n      selected.elongationPercentage\r\n    ) {\r\n      form.setValue(\r\n        'steelGrade.elongationPercentage',\r\n        selected.elongationPercentage,\r\n      )\r\n    }\r\n    if (\r\n      form.getValues('steelGrade.elasticModulus') !== selected.elasticModulus\r\n    ) {\r\n      form.setValue('steelGrade.elasticModulus', selected.elasticModulus)\r\n    }\r\n  }, [steelGradeId, steelMaterials, form, isCustomSelected])\r\n\r\n  useEffect(() => {\r\n    if (\r\n      knowledgeLevel &&\r\n      yieldStrength !== undefined &&\r\n      yieldStrength !== null\r\n    ) {\r\n      const confidenceFactor =\r\n        moduleMaterialKnowledgeLevelValues[knowledgeLevel]\r\n      form.setValue(\r\n        'steelGrade.designYieldStrengthForDuctileMechanisms',\r\n        calculateDesignYieldStrengthForDuctileMechanisms(\r\n          yieldStrength,\r\n          confidenceFactor,\r\n        ),\r\n      )\r\n      form.setValue(\r\n        'steelGrade.designYieldStrengthForBrittleMechanisms',\r\n        calculateDesignYieldStrengthForBrittleMechanisms(\r\n          yieldStrength,\r\n          confidenceFactor,\r\n        ),\r\n      )\r\n    }\r\n  }, [knowledgeLevel, yieldStrength, form])\r\n\r\n  return (\r\n    <>\r\n      <SelectFormInput\r\n        control={form.control}\r\n        name=\"steelGrade.id\"\r\n        options={steelMaterialsOptions}\r\n        t={t}\r\n        loading={steelMaterialsLoading}\r\n        requestError={steelMaterialsError}\r\n        errorMessage={t('steelGrade.error')}\r\n      />\r\n      {isCustomSelected && (\r\n        <StringFormInput control={form.control} name=\"steelGrade.name\" t={t} />\r\n      )}\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"steelGrade.yieldStrength\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"steelGrade.elasticModulus\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"steelGrade.designYieldStrengthForDuctileMechanisms\"\r\n        t={t}\r\n        disabled={true}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"steelGrade.designYieldStrengthForBrittleMechanisms\"\r\n        t={t}\r\n        disabled={true}\r\n      />\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAIA;AAGA;AACA;AACA;;;;;;;;;;;AAEO,SAAS,4BAA4B,EAAE,OAAO,EAAwB;IAC3E,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,OAAO,IAAA,gLAAc;IAE3B,MAAM,EACJ,MAAM,cAAc,EACpB,SAAS,mBAAmB,EAC5B,WAAW,qBAAqB,EACjC,GAAG,IAAA,kMAAuB,EAAC;QAAE;QAAS,MAAM;YAAE,MAAM;QAAI;IAAE;IAE3D,MAAM,eAAe;QAAE,OAAO;QAAU,OAAO,EAAE;IAAqB;IACtE,MAAM,wBAAwB;WACxB,gBAAgB,QAAQ,IAAI,CAAA,IAAK,CAAC;gBAAE,OAAO,EAAE,EAAE;gBAAE,OAAO,EAAE,IAAI;YAAC,CAAC,MAClE,EAAE;QACJ;KACD;IAED,MAAM,iBAAiB,KAAK,KAAK,CAAC;IAClC,MAAM,eAAe,KAAK,KAAK,CAAC;IAChC,MAAM,gBAAgB,KAAK,KAAK,CAAC;IACjC,MAAM,mBAAmB,iBAAiB;IAE1C,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,kBAAkB;YACxD;QACF;QACA,MAAM,WAAW,eAAe,OAAO,CAAC,IAAI,CAC1C,CAAC,IAAW,EAAE,EAAE,KAAK;QAEvB,IAAI,CAAC,UAAU;YACb;QACF;QACA,IAAI,KAAK,SAAS,CAAC,uBAAuB,SAAS,IAAI,EAAE;YACvD,KAAK,QAAQ,CAAC,mBAAmB,SAAS,IAAI;QAChD;QACA,IAAI,KAAK,SAAS,CAAC,gCAAgC,SAAS,aAAa,EAAE;YACzE,KAAK,QAAQ,CAAC,4BAA4B,SAAS,aAAa;QAClE;QACA,IACE,KAAK,SAAS,CAAC,kCAAkC,SAAS,eAAe,EACzE;YACA,KAAK,QAAQ,CAAC,8BAA8B,SAAS,eAAe;QACtE;QACA,IACE,KAAK,SAAS,CAAC,uCACf,SAAS,oBAAoB,EAC7B;YACA,KAAK,QAAQ,CACX,mCACA,SAAS,oBAAoB;QAEjC;QACA,IACE,KAAK,SAAS,CAAC,iCAAiC,SAAS,cAAc,EACvE;YACA,KAAK,QAAQ,CAAC,6BAA6B,SAAS,cAAc;QACpE;IACF,GAAG;QAAC;QAAc;QAAgB;QAAM;KAAiB;IAEzD,IAAA,kNAAS,EAAC;QACR,IACE,kBACA,kBAAkB,aAClB,kBAAkB,MAClB;YACA,MAAM,mBACJ,iKAAkC,CAAC,eAAe;YACpD,KAAK,QAAQ,CACX,sDACA,IAAA,4NAAgD,EAC9C,eACA;YAGJ,KAAK,QAAQ,CACX,sDACA,IAAA,4NAAgD,EAC9C,eACA;QAGN;IACF,GAAG;QAAC;QAAgB;QAAe;KAAK;IAExC,qBACE;;0BACE,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,SAAS;gBACT,GAAG;gBACH,SAAS;gBACT,cAAc;gBACd,cAAc,EAAE;;;;;;YAEjB,kCACC,8OAAC,kLAAe;gBAAC,SAAS,KAAK,OAAO;gBAAE,MAAK;gBAAkB,GAAG;;;;;;0BAEpE,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU;;;;;;0BAEZ,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU;;;;;;;;AAIlB", "debugId": null}}, {"offset": {"line": 1591, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/pillar/sections/pillar-material-form.tsx"], "sourcesContent": ["import { PillarConcreteClassFormSection } from '@atlas/components/common/atlas/module-detail/params-forms/pillar/sections/pillar-concrete-class-form-section'\r\nimport { PillarSteelGradeFormSection } from '@atlas/components/common/atlas/module-detail/params-forms/pillar/sections/pillar-steel-grade-form-section'\r\nimport { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport {\r\n  MODULE_MATERIAL_KNOWLEDGE_LEVEL,\r\n  moduleMaterialKnowledgeLevel,\r\n  moduleMaterialKnowledgeLevelValues,\r\n} from '@atlas/constants/module'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type PillarMaterialFormInputs,\r\n  pillarMaterialSchema,\r\n} from '@atlas/types/schemas/pillar-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<PillarMaterialFormInputs>\r\n  onSave: () => void\r\n}\r\n\r\nexport const PillarMaterialForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.pillar.materialProperties')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n  const form = useForm<PillarMaterialFormInputs>({\r\n    resolver: zodResolver(pillarMaterialSchema),\r\n    defaultValues: {\r\n      knowledgeLevel:\r\n        defaultValues?.knowledgeLevel ?? moduleMaterialKnowledgeLevel.LC1,\r\n      confidenceFactor: defaultValues?.knowledgeLevel\r\n        ? moduleMaterialKnowledgeLevelValues[defaultValues?.knowledgeLevel]\r\n        : moduleMaterialKnowledgeLevelValues[moduleMaterialKnowledgeLevel.LC1],\r\n      concreteClass: {\r\n        id: defaultValues?.concreteClass?.id,\r\n        name: defaultValues?.concreteClass?.name,\r\n        cubeCompressiveStrength:\r\n          defaultValues?.concreteClass?.cubeCompressiveStrength,\r\n        cylinderCompressiveStrength:\r\n          defaultValues?.concreteClass?.cylinderCompressiveStrength,\r\n        averageCompressiveStrength:\r\n          defaultValues?.concreteClass?.averageCompressiveStrength,\r\n        averageTensileStrength:\r\n          defaultValues?.concreteClass?.averageTensileStrength,\r\n        elasticModulus: defaultValues?.concreteClass?.elasticModulus,\r\n        designCompressiveStrengthForBrittleMechanisms:\r\n          defaultValues?.concreteClass\r\n            ?.designCompressiveStrengthForBrittleMechanisms,\r\n        designCompressiveStrengthForDuctileMechanisms:\r\n          defaultValues?.concreteClass\r\n            ?.designCompressiveStrengthForDuctileMechanisms,\r\n      },\r\n      steelGrade: {\r\n        id: defaultValues?.steelGrade?.id,\r\n        name: defaultValues?.steelGrade?.name,\r\n        yieldStrength: defaultValues?.steelGrade?.yieldStrength,\r\n        tensileStrength: defaultValues?.steelGrade?.tensileStrength,\r\n        elongationPercentage: defaultValues?.steelGrade?.elongationPercentage,\r\n        elasticModulus: defaultValues?.steelGrade?.elasticModulus,\r\n        designYieldStrengthForBrittleMechanisms:\r\n          defaultValues?.steelGrade?.designYieldStrengthForBrittleMechanisms,\r\n        designYieldStrengthForDuctileMechanisms:\r\n          defaultValues?.steelGrade?.designYieldStrengthForDuctileMechanisms,\r\n      },\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (materialProperties: PillarMaterialFormInputs) => {\r\n      mutate({ projectId, moduleId, body: { materialProperties } })\r\n    },\r\n    [mutate, projectId, moduleId],\r\n  )\r\n\r\n  const [knowledgeLevel] = form.watch(['knowledgeLevel'])\r\n\r\n  useEffect(() => {\r\n    if (knowledgeLevel) {\r\n      form.setValue(\r\n        'confidenceFactor',\r\n        moduleMaterialKnowledgeLevelValues[knowledgeLevel],\r\n        { shouldValidate: true },\r\n      )\r\n    }\r\n  }, [knowledgeLevel, form])\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"knowledgeLevel\"\r\n          options={MODULE_MATERIAL_KNOWLEDGE_LEVEL}\r\n          optionLabelFn={p => t(`knowledgeLevel.${p}`)}\r\n          t={t}\r\n          description\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"confidenceFactor\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <Separator />\r\n        <PillarConcreteClassFormSection session={session} />\r\n        <Separator />\r\n        <PillarSteelGradeFormSection session={session} />\r\n        <Button type=\"submit\" className=\"w-full sm:w-auto\" disabled={isPending}>\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AAUO,MAAM,qBAAqB,CAAC,EACjC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,cAAc,IAAA,wMAAc;IAClC,MAAM,OAAO,IAAA,yKAAO,EAA2B;QAC7C,UAAU,IAAA,6KAAW,EAAC,iKAAoB;QAC1C,eAAe;YACb,gBACE,eAAe,kBAAkB,2JAA4B,CAAC,GAAG;YACnE,kBAAkB,eAAe,iBAC7B,iKAAkC,CAAC,eAAe,eAAe,GACjE,iKAAkC,CAAC,2JAA4B,CAAC,GAAG,CAAC;YACxE,eAAe;gBACb,IAAI,eAAe,eAAe;gBAClC,MAAM,eAAe,eAAe;gBACpC,yBACE,eAAe,eAAe;gBAChC,6BACE,eAAe,eAAe;gBAChC,4BACE,eAAe,eAAe;gBAChC,wBACE,eAAe,eAAe;gBAChC,gBAAgB,eAAe,eAAe;gBAC9C,+CACE,eAAe,eACX;gBACN,+CACE,eAAe,eACX;YACR;YACA,YAAY;gBACV,IAAI,eAAe,YAAY;gBAC/B,MAAM,eAAe,YAAY;gBACjC,eAAe,eAAe,YAAY;gBAC1C,iBAAiB,eAAe,YAAY;gBAC5C,sBAAsB,eAAe,YAAY;gBACjD,gBAAgB,eAAe,YAAY;gBAC3C,yCACE,eAAe,YAAY;gBAC7B,yCACE,eAAe,YAAY;YAC/B;QACF;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAW;wBAAE;wBAAW;oBAAS;iBAAE;YAChD;YACA;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,OAAO;YAAE;YAAW;YAAU,MAAM;gBAAE;YAAmB;QAAE;IAC7D,GACA;QAAC;QAAQ;QAAW;KAAS;IAG/B,MAAM,CAAC,eAAe,GAAG,KAAK,KAAK,CAAC;QAAC;KAAiB;IAEtD,IAAA,kNAAS,EAAC;QACR,IAAI,gBAAgB;YAClB,KAAK,QAAQ,CACX,oBACA,iKAAkC,CAAC,eAAe,EAClD;gBAAE,gBAAgB;YAAK;QAE3B;IACF,GAAG;QAAC;QAAgB;KAAK;IAEzB,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,8JAA+B;oBACxC,eAAe,CAAA,IAAK,EAAE,CAAC,eAAe,EAAE,GAAG;oBAC3C,GAAG;oBACH,WAAW;;;;;;8BAEb,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kJAAS;;;;;8BACV,8OAAC,sRAA8B;oBAAC,SAAS;;;;;;8BACzC,8OAAC,kJAAS;;;;;8BACV,8OAAC,gRAA2B;oBAAC,SAAS;;;;;;8BACtC,8OAAC,4IAAM;oBAAC,MAAK;oBAAS,WAAU;oBAAmB,UAAU;;wBAC1D,2BAAa,8OAAC,oOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 1797, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/pillar/sections/pillar-reinforcement-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport { calculateArmorArea } from '@atlas/functions/forms/rectangular-beam-form-calculations'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport {\r\n  type PillarReinforcementFormInputs as FormSchema,\r\n  pillarReinforcementSchema,\r\n} from '@atlas/types/schemas/pillar-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FormSchema>\r\n  onSave: () => void\r\n}\r\n\r\nexport const PillarReinforcementForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.pillar.reinforcementBar')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n  const form = useForm<FormSchema>({\r\n    resolver: zodResolver(pillarReinforcementSchema),\r\n    defaultValues: {\r\n      top: {\r\n        diameter: defaultValues?.top?.diameter,\r\n        quantity: defaultValues?.top?.quantity,\r\n        area: defaultValues?.top?.area,\r\n      },\r\n      bottom: {\r\n        diameter: defaultValues?.bottom?.diameter,\r\n        quantity: defaultValues?.bottom?.quantity,\r\n        area: defaultValues?.bottom?.area,\r\n      },\r\n      transverse: {\r\n        diameter: defaultValues?.transverse?.diameter,\r\n        legs: defaultValues?.transverse?.legs,\r\n        area: defaultValues?.transverse?.area,\r\n        stirrupSpacing: defaultValues?.transverse?.stirrupSpacing,\r\n        stirrupInclination: defaultValues?.transverse?.stirrupInclination,\r\n        cornerRadius: defaultValues?.transverse?.cornerRadius,\r\n      },\r\n    },\r\n  })\r\n\r\n  const [\r\n    topDiameter,\r\n    topQuantity,\r\n    bottomDiameter,\r\n    bottomQuantity,\r\n    transverseDiameter,\r\n    transverseLegs,\r\n  ] = form.watch([\r\n    'top.diameter',\r\n    'top.quantity',\r\n    'bottom.diameter',\r\n    'bottom.quantity',\r\n    'transverse.diameter',\r\n    'transverse.legs',\r\n  ])\r\n\r\n  useEffect(() => {\r\n    if (topDiameter && topQuantity) {\r\n      form.setValue('top.area', calculateArmorArea(topDiameter, topQuantity))\r\n    }\r\n  }, [topDiameter, topQuantity, form])\r\n\r\n  useEffect(() => {\r\n    if (bottomDiameter && bottomQuantity) {\r\n      form.setValue(\r\n        'bottom.area',\r\n        calculateArmorArea(bottomDiameter, bottomQuantity),\r\n      )\r\n    }\r\n  }, [bottomDiameter, bottomQuantity, form])\r\n\r\n  useEffect(() => {\r\n    if (transverseDiameter && transverseLegs) {\r\n      form.setValue(\r\n        'transverse.area',\r\n        calculateArmorArea(transverseDiameter, transverseLegs),\r\n      )\r\n    }\r\n  }, [transverseDiameter, transverseLegs, form])\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (reinforcementBar: FormSchema) => {\r\n      mutate({ projectId, moduleId, body: { reinforcementBar } })\r\n    },\r\n    [mutate, projectId, moduleId],\r\n  )\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <h1 className=\"text-md font-bold\">{t('top.subtitle')}</h1>\r\n        <p className={cn('text-muted-foreground text-sm')}>\r\n          {t('top.description')}\r\n        </p>\r\n        <NumberFormInput control={form.control} name=\"top.diameter\" t={t} />\r\n        <NumberFormInput control={form.control} name=\"top.quantity\" t={t} />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"top.area\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <Separator />\r\n        <h1 className=\"text-md font-bold\">{t('bottom.subtitle')}</h1>\r\n        <p className={cn('text-muted-foreground text-sm')}>\r\n          {t('bottom.description')}\r\n        </p>\r\n        <NumberFormInput control={form.control} name=\"bottom.diameter\" t={t} />\r\n        <NumberFormInput control={form.control} name=\"bottom.quantity\" t={t} />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"bottom.area\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <Separator />\r\n        <h1 className=\"text-md font-bold\">{t('transverse.subtitle')}</h1>\r\n        <p className={cn('text-muted-foreground text-sm')}>\r\n          {t('transverse.description')}\r\n        </p>\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"transverse.diameter\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput control={form.control} name=\"transverse.legs\" t={t} />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"transverse.area\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"transverse.stirrupSpacing\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"transverse.stirrupInclination\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"transverse.cornerRadius\"\r\n          t={t}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AAUO,MAAM,0BAA0B,CAAC,EACtC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,cAAc,IAAA,wMAAc;IAClC,MAAM,OAAO,IAAA,yKAAO,EAAa;QAC/B,UAAU,IAAA,6KAAW,EAAC,sKAAyB;QAC/C,eAAe;YACb,KAAK;gBACH,UAAU,eAAe,KAAK;gBAC9B,UAAU,eAAe,KAAK;gBAC9B,MAAM,eAAe,KAAK;YAC5B;YACA,QAAQ;gBACN,UAAU,eAAe,QAAQ;gBACjC,UAAU,eAAe,QAAQ;gBACjC,MAAM,eAAe,QAAQ;YAC/B;YACA,YAAY;gBACV,UAAU,eAAe,YAAY;gBACrC,MAAM,eAAe,YAAY;gBACjC,MAAM,eAAe,YAAY;gBACjC,gBAAgB,eAAe,YAAY;gBAC3C,oBAAoB,eAAe,YAAY;gBAC/C,cAAc,eAAe,YAAY;YAC3C;QACF;IACF;IAEA,MAAM,CACJ,aACA,aACA,gBACA,gBACA,oBACA,eACD,GAAG,KAAK,KAAK,CAAC;QACb;QACA;QACA;QACA;QACA;QACA;KACD;IAED,IAAA,kNAAS,EAAC;QACR,IAAI,eAAe,aAAa;YAC9B,KAAK,QAAQ,CAAC,YAAY,IAAA,8LAAkB,EAAC,aAAa;QAC5D;IACF,GAAG;QAAC;QAAa;QAAa;KAAK;IAEnC,IAAA,kNAAS,EAAC;QACR,IAAI,kBAAkB,gBAAgB;YACpC,KAAK,QAAQ,CACX,eACA,IAAA,8LAAkB,EAAC,gBAAgB;QAEvC;IACF,GAAG;QAAC;QAAgB;QAAgB;KAAK;IAEzC,IAAA,kNAAS,EAAC;QACR,IAAI,sBAAsB,gBAAgB;YACxC,KAAK,QAAQ,CACX,mBACA,IAAA,8LAAkB,EAAC,oBAAoB;QAE3C;IACF,GAAG;QAAC;QAAoB;QAAgB;KAAK;IAE7C,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAW;wBAAE;wBAAW;oBAAS;iBAAE;YAChD;YACA;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,OAAO;YAAE;YAAW;YAAU,MAAM;gBAAE;YAAiB;QAAE;IAC3D,GACA;QAAC;QAAQ;QAAW;KAAS;IAG/B,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,8OAAC;oBAAG,WAAU;8BAAqB,EAAE;;;;;;8BACrC,8OAAC;oBAAE,WAAW,IAAA,yHAAE,EAAC;8BACd,EAAE;;;;;;8BAEL,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAe,GAAG;;;;;;8BAC/D,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAe,GAAG;;;;;;8BAC/D,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kJAAS;;;;;8BACV,8OAAC;oBAAG,WAAU;8BAAqB,EAAE;;;;;;8BACrC,8OAAC;oBAAE,WAAW,IAAA,yHAAE,EAAC;8BACd,EAAE;;;;;;8BAEL,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAkB,GAAG;;;;;;8BAClE,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAkB,GAAG;;;;;;8BAClE,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kJAAS;;;;;8BACV,8OAAC;oBAAG,WAAU;8BAAqB,EAAE;;;;;;8BACrC,8OAAC;oBAAE,WAAW,IAAA,yHAAE,EAAC;8BACd,EAAE;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAkB,GAAG;;;;;;8BAClE,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,8OAAC,4IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,8OAAC,oOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 2140, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/pillar/pillar-params-form.tsx"], "sourcesContent": ["import { PillarCalculations } from '@atlas/components/common/atlas/module-detail/params-forms/pillar/pillar-calculations'\r\nimport { PillarGeneralForm } from '@atlas/components/common/atlas/module-detail/params-forms/pillar/sections/pillar-general-form'\r\nimport { PillarGeometryForm } from '@atlas/components/common/atlas/module-detail/params-forms/pillar/sections/pillar-geometry-form'\r\nimport { PillarMaterialForm } from '@atlas/components/common/atlas/module-detail/params-forms/pillar/sections/pillar-material-form'\r\nimport { PillarReinforcementForm } from '@atlas/components/common/atlas/module-detail/params-forms/pillar/sections/pillar-reinforcement-form'\r\nimport {\r\n  Accordion as AccordionComponent,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from '@atlas/components/ui/accordion'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport type {\r\n  Module,\r\n  ModuleWithParamsPillar,\r\n} from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { pillarParamsCheckSchema } from '@atlas/types/schemas/pillar-form'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useMemo, useState } from 'react'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  module: ModuleWithParamsPillar\r\n}\r\n\r\nexport const PillarParamsForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  module,\r\n}: Props) => {\r\n  const { params } = module\r\n  const [openItems, setOpenItems] = useState(['0'])\r\n  const t = useTranslations('forms.project-params.pillar')\r\n\r\n  const handleItemSaved = useCallback((id: string) => {\r\n    const nextId = String(Number(id) + 1)\r\n    setOpenItems(old => {\r\n      const temp = old.filter(v => v !== id)\r\n      return old.includes(nextId) ? temp : [...temp, nextId]\r\n    })\r\n  }, [])\r\n\r\n  const { success } = useMemo(\r\n    () => pillarParamsCheckSchema.safeParse(params),\r\n    [params],\r\n  )\r\n\r\n  return (\r\n    <div>\r\n      <AccordionComponent\r\n        type=\"multiple\"\r\n        value={openItems}\r\n        onValueChange={setOpenItems}\r\n      >\r\n        <AccordionItem value=\"0\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">{t('general.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <PillarGeneralForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={{\r\n                initialDeformation: params?.initialDeformation,\r\n              }}\r\n              onSave={() => handleItemSaved('0')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"1\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">{t('geometry.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <PillarGeometryForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={params?.geometry}\r\n              onSave={() => handleItemSaved('1')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"2\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('reinforcementBar.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <PillarReinforcementForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={params?.reinforcementBar}\r\n              onSave={() => handleItemSaved('2')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"3\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('materialProperties.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <PillarMaterialForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={params?.materialProperties}\r\n              onSave={() => handleItemSaved('3')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n      </AccordionComponent>\r\n\r\n      {success && (\r\n        <>\r\n          <Separator />\r\n          <PillarCalculations\r\n            module={module}\r\n            session={session}\r\n            projectId={projectId}\r\n          />\r\n        </>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAMA;AAMA;AAEA;AACA;;;;;;;;;;;;AASO,MAAM,mBAAmB,CAAC,EAC/B,OAAO,EACP,SAAS,EACT,QAAQ,EACR,MAAM,EACA;IACN,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;QAAC;KAAI;IAChD,MAAM,IAAI,IAAA,yNAAe,EAAC;IAE1B,MAAM,kBAAkB,IAAA,oNAAW,EAAC,CAAC;QACnC,MAAM,SAAS,OAAO,OAAO,MAAM;QACnC,aAAa,CAAA;YACX,MAAM,OAAO,IAAI,MAAM,CAAC,CAAA,IAAK,MAAM;YACnC,OAAO,IAAI,QAAQ,CAAC,UAAU,OAAO;mBAAI;gBAAM;aAAO;QACxD;IACF,GAAG,EAAE;IAEL,MAAM,EAAE,OAAO,EAAE,GAAG,IAAA,gNAAO,EACzB,IAAM,oKAAuB,CAAC,SAAS,CAAC,SACxC;QAAC;KAAO;IAGV,qBACE,8OAAC;;0BACC,8OAAC,kJAAkB;gBACjB,MAAK;gBACL,OAAO;gBACP,eAAe;;kCAEf,8OAAC,sJAAa;wBAAC,OAAM;;0CACnB,8OAAC,yJAAgB;0CACf,cAAA,8OAAC;oCAAG,WAAU;8CAAuB,EAAE;;;;;;;;;;;0CAEzC,8OAAC,yJAAgB;0CACf,cAAA,8OAAC,oPAAiB;oCAChB,SAAS;oCACT,WAAW;oCACX,UAAU;oCACV,eAAe;wCACb,oBAAoB,QAAQ;oCAC9B;oCACA,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;kCAIpC,8OAAC,sJAAa;wBAAC,OAAM;;0CACnB,8OAAC,yJAAgB;0CACf,cAAA,8OAAC;oCAAG,WAAU;8CAAuB,EAAE;;;;;;;;;;;0CAEzC,8OAAC,yJAAgB;0CACf,cAAA,8OAAC,sPAAkB;oCACjB,SAAS;oCACT,WAAW;oCACX,UAAU;oCACV,eAAe,QAAQ;oCACvB,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;kCAIpC,8OAAC,sJAAa;wBAAC,OAAM;;0CACnB,8OAAC,yJAAgB;0CACf,cAAA,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;;;;;;0CAGP,8OAAC,yJAAgB;0CACf,cAAA,8OAAC,gQAAuB;oCACtB,SAAS;oCACT,WAAW;oCACX,UAAU;oCACV,eAAe,QAAQ;oCACvB,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;kCAIpC,8OAAC,sJAAa;wBAAC,OAAM;;0CACnB,8OAAC,yJAAgB;0CACf,cAAA,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;;;;;;0CAGP,8OAAC,yJAAgB;0CACf,cAAA,8OAAC,sPAAkB;oCACjB,SAAS;oCACT,WAAW;oCACX,UAAU;oCACV,eAAe,QAAQ;oCACvB,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;YAMrC,yBACC;;kCACE,8OAAC,kJAAS;;;;;kCACV,8OAAC,sOAAkB;wBACjB,QAAQ;wBACR,SAAS;wBACT,WAAW;;;;;;;;;;;;;;AAMvB", "debugId": null}}]}