import {
  MODULE_MATERIAL_CLASS,
  MODULE_MATERIAL_KNOWLEDGE_LEVEL,
} from '@atlas/constants/module'
import { z } from 'zod'

export const slabGeometrySchema = z.object({
  joistFormwork: z.enum(['T_SHAPED', 'RECTANGULAR_WITHOUT_SLAB']),
  joistBase: z.number().positive(), // D9 - b [mm]
  joistWebHeight: z.number().positive(), // D10 - h [mm]
  existingSlabHeight: z.number().min(0), // D11 - s [mm] (0 if rectangular)
  joistSpacing: z.number().positive(), // D12 - i [mm]
  bottomRebarCover: z.number().positive(), // D14 - c1 [mm]
  topRebarCover: z.number().positive(), // D15 - c2 [mm]
  structuralScheme: z.enum(['SIMPLY_SUPPORTED', 'CANTILEVER']),
  totalSlabThickness: z.number(), // D13 - H [mm] (calculated)
  effectiveDepth: z.number(), // D16 - d [mm] (calculated)
})

export const slabRebarSchema = z.object({
  // Span section (campata) - only for simply supported
  spanBottomRebar: z.object({
    diameter: z.number().positive().optional(), // D21 [mm]
    quantity: z.number().positive().optional(), // D22 [-]
    area: z.number().optional(), // D23 [mm2] (calculated)
  }),
  spanTopRebar: z.object({
    diameter: z.number().positive().optional(), // D24 [mm]
    quantity: z.number().positive().optional(), // D25 [-]
    area: z.number().optional(), // D26 [mm2] (calculated)
  }),
  // Support section (appoggio)
  supportBottomRebar: z.object({
    diameter: z.number().positive().optional(), // D28 [mm]
    quantity: z.number().positive().optional(), // D29 [-]
    area: z.number().optional(), // D30 [mm2] (calculated)
  }),
  supportTopRebar: z.object({
    diameter: z.number().positive().optional(), // D31 [mm]
    quantity: z.number().positive().optional(), // D32 [-]
    area: z.number().optional(), // D33 [mm2] (calculated)
  }),
  // Additional reinforcement (optional)
  additionalRebar: z
    .object({
      diameter: z.number().positive().optional(), // D78 [mm]
      quantity: z.number().positive().optional(), // D79 [-]
      area: z.number().optional(), // D80 [mm2] (calculated)
    })
    .optional(),
  additionalSteelElasticModulus: z.number().positive().optional(), // D81 [MPa]
})

export const slabMaterialSchema = z.object({
  concreteClassKnowledgeLevel: z.enum(MODULE_MATERIAL_KNOWLEDGE_LEVEL),
  steelGradeKnowledgeLevel: z.enum(MODULE_MATERIAL_KNOWLEDGE_LEVEL),
  concreteMaterialClass: z.enum(MODULE_MATERIAL_CLASS),
  steelMaterialClass: z.enum(MODULE_MATERIAL_CLASS),
  concreteClass: z.object({
    id: z.string().optional(),
    name: z.string().optional(),
    cubeCompressiveStrength: z.number().optional(),
    cylinderCompressiveStrength: z.number().optional(),
    averageCompressiveStrength: z.number().optional(),
    averageTensileStrength: z.number().optional(),
    elasticModulus: z.number().optional(),
    designCompressiveStrengthForBrittleMechanisms: z.number().optional(),
    designTensileStrengthForBrittleMechanisms: z.number().optional(),
    designCompressiveStrengthForDuctileMechanisms: z.number().optional(),
  }),
  steelGrade: z.object({
    id: z.string().optional(),
    name: z.string().optional(),
    yieldStrength: z.number().optional(),
    tensileStrength: z.number().optional(),
    elongationPercentage: z.number().optional(),
    elasticModulus: z.number().optional(),
    designYieldStrengthForBrittleMechanisms: z.number().optional(),
    designYieldStrengthForDuctileMechanisms: z.number().optional(),
  }),
})

export const slabFrcReinforcementSchema = z.object({
  frcReinforcementType: z.string(), // C64 - Product.name
  toughnessClassAndFibersType: z.string().optional(), // Combined field for display only --- IGNORE ---
  toughnessClass: z.string().optional(), // Extracted from toughnessClassAndFibersType
  fiberType: z.string().optional(), // Extracted from toughnessClassAndFibersType
  // Product properties
  frcSlabThickness: z.number().min(0).max(40), // D65 - hR [mm] (0-40)
  elasticModulus: z.number().positive().optional(), // D66 - E [MPa]
  characteristicCylindricalCompressiveStrengthFrcMatrix: z
    .number()
    .positive()
    .optional(), // D67 - fFck [MPa]
  frcCharacteristicTensileStrength: z.number().positive().optional(), // D71 - fFtu,k [MPa]
  tensileStrength: z.number().positive().optional(), // Product tensile strength [MPa]
  adhesionToConcrete: z.number().positive().optional(), // Adhesion to concrete [MPa]
  specificWeight: z.number().positive().optional(), // D74 - ρ [kN/m3]
  // Additional reinforcement for support (optional)
  supportAdditionalRebar: z
    .object({
      diameter: z.number().positive().optional(), // D78 [mm]
      quantity: z.number().positive().optional(), // D79 [-]
      elasticModulus: z.number().positive().optional(), // D81 [MPa]
    })
    .optional(),
})

export const slabFormSchema = z.object({
  initialDeformation: z.number().optional(),
  geometry: slabGeometrySchema.optional(),
  slabRebar: slabRebarSchema.optional(),
  materialProperties: slabMaterialSchema.optional(),
  slabFrcReinforcement: slabFrcReinforcementSchema.optional(),
})

export const slabParamsCheckSchema = z.object({
  initialDeformation: z.number(),
  geometry: slabGeometrySchema,
  slabRebar: slabRebarSchema,
  materialProperties: slabMaterialSchema,
  slabFrcReinforcement: slabFrcReinforcementSchema,
})

// Calculation schemas for the 4 verification types
export const slabFlexuralPositiveCalculationSchema = z.object({
  calculationType: z.literal('FLEXURAL_VERIFY'),
  input: z.object({
    bendingMoment: z.number().positive(),
    isSpanVerification: z.literal(true),
    productInput: z.object({
      id: z.string(),
      name: z.string(),
      sourceType: z.enum(['DATABASE', 'CUSTOM']),
      frcSlabThickness: z.number().positive().optional(),
      elasticModulus: z.number().positive().optional(),
      cylindricCompressiveStrength: z.number().positive().optional(),
      characteristicTensileStrength: z.number().positive().optional(),
      specificWeight: z.number().positive().optional(),
      adhesionToConcrete: z.number().positive().optional(),
      fiberType: z.string().optional(),
    }),
  }),
})

export const slabFlexuralNegativeCalculationSchema = z.object({
  calculationType: z.literal('FLEXURAL_VERIFY'),
  input: z.object({
    bendingMoment: z.number().positive(),
    isSpanVerification: z.literal(false),
    productInput: z.object({
      id: z.string(),
      name: z.string(),
      sourceType: z.enum(['DATABASE', 'CUSTOM']),
      frcSlabThickness: z.number().positive().optional(),
      elasticModulus: z.number().positive().optional(),
      cylindricCompressiveStrength: z.number().positive().optional(),
      characteristicTensileStrength: z.number().positive().optional(),
      specificWeight: z.number().positive().optional(),
      adhesionToConcrete: z.number().positive().optional(),
      fiberType: z.string().optional(),
    }),
  }),
})

export const slabShearCalculationSchema = z.object({
  calculationType: z.literal('SHEAR_VERIFY'),
  input: z.object({
    sectionFillType: z.string(),
    shearForce: z.number().positive(),
    productInput: z.object({
      id: z.string(),
      name: z.string(),
      sourceType: z.enum(['DATABASE', 'CUSTOM']),
      frcSlabThickness: z.number().positive().optional(),
      elasticModulus: z.number().positive().optional(),
      cylindricCompressiveStrength: z.number().positive().optional(),
      characteristicTensileStrength: z.number().positive().optional(),
      specificWeight: z.number().positive().optional(),
      adhesionToConcrete: z.number().positive().optional(),
      fiberType: z.string().optional(),
    }),
    isCantilever: z.boolean(),
  }),
})

export const slabInterfaceSlipCalculationSchema = z.object({
  calculationType: z.literal('INTERFACE_SLIP_VERIFY'),
  input: z.object({
    shearForce: z.number().positive(),
    productInput: z.object({
      id: z.string(),
      name: z.string(),
      sourceType: z.enum(['DATABASE', 'CUSTOM']),
      frcSlabThickness: z.number().positive().optional(),
      elasticModulus: z.number().positive().optional(),
      cylindricCompressiveStrength: z.number().positive().optional(),
      characteristicTensileStrength: z.number().positive().optional(),
      specificWeight: z.number().positive().optional(),
      adhesionToConcrete: z.number().positive().optional(),
      fiberType: z.string().optional(),
    }),
  }),
})

export type SlabForm = z.infer<typeof slabFormSchema>
export type SlabGeometryFormInputs = z.infer<typeof slabGeometrySchema>
export type SlabRebarFormInputs = z.infer<typeof slabRebarSchema>
export type SlabMaterialFormInputs = z.infer<typeof slabMaterialSchema>
export type SlabFrcReinforcementFormInputs = z.infer<
  typeof slabFrcReinforcementSchema
>
export type SlabFlexuralPositiveCalculationInput = z.infer<
  typeof slabFlexuralPositiveCalculationSchema
>
export type SlabFlexuralNegativeCalculationInput = z.infer<
  typeof slabFlexuralNegativeCalculationSchema
>
export type SlabShearCalculationInput = z.infer<
  typeof slabShearCalculationSchema
>
export type SlabInterfaceSlipCalculationInput = z.infer<
  typeof slabInterfaceSlipCalculationSchema
>
