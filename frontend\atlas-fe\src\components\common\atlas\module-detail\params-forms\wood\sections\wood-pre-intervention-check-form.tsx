import { NumberFormInput } from '@atlas/components/common/form/number-form-input'
import { Badge } from '@atlas/components/ui/badge'
import { Button } from '@atlas/components/ui/button'
import { Form } from '@atlas/components/ui/form'
import { Separator } from '@atlas/components/ui/separator'
import type { Module } from '@atlas/lib/api/modules/schemas/modules'
import { woodPreInterventionCheckSchema } from '@atlas/lib/api/modules/schemas/wood-params'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'
import { zodResolver } from '@hookform/resolvers/zod'
import { Loader2Icon } from 'lucide-react'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useCallback, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import type { z } from 'zod'

// Custom form schema that extends the API schema
const woodPreInterventionCheckFormSchema = woodPreInterventionCheckSchema

type FormSchema = z.infer<typeof woodPreInterventionCheckFormSchema>

type Props = {
  session: Session
  projectId: Project['id']
  moduleId: Module['id']
  defaultValues?: Partial<FormSchema>
  geometryProperties?: any // Geometry properties from form 3
  onSave: () => void
  initialDeformation?: any
}

export const WoodPreInterventionCheckForm = ({
  session,
  projectId,
  moduleId,
  defaultValues,
  geometryProperties,
  onSave,
  initialDeformation,
}: Props) => {
  const t = useTranslations('forms.project-params.wood.preIntervationCheck')
  const tAction = useTranslations('actions.save-project-params.messages')
  const tCommon = useTranslations('actions.common')

  const form = useForm<FormSchema>({
    resolver: zodResolver(woodPreInterventionCheckFormSchema),
    defaultValues: {
      maximumBendingMoment: defaultValues?.maximumBendingMoment ?? 0,
      maximumShearForce: defaultValues?.maximumShearForce ?? 0,
      designBendingStress: defaultValues?.designBendingStress ?? 0,
      designBendingStrength: defaultValues?.designBendingStrength ?? 0,
      bendingCheck: defaultValues?.bendingCheck ?? 0,
      designShearStress: defaultValues?.designShearStress ?? 0,
      designShearStrength: defaultValues?.designShearStrength ?? 0,
      shearCheck: defaultValues?.shearCheck ?? 0,
      permanentLoadPerLinearMeter:
        defaultValues?.permanentLoadPerLinearMeter ?? 0,
      imposedLoadPerLinearMeter: defaultValues?.imposedLoadPerLinearMeter ?? 0,
      instantaneousDeflectionPermanentLoad:
        defaultValues?.instantaneousDeflectionPermanentLoad ?? 0,
      instantaneousDeflectionImposedLoad:
        defaultValues?.instantaneousDeflectionImposedLoad ?? 0,
      instantaneousDeflectionTotalLoads:
        defaultValues?.instantaneousDeflectionTotalLoads ?? 0,
      deformabilityCheck: defaultValues?.deformabilityCheck ?? 0,
      combinationFactor: defaultValues?.combinationFactor ?? 0.3,
      finalDeflectionTotalLoads: defaultValues?.finalDeflectionTotalLoads ?? 0,
      finalCheckResult: defaultValues?.finalCheckResult ?? 0,
    },
  })

  // Reset form when defaultValues change
  useEffect(() => {
    if (defaultValues) {
      form.reset({
        maximumBendingMoment: defaultValues.maximumBendingMoment ?? 0,
        maximumShearForce: defaultValues.maximumShearForce ?? 0,
        designBendingStress: defaultValues.designBendingStress ?? 0,
        designBendingStrength: defaultValues.designBendingStrength ?? 0,
        bendingCheck: defaultValues.bendingCheck ?? 0,
        designShearStress: defaultValues.designShearStress ?? 0,
        designShearStrength: defaultValues.designShearStrength ?? 0,
        shearCheck: defaultValues.shearCheck ?? 0,
        permanentLoadPerLinearMeter:
          defaultValues.permanentLoadPerLinearMeter ?? 0,
        imposedLoadPerLinearMeter: defaultValues.imposedLoadPerLinearMeter ?? 0,
        instantaneousDeflectionPermanentLoad:
          defaultValues.instantaneousDeflectionPermanentLoad ?? 0,
        instantaneousDeflectionImposedLoad:
          defaultValues.instantaneousDeflectionImposedLoad ?? 0,
        instantaneousDeflectionTotalLoads:
          defaultValues.instantaneousDeflectionTotalLoads ?? 0,
        deformabilityCheck: defaultValues.deformabilityCheck ?? 0,
        combinationFactor: defaultValues.combinationFactor ?? 0.3,
        finalDeflectionTotalLoads: defaultValues.finalDeflectionTotalLoads ?? 0,
        finalCheckResult: defaultValues.finalCheckResult ?? 0,
      })
    }
  }, [defaultValues, form])

  const { mutate, isPending } = useSaveModuleParamsMutation(
    session.accessToken,
    {
      onSuccess: () => {
        toast.success(tAction('edit.success'))
        // Query invalidation is handled by the mutation hook itself
        onSave()
      },
      onError: error => {
        toast.error(tAction('edit.failure', { error: error.message }))
      },
    },
  )

  const handleFormSubmit = useCallback(
    (data: FormSchema) => {
      // Send as preIntervationCheck to match the API structure
      mutate({
        projectId,
        moduleId,
        body: { preIntervationCheck: data } as any,
      })
    },
    [mutate, projectId, moduleId],
  )

  // Watch form values for calculations
  const [
    maximumBendingMoment,
    maximumShearForce,
    permanentLoadPerLinearMeter,
    imposedLoadPerLinearMeter,
    combinationFactor,
  ] = form.watch([
    'maximumBendingMoment',
    'maximumShearForce',
    'permanentLoadPerLinearMeter',
    'imposedLoadPerLinearMeter',
    'combinationFactor',
  ])

  // Helper function to determine check result
  const getCheckResult = (
    value: number,
    threshold: number,
    isGreaterThan: boolean = true,
  ) => {
    if (isGreaterThan) {
      return value > threshold ? 'Non-satisfy' : 'Satisfy'
    }
    return value < threshold ? 'Non-satisfy' : 'Satisfy'
  }

  // Helper function to get badge variant
  const getBadgeVariant = (result: string) => {
    return result === 'Satisfy' ? 'default' : 'destructive'
  }

  // Calculations based on form values and properties from other forms
  useEffect(() => {
    if (maximumBendingMoment && geometryProperties?.sectionModulus) {
      const designBendingStress =
        maximumBendingMoment / (geometryProperties.sectionModulus * 1000)
      form.setValue('designBendingStress', designBendingStress)
    }
  }, [maximumBendingMoment, geometryProperties?.sectionModulus, form])

  useEffect(() => {
    if (geometryProperties?.designBendingStrength) {
      form.setValue(
        'designBendingStrength',
        geometryProperties.designBendingStrength,
      )
    }
  }, [geometryProperties?.designBendingStrength, form])

  useEffect(() => {
    const designBendingStress = form.getValues('designBendingStress')
    const designBendingStrength = form.getValues('designBendingStrength')
    if (designBendingStress && designBendingStrength) {
      const bendingCheck = designBendingStress / designBendingStrength
      form.setValue('bendingCheck', bendingCheck)
    }
  }, [form])

  useEffect(() => {
    if (
      maximumShearForce &&
      geometryProperties?.beamSectionHeight &&
      geometryProperties?.beamSectionWidth
    ) {
      const designShearStress =
        (3 * 1000 * maximumShearForce) /
        (2 *
          1000 *
          geometryProperties.beamSectionHeight *
          1000 *
          geometryProperties.beamSectionWidth)
      form.setValue('designShearStress', designShearStress)
    }
  }, [
    maximumShearForce,
    geometryProperties?.beamSectionHeight,
    geometryProperties?.beamSectionWidth,
    form,
  ])

  useEffect(() => {
    if (geometryProperties?.designShearStrength) {
      form.setValue(
        'designShearStrength',
        geometryProperties.designShearStrength,
      )
    }
  }, [geometryProperties?.designShearStrength, form])

  useEffect(() => {
    const designShearStress = form.getValues('designShearStress')
    const designShearStrength = form.getValues('designShearStrength')
    if (designShearStress && designShearStrength) {
      const shearCheck = designShearStress / designShearStrength
      form.setValue('shearCheck', shearCheck)
    }
  }, [form])

  // Deflection calculations
  useEffect(() => {
    if (
      permanentLoadPerLinearMeter &&
      geometryProperties?.beamSpan &&
      geometryProperties?.elasticityInstantaneousModulus &&
      geometryProperties?.inertiaMomentAboutY
    ) {
      const instantaneousDeflectionPermanentLoad =
        (100 *
          (5 *
            permanentLoadPerLinearMeter *
            geometryProperties.beamSpan ** 4)) /
        (384 *
          1000 *
          geometryProperties.elasticityInstantaneousModulus *
          geometryProperties.inertiaMomentAboutY)
      form.setValue(
        'instantaneousDeflectionPermanentLoad',
        instantaneousDeflectionPermanentLoad,
      )
    }
  }, [
    permanentLoadPerLinearMeter,
    geometryProperties?.beamSpan,
    geometryProperties?.elasticityInstantaneousModulus,
    geometryProperties?.inertiaMomentAboutY,
    form,
  ])

  useEffect(() => {
    if (
      imposedLoadPerLinearMeter &&
      geometryProperties?.beamSpan &&
      geometryProperties?.elasticityInstantaneousModulus &&
      geometryProperties?.inertiaMomentAboutY
    ) {
      const instantaneousDeflectionImposedLoad =
        (100 *
          (5 * imposedLoadPerLinearMeter * geometryProperties.beamSpan ** 4)) /
        (384 *
          1000 *
          geometryProperties.elasticityInstantaneousModulus *
          geometryProperties.inertiaMomentAboutY)
      form.setValue(
        'instantaneousDeflectionImposedLoad',
        instantaneousDeflectionImposedLoad,
      )
    }
  }, [
    imposedLoadPerLinearMeter,
    geometryProperties?.beamSpan,
    geometryProperties?.elasticityInstantaneousModulus,
    geometryProperties?.inertiaMomentAboutY,
    form,
  ])

  useEffect(() => {
    const instantaneousDeflectionPermanentLoad = form.getValues(
      'instantaneousDeflectionPermanentLoad',
    )
    const instantaneousDeflectionImposedLoad = form.getValues(
      'instantaneousDeflectionImposedLoad',
    )
    if (
      instantaneousDeflectionPermanentLoad &&
      instantaneousDeflectionImposedLoad
    ) {
      // Note: Adding 0 for the "peli field" as mentioned in requirements
      const instantaneousDeflectionTotalLoads =
        instantaneousDeflectionPermanentLoad +
        instantaneousDeflectionImposedLoad +
        initialDeformation
      form.setValue(
        'instantaneousDeflectionTotalLoads',
        instantaneousDeflectionTotalLoads,
      )
    }
  }, [form, initialDeformation])

  useEffect(() => {
    const instantaneousDeflectionTotalLoads = form.getValues(
      'instantaneousDeflectionTotalLoads',
    )
    if (instantaneousDeflectionTotalLoads && geometryProperties?.beamSpan) {
      const deformabilityCheck =
        (100 * geometryProperties.beamSpan) / instantaneousDeflectionTotalLoads
      form.setValue('deformabilityCheck', deformabilityCheck)
    }
  }, [geometryProperties?.beamSpan, form])

  useEffect(() => {
    const instantaneousDeflectionPermanentLoad = form.getValues(
      'instantaneousDeflectionPermanentLoad',
    )
    const instantaneousDeflectionImposedLoad = form.getValues(
      'instantaneousDeflectionImposedLoad',
    )
    if (
      instantaneousDeflectionPermanentLoad &&
      instantaneousDeflectionImposedLoad &&
      combinationFactor &&
      geometryProperties?.deformabilityFactor
    ) {
      // Note: Adding 0 for the "peli field" as mentioned in requirements
      const finalDeflectionTotalLoads =
        instantaneousDeflectionPermanentLoad *
          (1 + geometryProperties.deformabilityFactor) +
        instantaneousDeflectionImposedLoad *
          (1 + combinationFactor * geometryProperties.deformabilityFactor) +
        initialDeformation
      form.setValue('finalDeflectionTotalLoads', finalDeflectionTotalLoads)
    }
  }, [
    combinationFactor,
    geometryProperties?.deformabilityFactor,
    initialDeformation,
    form,
  ])

  useEffect(() => {
    const finalDeflectionTotalLoads = form.getValues(
      'finalDeflectionTotalLoads',
    )
    if (finalDeflectionTotalLoads && geometryProperties?.beamSpan) {
      const finalCheckResult =
        (100 * geometryProperties.beamSpan) / finalDeflectionTotalLoads
      form.setValue('finalCheckResult', finalCheckResult)
    }
  }, [geometryProperties?.beamSpan, form])

  return (
    <Form {...form}>
      <form
        className="space-y-4 rounded-md border p-4"
        onSubmit={form.handleSubmit(handleFormSubmit)}
      >
        {/* User Input Fields */}
        <NumberFormInput
          control={form.control}
          name="maximumBendingMoment"
          t={t}
          required={true}
        />
        <NumberFormInput
          control={form.control}
          name="maximumShearForce"
          t={t}
          required={true}
        />

        {/* Calculated Bending Fields */}
        <div className="space-y-4">
          <Separator />
          <NumberFormInput
            control={form.control}
            name="designBendingStress"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />
          <NumberFormInput
            control={form.control}
            name="designBendingStrength"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />
          <div className="flex items-start gap-4 flex">
            <NumberFormInput
              control={form.control}
              name="bendingCheck"
              t={t}
              disabled={true}
              fieldContainerClassName="flex-1"
              decimalPlaces={3}
            />
            <Badge
              variant={getBadgeVariant(
                getCheckResult(form.watch('bendingCheck'), 1),
              )}
              className="mt-7"
            >
              {getCheckResult(form.watch('bendingCheck'), 1)}
            </Badge>
          </div>
        </div>

        {/* Calculated Shear Fields */}
        <div className="space-y-4">
          <Separator />
          <NumberFormInput
            control={form.control}
            name="designShearStress"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />
          <NumberFormInput
            control={form.control}
            name="designShearStrength"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />
          <div className="flex items-start gap-4 flex">
            <NumberFormInput
              control={form.control}
              name="shearCheck"
              t={t}
              disabled={true}
              fieldContainerClassName="flex-1"
              decimalPlaces={3}
            />
            <Badge
              variant={getBadgeVariant(
                getCheckResult(form.watch('shearCheck'), 1),
              )}
              className="mt-7"
            >
              {getCheckResult(form.watch('shearCheck'), 1)}
            </Badge>
          </div>
        </div>

        {/* Load Input Fields */}
        <div className="space-y-4">
          <Separator />
          <NumberFormInput
            control={form.control}
            name="permanentLoadPerLinearMeter"
            t={t}
            required={true}
          />
          <NumberFormInput
            control={form.control}
            name="imposedLoadPerLinearMeter"
            t={t}
            required={true}
          />
        </div>

        {/* Deflection Analysis */}
        <div className="space-y-4">
          <NumberFormInput
            control={form.control}
            name="instantaneousDeflectionPermanentLoad"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />
          <NumberFormInput
            control={form.control}
            name="instantaneousDeflectionImposedLoad"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />
          <NumberFormInput
            control={form.control}
            name="instantaneousDeflectionTotalLoads"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />
          <div className="flex items-start gap-4 flex">
            <NumberFormInput
              control={form.control}
              name="deformabilityCheck"
              t={t}
              disabled={true}
              fieldContainerClassName="flex-1"
              decimalPlaces={3}
            />
            <Badge
              variant={getBadgeVariant(
                getCheckResult(form.watch('deformabilityCheck'), 300, false),
              )}
              className="mt-7"
            >
              {getCheckResult(form.watch('deformabilityCheck'), 300, false)}
            </Badge>
          </div>
        </div>

        {/* Final Analysis */}
        <div className="space-y-4">
          <NumberFormInput
            control={form.control}
            name="combinationFactor"
            t={t}
            required={false}
            disabled={true}
          />
          <NumberFormInput
            control={form.control}
            name="finalDeflectionTotalLoads"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />
          <div className="flex items-start gap-4 flex">
            <NumberFormInput
              control={form.control}
              name="finalCheckResult"
              t={t}
              disabled={true}
              fieldContainerClassName="flex-1"
              decimalPlaces={3}
            />
            <Badge
              variant={getBadgeVariant(
                getCheckResult(form.watch('finalCheckResult'), 200, false),
              )}
              className="mt-7"
            >
              {getCheckResult(form.watch('finalCheckResult'), 200, false)}
            </Badge>
          </div>
        </div>

        <Button type="submit" className="w-full sm:w-auto" disabled={isPending}>
          {isPending && <Loader2Icon className="animate-spin" />}
          {tCommon('save')}
        </Button>
      </form>
    </Form>
  )
}
