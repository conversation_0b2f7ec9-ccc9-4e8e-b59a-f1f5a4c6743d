import {
  Accordion as AccordionComponent,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@atlas/components/ui/accordion'
import type {
  Module,
  ModuleWithParamsFrcmColumn,
} from '@atlas/lib/api/modules/schemas/modules'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import type { FrcmColumnParamsSchemaInput } from '@atlas/types/schemas/masonry/frcm-column-form'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useCallback, useState } from 'react'
import { FrcmColumnConfinementReinforcementCalculation } from './calculations/frcm-column-confinement-reinforcement-calculation'
import { FrcmColumnGeometryForm } from './sections/frcm-column-geometry-form'
import { FrcmColumnMasonryCharacteristicsForm } from './sections/frcm-column-masonry-characteristics-form'
import { FrcmColumnStressForm } from './sections/frcm-column-stress-form'

type Props = {
  session: Session
  projectId: Project['id']
  moduleId: Module['id']
  module: ModuleWithParamsFrcmColumn
}

export const FrcmColumnParamsForm = ({
  session,
  projectId,
  moduleId,
  module,
}: Props) => {
  const [params, setParams] = useState<FrcmColumnParamsSchemaInput>(
    module?.params ?? ({} as FrcmColumnParamsSchemaInput),
  )

  const [openItems, setOpenItems] = useState(['0'])
  const t = useTranslations('forms.project-params.frcm-column')

  const handleItemSaved = useCallback((id: string) => {
    const nextId = String(Number(id) + 1)
    setOpenItems(old => {
      const temp = old.filter(v => v !== id)
      return old.includes(nextId) ? temp : [...temp, nextId]
    })
  }, [])

  return (
    <div>
      <AccordionComponent
        type="multiple"
        value={openItems}
        onValueChange={setOpenItems}
      >
        <AccordionItem value="0">
          <AccordionTrigger>
            <h3 className="text-lg font-medium">{t('geometry.title')}</h3>
          </AccordionTrigger>
          <AccordionContent>
            <FrcmColumnGeometryForm
              session={session}
              projectId={projectId}
              moduleId={moduleId}
              defaultValues={params?.geometry}
              setParams={setParams}
              params={params}
              onSave={() => handleItemSaved('0')}
            />
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="1">
          <AccordionTrigger>
            <h3 className="text-lg font-medium">
              {t('masonry-characteristics.title')}
            </h3>
          </AccordionTrigger>
          <AccordionContent>
            <FrcmColumnMasonryCharacteristicsForm
              session={session}
              projectId={projectId}
              moduleId={moduleId}
              defaultValues={params?.masonryCharacteristics}
              setParams={setParams}
              params={params}
              onSave={() => handleItemSaved('1')}
            />
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="2">
          <AccordionTrigger>
            <h3 className="text-lg font-medium">{t('stress.title')}</h3>
          </AccordionTrigger>
          <AccordionContent>
            <FrcmColumnStressForm
              session={session}
              projectId={projectId}
              moduleId={moduleId}
              defaultValues={params.stress}
              params={params}
              setParams={setParams}
              onSave={() => handleItemSaved('2')}
            />
          </AccordionContent>
        </AccordionItem>
        <AccordionItem
          value="3"
          disabled={
            !module?.params?.geometry ||
            !module?.params?.masonryCharacteristics ||
            !module?.params?.stress
          }
        >
          <AccordionTrigger>
            <h3 className="text-lg font-medium">
              {t('confinement-reinforcement.title')}
            </h3>
          </AccordionTrigger>
          <AccordionContent>
            <FrcmColumnConfinementReinforcementCalculation
              session={session}
              projectId={projectId}
              module={module}
            />
          </AccordionContent>
        </AccordionItem>
      </AccordionComponent>
    </div>
  )
}
