import { ModuleReportGenerationSection } from '@atlas/components/common/atlas/module-detail/module-report-generation-section'
import { SlabFrcReinforcementForm } from '@atlas/components/common/atlas/module-detail/params-forms/slab/sections/slab-frc-reinforcement-form'
import { SlabGeometryForm } from '@atlas/components/common/atlas/module-detail/params-forms/slab/sections/slab-geometry-form'
import { SlabMaterialForm } from '@atlas/components/common/atlas/module-detail/params-forms/slab/sections/slab-material-form'
import { SlabReinforcementForm } from '@atlas/components/common/atlas/module-detail/params-forms/slab/sections/slab-reinforcement-form'
import { SlabCalculations } from '@atlas/components/common/atlas/module-detail/params-forms/slab/slab-calculations'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@atlas/components/ui/accordion'
import { Separator } from '@atlas/components/ui/separator'
import type {
  Module,
  ModuleWithParamsSlab,
} from '@atlas/lib/api/modules/schemas/modules'
import { slabCalculationCheck } from '@atlas/lib/api/modules/schemas/slab-params'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useCallback, useMemo, useState } from 'react'

type Props = {
  session: Session
  projectId: Project['id']
  moduleId: Module['id']
  module: ModuleWithParamsSlab
}

export const SlabParamsForm = ({
  session,
  projectId,
  moduleId,
  module,
}: Props) => {
  const { params } = module
  const [openItems, setOpenItems] = useState(['0'])
  const t = useTranslations('forms.project-params.slab')

  const handleItemSaved = useCallback((id: string) => {
    const nextId = String(Number(id) + 1)
    setOpenItems(old => {
      const temp = old.filter(v => v !== id)
      return old.includes(nextId) ? temp : [...temp, nextId]
    })
  }, [])

  const enableReport = useMemo(() => {
    const result = slabCalculationCheck.safeParse(module)
    return result.success
  }, [module])

  const hasParamsForCalculations = useMemo(() => {
    // Check if necessary parameters exist for calculations
    return Boolean(
      params?.geometry && params?.slabRebar && params?.materialProperties,
    )
  }, [params])

  return (
    <div>
      <Accordion type="multiple" value={openItems} onValueChange={setOpenItems}>
        <AccordionItem value="0">
          <AccordionTrigger>
            <h3 className="text-lg font-medium">{t('geometry.title')}</h3>
          </AccordionTrigger>
          <AccordionContent>
            <SlabGeometryForm
              session={session}
              projectId={projectId}
              moduleId={moduleId}
              defaultValues={params?.geometry as any}
              onSave={() => handleItemSaved('0')}
            />
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="1">
          <AccordionTrigger>
            <h3 className="text-lg font-medium">{t('slabRebar.title')}</h3>
          </AccordionTrigger>
          <AccordionContent>
            <SlabReinforcementForm
              session={session}
              projectId={projectId}
              moduleId={moduleId}
              defaultValues={params?.slabRebar ?? undefined}
              structuralScheme={params?.geometry?.structuralScheme as any}
              onSave={() => handleItemSaved('1')}
            />
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="2">
          <AccordionTrigger>
            <h3 className="text-lg font-medium">
              {t('materialProperties.title')}
            </h3>
          </AccordionTrigger>
          <AccordionContent>
            <SlabMaterialForm
              session={session}
              projectId={projectId}
              moduleId={moduleId}
              defaultValues={(params?.materialProperties ?? undefined) as any}
              onSave={() => handleItemSaved('2')}
            />
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="3">
          <AccordionTrigger>
            <h3 className="text-lg font-medium">
              {t('slabFrcReinforcement.title')}
            </h3>
          </AccordionTrigger>
          <AccordionContent>
            <SlabFrcReinforcementForm
              session={session}
              projectId={projectId}
              moduleId={moduleId}
              defaultValues={params?.slabFrcReinforcement ?? undefined}
              onSave={() => handleItemSaved('3')}
            />
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      {hasParamsForCalculations && (
        <>
          <Separator />
          <SlabCalculations
            module={module}
            session={session}
            projectId={projectId}
          />
        </>
      )}

      {enableReport && (
        <>
          <Separator />
          <ModuleReportGenerationSection
            session={session}
            projectId={projectId}
            moduleId={moduleId}
            enabled={enableReport}
          />
        </>
      )}
    </div>
  )
}
