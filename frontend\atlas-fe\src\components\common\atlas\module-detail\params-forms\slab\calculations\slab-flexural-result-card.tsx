import { Badge } from '@atlas/components/ui/badge'
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from '@atlas/components/ui/card'
import { Separator } from '@atlas/components/ui/separator'
import { cn } from '@atlas/lib/utils'
import { useLocale, useTranslations } from 'next-intl'

type SlabFlexuralResult = {
  sectionType: string
  // Unreinforced section
  unreinforcedBottomSteelStrain?: number
  unreinforcedTopSteelStrain?: number
  unreinforcedBottomSteelStress?: number
  unreinforcedTopSteelStress?: number
  unreinforcedNeutralAxisDistance?: number
  unreinforcedTranslationalEquilibrium?: number
  unreinforcedResistanceMoment?: number
  // Reinforced section
  maximumBendingMoment?: number
  reinforcedBottomSteelStrain?: number
  reinforcedTopSteelStrain?: number
  reinforcedSupplementarySteelStrain?: number
  reinforcedBottomSteelStress?: number
  reinforcedTopSteelStress?: number
  reinforcedSupplementarySteelStress?: number
  reinforcedNeutralAxisDistance?: number
  reinforcedTranslationalEquilibrium?: number
  reinforcedSectionResistanceMoment?: number
  checkResult: boolean
  checkValue?: number
}

type Props = {
  result: SlabFlexuralResult
}

export const SlabFlexuralResultCard = ({ result }: Props) => {
  const t = useTranslations('forms.calculations.slab.flexural-result')
  const locale = useLocale()

  const isVerified = result.checkResult

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>{t('title')}</span>
          <Badge
            className={cn(
              'text-base px-3 py-1',
              isVerified ? 'bg-green-600' : 'bg-red-600',
            )}
          >
            {isVerified ? t('verified') : t('not-verified')}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Section Type */}
        <div>
          <h3 className="font-semibold text-lg mb-2">
            {t('section-type')}: {result.sectionType}
          </h3>
        </div>

        <Separator />

        {/* Unreinforced Section */}
        <div>
          <h4 className="font-semibold mb-2">{t('unreinforced-section')}</h4>
          <div className="grid grid-cols-2 gap-2 text-sm">
            {result.unreinforcedBottomSteelStrain !== undefined && (
              <div>
                <span className="text-muted-foreground">
                  {t('bottom-steel-strain')}:
                </span>
                <span className="ml-2 font-medium">
                  {result.unreinforcedBottomSteelStrain.toFixed(6)}
                </span>
              </div>
            )}
            {result.unreinforcedTopSteelStrain !== undefined && (
              <div>
                <span className="text-muted-foreground">
                  {t('top-steel-strain')}:
                </span>
                <span className="ml-2 font-medium">
                  {result.unreinforcedTopSteelStrain.toFixed(6)}
                </span>
              </div>
            )}
            {result.unreinforcedNeutralAxisDistance !== undefined && (
              <div>
                <span className="text-muted-foreground">
                  {t('neutral-axis')}:
                </span>
                <span className="ml-2 font-medium">
                  {result.unreinforcedNeutralAxisDistance.toLocaleString(
                    locale,
                    { maximumFractionDigits: 2 },
                  )}{' '}
                  mm
                </span>
              </div>
            )}
            {result.unreinforcedResistanceMoment !== undefined && (
              <div>
                <span className="text-muted-foreground">
                  {t('resistance-moment')}:
                </span>
                <span className="ml-2 font-medium">
                  {result.unreinforcedResistanceMoment.toLocaleString(locale, {
                    maximumFractionDigits: 2,
                  })}{' '}
                  kNm
                </span>
              </div>
            )}
          </div>
        </div>

        <Separator />

        {/* Reinforced Section */}
        <div>
          <h4 className="font-semibold mb-2">{t('reinforced-section')}</h4>
          <div className="grid grid-cols-2 gap-2 text-sm">
            {result.maximumBendingMoment !== undefined && (
              <div>
                <span className="text-muted-foreground">
                  {t('applied-moment')}:
                </span>
                <span className="ml-2 font-medium">
                  {result.maximumBendingMoment.toLocaleString(locale, {
                    maximumFractionDigits: 2,
                  })}{' '}
                  kNm
                </span>
              </div>
            )}
            {result.reinforcedNeutralAxisDistance !== undefined && (
              <div>
                <span className="text-muted-foreground">
                  {t('neutral-axis')}:
                </span>
                <span className="ml-2 font-medium">
                  {result.reinforcedNeutralAxisDistance.toLocaleString(locale, {
                    maximumFractionDigits: 2,
                  })}{' '}
                  mm
                </span>
              </div>
            )}
            {result.reinforcedSectionResistanceMoment !== undefined && (
              <div className="col-span-2">
                <span className="text-muted-foreground font-semibold">
                  {t('resistance-moment')}:
                </span>
                <span className="ml-2 font-medium text-lg">
                  {result.reinforcedSectionResistanceMoment.toLocaleString(
                    locale,
                    { maximumFractionDigits: 2 },
                  )}{' '}
                  kNm
                </span>
              </div>
            )}
            {result.checkValue !== undefined && (
              <div className="col-span-2">
                <span className="text-muted-foreground font-semibold">
                  {t('safety-factor')}:
                </span>
                <span
                  className={cn(
                    'ml-2 font-medium text-lg',
                    result.checkValue >= 1 ? 'text-green-600' : 'text-red-600',
                  )}
                >
                  {result.checkValue.toLocaleString(locale, {
                    minimumFractionDigits: 3,
                    maximumFractionDigits: 3,
                  })}
                </span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
