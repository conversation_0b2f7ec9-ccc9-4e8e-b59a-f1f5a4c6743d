var R=require("../../../chunks/ssr/[turbopack]_runtime.js")("server/app/[locale]/dashboard/page.js")
R.c("server/chunks/ssr/node_modules_5f2d4120._.js")
R.c("server/chunks/ssr/[root-of-the-server]__e8a2741f._.js")
R.c("server/chunks/ssr/src_app_ca777385._.js")
R.c("server/chunks/ssr/node_modules_next_dist_client_components_9774470f._.js")
R.c("server/chunks/ssr/node_modules_next_dist_client_components_builtin_forbidden_45780354.js")
R.c("server/chunks/ssr/node_modules_next_dist_client_components_builtin_unauthorized_15817684.js")
R.c("server/chunks/ssr/node_modules_next_dist_client_components_builtin_global-error_ece394eb.js")
R.c("server/chunks/ssr/messages_22de1e77._.js")
R.c("server/chunks/ssr/node_modules_4507ba38._.js")
R.c("server/chunks/ssr/src_7c14424b._.js")
R.c("server/chunks/ssr/_4ba54b1b._.js")
R.c("server/chunks/ssr/node_modules_axios_lib_895191e1._.js")
R.c("server/chunks/ssr/node_modules_mime-db_f953de6b._.js")
R.c("server/chunks/ssr/node_modules_@auth_core_c5a86bde._.js")
R.c("server/chunks/ssr/node_modules_jose_dist_node_esm_ac2ce1ca._.js")
R.c("server/chunks/ssr/node_modules_next_9cde7c2b._.js")
R.c("server/chunks/ssr/node_modules_fa0bfd75._.js")
R.c("server/chunks/ssr/[root-of-the-server]__db402811._.js")
R.c("server/chunks/ssr/node_modules_0071c19f._.js")
R.c("server/chunks/ssr/[root-of-the-server]__bc7d3f2d._.js")
R.m("[project]/.next-internal/server/app/[locale]/dashboard/page/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/[locale]/dashboard/page { GLOBAL_ERROR_MODULE => \"[project]/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/src/app/[locale]/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/src/app/[locale]/not-found.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_7 => \"[project]/src/app/[locale]/dashboard/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_8 => \"[project]/src/app/[locale]/dashboard/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-page.js?page=/[locale]/dashboard/page { GLOBAL_ERROR_MODULE => \"[project]/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", METADATA_0 => \"[project]/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)\", MODULE_1 => \"[project]/node_modules/next/dist/client/components/builtin/not-found.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_2 => \"[project]/node_modules/next/dist/client/components/builtin/forbidden.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_3 => \"[project]/node_modules/next/dist/client/components/builtin/unauthorized.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_4 => \"[project]/node_modules/next/dist/client/components/builtin/global-error.js [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_5 => \"[project]/src/app/[locale]/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_6 => \"[project]/src/app/[locale]/not-found.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_7 => \"[project]/src/app/[locale]/dashboard/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)\", MODULE_8 => \"[project]/src/app/[locale]/dashboard/page.tsx [app-rsc] (ecmascript, Next.js Server Component)\" } [app-rsc] (ecmascript)").exports
