(globalThis.TURBOPACK || (globalThis.TURBOPACK = [])).push([typeof document === "object" ? document.currentScript : undefined,
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "CrmPostInterventionDesignStrengthCalculationResultForm",
    ()=>CrmPostInterventionDesignStrengthCalculationResultForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$crm$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/masonry/crm-form.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dropdown$2d$menu$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
const CrmPostInterventionDesignStrengthCalculationResultForm = (param)=>{
    let { designStrengthPostInterventionCalculationResult } = param;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.calculations.crm.postIntervention.designStrengthPostInterventionCalculationResult');
    var _designStrengthPostInterventionCalculationResult_panelThickness, _designStrengthPostInterventionCalculationResult_designCompressiveStrength, _designStrengthPostInterventionCalculationResult_designShearStrengthRegularMasonry, _designStrengthPostInterventionCalculationResult_designShearStrengthIrregularMasonry, _designStrengthPostInterventionCalculationResult_designNormalElasticityModulus, _designStrengthPostInterventionCalculationResult_designShearElasticityModulus, _designStrengthPostInterventionCalculationResult_ultimateCompressiveStrainLinearBehavior, _designStrengthPostInterventionCalculationResult_secondCoefficient, _designStrengthPostInterventionCalculationResult_panelSelfWeight, _designStrengthPostInterventionCalculationResult_inPlaneBendingStrengthCalculationResult, _designStrengthPostInterventionCalculationResult_outOfPlaneBendingStrengthCalculationResult, _designStrengthPostInterventionCalculationResult_inPlaneShearStrengthCalculationResult;
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$crm$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["designStrengthPostInterventionCalculationResultSchema"]),
        defaultValues: {
            panelThickness: (_designStrengthPostInterventionCalculationResult_panelThickness = designStrengthPostInterventionCalculationResult === null || designStrengthPostInterventionCalculationResult === void 0 ? void 0 : designStrengthPostInterventionCalculationResult.panelThickness) !== null && _designStrengthPostInterventionCalculationResult_panelThickness !== void 0 ? _designStrengthPostInterventionCalculationResult_panelThickness : 0,
            designCompressiveStrength: (_designStrengthPostInterventionCalculationResult_designCompressiveStrength = designStrengthPostInterventionCalculationResult === null || designStrengthPostInterventionCalculationResult === void 0 ? void 0 : designStrengthPostInterventionCalculationResult.designCompressiveStrength) !== null && _designStrengthPostInterventionCalculationResult_designCompressiveStrength !== void 0 ? _designStrengthPostInterventionCalculationResult_designCompressiveStrength : 0,
            designShearStrengthRegularMasonry: (_designStrengthPostInterventionCalculationResult_designShearStrengthRegularMasonry = designStrengthPostInterventionCalculationResult === null || designStrengthPostInterventionCalculationResult === void 0 ? void 0 : designStrengthPostInterventionCalculationResult.designShearStrengthRegularMasonry) !== null && _designStrengthPostInterventionCalculationResult_designShearStrengthRegularMasonry !== void 0 ? _designStrengthPostInterventionCalculationResult_designShearStrengthRegularMasonry : 0,
            designShearStrengthIrregularMasonry: (_designStrengthPostInterventionCalculationResult_designShearStrengthIrregularMasonry = designStrengthPostInterventionCalculationResult === null || designStrengthPostInterventionCalculationResult === void 0 ? void 0 : designStrengthPostInterventionCalculationResult.designShearStrengthIrregularMasonry) !== null && _designStrengthPostInterventionCalculationResult_designShearStrengthIrregularMasonry !== void 0 ? _designStrengthPostInterventionCalculationResult_designShearStrengthIrregularMasonry : 0,
            designNormalElasticityModulus: (_designStrengthPostInterventionCalculationResult_designNormalElasticityModulus = designStrengthPostInterventionCalculationResult === null || designStrengthPostInterventionCalculationResult === void 0 ? void 0 : designStrengthPostInterventionCalculationResult.designNormalElasticityModulus) !== null && _designStrengthPostInterventionCalculationResult_designNormalElasticityModulus !== void 0 ? _designStrengthPostInterventionCalculationResult_designNormalElasticityModulus : 0,
            designShearElasticityModulus: (_designStrengthPostInterventionCalculationResult_designShearElasticityModulus = designStrengthPostInterventionCalculationResult === null || designStrengthPostInterventionCalculationResult === void 0 ? void 0 : designStrengthPostInterventionCalculationResult.designShearElasticityModulus) !== null && _designStrengthPostInterventionCalculationResult_designShearElasticityModulus !== void 0 ? _designStrengthPostInterventionCalculationResult_designShearElasticityModulus : 0,
            ultimateCompressiveStrainLinearBehavior: (_designStrengthPostInterventionCalculationResult_ultimateCompressiveStrainLinearBehavior = designStrengthPostInterventionCalculationResult === null || designStrengthPostInterventionCalculationResult === void 0 ? void 0 : designStrengthPostInterventionCalculationResult.ultimateCompressiveStrainLinearBehavior) !== null && _designStrengthPostInterventionCalculationResult_ultimateCompressiveStrainLinearBehavior !== void 0 ? _designStrengthPostInterventionCalculationResult_ultimateCompressiveStrainLinearBehavior : 0,
            secondCoefficient: (_designStrengthPostInterventionCalculationResult_secondCoefficient = designStrengthPostInterventionCalculationResult === null || designStrengthPostInterventionCalculationResult === void 0 ? void 0 : designStrengthPostInterventionCalculationResult.secondCoefficient) !== null && _designStrengthPostInterventionCalculationResult_secondCoefficient !== void 0 ? _designStrengthPostInterventionCalculationResult_secondCoefficient : 0,
            panelSelfWeight: (_designStrengthPostInterventionCalculationResult_panelSelfWeight = designStrengthPostInterventionCalculationResult === null || designStrengthPostInterventionCalculationResult === void 0 ? void 0 : designStrengthPostInterventionCalculationResult.panelSelfWeight) !== null && _designStrengthPostInterventionCalculationResult_panelSelfWeight !== void 0 ? _designStrengthPostInterventionCalculationResult_panelSelfWeight : 0,
            inPlaneBendingStrengthCalculationResult: (_designStrengthPostInterventionCalculationResult_inPlaneBendingStrengthCalculationResult = designStrengthPostInterventionCalculationResult === null || designStrengthPostInterventionCalculationResult === void 0 ? void 0 : designStrengthPostInterventionCalculationResult.inPlaneBendingStrengthCalculationResult) !== null && _designStrengthPostInterventionCalculationResult_inPlaneBendingStrengthCalculationResult !== void 0 ? _designStrengthPostInterventionCalculationResult_inPlaneBendingStrengthCalculationResult : {
                compressedFlangeNeutralAxisDistance: 0,
                inOrOutplaneBendingMoment: 0
            },
            outOfPlaneBendingStrengthCalculationResult: (_designStrengthPostInterventionCalculationResult_outOfPlaneBendingStrengthCalculationResult = designStrengthPostInterventionCalculationResult === null || designStrengthPostInterventionCalculationResult === void 0 ? void 0 : designStrengthPostInterventionCalculationResult.outOfPlaneBendingStrengthCalculationResult) !== null && _designStrengthPostInterventionCalculationResult_outOfPlaneBendingStrengthCalculationResult !== void 0 ? _designStrengthPostInterventionCalculationResult_outOfPlaneBendingStrengthCalculationResult : {
                compressedFlangeNeutralAxisDistance: 0,
                inOrOutplaneBendingMoment: 0
            },
            inPlaneShearStrengthCalculationResult: (_designStrengthPostInterventionCalculationResult_inPlaneShearStrengthCalculationResult = designStrengthPostInterventionCalculationResult === null || designStrengthPostInterventionCalculationResult === void 0 ? void 0 : designStrengthPostInterventionCalculationResult.inPlaneShearStrengthCalculationResult) !== null && _designStrengthPostInterventionCalculationResult_inPlaneShearStrengthCalculationResult !== void 0 ? _designStrengthPostInterventionCalculationResult_inPlaneShearStrengthCalculationResult : {
                shearStrength: 0,
                verticalStress: 0,
                wallSlendernessCorrectionCoefficient: 0
            }
        }
    });
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                    children: t('title')
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                    lineNumber: 80,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                lineNumber: 79,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                className: "space-y-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('panelThickness.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 84,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: form.getValues('panelThickness')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 85,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-muted-foreground text-sm",
                                children: t('panelThickness.description')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 86,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                        lineNumber: 83,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('designCompressiveStrength.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 91,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: form.getValues('designCompressiveStrength')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 94,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-muted-foreground text-sm",
                                children: t('designCompressiveStrength.description')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 95,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                        lineNumber: 90,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('designShearStrengthRegularMasonry.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 100,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: form.getValues('designShearStrengthRegularMasonry')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 103,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-muted-foreground text-sm",
                                children: t('designShearStrengthRegularMasonry.description')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 104,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                        lineNumber: 99,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('designShearStrengthIrregularMasonry.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 109,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: form.getValues('designShearStrengthIrregularMasonry')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 112,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-muted-foreground text-sm",
                                children: t('designShearStrengthIrregularMasonry.description')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 113,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                        lineNumber: 108,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('designNormalElasticityModulus.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 118,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: form.getValues('designNormalElasticityModulus')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 121,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-muted-foreground text-sm",
                                children: t('designNormalElasticityModulus.description')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 122,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                        lineNumber: 117,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('designShearElasticityModulus.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 127,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: form.getValues('designShearElasticityModulus')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 130,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-muted-foreground text-sm",
                                children: t('designShearElasticityModulus.description')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 131,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                        lineNumber: 126,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('ultimateCompressiveStrainLinearBehavior.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 136,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: form.getValues('ultimateCompressiveStrainLinearBehavior')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 139,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-muted-foreground text-sm",
                                children: t('ultimateCompressiveStrainLinearBehavior.description')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 142,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                        lineNumber: 135,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('secondCoefficient.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 147,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: form.getValues('secondCoefficient')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 148,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-muted-foreground text-sm",
                                children: t('secondCoefficient.description')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 149,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                        lineNumber: 146,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('panelSelfWeight.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 154,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: form.getValues('panelSelfWeight')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 155,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-muted-foreground text-sm",
                                children: t('panelSelfWeight.description')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 156,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                        lineNumber: 153,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dropdown$2d$menu$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                        lineNumber: 161,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "text-xl font-bold",
                        children: t('inPlaneBendingStrengthCalculationResult.title')
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                        lineNumber: 162,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('inPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 166,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: form.getValues('inPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 172,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-muted-foreground text-sm",
                                children: t('inPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance.description')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 177,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                        lineNumber: 165,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('inPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 184,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: form.getValues('inPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 190,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-muted-foreground text-sm",
                                children: t('inPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment.description')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 195,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                        lineNumber: 183,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dropdown$2d$menu$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                        lineNumber: 202,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "text-xl font-bold",
                        children: t('outOfPlaneBendingStrengthCalculationResult.title')
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                        lineNumber: 203,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('outOfPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 207,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: form.getValues('outOfPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 213,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-muted-foreground text-sm",
                                children: t('outOfPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance.description')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 218,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                        lineNumber: 206,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('outOfPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 225,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: form.getValues('outOfPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 231,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-muted-foreground text-sm",
                                children: t('outOfPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment.description')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 236,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                        lineNumber: 224,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dropdown$2d$menu$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                        lineNumber: 243,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "text-xl font-bold",
                        children: t('inPlaneShearStrengthCalculationResult.title')
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                        lineNumber: 244,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('inPlaneShearStrengthCalculationResult.verticalStress.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 248,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: form.getValues('inPlaneShearStrengthCalculationResult.verticalStress')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 251,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-muted-foreground text-sm",
                                children: t('inPlaneShearStrengthCalculationResult.verticalStress.description')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 256,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                        lineNumber: 247,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('inPlaneShearStrengthCalculationResult.shearStrength.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 263,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: form.getValues('inPlaneShearStrengthCalculationResult.shearStrength')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 266,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-muted-foreground text-sm",
                                children: t('inPlaneShearStrengthCalculationResult.shearStrength.description')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                                lineNumber: 271,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                        lineNumber: 262,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
                lineNumber: 82,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx",
        lineNumber: 78,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(CrmPostInterventionDesignStrengthCalculationResultForm, "iRPoJh/A/HakTzyf1B06E89visA=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"]
    ];
});
_c = CrmPostInterventionDesignStrengthCalculationResultForm;
var _c;
__turbopack_context__.k.register(_c, "CrmPostInterventionDesignStrengthCalculationResultForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "CrmPostInterventionDesignStrengthCalculationForm",
    ()=>CrmPostInterventionDesignStrengthCalculationForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/select-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$module$2d$calculation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-module-calculation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$products$2f$use$2d$products$2d$by$2d$category$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/query/products/use-products-by-category.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$crm$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/masonry/crm-form.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dropdown$2d$menu$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$crm$2f$sections$2f$post$2d$intervention$2f$crm$2d$post$2d$intervention$2d$design$2d$strength$2d$calculation$2d$result$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const _MESH_OPTIONS_FILTER = [
    'Kimitech WALLMESH HR (Certificata CVT)',
    'Kimitech WALLMESH HR-HD (Marcata CE)',
    'Kimitech WALLMESH MR (Certificata CVT)',
    'Kimitech WALLMESH MR-HD (Marcata CE)'
];
const _CONNECTOR_OPTIONS_FILTER = [
    'Kimisteel INOX X-BAR da 8 mm (Marcata CE)',
    'Kimisteel INOX X-BAR da 12 mm (Marcata CE)',
    'Kimisteel INOX X-BAR da 10 mm (Marcata CE)',
    'Kimisteel PLUG VR (Certificata CVT)',
    'Kimisteel PLUG VR HD (Marcata CE)'
];
const _MATRIX_OPTIONS_FILTER = [
    'Basic MALTA M15',
    'Basic MALTA R3',
    'Betonfix FB',
    'Tectoria M15'
];
const CrmPostInterventionDesignStrengthCalculationForm = (param)=>{
    let { session, projectId, module } = param;
    var _module_postIntervention, _module_postIntervention_designStrengthPostInterventionVerifyInput_meshInput, _module_postIntervention_designStrengthPostInterventionVerifyInput, _module_postIntervention1, _module_postIntervention_designStrengthPostInterventionVerifyInput_connectorInput, _module_postIntervention_designStrengthPostInterventionVerifyInput1, _module_postIntervention2, _module_postIntervention_designStrengthPostInterventionVerifyInput_matrixInput, _module_postIntervention_designStrengthPostInterventionVerifyInput2, _module_postIntervention3, _module_postIntervention_designStrengthPostInterventionVerifyInput3, _module_postIntervention4, _form_formState_errors_input_meshInput_meshProduct_id, _form_formState_errors_input_meshInput_meshProduct, _form_formState_errors_input_meshInput, _form_formState_errors_input, _form_formState_errors_input_connectorInput_connectorProduct_id, _form_formState_errors_input_connectorInput_connectorProduct, _form_formState_errors_input_connectorInput, _form_formState_errors_input1, _form_formState_errors_input_matrixInput_matrixProduct_id, _form_formState_errors_input_matrixInput_matrixProduct, _form_formState_errors_input_matrixInput, _form_formState_errors_input2;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.calculations.crm.postIntervention.designStrengthPostInterventionCalculationResult');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.calculations.anti-overturning.composite-reinforcement-system');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const designStrengthPostInterventionCalculationResult = (_module_postIntervention = module.postIntervention) === null || _module_postIntervention === void 0 ? void 0 : _module_postIntervention.designStrengthPostInterventionCalculationResult;
    const meshProduct = module === null || module === void 0 ? void 0 : (_module_postIntervention1 = module.postIntervention) === null || _module_postIntervention1 === void 0 ? void 0 : (_module_postIntervention_designStrengthPostInterventionVerifyInput = _module_postIntervention1.designStrengthPostInterventionVerifyInput) === null || _module_postIntervention_designStrengthPostInterventionVerifyInput === void 0 ? void 0 : (_module_postIntervention_designStrengthPostInterventionVerifyInput_meshInput = _module_postIntervention_designStrengthPostInterventionVerifyInput.meshInput) === null || _module_postIntervention_designStrengthPostInterventionVerifyInput_meshInput === void 0 ? void 0 : _module_postIntervention_designStrengthPostInterventionVerifyInput_meshInput.meshProduct;
    const connectorProduct = module === null || module === void 0 ? void 0 : (_module_postIntervention2 = module.postIntervention) === null || _module_postIntervention2 === void 0 ? void 0 : (_module_postIntervention_designStrengthPostInterventionVerifyInput1 = _module_postIntervention2.designStrengthPostInterventionVerifyInput) === null || _module_postIntervention_designStrengthPostInterventionVerifyInput1 === void 0 ? void 0 : (_module_postIntervention_designStrengthPostInterventionVerifyInput_connectorInput = _module_postIntervention_designStrengthPostInterventionVerifyInput1.connectorInput) === null || _module_postIntervention_designStrengthPostInterventionVerifyInput_connectorInput === void 0 ? void 0 : _module_postIntervention_designStrengthPostInterventionVerifyInput_connectorInput.connectorProduct;
    const matrixProduct = module === null || module === void 0 ? void 0 : (_module_postIntervention3 = module.postIntervention) === null || _module_postIntervention3 === void 0 ? void 0 : (_module_postIntervention_designStrengthPostInterventionVerifyInput2 = _module_postIntervention3.designStrengthPostInterventionVerifyInput) === null || _module_postIntervention_designStrengthPostInterventionVerifyInput2 === void 0 ? void 0 : (_module_postIntervention_designStrengthPostInterventionVerifyInput_matrixInput = _module_postIntervention_designStrengthPostInterventionVerifyInput2.matrixInput) === null || _module_postIntervention_designStrengthPostInterventionVerifyInput_matrixInput === void 0 ? void 0 : _module_postIntervention_designStrengthPostInterventionVerifyInput_matrixInput.matrixProduct;
    var _meshProduct_id, _connectorProduct_id, _matrixProduct_id, _matrixProduct_name, _module_postIntervention_designStrengthPostInterventionVerifyInput_reinforcementTotalThickness;
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$crm$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["designStrengthPostInterventionInputSchema"]),
        defaultValues: {
            calculationType: 'DESIGN_STRENGTH_VERIFY',
            input: {
                meshInput: {
                    meshProduct: {
                        id: (_meshProduct_id = meshProduct === null || meshProduct === void 0 ? void 0 : meshProduct.id) !== null && _meshProduct_id !== void 0 ? _meshProduct_id : '',
                        name: meshProduct === null || meshProduct === void 0 ? void 0 : meshProduct.name,
                        sourceType: 'DATABASE'
                    }
                },
                connectorInput: {
                    connectorProduct: {
                        id: (_connectorProduct_id = connectorProduct === null || connectorProduct === void 0 ? void 0 : connectorProduct.id) !== null && _connectorProduct_id !== void 0 ? _connectorProduct_id : '',
                        name: connectorProduct === null || connectorProduct === void 0 ? void 0 : connectorProduct.name,
                        sourceType: 'DATABASE'
                    }
                },
                matrixInput: {
                    matrixProduct: {
                        id: (_matrixProduct_id = matrixProduct === null || matrixProduct === void 0 ? void 0 : matrixProduct.id) !== null && _matrixProduct_id !== void 0 ? _matrixProduct_id : '',
                        name: (_matrixProduct_name = matrixProduct === null || matrixProduct === void 0 ? void 0 : matrixProduct.name) !== null && _matrixProduct_name !== void 0 ? _matrixProduct_name : '',
                        sourceType: 'DATABASE'
                    }
                },
                reinforcementTotalThickness: (_module_postIntervention_designStrengthPostInterventionVerifyInput_reinforcementTotalThickness = module === null || module === void 0 ? void 0 : (_module_postIntervention4 = module.postIntervention) === null || _module_postIntervention4 === void 0 ? void 0 : (_module_postIntervention_designStrengthPostInterventionVerifyInput3 = _module_postIntervention4.designStrengthPostInterventionVerifyInput) === null || _module_postIntervention_designStrengthPostInterventionVerifyInput3 === void 0 ? void 0 : _module_postIntervention_designStrengthPostInterventionVerifyInput3.reinforcementTotalThickness) !== null && _module_postIntervention_designStrengthPostInterventionVerifyInput_reinforcementTotalThickness !== void 0 ? _module_postIntervention_designStrengthPostInterventionVerifyInput_reinforcementTotalThickness : 0
            }
        }
    });
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$module$2d$calculation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useModuleCalculation"])(session.accessToken, {
        onSuccess: {
            "CrmPostInterventionDesignStrengthCalculationForm.useModuleCalculation": ()=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(tAction('calculate.success'));
            }
        }["CrmPostInterventionDesignStrengthCalculationForm.useModuleCalculation"],
        onError: {
            "CrmPostInterventionDesignStrengthCalculationForm.useModuleCalculation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(tAction('calculate.failure', {
                    error: error.message
                }));
            }
        }["CrmPostInterventionDesignStrengthCalculationForm.useModuleCalculation"]
    });
    const handleFormSubmit = (body)=>{
        mutate({
            projectId,
            moduleId: module.id,
            body
        });
    };
    // LATER CHANGE TO CRM TYPE
    const { data: productsAll, isError: errorGettingProducts, isLoading: isLoadingProducts } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$products$2f$use$2d$products$2d$by$2d$category$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useProductsByCategory"])(session, 'CRM', 0, 100);
    var _productsAll_content;
    const productsMeshOptions = [
        ...((_productsAll_content = productsAll === null || productsAll === void 0 ? void 0 : productsAll.content) !== null && _productsAll_content !== void 0 ? _productsAll_content : []).filter((m)=>m.productType === 'MESH').map((m)=>{
            var _m_name;
            return {
                value: m.id,
                label: (_m_name = m.name) !== null && _m_name !== void 0 ? _m_name : t('product.unnamed')
            };
        })
    ];
    var _productsAll_content_filter_map;
    const productsConnectorOptions = [
        ...(_productsAll_content_filter_map = productsAll === null || productsAll === void 0 ? void 0 : productsAll.content.filter((m)=>m.productType === 'CONNECTOR').map((m)=>{
            var _m_name;
            return {
                value: m.id,
                label: (_m_name = m.name) !== null && _m_name !== void 0 ? _m_name : t('product.unnamed')
            };
        })) !== null && _productsAll_content_filter_map !== void 0 ? _productsAll_content_filter_map : []
    ];
    var _productsAll_content_filter_map1;
    const productsMatrixOptions = [
        ...(_productsAll_content_filter_map1 = productsAll === null || productsAll === void 0 ? void 0 : productsAll.content.filter((m)=>m.productType === 'MATRIX').map((m)=>{
            var _m_name;
            return {
                value: m.id,
                label: (_m_name = m.name) !== null && _m_name !== void 0 ? _m_name : t('product.unnamed')
            };
        })) !== null && _productsAll_content_filter_map1 !== void 0 ? _productsAll_content_filter_map1 : []
    ];
    const [productMeshId] = form.watch([
        'input.meshInput.meshProduct.id'
    ]);
    const [productConnectorId] = form.watch([
        'input.connectorInput.connectorProduct.id'
    ]);
    const [productMatrixId] = form.watch([
        'input.matrixInput.matrixProduct.id'
    ]);
    const selectedProductMesh = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "CrmPostInterventionDesignStrengthCalculationForm.useMemo[selectedProductMesh]": ()=>productsAll === null || productsAll === void 0 ? void 0 : productsAll.content.filter({
                "CrmPostInterventionDesignStrengthCalculationForm.useMemo[selectedProductMesh]": (m)=>m.productType === 'MESH'
            }["CrmPostInterventionDesignStrengthCalculationForm.useMemo[selectedProductMesh]"]).find({
                "CrmPostInterventionDesignStrengthCalculationForm.useMemo[selectedProductMesh]": (p)=>p.id === productMeshId
            }["CrmPostInterventionDesignStrengthCalculationForm.useMemo[selectedProductMesh]"])
    }["CrmPostInterventionDesignStrengthCalculationForm.useMemo[selectedProductMesh]"], [
        productMeshId,
        productsAll
    ]);
    const selectedProductConnector = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "CrmPostInterventionDesignStrengthCalculationForm.useMemo[selectedProductConnector]": ()=>productsAll === null || productsAll === void 0 ? void 0 : productsAll.content.filter({
                "CrmPostInterventionDesignStrengthCalculationForm.useMemo[selectedProductConnector]": (m)=>m.productType === 'CONNECTOR'
            }["CrmPostInterventionDesignStrengthCalculationForm.useMemo[selectedProductConnector]"]).find({
                "CrmPostInterventionDesignStrengthCalculationForm.useMemo[selectedProductConnector]": (p)=>p.id === productConnectorId
            }["CrmPostInterventionDesignStrengthCalculationForm.useMemo[selectedProductConnector]"])
    }["CrmPostInterventionDesignStrengthCalculationForm.useMemo[selectedProductConnector]"], [
        productConnectorId,
        productsAll
    ]);
    const selectedProductMatrix = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "CrmPostInterventionDesignStrengthCalculationForm.useMemo[selectedProductMatrix]": ()=>productsAll === null || productsAll === void 0 ? void 0 : productsAll.content.filter({
                "CrmPostInterventionDesignStrengthCalculationForm.useMemo[selectedProductMatrix]": (m)=>m.productType === 'MATRIX'
            }["CrmPostInterventionDesignStrengthCalculationForm.useMemo[selectedProductMatrix]"]).find({
                "CrmPostInterventionDesignStrengthCalculationForm.useMemo[selectedProductMatrix]": (p)=>p.id === productMatrixId
            }["CrmPostInterventionDesignStrengthCalculationForm.useMemo[selectedProductMatrix]"])
    }["CrmPostInterventionDesignStrengthCalculationForm.useMemo[selectedProductMatrix]"], [
        productMatrixId,
        productsAll
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CrmPostInterventionDesignStrengthCalculationForm.useEffect": ()=>{
            if (selectedProductMesh) {
                form.setValue('input.meshInput.meshProduct', {
                    id: selectedProductMesh.id,
                    name: selectedProductMesh.name,
                    sourceType: 'DATABASE'
                });
            }
            if (selectedProductConnector) {
                form.setValue('input.connectorInput.connectorProduct', {
                    id: selectedProductConnector.id,
                    name: selectedProductConnector.name,
                    sourceType: 'DATABASE'
                });
            }
            if (selectedProductMatrix) {
                form.setValue('input.matrixInput.matrixProduct', {
                    id: selectedProductMatrix.id,
                    name: selectedProductMatrix.name,
                    sourceType: 'DATABASE'
                });
            }
        }
    }["CrmPostInterventionDesignStrengthCalculationForm.useEffect"], [
        form,
        selectedProductMesh,
        selectedProductConnector,
        selectedProductMatrix
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col justify-center gap-4",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Form"], {
                ...form,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                    className: "space-y-4 rounded-md border p-4",
                    onSubmit: form.handleSubmit(handleFormSubmit),
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            src: "/assets/masonry-anti-overturning/antibaltamento-verify.jpg",
                            alt: "composite reinforcement system verify",
                            height: 250,
                            width: 500,
                            className: "mx-auto rounded-md object-contain",
                            priority: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-form.tsx",
                            lineNumber: 232,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-xl font-bold",
                            children: t('mesh-sub-heading')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-form.tsx",
                            lineNumber: 241,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormInput"], {
                            control: form.control,
                            name: "input.meshInput.meshProduct.id",
                            options: productsMeshOptions,
                            t: t,
                            loading: isLoadingProducts,
                            requestError: errorGettingProducts,
                            // get the error message from the form state if any
                            errorMessage: (_form_formState_errors_input = form.formState.errors.input) === null || _form_formState_errors_input === void 0 ? void 0 : (_form_formState_errors_input_meshInput = _form_formState_errors_input.meshInput) === null || _form_formState_errors_input_meshInput === void 0 ? void 0 : (_form_formState_errors_input_meshInput_meshProduct = _form_formState_errors_input_meshInput.meshProduct) === null || _form_formState_errors_input_meshInput_meshProduct === void 0 ? void 0 : (_form_formState_errors_input_meshInput_meshProduct_id = _form_formState_errors_input_meshInput_meshProduct.id) === null || _form_formState_errors_input_meshInput_meshProduct_id === void 0 ? void 0 : _form_formState_errors_input_meshInput_meshProduct_id.message
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-form.tsx",
                            lineNumber: 242,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dropdown$2d$menu$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-form.tsx",
                            lineNumber: 254,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-xl font-bold",
                            children: t('connector-sub-heading')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-form.tsx",
                            lineNumber: 256,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormInput"], {
                            control: form.control,
                            name: "input.connectorInput.connectorProduct.id",
                            options: productsConnectorOptions,
                            t: t,
                            loading: isLoadingProducts,
                            requestError: errorGettingProducts,
                            errorMessage: (_form_formState_errors_input1 = form.formState.errors.input) === null || _form_formState_errors_input1 === void 0 ? void 0 : (_form_formState_errors_input_connectorInput = _form_formState_errors_input1.connectorInput) === null || _form_formState_errors_input_connectorInput === void 0 ? void 0 : (_form_formState_errors_input_connectorInput_connectorProduct = _form_formState_errors_input_connectorInput.connectorProduct) === null || _form_formState_errors_input_connectorInput_connectorProduct === void 0 ? void 0 : (_form_formState_errors_input_connectorInput_connectorProduct_id = _form_formState_errors_input_connectorInput_connectorProduct.id) === null || _form_formState_errors_input_connectorInput_connectorProduct_id === void 0 ? void 0 : _form_formState_errors_input_connectorInput_connectorProduct_id.message
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-form.tsx",
                            lineNumber: 257,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dropdown$2d$menu$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-form.tsx",
                            lineNumber: 269,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-xl font-bold",
                            children: t('matrix-sub-heading')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-form.tsx",
                            lineNumber: 271,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormInput"], {
                            control: form.control,
                            name: "input.matrixInput.matrixProduct.id",
                            options: productsMatrixOptions,
                            t: t,
                            loading: isLoadingProducts,
                            requestError: errorGettingProducts,
                            errorMessage: (_form_formState_errors_input2 = form.formState.errors.input) === null || _form_formState_errors_input2 === void 0 ? void 0 : (_form_formState_errors_input_matrixInput = _form_formState_errors_input2.matrixInput) === null || _form_formState_errors_input_matrixInput === void 0 ? void 0 : (_form_formState_errors_input_matrixInput_matrixProduct = _form_formState_errors_input_matrixInput.matrixProduct) === null || _form_formState_errors_input_matrixInput_matrixProduct === void 0 ? void 0 : (_form_formState_errors_input_matrixInput_matrixProduct_id = _form_formState_errors_input_matrixInput_matrixProduct.id) === null || _form_formState_errors_input_matrixInput_matrixProduct_id === void 0 ? void 0 : _form_formState_errors_input_matrixInput_matrixProduct_id.message
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-form.tsx",
                            lineNumber: 272,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$dropdown$2d$menu$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-form.tsx",
                            lineNumber: 284,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "input.reinforcementTotalThickness",
                            t: t
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-form.tsx",
                            lineNumber: 285,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                            type: "submit",
                            className: "w-full sm:w-auto",
                            disabled: isPending,
                            children: [
                                isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                                    className: "animate-spin"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-form.tsx",
                                    lineNumber: 295,
                                    columnNumber: 27
                                }, ("TURBOPACK compile-time value", void 0)),
                                tCommon('calculate')
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-form.tsx",
                            lineNumber: 290,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-form.tsx",
                    lineNumber: 228,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-form.tsx",
                lineNumber: 227,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            designStrengthPostInterventionCalculationResult && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$crm$2f$sections$2f$post$2d$intervention$2f$crm$2d$post$2d$intervention$2d$design$2d$strength$2d$calculation$2d$result$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CrmPostInterventionDesignStrengthCalculationResultForm"], {
                designStrengthPostInterventionCalculationResult: designStrengthPostInterventionCalculationResult
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-form.tsx",
                lineNumber: 301,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-form.tsx",
        lineNumber: 226,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(CrmPostInterventionDesignStrengthCalculationForm, "nBU0VeGdc3azT0JGeoSaFQ7xf18=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$module$2d$calculation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useModuleCalculation"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$products$2f$use$2d$products$2d$by$2d$category$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useProductsByCategory"]
    ];
});
_c = CrmPostInterventionDesignStrengthCalculationForm;
var _c;
__turbopack_context__.k.register(_c, "CrmPostInterventionDesignStrengthCalculationForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/reinforced-masonry-characteristics-params-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "ReinforcedMasonryCharacteristicsParamsForm",
    ()=>ReinforcedMasonryCharacteristicsParamsForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/select-form-fixed-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-save-module-params-mutation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$crm$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/masonry/crm-form.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const ReinforcedMasonryCharacteristicsParamsForm = (param)=>{
    let { session, projectId, module, onSave } = param;
    var _module_postIntervention, _module_preIntervention_existingMasonryCharacteristicsParams, _module_preIntervention, _module_preIntervention_existingMasonryCharacteristicsParams1, _module_preIntervention1, _module_preIntervention_existingMasonryCharacteristicsParams2, _module_preIntervention2, _module_preIntervention_existingMasonryCharacteristicsParams3, _module_preIntervention3, _module_preIntervention_existingMasonryCharacteristicsParams4, _module_preIntervention4, _module_preIntervention_existingMasonryCharacteristicsParams5, _module_preIntervention5, _module_preIntervention_existingMasonryCharacteristicsParams6, _module_preIntervention6, _module_preIntervention_existingMasonryCharacteristicsParams7, _module_preIntervention7;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.crm.crmPostIntervention.reinforcedMasonryCharacteristics');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.save-project-params.messages');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const moduleId = module.id;
    const reinforcedMasonryCharacteristicsParams = module === null || module === void 0 ? void 0 : (_module_postIntervention = module.postIntervention) === null || _module_postIntervention === void 0 ? void 0 : _module_postIntervention.reinforcedMasonryCharacteristicsParams;
    var _reinforcedMasonryCharacteristicsParams_reinforcementApplicationType, _reinforcedMasonryCharacteristicsParams_singleFaceApplicationReductionCoefficient, _reinforcedMasonryCharacteristicsParams_reinforcedPlasterCoefficient, _reinforcedMasonryCharacteristicsParams_binderMixturesInjections, _reinforcedMasonryCharacteristicsParams_correctionCoefficient, _reinforcedMasonryCharacteristicsParams_overallAmplificationCoefficient, _reinforcedMasonryCharacteristicsParams_amplifiedAverageCompressiveStrength, _reinforcedMasonryCharacteristicsParams_amplifiedAverageShearStrengthRegularMasonry, _reinforcedMasonryCharacteristicsParams_amplifiedAverageShearStrengthIrregularMasonry, _reinforcedMasonryCharacteristicsParams_amplifiedAverageNormalElasticityModulus, _reinforcedMasonryCharacteristicsParams_amplifiedAverageShearElasticityModulus;
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$crm$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reinforcedMasonryCharacteristicsParamsSchema"]),
        defaultValues: {
            reinforcementApplicationType: (_reinforcedMasonryCharacteristicsParams_reinforcementApplicationType = reinforcedMasonryCharacteristicsParams === null || reinforcedMasonryCharacteristicsParams === void 0 ? void 0 : reinforcedMasonryCharacteristicsParams.reinforcementApplicationType) !== null && _reinforcedMasonryCharacteristicsParams_reinforcementApplicationType !== void 0 ? _reinforcedMasonryCharacteristicsParams_reinforcementApplicationType : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REINFORCEMENT_APPLICATION_TYPE"].APPLICAZIONE_SU_ENTRAMBI_I_LATI,
            singleFaceApplicationReductionCoefficient: (_reinforcedMasonryCharacteristicsParams_singleFaceApplicationReductionCoefficient = reinforcedMasonryCharacteristicsParams === null || reinforcedMasonryCharacteristicsParams === void 0 ? void 0 : reinforcedMasonryCharacteristicsParams.singleFaceApplicationReductionCoefficient) !== null && _reinforcedMasonryCharacteristicsParams_singleFaceApplicationReductionCoefficient !== void 0 ? _reinforcedMasonryCharacteristicsParams_singleFaceApplicationReductionCoefficient : 30,
            reinforcedPlasterCoefficient: (_reinforcedMasonryCharacteristicsParams_reinforcedPlasterCoefficient = reinforcedMasonryCharacteristicsParams === null || reinforcedMasonryCharacteristicsParams === void 0 ? void 0 : reinforcedMasonryCharacteristicsParams.reinforcedPlasterCoefficient) !== null && _reinforcedMasonryCharacteristicsParams_reinforcedPlasterCoefficient !== void 0 ? _reinforcedMasonryCharacteristicsParams_reinforcedPlasterCoefficient : 0,
            binderMixturesInjections: (_reinforcedMasonryCharacteristicsParams_binderMixturesInjections = reinforcedMasonryCharacteristicsParams === null || reinforcedMasonryCharacteristicsParams === void 0 ? void 0 : reinforcedMasonryCharacteristicsParams.binderMixturesInjections) !== null && _reinforcedMasonryCharacteristicsParams_binderMixturesInjections !== void 0 ? _reinforcedMasonryCharacteristicsParams_binderMixturesInjections : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BINDER_MIXTURE_INJECTIONS"].YES,
            correctionCoefficient: (_reinforcedMasonryCharacteristicsParams_correctionCoefficient = reinforcedMasonryCharacteristicsParams === null || reinforcedMasonryCharacteristicsParams === void 0 ? void 0 : reinforcedMasonryCharacteristicsParams.correctionCoefficient) !== null && _reinforcedMasonryCharacteristicsParams_correctionCoefficient !== void 0 ? _reinforcedMasonryCharacteristicsParams_correctionCoefficient : 0,
            overallAmplificationCoefficient: (_reinforcedMasonryCharacteristicsParams_overallAmplificationCoefficient = reinforcedMasonryCharacteristicsParams === null || reinforcedMasonryCharacteristicsParams === void 0 ? void 0 : reinforcedMasonryCharacteristicsParams.overallAmplificationCoefficient) !== null && _reinforcedMasonryCharacteristicsParams_overallAmplificationCoefficient !== void 0 ? _reinforcedMasonryCharacteristicsParams_overallAmplificationCoefficient : 0,
            amplifiedAverageCompressiveStrength: (_reinforcedMasonryCharacteristicsParams_amplifiedAverageCompressiveStrength = reinforcedMasonryCharacteristicsParams === null || reinforcedMasonryCharacteristicsParams === void 0 ? void 0 : reinforcedMasonryCharacteristicsParams.amplifiedAverageCompressiveStrength) !== null && _reinforcedMasonryCharacteristicsParams_amplifiedAverageCompressiveStrength !== void 0 ? _reinforcedMasonryCharacteristicsParams_amplifiedAverageCompressiveStrength : 0,
            amplifiedAverageShearStrengthRegularMasonry: (_reinforcedMasonryCharacteristicsParams_amplifiedAverageShearStrengthRegularMasonry = reinforcedMasonryCharacteristicsParams === null || reinforcedMasonryCharacteristicsParams === void 0 ? void 0 : reinforcedMasonryCharacteristicsParams.amplifiedAverageShearStrengthRegularMasonry) !== null && _reinforcedMasonryCharacteristicsParams_amplifiedAverageShearStrengthRegularMasonry !== void 0 ? _reinforcedMasonryCharacteristicsParams_amplifiedAverageShearStrengthRegularMasonry : 0,
            amplifiedAverageShearStrengthIrregularMasonry: (_reinforcedMasonryCharacteristicsParams_amplifiedAverageShearStrengthIrregularMasonry = reinforcedMasonryCharacteristicsParams === null || reinforcedMasonryCharacteristicsParams === void 0 ? void 0 : reinforcedMasonryCharacteristicsParams.amplifiedAverageShearStrengthIrregularMasonry) !== null && _reinforcedMasonryCharacteristicsParams_amplifiedAverageShearStrengthIrregularMasonry !== void 0 ? _reinforcedMasonryCharacteristicsParams_amplifiedAverageShearStrengthIrregularMasonry : 0,
            amplifiedAverageNormalElasticityModulus: (_reinforcedMasonryCharacteristicsParams_amplifiedAverageNormalElasticityModulus = reinforcedMasonryCharacteristicsParams === null || reinforcedMasonryCharacteristicsParams === void 0 ? void 0 : reinforcedMasonryCharacteristicsParams.amplifiedAverageNormalElasticityModulus) !== null && _reinforcedMasonryCharacteristicsParams_amplifiedAverageNormalElasticityModulus !== void 0 ? _reinforcedMasonryCharacteristicsParams_amplifiedAverageNormalElasticityModulus : 0,
            amplifiedAverageShearElasticityModulus: (_reinforcedMasonryCharacteristicsParams_amplifiedAverageShearElasticityModulus = reinforcedMasonryCharacteristicsParams === null || reinforcedMasonryCharacteristicsParams === void 0 ? void 0 : reinforcedMasonryCharacteristicsParams.amplifiedAverageShearElasticityModulus) !== null && _reinforcedMasonryCharacteristicsParams_amplifiedAverageShearElasticityModulus !== void 0 ? _reinforcedMasonryCharacteristicsParams_amplifiedAverageShearElasticityModulus : 0
        }
    });
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"])(session.accessToken, {
        onSuccess: {
            "ReinforcedMasonryCharacteristicsParamsForm.useSaveModuleParamsMutation": async ()=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(tAction('edit.success'));
                await queryClient.invalidateQueries({
                    queryKey: [
                        'modules',
                        {
                            projectId,
                            moduleId
                        }
                    ]
                });
                onSave();
            }
        }["ReinforcedMasonryCharacteristicsParamsForm.useSaveModuleParamsMutation"],
        onError: {
            "ReinforcedMasonryCharacteristicsParamsForm.useSaveModuleParamsMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(tAction('edit.failure', {
                    error: error.message
                }));
            }
        }["ReinforcedMasonryCharacteristicsParamsForm.useSaveModuleParamsMutation"]
    });
    const reinforcementApplicationType = form.watch('reinforcementApplicationType');
    const binderMixtureInjections = form.watch('binderMixturesInjections');
    var _module_preIntervention_existingMasonryCharacteristicsParams_masonryType;
    const masonryType = (_module_preIntervention_existingMasonryCharacteristicsParams_masonryType = (_module_preIntervention = module.preIntervention) === null || _module_preIntervention === void 0 ? void 0 : (_module_preIntervention_existingMasonryCharacteristicsParams = _module_preIntervention.existingMasonryCharacteristicsParams) === null || _module_preIntervention_existingMasonryCharacteristicsParams === void 0 ? void 0 : _module_preIntervention_existingMasonryCharacteristicsParams.masonryType) !== null && _module_preIntervention_existingMasonryCharacteristicsParams_masonryType !== void 0 ? _module_preIntervention_existingMasonryCharacteristicsParams_masonryType : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CRM_MASONRY_TYPE"].MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI;
    var _module_preIntervention_existingMasonryCharacteristicsParams_enhancementCharacteristics;
    const preInterventionEnhancementCharacteristics = (_module_preIntervention_existingMasonryCharacteristicsParams_enhancementCharacteristics = (_module_preIntervention1 = module.preIntervention) === null || _module_preIntervention1 === void 0 ? void 0 : (_module_preIntervention_existingMasonryCharacteristicsParams1 = _module_preIntervention1.existingMasonryCharacteristicsParams) === null || _module_preIntervention_existingMasonryCharacteristicsParams1 === void 0 ? void 0 : _module_preIntervention_existingMasonryCharacteristicsParams1.enhancementCharacteristics) !== null && _module_preIntervention_existingMasonryCharacteristicsParams_enhancementCharacteristics !== void 0 ? _module_preIntervention_existingMasonryCharacteristicsParams_enhancementCharacteristics : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ENHANCEMENT_CHARACTERISTICS"].NON_PRESENTI;
    var _module_preIntervention_existingMasonryCharacteristicsParams_correctionCoefficient;
    const preInterventionCorrectionCoefficient = (_module_preIntervention_existingMasonryCharacteristicsParams_correctionCoefficient = (_module_preIntervention2 = module.preIntervention) === null || _module_preIntervention2 === void 0 ? void 0 : (_module_preIntervention_existingMasonryCharacteristicsParams2 = _module_preIntervention2.existingMasonryCharacteristicsParams) === null || _module_preIntervention_existingMasonryCharacteristicsParams2 === void 0 ? void 0 : _module_preIntervention_existingMasonryCharacteristicsParams2.correctionCoefficient) !== null && _module_preIntervention_existingMasonryCharacteristicsParams_correctionCoefficient !== void 0 ? _module_preIntervention_existingMasonryCharacteristicsParams_correctionCoefficient : 1;
    var _module_preIntervention_existingMasonryCharacteristicsParams_averageCompressiveStrength;
    const averageCompressiveStrengthPreIntervention = (_module_preIntervention_existingMasonryCharacteristicsParams_averageCompressiveStrength = (_module_preIntervention3 = module.preIntervention) === null || _module_preIntervention3 === void 0 ? void 0 : (_module_preIntervention_existingMasonryCharacteristicsParams3 = _module_preIntervention3.existingMasonryCharacteristicsParams) === null || _module_preIntervention_existingMasonryCharacteristicsParams3 === void 0 ? void 0 : _module_preIntervention_existingMasonryCharacteristicsParams3.averageCompressiveStrength) !== null && _module_preIntervention_existingMasonryCharacteristicsParams_averageCompressiveStrength !== void 0 ? _module_preIntervention_existingMasonryCharacteristicsParams_averageCompressiveStrength : 0;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ReinforcedMasonryCharacteristicsParamsForm.useEffect": ()=>{
            var _module_preIntervention_existingMasonryCharacteristicsParams, _module_preIntervention, _module_preIntervention_existingMasonryCharacteristicsParams1, _module_preIntervention1, _module_preIntervention_existingMasonryCharacteristicsParams2, _module_preIntervention2, _module_preIntervention_existingMasonryCharacteristicsParams3, _module_preIntervention3;
            const singleFaceApplicationReductionCoefficient = reinforcementApplicationType === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["REINFORCEMENT_APPLICATION_TYPE"].APPLICAZIONE_SU_ENTRAMBI_I_LATI ? 0 : 30;
            form.setValue('singleFaceApplicationReductionCoefficient', singleFaceApplicationReductionCoefficient);
            // get the coefficient based on the masonry type:
            // ((VLOOKUP('ANTE-INTERVENTO'!D13,Parametri!B38:J45,7,0)-1)/100*(100-D11))+1
            const reinforcedPlasterCoefficient = (__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["postInterventionFirstSideReinforcedPlasterCoefficientValues"][masonryType] - 1) / 100 * (100 - singleFaceApplicationReductionCoefficient) + 1;
            form.setValue('reinforcedPlasterCoefficient', reinforcedPlasterCoefficient);
            // correctionCoefficient =IF(C13="SI",VLOOKUP('ANTE-INTERVENTO'!D13,Parametri!B38:J45,6,0),1)
            const correctionCoefficient = binderMixtureInjections === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BINDER_MIXTURE_INJECTIONS"].YES ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["bindingCoefficientValues"][masonryType] : 1;
            form.setValue('correctionCoefficient', correctionCoefficient);
            // =IF('ANTE-INTERVENTO'!C21="non presenti",
            //    MIN(D13*D12,VLOOKUP('ANTE-INTERVENTO'!D13,Parametri!B38:J45,9,0)),
            //    IF('ANTE-INTERVENTO'!C21="Malta buona",
            //      IF(C13="NO",
            //        MIN('ANTE-INTERVENTO'!D21*D12,VLOOKUP('ANTE-INTERVENTO'!D13,Parametri!B38:J45,9,0)),
            //        MIN(MAX('ANTE-INTERVENTO'!D21,D13)*D12,cerva.cert('ANTE-INTERVENTO'!D13,Parametri!B38:J45,9,0)))))
            const overallAmplificationCoefficient = preInterventionEnhancementCharacteristics === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ENHANCEMENT_CHARACTERISTICS"].NON_PRESENTI ? Math.min(correctionCoefficient * reinforcedPlasterCoefficient, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["maxAmplficationCoefficientValues"][masonryType]) : preInterventionEnhancementCharacteristics === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ENHANCEMENT_CHARACTERISTICS"].MALTA_BUONA ? binderMixtureInjections === __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BINDER_MIXTURE_INJECTIONS"].NO ? Math.min(preInterventionCorrectionCoefficient * reinforcedPlasterCoefficient, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["maxAmplficationCoefficientValues"][masonryType]) : Math.min(Math.max(preInterventionCorrectionCoefficient, correctionCoefficient) * reinforcedPlasterCoefficient, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["maxAmplficationCoefficientValues"][masonryType]) : 1;
            form.setValue('overallAmplificationCoefficient', overallAmplificationCoefficient);
            // amplifiedAverageCompressiveStrength = overallAmplificationCoefficient * 'ANTE-INTERVENTO:amplifiedAverageCompressiveStrength
            const amplifiedAverageCompressiveStrength = overallAmplificationCoefficient * averageCompressiveStrengthPreIntervention;
            form.setValue('amplifiedAverageCompressiveStrength', amplifiedAverageCompressiveStrength);
            var _module_preIntervention_existingMasonryCharacteristicsParams_averageShearStrengthRegularMasonry;
            // amplifiedAverageShearStrengthRegularMasonry = overallAmplificationCoefficient * 'ANTE-INTERVENTO:amplifiedAverageShearStrengthRegularMasonry
            const amplifiedAverageShearStrengthRegularMasonry = overallAmplificationCoefficient * ((_module_preIntervention_existingMasonryCharacteristicsParams_averageShearStrengthRegularMasonry = (_module_preIntervention = module.preIntervention) === null || _module_preIntervention === void 0 ? void 0 : (_module_preIntervention_existingMasonryCharacteristicsParams = _module_preIntervention.existingMasonryCharacteristicsParams) === null || _module_preIntervention_existingMasonryCharacteristicsParams === void 0 ? void 0 : _module_preIntervention_existingMasonryCharacteristicsParams.averageShearStrengthRegularMasonry) !== null && _module_preIntervention_existingMasonryCharacteristicsParams_averageShearStrengthRegularMasonry !== void 0 ? _module_preIntervention_existingMasonryCharacteristicsParams_averageShearStrengthRegularMasonry : 0);
            form.setValue('amplifiedAverageShearStrengthRegularMasonry', amplifiedAverageShearStrengthRegularMasonry);
            var _module_preIntervention_existingMasonryCharacteristicsParams_averageShearStrengthIrregularMasonry;
            // amplifiedAverageShearStrengthIrregularMasonry = overallAmplificationCoefficient * 'ANTE-INTERVENTO:amplifiedAverageShearStrengthIrregularMasonry
            const amplifiedAverageShearStrengthIrregularMasonry = overallAmplificationCoefficient * ((_module_preIntervention_existingMasonryCharacteristicsParams_averageShearStrengthIrregularMasonry = (_module_preIntervention1 = module.preIntervention) === null || _module_preIntervention1 === void 0 ? void 0 : (_module_preIntervention_existingMasonryCharacteristicsParams1 = _module_preIntervention1.existingMasonryCharacteristicsParams) === null || _module_preIntervention_existingMasonryCharacteristicsParams1 === void 0 ? void 0 : _module_preIntervention_existingMasonryCharacteristicsParams1.averageShearStrengthIrregularMasonry) !== null && _module_preIntervention_existingMasonryCharacteristicsParams_averageShearStrengthIrregularMasonry !== void 0 ? _module_preIntervention_existingMasonryCharacteristicsParams_averageShearStrengthIrregularMasonry : 0);
            form.setValue('amplifiedAverageShearStrengthIrregularMasonry', amplifiedAverageShearStrengthIrregularMasonry);
            var _module_preIntervention_existingMasonryCharacteristicsParams_averageNormalElasticityModulus;
            // amplifiedAverageNormalElasticityModulus = overallAmplificationCoefficient * 'ANTE-INTERVENTO:amplifiedAverageNormalElasticityModulus
            const amplifiedAverageNormalElasticityModulus = overallAmplificationCoefficient * ((_module_preIntervention_existingMasonryCharacteristicsParams_averageNormalElasticityModulus = (_module_preIntervention2 = module.preIntervention) === null || _module_preIntervention2 === void 0 ? void 0 : (_module_preIntervention_existingMasonryCharacteristicsParams2 = _module_preIntervention2.existingMasonryCharacteristicsParams) === null || _module_preIntervention_existingMasonryCharacteristicsParams2 === void 0 ? void 0 : _module_preIntervention_existingMasonryCharacteristicsParams2.averageNormalElasticityModulus) !== null && _module_preIntervention_existingMasonryCharacteristicsParams_averageNormalElasticityModulus !== void 0 ? _module_preIntervention_existingMasonryCharacteristicsParams_averageNormalElasticityModulus : 0);
            form.setValue('amplifiedAverageNormalElasticityModulus', amplifiedAverageNormalElasticityModulus);
            var _module_preIntervention_existingMasonryCharacteristicsParams_averageShearElasticityModulus;
            // amplifiedAverageShearElasticityModulus = overallAmplificationCoefficient * 'ANTE-INTERVENTO:amplifiedAverageShearElasticityModulus
            const amplifiedAverageShearElasticityModulus = overallAmplificationCoefficient * ((_module_preIntervention_existingMasonryCharacteristicsParams_averageShearElasticityModulus = (_module_preIntervention3 = module.preIntervention) === null || _module_preIntervention3 === void 0 ? void 0 : (_module_preIntervention_existingMasonryCharacteristicsParams3 = _module_preIntervention3.existingMasonryCharacteristicsParams) === null || _module_preIntervention_existingMasonryCharacteristicsParams3 === void 0 ? void 0 : _module_preIntervention_existingMasonryCharacteristicsParams3.averageShearElasticityModulus) !== null && _module_preIntervention_existingMasonryCharacteristicsParams_averageShearElasticityModulus !== void 0 ? _module_preIntervention_existingMasonryCharacteristicsParams_averageShearElasticityModulus : 0);
            form.setValue('amplifiedAverageShearElasticityModulus', amplifiedAverageShearElasticityModulus);
        }
    }["ReinforcedMasonryCharacteristicsParamsForm.useEffect"], [
        form,
        reinforcementApplicationType,
        masonryType,
        binderMixtureInjections,
        preInterventionEnhancementCharacteristics,
        preInterventionCorrectionCoefficient,
        averageCompressiveStrengthPreIntervention,
        (_module_preIntervention4 = module.preIntervention) === null || _module_preIntervention4 === void 0 ? void 0 : (_module_preIntervention_existingMasonryCharacteristicsParams4 = _module_preIntervention4.existingMasonryCharacteristicsParams) === null || _module_preIntervention_existingMasonryCharacteristicsParams4 === void 0 ? void 0 : _module_preIntervention_existingMasonryCharacteristicsParams4.averageNormalElasticityModulus,
        (_module_preIntervention5 = module.preIntervention) === null || _module_preIntervention5 === void 0 ? void 0 : (_module_preIntervention_existingMasonryCharacteristicsParams5 = _module_preIntervention5.existingMasonryCharacteristicsParams) === null || _module_preIntervention_existingMasonryCharacteristicsParams5 === void 0 ? void 0 : _module_preIntervention_existingMasonryCharacteristicsParams5.averageShearElasticityModulus,
        (_module_preIntervention6 = module.preIntervention) === null || _module_preIntervention6 === void 0 ? void 0 : (_module_preIntervention_existingMasonryCharacteristicsParams6 = _module_preIntervention6.existingMasonryCharacteristicsParams) === null || _module_preIntervention_existingMasonryCharacteristicsParams6 === void 0 ? void 0 : _module_preIntervention_existingMasonryCharacteristicsParams6.averageShearStrengthIrregularMasonry,
        (_module_preIntervention7 = module.preIntervention) === null || _module_preIntervention7 === void 0 ? void 0 : (_module_preIntervention_existingMasonryCharacteristicsParams7 = _module_preIntervention7.existingMasonryCharacteristicsParams) === null || _module_preIntervention_existingMasonryCharacteristicsParams7 === void 0 ? void 0 : _module_preIntervention_existingMasonryCharacteristicsParams7.averageShearStrengthRegularMasonry
    ]);
    const handleFormSubmit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ReinforcedMasonryCharacteristicsParamsForm.useCallback[handleFormSubmit]": (body)=>{
            var _module_postIntervention, _module_postIntervention1;
            var _module_preIntervention, _module_postIntervention_designStrengthPostInterventionVerifyInput, _module_postIntervention_designStrengthPostInterventionCalculationResult;
            const updatedModule = {
                ...module,
                preIntervention: (_module_preIntervention = module.preIntervention) !== null && _module_preIntervention !== void 0 ? _module_preIntervention : undefined,
                postIntervention: {
                    reinforcedMasonryCharacteristicsParams: body,
                    designStrengthPostInterventionVerifyInput: (_module_postIntervention_designStrengthPostInterventionVerifyInput = (_module_postIntervention = module.postIntervention) === null || _module_postIntervention === void 0 ? void 0 : _module_postIntervention.designStrengthPostInterventionVerifyInput) !== null && _module_postIntervention_designStrengthPostInterventionVerifyInput !== void 0 ? _module_postIntervention_designStrengthPostInterventionVerifyInput : undefined,
                    designStrengthPostInterventionCalculationResult: (_module_postIntervention_designStrengthPostInterventionCalculationResult = (_module_postIntervention1 = module.postIntervention) === null || _module_postIntervention1 === void 0 ? void 0 : _module_postIntervention1.designStrengthPostInterventionCalculationResult) !== null && _module_postIntervention_designStrengthPostInterventionCalculationResult !== void 0 ? _module_postIntervention_designStrengthPostInterventionCalculationResult : undefined
                }
            };
            mutate({
                projectId,
                moduleId,
                body: updatedModule
            });
        }
    }["ReinforcedMasonryCharacteristicsParamsForm.useCallback[handleFormSubmit]"], [
        mutate,
        projectId,
        moduleId,
        module
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Form"], {
        ...form,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            className: "space-y-4 rounded-md border p-4",
            onSubmit: form.handleSubmit(handleFormSubmit),
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    src: "/assets/masonry-crm/crm_post_intervento.jpg",
                    alt: "site characteristics",
                    height: 250,
                    width: 500,
                    className: "mx-auto rounded-md object-contain",
                    priority: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/reinforced-masonry-characteristics-params-form.tsx",
                    lineNumber: 290,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "reinforcementApplicationType",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_REINFORCEMENT_APPLICATION_TYPE"],
                    optionLabelFn: (p)=>t("reinforcementApplicationType.".concat(p)),
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/reinforced-masonry-characteristics-params-form.tsx",
                    lineNumber: 298,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "singleFaceApplicationReductionCoefficient",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/reinforced-masonry-characteristics-params-form.tsx",
                    lineNumber: 305,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "reinforcedPlasterCoefficient",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/reinforced-masonry-characteristics-params-form.tsx",
                    lineNumber: 311,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "binderMixturesInjections",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_BINDER_MIXTURE_INJECTIONS"],
                    optionLabelFn: (p)=>t("binderMixturesInjections.".concat(p)),
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/reinforced-masonry-characteristics-params-form.tsx",
                    lineNumber: 317,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "correctionCoefficient",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/reinforced-masonry-characteristics-params-form.tsx",
                    lineNumber: 325,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "overallAmplificationCoefficient",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/reinforced-masonry-characteristics-params-form.tsx",
                    lineNumber: 331,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "amplifiedAverageCompressiveStrength",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/reinforced-masonry-characteristics-params-form.tsx",
                    lineNumber: 337,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "amplifiedAverageShearStrengthRegularMasonry",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/reinforced-masonry-characteristics-params-form.tsx",
                    lineNumber: 343,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "amplifiedAverageShearStrengthIrregularMasonry",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/reinforced-masonry-characteristics-params-form.tsx",
                    lineNumber: 349,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "amplifiedAverageNormalElasticityModulus",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/reinforced-masonry-characteristics-params-form.tsx",
                    lineNumber: 355,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "amplifiedAverageShearElasticityModulus",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/reinforced-masonry-characteristics-params-form.tsx",
                    lineNumber: 361,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    type: "submit",
                    className: "w-full sm:w-auto",
                    disabled: isPending,
                    onClick: form.handleSubmit(handleFormSubmit),
                    children: [
                        isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                            className: "animate-spin"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/reinforced-masonry-characteristics-params-form.tsx",
                            lineNumber: 373,
                            columnNumber: 25
                        }, ("TURBOPACK compile-time value", void 0)),
                        tCommon('save')
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/reinforced-masonry-characteristics-params-form.tsx",
                    lineNumber: 367,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/reinforced-masonry-characteristics-params-form.tsx",
            lineNumber: 286,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/reinforced-masonry-characteristics-params-form.tsx",
        lineNumber: 285,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(ReinforcedMasonryCharacteristicsParamsForm, "DwT9Ft7cYVTNSvwpcnZpHVbBj1M=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"]
    ];
});
_c = ReinforcedMasonryCharacteristicsParamsForm;
var _c;
__turbopack_context__.k.register(_c, "ReinforcedMasonryCharacteristicsParamsForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "CrmPostInterventionForm",
    ()=>CrmPostInterventionForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/accordion.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$crm$2f$sections$2f$post$2d$intervention$2f$crm$2d$post$2d$intervention$2d$design$2d$strength$2d$calculation$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$crm$2f$sections$2f$post$2d$intervention$2f$reinforced$2d$masonry$2d$characteristics$2d$params$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/reinforced-masonry-characteristics-params-form.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
const CrmPostInterventionForm = (param)=>{
    let { session, projectId, module } = param;
    var _module_postIntervention;
    _s();
    const [openItems, setOpenItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([
        '0'
    ]);
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.crm.crmPostIntervention');
    const reinforcedMasonryCharacteristics = module === null || module === void 0 ? void 0 : (_module_postIntervention = module.postIntervention) === null || _module_postIntervention === void 0 ? void 0 : _module_postIntervention.reinforcedMasonryCharacteristicsParams;
    const handleItemSaved = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CrmPostInterventionForm.useCallback[handleItemSaved]": (id)=>{
            const nextId = String(Number(id) + 1);
            setOpenItems({
                "CrmPostInterventionForm.useCallback[handleItemSaved]": (old)=>{
                    const temp = old.filter({
                        "CrmPostInterventionForm.useCallback[handleItemSaved].temp": (v)=>v !== id
                    }["CrmPostInterventionForm.useCallback[handleItemSaved].temp"]);
                    return old.includes(nextId) ? temp : [
                        ...temp,
                        nextId
                    ];
                }
            }["CrmPostInterventionForm.useCallback[handleItemSaved]"]);
        }
    }["CrmPostInterventionForm.useCallback[handleItemSaved]"], []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Accordion"], {
            type: "multiple",
            value: openItems,
            onValueChange: setOpenItems,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionItem"], {
                    value: "0",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-medium",
                                children: t('reinforcedMasonryCharacteristics.title')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-form.tsx",
                                lineNumber: 48,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-form.tsx",
                            lineNumber: 47,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionContent"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$crm$2f$sections$2f$post$2d$intervention$2f$reinforced$2d$masonry$2d$characteristics$2d$params$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ReinforcedMasonryCharacteristicsParamsForm"], {
                                session: session,
                                projectId: projectId,
                                module: module,
                                onSave: ()=>handleItemSaved('0')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-form.tsx",
                                lineNumber: 53,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-form.tsx",
                            lineNumber: 52,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-form.tsx",
                    lineNumber: 46,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionItem"], {
                    value: "1",
                    disabled: !reinforcedMasonryCharacteristics,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-lg font-medium",
                                children: t('designStrengthPostInterventionCalculationResult.title')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-form.tsx",
                                lineNumber: 63,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-form.tsx",
                            lineNumber: 62,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionContent"], {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$crm$2f$sections$2f$post$2d$intervention$2f$crm$2d$post$2d$intervention$2d$design$2d$strength$2d$calculation$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CrmPostInterventionDesignStrengthCalculationForm"], {
                                session: session,
                                projectId: projectId,
                                module: module
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-form.tsx",
                                lineNumber: 68,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-form.tsx",
                            lineNumber: 67,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-form.tsx",
                    lineNumber: 61,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-form.tsx",
            lineNumber: 41,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-form.tsx",
        lineNumber: 40,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(CrmPostInterventionForm, "QZ1xO2nUcytjANgEBl9WGpn2Ozs=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"]
    ];
});
_c = CrmPostInterventionForm;
var _c;
__turbopack_context__.k.register(_c, "CrmPostInterventionForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "DesignStrengthPreInterventionCalculationResultCard",
    ()=>DesignStrengthPreInterventionCalculationResultCard
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$separator$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-separator/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/use-intl/dist/esm/development/react.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
function DesignStrengthPreInterventionCalculationResultCard(param) {
    let { designStrengthPreInterventionCalculationResult } = param;
    var _designStrengthPreInterventionCalculationResult_inPlaneBendingStrengthCalculationResult, _designStrengthPreInterventionCalculationResult_inPlaneBendingStrengthCalculationResult1, _designStrengthPreInterventionCalculationResult_outOfPlaneBendingStrengthCalculationResult, _designStrengthPreInterventionCalculationResult_outOfPlaneBendingStrengthCalculationResult1, _designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult, _designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult1, _designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult2;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.crm.crmPreIntervention.designStrengthPreInterventionCalculationResult');
    const _tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.save-project-params.messages');
    const _tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const _locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"])();
    const excludeKeys = [
        'executionClass',
        'structuralElementsCategory',
        'inPlaneBendingStrengthCalculationResult',
        'outOfPlaneBendingStrengthCalculationResult',
        'inPlaneShearStrengthCalculationResult'
    ];
    var _designStrengthPreInterventionCalculationResult_inPlaneBendingStrengthCalculationResult_compressedFlangeNeutralAxisDistance, _designStrengthPreInterventionCalculationResult_inPlaneBendingStrengthCalculationResult_inOrOutplaneBendingMoment, _designStrengthPreInterventionCalculationResult_outOfPlaneBendingStrengthCalculationResult_compressedFlangeNeutralAxisDistance, _designStrengthPreInterventionCalculationResult_outOfPlaneBendingStrengthCalculationResult_inOrOutplaneBendingMoment, _designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult_verticalStress, _designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult_wallSlendernessCorrectionCoefficient, _designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult_shearStrength;
    // form is not available in this component, so we cannot use NumberFormInput directly here
    // Instead, render the values as disabled inputs for display only
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Card"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardHeader"], {
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardTitle"], {
                    children: t('title')
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                    lineNumber: 39,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                lineNumber: 38,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CardContent"], {
                className: "space-y-4",
                children: [
                    Object.entries(designStrengthPreInterventionCalculationResult).filter((param)=>{
                        let [key, value] = param;
                        return !excludeKeys.includes(key) && (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean');
                    }).map((param)=>{
                        let [key, value] = param;
                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "font-medium",
                                    children: [
                                        t("".concat(key, ".label")),
                                        ":"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                                    lineNumber: 53,
                                    columnNumber: 15
                                }, this),
                                ' ',
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: String(value)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                                    lineNumber: 54,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    "data-slot": "form-description",
                                    id: key,
                                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-muted-foreground text-sm'),
                                    children: t("".concat(key, ".description"))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                                    lineNumber: 55,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, key, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                            lineNumber: 52,
                            columnNumber: 13
                        }, this);
                    }),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$separator$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                        lineNumber: 66,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "text-xl font-bold",
                        children: t('inPlaneBendingStrengthCalculationResult.title')
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                        lineNumber: 67,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('inPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                                lineNumber: 71,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: (_designStrengthPreInterventionCalculationResult_inPlaneBendingStrengthCalculationResult_compressedFlangeNeutralAxisDistance = (_designStrengthPreInterventionCalculationResult_inPlaneBendingStrengthCalculationResult = designStrengthPreInterventionCalculationResult.inPlaneBendingStrengthCalculationResult) === null || _designStrengthPreInterventionCalculationResult_inPlaneBendingStrengthCalculationResult === void 0 ? void 0 : _designStrengthPreInterventionCalculationResult_inPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance) !== null && _designStrengthPreInterventionCalculationResult_inPlaneBendingStrengthCalculationResult_compressedFlangeNeutralAxisDistance !== void 0 ? _designStrengthPreInterventionCalculationResult_inPlaneBendingStrengthCalculationResult_compressedFlangeNeutralAxisDistance : ''
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                                lineNumber: 77,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-muted-foreground text-sm'),
                                children: t('inPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance.description')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                                lineNumber: 82,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                        lineNumber: 70,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('inPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                                lineNumber: 89,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: (_designStrengthPreInterventionCalculationResult_inPlaneBendingStrengthCalculationResult_inOrOutplaneBendingMoment = (_designStrengthPreInterventionCalculationResult_inPlaneBendingStrengthCalculationResult1 = designStrengthPreInterventionCalculationResult.inPlaneBendingStrengthCalculationResult) === null || _designStrengthPreInterventionCalculationResult_inPlaneBendingStrengthCalculationResult1 === void 0 ? void 0 : _designStrengthPreInterventionCalculationResult_inPlaneBendingStrengthCalculationResult1.inOrOutplaneBendingMoment) !== null && _designStrengthPreInterventionCalculationResult_inPlaneBendingStrengthCalculationResult_inOrOutplaneBendingMoment !== void 0 ? _designStrengthPreInterventionCalculationResult_inPlaneBendingStrengthCalculationResult_inOrOutplaneBendingMoment : ''
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                                lineNumber: 95,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-muted-foreground text-sm'),
                                children: t('inPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment.description')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                                lineNumber: 100,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                        lineNumber: 88,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$separator$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                        lineNumber: 108,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "text-xl font-bold",
                        children: t('outOfPlaneBendingStrengthCalculationResult.title')
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                        lineNumber: 109,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('outOfPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                                lineNumber: 113,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: (_designStrengthPreInterventionCalculationResult_outOfPlaneBendingStrengthCalculationResult_compressedFlangeNeutralAxisDistance = (_designStrengthPreInterventionCalculationResult_outOfPlaneBendingStrengthCalculationResult = designStrengthPreInterventionCalculationResult.outOfPlaneBendingStrengthCalculationResult) === null || _designStrengthPreInterventionCalculationResult_outOfPlaneBendingStrengthCalculationResult === void 0 ? void 0 : _designStrengthPreInterventionCalculationResult_outOfPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance) !== null && _designStrengthPreInterventionCalculationResult_outOfPlaneBendingStrengthCalculationResult_compressedFlangeNeutralAxisDistance !== void 0 ? _designStrengthPreInterventionCalculationResult_outOfPlaneBendingStrengthCalculationResult_compressedFlangeNeutralAxisDistance : ''
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                                lineNumber: 119,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-muted-foreground text-sm'),
                                children: t('outOfPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance.description')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                                lineNumber: 124,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                        lineNumber: 112,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('outOfPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                                lineNumber: 131,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: (_designStrengthPreInterventionCalculationResult_outOfPlaneBendingStrengthCalculationResult_inOrOutplaneBendingMoment = (_designStrengthPreInterventionCalculationResult_outOfPlaneBendingStrengthCalculationResult1 = designStrengthPreInterventionCalculationResult.outOfPlaneBendingStrengthCalculationResult) === null || _designStrengthPreInterventionCalculationResult_outOfPlaneBendingStrengthCalculationResult1 === void 0 ? void 0 : _designStrengthPreInterventionCalculationResult_outOfPlaneBendingStrengthCalculationResult1.inOrOutplaneBendingMoment) !== null && _designStrengthPreInterventionCalculationResult_outOfPlaneBendingStrengthCalculationResult_inOrOutplaneBendingMoment !== void 0 ? _designStrengthPreInterventionCalculationResult_outOfPlaneBendingStrengthCalculationResult_inOrOutplaneBendingMoment : ''
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                                lineNumber: 137,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-muted-foreground text-sm'),
                                children: t('outOfPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment.description')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                                lineNumber: 142,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                        lineNumber: 130,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$separator$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                        lineNumber: 150,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "text-xl font-bold",
                        children: t('inPlaneShearStrengthCalculationResult.title')
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                        lineNumber: 151,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('inPlaneShearStrengthCalculationResult.verticalStress.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                                lineNumber: 155,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: (_designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult_verticalStress = (_designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult = designStrengthPreInterventionCalculationResult.inPlaneShearStrengthCalculationResult) === null || _designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult === void 0 ? void 0 : _designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult.verticalStress) !== null && _designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult_verticalStress !== void 0 ? _designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult_verticalStress : ''
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                                lineNumber: 158,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-muted-foreground text-sm'),
                                children: t('inPlaneShearStrengthCalculationResult.verticalStress.description')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                                lineNumber: 162,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                        lineNumber: 154,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('inPlaneShearStrengthCalculationResult.wallSlendernessCorrectionCoefficient.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                                lineNumber: 169,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: (_designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult_wallSlendernessCorrectionCoefficient = (_designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult1 = designStrengthPreInterventionCalculationResult.inPlaneShearStrengthCalculationResult) === null || _designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult1 === void 0 ? void 0 : _designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult1.wallSlendernessCorrectionCoefficient) !== null && _designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult_wallSlendernessCorrectionCoefficient !== void 0 ? _designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult_wallSlendernessCorrectionCoefficient : ''
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                                lineNumber: 175,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-muted-foreground text-sm'),
                                children: t('inPlaneShearStrengthCalculationResult.wallSlendernessCorrectionCoefficient.description')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                                lineNumber: 180,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                        lineNumber: 168,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-medium",
                                children: [
                                    t('inPlaneShearStrengthCalculationResult.shearStrength.label'),
                                    ":"
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                                lineNumber: 187,
                                columnNumber: 11
                            }, this),
                            ' ',
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                children: (_designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult_shearStrength = (_designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult2 = designStrengthPreInterventionCalculationResult.inPlaneShearStrengthCalculationResult) === null || _designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult2 === void 0 ? void 0 : _designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult2.shearStrength) !== null && _designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult_shearStrength !== void 0 ? _designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult_shearStrength : ''
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                                lineNumber: 190,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('text-muted-foreground text-sm'),
                                children: t('inPlaneShearStrengthCalculationResult.shearStrength.description')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                                lineNumber: 194,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                        lineNumber: 186,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
                lineNumber: 41,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx",
        lineNumber: 37,
        columnNumber: 5
    }, this);
}
_s(DesignStrengthPreInterventionCalculationResultCard, "pC2DzZCCPzg7Py4ab/kIMaqz6bU=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLocale"]
    ];
});
_c = DesignStrengthPreInterventionCalculationResultCard;
var _c;
__turbopack_context__.k.register(_c, "DesignStrengthPreInterventionCalculationResultCard");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "DesignStrengthPreInterventionCalculationResultForm",
    ()=>DesignStrengthPreInterventionCalculationResultForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/select-form-fixed-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-save-module-params-mutation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$crm$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/masonry/crm-form.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$crm$2f$sections$2f$pre$2d$intervention$2f$design$2d$strength$2d$pre$2d$intervention$2d$calculation$2d$result$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const DesignStrengthPreInterventionCalculationResultForm = (param)=>{
    let { session, projectId, module, onSave } = param;
    var _module_preIntervention, _module_preIntervention1;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.crm.crmPreIntervention.designStrengthPreInterventionCalculationResult');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.save-project-params.messages');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const [showCalculationResult, setShowCalculationResult] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const moduleId = module.id;
    const designStrengthPreInterventionCalculationResult = module === null || module === void 0 ? void 0 : (_module_preIntervention = module.preIntervention) === null || _module_preIntervention === void 0 ? void 0 : _module_preIntervention.designStrengthPreInterventionCalculationResult;
    const existingMasonryCharacteristicsParams = (_module_preIntervention1 = module.preIntervention) === null || _module_preIntervention1 === void 0 ? void 0 : _module_preIntervention1.existingMasonryCharacteristicsParams;
    var _designStrengthPreInterventionCalculationResult_executionClass, _designStrengthPreInterventionCalculationResult_structuralElementsCategory, _designStrengthPreInterventionCalculationResult_masonryStrengthSafetyFactor, _designStrengthPreInterventionCalculationResult_designCompressiveStrength, _designStrengthPreInterventionCalculationResult_designShearStrengthRegularMasonry, _designStrengthPreInterventionCalculationResult_designShearStrengthIrregularMasonry, _designStrengthPreInterventionCalculationResult_designNormalElasticityModulus, _designStrengthPreInterventionCalculationResult_designShearElasticityModulus, _designStrengthPreInterventionCalculationResult_ultimateCompressiveStrainLinearBehavior, _designStrengthPreInterventionCalculationResult_ultimateCompressiveStrain, _designStrengthPreInterventionCalculationResult_firstCoefficient, _designStrengthPreInterventionCalculationResult_panelSelfWeight, _designStrengthPreInterventionCalculationResult_inPlaneBendingStrengthCalculationResult, _designStrengthPreInterventionCalculationResult_outOfPlaneBendingStrengthCalculationResult, _designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult;
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$crm$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["designStrengthPreInterventionCalculationResultSchema"]),
        defaultValues: {
            executionClass: (_designStrengthPreInterventionCalculationResult_executionClass = designStrengthPreInterventionCalculationResult === null || designStrengthPreInterventionCalculationResult === void 0 ? void 0 : designStrengthPreInterventionCalculationResult.executionClass) !== null && _designStrengthPreInterventionCalculationResult_executionClass !== void 0 ? _designStrengthPreInterventionCalculationResult_executionClass : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["executionClass"].ONE,
            structuralElementsCategory: (_designStrengthPreInterventionCalculationResult_structuralElementsCategory = designStrengthPreInterventionCalculationResult === null || designStrengthPreInterventionCalculationResult === void 0 ? void 0 : designStrengthPreInterventionCalculationResult.structuralElementsCategory) !== null && _designStrengthPreInterventionCalculationResult_structuralElementsCategory !== void 0 ? _designStrengthPreInterventionCalculationResult_structuralElementsCategory : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["loadResistingCategory"].MASONRY_CAT_II_AND_ANY_TYPE_OF_MORTAR,
            masonryStrengthSafetyFactor: (_designStrengthPreInterventionCalculationResult_masonryStrengthSafetyFactor = designStrengthPreInterventionCalculationResult === null || designStrengthPreInterventionCalculationResult === void 0 ? void 0 : designStrengthPreInterventionCalculationResult.masonryStrengthSafetyFactor) !== null && _designStrengthPreInterventionCalculationResult_masonryStrengthSafetyFactor !== void 0 ? _designStrengthPreInterventionCalculationResult_masonryStrengthSafetyFactor : 0,
            designCompressiveStrength: (_designStrengthPreInterventionCalculationResult_designCompressiveStrength = designStrengthPreInterventionCalculationResult === null || designStrengthPreInterventionCalculationResult === void 0 ? void 0 : designStrengthPreInterventionCalculationResult.designCompressiveStrength) !== null && _designStrengthPreInterventionCalculationResult_designCompressiveStrength !== void 0 ? _designStrengthPreInterventionCalculationResult_designCompressiveStrength : 0,
            designShearStrengthRegularMasonry: (_designStrengthPreInterventionCalculationResult_designShearStrengthRegularMasonry = designStrengthPreInterventionCalculationResult === null || designStrengthPreInterventionCalculationResult === void 0 ? void 0 : designStrengthPreInterventionCalculationResult.designShearStrengthRegularMasonry) !== null && _designStrengthPreInterventionCalculationResult_designShearStrengthRegularMasonry !== void 0 ? _designStrengthPreInterventionCalculationResult_designShearStrengthRegularMasonry : 0,
            designShearStrengthIrregularMasonry: (_designStrengthPreInterventionCalculationResult_designShearStrengthIrregularMasonry = designStrengthPreInterventionCalculationResult === null || designStrengthPreInterventionCalculationResult === void 0 ? void 0 : designStrengthPreInterventionCalculationResult.designShearStrengthIrregularMasonry) !== null && _designStrengthPreInterventionCalculationResult_designShearStrengthIrregularMasonry !== void 0 ? _designStrengthPreInterventionCalculationResult_designShearStrengthIrregularMasonry : 0,
            designNormalElasticityModulus: (_designStrengthPreInterventionCalculationResult_designNormalElasticityModulus = designStrengthPreInterventionCalculationResult === null || designStrengthPreInterventionCalculationResult === void 0 ? void 0 : designStrengthPreInterventionCalculationResult.designNormalElasticityModulus) !== null && _designStrengthPreInterventionCalculationResult_designNormalElasticityModulus !== void 0 ? _designStrengthPreInterventionCalculationResult_designNormalElasticityModulus : 0,
            designShearElasticityModulus: (_designStrengthPreInterventionCalculationResult_designShearElasticityModulus = designStrengthPreInterventionCalculationResult === null || designStrengthPreInterventionCalculationResult === void 0 ? void 0 : designStrengthPreInterventionCalculationResult.designShearElasticityModulus) !== null && _designStrengthPreInterventionCalculationResult_designShearElasticityModulus !== void 0 ? _designStrengthPreInterventionCalculationResult_designShearElasticityModulus : 0,
            ultimateCompressiveStrainLinearBehavior: (_designStrengthPreInterventionCalculationResult_ultimateCompressiveStrainLinearBehavior = designStrengthPreInterventionCalculationResult === null || designStrengthPreInterventionCalculationResult === void 0 ? void 0 : designStrengthPreInterventionCalculationResult.ultimateCompressiveStrainLinearBehavior) !== null && _designStrengthPreInterventionCalculationResult_ultimateCompressiveStrainLinearBehavior !== void 0 ? _designStrengthPreInterventionCalculationResult_ultimateCompressiveStrainLinearBehavior : 0,
            ultimateCompressiveStrain: (_designStrengthPreInterventionCalculationResult_ultimateCompressiveStrain = designStrengthPreInterventionCalculationResult === null || designStrengthPreInterventionCalculationResult === void 0 ? void 0 : designStrengthPreInterventionCalculationResult.ultimateCompressiveStrain) !== null && _designStrengthPreInterventionCalculationResult_ultimateCompressiveStrain !== void 0 ? _designStrengthPreInterventionCalculationResult_ultimateCompressiveStrain : 0.0035,
            firstCoefficient: (_designStrengthPreInterventionCalculationResult_firstCoefficient = designStrengthPreInterventionCalculationResult === null || designStrengthPreInterventionCalculationResult === void 0 ? void 0 : designStrengthPreInterventionCalculationResult.firstCoefficient) !== null && _designStrengthPreInterventionCalculationResult_firstCoefficient !== void 0 ? _designStrengthPreInterventionCalculationResult_firstCoefficient : 0,
            panelSelfWeight: (_designStrengthPreInterventionCalculationResult_panelSelfWeight = designStrengthPreInterventionCalculationResult === null || designStrengthPreInterventionCalculationResult === void 0 ? void 0 : designStrengthPreInterventionCalculationResult.panelSelfWeight) !== null && _designStrengthPreInterventionCalculationResult_panelSelfWeight !== void 0 ? _designStrengthPreInterventionCalculationResult_panelSelfWeight : 0,
            inPlaneBendingStrengthCalculationResult: (_designStrengthPreInterventionCalculationResult_inPlaneBendingStrengthCalculationResult = designStrengthPreInterventionCalculationResult === null || designStrengthPreInterventionCalculationResult === void 0 ? void 0 : designStrengthPreInterventionCalculationResult.inPlaneBendingStrengthCalculationResult) !== null && _designStrengthPreInterventionCalculationResult_inPlaneBendingStrengthCalculationResult !== void 0 ? _designStrengthPreInterventionCalculationResult_inPlaneBendingStrengthCalculationResult : {
                compressedFlangeNeutralAxisDistance: 0,
                inOrOutplaneBendingMoment: 0
            },
            outOfPlaneBendingStrengthCalculationResult: (_designStrengthPreInterventionCalculationResult_outOfPlaneBendingStrengthCalculationResult = designStrengthPreInterventionCalculationResult === null || designStrengthPreInterventionCalculationResult === void 0 ? void 0 : designStrengthPreInterventionCalculationResult.outOfPlaneBendingStrengthCalculationResult) !== null && _designStrengthPreInterventionCalculationResult_outOfPlaneBendingStrengthCalculationResult !== void 0 ? _designStrengthPreInterventionCalculationResult_outOfPlaneBendingStrengthCalculationResult : {
                compressedFlangeNeutralAxisDistance: 0,
                inOrOutplaneBendingMoment: 0
            },
            inPlaneShearStrengthCalculationResult: (_designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult = designStrengthPreInterventionCalculationResult === null || designStrengthPreInterventionCalculationResult === void 0 ? void 0 : designStrengthPreInterventionCalculationResult.inPlaneShearStrengthCalculationResult) !== null && _designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult !== void 0 ? _designStrengthPreInterventionCalculationResult_inPlaneShearStrengthCalculationResult : {
                shearStrength: 0,
                verticalStress: 0,
                wallSlendernessCorrectionCoefficient: 0
            }
        }
    });
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"])(session.accessToken, {
        onSuccess: {
            "DesignStrengthPreInterventionCalculationResultForm.useSaveModuleParamsMutation": async ()=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(tAction('edit.success'));
                await queryClient.invalidateQueries({
                    queryKey: [
                        'modules',
                        {
                            projectId,
                            moduleId
                        }
                    ]
                });
                onSave();
            }
        }["DesignStrengthPreInterventionCalculationResultForm.useSaveModuleParamsMutation"],
        onError: {
            "DesignStrengthPreInterventionCalculationResultForm.useSaveModuleParamsMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(tAction('edit.failure', {
                    error: error.message
                }));
            }
        }["DesignStrengthPreInterventionCalculationResultForm.useSaveModuleParamsMutation"]
    });
    const structuralElementsCategoryFormValue = form.watch('structuralElementsCategory');
    const executionClassFormValue = form.watch('executionClass');
    var _form_watch;
    const ultimateCompressiveStrainFormValue = (_form_watch = form.watch('ultimateCompressiveStrain')) !== null && _form_watch !== void 0 ? _form_watch : 0.0035;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "DesignStrengthPreInterventionCalculationResultForm.useEffect": ()=>{
            const masonryStrengthSafetyFactor = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["masonryStrengthSafetyFactorMapping"][structuralElementsCategoryFormValue][executionClassFormValue];
            form.setValue('masonryStrengthSafetyFactor', masonryStrengthSafetyFactor);
            var _existingMasonryCharacteristicsParams_confidenceFactor, _existingMasonryCharacteristicsParams_confidenceFactor1;
            const divider = masonryStrengthSafetyFactor * ((_existingMasonryCharacteristicsParams_confidenceFactor = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.confidenceFactor) !== null && _existingMasonryCharacteristicsParams_confidenceFactor !== void 0 ? _existingMasonryCharacteristicsParams_confidenceFactor : 0) === 0 ? 1 : masonryStrengthSafetyFactor * ((_existingMasonryCharacteristicsParams_confidenceFactor1 = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.confidenceFactor) !== null && _existingMasonryCharacteristicsParams_confidenceFactor1 !== void 0 ? _existingMasonryCharacteristicsParams_confidenceFactor1 : 0);
            var _existingMasonryCharacteristicsParams_amplifiedAverageCompressiveStrength;
            // designCompressiveStrength: amplifiedAverageCompressiveStrength / confidenceFactor / masonryStrengthSafetyFactor
            const designCompressiveStrength = ((_existingMasonryCharacteristicsParams_amplifiedAverageCompressiveStrength = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.amplifiedAverageCompressiveStrength) !== null && _existingMasonryCharacteristicsParams_amplifiedAverageCompressiveStrength !== void 0 ? _existingMasonryCharacteristicsParams_amplifiedAverageCompressiveStrength : 0) / divider;
            form.setValue('designCompressiveStrength', designCompressiveStrength);
            var _existingMasonryCharacteristicsParams_amplifiedAverageShearStrengthRegularMasonry;
            const designShearStrengthRegularMasonry = ((_existingMasonryCharacteristicsParams_amplifiedAverageShearStrengthRegularMasonry = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.amplifiedAverageShearStrengthRegularMasonry) !== null && _existingMasonryCharacteristicsParams_amplifiedAverageShearStrengthRegularMasonry !== void 0 ? _existingMasonryCharacteristicsParams_amplifiedAverageShearStrengthRegularMasonry : 0) / divider;
            form.setValue('designShearStrengthRegularMasonry', designShearStrengthRegularMasonry);
            var _existingMasonryCharacteristicsParams_amplifiedAverageShearStrengthIrregularMasonry;
            const designShearStrengthIrregularMasonry = ((_existingMasonryCharacteristicsParams_amplifiedAverageShearStrengthIrregularMasonry = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.amplifiedAverageShearStrengthIrregularMasonry) !== null && _existingMasonryCharacteristicsParams_amplifiedAverageShearStrengthIrregularMasonry !== void 0 ? _existingMasonryCharacteristicsParams_amplifiedAverageShearStrengthIrregularMasonry : 0) / divider;
            form.setValue('designShearStrengthIrregularMasonry', designShearStrengthIrregularMasonry);
            var _existingMasonryCharacteristicsParams_amplifiedAverageNormalElasticityModulus;
            const designNormalElasticityModulus = ((_existingMasonryCharacteristicsParams_amplifiedAverageNormalElasticityModulus = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.amplifiedAverageNormalElasticityModulus) !== null && _existingMasonryCharacteristicsParams_amplifiedAverageNormalElasticityModulus !== void 0 ? _existingMasonryCharacteristicsParams_amplifiedAverageNormalElasticityModulus : 0) / masonryStrengthSafetyFactor;
            form.setValue('designNormalElasticityModulus', designNormalElasticityModulus);
            var _existingMasonryCharacteristicsParams_amplifiedAverageShearElasticityModulus;
            const designShearElasticityModulus = ((_existingMasonryCharacteristicsParams_amplifiedAverageShearElasticityModulus = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.amplifiedAverageShearElasticityModulus) !== null && _existingMasonryCharacteristicsParams_amplifiedAverageShearElasticityModulus !== void 0 ? _existingMasonryCharacteristicsParams_amplifiedAverageShearElasticityModulus : 0) / masonryStrengthSafetyFactor;
            form.setValue('designShearElasticityModulus', designShearElasticityModulus);
            const ultimateCompressiveStrainLinearBehavior = designCompressiveStrength / designNormalElasticityModulus;
            form.setValue('ultimateCompressiveStrainLinearBehavior', ultimateCompressiveStrainLinearBehavior);
            const firstCoefficient = ultimateCompressiveStrainLinearBehavior / ultimateCompressiveStrainFormValue;
            form.setValue('firstCoefficient', firstCoefficient);
            var _existingMasonryCharacteristicsParams_panelThickness;
            const panelThickness = (_existingMasonryCharacteristicsParams_panelThickness = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.panelThickness) !== null && _existingMasonryCharacteristicsParams_panelThickness !== void 0 ? _existingMasonryCharacteristicsParams_panelThickness : 0;
            var _existingMasonryCharacteristicsParams_panelWidth;
            const panelWidth = (_existingMasonryCharacteristicsParams_panelWidth = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.panelWidth) !== null && _existingMasonryCharacteristicsParams_panelWidth !== void 0 ? _existingMasonryCharacteristicsParams_panelWidth : 0;
            var _existingMasonryCharacteristicsParams_panelHeight;
            const panelHeight = (_existingMasonryCharacteristicsParams_panelHeight = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.panelHeight) !== null && _existingMasonryCharacteristicsParams_panelHeight !== void 0 ? _existingMasonryCharacteristicsParams_panelHeight : 0;
            var _existingMasonryCharacteristicsParams_specificWeight;
            const specificWeight = (_existingMasonryCharacteristicsParams_specificWeight = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.specificWeight) !== null && _existingMasonryCharacteristicsParams_specificWeight !== void 0 ? _existingMasonryCharacteristicsParams_specificWeight : 0;
            const panelSelfWeight = panelWidth * panelHeight * panelThickness * specificWeight * 1.3;
            form.setValue('panelSelfWeight', panelSelfWeight);
            // inPlaneBendingStrengthCalculationResult, outOfPlaneBendingStrengthCalculationResult, inPlaneShearStrengthCalculationResult are calculated in another form
            // inOrOutplanBendingMoment: =2*(D39*1000)/(D11*1000*D31)*D37/(2*D37-D36)
            const inPlaneCompressedFlangeNeutralAxisDistance = 2 * (panelSelfWeight * 1000) / (panelThickness * 1000 * designCompressiveStrength) * ultimateCompressiveStrainFormValue / (2 * ultimateCompressiveStrainFormValue - ultimateCompressiveStrainLinearBehavior);
            // inPlaneBendingMoment
            // =(D31*(D11*1000)*D41/2*(D10*1000)*(1-D38)-D41*((1-D38)^2+D38*((D10*1000)/2-D41+2/3*D38*D41)))/1000/1000
            const firstValue = designCompressiveStrength * (panelThickness * 1000) * inPlaneCompressedFlangeNeutralAxisDistance / 2 * (panelWidth * 1000) * (1 - firstCoefficient);
            const secondValue = inPlaneCompressedFlangeNeutralAxisDistance * ((1 - firstCoefficient) ** 2 + firstCoefficient * (panelWidth * 1000 / 2 - inPlaneCompressedFlangeNeutralAxisDistance + 2 / 3 * firstCoefficient * inPlaneCompressedFlangeNeutralAxisDistance));
            const inPlaneBendingMoment = (firstValue - secondValue) / 1000 / 1000;
            const inPlaneBendingStrengthCalculationResult = {
                compressedFlangeNeutralAxisDistance: Number(inPlaneCompressedFlangeNeutralAxisDistance.toFixed(2)),
                inOrOutplaneBendingMoment: Number(inPlaneBendingMoment.toFixed(2))
            };
            form.setValue('inPlaneBendingStrengthCalculationResult', inPlaneBendingStrengthCalculationResult);
            // outOfPlaneCompressedFlangeNeutralAxisDistance
            // =D39/(0.85*D31*0.7)
            const outOfPlaneCompressedFlangeNeutralAxisDistance = panelSelfWeight / (0.85 * designCompressiveStrength * 0.7);
            // outOfPlaneBendingMoment
            // =D39*(D11*1000/1000/2-0.7*D44/1000/2)
            const outOfPlaneBendingMoment = panelSelfWeight * (panelThickness * 1000 / 1000 / 2 - 0.7 * outOfPlaneCompressedFlangeNeutralAxisDistance / 1000 / 2);
            const outOfPlaneBendingStrengthCalculationResult = {
                compressedFlangeNeutralAxisDistance: Number(outOfPlaneCompressedFlangeNeutralAxisDistance.toFixed(2)),
                inOrOutplaneBendingMoment: Number(outOfPlaneBendingMoment.toFixed(2))
            };
            form.setValue('outOfPlaneBendingStrengthCalculationResult', outOfPlaneBendingStrengthCalculationResult);
            // inPlaneShearStrengthCalculationResult
            // verticalStress = =D39*1000/(D11*1000*D10*1000)
            const verticalStress = panelSelfWeight * 1000 / (panelThickness * 1000 * panelWidth * 1000);
            // wallSlendernessCorrectionCoefficient =IF(D12/D10<=1,1,IF(D12/D10<=1.5,D12/D10,1.5))
            const wallSlendernessRatio = panelHeight / panelWidth;
            const wallSlendernessCorrectionCoefficient = wallSlendernessRatio <= 1 ? 1 : wallSlendernessRatio <= 1.5 ? wallSlendernessRatio : 1.5;
            // shearStrength =(D10*1000*D11*1000*(1.5*D32/D48)*SQRT(1+D47/(1.5*D32)))/1000
            const shearStrength = panelWidth * 1000 * panelThickness * 1000 * (1.5 * designShearStrengthRegularMasonry / wallSlendernessCorrectionCoefficient) * Math.sqrt(1 + verticalStress / (1.5 * designShearStrengthRegularMasonry)) / 1000;
            const inPlaneShearStrengthCalculationResult = {
                verticalStress: Number(verticalStress.toFixed(2)),
                wallSlendernessCorrectionCoefficient: Number(wallSlendernessCorrectionCoefficient.toFixed(2)),
                shearStrength: Number(shearStrength.toFixed(2))
            };
            form.setValue('inPlaneShearStrengthCalculationResult', inPlaneShearStrengthCalculationResult);
        }
    }["DesignStrengthPreInterventionCalculationResultForm.useEffect"], [
        structuralElementsCategoryFormValue,
        executionClassFormValue,
        existingMasonryCharacteristicsParams,
        ultimateCompressiveStrainFormValue,
        form
    ]);
    const handleFormSubmit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "DesignStrengthPreInterventionCalculationResultForm.useCallback[handleFormSubmit]": (body)=>{
            var _module_preIntervention;
            var _module_preIntervention_existingMasonryCharacteristicsParams, _module_postIntervention;
            const updatedModule = {
                ...module,
                preIntervention: {
                    existingMasonryCharacteristicsParams: (_module_preIntervention_existingMasonryCharacteristicsParams = (_module_preIntervention = module.preIntervention) === null || _module_preIntervention === void 0 ? void 0 : _module_preIntervention.existingMasonryCharacteristicsParams) !== null && _module_preIntervention_existingMasonryCharacteristicsParams !== void 0 ? _module_preIntervention_existingMasonryCharacteristicsParams : undefined,
                    designStrengthPreInterventionCalculationResult: body !== null && body !== void 0 ? body : undefined
                },
                postIntervention: (_module_postIntervention = module === null || module === void 0 ? void 0 : module.postIntervention) !== null && _module_postIntervention !== void 0 ? _module_postIntervention : undefined
            };
            mutate({
                projectId,
                moduleId,
                body: updatedModule
            });
        }
    }["DesignStrengthPreInterventionCalculationResultForm.useCallback[handleFormSubmit]"], [
        mutate,
        projectId,
        moduleId,
        module
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Form"], {
        ...form,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            className: "space-y-4 rounded-md border p-4",
            onSubmit: form.handleSubmit(handleFormSubmit),
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "executionClass",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_EXECUTION_CLASS"],
                    optionLabelFn: (p)=>t("executionClass.".concat(p)),
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-form.tsx",
                    lineNumber: 348,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "structuralElementsCategory",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_LOAD_RESISTING_CATEGORY"],
                    optionLabelFn: (p)=>t("structuralElementsCategory.".concat(p)),
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-form.tsx",
                    lineNumber: 355,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    type: "button",
                    className: "w-full sm:w-auto",
                    disabled: isPending,
                    onClick: ()=>setShowCalculationResult(true),
                    children: [
                        isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                            className: "animate-spin"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-form.tsx",
                            lineNumber: 368,
                            columnNumber: 25
                        }, ("TURBOPACK compile-time value", void 0)),
                        tCommon('calculate')
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-form.tsx",
                    lineNumber: 362,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                showCalculationResult && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$crm$2f$sections$2f$pre$2d$intervention$2f$design$2d$strength$2d$pre$2d$intervention$2d$calculation$2d$result$2d$card$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DesignStrengthPreInterventionCalculationResultCard"], {
                    designStrengthPreInterventionCalculationResult: form.getValues()
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-form.tsx",
                    lineNumber: 372,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    type: "submit",
                    className: "w-full sm:w-auto ".concat(!showCalculationResult ? 'hidden' : ''),
                    disabled: isPending,
                    onClick: form.handleSubmit(handleFormSubmit),
                    children: [
                        isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                            className: "animate-spin"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-form.tsx",
                            lineNumber: 382,
                            columnNumber: 25
                        }, ("TURBOPACK compile-time value", void 0)),
                        tCommon('next')
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-form.tsx",
                    lineNumber: 376,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-form.tsx",
            lineNumber: 344,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-form.tsx",
        lineNumber: 343,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(DesignStrengthPreInterventionCalculationResultForm, "yA6nQDeWNzbeAT8JvxRrKsLcOlg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"]
    ];
});
_c = DesignStrengthPreInterventionCalculationResultForm;
var _c;
__turbopack_context__.k.register(_c, "DesignStrengthPreInterventionCalculationResultForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "ExistingMasonryCharacteristicsParamsForm",
    ()=>ExistingMasonryCharacteristicsParamsForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/select-form-fixed-input.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-save-module-params-mutation.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$crm$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/masonry/crm-form.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-client] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const ExistingMasonryCharacteristicsParamsForm = (param)=>{
    let { session, projectId, module, onSave } = param;
    var _module_preIntervention;
    _s();
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.crm.crmPreIntervention.existingMasonryCharacteristicsParams');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.save-project-params.messages');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const queryClient = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"])();
    const moduleId = module.id;
    const existingMasonryCharacteristicsParams = module === null || module === void 0 ? void 0 : (_module_preIntervention = module.preIntervention) === null || _module_preIntervention === void 0 ? void 0 : _module_preIntervention.existingMasonryCharacteristicsParams;
    var _existingMasonryCharacteristicsParams_panelWidth, _existingMasonryCharacteristicsParams_panelThickness, _existingMasonryCharacteristicsParams_panelHeight, _existingMasonryCharacteristicsParams_masonryType, _existingMasonryCharacteristicsParams_knowledgeLevel, _existingMasonryCharacteristicsParams_confidenceFactor, _existingMasonryCharacteristicsParams_averageCompressiveStrength, _existingMasonryCharacteristicsParams_averageShearStrengthRegularMasonry, _existingMasonryCharacteristicsParams_averageShearStrengthIrregularMasonry, _existingMasonryCharacteristicsParams_averageNormalElasticityModulus, _existingMasonryCharacteristicsParams_averageShearElasticityModulus, _existingMasonryCharacteristicsParams_specificWeight, _existingMasonryCharacteristicsParams_enhancementCharacteristics, _existingMasonryCharacteristicsParams_correctionCoefficient, _existingMasonryCharacteristicsParams_amplifiedAverageCompressiveStrength, _existingMasonryCharacteristicsParams_amplifiedAverageShearStrengthRegularMasonry, _existingMasonryCharacteristicsParams_amplifiedAverageShearStrengthIrregularMasonry, _existingMasonryCharacteristicsParams_amplifiedAverageNormalElasticityModulus, _existingMasonryCharacteristicsParams_amplifiedAverageShearElasticityModulus;
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$crm$2d$form$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["existingMasonryCharacteristicsParamsSchema"]),
        defaultValues: {
            panelWidth: (_existingMasonryCharacteristicsParams_panelWidth = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.panelWidth) !== null && _existingMasonryCharacteristicsParams_panelWidth !== void 0 ? _existingMasonryCharacteristicsParams_panelWidth : 0,
            panelThickness: (_existingMasonryCharacteristicsParams_panelThickness = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.panelThickness) !== null && _existingMasonryCharacteristicsParams_panelThickness !== void 0 ? _existingMasonryCharacteristicsParams_panelThickness : 0,
            panelHeight: (_existingMasonryCharacteristicsParams_panelHeight = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.panelHeight) !== null && _existingMasonryCharacteristicsParams_panelHeight !== void 0 ? _existingMasonryCharacteristicsParams_panelHeight : 0,
            masonryType: (_existingMasonryCharacteristicsParams_masonryType = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.masonryType) !== null && _existingMasonryCharacteristicsParams_masonryType !== void 0 ? _existingMasonryCharacteristicsParams_masonryType : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CRM_MASONRY_TYPE"].MURATURA_IN_PIETRAME_DISORDINATA,
            knowledgeLevel: (_existingMasonryCharacteristicsParams_knowledgeLevel = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.knowledgeLevel) !== null && _existingMasonryCharacteristicsParams_knowledgeLevel !== void 0 ? _existingMasonryCharacteristicsParams_knowledgeLevel : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleMaterialKnowledgeLevel"].LC1,
            confidenceFactor: (_existingMasonryCharacteristicsParams_confidenceFactor = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.confidenceFactor) !== null && _existingMasonryCharacteristicsParams_confidenceFactor !== void 0 ? _existingMasonryCharacteristicsParams_confidenceFactor : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleMaterialKnowledgeLevelValues"].LC1,
            averageCompressiveStrength: (_existingMasonryCharacteristicsParams_averageCompressiveStrength = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.averageCompressiveStrength) !== null && _existingMasonryCharacteristicsParams_averageCompressiveStrength !== void 0 ? _existingMasonryCharacteristicsParams_averageCompressiveStrength : 0,
            averageShearStrengthRegularMasonry: (_existingMasonryCharacteristicsParams_averageShearStrengthRegularMasonry = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.averageShearStrengthRegularMasonry) !== null && _existingMasonryCharacteristicsParams_averageShearStrengthRegularMasonry !== void 0 ? _existingMasonryCharacteristicsParams_averageShearStrengthRegularMasonry : 0,
            averageShearStrengthIrregularMasonry: (_existingMasonryCharacteristicsParams_averageShearStrengthIrregularMasonry = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.averageShearStrengthIrregularMasonry) !== null && _existingMasonryCharacteristicsParams_averageShearStrengthIrregularMasonry !== void 0 ? _existingMasonryCharacteristicsParams_averageShearStrengthIrregularMasonry : 0,
            averageNormalElasticityModulus: (_existingMasonryCharacteristicsParams_averageNormalElasticityModulus = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.averageNormalElasticityModulus) !== null && _existingMasonryCharacteristicsParams_averageNormalElasticityModulus !== void 0 ? _existingMasonryCharacteristicsParams_averageNormalElasticityModulus : 0,
            averageShearElasticityModulus: (_existingMasonryCharacteristicsParams_averageShearElasticityModulus = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.averageShearElasticityModulus) !== null && _existingMasonryCharacteristicsParams_averageShearElasticityModulus !== void 0 ? _existingMasonryCharacteristicsParams_averageShearElasticityModulus : 0,
            specificWeight: (_existingMasonryCharacteristicsParams_specificWeight = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.specificWeight) !== null && _existingMasonryCharacteristicsParams_specificWeight !== void 0 ? _existingMasonryCharacteristicsParams_specificWeight : 0,
            enhancementCharacteristics: (_existingMasonryCharacteristicsParams_enhancementCharacteristics = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.enhancementCharacteristics) !== null && _existingMasonryCharacteristicsParams_enhancementCharacteristics !== void 0 ? _existingMasonryCharacteristicsParams_enhancementCharacteristics : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ENHANCEMENT_CHARACTERISTICS"].NON_PRESENTI,
            correctionCoefficient: (_existingMasonryCharacteristicsParams_correctionCoefficient = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.correctionCoefficient) !== null && _existingMasonryCharacteristicsParams_correctionCoefficient !== void 0 ? _existingMasonryCharacteristicsParams_correctionCoefficient : __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["correctiveCoefficientValues"][__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ENHANCEMENT_CHARACTERISTICS"].NON_PRESENTI][__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CRM_MASONRY_TYPE"].MURATURA_IN_PIETRAME_DISORDINATA],
            amplifiedAverageCompressiveStrength: (_existingMasonryCharacteristicsParams_amplifiedAverageCompressiveStrength = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.amplifiedAverageCompressiveStrength) !== null && _existingMasonryCharacteristicsParams_amplifiedAverageCompressiveStrength !== void 0 ? _existingMasonryCharacteristicsParams_amplifiedAverageCompressiveStrength : 0,
            amplifiedAverageShearStrengthRegularMasonry: (_existingMasonryCharacteristicsParams_amplifiedAverageShearStrengthRegularMasonry = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.amplifiedAverageShearStrengthRegularMasonry) !== null && _existingMasonryCharacteristicsParams_amplifiedAverageShearStrengthRegularMasonry !== void 0 ? _existingMasonryCharacteristicsParams_amplifiedAverageShearStrengthRegularMasonry : 0,
            amplifiedAverageShearStrengthIrregularMasonry: (_existingMasonryCharacteristicsParams_amplifiedAverageShearStrengthIrregularMasonry = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.amplifiedAverageShearStrengthIrregularMasonry) !== null && _existingMasonryCharacteristicsParams_amplifiedAverageShearStrengthIrregularMasonry !== void 0 ? _existingMasonryCharacteristicsParams_amplifiedAverageShearStrengthIrregularMasonry : 0,
            amplifiedAverageNormalElasticityModulus: (_existingMasonryCharacteristicsParams_amplifiedAverageNormalElasticityModulus = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.amplifiedAverageNormalElasticityModulus) !== null && _existingMasonryCharacteristicsParams_amplifiedAverageNormalElasticityModulus !== void 0 ? _existingMasonryCharacteristicsParams_amplifiedAverageNormalElasticityModulus : 0,
            amplifiedAverageShearElasticityModulus: (_existingMasonryCharacteristicsParams_amplifiedAverageShearElasticityModulus = existingMasonryCharacteristicsParams === null || existingMasonryCharacteristicsParams === void 0 ? void 0 : existingMasonryCharacteristicsParams.amplifiedAverageShearElasticityModulus) !== null && _existingMasonryCharacteristicsParams_amplifiedAverageShearElasticityModulus !== void 0 ? _existingMasonryCharacteristicsParams_amplifiedAverageShearElasticityModulus : 0
        }
    });
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"])(session.accessToken, {
        onSuccess: {
            "ExistingMasonryCharacteristicsParamsForm.useSaveModuleParamsMutation": async ()=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success(tAction('edit.success'));
                await queryClient.invalidateQueries({
                    queryKey: [
                        'modules',
                        {
                            projectId,
                            moduleId
                        }
                    ]
                });
                onSave();
            }
        }["ExistingMasonryCharacteristicsParamsForm.useSaveModuleParamsMutation"],
        onError: {
            "ExistingMasonryCharacteristicsParamsForm.useSaveModuleParamsMutation": (error)=>{
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(tAction('edit.failure', {
                    error: error.message
                }));
            }
        }["ExistingMasonryCharacteristicsParamsForm.useSaveModuleParamsMutation"]
    });
    const knowledgeMaterialLevel = form.watch('knowledgeLevel');
    const masonryType = form.watch('masonryType');
    const enhancementCharacteristics = form.watch('enhancementCharacteristics');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ExistingMasonryCharacteristicsParamsForm.useEffect": ()=>{
            const confidenceFactor = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["moduleMaterialKnowledgeLevelValues"][knowledgeMaterialLevel];
            form.setValue('confidenceFactor', confidenceFactor);
            const averageCompressiveStrength = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["averageCompressiveStrengthValues"][masonryType][knowledgeMaterialLevel];
            form.setValue('averageCompressiveStrength', averageCompressiveStrength);
            const averageShearStrengthRegularMasonry = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["averageShearStrengthRegularMasonryValues"][masonryType][knowledgeMaterialLevel];
            form.setValue('averageShearStrengthRegularMasonry', averageShearStrengthRegularMasonry);
            const averageShearStrengthIrregularMasonry = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["averageShearStrengthIrregularMasonryValues"][masonryType][knowledgeMaterialLevel];
            form.setValue('averageShearStrengthIrregularMasonry', averageShearStrengthIrregularMasonry);
            const averageNormalElasticityModulus = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["averageNormalElasticityModulusValues"][masonryType][knowledgeMaterialLevel];
            form.setValue('averageNormalElasticityModulus', averageNormalElasticityModulus);
            const averageShearElasticityModulus = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["averageShearElasticityModulusValues"][masonryType][knowledgeMaterialLevel];
            form.setValue('averageShearElasticityModulus', averageShearElasticityModulus);
            const specificWeight = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["specificWeightValues"][masonryType];
            form.setValue('specificWeight', specificWeight);
            const correctionCoefficient = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["correctiveCoefficientValues"][enhancementCharacteristics][masonryType];
            form.setValue('correctionCoefficient', correctionCoefficient);
            // amplified values:
            form.setValue('amplifiedAverageCompressiveStrength', averageCompressiveStrength * correctionCoefficient);
            form.setValue('amplifiedAverageShearStrengthRegularMasonry', averageShearStrengthRegularMasonry * correctionCoefficient);
            form.setValue('amplifiedAverageShearStrengthIrregularMasonry', averageShearStrengthIrregularMasonry * correctionCoefficient);
            form.setValue('amplifiedAverageNormalElasticityModulus', averageNormalElasticityModulus * correctionCoefficient);
            form.setValue('amplifiedAverageShearElasticityModulus', averageShearElasticityModulus * correctionCoefficient);
        }
    }["ExistingMasonryCharacteristicsParamsForm.useEffect"], [
        knowledgeMaterialLevel,
        masonryType,
        enhancementCharacteristics,
        form
    ]);
    const handleFormSubmit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ExistingMasonryCharacteristicsParamsForm.useCallback[handleFormSubmit]": (body)=>{
            var _module_preIntervention;
            var _module_preIntervention_designStrengthPreInterventionCalculationResult, _module_postIntervention;
            const updatedModule = {
                ...module,
                preIntervention: {
                    existingMasonryCharacteristicsParams: body,
                    designStrengthPreInterventionCalculationResult: (_module_preIntervention_designStrengthPreInterventionCalculationResult = (_module_preIntervention = module.preIntervention) === null || _module_preIntervention === void 0 ? void 0 : _module_preIntervention.designStrengthPreInterventionCalculationResult) !== null && _module_preIntervention_designStrengthPreInterventionCalculationResult !== void 0 ? _module_preIntervention_designStrengthPreInterventionCalculationResult : undefined
                },
                postIntervention: (_module_postIntervention = module === null || module === void 0 ? void 0 : module.postIntervention) !== null && _module_postIntervention !== void 0 ? _module_postIntervention : undefined
            };
            mutate({
                projectId,
                moduleId,
                body: updatedModule
            });
        }
    }["ExistingMasonryCharacteristicsParamsForm.useCallback[handleFormSubmit]"], [
        mutate,
        projectId,
        moduleId,
        module
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Form"], {
        ...form,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            className: "space-y-4 rounded-md border p-4",
            onSubmit: form.handleSubmit(handleFormSubmit),
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    src: "/assets/masonry-crm/crm-ante-intervento.jpg",
                    alt: "site characteristics",
                    height: 250,
                    width: 500,
                    className: "mx-auto rounded-md object-contain",
                    priority: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx",
                    lineNumber: 228,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "panelWidth",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx",
                    lineNumber: 236,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "panelThickness",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx",
                    lineNumber: 237,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "panelHeight",
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx",
                    lineNumber: 238,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "masonryType",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_CRM_MASONRY_TYPE"],
                    optionLabelFn: (p)=>t("masonryType.".concat(p)),
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx",
                    lineNumber: 239,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "knowledgeLevel",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_MATERIAL_KNOWLEDGE_LEVEL"],
                    optionLabelFn: (p)=>t("knowledgeLevel.".concat(p)),
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx",
                    lineNumber: 246,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "confidenceFactor",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx",
                    lineNumber: 253,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "averageCompressiveStrength",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx",
                    lineNumber: 260,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "averageShearStrengthRegularMasonry",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx",
                    lineNumber: 267,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "averageShearStrengthIrregularMasonry",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx",
                    lineNumber: 274,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "averageNormalElasticityModulus",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx",
                    lineNumber: 281,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "averageShearElasticityModulus",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx",
                    lineNumber: 288,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "specificWeight",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx",
                    lineNumber: 295,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$fixed$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SelectFormFixedInput"], {
                    control: form.control,
                    name: "enhancementCharacteristics",
                    options: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MODULE_ENHANCEMENT_CHARACTERISTICS"],
                    optionLabelFn: (p)=>t("enhancementCharacteristics.".concat(p)),
                    t: t
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx",
                    lineNumber: 302,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "correctionCoefficient",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx",
                    lineNumber: 309,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "amplifiedAverageCompressiveStrength",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx",
                    lineNumber: 315,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "amplifiedAverageShearStrengthRegularMasonry",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx",
                    lineNumber: 321,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "amplifiedAverageShearStrengthIrregularMasonry",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx",
                    lineNumber: 327,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "amplifiedAverageNormalElasticityModulus",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx",
                    lineNumber: 333,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "amplifiedAverageShearElasticityModulus",
                    t: t,
                    disabled: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx",
                    lineNumber: 339,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Button"], {
                    type: "submit",
                    className: "w-full sm:w-auto",
                    disabled: isPending,
                    onClick: form.handleSubmit(handleFormSubmit),
                    children: [
                        isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                            className: "animate-spin"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx",
                            lineNumber: 352,
                            columnNumber: 25
                        }, ("TURBOPACK compile-time value", void 0)),
                        tCommon('save')
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx",
                    lineNumber: 346,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx",
            lineNumber: 224,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx",
        lineNumber: 223,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(ExistingMasonryCharacteristicsParamsForm, "DwT9Ft7cYVTNSvwpcnZpHVbBj1M=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$tanstack$2f$react$2d$query$2f$build$2f$modern$2f$QueryClientProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useQueryClient"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useForm"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"]
    ];
});
_c = ExistingMasonryCharacteristicsParamsForm;
var _c;
__turbopack_context__.k.register(_c, "ExistingMasonryCharacteristicsParamsForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
"[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/crm-params-form.tsx [app-client] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "CrmParamsForm",
    ()=>CrmParamsForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/accordion.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/tabs.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$crm$2f$sections$2f$post$2d$intervention$2f$crm$2d$post$2d$intervention$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$crm$2f$sections$2f$pre$2d$intervention$2f$design$2d$strength$2d$pre$2d$intervention$2d$calculation$2d$result$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-form.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$crm$2f$sections$2f$pre$2d$intervention$2f$existing$2d$masonry$2d$characteristics$2d$params$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
;
;
;
;
;
;
;
const CrmParamsForm = (param)=>{
    let { session, projectId, module } = param;
    var _module_preIntervention, _module_preIntervention1;
    _s();
    const [openItems, setOpenItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([
        '0'
    ]);
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.crm');
    const [activeTab, setActiveTab] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])('preIntervention');
    const handleItemSaved = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CrmParamsForm.useCallback[handleItemSaved]": (id)=>{
            if (id === '1') {
                setActiveTab('postIntervention');
                return;
            }
            const nextId = String(Number(id) + 1);
            setOpenItems({
                "CrmParamsForm.useCallback[handleItemSaved]": (old)=>{
                    const temp = old.filter({
                        "CrmParamsForm.useCallback[handleItemSaved].temp": (v)=>v !== id
                    }["CrmParamsForm.useCallback[handleItemSaved].temp"]);
                    return old.includes(nextId) ? temp : [
                        ...temp,
                        nextId
                    ];
                }
            }["CrmParamsForm.useCallback[handleItemSaved]"]);
        }
    }["CrmParamsForm.useCallback[handleItemSaved]"], []);
    const isPreInterventionValid = !!(module === null || module === void 0 ? void 0 : (_module_preIntervention = module.preIntervention) === null || _module_preIntervention === void 0 ? void 0 : _module_preIntervention.designStrengthPreInterventionCalculationResult) && !!(module === null || module === void 0 ? void 0 : (_module_preIntervention1 = module.preIntervention) === null || _module_preIntervention1 === void 0 ? void 0 : _module_preIntervention1.existingMasonryCharacteristicsParams);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Tabs"], {
            defaultValue: "preIntervention",
            value: activeTab,
            onValueChange: setActiveTab,
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsList"], {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                            value: "preIntervention",
                            children: t('crmPreIntervention.title')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/crm-params-form.tsx",
                            lineNumber: 59,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                            value: "postIntervention",
                            disabled: !isPreInterventionValid,
                            children: t('crmPostIntervention.title')
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/crm-params-form.tsx",
                            lineNumber: 62,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/crm-params-form.tsx",
                    lineNumber: 58,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                    value: "preIntervention",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Accordion"], {
                        type: "multiple",
                        value: openItems,
                        onValueChange: setOpenItems,
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionItem"], {
                                value: "0",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "text-lg font-medium",
                                            children: t('crmPreIntervention.existingMasonryCharacteristicsParams.title')
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/crm-params-form.tsx",
                                            lineNumber: 77,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/crm-params-form.tsx",
                                        lineNumber: 76,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionContent"], {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$crm$2f$sections$2f$pre$2d$intervention$2f$existing$2d$masonry$2d$characteristics$2d$params$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ExistingMasonryCharacteristicsParamsForm"], {
                                            session: session,
                                            projectId: projectId,
                                            module: module,
                                            onSave: ()=>handleItemSaved('0')
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/crm-params-form.tsx",
                                            lineNumber: 84,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/crm-params-form.tsx",
                                        lineNumber: 83,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/crm-params-form.tsx",
                                lineNumber: 75,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionItem"], {
                                value: "1",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "text-lg font-medium",
                                            children: t('crmPreIntervention.designStrengthPreInterventionCalculationResult.title')
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/crm-params-form.tsx",
                                            lineNumber: 94,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/crm-params-form.tsx",
                                        lineNumber: 93,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AccordionContent"], {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$crm$2f$sections$2f$pre$2d$intervention$2f$design$2d$strength$2d$pre$2d$intervention$2d$calculation$2d$result$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DesignStrengthPreInterventionCalculationResultForm"], {
                                            session: session,
                                            projectId: projectId,
                                            module: module,
                                            onSave: ()=>handleItemSaved('1')
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/crm-params-form.tsx",
                                            lineNumber: 101,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/crm-params-form.tsx",
                                        lineNumber: 100,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/crm-params-form.tsx",
                                lineNumber: 92,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/crm-params-form.tsx",
                        lineNumber: 70,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/crm-params-form.tsx",
                    lineNumber: 69,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$tabs$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TabsContent"], {
                    value: "postIntervention",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$masonry$2f$crm$2f$sections$2f$post$2d$intervention$2f$crm$2d$post$2d$intervention$2d$form$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CrmPostInterventionForm"], {
                        session: session,
                        projectId: projectId,
                        module: module
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/crm-params-form.tsx",
                        lineNumber: 112,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/crm-params-form.tsx",
                    lineNumber: 111,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/crm-params-form.tsx",
            lineNumber: 53,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/masonry/crm/crm-params-form.tsx",
        lineNumber: 52,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(CrmParamsForm, "Z0sWJvnTOisowqRwEkXD+dySrmk=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"]
    ];
});
_c = CrmParamsForm;
var _c;
__turbopack_context__.k.register(_c, "CrmParamsForm");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(__turbopack_context__.m, globalThis.$RefreshHelpers$);
}
}),
]);

//# sourceMappingURL=src_components_common_atlas_module-detail_params-forms_masonry_crm_44835528._.js.map