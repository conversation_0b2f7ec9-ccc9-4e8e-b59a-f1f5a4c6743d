import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from '@atlas/components/ui/card'
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>Content,
  <PERSON><PERSON>List,
  TabsTrigger,
} from '@atlas/components/ui/tabs'
import type { MaschiMurariShearReinforcementCalculationResultSchema } from '@atlas/types/schemas/masonry/maschi-murari-form'
import { useLocale, useTranslations } from 'next-intl'
import { ShearNonReinforcedSectionResultCard } from './maschi-murari-shear-nonreinforced-section-result-card'
import { ShearReinforcedSectionResultCard } from './maschi-murari-shear-reinforced-section-result-card'

type Props = {
  shearReinforcementCalculationResult: MaschiMurariShearReinforcementCalculationResultSchema
}

export function MaschiMurariReinforcementShearCalculationResult({
  shearReinforcementCalculationResult,
}: Props) {
  const t = useTranslations(
    'components.calculations.maschi-murari.shearReinforcementCalculationResult',
  )
  const _locale = useLocale()

  return (
    <>
      <h3 className="text-lg font-medium py-4">{t('title')}</h3>
      <Card>
        <CardHeader>
          <CardTitle>{t('title')}</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Tabs defaultValue="nonReinforcedSection">
            <TabsList>
              <TabsTrigger value="nonReinforcedSection">
                {t('shearReinforcedSectionResult.label')}
              </TabsTrigger>
              <TabsTrigger value="reinforcedSection">
                {t('shearReinforcedSectionResult.label')}
              </TabsTrigger>
            </TabsList>
            <TabsContent value="nonReinforcedSection">
              <ShearNonReinforcedSectionResultCard
                nonReinforcedSectionShearResults={
                  shearReinforcementCalculationResult?.inPlaneShearCheck
                    ?.nonReinforcedSection
                }
              />
            </TabsContent>
            <TabsContent value="reinforcedSection">
              <ShearReinforcedSectionResultCard
                reinforcedSectionShearResults={
                  shearReinforcementCalculationResult?.inPlaneShearCheck
                    ?.reinforcedSection
                }
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </>
  )
}
