self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"0044f9ee86f66c90fe7b1aeac96677d3571a4ae54e\": {\n      \"workers\": {\n        \"app/[locale]/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/components/common/atlas/login-form.tsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false,\n          \"exportedName\": \"$$RSC_SERVER_ACTION_0\",\n          \"filename\": \"src/components/common/atlas/login-form.tsx\"\n        }\n      },\n      \"layer\": {\n        \"app/[locale]/page\": \"rsc\"\n      },\n      \"filename\": \"src/components/common/atlas/login-form.tsx\",\n      \"exportedName\": \"$$RSC_SERVER_ACTION_0\"\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"I6RcvEX/7jg239u70bwr/neXvphypWgYq6I/3D6/niM=\"\n}"