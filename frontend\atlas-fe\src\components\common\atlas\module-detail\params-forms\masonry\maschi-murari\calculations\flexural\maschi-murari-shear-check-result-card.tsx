import { Badge } from '@atlas/components/ui/badge'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@atlas/components/ui/card'
import { cn } from '@atlas/lib/utils'
import type { ShearCheckSchema } from '@atlas/types/schemas/masonry/maschi-murari-form'
import { useLocale, useTranslations } from 'next-intl'

type Props = {
  shearCheckResults?: ShearCheckSchema
}

export function ShearCheckResultCard({ shearCheckResults }: Props) {
  const {
    appliedDesignSpecificShearForce,
    averageNormalStress,
    resultantCompressiveForceMasonry,
    resultantTensileForceFrcm,
    check,
  } = shearCheckResults || {}

  const t = useTranslations(
    'components.calculations.maschi-murari.flexuralReinforcementCalculationResult.shearCheck',
  )

  const locale = useLocale()

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('title')}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <span className="font-medium">
            {t('appliedDesignSpecificShearForce.label')}:
          </span>{' '}
          <span>
            {appliedDesignSpecificShearForce?.toLocaleString(locale, {
              maximumFractionDigits: 5,
            })}{' '}
          </span>
        </div>
        <div>
          <span className="font-medium">{t('averageNormalStress.label')}:</span>{' '}
          <span>
            {averageNormalStress?.toLocaleString(locale, {
              maximumFractionDigits: 5,
            })}{' '}
          </span>
        </div>
        <div>
          <span className="font-medium">
            {t('resultantCompressiveForceMasonry.label')}:
          </span>{' '}
          <span>
            {resultantCompressiveForceMasonry?.toLocaleString(locale, {
              maximumFractionDigits: 5,
            })}{' '}
          </span>
        </div>
        <div>
          <span className="font-medium">
            {t('resultantTensileForceFrcm.label')}:
          </span>{' '}
          <span>
            {resultantTensileForceFrcm?.toLocaleString(locale, {
              maximumFractionDigits: 5,
            })}{' '}
          </span>
        </div>
        <span className="font-medium">{t('check.label')}:</span>{' '}
        <Badge
          className={cn(
            'text-base px-3 py-1',
            check ? 'bg-green-600' : 'bg-red-600',
          )}
        >
          {check ? t('check.satisfied') : t('check.notSatisfied')}
        </Badge>
      </CardContent>
    </Card>
  )
}
