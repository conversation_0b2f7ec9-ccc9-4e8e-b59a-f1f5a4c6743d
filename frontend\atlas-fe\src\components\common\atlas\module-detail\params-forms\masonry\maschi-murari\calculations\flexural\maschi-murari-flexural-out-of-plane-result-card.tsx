import { Badge } from '@atlas/components/ui/badge'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@atlas/components/ui/card'
import {
  Ta<PERSON>,
  TabsContent,
  Ta<PERSON>List,
  TabsTrigger,
} from '@atlas/components/ui/tabs'
import { cn } from '@atlas/lib/utils'
import type { OutOfPlaneFlexuralCheckSchema } from '@atlas/types/schemas/masonry/maschi-murari-form'
import { Separator } from '@radix-ui/react-separator'
import { useLocale, useTranslations } from 'next-intl'

type Props = {
  outOfPlaneFlexuralCheckSchema?: OutOfPlaneFlexuralCheckSchema
}

export function OutOfPlaneFlexuralCheckResultCard({
  outOfPlaneFlexuralCheckSchema,
}: Props) {
  const { nonReinforcedSection, reinforcedSection } =
    outOfPlaneFlexuralCheckSchema || {}

  const t = useTranslations(
    'components.calculations.maschi-murari.flexuralReinforcementCalculationResult.outOfPlaneFlexuralCheck',
  )

  const locale = useLocale()

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('title')}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <h2>{t('nonReinforcedSection.title')}</h2>
        <div>
          <span className="font-medium">
            {t('nonReinforcedSection.appliedDesignBendingMoment.label')}:
          </span>{' '}
          <span>
            {nonReinforcedSection?.appliedDesignBendingMoment.toLocaleString(
              locale,
              {
                maximumFractionDigits: 5,
              },
            )}{' '}
          </span>
        </div>
        <div>
          <span className="font-medium">
            {t('nonReinforcedSection.neutralAxisCompressedEdgeDistance.label')}:
          </span>{' '}
          <span>
            {nonReinforcedSection?.neutralAxisCompressedEdgeDistance.toLocaleString(
              locale,
              {
                maximumFractionDigits: 5,
              },
            )}{' '}
          </span>
        </div>
        <div>
          <span className="font-medium">
            {t('nonReinforcedSection.resultantCompressiveForceMasonry.label')}:
          </span>{' '}
          <span>
            {nonReinforcedSection?.resultantCompressiveForceMasonry.toLocaleString(
              locale,
              {
                maximumFractionDigits: 5,
              },
            )}{' '}
          </span>
        </div>
        <div>
          <span className="font-medium">
            {t('nonReinforcedSection.designBendingMoment.label')}:
          </span>{' '}
          <span>
            {nonReinforcedSection?.designBendingMoment.toLocaleString(locale, {
              maximumFractionDigits: 5,
            })}{' '}
          </span>
        </div>
        <span className="font-medium">
          {t('nonReinforcedSection.check.label')}:
        </span>{' '}
        <Badge
          className={cn(
            'text-base px-3 py-1',
            nonReinforcedSection?.check ? 'bg-green-600' : 'bg-red-600',
          )}
        >
          {nonReinforcedSection?.check
            ? t('nonReinforcedSection.check.satisfied')
            : t('nonReinforcedSection.check.notSatisfied')}
        </Badge>
        <Separator />
        <h2>{t('reinforcedSection.title')}</h2>
        <Tabs defaultValue="regionHypothesisOne">
          <TabsList>
            <TabsTrigger value="regionHypothesisOne">
              {t('reinforcedSection.regionHypothesisOne.label')}
            </TabsTrigger>
            <TabsTrigger value="regionHypothesisTwo">
              {t('reinforcedSection.regionHypothesisTwo.label')}
            </TabsTrigger>
          </TabsList>
          <TabsContent value="regionHypothesisOne">
            {renderRegionHypothese(
              reinforcedSection?.regionHypothesisOne,
              'regionHypothesisOne',
            )}
          </TabsContent>
          <TabsContent value="regionHypothesisTwo">
            {renderRegionHypothese(
              reinforcedSection?.regionHypothesisTwo,
              'regionHypothesisTwo',
            )}
          </TabsContent>
        </Tabs>
        <div>
          <span className="font-medium">
            {t('reinforcedSection.momentCapacity.label')}:
          </span>{' '}
          <span>
            {reinforcedSection?.momentCapacity.toLocaleString(locale, {
              maximumFractionDigits: 5,
            })}{' '}
          </span>
        </div>
        <span className="font-medium">
          {t('reinforcedSection.check.label')}:
        </span>{' '}
        <Badge
          className={cn(
            'text-base px-3 py-1',
            reinforcedSection?.check ? 'bg-green-600' : 'bg-red-600',
          )}
        >
          {reinforcedSection?.check
            ? t('reinforcedSection.check.satisfied')
            : t('reinforcedSection.check.notSatisfied')}
        </Badge>
      </CardContent>
    </Card>
  )

  function renderRegionHypothese(regionHypothesis: any, labelKey: string) {
    if (!regionHypothesis) {
      return null
    }
    return (
      <>
        <h2>{t(`reinforcedSection.${labelKey}.label`)}</h2>
        <div>
          <span className="font-medium">
            {t(
              'reinforcedSection.regionHypothesis.neutralAxisCompressedEdgeDistance.label',
            )}
            :
          </span>{' '}
          <span>
            {regionHypothesis.neutralAxisCompressedEdgeDistance?.toLocaleString(
              locale,
              {
                maximumFractionDigits: 5,
              },
            )}{' '}
          </span>
        </div>
        <div>
          <span className="font-medium">
            {t(
              `reinforcedSection.${labelKey}.reinforcementOrMasonryStrain.label`,
            )}
            :
          </span>{' '}
          <span>
            {regionHypothesis.reinforcementOrMasonryStrain?.toLocaleString(
              locale,
              {
                maximumFractionDigits: 5,
              },
            )}{' '}
          </span>
        </div>
        <div>
          <span className="font-medium">
            {t(
              'reinforcedSection.regionHypothesis.resultantCompressiveForceMasonry.label',
            )}
            :
          </span>{' '}
          <span>
            {regionHypothesis.resultantCompressiveForceMasonry?.toLocaleString(
              locale,
              {
                maximumFractionDigits: 5,
              },
            )}{' '}
          </span>
        </div>
        <div>
          <span className="font-medium">
            {t(
              'reinforcedSection.regionHypothesis.resultantTensileForceFrcm.label',
            )}
            :
          </span>{' '}
          <span>
            {regionHypothesis.resultantTensileForceFrcm?.toLocaleString(
              locale,
              {
                maximumFractionDigits: 5,
              },
            )}{' '}
          </span>
        </div>
        <div>
          <span className="font-medium">
            {t(
              'reinforcedSection.regionHypothesis.designBendingMomentReinforcedSection.label',
            )}
            :
          </span>{' '}
          <span>
            {regionHypothesis.designBendingMomentReinforcedSection?.toLocaleString(
              locale,
              {
                maximumFractionDigits: 5,
              },
            )}{' '}
          </span>
        </div>
        <span className="font-medium">
          {t(`reinforcedSection.${labelKey}.check.label`)}:
        </span>{' '}
        <span className={cn('text-base px-3 py-1')}>
          {regionHypothesis.check
            ? t(`reinforcedSection.${labelKey}.check.satisfied`)
            : t(`reinforcedSection.${labelKey}.check.notSatisfied`)}
        </span>
      </>
    )
  }
}
