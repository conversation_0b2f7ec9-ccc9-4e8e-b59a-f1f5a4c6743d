import { SlabFlexuralNegativeCalculation } from '@atlas/components/common/atlas/module-detail/params-forms/slab/calculations/slab-flexural-negative-calculation'
import { SlabFlexuralPositiveCalculation } from '@atlas/components/common/atlas/module-detail/params-forms/slab/calculations/slab-flexural-positive-calculation'
import { SlabInterfaceSlipCalculation } from '@atlas/components/common/atlas/module-detail/params-forms/slab/calculations/slab-interface-slip-calculation'
import { SlabShearCalculation } from '@atlas/components/common/atlas/module-detail/params-forms/slab/calculations/slab-shear-calculation'
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from '@atlas/components/ui/tabs'
import type { ModuleWithParamsSlab } from '@atlas/lib/api/modules/schemas/modules'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'

type Props = {
  module: ModuleWithParamsSlab
  session: Session
  projectId: string
}

export const SlabCalculations = ({ module, session, projectId }: Props) => {
  const t = useTranslations('forms.calculations.slab')

  return (
    <>
      <h3 className="text-lg font-medium py-4">{t('title')}</h3>
      <Tabs defaultValue="flexural-positive">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="flexural-positive">
            {t('flexural-positive.label')}
          </TabsTrigger>
          <TabsTrigger value="flexural-negative">
            {t('flexural-negative.label')}
          </TabsTrigger>
          <TabsTrigger value="shear">{t('shear.label')}</TabsTrigger>
          <TabsTrigger value="interface-slip">
            {t('interface-slip.label')}
          </TabsTrigger>
        </TabsList>
        <TabsContent value="flexural-positive">
          <SlabFlexuralPositiveCalculation
            session={session}
            module={module}
            projectId={projectId}
          />
        </TabsContent>
        <TabsContent value="flexural-negative">
          <SlabFlexuralNegativeCalculation
            session={session}
            module={module}
            projectId={projectId}
          />
        </TabsContent>
        <TabsContent value="shear">
          <SlabShearCalculation
            session={session}
            module={module}
            projectId={projectId}
          />
        </TabsContent>
        <TabsContent value="interface-slip">
          <SlabInterfaceSlipCalculation
            session={session}
            module={module}
            projectId={projectId}
          />
        </TabsContent>
      </Tabs>
    </>
  )
}
