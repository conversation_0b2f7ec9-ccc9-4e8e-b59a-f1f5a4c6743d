import {
  Accordion as AccordionComponent,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@atlas/components/ui/accordion'
import type {
  Module,
  ModuleWithParamsMaschiMurari,
} from '@atlas/lib/api/modules/schemas/modules'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import type { MaschiMurariParamsSchemaInputs } from '@atlas/types/schemas/masonry/maschi-murari-form'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useCallback, useState } from 'react'
import { MaschiMurariFlexuralCalculation } from './calculations/flexural/maschi-murari-reinforcement-flexural-calculation'
import { MaschiMurariShearCalculation } from './calculations/shear/maschi-murari-reinforcement-shear-calculation'
import { MaschiMurariActionsForm } from './sections/maschi-murari-actions-form'
import { MaschiMurariMaterialPropertiesForm } from './sections/maschi-murari-material-properties-form'
import { MaschiMurariPanelGeometryForm } from './sections/maschi-murari-panel-geometry-form'

type Props = {
  session: Session
  projectId: Project['id']
  moduleId: Module['id']
  module: ModuleWithParamsMaschiMurari
}

export const MaschiMurariParamsForm = ({
  session,
  projectId,
  moduleId,
  module,
}: Props) => {
  const [params, setParams] = useState<MaschiMurariParamsSchemaInputs>(
    module?.params ?? ({} as MaschiMurariParamsSchemaInputs),
  )

  const [openItems, setOpenItems] = useState(['0'])
  const t = useTranslations('forms.project-params.maschi-murari')

  const handleItemSaved = useCallback((id: string) => {
    const nextId = String(Number(id) + 1)
    setOpenItems(old => {
      const temp = old.filter(v => v !== id)
      return old.includes(nextId) ? temp : [...temp, nextId]
    })
  }, [])

  return (
    <div>
      <AccordionComponent
        type="multiple"
        value={openItems}
        onValueChange={setOpenItems}
      >
        <AccordionItem value="0">
          <AccordionTrigger>
            <h3 className="text-lg font-medium">{t('panel-geometry.title')}</h3>
          </AccordionTrigger>
          <AccordionContent>
            <MaschiMurariPanelGeometryForm
              session={session}
              projectId={projectId}
              moduleId={moduleId}
              defaultValues={params?.panelGeometry}
              setParams={setParams}
              params={params}
              onSave={() => handleItemSaved('0')}
            />
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="1">
          <AccordionTrigger>
            <h3 className="text-lg font-medium">
              {t('material-properties.title')}
            </h3>
          </AccordionTrigger>
          <AccordionContent>
            <MaschiMurariMaterialPropertiesForm
              session={session}
              projectId={projectId}
              moduleId={moduleId}
              defaultValues={params?.materialProperties}
              setParams={setParams}
              params={params}
              onSave={() => handleItemSaved('1')}
            />
          </AccordionContent>
        </AccordionItem>
        <AccordionItem value="2">
          <AccordionTrigger>
            <h3 className="text-lg font-medium">{t('actions.title')}</h3>
          </AccordionTrigger>
          <AccordionContent>
            <MaschiMurariActionsForm
              session={session}
              projectId={projectId}
              moduleId={moduleId}
              defaultValues={params.actions}
              params={params}
              setParams={setParams}
              onSave={() => handleItemSaved('2')}
            />
          </AccordionContent>
        </AccordionItem>
        <AccordionItem
          value="3"
          disabled={
            !module?.params?.panelGeometry ||
            !module?.params?.materialProperties ||
            !module?.params?.actions
          }
        >
          <AccordionTrigger>
            <h3 className="text-lg font-medium">{t('shear.title')}</h3>
          </AccordionTrigger>
          <AccordionContent>
            <MaschiMurariShearCalculation
              session={session}
              projectId={projectId}
              module={module}
              onNext={() => handleItemSaved('3')}
            />
          </AccordionContent>
        </AccordionItem>
        <AccordionItem
          value="4"
          disabled={
            !module?.params?.panelGeometry ||
            !module?.params?.materialProperties ||
            !module?.params?.actions
          }
        >
          <AccordionTrigger>
            <h3 className="text-lg font-medium">{t('flexural.title')}</h3>
          </AccordionTrigger>
          <AccordionContent>
            <MaschiMurariFlexuralCalculation
              session={session}
              projectId={projectId}
              module={module}
            />
          </AccordionContent>
        </AccordionItem>
      </AccordionComponent>
    </div>
  )
}
