import { Badge } from '@atlas/components/ui/badge'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@atlas/components/ui/card'
import {
  Ta<PERSON>,
  TabsContent,
  Ta<PERSON>List,
  TabsTrigger,
} from '@atlas/components/ui/tabs'
import { cn } from '@atlas/lib/utils'
import type { InPlaneFlexuralCheckSchema } from '@atlas/types/schemas/masonry/maschi-murari-form'
import { Separator } from '@radix-ui/react-separator'
import { useLocale, useTranslations } from 'next-intl'

type Props = {
  inPlaneFlexuralCheckSchema?: InPlaneFlexuralCheckSchema
}

export function InPlaneFlexuralCheckResultCard({
  inPlaneFlexuralCheckSchema,
}: Props) {
  const { nonReinforcedSection, reinforcedSection } =
    inPlaneFlexuralCheckSchema || {}

  const t = useTranslations(
    'components.calculations.maschi-murari.flexuralReinforcementCalculationResult.inPlaneFlexuralCheck',
  )

  const locale = useLocale()

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('title')}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <h2>{t('nonReinforcedSection.title')}</h2>
        <div>
          <span className="font-medium">
            {t('nonReinforcedSection.inPlaneBendingMoment.label')}:
          </span>{' '}
          <span>
            {nonReinforcedSection?.inPlaneBendingMoment.toLocaleString(locale, {
              maximumFractionDigits: 5,
            })}{' '}
          </span>
        </div>
        <div>
          <span className="font-medium">
            {t('nonReinforcedSection.neutralAxisCompressedEdgeDistance.label')}:
          </span>{' '}
          <span>
            {nonReinforcedSection?.neutralAxisCompressedEdgeDistance.toLocaleString(
              locale,
              {
                maximumFractionDigits: 5,
              },
            )}{' '}
          </span>
        </div>
        <div>
          <span className="font-medium">
            {t('nonReinforcedSection.inPlaneFlexuralCapacity.label')}:
          </span>{' '}
          <span>
            {nonReinforcedSection?.inPlaneFlexuralCapacity.toLocaleString(
              locale,
              {
                maximumFractionDigits: 5,
              },
            )}{' '}
          </span>
        </div>
        <span className="font-medium">
          {t('nonReinforcedSection.check.label')}:
        </span>{' '}
        <Badge
          className={cn(
            'text-base px-3 py-1',
            nonReinforcedSection?.check ? 'bg-green-600' : 'bg-red-600',
          )}
        >
          {nonReinforcedSection?.check
            ? t('nonReinforcedSection.check.satisfied')
            : t('nonReinforcedSection.check.notSatisfied')}
        </Badge>
        <Separator />
        <h2>{t('reinforcedSection.title')}</h2>
        <Tabs defaultValue="hypothesisOne">
          <TabsList>
            <TabsTrigger value="hypothesisOne">
              {t('reinforcedSection.hypothesisOne.label')}
            </TabsTrigger>
            <TabsTrigger value="hypothesisTwo">
              {t('reinforcedSection.hypothesisTwo.label')}
            </TabsTrigger>
            <TabsTrigger value="hypothesisThree">
              {t('reinforcedSection.hypothesisThree.label')}
            </TabsTrigger>
          </TabsList>
          <TabsContent value="hypothesisOne">
            {renderHypothesis(
              reinforcedSection?.hypothesisOne,
              'hypothesisOne',
            )}
          </TabsContent>
          <TabsContent value="hypothesisTwo">
            {renderHypothesis(
              reinforcedSection?.hypothesisTwo,
              'hypothesisTwo',
            )}
          </TabsContent>
          <TabsContent value="hypothesisThree">
            {renderHypothesis(
              reinforcedSection?.hypothesisThree,
              'hypothesisThree',
            )}
          </TabsContent>
        </Tabs>
        <div>
          <span className="font-medium">
            {t('reinforcedSection.momentCapacity.label')}:
          </span>{' '}
          <span>
            {reinforcedSection?.momentCapacity.toLocaleString(locale, {
              maximumFractionDigits: 5,
            })}{' '}
          </span>
        </div>
        <span className="font-medium">
          {t('reinforcedSection.check.label')}:
        </span>{' '}
        <Badge
          className={cn(
            'text-base px-3 py-1',
            reinforcedSection?.check ? 'bg-green-600' : 'bg-red-600',
          )}
        >
          {reinforcedSection?.check
            ? t('reinforcedSection.check.satisfied')
            : t('reinforcedSection.check.notSatisfied')}
        </Badge>
      </CardContent>
    </Card>
  )

  function renderHypothesis(regionHypothesis: any, labelKey: string) {
    if (!regionHypothesis) {
      return null
    }
    return (
      <>
        <h2>{t(`reinforcedSection.${labelKey}.label`)}</h2>
        <div>
          <span className="font-medium">
            {t(
              'reinforcedSection.hypothesis.neutralAxisCompressedEdgeDistance.label',
            )}
            :
          </span>{' '}
          <span>
            {regionHypothesis.neutralAxisCompressedEdgeDistance?.toLocaleString(
              locale,
              {
                maximumFractionDigits: 5,
              },
            )}{' '}
          </span>
        </div>
        <div>
          <span className="font-medium">
            {t(
              `reinforcedSection.${labelKey}.reinforcementOrMasonryStrain.label`,
            )}
            :
          </span>{' '}
          <span>
            {regionHypothesis.reinforcementOrMasonryStrain?.toLocaleString(
              locale,
              {
                maximumFractionDigits: 5,
              },
            )}{' '}
          </span>
        </div>
        <span className="font-medium">
          {t(`reinforcedSection.${labelKey}.check.label`)}:
        </span>{' '}
        <span className={cn('text-base px-3 py-1')}>
          {regionHypothesis.check
            ? t(`reinforcedSection.${labelKey}.check.satisfied`)
            : t(`reinforcedSection.${labelKey}.check.notSatisfied`)}
        </span>
      </>
    )
  }
}
