{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/form/string-form-input.tsx"], "sourcesContent": ["import {\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  useFormField,\r\n} from '@atlas/components/ui/form'\r\nimport { Input } from '@atlas/components/ui/input'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { Control, FieldPath, FieldValues } from 'react-hook-form'\r\n\r\ntype Props<T extends FieldValues> = {\r\n  control: Control<T>\r\n  description?: boolean\r\n  disabled?: boolean\r\n  name: FieldPath<T>\r\n  required?: boolean\r\n  t: (message: string) => string\r\n}\r\n\r\nexport const StringFormInput = <T extends FieldValues>({\r\n  control,\r\n  description = false,\r\n  disabled = false,\r\n  name,\r\n  required = false,\r\n  t,\r\n}: Props<T>) => {\r\n  const { error } = useFormField()\r\n\r\n  return (\r\n    <FormField\r\n      control={control}\r\n      name={name}\r\n      render={({ field }) => (\r\n        <FormItem>\r\n          <FormLabel>\r\n            {t(`${name}.label`)}\r\n            {required && <p className=\"text-red-600\">*</p>}\r\n          </FormLabel>\r\n          <FormControl>\r\n            <Input\r\n              placeholder={t(`${name}.placeholder`)}\r\n              disabled={disabled}\r\n              {...field}\r\n            />\r\n          </FormControl>\r\n          {description && (\r\n            <FormDescription>{t(`${name}.description`)}</FormDescription>\r\n          )}\r\n          {error?.message && (\r\n            <p className={cn('text-destructive text-sm')}>\r\n              {t(`error.${error}`)}\r\n            </p>\r\n          )}\r\n        </FormItem>\r\n      )}\r\n    />\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAQA;AACA;;;;;;AAYO,MAAM,kBAAkB;QAAwB,EACrD,OAAO,EACP,cAAc,KAAK,EACnB,WAAW,KAAK,EAChB,IAAI,EACJ,WAAW,KAAK,EAChB,CAAC,EACQ;;IACT,MAAM,EAAE,KAAK,EAAE,GAAG,IAAA,mJAAY;IAE9B,qBACE,6LAAC,gJAAS;QACR,SAAS;QACT,MAAM;QACN,QAAQ;gBAAC,EAAE,KAAK,EAAE;iCAChB,6LAAC,+IAAQ;;kCACP,6LAAC,gJAAS;;4BACP,EAAE,AAAC,GAAO,OAAL,MAAK;4BACV,0BAAY,6LAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;kCAE3C,6LAAC,kJAAW;kCACV,cAAA,6LAAC,6IAAK;4BACJ,aAAa,EAAE,AAAC,GAAO,OAAL,MAAK;4BACvB,UAAU;4BACT,GAAG,KAAK;;;;;;;;;;;oBAGZ,6BACC,6LAAC,sJAAe;kCAAE,EAAE,AAAC,GAAO,OAAL,MAAK;;;;;;oBAE7B,CAAA,kBAAA,4BAAA,MAAO,OAAO,mBACb,6LAAC;wBAAE,WAAW,IAAA,4HAAE,EAAC;kCACd,EAAE,AAAC,SAAc,OAAN;;;;;;;;;;;;;;;;;;AAO1B;GAvCa;;QAQO,mJAAY;;;KARnB", "debugId": null}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/form/number-form-input.tsx"], "sourcesContent": ["import {\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  useFormField,\r\n} from '@atlas/components/ui/form'\r\nimport { Input } from '@atlas/components/ui/input'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { Control, FieldPath, FieldValues } from 'react-hook-form'\r\n\r\ntype Props<T extends FieldValues> = {\r\n  control: Control<T>\r\n  disabled?: boolean\r\n  max?: number\r\n  min?: number\r\n  name: FieldPath<T>\r\n  required?: boolean\r\n  t: (message: string) => string\r\n  fieldContainerClassName?: string\r\n  decimalPlaces?: number // New prop for formatting display values\r\n}\r\n\r\nexport const NumberFormInput = <T extends FieldValues>({\r\n  control,\r\n  disabled = false,\r\n  max,\r\n  min,\r\n  name,\r\n  required = false,\r\n  t,\r\n  fieldContainerClassName = '',\r\n  decimalPlaces,\r\n}: Props<T>) => {\r\n  const { error } = useFormField()\r\n\r\n  return (\r\n    <FormField\r\n      control={control}\r\n      name={name}\r\n      render={({ field }) => (\r\n        <FormItem className={fieldContainerClassName}>\r\n          <FormLabel>\r\n            {t(`${name}.label`)}\r\n            {required && !disabled && (\r\n              <span className=\"text-red-600 ml-1\">*</span>\r\n            )}\r\n          </FormLabel>\r\n          <FormControl>\r\n            <Input\r\n              type=\"number\"\r\n              step=\"any\"\r\n              placeholder={t(`${name}.placeholder`)}\r\n              min={min}\r\n              max={max}\r\n              disabled={disabled}\r\n              {...field}\r\n              value={\r\n                disabled &&\r\n                decimalPlaces !== undefined &&\r\n                typeof field.value === 'number'\r\n                  ? field.value.toFixed(decimalPlaces)\r\n                  : field.value\r\n              }\r\n              onChange={e => field.onChange(e.target.valueAsNumber)}\r\n            />\r\n          </FormControl>\r\n          <FormDescription>{t(`${name}.description`)}</FormDescription>\r\n          {error?.message && (\r\n            <p className={cn('text-destructive text-sm')}>\r\n              {t(`error.${error}`)}\r\n            </p>\r\n          )}\r\n        </FormItem>\r\n      )}\r\n    />\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAQA;AACA;;;;;;AAeO,MAAM,kBAAkB;QAAwB,EACrD,OAAO,EACP,WAAW,KAAK,EAChB,GAAG,EACH,GAAG,EACH,IAAI,EACJ,WAAW,KAAK,EAChB,CAAC,EACD,0BAA0B,EAAE,EAC5B,aAAa,EACJ;;IACT,MAAM,EAAE,KAAK,EAAE,GAAG,IAAA,mJAAY;IAE9B,qBACE,6LAAC,gJAAS;QACR,SAAS;QACT,MAAM;QACN,QAAQ;gBAAC,EAAE,KAAK,EAAE;iCAChB,6LAAC,+IAAQ;gBAAC,WAAW;;kCACnB,6LAAC,gJAAS;;4BACP,EAAE,AAAC,GAAO,OAAL,MAAK;4BACV,YAAY,CAAC,0BACZ,6LAAC;gCAAK,WAAU;0CAAoB;;;;;;;;;;;;kCAGxC,6LAAC,kJAAW;kCACV,cAAA,6LAAC,6IAAK;4BACJ,MAAK;4BACL,MAAK;4BACL,aAAa,EAAE,AAAC,GAAO,OAAL,MAAK;4BACvB,KAAK;4BACL,KAAK;4BACL,UAAU;4BACT,GAAG,KAAK;4BACT,OACE,YACA,kBAAkB,aAClB,OAAO,MAAM,KAAK,KAAK,WACnB,MAAM,KAAK,CAAC,OAAO,CAAC,iBACpB,MAAM,KAAK;4BAEjB,UAAU,CAAA,IAAK,MAAM,QAAQ,CAAC,EAAE,MAAM,CAAC,aAAa;;;;;;;;;;;kCAGxD,6LAAC,sJAAe;kCAAE,EAAE,AAAC,GAAO,OAAL,MAAK;;;;;;oBAC3B,CAAA,kBAAA,4BAAA,MAAO,OAAO,mBACb,6LAAC;wBAAE,WAAW,IAAA,4HAAE,EAAC;kCACd,EAAE,AAAC,SAAc,OAAN;;;;;;;;;;;;;;;;;;AAO1B;GAtDa;;QAWO,mJAAY;;;KAXnB", "debugId": null}}, {"offset": {"line": 209, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/form/select-form-fixed-input.tsx"], "sourcesContent": ["import {\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  useFormField,\r\n} from '@atlas/components/ui/form'\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@atlas/components/ui/select'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { Control, FieldPath, FieldValues } from 'react-hook-form'\r\n\r\ntype Props<T extends FieldValues, N extends FieldPath<T>> = {\r\n  control: Control<T>\r\n  description?: boolean\r\n  name: N\r\n  options: readonly string[]\r\n  optionLabelFn: (option: string) => string\r\n  required?: boolean\r\n  t: (message: string) => string\r\n  disabled?: boolean\r\n}\r\n\r\nexport const SelectFormFixedInput = <\r\n  T extends FieldValues,\r\n  N extends FieldPath<T>,\r\n>({\r\n  control,\r\n  description = false,\r\n  name,\r\n  options,\r\n  optionLabelFn,\r\n  required = false,\r\n  t,\r\n  disabled = false,\r\n}: Props<T, N>) => {\r\n  const { error } = useFormField()\r\n\r\n  return (\r\n    <FormField\r\n      control={control}\r\n      name={name}\r\n      render={({ field }) => (\r\n        <FormItem>\r\n          <FormLabel>\r\n            {t(`${name}.label`)}\r\n            {required && <p className=\"text-red-600\">*</p>}\r\n          </FormLabel>\r\n          <FormControl>\r\n            <Select\r\n              {...field}\r\n              onValueChange={field.onChange}\r\n              disabled={disabled}\r\n            >\r\n              <SelectTrigger className=\"w-full\">\r\n                <SelectValue placeholder={t(`${name}.placeholder`)} />\r\n              </SelectTrigger>\r\n              <SelectContent>\r\n                {options.map(option => (\r\n                  <SelectItem value={option} key={option}>\r\n                    {optionLabelFn(option)}\r\n                  </SelectItem>\r\n                ))}\r\n              </SelectContent>\r\n            </Select>\r\n          </FormControl>\r\n          {description && (\r\n            <FormDescription>{t(`${name}.description`)}</FormDescription>\r\n          )}\r\n          {error?.message && (\r\n            <p className={cn('text-destructive text-sm')}>\r\n              {t(`error.${error}`)}\r\n            </p>\r\n          )}\r\n        </FormItem>\r\n      )}\r\n    />\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAQA;AAOA;;;;;;AAcO,MAAM,uBAAuB;QAGlC,EACA,OAAO,EACP,cAAc,KAAK,EACnB,IAAI,EACJ,OAAO,EACP,aAAa,EACb,WAAW,KAAK,EAChB,CAAC,EACD,WAAW,KAAK,EACJ;;IACZ,MAAM,EAAE,KAAK,EAAE,GAAG,IAAA,mJAAY;IAE9B,qBACE,6LAAC,gJAAS;QACR,SAAS;QACT,MAAM;QACN,QAAQ;gBAAC,EAAE,KAAK,EAAE;iCAChB,6LAAC,+IAAQ;;kCACP,6LAAC,gJAAS;;4BACP,EAAE,AAAC,GAAO,OAAL,MAAK;4BACV,0BAAY,6LAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;kCAE3C,6LAAC,kJAAW;kCACV,cAAA,6LAAC,+IAAM;4BACJ,GAAG,KAAK;4BACT,eAAe,MAAM,QAAQ;4BAC7B,UAAU;;8CAEV,6LAAC,sJAAa;oCAAC,WAAU;8CACvB,cAAA,6LAAC,oJAAW;wCAAC,aAAa,EAAE,AAAC,GAAO,OAAL,MAAK;;;;;;;;;;;8CAEtC,6LAAC,sJAAa;8CACX,QAAQ,GAAG,CAAC,CAAA,uBACX,6LAAC,mJAAU;4CAAC,OAAO;sDAChB,cAAc;2CADe;;;;;;;;;;;;;;;;;;;;;oBAOvC,6BACC,6LAAC,sJAAe;kCAAE,EAAE,AAAC,GAAO,OAAL,MAAK;;;;;;oBAE7B,CAAA,kBAAA,4BAAA,MAAO,OAAO,mBACb,6LAAC;wBAAE,WAAW,IAAA,4HAAE,EAAC;kCACd,EAAE,AAAC,SAAc,OAAN;;;;;;;;;;;;;;;;;;AAO1B;GAvDa;;QAaO,mJAAY;;;KAbnB", "debugId": null}}, {"offset": {"line": 338, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/form/select-form-input.tsx"], "sourcesContent": ["import { <PERSON>ert, AlertTitle } from '@atlas/components/ui/alert'\r\nimport {\r\n  FormControl,\r\n  FormDescription,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  useFormField,\r\n} from '@atlas/components/ui/form'\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@atlas/components/ui/select'\r\nimport { Skeleton } from '@atlas/components/ui/skeleton'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { Control, FieldPath, FieldValues } from 'react-hook-form'\r\n\r\nexport type SelectOption = {\r\n  label: string\r\n  value: string\r\n}\r\n\r\ntype Props<T extends FieldValues> = {\r\n  control: Control<T>\r\n  errorMessage?: string\r\n  loading?: boolean\r\n  name: FieldPath<T>\r\n  options: SelectOption[]\r\n  requestError?: boolean\r\n  required?: boolean\r\n  t: (message: string) => string\r\n}\r\n\r\nexport const SelectFormInput = <T extends FieldValues>({\r\n  control,\r\n  errorMessage,\r\n  loading = false,\r\n  name,\r\n  options,\r\n  requestError = false,\r\n  required = false,\r\n  t,\r\n}: Props<T>) => {\r\n  const { error } = useFormField()\r\n\r\n  return (\r\n    <FormField\r\n      control={control}\r\n      name={name}\r\n      render={({ field }) => (\r\n        <FormItem>\r\n          <FormLabel>\r\n            {t(`${name}.label`)}\r\n            {required && <p className=\"text-red-600\">*</p>}\r\n          </FormLabel>\r\n          {requestError && (\r\n            <Alert variant=\"destructive\">\r\n              <AlertTitle>{errorMessage ?? 'Error'}</AlertTitle>\r\n            </Alert>\r\n          )}\r\n          {loading && <Skeleton className=\"h-[30px] w-full\" />}\r\n          {!requestError && !loading && (\r\n            <FormControl>\r\n              <Select {...field} onValueChange={field.onChange}>\r\n                <SelectTrigger className=\"w-full\">\r\n                  <SelectValue placeholder={t(`${name}.placeholder`)} />\r\n                </SelectTrigger>\r\n                <SelectContent>\r\n                  {options.map(option => (\r\n                    <SelectItem value={option.value} key={option.value}>\r\n                      {option.label}\r\n                    </SelectItem>\r\n                  ))}\r\n                </SelectContent>\r\n              </Select>\r\n            </FormControl>\r\n          )}\r\n          <FormDescription>{t(`${name}.description`)}</FormDescription>\r\n          {error?.message && (\r\n            <p className={cn('text-destructive text-sm')}>\r\n              {t(`error.${error}`)}\r\n            </p>\r\n          )}\r\n        </FormItem>\r\n      )}\r\n    />\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAQA;AAOA;AACA;;;;;;;;AAmBO,MAAM,kBAAkB;QAAwB,EACrD,OAAO,EACP,YAAY,EACZ,UAAU,KAAK,EACf,IAAI,EACJ,OAAO,EACP,eAAe,KAAK,EACpB,WAAW,KAAK,EAChB,CAAC,EACQ;;IACT,MAAM,EAAE,KAAK,EAAE,GAAG,IAAA,mJAAY;IAE9B,qBACE,6LAAC,gJAAS;QACR,SAAS;QACT,MAAM;QACN,QAAQ;gBAAC,EAAE,KAAK,EAAE;iCAChB,6LAAC,+IAAQ;;kCACP,6LAAC,gJAAS;;4BACP,EAAE,AAAC,GAAO,OAAL,MAAK;4BACV,0BAAY,6LAAC;gCAAE,WAAU;0CAAe;;;;;;;;;;;;oBAE1C,8BACC,6LAAC,6IAAK;wBAAC,SAAQ;kCACb,cAAA,6LAAC,kJAAU;sCAAE,yBAAA,0BAAA,eAAgB;;;;;;;;;;;oBAGhC,yBAAW,6LAAC,mJAAQ;wBAAC,WAAU;;;;;;oBAC/B,CAAC,gBAAgB,CAAC,yBACjB,6LAAC,kJAAW;kCACV,cAAA,6LAAC,+IAAM;4BAAE,GAAG,KAAK;4BAAE,eAAe,MAAM,QAAQ;;8CAC9C,6LAAC,sJAAa;oCAAC,WAAU;8CACvB,cAAA,6LAAC,oJAAW;wCAAC,aAAa,EAAE,AAAC,GAAO,OAAL,MAAK;;;;;;;;;;;8CAEtC,6LAAC,sJAAa;8CACX,QAAQ,GAAG,CAAC,CAAA,uBACX,6LAAC,mJAAU;4CAAC,OAAO,OAAO,KAAK;sDAC5B,OAAO,KAAK;2CADuB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;kCAQ5D,6LAAC,sJAAe;kCAAE,EAAE,AAAC,GAAO,OAAL,MAAK;;;;;;oBAC3B,CAAA,kBAAA,4BAAA,MAAO,OAAO,mBACb,6LAAC;wBAAE,WAAW,IAAA,4HAAE,EAAC;kCACd,EAAE,AAAC,SAAc,OAAN;;;;;;;;;;;;;;;;;;AAO1B;GAtDa;;QAUO,mJAAY;;;KAVnB", "debugId": null}}, {"offset": {"line": 491, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/form/polarity-aware-number-form-input.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport type { ModulePolarity } from '@atlas/constants/module'\r\nimport type { Control, FieldPath, FieldValues } from 'react-hook-form'\r\n\r\ntype Props<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TN<PERSON> extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n> = {\r\n  control: Control<TFieldValues>\r\n  name: TName\r\n  t: (key: string, options?: any) => string\r\n  polarity?: ModulePolarity\r\n  disabled?: boolean\r\n}\r\n\r\nexport function PolarityAwareNumberFormInput<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\r\n>({ control, name, t, polarity, disabled }: Props<TFieldValues, TName>) {\r\n  // Get the base translations\r\n  const baseLabel = t(`${name}.label`)\r\n  const baseDescription = t(`${name}.description`)\r\n  const placeholder = t(`${name}.placeholder`)\r\n\r\n  // Get polarity-specific translations if polarity is provided\r\n  const polarityLabel = polarity\r\n    ? t(`${name}.labelPolarity.${polarity}`, { fallback: baseLabel })\r\n    : baseLabel\r\n\r\n  const polarityDescription = polarity\r\n    ? t(`${name}.descriptionPolarity.${polarity}`, {\r\n        fallback: baseDescription,\r\n      })\r\n    : baseDescription\r\n\r\n  // Create a custom t function that returns the polarity-aware values\r\n  const customT = (key: string) => {\r\n    if (key === `${name}.label`) {\r\n      return polarityLabel\r\n    }\r\n    if (key === `${name}.description`) {\r\n      return polarityDescription\r\n    }\r\n    if (key === `${name}.placeholder`) {\r\n      return placeholder\r\n    }\r\n    return t(key)\r\n  }\r\n\r\n  return (\r\n    <NumberFormInput\r\n      control={control}\r\n      name={name}\r\n      t={customT}\r\n      disabled={disabled}\r\n    />\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;;;AAeO,SAAS,6BAGd,KAAoE;QAApE,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,QAAQ,EAA8B,GAApE;IACA,4BAA4B;IAC5B,MAAM,YAAY,EAAE,AAAC,GAAO,OAAL,MAAK;IAC5B,MAAM,kBAAkB,EAAE,AAAC,GAAO,OAAL,MAAK;IAClC,MAAM,cAAc,EAAE,AAAC,GAAO,OAAL,MAAK;IAE9B,6DAA6D;IAC7D,MAAM,gBAAgB,WAClB,EAAE,AAAC,GAAwB,OAAtB,MAAK,mBAA0B,OAAT,WAAY;QAAE,UAAU;IAAU,KAC7D;IAEJ,MAAM,sBAAsB,WACxB,EAAE,AAAC,GAA8B,OAA5B,MAAK,yBAAgC,OAAT,WAAY;QAC3C,UAAU;IACZ,KACA;IAEJ,oEAAoE;IACpE,MAAM,UAAU,CAAC;QACf,IAAI,QAAQ,AAAC,GAAO,OAAL,MAAK,WAAS;YAC3B,OAAO;QACT;QACA,IAAI,QAAQ,AAAC,GAAO,OAAL,MAAK,iBAAe;YACjC,OAAO;QACT;QACA,IAAI,QAAQ,AAAC,GAAO,OAAL,MAAK,iBAAe;YACjC,OAAO;QACT;QACA,OAAO,EAAE;IACX;IAEA,qBACE,6LAAC,qLAAe;QACd,SAAS;QACT,MAAM;QACN,GAAG;QACH,UAAU;;;;;;AAGhB;KA1CgB", "debugId": null}}]}