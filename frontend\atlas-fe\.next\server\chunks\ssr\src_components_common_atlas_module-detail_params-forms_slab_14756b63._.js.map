{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/slab/sections/slab-frc-reinforcement-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { StringFormInput } from '@atlas/components/common/form/string-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport { useProductsByType } from '@atlas/lib/query/products/use-products-by-type'\r\nimport {\r\n  type SlabFrcReinforcementFormInputs as FormSchema,\r\n  slabFrcReinforcementSchema,\r\n} from '@atlas/types/schemas/slab-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect, useMemo } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FormSchema>\r\n  onSave: () => void\r\n}\r\n\r\nexport const SlabFrcReinforcementForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.slab.slabFrcReinforcement')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const tProduct = useTranslations('components.common.atlas.product')\r\n  const queryClient = useQueryClient()\r\n\r\n  const {\r\n    data: products,\r\n    isError: errorGettingProducts,\r\n    isLoading: isLoadingProducts,\r\n  } = useProductsByType(session, 'SLAB', 0, 100)\r\n\r\n  const customOption = { value: 'custom', label: t('product.custom') }\r\n\r\n  const productsOptions = [\r\n    ...(products?.content.map(m => ({\r\n      value: m.name ?? '',\r\n      label: m.name ?? t('product.unnamed'),\r\n    })) ?? []),\r\n    customOption,\r\n  ]\r\n\r\n  const form = useForm<FormSchema>({\r\n    resolver: zodResolver(slabFrcReinforcementSchema),\r\n    defaultValues: {\r\n      frcReinforcementType: defaultValues?.frcReinforcementType,\r\n      toughnessClass: defaultValues?.toughnessClass,\r\n      fiberType: defaultValues?.fiberType,\r\n      frcSlabThickness: defaultValues?.frcSlabThickness,\r\n      elasticModulus: defaultValues?.elasticModulus,\r\n      characteristicCylindricalCompressiveStrengthFrcMatrix:\r\n        defaultValues?.characteristicCylindricalCompressiveStrengthFrcMatrix,\r\n      frcCharacteristicTensileStrength:\r\n        defaultValues?.frcCharacteristicTensileStrength,\r\n      tensileStrength: defaultValues?.tensileStrength,\r\n      adhesionToConcrete: defaultValues?.adhesionToConcrete,\r\n      specificWeight: defaultValues?.specificWeight,\r\n    },\r\n  })\r\n\r\n  const [frcReinforcementType] = form.watch(['frcReinforcementType'])\r\n\r\n  const isCustomSelected = frcReinforcementType === 'custom'\r\n\r\n  const selectedProduct = useMemo(\r\n    () => products?.content.find(p => p.name === frcReinforcementType),\r\n    [frcReinforcementType, products],\r\n  )\r\n\r\n  // Auto-fill product properties when a product is selected\r\n  useEffect(() => {\r\n    if (isCustomSelected || !selectedProduct) {\r\n      return\r\n    }\r\n\r\n    // Format toughness class and fiber type\r\n    if (selectedProduct.toughnessClass || selectedProduct.fiberType) {\r\n      const parts = []\r\n      if (selectedProduct.toughnessClass) {\r\n        parts.push(`${t('toughnessClass')}: ${selectedProduct.toughnessClass}`)\r\n      }\r\n      if (selectedProduct.fiberType) {\r\n        const fiberTypeLabel = tProduct(selectedProduct.fiberType)\r\n        parts.push(`${t('fiberType')}: ${fiberTypeLabel}`)\r\n      }\r\n      form.setValue('toughnessClass', selectedProduct.toughnessClass)\r\n      form.setValue('fiberType', selectedProduct.fiberType)\r\n      form.setValue('toughnessClassAndFibersType', parts.join(', '))\r\n    }\r\n\r\n    // Populate elastic modulus\r\n    if (selectedProduct.elasticModulus !== undefined) {\r\n      form.setValue('elasticModulus', selectedProduct.elasticModulus)\r\n    }\r\n\r\n    // Populate cylindric compressive strength\r\n    if (selectedProduct.cylindricCompressiveStrength !== undefined) {\r\n      form.setValue(\r\n        'characteristicCylindricalCompressiveStrengthFrcMatrix',\r\n        selectedProduct.cylindricCompressiveStrength,\r\n      )\r\n    }\r\n\r\n    // Populate characteristic tensile strength\r\n    if (selectedProduct.characteristicTensileStrength !== undefined) {\r\n      form.setValue(\r\n        'frcCharacteristicTensileStrength',\r\n        selectedProduct.characteristicTensileStrength,\r\n      )\r\n    }\r\n\r\n    // Populate tensile strength\r\n    if (selectedProduct.tensileStrength !== undefined) {\r\n      form.setValue('tensileStrength', selectedProduct.tensileStrength)\r\n    }\r\n\r\n    // Populate adhesion to concrete\r\n    if (selectedProduct.adhesionToConcrete !== undefined) {\r\n      form.setValue('adhesionToConcrete', selectedProduct.adhesionToConcrete)\r\n    }\r\n\r\n    // Populate characteristicCylindricalCompressiveStrengthFrcMatrix\r\n    if (\r\n      selectedProduct.characteristicCylindricalCompressiveStrengthFrcMatrix !==\r\n      undefined\r\n    ) {\r\n      form.setValue(\r\n        'characteristicCylindricalCompressiveStrengthFrcMatrix',\r\n        selectedProduct.characteristicCylindricalCompressiveStrengthFrcMatrix,\r\n      )\r\n    }\r\n\r\n    // Populate specific weight\r\n    if (selectedProduct.specificWeight !== undefined) {\r\n      form.setValue('specificWeight', selectedProduct.specificWeight)\r\n    }\r\n  }, [selectedProduct, form, t, tProduct, isCustomSelected])\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (slabFrcReinforcement: FormSchema) => {\r\n      mutate({ projectId, moduleId, body: { slabFrcReinforcement } })\r\n    },\r\n    [mutate, projectId, moduleId],\r\n  )\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <h1 className=\"text-lg font-bold\">{t('frcProperties.title')}</h1>\r\n\r\n        <SelectFormInput\r\n          control={form.control}\r\n          name=\"frcReinforcementType\"\r\n          options={productsOptions}\r\n          t={t}\r\n          loading={isLoadingProducts}\r\n          requestError={errorGettingProducts}\r\n          errorMessage={t('products.error')}\r\n        />\r\n\r\n        {frcReinforcementType && (\r\n          <StringFormInput\r\n            control={form.control}\r\n            name=\"toughnessClassAndFibersType\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n        )}\r\n\r\n        <NumberFormInput control={form.control} name=\"frcSlabThickness\" t={t} />\r\n\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"elasticModulus\"\r\n          t={t}\r\n          disabled={!isCustomSelected}\r\n        />\r\n\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"characteristicCylindricalCompressiveStrengthFrcMatrix\"\r\n          t={t}\r\n          disabled={!isCustomSelected}\r\n        />\r\n\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"frcCharacteristicTensileStrength\"\r\n          t={t}\r\n          disabled={!isCustomSelected}\r\n        />\r\n\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"tensileStrength\"\r\n          t={t}\r\n          disabled={!isCustomSelected}\r\n        />\r\n\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"adhesionToConcrete\"\r\n          t={t}\r\n          disabled={!isCustomSelected}\r\n        />\r\n\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"specificWeight\"\r\n          t={t}\r\n          disabled={!isCustomSelected}\r\n        />\r\n\r\n        <Button type=\"submit\" className=\"w-full sm:w-auto\" disabled={isPending}>\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AAUO,MAAM,2BAA2B,CAAC,EACvC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,WAAW,IAAA,yNAAe,EAAC;IACjC,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,EACJ,MAAM,QAAQ,EACd,SAAS,oBAAoB,EAC7B,WAAW,iBAAiB,EAC7B,GAAG,IAAA,sLAAiB,EAAC,SAAS,QAAQ,GAAG;IAE1C,MAAM,eAAe;QAAE,OAAO;QAAU,OAAO,EAAE;IAAkB;IAEnE,MAAM,kBAAkB;WAClB,UAAU,QAAQ,IAAI,CAAA,IAAK,CAAC;gBAC9B,OAAO,EAAE,IAAI,IAAI;gBACjB,OAAO,EAAE,IAAI,IAAI,EAAE;YACrB,CAAC,MAAM,EAAE;QACT;KACD;IAED,MAAM,OAAO,IAAA,yKAAO,EAAa;QAC/B,UAAU,IAAA,6KAAW,EAAC,qKAA0B;QAChD,eAAe;YACb,sBAAsB,eAAe;YACrC,gBAAgB,eAAe;YAC/B,WAAW,eAAe;YAC1B,kBAAkB,eAAe;YACjC,gBAAgB,eAAe;YAC/B,uDACE,eAAe;YACjB,kCACE,eAAe;YACjB,iBAAiB,eAAe;YAChC,oBAAoB,eAAe;YACnC,gBAAgB,eAAe;QACjC;IACF;IAEA,MAAM,CAAC,qBAAqB,GAAG,KAAK,KAAK,CAAC;QAAC;KAAuB;IAElE,MAAM,mBAAmB,yBAAyB;IAElD,MAAM,kBAAkB,IAAA,gNAAO,EAC7B,IAAM,UAAU,QAAQ,KAAK,CAAA,IAAK,EAAE,IAAI,KAAK,uBAC7C;QAAC;QAAsB;KAAS;IAGlC,0DAA0D;IAC1D,IAAA,kNAAS,EAAC;QACR,IAAI,oBAAoB,CAAC,iBAAiB;YACxC;QACF;QAEA,wCAAwC;QACxC,IAAI,gBAAgB,cAAc,IAAI,gBAAgB,SAAS,EAAE;YAC/D,MAAM,QAAQ,EAAE;YAChB,IAAI,gBAAgB,cAAc,EAAE;gBAClC,MAAM,IAAI,CAAC,GAAG,EAAE,kBAAkB,EAAE,EAAE,gBAAgB,cAAc,EAAE;YACxE;YACA,IAAI,gBAAgB,SAAS,EAAE;gBAC7B,MAAM,iBAAiB,SAAS,gBAAgB,SAAS;gBACzD,MAAM,IAAI,CAAC,GAAG,EAAE,aAAa,EAAE,EAAE,gBAAgB;YACnD;YACA,KAAK,QAAQ,CAAC,kBAAkB,gBAAgB,cAAc;YAC9D,KAAK,QAAQ,CAAC,aAAa,gBAAgB,SAAS;YACpD,KAAK,QAAQ,CAAC,+BAA+B,MAAM,IAAI,CAAC;QAC1D;QAEA,2BAA2B;QAC3B,IAAI,gBAAgB,cAAc,KAAK,WAAW;YAChD,KAAK,QAAQ,CAAC,kBAAkB,gBAAgB,cAAc;QAChE;QAEA,0CAA0C;QAC1C,IAAI,gBAAgB,4BAA4B,KAAK,WAAW;YAC9D,KAAK,QAAQ,CACX,yDACA,gBAAgB,4BAA4B;QAEhD;QAEA,2CAA2C;QAC3C,IAAI,gBAAgB,6BAA6B,KAAK,WAAW;YAC/D,KAAK,QAAQ,CACX,oCACA,gBAAgB,6BAA6B;QAEjD;QAEA,4BAA4B;QAC5B,IAAI,gBAAgB,eAAe,KAAK,WAAW;YACjD,KAAK,QAAQ,CAAC,mBAAmB,gBAAgB,eAAe;QAClE;QAEA,gCAAgC;QAChC,IAAI,gBAAgB,kBAAkB,KAAK,WAAW;YACpD,KAAK,QAAQ,CAAC,sBAAsB,gBAAgB,kBAAkB;QACxE;QAEA,iEAAiE;QACjE,IACE,gBAAgB,qDAAqD,KACrE,WACA;YACA,KAAK,QAAQ,CACX,yDACA,gBAAgB,qDAAqD;QAEzE;QAEA,2BAA2B;QAC3B,IAAI,gBAAgB,cAAc,KAAK,WAAW;YAChD,KAAK,QAAQ,CAAC,kBAAkB,gBAAgB,cAAc;QAChE;IACF,GAAG;QAAC;QAAiB;QAAM;QAAG;QAAU;KAAiB;IAEzD,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAW;wBAAE;wBAAW;oBAAS;iBAAE;YAChD;YACA;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,OAAO;YAAE;YAAW;YAAU,MAAM;gBAAE;YAAqB;QAAE;IAC/D,GACA;QAAC;QAAQ;QAAW;KAAS;IAG/B,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,8OAAC;oBAAG,WAAU;8BAAqB,EAAE;;;;;;8BAErC,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS;oBACT,GAAG;oBACH,SAAS;oBACT,cAAc;oBACd,cAAc,EAAE;;;;;;gBAGjB,sCACC,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAId,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAmB,GAAG;;;;;;8BAEnE,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU,CAAC;;;;;;8BAGb,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU,CAAC;;;;;;8BAGb,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU,CAAC;;;;;;8BAGb,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU,CAAC;;;;;;8BAGb,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU,CAAC;;;;;;8BAGb,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU,CAAC;;;;;;8BAGb,8OAAC,4IAAM;oBAAC,MAAK;oBAAS,WAAU;oBAAmB,UAAU;;wBAC1D,2BAAa,8OAAC,oOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/slab/sections/slab-geometry-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport {\r\n  calculateSlabEffectiveDepth,\r\n  calculateSlabTotalHeight,\r\n} from '@atlas/functions/forms/slab-form-calculations'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type SlabGeometryFormInputs as FormSchema,\r\n  slabGeometrySchema,\r\n} from '@atlas/types/schemas/slab-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FormSchema>\r\n  onSave: () => void\r\n}\r\n\r\nconst JOIST_FORMWORK_OPTIONS = ['T_SHAPED', 'RECTANGULAR_WITHOUT_SLAB'] as const\r\nconst STRUCTURAL_SCHEME_OPTIONS = ['SIMPLY_SUPPORTED', 'CANTILEVER'] as const\r\n\r\nexport const SlabGeometryForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.slab.geometry')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const form = useForm<FormSchema>({\r\n    resolver: zodResolver(slabGeometrySchema),\r\n    defaultValues: {\r\n      joistFormwork: defaultValues?.joistFormwork ?? 'T_SHAPED',\r\n      joistBase: defaultValues?.joistBase,\r\n      joistWebHeight: defaultValues?.joistWebHeight,\r\n      existingSlabHeight: defaultValues?.existingSlabHeight ?? 0,\r\n      joistSpacing: defaultValues?.joistSpacing,\r\n      bottomRebarCover: defaultValues?.bottomRebarCover,\r\n      topRebarCover: defaultValues?.topRebarCover,\r\n      structuralScheme: defaultValues?.structuralScheme ?? 'SIMPLY_SUPPORTED',\r\n      totalSlabThickness: defaultValues?.totalSlabThickness,\r\n      effectiveDepth: defaultValues?.effectiveDepth,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (geometry: FormSchema) => {\r\n      mutate({ projectId, moduleId, body: { geometry } })\r\n    },\r\n    [mutate, projectId, moduleId],\r\n  )\r\n\r\n  const [joistFormwork, joistWebHeight, existingSlabHeight, bottomRebarCover] =\r\n    form.watch([\r\n      'joistFormwork',\r\n      'joistWebHeight',\r\n      'existingSlabHeight',\r\n      'bottomRebarCover',\r\n    ])\r\n\r\n  // Auto-calculate total slab thickness (D13)\r\n  useEffect(() => {\r\n    if (joistWebHeight && existingSlabHeight !== undefined) {\r\n      const totalHeight = calculateSlabTotalHeight(\r\n        joistWebHeight,\r\n        existingSlabHeight,\r\n      )\r\n      form.setValue('totalSlabThickness', totalHeight, { shouldValidate: true })\r\n    }\r\n  }, [joistWebHeight, existingSlabHeight, form])\r\n\r\n  // Auto-calculate effective depth (D16)\r\n  useEffect(() => {\r\n    if (\r\n      joistWebHeight &&\r\n      existingSlabHeight !== undefined &&\r\n      bottomRebarCover\r\n    ) {\r\n      const totalHeight = calculateSlabTotalHeight(\r\n        joistWebHeight,\r\n        existingSlabHeight,\r\n      )\r\n      const effectiveDepth = calculateSlabEffectiveDepth(\r\n        totalHeight,\r\n        bottomRebarCover,\r\n      )\r\n      form.setValue('effectiveDepth', effectiveDepth, { shouldValidate: true })\r\n    }\r\n  }, [joistWebHeight, existingSlabHeight, bottomRebarCover, form])\r\n\r\n  // Reset existing slab height to 0 when formwork is rectangular\r\n  useEffect(() => {\r\n    if (joistFormwork === 'RECTANGULAR_WITHOUT_SLAB') {\r\n      form.setValue('existingSlabHeight', 0, { shouldValidate: true })\r\n    }\r\n  }, [joistFormwork, form])\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        {/* <Image\r\n          src=\"/assets/slab/slab_geometry.jpg\"\r\n          alt=\"slab geometry\"\r\n          height={250}\r\n          width={500}\r\n          className=\"mx-auto rounded-md object-contain\"\r\n          priority\r\n        /> */}\r\n\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"joistFormwork\"\r\n          options={JOIST_FORMWORK_OPTIONS}\r\n          optionLabelFn={p => t(`joistFormwork.${p}`)}\r\n          t={t}\r\n        />\r\n\r\n        <NumberFormInput control={form.control} name=\"joistBase\" t={t} />\r\n\r\n        <NumberFormInput control={form.control} name=\"joistWebHeight\" t={t} />\r\n\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"existingSlabHeight\"\r\n          t={t}\r\n          disabled={joistFormwork === 'RECTANGULAR_WITHOUT_SLAB'}\r\n        />\r\n\r\n        <NumberFormInput control={form.control} name=\"joistSpacing\" t={t} />\r\n\r\n        <NumberFormInput control={form.control} name=\"bottomRebarCover\" t={t} />\r\n\r\n        <NumberFormInput control={form.control} name=\"topRebarCover\" t={t} />\r\n\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"structuralScheme\"\r\n          options={STRUCTURAL_SCHEME_OPTIONS}\r\n          optionLabelFn={p => t(`structuralScheme.${p}`)}\r\n          t={t}\r\n        />\r\n\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"totalSlabThickness\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"effectiveDepth\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n\r\n        <Button type=\"submit\" className=\"w-full sm:w-auto\" disabled={isPending}>\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAMA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AAUA,MAAM,yBAAyB;IAAC;IAAY;CAA2B;AACvE,MAAM,4BAA4B;IAAC;IAAoB;CAAa;AAE7D,MAAM,mBAAmB,CAAC,EAC/B,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,OAAO,IAAA,yKAAO,EAAa;QAC/B,UAAU,IAAA,6KAAW,EAAC,6JAAkB;QACxC,eAAe;YACb,eAAe,eAAe,iBAAiB;YAC/C,WAAW,eAAe;YAC1B,gBAAgB,eAAe;YAC/B,oBAAoB,eAAe,sBAAsB;YACzD,cAAc,eAAe;YAC7B,kBAAkB,eAAe;YACjC,eAAe,eAAe;YAC9B,kBAAkB,eAAe,oBAAoB;YACrD,oBAAoB,eAAe;YACnC,gBAAgB,eAAe;QACjC;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAW;wBAAE;wBAAW;oBAAS;iBAAE;YAChD;YACA;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,OAAO;YAAE;YAAW;YAAU,MAAM;gBAAE;YAAS;QAAE;IACnD,GACA;QAAC;QAAQ;QAAW;KAAS;IAG/B,MAAM,CAAC,eAAe,gBAAgB,oBAAoB,iBAAiB,GACzE,KAAK,KAAK,CAAC;QACT;QACA;QACA;QACA;KACD;IAEH,4CAA4C;IAC5C,IAAA,kNAAS,EAAC;QACR,IAAI,kBAAkB,uBAAuB,WAAW;YACtD,MAAM,cAAc,IAAA,qLAAwB,EAC1C,gBACA;YAEF,KAAK,QAAQ,CAAC,sBAAsB,aAAa;gBAAE,gBAAgB;YAAK;QAC1E;IACF,GAAG;QAAC;QAAgB;QAAoB;KAAK;IAE7C,uCAAuC;IACvC,IAAA,kNAAS,EAAC;QACR,IACE,kBACA,uBAAuB,aACvB,kBACA;YACA,MAAM,cAAc,IAAA,qLAAwB,EAC1C,gBACA;YAEF,MAAM,iBAAiB,IAAA,wLAA2B,EAChD,aACA;YAEF,KAAK,QAAQ,CAAC,kBAAkB,gBAAgB;gBAAE,gBAAgB;YAAK;QACzE;IACF,GAAG;QAAC;QAAgB;QAAoB;QAAkB;KAAK;IAE/D,+DAA+D;IAC/D,IAAA,kNAAS,EAAC;QACR,IAAI,kBAAkB,4BAA4B;YAChD,KAAK,QAAQ,CAAC,sBAAsB,GAAG;gBAAE,gBAAgB;YAAK;QAChE;IACF,GAAG;QAAC;QAAe;KAAK;IAExB,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAW5B,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS;oBACT,eAAe,CAAA,IAAK,EAAE,CAAC,cAAc,EAAE,GAAG;oBAC1C,GAAG;;;;;;8BAGL,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAY,GAAG;;;;;;8BAE5D,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAiB,GAAG;;;;;;8BAEjE,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU,kBAAkB;;;;;;8BAG9B,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAe,GAAG;;;;;;8BAE/D,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAmB,GAAG;;;;;;8BAEnE,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAgB,GAAG;;;;;;8BAEhE,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS;oBACT,eAAe,CAAA,IAAK,EAAE,CAAC,iBAAiB,EAAE,GAAG;oBAC7C,GAAG;;;;;;8BAGL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAGZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAGZ,8OAAC,4IAAM;oBAAC,MAAK;oBAAS,WAAU;oBAAmB,UAAU;;wBAC1D,2BAAa,8OAAC,oOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 587, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/slab/sections/slab-concrete-class-form-section.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { StringFormInput } from '@atlas/components/common/form/string-form-input'\r\nimport { moduleMaterialKnowledgeLevelValues } from '@atlas/constants/module'\r\nimport {\r\n  calculateSlabDesignStrengthForBrittleMechanisms,\r\n  calculateSlabDesignStrengthForDuctileMechanisms,\r\n  calculateSlabDesignTensileStrengthForBrittleMechanisms,\r\n} from '@atlas/functions/forms/slab-form-calculations'\r\nimport { useReinforcedConcreteMaterials } from '@atlas/lib/query/materials/use-reinforced-concrete-materials'\r\nimport type { SlabMaterialFormInputs } from '@atlas/types/schemas/slab-form'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useMemo } from 'react'\r\nimport { useFormContext } from 'react-hook-form'\r\n\r\ntype Props = {\r\n  session: Session\r\n}\r\n\r\nexport function SlabConcreteClassFormSection({ session }: Props) {\r\n  const t = useTranslations('forms.project-params.slab.materialProperties')\r\n\r\n  const form = useFormContext<SlabMaterialFormInputs>()\r\n\r\n  const {\r\n    data: concreteMaterials,\r\n    isError: concreteMaterialsError,\r\n    isLoading: concreteMaterialsLoading,\r\n  } = useReinforcedConcreteMaterials({ session, body: { size: 100 } })\r\n\r\n  const customOption = { value: 'custom', label: t('concreteClass.custom') }\r\n\r\n  const concreteMaterialsOptions = [\r\n    ...(concreteMaterials?.content.map(m => ({\r\n      value: m.id,\r\n      label: m.name,\r\n    })) ?? []),\r\n    customOption,\r\n  ]\r\n\r\n  const concreteClassKnowledgeLevel = form.watch('concreteClassKnowledgeLevel')\r\n  const concreteClassId = form.watch('concreteClass.id')\r\n  const averageCompressiveStrength = form.watch(\r\n    'concreteClass.averageCompressiveStrength',\r\n  )\r\n  const averageTensileStrength = form.watch(\r\n    'concreteClass.averageTensileStrength',\r\n  )\r\n\r\n  const isCustomSelected = concreteClassId === 'custom'\r\n\r\n  const selectedConcreteMaterial = useMemo(\r\n    () => concreteMaterials?.content.find(m => m.id === concreteClassId),\r\n    [concreteClassId, concreteMaterials],\r\n  )\r\n\r\n  useEffect(() => {\r\n    if (!selectedConcreteMaterial || isCustomSelected) {\r\n      return\r\n    }\r\n    const selected = selectedConcreteMaterial\r\n    if (form.getValues('concreteClass.name') !== selected.name) {\r\n      form.setValue('concreteClass.name', selected.name)\r\n    }\r\n    if (\r\n      form.getValues('concreteClass.cubeCompressiveStrength') !==\r\n      selected.cubeCompressiveStrength\r\n    ) {\r\n      form.setValue(\r\n        'concreteClass.cubeCompressiveStrength',\r\n        selected.cubeCompressiveStrength,\r\n      )\r\n    }\r\n    if (\r\n      form.getValues('concreteClass.cylinderCompressiveStrength') !==\r\n      selected.cylinderCompressiveStrength\r\n    ) {\r\n      form.setValue(\r\n        'concreteClass.cylinderCompressiveStrength',\r\n        selected.cylinderCompressiveStrength,\r\n      )\r\n    }\r\n    if (\r\n      form.getValues('concreteClass.averageCompressiveStrength') !==\r\n      selected.averageCompressiveStrength\r\n    ) {\r\n      form.setValue(\r\n        'concreteClass.averageCompressiveStrength',\r\n        selected.averageCompressiveStrength,\r\n      )\r\n    }\r\n    if (\r\n      form.getValues('concreteClass.averageTensileStrength') !==\r\n      selected.averageTensileStrength\r\n    ) {\r\n      form.setValue(\r\n        'concreteClass.averageTensileStrength',\r\n        selected.averageTensileStrength,\r\n      )\r\n    }\r\n    if (\r\n      form.getValues('concreteClass.elasticModulus') !== selected.elasticModulus\r\n    ) {\r\n      form.setValue('concreteClass.elasticModulus', selected.elasticModulus)\r\n    }\r\n  }, [form, isCustomSelected, selectedConcreteMaterial])\r\n\r\n  useEffect(() => {\r\n    if (\r\n      concreteClassKnowledgeLevel &&\r\n      averageCompressiveStrength !== undefined &&\r\n      averageCompressiveStrength !== null\r\n    ) {\r\n      const confidenceFactor =\r\n        moduleMaterialKnowledgeLevelValues[concreteClassKnowledgeLevel]\r\n      form.setValue(\r\n        'concreteClass.designCompressiveStrengthForBrittleMechanisms',\r\n        calculateSlabDesignStrengthForBrittleMechanisms(\r\n          averageCompressiveStrength,\r\n          confidenceFactor,\r\n        ),\r\n      )\r\n      form.setValue(\r\n        'concreteClass.designCompressiveStrengthForDuctileMechanisms',\r\n        calculateSlabDesignStrengthForDuctileMechanisms(\r\n          averageCompressiveStrength,\r\n          confidenceFactor,\r\n        ),\r\n      )\r\n    }\r\n  }, [concreteClassKnowledgeLevel, averageCompressiveStrength, form])\r\n\r\n  useEffect(() => {\r\n    if (\r\n      concreteClassKnowledgeLevel &&\r\n      averageTensileStrength !== undefined &&\r\n      averageTensileStrength !== null\r\n    ) {\r\n      const confidenceFactor =\r\n        moduleMaterialKnowledgeLevelValues[concreteClassKnowledgeLevel]\r\n      form.setValue(\r\n        'concreteClass.designTensileStrengthForBrittleMechanisms',\r\n        calculateSlabDesignTensileStrengthForBrittleMechanisms(\r\n          averageTensileStrength,\r\n          confidenceFactor,\r\n        ),\r\n      )\r\n    }\r\n  }, [concreteClassKnowledgeLevel, averageTensileStrength, form])\r\n\r\n  return (\r\n    <>\r\n      <SelectFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.id\"\r\n        options={concreteMaterialsOptions}\r\n        t={t}\r\n        loading={concreteMaterialsLoading}\r\n        requestError={concreteMaterialsError}\r\n        errorMessage={t('concreteClass.error')}\r\n      />\r\n      {isCustomSelected && (\r\n        <StringFormInput\r\n          control={form.control}\r\n          name=\"concreteClass.name\"\r\n          t={t}\r\n        />\r\n      )}\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.cubeCompressiveStrength\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.cylinderCompressiveStrength\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.averageCompressiveStrength\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.averageTensileStrength\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.elasticModulus\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.designCompressiveStrengthForDuctileMechanisms\"\r\n        t={t}\r\n        disabled={true}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.designTensileStrengthForBrittleMechanisms\"\r\n        t={t}\r\n        disabled={true}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.designCompressiveStrengthForBrittleMechanisms\"\r\n        t={t}\r\n        disabled={true}\r\n      />\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAKA;AAGA;AACA;AACA;;;;;;;;;;;AAMO,SAAS,6BAA6B,EAAE,OAAO,EAAS;IAC7D,MAAM,IAAI,IAAA,yNAAe,EAAC;IAE1B,MAAM,OAAO,IAAA,gLAAc;IAE3B,MAAM,EACJ,MAAM,iBAAiB,EACvB,SAAS,sBAAsB,EAC/B,WAAW,wBAAwB,EACpC,GAAG,IAAA,gNAA8B,EAAC;QAAE;QAAS,MAAM;YAAE,MAAM;QAAI;IAAE;IAElE,MAAM,eAAe;QAAE,OAAO;QAAU,OAAO,EAAE;IAAwB;IAEzE,MAAM,2BAA2B;WAC3B,mBAAmB,QAAQ,IAAI,CAAA,IAAK,CAAC;gBACvC,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,IAAI;YACf,CAAC,MAAM,EAAE;QACT;KACD;IAED,MAAM,8BAA8B,KAAK,KAAK,CAAC;IAC/C,MAAM,kBAAkB,KAAK,KAAK,CAAC;IACnC,MAAM,6BAA6B,KAAK,KAAK,CAC3C;IAEF,MAAM,yBAAyB,KAAK,KAAK,CACvC;IAGF,MAAM,mBAAmB,oBAAoB;IAE7C,MAAM,2BAA2B,IAAA,gNAAO,EACtC,IAAM,mBAAmB,QAAQ,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK,kBACpD;QAAC;QAAiB;KAAkB;IAGtC,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,4BAA4B,kBAAkB;YACjD;QACF;QACA,MAAM,WAAW;QACjB,IAAI,KAAK,SAAS,CAAC,0BAA0B,SAAS,IAAI,EAAE;YAC1D,KAAK,QAAQ,CAAC,sBAAsB,SAAS,IAAI;QACnD;QACA,IACE,KAAK,SAAS,CAAC,6CACf,SAAS,uBAAuB,EAChC;YACA,KAAK,QAAQ,CACX,yCACA,SAAS,uBAAuB;QAEpC;QACA,IACE,KAAK,SAAS,CAAC,iDACf,SAAS,2BAA2B,EACpC;YACA,KAAK,QAAQ,CACX,6CACA,SAAS,2BAA2B;QAExC;QACA,IACE,KAAK,SAAS,CAAC,gDACf,SAAS,0BAA0B,EACnC;YACA,KAAK,QAAQ,CACX,4CACA,SAAS,0BAA0B;QAEvC;QACA,IACE,KAAK,SAAS,CAAC,4CACf,SAAS,sBAAsB,EAC/B;YACA,KAAK,QAAQ,CACX,wCACA,SAAS,sBAAsB;QAEnC;QACA,IACE,KAAK,SAAS,CAAC,oCAAoC,SAAS,cAAc,EAC1E;YACA,KAAK,QAAQ,CAAC,gCAAgC,SAAS,cAAc;QACvE;IACF,GAAG;QAAC;QAAM;QAAkB;KAAyB;IAErD,IAAA,kNAAS,EAAC;QACR,IACE,+BACA,+BAA+B,aAC/B,+BAA+B,MAC/B;YACA,MAAM,mBACJ,iKAAkC,CAAC,4BAA4B;YACjE,KAAK,QAAQ,CACX,+DACA,IAAA,4MAA+C,EAC7C,4BACA;YAGJ,KAAK,QAAQ,CACX,+DACA,IAAA,4MAA+C,EAC7C,4BACA;QAGN;IACF,GAAG;QAAC;QAA6B;QAA4B;KAAK;IAElE,IAAA,kNAAS,EAAC;QACR,IACE,+BACA,2BAA2B,aAC3B,2BAA2B,MAC3B;YACA,MAAM,mBACJ,iKAAkC,CAAC,4BAA4B;YACjE,KAAK,QAAQ,CACX,2DACA,IAAA,mNAAsD,EACpD,wBACA;QAGN;IACF,GAAG;QAAC;QAA6B;QAAwB;KAAK;IAE9D,qBACE;;0BACE,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,SAAS;gBACT,GAAG;gBACH,SAAS;gBACT,cAAc;gBACd,cAAc,EAAE;;;;;;YAEjB,kCACC,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;;;;;;0BAGP,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU;;;;;;0BAEZ,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU;;;;;;0BAEZ,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU;;;;;;;;AAIlB", "debugId": null}}, {"offset": {"line": 800, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/slab/sections/slab-steel-grade-form-section.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { StringFormInput } from '@atlas/components/common/form/string-form-input'\r\nimport { moduleMaterialKnowledgeLevelValues } from '@atlas/constants/module'\r\nimport {\r\n  calculateSlabDesignYieldStrengthForBrittleMechanisms,\r\n  calculateSlabDesignYieldStrengthForDuctileMechanisms,\r\n} from '@atlas/functions/forms/slab-form-calculations'\r\nimport { useSteelGradesMaterials } from '@atlas/lib/query/materials/use-steel-grades-materials'\r\nimport type { SlabMaterialFormInputs } from '@atlas/types/schemas/slab-form'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useMemo } from 'react'\r\nimport { useFormContext } from 'react-hook-form'\r\n\r\nexport function SlabSteelGradeFormSection({ session }: { session: Session }) {\r\n  const t = useTranslations('forms.project-params.slab.materialProperties')\r\n  const form = useFormContext<SlabMaterialFormInputs>()\r\n\r\n  const {\r\n    data: steelMaterials,\r\n    isError: steelMaterialsError,\r\n    isLoading: steelMaterialsLoading,\r\n  } = useSteelGradesMaterials({ session, body: { size: 100 } })\r\n\r\n  const customOption = { value: 'custom', label: t('steelGrade.custom') }\r\n  const steelMaterialsOptions = [\r\n    ...(steelMaterials?.content.map(m => ({ value: m.id, label: m.name })) ??\r\n      []),\r\n    customOption,\r\n  ]\r\n\r\n  const steelGradeKnowledgeLevel = form.watch('steelGradeKnowledgeLevel')\r\n  const steelGradeId = form.watch('steelGrade.id')\r\n  const yieldStrength = form.watch('steelGrade.yieldStrength')\r\n\r\n  const isCustomSelected = steelGradeId === 'custom'\r\n\r\n  const selectedSteelMaterial = useMemo(\r\n    () => steelMaterials?.content.find(m => m.id === steelGradeId),\r\n    [steelGradeId, steelMaterials],\r\n  )\r\n\r\n  useEffect(() => {\r\n    if (!selectedSteelMaterial || isCustomSelected) {\r\n      return\r\n    }\r\n    const selected = selectedSteelMaterial\r\n    if (form.getValues('steelGrade.name') !== selected.name) {\r\n      form.setValue('steelGrade.name', selected.name)\r\n    }\r\n    if (form.getValues('steelGrade.yieldStrength') !== selected.yieldStrength) {\r\n      form.setValue('steelGrade.yieldStrength', selected.yieldStrength)\r\n    }\r\n    if (\r\n      form.getValues('steelGrade.tensileStrength') !== selected.tensileStrength\r\n    ) {\r\n      form.setValue('steelGrade.tensileStrength', selected.tensileStrength)\r\n    }\r\n    if (\r\n      form.getValues('steelGrade.elongationPercentage') !==\r\n      selected.elongationPercentage\r\n    ) {\r\n      form.setValue(\r\n        'steelGrade.elongationPercentage',\r\n        selected.elongationPercentage,\r\n      )\r\n    }\r\n    if (\r\n      form.getValues('steelGrade.elasticModulus') !== selected.elasticModulus\r\n    ) {\r\n      form.setValue('steelGrade.elasticModulus', selected.elasticModulus)\r\n    }\r\n  }, [form, isCustomSelected, selectedSteelMaterial])\r\n\r\n  useEffect(() => {\r\n    if (\r\n      steelGradeKnowledgeLevel &&\r\n      yieldStrength !== undefined &&\r\n      yieldStrength !== null\r\n    ) {\r\n      const confidenceFactor =\r\n        moduleMaterialKnowledgeLevelValues[steelGradeKnowledgeLevel]\r\n      form.setValue(\r\n        'steelGrade.designYieldStrengthForDuctileMechanisms',\r\n        calculateSlabDesignYieldStrengthForDuctileMechanisms(\r\n          yieldStrength,\r\n          confidenceFactor,\r\n        ),\r\n      )\r\n      form.setValue(\r\n        'steelGrade.designYieldStrengthForBrittleMechanisms',\r\n        calculateSlabDesignYieldStrengthForBrittleMechanisms(\r\n          yieldStrength,\r\n          confidenceFactor,\r\n        ),\r\n      )\r\n    }\r\n  }, [steelGradeKnowledgeLevel, yieldStrength, form])\r\n\r\n  return (\r\n    <>\r\n      <SelectFormInput\r\n        control={form.control}\r\n        name=\"steelGrade.id\"\r\n        options={steelMaterialsOptions}\r\n        t={t}\r\n        loading={steelMaterialsLoading}\r\n        requestError={steelMaterialsError}\r\n        errorMessage={t('steelGrade.error')}\r\n      />\r\n      {isCustomSelected && (\r\n        <StringFormInput control={form.control} name=\"steelGrade.name\" t={t} />\r\n      )}\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"steelGrade.yieldStrength\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"steelGrade.elasticModulus\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"steelGrade.designYieldStrengthForDuctileMechanisms\"\r\n        t={t}\r\n        disabled={true}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"steelGrade.designYieldStrengthForBrittleMechanisms\"\r\n        t={t}\r\n        disabled={true}\r\n      />\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAIA;AAGA;AACA;AACA;;;;;;;;;;;AAEO,SAAS,0BAA0B,EAAE,OAAO,EAAwB;IACzE,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,OAAO,IAAA,gLAAc;IAE3B,MAAM,EACJ,MAAM,cAAc,EACpB,SAAS,mBAAmB,EAC5B,WAAW,qBAAqB,EACjC,GAAG,IAAA,kMAAuB,EAAC;QAAE;QAAS,MAAM;YAAE,MAAM;QAAI;IAAE;IAE3D,MAAM,eAAe;QAAE,OAAO;QAAU,OAAO,EAAE;IAAqB;IACtE,MAAM,wBAAwB;WACxB,gBAAgB,QAAQ,IAAI,CAAA,IAAK,CAAC;gBAAE,OAAO,EAAE,EAAE;gBAAE,OAAO,EAAE,IAAI;YAAC,CAAC,MAClE,EAAE;QACJ;KACD;IAED,MAAM,2BAA2B,KAAK,KAAK,CAAC;IAC5C,MAAM,eAAe,KAAK,KAAK,CAAC;IAChC,MAAM,gBAAgB,KAAK,KAAK,CAAC;IAEjC,MAAM,mBAAmB,iBAAiB;IAE1C,MAAM,wBAAwB,IAAA,gNAAO,EACnC,IAAM,gBAAgB,QAAQ,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK,eACjD;QAAC;QAAc;KAAe;IAGhC,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,yBAAyB,kBAAkB;YAC9C;QACF;QACA,MAAM,WAAW;QACjB,IAAI,KAAK,SAAS,CAAC,uBAAuB,SAAS,IAAI,EAAE;YACvD,KAAK,QAAQ,CAAC,mBAAmB,SAAS,IAAI;QAChD;QACA,IAAI,KAAK,SAAS,CAAC,gCAAgC,SAAS,aAAa,EAAE;YACzE,KAAK,QAAQ,CAAC,4BAA4B,SAAS,aAAa;QAClE;QACA,IACE,KAAK,SAAS,CAAC,kCAAkC,SAAS,eAAe,EACzE;YACA,KAAK,QAAQ,CAAC,8BAA8B,SAAS,eAAe;QACtE;QACA,IACE,KAAK,SAAS,CAAC,uCACf,SAAS,oBAAoB,EAC7B;YACA,KAAK,QAAQ,CACX,mCACA,SAAS,oBAAoB;QAEjC;QACA,IACE,KAAK,SAAS,CAAC,iCAAiC,SAAS,cAAc,EACvE;YACA,KAAK,QAAQ,CAAC,6BAA6B,SAAS,cAAc;QACpE;IACF,GAAG;QAAC;QAAM;QAAkB;KAAsB;IAElD,IAAA,kNAAS,EAAC;QACR,IACE,4BACA,kBAAkB,aAClB,kBAAkB,MAClB;YACA,MAAM,mBACJ,iKAAkC,CAAC,yBAAyB;YAC9D,KAAK,QAAQ,CACX,sDACA,IAAA,iNAAoD,EAClD,eACA;YAGJ,KAAK,QAAQ,CACX,sDACA,IAAA,iNAAoD,EAClD,eACA;QAGN;IACF,GAAG;QAAC;QAA0B;QAAe;KAAK;IAElD,qBACE;;0BACE,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,SAAS;gBACT,GAAG;gBACH,SAAS;gBACT,cAAc;gBACd,cAAc,EAAE;;;;;;YAEjB,kCACC,8OAAC,kLAAe;gBAAC,SAAS,KAAK,OAAO;gBAAE,MAAK;gBAAkB,GAAG;;;;;;0BAEpE,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU;;;;;;0BAEZ,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU;;;;;;;;AAIlB", "debugId": null}}, {"offset": {"line": 959, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/slab/sections/slab-material-form.tsx"], "sourcesContent": ["import { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport {\r\n  MODULE_MATERIAL_CLASS,\r\n  MODULE_MATERIAL_KNOWLEDGE_LEVEL,\r\n  moduleMaterialClass,\r\n  moduleMaterialKnowledgeLevel,\r\n} from '@atlas/constants/module'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type SlabMaterialFormInputs as FormSchema,\r\n  slabMaterialSchema,\r\n} from '@atlas/types/schemas/slab-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\nimport { SlabConcreteClassFormSection } from './slab-concrete-class-form-section'\r\nimport { SlabSteelGradeFormSection } from './slab-steel-grade-form-section'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FormSchema>\r\n  onSave: () => void\r\n}\r\n\r\nexport const SlabMaterialForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.slab.materialProperties')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const form = useForm<FormSchema>({\r\n    resolver: zodResolver(slabMaterialSchema),\r\n    defaultValues: {\r\n      concreteClassKnowledgeLevel:\r\n        defaultValues?.concreteClassKnowledgeLevel ??\r\n        moduleMaterialKnowledgeLevel.LC1,\r\n      steelGradeKnowledgeLevel:\r\n        defaultValues?.steelGradeKnowledgeLevel ??\r\n        moduleMaterialKnowledgeLevel.LC1,\r\n      concreteMaterialClass:\r\n        defaultValues?.concreteMaterialClass ?? moduleMaterialClass.DUCTILE,\r\n      steelMaterialClass:\r\n        defaultValues?.steelMaterialClass ?? moduleMaterialClass.DUCTILE,\r\n      concreteClass: {\r\n        id: defaultValues?.concreteClass?.id,\r\n        name: defaultValues?.concreteClass?.name,\r\n        cubeCompressiveStrength:\r\n          defaultValues?.concreteClass?.cubeCompressiveStrength,\r\n        cylinderCompressiveStrength:\r\n          defaultValues?.concreteClass?.cylinderCompressiveStrength,\r\n        averageCompressiveStrength:\r\n          defaultValues?.concreteClass?.averageCompressiveStrength,\r\n        averageTensileStrength:\r\n          defaultValues?.concreteClass?.averageTensileStrength,\r\n        elasticModulus: defaultValues?.concreteClass?.elasticModulus,\r\n        designCompressiveStrengthForBrittleMechanisms:\r\n          defaultValues?.concreteClass\r\n            ?.designCompressiveStrengthForBrittleMechanisms,\r\n        designTensileStrengthForBrittleMechanisms:\r\n          defaultValues?.concreteClass\r\n            ?.designTensileStrengthForBrittleMechanisms,\r\n        designCompressiveStrengthForDuctileMechanisms:\r\n          defaultValues?.concreteClass\r\n            ?.designCompressiveStrengthForDuctileMechanisms,\r\n      },\r\n      steelGrade: {\r\n        id: defaultValues?.steelGrade?.id,\r\n        name: defaultValues?.steelGrade?.name,\r\n        yieldStrength: defaultValues?.steelGrade?.yieldStrength,\r\n        tensileStrength: defaultValues?.steelGrade?.tensileStrength,\r\n        elongationPercentage: defaultValues?.steelGrade?.elongationPercentage,\r\n        elasticModulus: defaultValues?.steelGrade?.elasticModulus,\r\n        designYieldStrengthForBrittleMechanisms:\r\n          defaultValues?.steelGrade?.designYieldStrengthForBrittleMechanisms,\r\n        designYieldStrengthForDuctileMechanisms:\r\n          defaultValues?.steelGrade?.designYieldStrengthForDuctileMechanisms,\r\n      },\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (materialProperties: FormSchema) => {\r\n      mutate({ projectId, moduleId, body: { materialProperties } })\r\n    },\r\n    [mutate, projectId, moduleId],\r\n  )\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"concreteClassKnowledgeLevel\"\r\n          options={MODULE_MATERIAL_KNOWLEDGE_LEVEL}\r\n          optionLabelFn={p => t(`concreteClassKnowledgeLevel.${p}`)}\r\n          t={t}\r\n        />\r\n\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"steelGradeKnowledgeLevel\"\r\n          options={MODULE_MATERIAL_KNOWLEDGE_LEVEL}\r\n          optionLabelFn={p => t(`steelGradeKnowledgeLevel.${p}`)}\r\n          t={t}\r\n        />\r\n\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"concreteMaterialClass\"\r\n          options={MODULE_MATERIAL_CLASS}\r\n          optionLabelFn={p => t(`concreteMaterialClass.${p}`)}\r\n          t={t}\r\n        />\r\n\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"steelMaterialClass\"\r\n          options={MODULE_MATERIAL_CLASS}\r\n          optionLabelFn={p => t(`steelMaterialClass.${p}`)}\r\n          t={t}\r\n        />\r\n\r\n        <Separator />\r\n\r\n        <SlabConcreteClassFormSection session={session} />\r\n\r\n        <Separator />\r\n\r\n        <SlabSteelGradeFormSection session={session} />\r\n\r\n        <Button type=\"submit\" className=\"w-full sm:w-auto\" disabled={isPending}>\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAQA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;AAUO,MAAM,mBAAmB,CAAC,EAC/B,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,OAAO,IAAA,yKAAO,EAAa;QAC/B,UAAU,IAAA,6KAAW,EAAC,6JAAkB;QACxC,eAAe;YACb,6BACE,eAAe,+BACf,2JAA4B,CAAC,GAAG;YAClC,0BACE,eAAe,4BACf,2JAA4B,CAAC,GAAG;YAClC,uBACE,eAAe,yBAAyB,kJAAmB,CAAC,OAAO;YACrE,oBACE,eAAe,sBAAsB,kJAAmB,CAAC,OAAO;YAClE,eAAe;gBACb,IAAI,eAAe,eAAe;gBAClC,MAAM,eAAe,eAAe;gBACpC,yBACE,eAAe,eAAe;gBAChC,6BACE,eAAe,eAAe;gBAChC,4BACE,eAAe,eAAe;gBAChC,wBACE,eAAe,eAAe;gBAChC,gBAAgB,eAAe,eAAe;gBAC9C,+CACE,eAAe,eACX;gBACN,2CACE,eAAe,eACX;gBACN,+CACE,eAAe,eACX;YACR;YACA,YAAY;gBACV,IAAI,eAAe,YAAY;gBAC/B,MAAM,eAAe,YAAY;gBACjC,eAAe,eAAe,YAAY;gBAC1C,iBAAiB,eAAe,YAAY;gBAC5C,sBAAsB,eAAe,YAAY;gBACjD,gBAAgB,eAAe,YAAY;gBAC3C,yCACE,eAAe,YAAY;gBAC7B,yCACE,eAAe,YAAY;YAC/B;QACF;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAW;wBAAE;wBAAW;oBAAS;iBAAE;YAChD;YACA;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,OAAO;YAAE;YAAW;YAAU,MAAM;gBAAE;YAAmB;QAAE;IAC7D,GACA;QAAC;QAAQ;QAAW;KAAS;IAG/B,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,8JAA+B;oBACxC,eAAe,CAAA,IAAK,EAAE,CAAC,4BAA4B,EAAE,GAAG;oBACxD,GAAG;;;;;;8BAGL,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,8JAA+B;oBACxC,eAAe,CAAA,IAAK,EAAE,CAAC,yBAAyB,EAAE,GAAG;oBACrD,GAAG;;;;;;8BAGL,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,oJAAqB;oBAC9B,eAAe,CAAA,IAAK,EAAE,CAAC,sBAAsB,EAAE,GAAG;oBAClD,GAAG;;;;;;8BAGL,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,oJAAqB;oBAC9B,eAAe,CAAA,IAAK,EAAE,CAAC,mBAAmB,EAAE,GAAG;oBAC/C,GAAG;;;;;;8BAGL,8OAAC,kJAAS;;;;;8BAEV,8OAAC,gRAA4B;oBAAC,SAAS;;;;;;8BAEvC,8OAAC,kJAAS;;;;;8BAEV,8OAAC,0QAAyB;oBAAC,SAAS;;;;;;8BAEpC,8OAAC,4IAAM;oBAAC,MAAK;oBAAS,WAAU;oBAAmB,UAAU;;wBAC1D,2BAAa,8OAAC,oOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 1175, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/slab/sections/slab-reinforcement-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport { calculateSlabRebarArea } from '@atlas/functions/forms/slab-form-calculations'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport {\r\n  type SlabRebarFormInputs as FormSchema,\r\n  slabRebarSchema,\r\n} from '@atlas/types/schemas/slab-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FormSchema>\r\n  structuralScheme?: 'SIMPLY_SUPPORTED' | 'CANTILEVER'\r\n  onSave: () => void\r\n}\r\n\r\nexport const SlabReinforcementForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  structuralScheme,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.slab.slabRebar')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const form = useForm<FormSchema>({\r\n    resolver: zodResolver(slabRebarSchema),\r\n    defaultValues: {\r\n      spanBottomRebar: {\r\n        diameter: defaultValues?.spanBottomRebar?.diameter,\r\n        quantity: defaultValues?.spanBottomRebar?.quantity,\r\n        area: defaultValues?.spanBottomRebar?.area,\r\n      },\r\n      spanTopRebar: {\r\n        diameter: defaultValues?.spanTopRebar?.diameter,\r\n        quantity: defaultValues?.spanTopRebar?.quantity,\r\n        area: defaultValues?.spanTopRebar?.area,\r\n      },\r\n      supportBottomRebar: {\r\n        diameter: defaultValues?.supportBottomRebar?.diameter,\r\n        quantity: defaultValues?.supportBottomRebar?.quantity,\r\n        area: defaultValues?.supportBottomRebar?.area,\r\n      },\r\n      supportTopRebar: {\r\n        diameter: defaultValues?.supportTopRebar?.diameter,\r\n        quantity: defaultValues?.supportTopRebar?.quantity,\r\n        area: defaultValues?.supportTopRebar?.area,\r\n      },\r\n      additionalRebar: {\r\n        diameter: defaultValues?.additionalRebar?.diameter,\r\n        quantity: defaultValues?.additionalRebar?.quantity,\r\n        area: defaultValues?.additionalRebar?.area,\r\n      },\r\n      additionalSteelElasticModulus:\r\n        defaultValues?.additionalSteelElasticModulus,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (slabRebar: FormSchema) => {\r\n      mutate({ projectId, moduleId, body: { slabRebar } })\r\n    },\r\n    [mutate, projectId, moduleId],\r\n  )\r\n\r\n  const [\r\n    spanBottomRebar,\r\n    spanTopRebar,\r\n    supportBottomRebar,\r\n    supportTopRebar,\r\n    additionalRebar,\r\n  ] = form.watch([\r\n    'spanBottomRebar',\r\n    'spanTopRebar',\r\n    'supportBottomRebar',\r\n    'supportTopRebar',\r\n    'additionalRebar',\r\n  ])\r\n\r\n  // Auto-calculate span bottom rebar area (D23)\r\n  useEffect(() => {\r\n    if (spanBottomRebar?.diameter && spanBottomRebar?.quantity) {\r\n      const area = calculateSlabRebarArea(\r\n        spanBottomRebar.diameter,\r\n        spanBottomRebar.quantity,\r\n      )\r\n      form.setValue('spanBottomRebar.area', area, { shouldValidate: true })\r\n    }\r\n  }, [spanBottomRebar?.diameter, spanBottomRebar?.quantity, form])\r\n\r\n  // Auto-calculate span top rebar area (D26)\r\n  useEffect(() => {\r\n    if (spanTopRebar?.diameter && spanTopRebar?.quantity) {\r\n      const area = calculateSlabRebarArea(\r\n        spanTopRebar.diameter,\r\n        spanTopRebar.quantity,\r\n      )\r\n      form.setValue('spanTopRebar.area', area, { shouldValidate: true })\r\n    }\r\n  }, [spanTopRebar?.diameter, spanTopRebar?.quantity, form])\r\n\r\n  // Auto-calculate support bottom rebar area (D30)\r\n  useEffect(() => {\r\n    if (supportBottomRebar?.diameter && supportBottomRebar?.quantity) {\r\n      const area = calculateSlabRebarArea(\r\n        supportBottomRebar.diameter,\r\n        supportBottomRebar.quantity,\r\n      )\r\n      form.setValue('supportBottomRebar.area', area, { shouldValidate: true })\r\n    }\r\n  }, [supportBottomRebar?.diameter, supportBottomRebar?.quantity, form])\r\n\r\n  // Auto-calculate support top rebar area (D33)\r\n  useEffect(() => {\r\n    if (supportTopRebar?.diameter && supportTopRebar?.quantity) {\r\n      const area = calculateSlabRebarArea(\r\n        supportTopRebar.diameter,\r\n        supportTopRebar.quantity,\r\n      )\r\n      form.setValue('supportTopRebar.area', area, { shouldValidate: true })\r\n    }\r\n  }, [supportTopRebar?.diameter, supportTopRebar?.quantity, form])\r\n\r\n  // Auto-calculate additional rebar area (D80)\r\n  useEffect(() => {\r\n    if (additionalRebar?.diameter && additionalRebar?.quantity) {\r\n      const area = calculateSlabRebarArea(\r\n        additionalRebar.diameter,\r\n        additionalRebar.quantity,\r\n      )\r\n      form.setValue('additionalRebar.area', area, { shouldValidate: true })\r\n    }\r\n  }, [additionalRebar?.diameter, additionalRebar?.quantity, form])\r\n\r\n  const showSpanSection = structuralScheme === 'SIMPLY_SUPPORTED'\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        {showSpanSection && (\r\n          <>\r\n            <h1 className=\"text-md font-bold\">{t('spanSection.title')}</h1>\r\n            <p className={cn('text-muted-foreground text-sm')}>\r\n              {t('spanSection.description')}\r\n            </p>\r\n\r\n            <div className=\"grid grid-cols-1 gap-4 md:grid-cols-3\">\r\n              <NumberFormInput\r\n                control={form.control}\r\n                name=\"spanBottomRebar.diameter\"\r\n                t={t}\r\n              />\r\n              <NumberFormInput\r\n                control={form.control}\r\n                name=\"spanBottomRebar.quantity\"\r\n                t={t}\r\n              />\r\n              <NumberFormInput\r\n                control={form.control}\r\n                name=\"spanBottomRebar.area\"\r\n                t={t}\r\n                disabled={true}\r\n              />\r\n            </div>\r\n\r\n            <div className=\"grid grid-cols-1 gap-4 md:grid-cols-3\">\r\n              <NumberFormInput\r\n                control={form.control}\r\n                name=\"spanTopRebar.diameter\"\r\n                t={t}\r\n              />\r\n              <NumberFormInput\r\n                control={form.control}\r\n                name=\"spanTopRebar.quantity\"\r\n                t={t}\r\n              />\r\n              <NumberFormInput\r\n                control={form.control}\r\n                name=\"spanTopRebar.area\"\r\n                t={t}\r\n                disabled={true}\r\n              />\r\n            </div>\r\n            <Separator />\r\n          </>\r\n        )}\r\n\r\n        <h1 className=\"text-md font-bold\">{t('supportSection.title')}</h1>\r\n        <p className={cn('text-muted-foreground text-sm')}>\r\n          {t('supportSection.description')}\r\n        </p>\r\n\r\n        <div className=\"grid grid-cols-1 gap-4 md:grid-cols-3\">\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"supportBottomRebar.diameter\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"supportBottomRebar.quantity\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"supportBottomRebar.area\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n        </div>\r\n\r\n        <div className=\"grid grid-cols-1 gap-4 md:grid-cols-3\">\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"supportTopRebar.diameter\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"supportTopRebar.quantity\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"supportTopRebar.area\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n        </div>\r\n\r\n        <Separator />\r\n\r\n        <h1 className=\"text-md font-bold\">{t('additionalRebar.title')}</h1>\r\n        <p className={cn('text-muted-foreground text-sm')}>\r\n          {t('additionalRebar.description')}\r\n        </p>\r\n\r\n        <div className=\"grid grid-cols-1 gap-4 md:grid-cols-3\">\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"additionalRebar.diameter\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"additionalRebar.quantity\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"additionalRebar.area\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n        </div>\r\n\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"additionalSteelElasticModulus\"\r\n          t={t}\r\n        />\r\n\r\n        <Button type=\"submit\" className=\"w-full sm:w-auto\" disabled={isPending}>\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AAWO,MAAM,wBAAwB,CAAC,EACpC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,gBAAgB,EAChB,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,OAAO,IAAA,yKAAO,EAAa;QAC/B,UAAU,IAAA,6KAAW,EAAC,0JAAe;QACrC,eAAe;YACb,iBAAiB;gBACf,UAAU,eAAe,iBAAiB;gBAC1C,UAAU,eAAe,iBAAiB;gBAC1C,MAAM,eAAe,iBAAiB;YACxC;YACA,cAAc;gBACZ,UAAU,eAAe,cAAc;gBACvC,UAAU,eAAe,cAAc;gBACvC,MAAM,eAAe,cAAc;YACrC;YACA,oBAAoB;gBAClB,UAAU,eAAe,oBAAoB;gBAC7C,UAAU,eAAe,oBAAoB;gBAC7C,MAAM,eAAe,oBAAoB;YAC3C;YACA,iBAAiB;gBACf,UAAU,eAAe,iBAAiB;gBAC1C,UAAU,eAAe,iBAAiB;gBAC1C,MAAM,eAAe,iBAAiB;YACxC;YACA,iBAAiB;gBACf,UAAU,eAAe,iBAAiB;gBAC1C,UAAU,eAAe,iBAAiB;gBAC1C,MAAM,eAAe,iBAAiB;YACxC;YACA,+BACE,eAAe;QACnB;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAW;wBAAE;wBAAW;oBAAS;iBAAE;YAChD;YACA;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,OAAO;YAAE;YAAW;YAAU,MAAM;gBAAE;YAAU;QAAE;IACpD,GACA;QAAC;QAAQ;QAAW;KAAS;IAG/B,MAAM,CACJ,iBACA,cACA,oBACA,iBACA,gBACD,GAAG,KAAK,KAAK,CAAC;QACb;QACA;QACA;QACA;QACA;KACD;IAED,8CAA8C;IAC9C,IAAA,kNAAS,EAAC;QACR,IAAI,iBAAiB,YAAY,iBAAiB,UAAU;YAC1D,MAAM,OAAO,IAAA,mLAAsB,EACjC,gBAAgB,QAAQ,EACxB,gBAAgB,QAAQ;YAE1B,KAAK,QAAQ,CAAC,wBAAwB,MAAM;gBAAE,gBAAgB;YAAK;QACrE;IACF,GAAG;QAAC,iBAAiB;QAAU,iBAAiB;QAAU;KAAK;IAE/D,2CAA2C;IAC3C,IAAA,kNAAS,EAAC;QACR,IAAI,cAAc,YAAY,cAAc,UAAU;YACpD,MAAM,OAAO,IAAA,mLAAsB,EACjC,aAAa,QAAQ,EACrB,aAAa,QAAQ;YAEvB,KAAK,QAAQ,CAAC,qBAAqB,MAAM;gBAAE,gBAAgB;YAAK;QAClE;IACF,GAAG;QAAC,cAAc;QAAU,cAAc;QAAU;KAAK;IAEzD,iDAAiD;IACjD,IAAA,kNAAS,EAAC;QACR,IAAI,oBAAoB,YAAY,oBAAoB,UAAU;YAChE,MAAM,OAAO,IAAA,mLAAsB,EACjC,mBAAmB,QAAQ,EAC3B,mBAAmB,QAAQ;YAE7B,KAAK,QAAQ,CAAC,2BAA2B,MAAM;gBAAE,gBAAgB;YAAK;QACxE;IACF,GAAG;QAAC,oBAAoB;QAAU,oBAAoB;QAAU;KAAK;IAErE,8CAA8C;IAC9C,IAAA,kNAAS,EAAC;QACR,IAAI,iBAAiB,YAAY,iBAAiB,UAAU;YAC1D,MAAM,OAAO,IAAA,mLAAsB,EACjC,gBAAgB,QAAQ,EACxB,gBAAgB,QAAQ;YAE1B,KAAK,QAAQ,CAAC,wBAAwB,MAAM;gBAAE,gBAAgB;YAAK;QACrE;IACF,GAAG;QAAC,iBAAiB;QAAU,iBAAiB;QAAU;KAAK;IAE/D,6CAA6C;IAC7C,IAAA,kNAAS,EAAC;QACR,IAAI,iBAAiB,YAAY,iBAAiB,UAAU;YAC1D,MAAM,OAAO,IAAA,mLAAsB,EACjC,gBAAgB,QAAQ,EACxB,gBAAgB,QAAQ;YAE1B,KAAK,QAAQ,CAAC,wBAAwB,MAAM;gBAAE,gBAAgB;YAAK;QACrE;IACF,GAAG;QAAC,iBAAiB;QAAU,iBAAiB;QAAU;KAAK;IAE/D,MAAM,kBAAkB,qBAAqB;IAE7C,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;gBAE3B,iCACC;;sCACE,8OAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,8OAAC;4BAAE,WAAW,IAAA,yHAAE,EAAC;sCACd,EAAE;;;;;;sCAGL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kLAAe;oCACd,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,GAAG;;;;;;8CAEL,8OAAC,kLAAe;oCACd,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,GAAG;;;;;;8CAEL,8OAAC,kLAAe;oCACd,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,GAAG;oCACH,UAAU;;;;;;;;;;;;sCAId,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kLAAe;oCACd,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,GAAG;;;;;;8CAEL,8OAAC,kLAAe;oCACd,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,GAAG;;;;;;8CAEL,8OAAC,kLAAe;oCACd,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,GAAG;oCACH,UAAU;;;;;;;;;;;;sCAGd,8OAAC,kJAAS;;;;;;;8BAId,8OAAC;oBAAG,WAAU;8BAAqB,EAAE;;;;;;8BACrC,8OAAC;oBAAE,WAAW,IAAA,yHAAE,EAAC;8BACd,EAAE;;;;;;8BAGL,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;;;;;;;8BAId,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;;;;;;;8BAId,8OAAC,kJAAS;;;;;8BAEV,8OAAC;oBAAG,WAAU;8BAAqB,EAAE;;;;;;8BACrC,8OAAC;oBAAE,WAAW,IAAA,yHAAE,EAAC;8BACd,EAAE;;;;;;8BAGL,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;;;;;;;8BAId,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAGL,8OAAC,4IAAM;oBAAC,MAAK;oBAAS,WAAU;oBAAmB,UAAU;;wBAC1D,2BAAa,8OAAC,oOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 1650, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/slab/calculations/slab-flexural-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  <PERSON>,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ntype SlabFlexuralResult = {\r\n  sectionType: string\r\n  // Unreinforced section\r\n  unreinforcedBottomSteelStrain?: number\r\n  unreinforcedTopSteelStrain?: number\r\n  unreinforcedBottomSteelStress?: number\r\n  unreinforcedTopSteelStress?: number\r\n  unreinforcedNeutralAxisDistance?: number\r\n  unreinforcedTranslationalEquilibrium?: number\r\n  unreinforcedResistanceMoment?: number\r\n  // Reinforced section\r\n  maximumBendingMoment?: number\r\n  reinforcedBottomSteelStrain?: number\r\n  reinforcedTopSteelStrain?: number\r\n  reinforcedSupplementarySteelStrain?: number\r\n  reinforcedBottomSteelStress?: number\r\n  reinforcedTopSteelStress?: number\r\n  reinforcedSupplementarySteelStress?: number\r\n  reinforcedNeutralAxisDistance?: number\r\n  reinforcedTranslationalEquilibrium?: number\r\n  reinforcedSectionResistanceMoment?: number\r\n  checkResult: boolean\r\n  checkValue?: number\r\n}\r\n\r\ntype Props = {\r\n  result: SlabFlexuralResult\r\n}\r\n\r\nexport const SlabFlexuralResultCard = ({ result }: Props) => {\r\n  const t = useTranslations('forms.calculations.slab.flexural-result')\r\n  const locale = useLocale()\r\n\r\n  const isVerified = result.checkResult\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle className=\"flex items-center justify-between\">\r\n          <span>{t('title')}</span>\r\n          <Badge\r\n            className={cn(\r\n              'text-base px-3 py-1',\r\n              isVerified ? 'bg-green-600' : 'bg-red-600',\r\n            )}\r\n          >\r\n            {isVerified ? t('verified') : t('not-verified')}\r\n          </Badge>\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-6\">\r\n        {/* Section Type */}\r\n        <div>\r\n          <h3 className=\"font-semibold text-lg mb-2\">\r\n            {t('section-type')}: {result.sectionType}\r\n          </h3>\r\n        </div>\r\n\r\n        <Separator />\r\n\r\n        {/* Unreinforced Section */}\r\n        <div>\r\n          <h4 className=\"font-semibold mb-2\">{t('unreinforced-section')}</h4>\r\n          <div className=\"grid grid-cols-2 gap-2 text-sm\">\r\n            {result.unreinforcedBottomSteelStrain !== undefined && (\r\n              <div>\r\n                <span className=\"text-muted-foreground\">\r\n                  {t('bottom-steel-strain')}:\r\n                </span>\r\n                <span className=\"ml-2 font-medium\">\r\n                  {result.unreinforcedBottomSteelStrain.toFixed(6)}\r\n                </span>\r\n              </div>\r\n            )}\r\n            {result.unreinforcedTopSteelStrain !== undefined && (\r\n              <div>\r\n                <span className=\"text-muted-foreground\">\r\n                  {t('top-steel-strain')}:\r\n                </span>\r\n                <span className=\"ml-2 font-medium\">\r\n                  {result.unreinforcedTopSteelStrain.toFixed(6)}\r\n                </span>\r\n              </div>\r\n            )}\r\n            {result.unreinforcedNeutralAxisDistance !== undefined && (\r\n              <div>\r\n                <span className=\"text-muted-foreground\">\r\n                  {t('neutral-axis')}:\r\n                </span>\r\n                <span className=\"ml-2 font-medium\">\r\n                  {result.unreinforcedNeutralAxisDistance.toLocaleString(\r\n                    locale,\r\n                    { maximumFractionDigits: 2 },\r\n                  )}{' '}\r\n                  mm\r\n                </span>\r\n              </div>\r\n            )}\r\n            {result.unreinforcedResistanceMoment !== undefined && (\r\n              <div>\r\n                <span className=\"text-muted-foreground\">\r\n                  {t('resistance-moment')}:\r\n                </span>\r\n                <span className=\"ml-2 font-medium\">\r\n                  {result.unreinforcedResistanceMoment.toLocaleString(locale, {\r\n                    maximumFractionDigits: 2,\r\n                  })}{' '}\r\n                  kNm\r\n                </span>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        <Separator />\r\n\r\n        {/* Reinforced Section */}\r\n        <div>\r\n          <h4 className=\"font-semibold mb-2\">{t('reinforced-section')}</h4>\r\n          <div className=\"grid grid-cols-2 gap-2 text-sm\">\r\n            {result.maximumBendingMoment !== undefined && (\r\n              <div>\r\n                <span className=\"text-muted-foreground\">\r\n                  {t('applied-moment')}:\r\n                </span>\r\n                <span className=\"ml-2 font-medium\">\r\n                  {result.maximumBendingMoment.toLocaleString(locale, {\r\n                    maximumFractionDigits: 2,\r\n                  })}{' '}\r\n                  kNm\r\n                </span>\r\n              </div>\r\n            )}\r\n            {result.reinforcedNeutralAxisDistance !== undefined && (\r\n              <div>\r\n                <span className=\"text-muted-foreground\">\r\n                  {t('neutral-axis')}:\r\n                </span>\r\n                <span className=\"ml-2 font-medium\">\r\n                  {result.reinforcedNeutralAxisDistance.toLocaleString(locale, {\r\n                    maximumFractionDigits: 2,\r\n                  })}{' '}\r\n                  mm\r\n                </span>\r\n              </div>\r\n            )}\r\n            {result.reinforcedSectionResistanceMoment !== undefined && (\r\n              <div className=\"col-span-2\">\r\n                <span className=\"text-muted-foreground font-semibold\">\r\n                  {t('resistance-moment')}:\r\n                </span>\r\n                <span className=\"ml-2 font-medium text-lg\">\r\n                  {result.reinforcedSectionResistanceMoment.toLocaleString(\r\n                    locale,\r\n                    { maximumFractionDigits: 2 },\r\n                  )}{' '}\r\n                  kNm\r\n                </span>\r\n              </div>\r\n            )}\r\n            {result.checkValue !== undefined && (\r\n              <div className=\"col-span-2\">\r\n                <span className=\"text-muted-foreground font-semibold\">\r\n                  {t('safety-factor')}:\r\n                </span>\r\n                <span\r\n                  className={cn(\r\n                    'ml-2 font-medium text-lg',\r\n                    result.checkValue >= 1 ? 'text-green-600' : 'text-red-600',\r\n                  )}\r\n                >\r\n                  {result.checkValue.toLocaleString(locale, {\r\n                    minimumFractionDigits: 3,\r\n                    maximumFractionDigits: 3,\r\n                  })}\r\n                </span>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AACA;AACA;AAAA;;;;;;;AA+BO,MAAM,yBAAyB,CAAC,EAAE,MAAM,EAAS;IACtD,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,SAAS,IAAA,+KAAS;IAExB,MAAM,aAAa,OAAO,WAAW;IAErC,qBACE,8OAAC,wIAAI;;0BACH,8OAAC,8IAAU;0BACT,cAAA,8OAAC,6IAAS;oBAAC,WAAU;;sCACnB,8OAAC;sCAAM,EAAE;;;;;;sCACT,8OAAC,0IAAK;4BACJ,WAAW,IAAA,yHAAE,EACX,uBACA,aAAa,iBAAiB;sCAG/B,aAAa,EAAE,cAAc,EAAE;;;;;;;;;;;;;;;;;0BAItC,8OAAC,+IAAW;gBAAC,WAAU;;kCAErB,8OAAC;kCACC,cAAA,8OAAC;4BAAG,WAAU;;gCACX,EAAE;gCAAgB;gCAAG,OAAO,WAAW;;;;;;;;;;;;kCAI5C,8OAAC,kJAAS;;;;;kCAGV,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAsB,EAAE;;;;;;0CACtC,8OAAC;gCAAI,WAAU;;oCACZ,OAAO,6BAA6B,KAAK,2BACxC,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAuB;;;;;;;0DAE5B,8OAAC;gDAAK,WAAU;0DACb,OAAO,6BAA6B,CAAC,OAAO,CAAC;;;;;;;;;;;;oCAInD,OAAO,0BAA0B,KAAK,2BACrC,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAoB;;;;;;;0DAEzB,8OAAC;gDAAK,WAAU;0DACb,OAAO,0BAA0B,CAAC,OAAO,CAAC;;;;;;;;;;;;oCAIhD,OAAO,+BAA+B,KAAK,2BAC1C,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAgB;;;;;;;0DAErB,8OAAC;gDAAK,WAAU;;oDACb,OAAO,+BAA+B,CAAC,cAAc,CACpD,QACA;wDAAE,uBAAuB;oDAAE;oDAC1B;oDAAI;;;;;;;;;;;;;oCAKZ,OAAO,4BAA4B,KAAK,2BACvC,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAqB;;;;;;;0DAE1B,8OAAC;gDAAK,WAAU;;oDACb,OAAO,4BAA4B,CAAC,cAAc,CAAC,QAAQ;wDAC1D,uBAAuB;oDACzB;oDAAI;oDAAI;;;;;;;;;;;;;;;;;;;;;;;;;kCAQlB,8OAAC,kJAAS;;;;;kCAGV,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAsB,EAAE;;;;;;0CACtC,8OAAC;gCAAI,WAAU;;oCACZ,OAAO,oBAAoB,KAAK,2BAC/B,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAkB;;;;;;;0DAEvB,8OAAC;gDAAK,WAAU;;oDACb,OAAO,oBAAoB,CAAC,cAAc,CAAC,QAAQ;wDAClD,uBAAuB;oDACzB;oDAAI;oDAAI;;;;;;;;;;;;;oCAKb,OAAO,6BAA6B,KAAK,2BACxC,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAgB;;;;;;;0DAErB,8OAAC;gDAAK,WAAU;;oDACb,OAAO,6BAA6B,CAAC,cAAc,CAAC,QAAQ;wDAC3D,uBAAuB;oDACzB;oDAAI;oDAAI;;;;;;;;;;;;;oCAKb,OAAO,iCAAiC,KAAK,2BAC5C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAqB;;;;;;;0DAE1B,8OAAC;gDAAK,WAAU;;oDACb,OAAO,iCAAiC,CAAC,cAAc,CACtD,QACA;wDAAE,uBAAuB;oDAAE;oDAC1B;oDAAI;;;;;;;;;;;;;oCAKZ,OAAO,UAAU,KAAK,2BACrB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAiB;;;;;;;0DAEtB,8OAAC;gDACC,WAAW,IAAA,yHAAE,EACX,4BACA,OAAO,UAAU,IAAI,IAAI,mBAAmB;0DAG7C,OAAO,UAAU,CAAC,cAAc,CAAC,QAAQ;oDACxC,uBAAuB;oDACvB,uBAAuB;gDACzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlB", "debugId": null}}, {"offset": {"line": 2052, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/slab/calculations/slab-flexural-negative-calculation.tsx"], "sourcesContent": ["import { SlabFlexuralResultCard } from '@atlas/components/common/atlas/module-detail/params-forms/slab/calculations/slab-flexural-result-card'\r\nimport { CustomProductSection } from '@atlas/components/common/atlas/product/custom-product-section'\r\nimport { ProductSummaryCard } from '@atlas/components/common/atlas/product/product-summary-card'\r\nimport { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport type { ModuleWithParamsSlab } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useModuleCalculation } from '@atlas/lib/mutation/modules/use-module-calculation'\r\nimport { useProductsByType } from '@atlas/lib/query/products/use-products-by-type'\r\nimport {\r\n  type SlabFlexuralNegativeCalculationInput,\r\n  slabFlexuralNegativeCalculationSchema,\r\n} from '@atlas/types/schemas/slab-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useMemo } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  module: ModuleWithParamsSlab\r\n  session: Session\r\n  projectId: Project['id']\r\n}\r\n\r\nexport const SlabFlexuralNegativeCalculation = ({\r\n  session,\r\n  module,\r\n  projectId,\r\n}: Props) => {\r\n  const t = useTranslations('forms.calculations.slab.flexural-negative')\r\n  const tAction = useTranslations('actions.calculations.slab.flexural-negative')\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  // Extract the execution input and result for negative flexural (M- / support verification)\r\n  const executionInput = module.flexuralVerifyExecutionInputMMinus\r\n  const calculationResult = module.flexuralVerifyExecutionResultMMinus\r\n\r\n  // Debug logging\r\n  console.log('🔍 Slab Flexural Negative Debug:', {\r\n    flexuralVerifyExecutionInputMMinus:\r\n      module.flexuralVerifyExecutionInputMMinus,\r\n    flexuralVerifyExecutionResultMMinus:\r\n      module.flexuralVerifyExecutionResultMMinus,\r\n    executionInput: executionInput,\r\n    calculationResult: calculationResult,\r\n  })\r\n\r\n  // Get FRC product name from module params\r\n  const frcProductName =\r\n    module.params?.slabFrcReinforcement?.frcReinforcementType\r\n\r\n  const {\r\n    data: products,\r\n    isError: errorGettingProducts,\r\n    isLoading: isLoadingProducts,\r\n  } = useProductsByType(session, 'SLAB', 0, 100)\r\n\r\n  // Find product by name from FRC reinforcement\r\n  const frcProduct = useMemo(\r\n    () => products?.content.find(p => p.name === frcProductName),\r\n    [frcProductName, products],\r\n  )\r\n\r\n  const form = useForm<SlabFlexuralNegativeCalculationInput>({\r\n    resolver: zodResolver(slabFlexuralNegativeCalculationSchema),\r\n    defaultValues: {\r\n      calculationType: 'FLEXURAL_VERIFY',\r\n      input: {\r\n        bendingMoment: executionInput?.bendingMoment ?? 40,\r\n        isSpanVerification: false,\r\n        productInput: {\r\n          id: executionInput?.product?.id ?? frcProduct?.id ?? '',\r\n          name: executionInput?.product?.name ?? frcProduct?.name ?? '',\r\n          sourceType:\r\n            executionInput?.product?.sourceType ??\r\n            (frcProduct ? 'DATABASE' : 'CUSTOM'),\r\n          frcSlabThickness:\r\n            executionInput?.product?.thickness ?? frcProduct?.thickness,\r\n          elasticModulus:\r\n            executionInput?.product?.elasticModulus ??\r\n            frcProduct?.elasticModulus,\r\n          cylindricCompressiveStrength:\r\n            executionInput?.product?.cylindricCompressiveStrength ??\r\n            frcProduct?.cylindricCompressiveStrength,\r\n          characteristicTensileStrength:\r\n            executionInput?.product?.characteristicTensileStrength ??\r\n            frcProduct?.characteristicTensileStrength,\r\n          specificWeight:\r\n            executionInput?.product?.specificWeight ??\r\n            frcProduct?.specificWeight,\r\n          adhesionToConcrete:\r\n            executionInput?.product?.adhesionToConcrete ??\r\n            frcProduct?.adhesionToConcrete,\r\n          fiberType:\r\n            executionInput?.product?.fiberType ??\r\n            frcProduct?.fiberType ??\r\n            'CARBON',\r\n        },\r\n      },\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useModuleCalculation(session.accessToken, {\r\n    onSuccess: () => {\r\n      toast.success(tAction('calculate.success'))\r\n    },\r\n    onError: error => {\r\n      toast.error(tAction('calculate.failure', { error: error.message }))\r\n    },\r\n  })\r\n\r\n  const handleFormSubmit = (body: SlabFlexuralNegativeCalculationInput) => {\r\n    mutate({ projectId, moduleId: module.id, body })\r\n  }\r\n\r\n  const productsOptions = [\r\n    ...(products?.content.map(m => ({\r\n      value: m.id,\r\n      label: m.name ?? t('product.unnamed'),\r\n    })) ?? []),\r\n    { value: 'custom', label: t('product.custom') },\r\n  ]\r\n\r\n  const [productId] = form.watch(['input.productInput.id'])\r\n\r\n  const selectedProduct = useMemo(\r\n    () => products?.content.find(p => p.id === productId),\r\n    [productId, products],\r\n  )\r\n\r\n  useEffect(() => {\r\n    if (!productId) {\r\n      return\r\n    }\r\n    if (productId === 'custom') {\r\n      form.setValue('input.productInput.sourceType', 'CUSTOM')\r\n      return\r\n    }\r\n\r\n    if (selectedProduct) {\r\n      form.setValue('input.productInput', {\r\n        id: selectedProduct.id,\r\n        name: selectedProduct.name ?? '',\r\n        sourceType: 'DATABASE',\r\n        frcSlabThickness: selectedProduct.thickness,\r\n        elasticModulus: selectedProduct.elasticModulus,\r\n        cylindricCompressiveStrength:\r\n          selectedProduct.cylindricCompressiveStrength,\r\n        characteristicTensileStrength:\r\n          selectedProduct.characteristicTensileStrength,\r\n        specificWeight: selectedProduct.specificWeight,\r\n        adhesionToConcrete: selectedProduct.adhesionToConcrete,\r\n        fiberType: selectedProduct.fiberType ?? 'CARBON',\r\n      })\r\n    }\r\n  }, [form, productId, selectedProduct])\r\n\r\n  return (\r\n    <div className=\"flex flex-col justify-center gap-4\">\r\n      <Form {...form}>\r\n        <form\r\n          className=\"space-y-4 rounded-md border p-4\"\r\n          onSubmit={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          <h1 className=\"text-3xl font-bold\">{t('heading')}</h1>\r\n          <p className=\"text-muted-foreground\">{t('description')}</p>\r\n\r\n          <Separator />\r\n\r\n          <h2 className=\"text-xl font-bold\">{t('product-section.title')}</h2>\r\n          <SelectFormInput\r\n            control={form.control}\r\n            name=\"input.productInput.id\"\r\n            options={productsOptions}\r\n            t={t}\r\n            loading={isLoadingProducts}\r\n            requestError={errorGettingProducts}\r\n            errorMessage={t('products.error')}\r\n          />\r\n\r\n          {productId === 'custom' && <CustomProductSection />}\r\n          {selectedProduct && <ProductSummaryCard product={selectedProduct} />}\r\n\r\n          <Separator />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.bendingMoment\"\r\n            t={t}\r\n          />\r\n\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full sm:w-auto\"\r\n            disabled={isPending}\r\n          >\r\n            {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n            {tCommon('calculate')}\r\n          </Button>\r\n        </form>\r\n      </Form>\r\n\r\n      {calculationResult && (\r\n        <SlabFlexuralResultCard result={calculationResult as any} />\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAIA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AAQO,MAAM,kCAAkC,CAAC,EAC9C,OAAO,EACP,MAAM,EACN,SAAS,EACH;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAEhC,2FAA2F;IAC3F,MAAM,iBAAiB,OAAO,kCAAkC;IAChE,MAAM,oBAAoB,OAAO,mCAAmC;IAEpE,gBAAgB;IAChB,QAAQ,GAAG,CAAC,oCAAoC;QAC9C,oCACE,OAAO,kCAAkC;QAC3C,qCACE,OAAO,mCAAmC;QAC5C,gBAAgB;QAChB,mBAAmB;IACrB;IAEA,0CAA0C;IAC1C,MAAM,iBACJ,OAAO,MAAM,EAAE,sBAAsB;IAEvC,MAAM,EACJ,MAAM,QAAQ,EACd,SAAS,oBAAoB,EAC7B,WAAW,iBAAiB,EAC7B,GAAG,IAAA,sLAAiB,EAAC,SAAS,QAAQ,GAAG;IAE1C,8CAA8C;IAC9C,MAAM,aAAa,IAAA,gNAAO,EACxB,IAAM,UAAU,QAAQ,KAAK,CAAA,IAAK,EAAE,IAAI,KAAK,iBAC7C;QAAC;QAAgB;KAAS;IAG5B,MAAM,OAAO,IAAA,yKAAO,EAAuC;QACzD,UAAU,IAAA,6KAAW,EAAC,gLAAqC;QAC3D,eAAe;YACb,iBAAiB;YACjB,OAAO;gBACL,eAAe,gBAAgB,iBAAiB;gBAChD,oBAAoB;gBACpB,cAAc;oBACZ,IAAI,gBAAgB,SAAS,MAAM,YAAY,MAAM;oBACrD,MAAM,gBAAgB,SAAS,QAAQ,YAAY,QAAQ;oBAC3D,YACE,gBAAgB,SAAS,cACzB,CAAC,aAAa,aAAa,QAAQ;oBACrC,kBACE,gBAAgB,SAAS,aAAa,YAAY;oBACpD,gBACE,gBAAgB,SAAS,kBACzB,YAAY;oBACd,8BACE,gBAAgB,SAAS,gCACzB,YAAY;oBACd,+BACE,gBAAgB,SAAS,iCACzB,YAAY;oBACd,gBACE,gBAAgB,SAAS,kBACzB,YAAY;oBACd,oBACE,gBAAgB,SAAS,sBACzB,YAAY;oBACd,WACE,gBAAgB,SAAS,aACzB,YAAY,aACZ;gBACJ;YACF;QACF;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,0LAAoB,EAAC,QAAQ,WAAW,EAAE;QACtE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;QACxB;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,qBAAqB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAClE;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAO;YAAE;YAAW,UAAU,OAAO,EAAE;YAAE;QAAK;IAChD;IAEA,MAAM,kBAAkB;WAClB,UAAU,QAAQ,IAAI,CAAA,IAAK,CAAC;gBAC9B,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,IAAI,IAAI,EAAE;YACrB,CAAC,MAAM,EAAE;QACT;YAAE,OAAO;YAAU,OAAO,EAAE;QAAkB;KAC/C;IAED,MAAM,CAAC,UAAU,GAAG,KAAK,KAAK,CAAC;QAAC;KAAwB;IAExD,MAAM,kBAAkB,IAAA,gNAAO,EAC7B,IAAM,UAAU,QAAQ,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK,YAC3C;QAAC;QAAW;KAAS;IAGvB,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,WAAW;YACd;QACF;QACA,IAAI,cAAc,UAAU;YAC1B,KAAK,QAAQ,CAAC,iCAAiC;YAC/C;QACF;QAEA,IAAI,iBAAiB;YACnB,KAAK,QAAQ,CAAC,sBAAsB;gBAClC,IAAI,gBAAgB,EAAE;gBACtB,MAAM,gBAAgB,IAAI,IAAI;gBAC9B,YAAY;gBACZ,kBAAkB,gBAAgB,SAAS;gBAC3C,gBAAgB,gBAAgB,cAAc;gBAC9C,8BACE,gBAAgB,4BAA4B;gBAC9C,+BACE,gBAAgB,6BAA6B;gBAC/C,gBAAgB,gBAAgB,cAAc;gBAC9C,oBAAoB,gBAAgB,kBAAkB;gBACtD,WAAW,gBAAgB,SAAS,IAAI;YAC1C;QACF;IACF,GAAG;QAAC;QAAM;QAAW;KAAgB;IAErC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,wIAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,8OAAC;oBACC,WAAU;oBACV,UAAU,KAAK,YAAY,CAAC;;sCAE5B,8OAAC;4BAAG,WAAU;sCAAsB,EAAE;;;;;;sCACtC,8OAAC;4BAAE,WAAU;sCAAyB,EAAE;;;;;;sCAExC,8OAAC,kJAAS;;;;;sCAEV,8OAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS;4BACT,GAAG;4BACH,SAAS;4BACT,cAAc;4BACd,cAAc,EAAE;;;;;;wBAGjB,cAAc,0BAAY,8OAAC,wMAAoB;;;;;wBAC/C,iCAAmB,8OAAC,oMAAkB;4BAAC,SAAS;;;;;;sCAEjD,8OAAC,kJAAS;;;;;sCAEV,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAGL,8OAAC,4IAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU;;gCAET,2BAAa,8OAAC,oOAAW;oCAAC,WAAU;;;;;;gCACpC,QAAQ;;;;;;;;;;;;;;;;;;YAKd,mCACC,8OAAC,oQAAsB;gBAAC,QAAQ;;;;;;;;;;;;AAIxC", "debugId": null}}, {"offset": {"line": 2322, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/slab/calculations/slab-flexural-positive-calculation.tsx"], "sourcesContent": ["import { SlabFlexuralResultCard } from '@atlas/components/common/atlas/module-detail/params-forms/slab/calculations/slab-flexural-result-card'\r\nimport { CustomProductSection } from '@atlas/components/common/atlas/product/custom-product-section'\r\nimport { ProductSummaryCard } from '@atlas/components/common/atlas/product/product-summary-card'\r\nimport { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport type { ModuleWithParamsSlab } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useModuleCalculation } from '@atlas/lib/mutation/modules/use-module-calculation'\r\nimport { useProductsByType } from '@atlas/lib/query/products/use-products-by-type'\r\nimport {\r\n  type SlabFlexuralPositiveCalculationInput,\r\n  slabFlexuralPositiveCalculationSchema,\r\n} from '@atlas/types/schemas/slab-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useMemo } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  module: ModuleWithParamsSlab\r\n  session: Session\r\n  projectId: Project['id']\r\n}\r\n\r\nexport const SlabFlexuralPositiveCalculation = ({\r\n  session,\r\n  module,\r\n  projectId,\r\n}: Props) => {\r\n  const t = useTranslations('forms.calculations.slab.flexural-positive')\r\n  const tAction = useTranslations('actions.calculations.slab.flexural-positive')\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  // Extract the execution input and result for positive flexural (M+ / span verification)\r\n  const executionInput = module.flexuralVerifyExecutionInputMPlus\r\n  const calculationResult = module.flexuralVerifyExecutionResultMPlus\r\n\r\n  // Debug logging\r\n  console.log('🔍 Slab Flexural Positive Debug:', {\r\n    flexuralVerifyExecutionInputMPlus: module.flexuralVerifyExecutionInputMPlus,\r\n    flexuralVerifyExecutionResultMPlus:\r\n      module.flexuralVerifyExecutionResultMPlus,\r\n    executionInput: executionInput,\r\n    calculationResult: calculationResult,\r\n  })\r\n\r\n  // Get FRC product name from module params\r\n  const frcProductName =\r\n    module.params?.slabFrcReinforcement?.frcReinforcementType\r\n\r\n  const {\r\n    data: products,\r\n    isError: errorGettingProducts,\r\n    isLoading: isLoadingProducts,\r\n  } = useProductsByType(session, 'SLAB', 0, 100)\r\n\r\n  // Find product by name from FRC reinforcement\r\n  const frcProduct = useMemo(\r\n    () => products?.content.find(p => p.name === frcProductName),\r\n    [frcProductName, products],\r\n  )\r\n\r\n  const form = useForm<SlabFlexuralPositiveCalculationInput>({\r\n    resolver: zodResolver(slabFlexuralPositiveCalculationSchema),\r\n    defaultValues: {\r\n      calculationType: 'FLEXURAL_VERIFY',\r\n      input: {\r\n        bendingMoment: executionInput?.bendingMoment ?? 50,\r\n        isSpanVerification: true,\r\n        productInput: {\r\n          id: executionInput?.product?.id ?? frcProduct?.id ?? '',\r\n          name: executionInput?.product?.name ?? frcProduct?.name ?? '',\r\n          sourceType:\r\n            executionInput?.product?.sourceType ??\r\n            (frcProduct ? 'DATABASE' : 'CUSTOM'),\r\n          frcSlabThickness:\r\n            executionInput?.product?.thickness ?? frcProduct?.thickness,\r\n          elasticModulus:\r\n            executionInput?.product?.elasticModulus ??\r\n            frcProduct?.elasticModulus,\r\n          cylindricCompressiveStrength:\r\n            executionInput?.product?.cylindricCompressiveStrength ??\r\n            frcProduct?.cylindricCompressiveStrength,\r\n          characteristicTensileStrength:\r\n            executionInput?.product?.characteristicTensileStrength ??\r\n            frcProduct?.characteristicTensileStrength,\r\n          specificWeight:\r\n            executionInput?.product?.specificWeight ??\r\n            frcProduct?.specificWeight,\r\n          adhesionToConcrete:\r\n            executionInput?.product?.adhesionToConcrete ??\r\n            frcProduct?.adhesionToConcrete,\r\n          fiberType:\r\n            executionInput?.product?.fiberType ??\r\n            frcProduct?.fiberType ??\r\n            'CARBON',\r\n        },\r\n      },\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useModuleCalculation(session.accessToken, {\r\n    onSuccess: () => {\r\n      toast.success(tAction('calculate.success'))\r\n    },\r\n    onError: error => {\r\n      toast.error(tAction('calculate.failure', { error: error.message }))\r\n    },\r\n  })\r\n\r\n  const handleFormSubmit = (body: SlabFlexuralPositiveCalculationInput) => {\r\n    mutate({ projectId, moduleId: module.id, body })\r\n  }\r\n\r\n  const productsOptions = [\r\n    ...(products?.content.map(m => ({\r\n      value: m.id,\r\n      label: m.name ?? t('product.unnamed'),\r\n    })) ?? []),\r\n    { value: 'custom', label: t('product.custom') },\r\n  ]\r\n\r\n  const [productId] = form.watch(['input.productInput.id'])\r\n\r\n  const selectedProduct = useMemo(\r\n    () => products?.content.find(p => p.id === productId),\r\n    [productId, products],\r\n  )\r\n\r\n  useEffect(() => {\r\n    if (!productId) {\r\n      return\r\n    }\r\n    if (productId === 'custom') {\r\n      form.setValue('input.productInput.sourceType', 'CUSTOM')\r\n      return\r\n    }\r\n\r\n    if (selectedProduct) {\r\n      form.setValue('input.productInput', {\r\n        id: selectedProduct.id,\r\n        name: selectedProduct.name ?? '',\r\n        sourceType: 'DATABASE',\r\n        frcSlabThickness: selectedProduct.thickness,\r\n        elasticModulus: selectedProduct.elasticModulus,\r\n        cylindricCompressiveStrength:\r\n          selectedProduct.cylindricCompressiveStrength,\r\n        characteristicTensileStrength:\r\n          selectedProduct.characteristicTensileStrength,\r\n        specificWeight: selectedProduct.specificWeight,\r\n        adhesionToConcrete: selectedProduct.adhesionToConcrete,\r\n        fiberType: selectedProduct.fiberType ?? 'CARBON',\r\n      })\r\n    }\r\n  }, [form, productId, selectedProduct])\r\n\r\n  return (\r\n    <div className=\"flex flex-col justify-center gap-4\">\r\n      <Form {...form}>\r\n        <form\r\n          className=\"space-y-4 rounded-md border p-4\"\r\n          onSubmit={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          <h1 className=\"text-3xl font-bold\">{t('heading')}</h1>\r\n          <p className=\"text-muted-foreground\">{t('description')}</p>\r\n\r\n          <Separator />\r\n\r\n          <h2 className=\"text-xl font-bold\">{t('product-section.title')}</h2>\r\n          <SelectFormInput\r\n            control={form.control}\r\n            name=\"input.productInput.id\"\r\n            options={productsOptions}\r\n            t={t}\r\n            loading={isLoadingProducts}\r\n            requestError={errorGettingProducts}\r\n            errorMessage={t('products.error')}\r\n          />\r\n\r\n          {productId === 'custom' && <CustomProductSection />}\r\n          {selectedProduct && <ProductSummaryCard product={selectedProduct} />}\r\n\r\n          <Separator />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.bendingMoment\"\r\n            t={t}\r\n          />\r\n\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full sm:w-auto\"\r\n            disabled={isPending}\r\n          >\r\n            {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n            {tCommon('calculate')}\r\n          </Button>\r\n        </form>\r\n      </Form>\r\n\r\n      {calculationResult && (\r\n        <SlabFlexuralResultCard result={calculationResult as any} />\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAIA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AAQO,MAAM,kCAAkC,CAAC,EAC9C,OAAO,EACP,MAAM,EACN,SAAS,EACH;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAEhC,wFAAwF;IACxF,MAAM,iBAAiB,OAAO,iCAAiC;IAC/D,MAAM,oBAAoB,OAAO,kCAAkC;IAEnE,gBAAgB;IAChB,QAAQ,GAAG,CAAC,oCAAoC;QAC9C,mCAAmC,OAAO,iCAAiC;QAC3E,oCACE,OAAO,kCAAkC;QAC3C,gBAAgB;QAChB,mBAAmB;IACrB;IAEA,0CAA0C;IAC1C,MAAM,iBACJ,OAAO,MAAM,EAAE,sBAAsB;IAEvC,MAAM,EACJ,MAAM,QAAQ,EACd,SAAS,oBAAoB,EAC7B,WAAW,iBAAiB,EAC7B,GAAG,IAAA,sLAAiB,EAAC,SAAS,QAAQ,GAAG;IAE1C,8CAA8C;IAC9C,MAAM,aAAa,IAAA,gNAAO,EACxB,IAAM,UAAU,QAAQ,KAAK,CAAA,IAAK,EAAE,IAAI,KAAK,iBAC7C;QAAC;QAAgB;KAAS;IAG5B,MAAM,OAAO,IAAA,yKAAO,EAAuC;QACzD,UAAU,IAAA,6KAAW,EAAC,gLAAqC;QAC3D,eAAe;YACb,iBAAiB;YACjB,OAAO;gBACL,eAAe,gBAAgB,iBAAiB;gBAChD,oBAAoB;gBACpB,cAAc;oBACZ,IAAI,gBAAgB,SAAS,MAAM,YAAY,MAAM;oBACrD,MAAM,gBAAgB,SAAS,QAAQ,YAAY,QAAQ;oBAC3D,YACE,gBAAgB,SAAS,cACzB,CAAC,aAAa,aAAa,QAAQ;oBACrC,kBACE,gBAAgB,SAAS,aAAa,YAAY;oBACpD,gBACE,gBAAgB,SAAS,kBACzB,YAAY;oBACd,8BACE,gBAAgB,SAAS,gCACzB,YAAY;oBACd,+BACE,gBAAgB,SAAS,iCACzB,YAAY;oBACd,gBACE,gBAAgB,SAAS,kBACzB,YAAY;oBACd,oBACE,gBAAgB,SAAS,sBACzB,YAAY;oBACd,WACE,gBAAgB,SAAS,aACzB,YAAY,aACZ;gBACJ;YACF;QACF;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,0LAAoB,EAAC,QAAQ,WAAW,EAAE;QACtE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;QACxB;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,qBAAqB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAClE;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAO;YAAE;YAAW,UAAU,OAAO,EAAE;YAAE;QAAK;IAChD;IAEA,MAAM,kBAAkB;WAClB,UAAU,QAAQ,IAAI,CAAA,IAAK,CAAC;gBAC9B,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,IAAI,IAAI,EAAE;YACrB,CAAC,MAAM,EAAE;QACT;YAAE,OAAO;YAAU,OAAO,EAAE;QAAkB;KAC/C;IAED,MAAM,CAAC,UAAU,GAAG,KAAK,KAAK,CAAC;QAAC;KAAwB;IAExD,MAAM,kBAAkB,IAAA,gNAAO,EAC7B,IAAM,UAAU,QAAQ,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK,YAC3C;QAAC;QAAW;KAAS;IAGvB,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,WAAW;YACd;QACF;QACA,IAAI,cAAc,UAAU;YAC1B,KAAK,QAAQ,CAAC,iCAAiC;YAC/C;QACF;QAEA,IAAI,iBAAiB;YACnB,KAAK,QAAQ,CAAC,sBAAsB;gBAClC,IAAI,gBAAgB,EAAE;gBACtB,MAAM,gBAAgB,IAAI,IAAI;gBAC9B,YAAY;gBACZ,kBAAkB,gBAAgB,SAAS;gBAC3C,gBAAgB,gBAAgB,cAAc;gBAC9C,8BACE,gBAAgB,4BAA4B;gBAC9C,+BACE,gBAAgB,6BAA6B;gBAC/C,gBAAgB,gBAAgB,cAAc;gBAC9C,oBAAoB,gBAAgB,kBAAkB;gBACtD,WAAW,gBAAgB,SAAS,IAAI;YAC1C;QACF;IACF,GAAG;QAAC;QAAM;QAAW;KAAgB;IAErC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,wIAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,8OAAC;oBACC,WAAU;oBACV,UAAU,KAAK,YAAY,CAAC;;sCAE5B,8OAAC;4BAAG,WAAU;sCAAsB,EAAE;;;;;;sCACtC,8OAAC;4BAAE,WAAU;sCAAyB,EAAE;;;;;;sCAExC,8OAAC,kJAAS;;;;;sCAEV,8OAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS;4BACT,GAAG;4BACH,SAAS;4BACT,cAAc;4BACd,cAAc,EAAE;;;;;;wBAGjB,cAAc,0BAAY,8OAAC,wMAAoB;;;;;wBAC/C,iCAAmB,8OAAC,oMAAkB;4BAAC,SAAS;;;;;;sCAEjD,8OAAC,kJAAS;;;;;sCAEV,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAGL,8OAAC,4IAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU;;gCAET,2BAAa,8OAAC,oOAAW;oCAAC,WAAU;;;;;;gCACpC,QAAQ;;;;;;;;;;;;;;;;;;YAKd,mCACC,8OAAC,oQAAsB;gBAAC,QAAQ;;;;;;;;;;;;AAIxC", "debugId": null}}, {"offset": {"line": 2592, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/slab/calculations/slab-interface-slip-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport type { SlabInterfaceSlipCalculationResult } from '@atlas/lib/api/modules/schemas/slab-params'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ninterface SlabInterfaceSlipResultCardProps {\r\n  result: SlabInterfaceSlipCalculationResult\r\n}\r\n\r\nexport function SlabInterfaceSlipResultCard({\r\n  result,\r\n}: SlabInterfaceSlipResultCardProps) {\r\n  const t = useTranslations('forms.calculations.slab.interface-slip-result')\r\n  const locale = useLocale()\r\n\r\n  const isVerified = result?.checkResult ?? false\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle className=\"flex items-center justify-between\">\r\n          <span>{t('title')}</span>\r\n          <Badge\r\n            className={cn(\r\n              'text-base px-3 py-1',\r\n              isVerified ? 'bg-green-600' : 'bg-red-600',\r\n            )}\r\n          >\r\n            {isVerified ? t('verified') : t('not-verified')}\r\n          </Badge>\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-6\">\r\n        {/* Input Section */}\r\n        {result.inputShearForce !== undefined &&\r\n          result.inputShearForce !== null && (\r\n            <div>\r\n              <span className=\"text-muted-foreground\">\r\n                {t('input-shear-force')}:\r\n              </span>\r\n              <span className=\"ml-2 font-medium text-lg\">\r\n                {result.inputShearForce.toLocaleString(locale, {\r\n                  maximumFractionDigits: 2,\r\n                })}{' '}\r\n                kN\r\n              </span>\r\n            </div>\r\n          )}\r\n\r\n        <Separator />\r\n\r\n        {/* Calculation Results */}\r\n        <div>\r\n          <h4 className=\"font-semibold mb-3\">{t('calculation-results')}</h4>\r\n          <div className=\"grid grid-cols-2 gap-4 text-sm\">\r\n            {result.negativeMomentAreaHomogenizationCoefficient !== undefined &&\r\n              result.negativeMomentAreaHomogenizationCoefficient !== null && (\r\n                <div>\r\n                  <span className=\"text-muted-foreground\">\r\n                    {t('homogenization-coefficient')}:\r\n                  </span>\r\n                  <span className=\"ml-2 font-medium\">\r\n                    {result.negativeMomentAreaHomogenizationCoefficient.toFixed(\r\n                      3,\r\n                    )}\r\n                  </span>\r\n                </div>\r\n              )}\r\n            {result.neutralAxisCompressedFlangeDistance !== undefined &&\r\n              result.neutralAxisCompressedFlangeDistance !== null && (\r\n                <div>\r\n                  <span className=\"text-muted-foreground\">\r\n                    {t('neutral-axis-distance')}:\r\n                  </span>\r\n                  <span className=\"ml-2 font-medium\">\r\n                    {result.neutralAxisCompressedFlangeDistance.toLocaleString(\r\n                      locale,\r\n                      { maximumFractionDigits: 2 },\r\n                    )}{' '}\r\n                    mm\r\n                  </span>\r\n                </div>\r\n              )}\r\n            {result.translationalEquilibrium !== undefined &&\r\n              result.translationalEquilibrium !== null && (\r\n                <div>\r\n                  <span className=\"text-muted-foreground\">\r\n                    {t('translational-equilibrium')}:\r\n                  </span>\r\n                  <span className=\"ml-2 font-medium\">\r\n                    {result.translationalEquilibrium.toExponential(4)}\r\n                  </span>\r\n                </div>\r\n              )}\r\n            {result.staticMoment !== undefined &&\r\n              result.staticMoment !== null && (\r\n                <div>\r\n                  <span className=\"text-muted-foreground\">\r\n                    {t('static-moment')}:\r\n                  </span>\r\n                  <span className=\"ml-2 font-medium\">\r\n                    {result.staticMoment.toLocaleString(locale, {\r\n                      maximumFractionDigits: 2,\r\n                    })}{' '}\r\n                    mm³\r\n                  </span>\r\n                </div>\r\n              )}\r\n            {result.neutralAxisInertiaMoment !== undefined &&\r\n              result.neutralAxisInertiaMoment !== null && (\r\n                <div>\r\n                  <span className=\"text-muted-foreground\">\r\n                    {t('inertia-moment')}:\r\n                  </span>\r\n                  <span className=\"ml-2 font-medium\">\r\n                    {result.neutralAxisInertiaMoment.toExponential(4)} mm⁴\r\n                  </span>\r\n                </div>\r\n              )}\r\n          </div>\r\n        </div>\r\n\r\n        <Separator />\r\n\r\n        {/* Interface Verification */}\r\n        <div>\r\n          <h4 className=\"font-semibold mb-3\">{t('interface-verification')}</h4>\r\n          <div className=\"grid grid-cols-2 gap-4 text-sm\">\r\n            {result.interfaceShearStress !== undefined &&\r\n              result.interfaceShearStress !== null && (\r\n                <div>\r\n                  <span className=\"text-muted-foreground\">\r\n                    {t('interface-shear-stress')}:\r\n                  </span>\r\n                  <span className=\"ml-2 font-medium text-lg\">\r\n                    {result.interfaceShearStress.toFixed(3)} MPa\r\n                  </span>\r\n                </div>\r\n              )}\r\n            {result.frcBondStrength !== undefined &&\r\n              result.frcBondStrength !== null && (\r\n                <div>\r\n                  <span className=\"text-muted-foreground\">\r\n                    {t('frc-bond-strength')}:\r\n                  </span>\r\n                  <span className=\"ml-2 font-medium text-lg\">\r\n                    {result.frcBondStrength.toFixed(3)} MPa\r\n                  </span>\r\n                </div>\r\n              )}\r\n            {result.concreteToConcreteFrictionCoefficient !== undefined &&\r\n              result.concreteToConcreteFrictionCoefficient !== null && (\r\n                <div>\r\n                  <span className=\"text-muted-foreground\">\r\n                    {t('friction-coefficient')}:\r\n                  </span>\r\n                  <span className=\"ml-2 font-medium\">\r\n                    {result.concreteToConcreteFrictionCoefficient.toFixed(2)}\r\n                  </span>\r\n                </div>\r\n              )}\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AAEA;AACA;AAAA;;;;;;;AAMO,SAAS,4BAA4B,EAC1C,MAAM,EAC2B;IACjC,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,SAAS,IAAA,+KAAS;IAExB,MAAM,aAAa,QAAQ,eAAe;IAE1C,qBACE,8OAAC,wIAAI;;0BACH,8OAAC,8IAAU;0BACT,cAAA,8OAAC,6IAAS;oBAAC,WAAU;;sCACnB,8OAAC;sCAAM,EAAE;;;;;;sCACT,8OAAC,0IAAK;4BACJ,WAAW,IAAA,yHAAE,EACX,uBACA,aAAa,iBAAiB;sCAG/B,aAAa,EAAE,cAAc,EAAE;;;;;;;;;;;;;;;;;0BAItC,8OAAC,+IAAW;gBAAC,WAAU;;oBAEpB,OAAO,eAAe,KAAK,aAC1B,OAAO,eAAe,KAAK,sBACzB,8OAAC;;0CACC,8OAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAqB;;;;;;;0CAE1B,8OAAC;gCAAK,WAAU;;oCACb,OAAO,eAAe,CAAC,cAAc,CAAC,QAAQ;wCAC7C,uBAAuB;oCACzB;oCAAI;oCAAI;;;;;;;;;;;;;kCAMhB,8OAAC,kJAAS;;;;;kCAGV,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAsB,EAAE;;;;;;0CACtC,8OAAC;gCAAI,WAAU;;oCACZ,OAAO,2CAA2C,KAAK,aACtD,OAAO,2CAA2C,KAAK,sBACrD,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAA8B;;;;;;;0DAEnC,8OAAC;gDAAK,WAAU;0DACb,OAAO,2CAA2C,CAAC,OAAO,CACzD;;;;;;;;;;;;oCAKT,OAAO,mCAAmC,KAAK,aAC9C,OAAO,mCAAmC,KAAK,sBAC7C,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAyB;;;;;;;0DAE9B,8OAAC;gDAAK,WAAU;;oDACb,OAAO,mCAAmC,CAAC,cAAc,CACxD,QACA;wDAAE,uBAAuB;oDAAE;oDAC1B;oDAAI;;;;;;;;;;;;;oCAKd,OAAO,wBAAwB,KAAK,aACnC,OAAO,wBAAwB,KAAK,sBAClC,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAA6B;;;;;;;0DAElC,8OAAC;gDAAK,WAAU;0DACb,OAAO,wBAAwB,CAAC,aAAa,CAAC;;;;;;;;;;;;oCAItD,OAAO,YAAY,KAAK,aACvB,OAAO,YAAY,KAAK,sBACtB,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAiB;;;;;;;0DAEtB,8OAAC;gDAAK,WAAU;;oDACb,OAAO,YAAY,CAAC,cAAc,CAAC,QAAQ;wDAC1C,uBAAuB;oDACzB;oDAAI;oDAAI;;;;;;;;;;;;;oCAKf,OAAO,wBAAwB,KAAK,aACnC,OAAO,wBAAwB,KAAK,sBAClC,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAkB;;;;;;;0DAEvB,8OAAC;gDAAK,WAAU;;oDACb,OAAO,wBAAwB,CAAC,aAAa,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;;;;;;;kCAO9D,8OAAC,kJAAS;;;;;kCAGV,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAsB,EAAE;;;;;;0CACtC,8OAAC;gCAAI,WAAU;;oCACZ,OAAO,oBAAoB,KAAK,aAC/B,OAAO,oBAAoB,KAAK,sBAC9B,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAA0B;;;;;;;0DAE/B,8OAAC;gDAAK,WAAU;;oDACb,OAAO,oBAAoB,CAAC,OAAO,CAAC;oDAAG;;;;;;;;;;;;;oCAI/C,OAAO,eAAe,KAAK,aAC1B,OAAO,eAAe,KAAK,sBACzB,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAqB;;;;;;;0DAE1B,8OAAC;gDAAK,WAAU;;oDACb,OAAO,eAAe,CAAC,OAAO,CAAC;oDAAG;;;;;;;;;;;;;oCAI1C,OAAO,qCAAqC,KAAK,aAChD,OAAO,qCAAqC,KAAK,sBAC/C,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAwB;;;;;;;0DAE7B,8OAAC;gDAAK,WAAU;0DACb,OAAO,qCAAqC,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1E", "debugId": null}}, {"offset": {"line": 2995, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/slab/calculations/slab-interface-slip-calculation.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Card, CardContent } from '@atlas/components/ui/card'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport type { ModuleWithParamsSlab } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useModuleCalculation } from '@atlas/lib/mutation/modules/use-module-calculation'\r\nimport { useProductsByType } from '@atlas/lib/query/products/use-products-by-type'\r\nimport {\r\n  type SlabInterfaceSlipCalculationInput,\r\n  slabInterfaceSlipCalculationSchema,\r\n} from '@atlas/types/schemas/slab-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useMemo } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\nimport { SlabInterfaceSlipResultCard } from './slab-interface-slip-result-card'\r\n\r\ntype Props = {\r\n  module: ModuleWithParamsSlab\r\n  session: Session\r\n  projectId: Project['id']\r\n}\r\n\r\nexport const SlabInterfaceSlipCalculation = ({\r\n  session,\r\n  module,\r\n  projectId,\r\n}: Props) => {\r\n  const t = useTranslations('forms.calculations.slab.interface-slip')\r\n  const tAction = useTranslations('actions.calculations.slab.interface-slip')\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  const { mutate, isPending } = useModuleCalculation(session.accessToken)\r\n\r\n  // Get results from module\r\n  const { interfaceSlipVerifyExecutionInput, interfaceSlipCalculationResult } =\r\n    module\r\n\r\n  // Debug logging\r\n  console.log('🔍 Slab Interface Slip Debug:', {\r\n    interfaceSlipVerifyExecutionInput: interfaceSlipVerifyExecutionInput,\r\n    interfaceSlipCalculationResult: interfaceSlipCalculationResult,\r\n    'module keys': Object.keys(module),\r\n  })\r\n\r\n  // Get FRC product name from module params\r\n  const frcProductName =\r\n    module.params?.slabFrcReinforcement?.frcReinforcementType\r\n\r\n  const {\r\n    data: products,\r\n    isError: errorGettingProducts,\r\n    isLoading: isLoadingProducts,\r\n  } = useProductsByType(session, 'SLAB', 0, 100)\r\n\r\n  // Find product by name from FRC reinforcement\r\n  const frcProduct = useMemo(\r\n    () => products?.content.find(p => p.name === frcProductName),\r\n    [frcProductName, products],\r\n  )\r\n\r\n  const form = useForm<SlabInterfaceSlipCalculationInput>({\r\n    resolver: zodResolver(slabInterfaceSlipCalculationSchema),\r\n    defaultValues: {\r\n      calculationType: 'INTERFACE_SLIP_VERIFY',\r\n      input: {\r\n        shearForce: interfaceSlipVerifyExecutionInput?.shearForce ?? 30,\r\n        productInput: {\r\n          id:\r\n            interfaceSlipVerifyExecutionInput?.product?.id ??\r\n            frcProduct?.id ??\r\n            '',\r\n          name:\r\n            interfaceSlipVerifyExecutionInput?.product?.name ??\r\n            frcProduct?.name ??\r\n            '',\r\n          sourceType:\r\n            interfaceSlipVerifyExecutionInput?.product?.sourceType ??\r\n            (frcProduct ? 'DATABASE' : 'CUSTOM'),\r\n          frcSlabThickness:\r\n            interfaceSlipVerifyExecutionInput?.product?.thickness ??\r\n            frcProduct?.thickness,\r\n          elasticModulus:\r\n            interfaceSlipVerifyExecutionInput?.product?.elasticModulus ??\r\n            frcProduct?.elasticModulus,\r\n          cylindricCompressiveStrength:\r\n            interfaceSlipVerifyExecutionInput?.product\r\n              ?.cylindricCompressiveStrength ??\r\n            frcProduct?.cylindricCompressiveStrength,\r\n          characteristicTensileStrength:\r\n            interfaceSlipVerifyExecutionInput?.product\r\n              ?.characteristicTensileStrength ??\r\n            frcProduct?.characteristicTensileStrength,\r\n          specificWeight:\r\n            interfaceSlipVerifyExecutionInput?.product?.specificWeight ??\r\n            frcProduct?.specificWeight,\r\n          adhesionToConcrete:\r\n            interfaceSlipVerifyExecutionInput?.product?.adhesionToConcrete ??\r\n            frcProduct?.adhesionToConcrete,\r\n          fiberType:\r\n            interfaceSlipVerifyExecutionInput?.product?.fiberType ??\r\n            frcProduct?.fiberType ??\r\n            'CARBON',\r\n        },\r\n      },\r\n    },\r\n  })\r\n\r\n  const productsOptions = [\r\n    ...(products?.content.map(m => ({\r\n      value: m.id,\r\n      label: m.name ?? t('product.unnamed'),\r\n    })) ?? []),\r\n    { value: 'custom', label: t('product.custom') },\r\n  ]\r\n\r\n  const [productId] = form.watch(['input.productInput.id'])\r\n  const selectedProduct = useMemo(\r\n    () => products?.content.find(p => p.id === productId),\r\n    [productId, products],\r\n  )\r\n\r\n  useEffect(() => {\r\n    if (!productId || productId === 'custom') {\r\n      return\r\n    }\r\n\r\n    if (selectedProduct) {\r\n      form.setValue('input.productInput.id', selectedProduct.id)\r\n      form.setValue('input.productInput.name', selectedProduct.name || '')\r\n      form.setValue('input.productInput.sourceType', 'DATABASE')\r\n\r\n      // Auto-populate product properties - use correct property names from Product schema\r\n      if (selectedProduct.thickness) {\r\n        form.setValue(\r\n          'input.productInput.frcSlabThickness',\r\n          selectedProduct.thickness,\r\n        )\r\n      }\r\n      if (selectedProduct.elasticModulus) {\r\n        form.setValue(\r\n          'input.productInput.elasticModulus',\r\n          selectedProduct.elasticModulus,\r\n        )\r\n      }\r\n      if (\r\n        selectedProduct.characteristicCylindricalCompressiveStrengthFrcMatrix\r\n      ) {\r\n        form.setValue(\r\n          'input.productInput.cylindricCompressiveStrength',\r\n          selectedProduct.characteristicCylindricalCompressiveStrengthFrcMatrix,\r\n        )\r\n      }\r\n      if (selectedProduct.characteristicTensileStrength) {\r\n        form.setValue(\r\n          'input.productInput.characteristicTensileStrength',\r\n          selectedProduct.characteristicTensileStrength,\r\n        )\r\n      }\r\n      if (selectedProduct.specificWeight) {\r\n        form.setValue(\r\n          'input.productInput.specificWeight',\r\n          selectedProduct.specificWeight,\r\n        )\r\n      }\r\n      if (selectedProduct.adhesionToConcrete) {\r\n        form.setValue(\r\n          'input.productInput.adhesionToConcrete',\r\n          selectedProduct.adhesionToConcrete,\r\n        )\r\n      }\r\n      if (selectedProduct.fiberType) {\r\n        form.setValue('input.productInput.fiberType', selectedProduct.fiberType)\r\n      }\r\n    }\r\n  }, [form, productId, selectedProduct])\r\n\r\n  const onSubmit = (data: SlabInterfaceSlipCalculationInput) => {\r\n    mutate(\r\n      {\r\n        projectId,\r\n        moduleId: module.id,\r\n        body: data,\r\n      },\r\n      {\r\n        onSuccess: () => {\r\n          toast.success(tAction('calculate.success'))\r\n        },\r\n        onError: (error: any) => {\r\n          toast.error(tAction('calculate.failure', { error: error.message }))\r\n        },\r\n      },\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <Card>\r\n        <CardContent className=\"pt-6\">\r\n          <Form {...form}>\r\n            <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-4\">\r\n              <h1 className=\"text-3xl font-bold\">{t('heading')}</h1>\r\n\r\n              <SelectFormInput\r\n                control={form.control}\r\n                name=\"input.productInput.id\"\r\n                options={productsOptions}\r\n                t={t}\r\n                loading={isLoadingProducts}\r\n                requestError={errorGettingProducts}\r\n              />\r\n\r\n              <NumberFormInput\r\n                control={form.control}\r\n                name=\"input.shearForce\"\r\n                t={t}\r\n              />\r\n\r\n              <Button type=\"submit\" disabled={isPending}>\r\n                {isPending ? tAction('calculating') : tCommon('calculate')}\r\n              </Button>\r\n            </form>\r\n          </Form>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {interfaceSlipCalculationResult && (\r\n        <SlabInterfaceSlipResultCard result={interfaceSlipCalculationResult} />\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAIA;AAEA;AACA;AACA;AACA;AACA;AArBA;;;;;;;;;;;;;;;;AA6BO,MAAM,+BAA+B,CAAC,EAC3C,OAAO,EACP,MAAM,EACN,SAAS,EACH;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAEhC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,0LAAoB,EAAC,QAAQ,WAAW;IAEtE,0BAA0B;IAC1B,MAAM,EAAE,iCAAiC,EAAE,8BAA8B,EAAE,GACzE;IAEF,gBAAgB;IAChB,QAAQ,GAAG,CAAC,iCAAiC;QAC3C,mCAAmC;QACnC,gCAAgC;QAChC,eAAe,OAAO,IAAI,CAAC;IAC7B;IAEA,0CAA0C;IAC1C,MAAM,iBACJ,OAAO,MAAM,EAAE,sBAAsB;IAEvC,MAAM,EACJ,MAAM,QAAQ,EACd,SAAS,oBAAoB,EAC7B,WAAW,iBAAiB,EAC7B,GAAG,IAAA,sLAAiB,EAAC,SAAS,QAAQ,GAAG;IAE1C,8CAA8C;IAC9C,MAAM,aAAa,IAAA,gNAAO,EACxB,IAAM,UAAU,QAAQ,KAAK,CAAA,IAAK,EAAE,IAAI,KAAK,iBAC7C;QAAC;QAAgB;KAAS;IAG5B,MAAM,OAAO,IAAA,yKAAO,EAAoC;QACtD,UAAU,IAAA,6KAAW,EAAC,6KAAkC;QACxD,eAAe;YACb,iBAAiB;YACjB,OAAO;gBACL,YAAY,mCAAmC,cAAc;gBAC7D,cAAc;oBACZ,IACE,mCAAmC,SAAS,MAC5C,YAAY,MACZ;oBACF,MACE,mCAAmC,SAAS,QAC5C,YAAY,QACZ;oBACF,YACE,mCAAmC,SAAS,cAC5C,CAAC,aAAa,aAAa,QAAQ;oBACrC,kBACE,mCAAmC,SAAS,aAC5C,YAAY;oBACd,gBACE,mCAAmC,SAAS,kBAC5C,YAAY;oBACd,8BACE,mCAAmC,SAC/B,gCACJ,YAAY;oBACd,+BACE,mCAAmC,SAC/B,iCACJ,YAAY;oBACd,gBACE,mCAAmC,SAAS,kBAC5C,YAAY;oBACd,oBACE,mCAAmC,SAAS,sBAC5C,YAAY;oBACd,WACE,mCAAmC,SAAS,aAC5C,YAAY,aACZ;gBACJ;YACF;QACF;IACF;IAEA,MAAM,kBAAkB;WAClB,UAAU,QAAQ,IAAI,CAAA,IAAK,CAAC;gBAC9B,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,IAAI,IAAI,EAAE;YACrB,CAAC,MAAM,EAAE;QACT;YAAE,OAAO;YAAU,OAAO,EAAE;QAAkB;KAC/C;IAED,MAAM,CAAC,UAAU,GAAG,KAAK,KAAK,CAAC;QAAC;KAAwB;IACxD,MAAM,kBAAkB,IAAA,gNAAO,EAC7B,IAAM,UAAU,QAAQ,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK,YAC3C;QAAC;QAAW;KAAS;IAGvB,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,aAAa,cAAc,UAAU;YACxC;QACF;QAEA,IAAI,iBAAiB;YACnB,KAAK,QAAQ,CAAC,yBAAyB,gBAAgB,EAAE;YACzD,KAAK,QAAQ,CAAC,2BAA2B,gBAAgB,IAAI,IAAI;YACjE,KAAK,QAAQ,CAAC,iCAAiC;YAE/C,oFAAoF;YACpF,IAAI,gBAAgB,SAAS,EAAE;gBAC7B,KAAK,QAAQ,CACX,uCACA,gBAAgB,SAAS;YAE7B;YACA,IAAI,gBAAgB,cAAc,EAAE;gBAClC,KAAK,QAAQ,CACX,qCACA,gBAAgB,cAAc;YAElC;YACA,IACE,gBAAgB,qDAAqD,EACrE;gBACA,KAAK,QAAQ,CACX,mDACA,gBAAgB,qDAAqD;YAEzE;YACA,IAAI,gBAAgB,6BAA6B,EAAE;gBACjD,KAAK,QAAQ,CACX,oDACA,gBAAgB,6BAA6B;YAEjD;YACA,IAAI,gBAAgB,cAAc,EAAE;gBAClC,KAAK,QAAQ,CACX,qCACA,gBAAgB,cAAc;YAElC;YACA,IAAI,gBAAgB,kBAAkB,EAAE;gBACtC,KAAK,QAAQ,CACX,yCACA,gBAAgB,kBAAkB;YAEtC;YACA,IAAI,gBAAgB,SAAS,EAAE;gBAC7B,KAAK,QAAQ,CAAC,gCAAgC,gBAAgB,SAAS;YACzE;QACF;IACF,GAAG;QAAC;QAAM;QAAW;KAAgB;IAErC,MAAM,WAAW,CAAC;QAChB,OACE;YACE;YACA,UAAU,OAAO,EAAE;YACnB,MAAM;QACR,GACA;YACE,WAAW;gBACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACxB;YACA,SAAS,CAAC;gBACR,iJAAK,CAAC,KAAK,CAAC,QAAQ,qBAAqB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAClE;QACF;IAEJ;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,wIAAI;0BACH,cAAA,8OAAC,+IAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC,wIAAI;wBAAE,GAAG,IAAI;kCACZ,cAAA,8OAAC;4BAAK,UAAU,KAAK,YAAY,CAAC;4BAAW,WAAU;;8CACrD,8OAAC;oCAAG,WAAU;8CAAsB,EAAE;;;;;;8CAEtC,8OAAC,kLAAe;oCACd,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,SAAS;oCACT,GAAG;oCACH,SAAS;oCACT,cAAc;;;;;;8CAGhB,8OAAC,kLAAe;oCACd,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,GAAG;;;;;;8CAGL,8OAAC,4IAAM;oCAAC,MAAK;oCAAS,UAAU;8CAC7B,YAAY,QAAQ,iBAAiB,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOvD,gDACC,8OAAC,kRAA2B;gBAAC,QAAQ;;;;;;;;;;;;AAI7C", "debugId": null}}, {"offset": {"line": 3230, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/slab/calculations/slab-shear-result-card.tsx"], "sourcesContent": ["import { Badge } from '@atlas/components/ui/badge'\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport type { SlabShearCalculationResult } from '@atlas/lib/api/modules/schemas/slab-params'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ninterface SlabShearResultCardProps {\r\n  result: SlabShearCalculationResult\r\n}\r\n\r\nexport function SlabShearResultCard({ result }: SlabShearResultCardProps) {\r\n  const t = useTranslations('forms.calculations.slab.shear-result')\r\n  const locale = useLocale()\r\n\r\n  const isVerified = result?.isShearVerificationSatisfied ?? false\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle className=\"flex items-center justify-between\">\r\n          <span>{t('title')}</span>\r\n          <Badge\r\n            className={cn(\r\n              'text-base px-3 py-1',\r\n              isVerified ? 'bg-green-600' : 'bg-red-600',\r\n            )}\r\n          >\r\n            {isVerified ? t('verified') : t('not-verified')}\r\n          </Badge>\r\n        </CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-6\">\r\n        {/* Input Section */}\r\n        <div>\r\n          <h4 className=\"font-semibold mb-3\">\r\n            {t('section-fill-type')}: {result.sectionFillType}\r\n          </h4>\r\n          {result.inputShearForce !== undefined &&\r\n            result.inputShearForce !== null && (\r\n              <div className=\"mt-2\">\r\n                <span className=\"text-muted-foreground\">\r\n                  {t('input-shear-force')}:\r\n                </span>\r\n                <span className=\"ml-2 font-medium text-lg\">\r\n                  {result.inputShearForce.toLocaleString(locale, {\r\n                    maximumFractionDigits: 2,\r\n                  })}{' '}\r\n                  kN\r\n                </span>\r\n              </div>\r\n            )}\r\n        </div>\r\n\r\n        <Separator />\r\n\r\n        {/* Unreinforced Section */}\r\n        <div>\r\n          <h4 className=\"font-semibold mb-3\">{t('unreinforced-section')}</h4>\r\n          <div className=\"grid grid-cols-2 gap-4 text-sm\">\r\n            {result.unreinforcedSectionEffectiveDepth !== undefined &&\r\n              result.unreinforcedSectionEffectiveDepth !== null && (\r\n                <div>\r\n                  <span className=\"text-muted-foreground\">\r\n                    {t('effective-depth')}:\r\n                  </span>\r\n                  <span className=\"ml-2 font-medium\">\r\n                    {result.unreinforcedSectionEffectiveDepth.toLocaleString(\r\n                      locale,\r\n                      { maximumFractionDigits: 2 },\r\n                    )}{' '}\r\n                    mm\r\n                  </span>\r\n                </div>\r\n              )}\r\n            {result.unreinforcedTensionAreaMinimumWidth !== undefined &&\r\n              result.unreinforcedTensionAreaMinimumWidth !== null && (\r\n                <div>\r\n                  <span className=\"text-muted-foreground\">\r\n                    {t('minimum-width')}:\r\n                  </span>\r\n                  <span className=\"ml-2 font-medium\">\r\n                    {result.unreinforcedTensionAreaMinimumWidth.toLocaleString(\r\n                      locale,\r\n                      { maximumFractionDigits: 2 },\r\n                    )}{' '}\r\n                    mm\r\n                  </span>\r\n                </div>\r\n              )}\r\n            {result.unreinforcedSizeEffectFactor !== undefined &&\r\n              result.unreinforcedSizeEffectFactor !== null && (\r\n                <div>\r\n                  <span className=\"text-muted-foreground\">\r\n                    {t('size-effect-factor')}:\r\n                  </span>\r\n                  <span className=\"ml-2 font-medium\">\r\n                    {result.unreinforcedSizeEffectFactor.toFixed(3)}\r\n                  </span>\r\n                </div>\r\n              )}\r\n            {result.unreinforcedCoefficient !== undefined &&\r\n              result.unreinforcedCoefficient !== null && (\r\n                <div>\r\n                  <span className=\"text-muted-foreground\">\r\n                    {t('coefficient')}:\r\n                  </span>\r\n                  <span className=\"ml-2 font-medium\">\r\n                    {result.unreinforcedCoefficient.toFixed(4)} MPa\r\n                  </span>\r\n                </div>\r\n              )}\r\n            {result.unreinforcedTensileLongitudinalReinforcementArea !==\r\n              undefined &&\r\n              result.unreinforcedTensileLongitudinalReinforcementArea !==\r\n                null && (\r\n                <div>\r\n                  <span className=\"text-muted-foreground\">\r\n                    {t('reinforcement-area')}:\r\n                  </span>\r\n                  <span className=\"ml-2 font-medium\">\r\n                    {result.unreinforcedTensileLongitudinalReinforcementArea.toLocaleString(\r\n                      locale,\r\n                      { maximumFractionDigits: 2 },\r\n                    )}{' '}\r\n                    mm²\r\n                  </span>\r\n                </div>\r\n              )}\r\n            {result.unreinforcedTensileReinforcementRatio !== undefined &&\r\n              result.unreinforcedTensileReinforcementRatio !== null && (\r\n                <div>\r\n                  <span className=\"text-muted-foreground\">\r\n                    {t('reinforcement-ratio')}:\r\n                  </span>\r\n                  <span className=\"ml-2 font-medium\">\r\n                    {(\r\n                      result.unreinforcedTensileReinforcementRatio * 100\r\n                    ).toFixed(4)}\r\n                    %\r\n                  </span>\r\n                </div>\r\n              )}\r\n            {result.unreinforcedShearCapacity !== undefined &&\r\n              result.unreinforcedShearCapacity !== null && (\r\n                <div className=\"col-span-2\">\r\n                  <span className=\"text-muted-foreground font-semibold\">\r\n                    {t('shear-capacity')}:\r\n                  </span>\r\n                  <span className=\"ml-2 font-medium text-lg\">\r\n                    {result.unreinforcedShearCapacity.toLocaleString(locale, {\r\n                      maximumFractionDigits: 2,\r\n                    })}{' '}\r\n                    kN\r\n                  </span>\r\n                </div>\r\n              )}\r\n          </div>\r\n        </div>\r\n\r\n        <Separator />\r\n\r\n        {/* Reinforced Section */}\r\n        <div>\r\n          <h4 className=\"font-semibold mb-3\">{t('reinforced-section')}</h4>\r\n          <div className=\"grid grid-cols-2 gap-4 text-sm\">\r\n            {result.reinforcedSectionEffectiveDepth !== undefined &&\r\n              result.reinforcedSectionEffectiveDepth !== null && (\r\n                <div>\r\n                  <span className=\"text-muted-foreground\">\r\n                    {t('effective-depth')}:\r\n                  </span>\r\n                  <span className=\"ml-2 font-medium\">\r\n                    {result.reinforcedSectionEffectiveDepth.toLocaleString(\r\n                      locale,\r\n                      { maximumFractionDigits: 2 },\r\n                    )}{' '}\r\n                    mm\r\n                  </span>\r\n                </div>\r\n              )}\r\n            {result.reinforcedTensionAreaMinimumWidth !== undefined &&\r\n              result.reinforcedTensionAreaMinimumWidth !== null && (\r\n                <div>\r\n                  <span className=\"text-muted-foreground\">\r\n                    {t('minimum-width')}:\r\n                  </span>\r\n                  <span className=\"ml-2 font-medium\">\r\n                    {result.reinforcedTensionAreaMinimumWidth.toLocaleString(\r\n                      locale,\r\n                      { maximumFractionDigits: 2 },\r\n                    )}{' '}\r\n                    mm\r\n                  </span>\r\n                </div>\r\n              )}\r\n            {result.reinforcedSizeEffectFactor !== undefined &&\r\n              result.reinforcedSizeEffectFactor !== null && (\r\n                <div>\r\n                  <span className=\"text-muted-foreground\">\r\n                    {t('size-effect-factor')}:\r\n                  </span>\r\n                  <span className=\"ml-2 font-medium\">\r\n                    {result.reinforcedSizeEffectFactor.toFixed(3)}\r\n                  </span>\r\n                </div>\r\n              )}\r\n            {result.reinforcedMeanCharacteristicCompressiveStrength !==\r\n              undefined &&\r\n              result.reinforcedMeanCharacteristicCompressiveStrength !==\r\n                null && (\r\n                <div>\r\n                  <span className=\"text-muted-foreground\">\r\n                    {t('mean-compressive-strength')}:\r\n                  </span>\r\n                  <span className=\"ml-2 font-medium\">\r\n                    {result.reinforcedMeanCharacteristicCompressiveStrength.toLocaleString(\r\n                      locale,\r\n                      { maximumFractionDigits: 2 },\r\n                    )}{' '}\r\n                    MPa\r\n                  </span>\r\n                </div>\r\n              )}\r\n            {result.reinforcedCoefficient !== undefined &&\r\n              result.reinforcedCoefficient !== null && (\r\n                <div>\r\n                  <span className=\"text-muted-foreground\">\r\n                    {t('coefficient')}:\r\n                  </span>\r\n                  <span className=\"ml-2 font-medium\">\r\n                    {result.reinforcedCoefficient.toFixed(4)} MPa\r\n                  </span>\r\n                </div>\r\n              )}\r\n            {result.reinforcedTensileLongitudinalReinforcementArea !==\r\n              undefined &&\r\n              result.reinforcedTensileLongitudinalReinforcementArea !==\r\n                null && (\r\n                <div>\r\n                  <span className=\"text-muted-foreground\">\r\n                    {t('reinforcement-area')}:\r\n                  </span>\r\n                  <span className=\"ml-2 font-medium\">\r\n                    {result.reinforcedTensileLongitudinalReinforcementArea.toLocaleString(\r\n                      locale,\r\n                      { maximumFractionDigits: 2 },\r\n                    )}{' '}\r\n                    mm²\r\n                  </span>\r\n                </div>\r\n              )}\r\n            {result.reinforcedTensileReinforcementRatio !== undefined &&\r\n              result.reinforcedTensileReinforcementRatio !== null && (\r\n                <div>\r\n                  <span className=\"text-muted-foreground\">\r\n                    {t('reinforcement-ratio')}:\r\n                  </span>\r\n                  <span className=\"ml-2 font-medium\">\r\n                    {(result.reinforcedTensileReinforcementRatio * 100).toFixed(\r\n                      4,\r\n                    )}\r\n                    %\r\n                  </span>\r\n                </div>\r\n              )}\r\n            {result.reinforcedSectionShearResistance !== undefined &&\r\n              result.reinforcedSectionShearResistance !== null && (\r\n                <div className=\"col-span-2\">\r\n                  <span className=\"text-muted-foreground font-semibold\">\r\n                    {t('shear-resistance')}:\r\n                  </span>\r\n                  <span className=\"ml-2 font-medium text-lg\">\r\n                    {result.reinforcedSectionShearResistance.toLocaleString(\r\n                      locale,\r\n                      { maximumFractionDigits: 2 },\r\n                    )}{' '}\r\n                    kN\r\n                  </span>\r\n                </div>\r\n              )}\r\n          </div>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAMA;AAEA;AACA;AAAA;;;;;;;AAMO,SAAS,oBAAoB,EAAE,MAAM,EAA4B;IACtE,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,SAAS,IAAA,+KAAS;IAExB,MAAM,aAAa,QAAQ,gCAAgC;IAE3D,qBACE,8OAAC,wIAAI;;0BACH,8OAAC,8IAAU;0BACT,cAAA,8OAAC,6IAAS;oBAAC,WAAU;;sCACnB,8OAAC;sCAAM,EAAE;;;;;;sCACT,8OAAC,0IAAK;4BACJ,WAAW,IAAA,yHAAE,EACX,uBACA,aAAa,iBAAiB;sCAG/B,aAAa,EAAE,cAAc,EAAE;;;;;;;;;;;;;;;;;0BAItC,8OAAC,+IAAW;gBAAC,WAAU;;kCAErB,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;;oCACX,EAAE;oCAAqB;oCAAG,OAAO,eAAe;;;;;;;4BAElD,OAAO,eAAe,KAAK,aAC1B,OAAO,eAAe,KAAK,sBACzB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;;4CACb,EAAE;4CAAqB;;;;;;;kDAE1B,8OAAC;wCAAK,WAAU;;4CACb,OAAO,eAAe,CAAC,cAAc,CAAC,QAAQ;gDAC7C,uBAAuB;4CACzB;4CAAI;4CAAI;;;;;;;;;;;;;;;;;;;kCAOlB,8OAAC,kJAAS;;;;;kCAGV,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAsB,EAAE;;;;;;0CACtC,8OAAC;gCAAI,WAAU;;oCACZ,OAAO,iCAAiC,KAAK,aAC5C,OAAO,iCAAiC,KAAK,sBAC3C,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAmB;;;;;;;0DAExB,8OAAC;gDAAK,WAAU;;oDACb,OAAO,iCAAiC,CAAC,cAAc,CACtD,QACA;wDAAE,uBAAuB;oDAAE;oDAC1B;oDAAI;;;;;;;;;;;;;oCAKd,OAAO,mCAAmC,KAAK,aAC9C,OAAO,mCAAmC,KAAK,sBAC7C,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAiB;;;;;;;0DAEtB,8OAAC;gDAAK,WAAU;;oDACb,OAAO,mCAAmC,CAAC,cAAc,CACxD,QACA;wDAAE,uBAAuB;oDAAE;oDAC1B;oDAAI;;;;;;;;;;;;;oCAKd,OAAO,4BAA4B,KAAK,aACvC,OAAO,4BAA4B,KAAK,sBACtC,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAsB;;;;;;;0DAE3B,8OAAC;gDAAK,WAAU;0DACb,OAAO,4BAA4B,CAAC,OAAO,CAAC;;;;;;;;;;;;oCAIpD,OAAO,uBAAuB,KAAK,aAClC,OAAO,uBAAuB,KAAK,sBACjC,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAe;;;;;;;0DAEpB,8OAAC;gDAAK,WAAU;;oDACb,OAAO,uBAAuB,CAAC,OAAO,CAAC;oDAAG;;;;;;;;;;;;;oCAIlD,OAAO,gDAAgD,KACtD,aACA,OAAO,gDAAgD,KACrD,sBACA,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAsB;;;;;;;0DAE3B,8OAAC;gDAAK,WAAU;;oDACb,OAAO,gDAAgD,CAAC,cAAc,CACrE,QACA;wDAAE,uBAAuB;oDAAE;oDAC1B;oDAAI;;;;;;;;;;;;;oCAKd,OAAO,qCAAqC,KAAK,aAChD,OAAO,qCAAqC,KAAK,sBAC/C,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAuB;;;;;;;0DAE5B,8OAAC;gDAAK,WAAU;;oDACb,CACC,OAAO,qCAAqC,GAAG,GACjD,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;oCAKpB,OAAO,yBAAyB,KAAK,aACpC,OAAO,yBAAyB,KAAK,sBACnC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAkB;;;;;;;0DAEvB,8OAAC;gDAAK,WAAU;;oDACb,OAAO,yBAAyB,CAAC,cAAc,CAAC,QAAQ;wDACvD,uBAAuB;oDACzB;oDAAI;oDAAI;;;;;;;;;;;;;;;;;;;;;;;;;kCAQpB,8OAAC,kJAAS;;;;;kCAGV,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAsB,EAAE;;;;;;0CACtC,8OAAC;gCAAI,WAAU;;oCACZ,OAAO,+BAA+B,KAAK,aAC1C,OAAO,+BAA+B,KAAK,sBACzC,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAmB;;;;;;;0DAExB,8OAAC;gDAAK,WAAU;;oDACb,OAAO,+BAA+B,CAAC,cAAc,CACpD,QACA;wDAAE,uBAAuB;oDAAE;oDAC1B;oDAAI;;;;;;;;;;;;;oCAKd,OAAO,iCAAiC,KAAK,aAC5C,OAAO,iCAAiC,KAAK,sBAC3C,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAiB;;;;;;;0DAEtB,8OAAC;gDAAK,WAAU;;oDACb,OAAO,iCAAiC,CAAC,cAAc,CACtD,QACA;wDAAE,uBAAuB;oDAAE;oDAC1B;oDAAI;;;;;;;;;;;;;oCAKd,OAAO,0BAA0B,KAAK,aACrC,OAAO,0BAA0B,KAAK,sBACpC,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAsB;;;;;;;0DAE3B,8OAAC;gDAAK,WAAU;0DACb,OAAO,0BAA0B,CAAC,OAAO,CAAC;;;;;;;;;;;;oCAIlD,OAAO,+CAA+C,KACrD,aACA,OAAO,+CAA+C,KACpD,sBACA,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAA6B;;;;;;;0DAElC,8OAAC;gDAAK,WAAU;;oDACb,OAAO,+CAA+C,CAAC,cAAc,CACpE,QACA;wDAAE,uBAAuB;oDAAE;oDAC1B;oDAAI;;;;;;;;;;;;;oCAKd,OAAO,qBAAqB,KAAK,aAChC,OAAO,qBAAqB,KAAK,sBAC/B,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAe;;;;;;;0DAEpB,8OAAC;gDAAK,WAAU;;oDACb,OAAO,qBAAqB,CAAC,OAAO,CAAC;oDAAG;;;;;;;;;;;;;oCAIhD,OAAO,8CAA8C,KACpD,aACA,OAAO,8CAA8C,KACnD,sBACA,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAsB;;;;;;;0DAE3B,8OAAC;gDAAK,WAAU;;oDACb,OAAO,8CAA8C,CAAC,cAAc,CACnE,QACA;wDAAE,uBAAuB;oDAAE;oDAC1B;oDAAI;;;;;;;;;;;;;oCAKd,OAAO,mCAAmC,KAAK,aAC9C,OAAO,mCAAmC,KAAK,sBAC7C,8OAAC;;0DACC,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAuB;;;;;;;0DAE5B,8OAAC;gDAAK,WAAU;;oDACb,CAAC,OAAO,mCAAmC,GAAG,GAAG,EAAE,OAAO,CACzD;oDACA;;;;;;;;;;;;;oCAKT,OAAO,gCAAgC,KAAK,aAC3C,OAAO,gCAAgC,KAAK,sBAC1C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;;oDACb,EAAE;oDAAoB;;;;;;;0DAEzB,8OAAC;gDAAK,WAAU;;oDACb,OAAO,gCAAgC,CAAC,cAAc,CACrD,QACA;wDAAE,uBAAuB;oDAAE;oDAC1B;oDAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU3B", "debugId": null}}, {"offset": {"line": 3890, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/slab/calculations/slab-shear-calculation.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Card, CardContent } from '@atlas/components/ui/card'\r\nimport { Checkbox } from '@atlas/components/ui/checkbox'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { Label } from '@atlas/components/ui/label'\r\nimport type { ModuleWithParamsSlab } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useModuleCalculation } from '@atlas/lib/mutation/modules/use-module-calculation'\r\nimport { useProductsByType } from '@atlas/lib/query/products/use-products-by-type'\r\nimport {\r\n  type SlabShearCalculationInput,\r\n  slabShearCalculationSchema,\r\n} from '@atlas/types/schemas/slab-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useId, useMemo } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\nimport { SlabShearResultCard } from './slab-shear-result-card'\r\n\r\ntype Props = {\r\n  module: ModuleWithParamsSlab\r\n  session: Session\r\n  projectId: Project['id']\r\n}\r\n\r\nconst SECTION_FILL_TYPES = [\r\n  { value: 'Full strip', label: 'Full Strip' },\r\n  { value: 'Joist web', label: 'Joist Web' },\r\n  { value: 'Semi-full strip', label: 'Semi-Full Strip' },\r\n]\r\n\r\nexport const SlabShearCalculation = ({ session, module, projectId }: Props) => {\r\n  const t = useTranslations('forms.calculations.slab.shear')\r\n  const tAction = useTranslations('actions.calculations.slab.shear')\r\n  const tCommon = useTranslations('actions.common')\r\n  const checkboxId = useId()\r\n\r\n  const { mutate, isPending } = useModuleCalculation(session.accessToken)\r\n\r\n  // Get results from module\r\n  const { shearVerifyExecutionInput, shearVerifyExecutionResult } = module\r\n\r\n  // Debug logging\r\n  console.log('🔍 Slab Shear Debug:', {\r\n    shearVerifyExecutionInput: shearVerifyExecutionInput,\r\n    shearVerifyExecutionResult: shearVerifyExecutionResult,\r\n    'module keys': Object.keys(module),\r\n  })\r\n\r\n  // Get FRC product name from module params\r\n  const frcProductName =\r\n    module.params?.slabFrcReinforcement?.frcReinforcementType\r\n\r\n  const {\r\n    data: products,\r\n    isError: errorGettingProducts,\r\n    isLoading: isLoadingProducts,\r\n  } = useProductsByType(session, 'SLAB', 0, 100)\r\n\r\n  // Find product by name from FRC reinforcement\r\n  const frcProduct = useMemo(\r\n    () => products?.content.find(p => p.name === frcProductName),\r\n    [frcProductName, products],\r\n  )\r\n\r\n  const form = useForm<SlabShearCalculationInput>({\r\n    resolver: zodResolver(slabShearCalculationSchema),\r\n    defaultValues: {\r\n      calculationType: 'SHEAR_VERIFY',\r\n      input: {\r\n        sectionFillType:\r\n          shearVerifyExecutionInput?.sectionFillType ?? 'Full strip',\r\n        shearForce: shearVerifyExecutionInput?.shearForce ?? 30,\r\n        isCantilever: shearVerifyExecutionInput?.isCantilever ?? false,\r\n        productInput: {\r\n          id: shearVerifyExecutionInput?.product?.id ?? frcProduct?.id ?? '',\r\n          name:\r\n            shearVerifyExecutionInput?.product?.name ?? frcProduct?.name ?? '',\r\n          sourceType:\r\n            shearVerifyExecutionInput?.product?.sourceType ??\r\n            (frcProduct ? 'DATABASE' : 'CUSTOM'),\r\n          frcSlabThickness:\r\n            shearVerifyExecutionInput?.product?.thickness ??\r\n            frcProduct?.thickness,\r\n          elasticModulus:\r\n            shearVerifyExecutionInput?.product?.elasticModulus ??\r\n            frcProduct?.elasticModulus,\r\n          cylindricCompressiveStrength:\r\n            shearVerifyExecutionInput?.product?.cylindricCompressiveStrength ??\r\n            frcProduct?.cylindricCompressiveStrength,\r\n          characteristicTensileStrength:\r\n            shearVerifyExecutionInput?.product?.characteristicTensileStrength ??\r\n            frcProduct?.characteristicTensileStrength,\r\n          specificWeight:\r\n            shearVerifyExecutionInput?.product?.specificWeight ??\r\n            frcProduct?.specificWeight,\r\n          adhesionToConcrete:\r\n            shearVerifyExecutionInput?.product?.adhesionToConcrete ??\r\n            frcProduct?.adhesionToConcrete,\r\n          fiberType:\r\n            shearVerifyExecutionInput?.product?.fiberType ??\r\n            frcProduct?.fiberType ??\r\n            'CARBON',\r\n        },\r\n      },\r\n    },\r\n  })\r\n\r\n  const productsOptions = [\r\n    ...(products?.content.map(m => ({\r\n      value: m.id,\r\n      label: m.name ?? t('product.unnamed'),\r\n    })) ?? []),\r\n    { value: 'custom', label: t('product.custom') },\r\n  ]\r\n\r\n  const [productId] = form.watch(['input.productInput.id'])\r\n  const selectedProduct = useMemo(\r\n    () => products?.content.find(p => p.id === productId),\r\n    [productId, products],\r\n  )\r\n\r\n  useEffect(() => {\r\n    if (!productId || productId === 'custom') {\r\n      return\r\n    }\r\n\r\n    if (selectedProduct) {\r\n      form.setValue('input.productInput.id', selectedProduct.id)\r\n      form.setValue('input.productInput.name', selectedProduct.name || '')\r\n      form.setValue('input.productInput.sourceType', 'DATABASE')\r\n\r\n      // Auto-populate product properties - use correct property names from Product schema\r\n      if (selectedProduct.thickness) {\r\n        form.setValue(\r\n          'input.productInput.frcSlabThickness',\r\n          selectedProduct.thickness,\r\n        )\r\n      }\r\n      if (selectedProduct.elasticModulus) {\r\n        form.setValue(\r\n          'input.productInput.elasticModulus',\r\n          selectedProduct.elasticModulus,\r\n        )\r\n      }\r\n      if (\r\n        selectedProduct.characteristicCylindricalCompressiveStrengthFrcMatrix\r\n      ) {\r\n        form.setValue(\r\n          'input.productInput.cylindricCompressiveStrength',\r\n          selectedProduct.characteristicCylindricalCompressiveStrengthFrcMatrix,\r\n        )\r\n      }\r\n      if (selectedProduct.characteristicTensileStrength) {\r\n        form.setValue(\r\n          'input.productInput.characteristicTensileStrength',\r\n          selectedProduct.characteristicTensileStrength,\r\n        )\r\n      }\r\n      if (selectedProduct.specificWeight) {\r\n        form.setValue(\r\n          'input.productInput.specificWeight',\r\n          selectedProduct.specificWeight,\r\n        )\r\n      }\r\n      if (selectedProduct.adhesionToConcrete) {\r\n        form.setValue(\r\n          'input.productInput.adhesionToConcrete',\r\n          selectedProduct.adhesionToConcrete,\r\n        )\r\n      }\r\n      if (selectedProduct.fiberType) {\r\n        form.setValue('input.productInput.fiberType', selectedProduct.fiberType)\r\n      }\r\n    }\r\n  }, [form, productId, selectedProduct])\r\n\r\n  const onSubmit = (data: SlabShearCalculationInput) => {\r\n    mutate(\r\n      {\r\n        projectId,\r\n        moduleId: module.id,\r\n        body: data,\r\n      },\r\n      {\r\n        onSuccess: () => {\r\n          toast.success(tAction('calculate.success'))\r\n        },\r\n        onError: (error: any) => {\r\n          toast.error(tAction('calculate.failure', { error: error.message }))\r\n        },\r\n      },\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <Card>\r\n        <CardContent className=\"pt-6\">\r\n          <Form {...form}>\r\n            <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-4\">\r\n              <h1 className=\"text-3xl font-bold\">{t('heading')}</h1>\r\n\r\n              <SelectFormInput\r\n                control={form.control}\r\n                name=\"input.productInput.id\"\r\n                options={productsOptions}\r\n                t={t}\r\n                loading={isLoadingProducts}\r\n                requestError={errorGettingProducts}\r\n              />\r\n\r\n              <SelectFormInput\r\n                control={form.control}\r\n                name=\"input.sectionFillType\"\r\n                t={t}\r\n                options={SECTION_FILL_TYPES}\r\n              />\r\n\r\n              <NumberFormInput\r\n                control={form.control}\r\n                name=\"input.shearForce\"\r\n                t={t}\r\n              />\r\n\r\n              <div className=\"flex items-center space-x-2\">\r\n                <Checkbox\r\n                  id={checkboxId}\r\n                  checked={form.watch('input.isCantilever')}\r\n                  onCheckedChange={checked =>\r\n                    form.setValue('input.isCantilever', !!checked)\r\n                  }\r\n                />\r\n                <Label htmlFor={checkboxId}>{t('is-cantilever')}</Label>\r\n              </div>\r\n\r\n              <Button type=\"submit\" disabled={isPending}>\r\n                {isPending ? tAction('calculating') : tCommon('calculate')}\r\n              </Button>\r\n            </form>\r\n          </Form>\r\n        </CardContent>\r\n      </Card>\r\n\r\n      {shearVerifyExecutionResult && (\r\n        <SlabShearResultCard result={shearVerifyExecutionResult} />\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAIA;AAEA;AACA;AACA;AACA;AACA;AAvBA;;;;;;;;;;;;;;;;;;AA+BA,MAAM,qBAAqB;IACzB;QAAE,OAAO;QAAc,OAAO;IAAa;IAC3C;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAmB,OAAO;IAAkB;CACtD;AAEM,MAAM,uBAAuB,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAS;IACxE,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,aAAa,IAAA,8MAAK;IAExB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,0LAAoB,EAAC,QAAQ,WAAW;IAEtE,0BAA0B;IAC1B,MAAM,EAAE,yBAAyB,EAAE,0BAA0B,EAAE,GAAG;IAElE,gBAAgB;IAChB,QAAQ,GAAG,CAAC,wBAAwB;QAClC,2BAA2B;QAC3B,4BAA4B;QAC5B,eAAe,OAAO,IAAI,CAAC;IAC7B;IAEA,0CAA0C;IAC1C,MAAM,iBACJ,OAAO,MAAM,EAAE,sBAAsB;IAEvC,MAAM,EACJ,MAAM,QAAQ,EACd,SAAS,oBAAoB,EAC7B,WAAW,iBAAiB,EAC7B,GAAG,IAAA,sLAAiB,EAAC,SAAS,QAAQ,GAAG;IAE1C,8CAA8C;IAC9C,MAAM,aAAa,IAAA,gNAAO,EACxB,IAAM,UAAU,QAAQ,KAAK,CAAA,IAAK,EAAE,IAAI,KAAK,iBAC7C;QAAC;QAAgB;KAAS;IAG5B,MAAM,OAAO,IAAA,yKAAO,EAA4B;QAC9C,UAAU,IAAA,6KAAW,EAAC,qKAA0B;QAChD,eAAe;YACb,iBAAiB;YACjB,OAAO;gBACL,iBACE,2BAA2B,mBAAmB;gBAChD,YAAY,2BAA2B,cAAc;gBACrD,cAAc,2BAA2B,gBAAgB;gBACzD,cAAc;oBACZ,IAAI,2BAA2B,SAAS,MAAM,YAAY,MAAM;oBAChE,MACE,2BAA2B,SAAS,QAAQ,YAAY,QAAQ;oBAClE,YACE,2BAA2B,SAAS,cACpC,CAAC,aAAa,aAAa,QAAQ;oBACrC,kBACE,2BAA2B,SAAS,aACpC,YAAY;oBACd,gBACE,2BAA2B,SAAS,kBACpC,YAAY;oBACd,8BACE,2BAA2B,SAAS,gCACpC,YAAY;oBACd,+BACE,2BAA2B,SAAS,iCACpC,YAAY;oBACd,gBACE,2BAA2B,SAAS,kBACpC,YAAY;oBACd,oBACE,2BAA2B,SAAS,sBACpC,YAAY;oBACd,WACE,2BAA2B,SAAS,aACpC,YAAY,aACZ;gBACJ;YACF;QACF;IACF;IAEA,MAAM,kBAAkB;WAClB,UAAU,QAAQ,IAAI,CAAA,IAAK,CAAC;gBAC9B,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,IAAI,IAAI,EAAE;YACrB,CAAC,MAAM,EAAE;QACT;YAAE,OAAO;YAAU,OAAO,EAAE;QAAkB;KAC/C;IAED,MAAM,CAAC,UAAU,GAAG,KAAK,KAAK,CAAC;QAAC;KAAwB;IACxD,MAAM,kBAAkB,IAAA,gNAAO,EAC7B,IAAM,UAAU,QAAQ,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK,YAC3C;QAAC;QAAW;KAAS;IAGvB,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,aAAa,cAAc,UAAU;YACxC;QACF;QAEA,IAAI,iBAAiB;YACnB,KAAK,QAAQ,CAAC,yBAAyB,gBAAgB,EAAE;YACzD,KAAK,QAAQ,CAAC,2BAA2B,gBAAgB,IAAI,IAAI;YACjE,KAAK,QAAQ,CAAC,iCAAiC;YAE/C,oFAAoF;YACpF,IAAI,gBAAgB,SAAS,EAAE;gBAC7B,KAAK,QAAQ,CACX,uCACA,gBAAgB,SAAS;YAE7B;YACA,IAAI,gBAAgB,cAAc,EAAE;gBAClC,KAAK,QAAQ,CACX,qCACA,gBAAgB,cAAc;YAElC;YACA,IACE,gBAAgB,qDAAqD,EACrE;gBACA,KAAK,QAAQ,CACX,mDACA,gBAAgB,qDAAqD;YAEzE;YACA,IAAI,gBAAgB,6BAA6B,EAAE;gBACjD,KAAK,QAAQ,CACX,oDACA,gBAAgB,6BAA6B;YAEjD;YACA,IAAI,gBAAgB,cAAc,EAAE;gBAClC,KAAK,QAAQ,CACX,qCACA,gBAAgB,cAAc;YAElC;YACA,IAAI,gBAAgB,kBAAkB,EAAE;gBACtC,KAAK,QAAQ,CACX,yCACA,gBAAgB,kBAAkB;YAEtC;YACA,IAAI,gBAAgB,SAAS,EAAE;gBAC7B,KAAK,QAAQ,CAAC,gCAAgC,gBAAgB,SAAS;YACzE;QACF;IACF,GAAG;QAAC;QAAM;QAAW;KAAgB;IAErC,MAAM,WAAW,CAAC;QAChB,OACE;YACE;YACA,UAAU,OAAO,EAAE;YACnB,MAAM;QACR,GACA;YACE,WAAW;gBACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACxB;YACA,SAAS,CAAC;gBACR,iJAAK,CAAC,KAAK,CAAC,QAAQ,qBAAqB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAClE;QACF;IAEJ;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,wIAAI;0BACH,cAAA,8OAAC,+IAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC,wIAAI;wBAAE,GAAG,IAAI;kCACZ,cAAA,8OAAC;4BAAK,UAAU,KAAK,YAAY,CAAC;4BAAW,WAAU;;8CACrD,8OAAC;oCAAG,WAAU;8CAAsB,EAAE;;;;;;8CAEtC,8OAAC,kLAAe;oCACd,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,SAAS;oCACT,GAAG;oCACH,SAAS;oCACT,cAAc;;;;;;8CAGhB,8OAAC,kLAAe;oCACd,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,GAAG;oCACH,SAAS;;;;;;8CAGX,8OAAC,kLAAe;oCACd,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,GAAG;;;;;;8CAGL,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gJAAQ;4CACP,IAAI;4CACJ,SAAS,KAAK,KAAK,CAAC;4CACpB,iBAAiB,CAAA,UACf,KAAK,QAAQ,CAAC,sBAAsB,CAAC,CAAC;;;;;;sDAG1C,8OAAC,0IAAK;4CAAC,SAAS;sDAAa,EAAE;;;;;;;;;;;;8CAGjC,8OAAC,4IAAM;oCAAC,MAAK;oCAAS,UAAU;8CAC7B,YAAY,QAAQ,iBAAiB,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOvD,4CACC,8OAAC,8PAAmB;gBAAC,QAAQ;;;;;;;;;;;;AAIrC", "debugId": null}}, {"offset": {"line": 4182, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/slab/slab-calculations.tsx"], "sourcesContent": ["import { SlabFlexuralNegativeCalculation } from '@atlas/components/common/atlas/module-detail/params-forms/slab/calculations/slab-flexural-negative-calculation'\r\nimport { SlabFlexuralPositiveCalculation } from '@atlas/components/common/atlas/module-detail/params-forms/slab/calculations/slab-flexural-positive-calculation'\r\nimport { SlabInterfaceSlipCalculation } from '@atlas/components/common/atlas/module-detail/params-forms/slab/calculations/slab-interface-slip-calculation'\r\nimport { SlabShearCalculation } from '@atlas/components/common/atlas/module-detail/params-forms/slab/calculations/slab-shear-calculation'\r\nimport {\r\n  Tabs,\r\n  TabsContent,\r\n  TabsList,\r\n  TabsTrigger,\r\n} from '@atlas/components/ui/tabs'\r\nimport type { ModuleWithParamsSlab } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  module: ModuleWithParamsSlab\r\n  session: Session\r\n  projectId: string\r\n}\r\n\r\nexport const SlabCalculations = ({ module, session, projectId }: Props) => {\r\n  const t = useTranslations('forms.calculations.slab')\r\n\r\n  return (\r\n    <>\r\n      <h3 className=\"text-lg font-medium py-4\">{t('title')}</h3>\r\n      <Tabs defaultValue=\"flexural-positive\">\r\n        <TabsList className=\"grid w-full grid-cols-4\">\r\n          <TabsTrigger value=\"flexural-positive\">\r\n            {t('flexural-positive.label')}\r\n          </TabsTrigger>\r\n          <TabsTrigger value=\"flexural-negative\">\r\n            {t('flexural-negative.label')}\r\n          </TabsTrigger>\r\n          <TabsTrigger value=\"shear\">{t('shear.label')}</TabsTrigger>\r\n          <TabsTrigger value=\"interface-slip\">\r\n            {t('interface-slip.label')}\r\n          </TabsTrigger>\r\n        </TabsList>\r\n        <TabsContent value=\"flexural-positive\">\r\n          <SlabFlexuralPositiveCalculation\r\n            session={session}\r\n            module={module}\r\n            projectId={projectId}\r\n          />\r\n        </TabsContent>\r\n        <TabsContent value=\"flexural-negative\">\r\n          <SlabFlexuralNegativeCalculation\r\n            session={session}\r\n            module={module}\r\n            projectId={projectId}\r\n          />\r\n        </TabsContent>\r\n        <TabsContent value=\"shear\">\r\n          <SlabShearCalculation\r\n            session={session}\r\n            module={module}\r\n            projectId={projectId}\r\n          />\r\n        </TabsContent>\r\n        <TabsContent value=\"interface-slip\">\r\n          <SlabInterfaceSlipCalculation\r\n            session={session}\r\n            module={module}\r\n            projectId={projectId}\r\n          />\r\n        </TabsContent>\r\n      </Tabs>\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAQA;;;;;;;;AAQO,MAAM,mBAAmB,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAS;IACpE,MAAM,IAAI,IAAA,yNAAe,EAAC;IAE1B,qBACE;;0BACE,8OAAC;gBAAG,WAAU;0BAA4B,EAAE;;;;;;0BAC5C,8OAAC,wIAAI;gBAAC,cAAa;;kCACjB,8OAAC,4IAAQ;wBAAC,WAAU;;0CAClB,8OAAC,+IAAW;gCAAC,OAAM;0CAChB,EAAE;;;;;;0CAEL,8OAAC,+IAAW;gCAAC,OAAM;0CAChB,EAAE;;;;;;0CAEL,8OAAC,+IAAW;gCAAC,OAAM;0CAAS,EAAE;;;;;;0CAC9B,8OAAC,+IAAW;gCAAC,OAAM;0CAChB,EAAE;;;;;;;;;;;;kCAGP,8OAAC,+IAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,sRAA+B;4BAC9B,SAAS;4BACT,QAAQ;4BACR,WAAW;;;;;;;;;;;kCAGf,8OAAC,+IAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,sRAA+B;4BAC9B,SAAS;4BACT,QAAQ;4BACR,WAAW;;;;;;;;;;;kCAGf,8OAAC,+IAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,4PAAoB;4BACnB,SAAS;4BACT,QAAQ;4BACR,WAAW;;;;;;;;;;;kCAGf,8OAAC,+IAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,gRAA4B;4BAC3B,SAAS;4BACT,QAAQ;4BACR,WAAW;;;;;;;;;;;;;;;;;;;AAMvB", "debugId": null}}, {"offset": {"line": 4333, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/slab/slab-params-form.tsx"], "sourcesContent": ["import { ModuleReportGenerationSection } from '@atlas/components/common/atlas/module-detail/module-report-generation-section'\r\nimport { SlabFrcReinforcementForm } from '@atlas/components/common/atlas/module-detail/params-forms/slab/sections/slab-frc-reinforcement-form'\r\nimport { SlabGeometryForm } from '@atlas/components/common/atlas/module-detail/params-forms/slab/sections/slab-geometry-form'\r\nimport { SlabMaterialForm } from '@atlas/components/common/atlas/module-detail/params-forms/slab/sections/slab-material-form'\r\nimport { SlabReinforcementForm } from '@atlas/components/common/atlas/module-detail/params-forms/slab/sections/slab-reinforcement-form'\r\nimport { SlabCalculations } from '@atlas/components/common/atlas/module-detail/params-forms/slab/slab-calculations'\r\nimport {\r\n  Accordion,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from '@atlas/components/ui/accordion'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport type {\r\n  Module,\r\n  ModuleWithParamsSlab,\r\n} from '@atlas/lib/api/modules/schemas/modules'\r\nimport { slabCalculationCheck } from '@atlas/lib/api/modules/schemas/slab-params'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useMemo, useState } from 'react'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  module: ModuleWithParamsSlab\r\n}\r\n\r\nexport const SlabParamsForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  module,\r\n}: Props) => {\r\n  const { params } = module\r\n  const [openItems, setOpenItems] = useState(['0'])\r\n  const t = useTranslations('forms.project-params.slab')\r\n\r\n  const handleItemSaved = useCallback((id: string) => {\r\n    const nextId = String(Number(id) + 1)\r\n    setOpenItems(old => {\r\n      const temp = old.filter(v => v !== id)\r\n      return old.includes(nextId) ? temp : [...temp, nextId]\r\n    })\r\n  }, [])\r\n\r\n  const enableReport = useMemo(() => {\r\n    const result = slabCalculationCheck.safeParse(module)\r\n    return result.success\r\n  }, [module])\r\n\r\n  const hasParamsForCalculations = useMemo(() => {\r\n    // Check if necessary parameters exist for calculations\r\n    return Boolean(\r\n      params?.geometry && params?.slabRebar && params?.materialProperties,\r\n    )\r\n  }, [params])\r\n\r\n  return (\r\n    <div>\r\n      <Accordion type=\"multiple\" value={openItems} onValueChange={setOpenItems}>\r\n        <AccordionItem value=\"0\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">{t('geometry.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <SlabGeometryForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={params?.geometry as any}\r\n              onSave={() => handleItemSaved('0')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"1\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">{t('slabRebar.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <SlabReinforcementForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={params?.slabRebar ?? undefined}\r\n              structuralScheme={params?.geometry?.structuralScheme as any}\r\n              onSave={() => handleItemSaved('1')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"2\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('materialProperties.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <SlabMaterialForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={(params?.materialProperties ?? undefined) as any}\r\n              onSave={() => handleItemSaved('2')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"3\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('slabFrcReinforcement.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <SlabFrcReinforcementForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={params?.slabFrcReinforcement ?? undefined}\r\n              onSave={() => handleItemSaved('3')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n      </Accordion>\r\n\r\n      {hasParamsForCalculations && (\r\n        <>\r\n          <Separator />\r\n          <SlabCalculations\r\n            module={module}\r\n            session={session}\r\n            projectId={projectId}\r\n          />\r\n        </>\r\n      )}\r\n\r\n      {enableReport && (\r\n        <>\r\n          <Separator />\r\n          <ModuleReportGenerationSection\r\n            session={session}\r\n            projectId={projectId}\r\n            moduleId={moduleId}\r\n            enabled={enableReport}\r\n          />\r\n        </>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AAKA;AAGA;AACA;;;;;;;;;;;;;AASO,MAAM,iBAAiB,CAAC,EAC7B,OAAO,EACP,SAAS,EACT,QAAQ,EACR,MAAM,EACA;IACN,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;QAAC;KAAI;IAChD,MAAM,IAAI,IAAA,yNAAe,EAAC;IAE1B,MAAM,kBAAkB,IAAA,oNAAW,EAAC,CAAC;QACnC,MAAM,SAAS,OAAO,OAAO,MAAM;QACnC,aAAa,CAAA;YACX,MAAM,OAAO,IAAI,MAAM,CAAC,CAAA,IAAK,MAAM;YACnC,OAAO,IAAI,QAAQ,CAAC,UAAU,OAAO;mBAAI;gBAAM;aAAO;QACxD;IACF,GAAG,EAAE;IAEL,MAAM,eAAe,IAAA,gNAAO,EAAC;QAC3B,MAAM,SAAS,iLAAoB,CAAC,SAAS,CAAC;QAC9C,OAAO,OAAO,OAAO;IACvB,GAAG;QAAC;KAAO;IAEX,MAAM,2BAA2B,IAAA,gNAAO,EAAC;QACvC,uDAAuD;QACvD,OAAO,QACL,QAAQ,YAAY,QAAQ,aAAa,QAAQ;IAErD,GAAG;QAAC;KAAO;IAEX,qBACE,8OAAC;;0BACC,8OAAC,kJAAS;gBAAC,MAAK;gBAAW,OAAO;gBAAW,eAAe;;kCAC1D,8OAAC,sJAAa;wBAAC,OAAM;;0CACnB,8OAAC,yJAAgB;0CACf,cAAA,8OAAC;oCAAG,WAAU;8CAAuB,EAAE;;;;;;;;;;;0CAEzC,8OAAC,yJAAgB;0CACf,cAAA,8OAAC,gPAAgB;oCACf,SAAS;oCACT,WAAW;oCACX,UAAU;oCACV,eAAe,QAAQ;oCACvB,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;kCAIpC,8OAAC,sJAAa;wBAAC,OAAM;;0CACnB,8OAAC,yJAAgB;0CACf,cAAA,8OAAC;oCAAG,WAAU;8CAAuB,EAAE;;;;;;;;;;;0CAEzC,8OAAC,yJAAgB;0CACf,cAAA,8OAAC,0PAAqB;oCACpB,SAAS;oCACT,WAAW;oCACX,UAAU;oCACV,eAAe,QAAQ,aAAa;oCACpC,kBAAkB,QAAQ,UAAU;oCACpC,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;kCAIpC,8OAAC,sJAAa;wBAAC,OAAM;;0CACnB,8OAAC,yJAAgB;0CACf,cAAA,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;;;;;;0CAGP,8OAAC,yJAAgB;0CACf,cAAA,8OAAC,gPAAgB;oCACf,SAAS;oCACT,WAAW;oCACX,UAAU;oCACV,eAAgB,QAAQ,sBAAsB;oCAC9C,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;kCAIpC,8OAAC,sJAAa;wBAAC,OAAM;;0CACnB,8OAAC,yJAAgB;0CACf,cAAA,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;;;;;;0CAGP,8OAAC,yJAAgB;0CACf,cAAA,8OAAC,oQAAwB;oCACvB,SAAS;oCACT,WAAW;oCACX,UAAU;oCACV,eAAe,QAAQ,wBAAwB;oCAC/C,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;YAMrC,0CACC;;kCACE,8OAAC,kJAAS;;;;;kCACV,8OAAC,gOAAgB;wBACf,QAAQ;wBACR,SAAS;wBACT,WAAW;;;;;;;;YAKhB,8BACC;;kCACE,8OAAC,kJAAS;;;;;kCACV,8OAAC,uOAA6B;wBAC5B,SAAS;wBACT,WAAW;wBACX,UAAU;wBACV,SAAS;;;;;;;;;;;;;;AAMrB", "debugId": null}}]}