import { NumberFormInput } from '@atlas/components/common/form/number-form-input'
import { SelectFormInput } from '@atlas/components/common/form/select-form-input'
import { Button } from '@atlas/components/ui/button'
import { Form } from '@atlas/components/ui/form'
import type { Module } from '@atlas/lib/api/modules/schemas/modules'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'
import { useWoodMaterialByName } from '@atlas/lib/query/materials/use-wood-material-by-name'
import { useWoodNames } from '@atlas/lib/query/materials/use-wood-names-api'
import { woodMaterialSchema1 } from '@atlas/types/schemas/wood-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Loader2Icon } from 'lucide-react'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useCallback, useEffect, useState } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { z } from 'zod'

// Partial Material Factor dropdown options
const PARTIAL_MATERIAL_FACTOR_OPTIONS = [
  { value: '1', label: '1' },
  { value: '5', label: '5' },
  { value: '45', label: '45' },
]

// Custom form schema that extends the API schema but with string for dropdown
const woodGeometryFormSchema = woodMaterialSchema1.extend({
  partialMaterialFactor: z.string(), // Override to string for dropdown
})

type FormSchema = z.infer<typeof woodGeometryFormSchema>

type Props = {
  session: Session
  projectId: Project['id']
  moduleId: Module['id']
  defaultValues?: Partial<FormSchema>
  woodName?: string
  onSave: () => void
}

export const WoodGeometryForm = ({
  session,
  projectId,
  moduleId,
  defaultValues,
  woodName: _woodName,
  onSave: _onSave,
}: Props) => {
  const t = useTranslations('forms.project-params.wood.geometry')
  const tAction = useTranslations('actions.save-project-params.messages')
  const tCommon = useTranslations('actions.common')
  const {
    data: woodNames,
    isError: woodNamesError,
    isLoading: woodNamesLoading,
  } = useWoodNames({ session })
  const woodNamesOptions = [
    ...(woodNames?.map(m => ({ value: m, label: m })) ?? []),
  ]
  const form = useForm<FormSchema>({
    resolver: zodResolver(woodGeometryFormSchema),
    defaultValues: {
      // Material Properties fields from woodMaterialSchema1
      id: defaultValues?.id,
      category: defaultValues?.woodName ?? '',
      partialMaterialFactor: defaultValues?.partialMaterialFactor
        ? defaultValues.partialMaterialFactor.toString()
        : '1',
      characteristicBendingStrength:
        defaultValues?.characteristicBendingStrength ?? 0,
      characteristicShearStrength:
        defaultValues?.characteristicShearStrength ?? 0,
      characteristicTensileStrength:
        defaultValues?.characteristicTensileStrength ?? 0,
      characteristicCompressiveStrength:
        defaultValues?.characteristicCompressiveStrength ?? 0,
      meanDensity: defaultValues?.meanDensity ?? 0,
      meanShearModulus: defaultValues?.meanShearModulus ?? 0,
      elasticityModulusParallelToGrain:
        defaultValues?.elasticityModulusParallelToGrain ?? 0,
      meanElasticityModulus: defaultValues?.meanElasticityModulus ?? 0,
    },
    // Prevent form from resetting after successful submission
    shouldUnregister: false,
  })

  // Reset form with new defaultValues when they change (component remount)
  useEffect(() => {
    if (defaultValues) {
      form.reset({
        id: defaultValues.id,
        category: defaultValues.woodName ?? '',
        partialMaterialFactor: defaultValues.partialMaterialFactor
          ? defaultValues.partialMaterialFactor.toString()
          : '1',
        characteristicBendingStrength:
          defaultValues.characteristicBendingStrength ?? 0,
        characteristicShearStrength:
          defaultValues.characteristicShearStrength ?? 0,
        characteristicTensileStrength:
          defaultValues.characteristicTensileStrength ?? 0,
        characteristicCompressiveStrength:
          defaultValues.characteristicCompressiveStrength ?? 0,
        meanDensity: defaultValues.meanDensity ?? 0,
        meanShearModulus: defaultValues.meanShearModulus ?? 0,
        elasticityModulusParallelToGrain:
          defaultValues.elasticityModulusParallelToGrain ?? 0,
        meanElasticityModulus: defaultValues.meanElasticityModulus ?? 0,
      })
    }
  }, [defaultValues, form])

  const { mutate, isPending } = useSaveModuleParamsMutation(
    session.accessToken,
    {
      onSuccess: () => {
        toast.success(tAction('edit.success'))
        // Query invalidation is handled by the mutation hook itself
        // Don't call onSave() immediately to prevent form clearing
        // The parent component should handle accordion state separately
        // onSave()
      },
      onError: error => {
        toast.error(tAction('edit.failure', { error: error.message }))
      },
    },
  )

  // State to store category name from API - initialize from defaultValues if available
  const [categoryName, setCategoryName] = useState<string>(
    defaultValues?.woodName || '',
  )

  const handleFormSubmit = useCallback(
    (data: FormSchema) => {
      // Include categoryName from API in the category field
      const dataWithCategory = {
        ...data,
        category: categoryName || data.category,
        woodName: data.category,
        // Convert partialMaterialFactor from string to number for API
        partialMaterialFactor: parseFloat(data.partialMaterialFactor),
      }

      // Send as materialProperties to match the API structure
      mutate({
        projectId,
        moduleId,
        body: { materialProperties: dataWithCategory as any },
      })
    },
    [mutate, projectId, moduleId, categoryName],
  )

  // Watch for category changes to fetch wood material data
  const selectedCategory = form.watch('category')

  const { data: woodMaterialData } = useWoodMaterialByName(
    session,
    selectedCategory,
    !!selectedCategory,
  )

  // Sync categoryName when defaultValues change (component remount or data refresh)
  useEffect(() => {
    if (defaultValues?.category && !categoryName) {
      setCategoryName(defaultValues.category)
    }
  }, [defaultValues?.category, categoryName])

  // Update form fields when wood material data is fetched
  useEffect(() => {
    if (woodMaterialData) {
      // Store category name from API response
      setCategoryName(woodMaterialData.category)

      // Map API response to form fields
      form.setValue(
        'characteristicBendingStrength',
        woodMaterialData.flexuralStrength,
      )
      form.setValue(
        'characteristicShearStrength',
        woodMaterialData.shearStrength,
      )
      form.setValue(
        'characteristicTensileStrength',
        woodMaterialData.tensileStrengthParallel,
      )
      form.setValue(
        'characteristicCompressiveStrength',
        woodMaterialData.compressiveStrengthParallel,
      )
      form.setValue('meanDensity', woodMaterialData.meanDensity)
      form.setValue('meanShearModulus', woodMaterialData.shearModulusMean)
      form.setValue(
        'elasticityModulusParallelToGrain',
        woodMaterialData.elasticModulusCharacteristicParallel,
      )
      form.setValue(
        'meanElasticityModulus',
        woodMaterialData.elasticModulusMeanParallel,
      )
    }
  }, [woodMaterialData, form])

  return (
    <div className="flex flex-col 2xl:flex-row justify-center gap-2">
      <Form {...form}>
        <form
          className="space-y-4 rounded-md border p-4 grow"
          onSubmit={form.handleSubmit(handleFormSubmit)}
        >
          <SelectFormInput
            control={form.control}
            name="category"
            options={woodNamesOptions}
            t={t}
            loading={woodNamesLoading}
            requestError={woodNamesError}
            errorMessage={t('category.error')}
            required={true}
          />
          <NumberFormInput
            control={form.control}
            name="characteristicBendingStrength"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />
          <NumberFormInput
            control={form.control}
            name="characteristicShearStrength"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />
          <NumberFormInput
            control={form.control}
            name="characteristicTensileStrength"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />
          <NumberFormInput
            control={form.control}
            name="characteristicCompressiveStrength"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />
          <NumberFormInput
            control={form.control}
            name="meanDensity"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />
          <NumberFormInput
            control={form.control}
            name="meanShearModulus"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />
          <NumberFormInput
            control={form.control}
            name="elasticityModulusParallelToGrain"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />
          <NumberFormInput
            control={form.control}
            name="meanElasticityModulus"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />
          <SelectFormInput
            control={form.control}
            name="partialMaterialFactor"
            options={PARTIAL_MATERIAL_FACTOR_OPTIONS}
            t={t}
            required={true}
          />
          <Button
            type="submit"
            className="w-full sm:w-auto"
            disabled={isPending}
            onClick={form.handleSubmit(handleFormSubmit)}
          >
            {isPending && <Loader2Icon className="animate-spin" />}
            {tCommon('save')}
          </Button>
        </form>
      </Form>
      {/* <Image
        src={'/assets/wood/wood_material_properties.jpg'}
        alt="wood material properties"
        height={500}
        width={250}
        className="mx-auto rounded-md object-contain"
        priority
      /> */}
    </div>
  )
}
