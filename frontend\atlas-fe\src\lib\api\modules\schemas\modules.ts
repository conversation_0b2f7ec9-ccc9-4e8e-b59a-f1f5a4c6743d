import { dateTransform } from '@atlas/functions/zod/date-transform'
import { nullToObjectTransform } from '@atlas/functions/zod/null-to-object-transform'
import { nullToUndefinedTransform } from '@atlas/functions/zod/null-to-undefined-transform'
import {
  pillarFlexuralCalculationResultSchema,
  pillarFlexuralVerifyExecutionInputSchema,
  pillarParamsSchema,
  pillarShearCalculationResultSchema,
  pillarShearVerifyExecutionInputSchema,
} from '@atlas/lib/api/modules/schemas/pillar-params'
import {
  rectangularBeamFlexuralCalculationResultSchema,
  rectangularBeamFlexuralVerifyExecutionInputSchema,
  rectangularBeamParamsSchema,
  rectangularBeamShearCalculationResultSchema,
  rectangularBeamShearVerifyExecutionInputSchema,
} from '@atlas/lib/api/modules/schemas/rectangular-beam-params'
import {
  slabCalculationsResultSchema,
  slabFlexuralCalculationResultSchema,
  slabFlexuralVerifyExecutionInputSchema,
  slabInterfaceSlipCalculationResultSchema,
  slabInterfaceSlipVerifyExecutionInputSchema,
  slabParamsSchema,
  slabShearCalculationResultSchema,
  slabShearVerifyExecutionInputSchema,
} from '@atlas/lib/api/modules/schemas/slab-params'
import { woodParamsSchema } from '@atlas/lib/api/modules/schemas/wood-params'
import { masonryAntiOverturningCompositeReinforcementSystemVerifyInputSchema } from '@atlas/types/schemas/masonry/antioverturning-form'
import {
  crmPostInterventionSchema,
  crmPreInterventionSchema,
} from '@atlas/types/schemas/masonry/crm-form'
import {
  confinementReinforcementCalculationResult,
  confinementReinforcementVerifyInput,
  frcmColumnParamsSchema,
} from '@atlas/types/schemas/masonry/frcm-column-form'
import {
  maschiMurariFlexuralReinforcementCalculationResultSchema,
  maschiMurariFlexuralReinforcementVerifyExecutionSchema,
  maschiMurariParamsSchema,
  maschiMurariShearReinforcementCalculationResultSchema,
  maschiMurariShearReinforcementVerifyExecutionSchema,
} from '@atlas/types/schemas/masonry/maschi-murari-form'
import { z } from 'zod'
import {
  masonryAntiOverturningParamsSchema,
  masonryAntiOverturningPostInterventionCalculationResultSchema,
  masonryAntiOverturningPreInterventionCalculationResultSchema,
  masonryAntiOverturningSeismicDemandCalculationResultSchema,
} from './masonry-antioverturning-params'
import {
  tBeamFlexuralCalculationResultSchema,
  tBeamFlexuralVerifyExecutionInputSchema,
  tBeamParamsSchema,
} from './t-beam-params'

export const moduleBase = z.object({
  id: z.string(),
  name: z.string(),
  description: z
    .string()
    .nullable()
    .optional()
    .transform(nullToUndefinedTransform),
  createdAt: z.string().nullable().optional().transform(dateTransform),
  lastModified: z.string().nullable().optional().transform(dateTransform),
})

export type Module = z.infer<typeof moduleSchema>

export const moduleSchema = z.discriminatedUnion('type', [
  moduleBase.extend({
    type: z.literal('RECTANGULAR_BEAM'),
    params: rectangularBeamParamsSchema
      .nullable()
      .optional()
      .transform(nullToObjectTransform),
    flexuralVerifyExecutionInput:
      rectangularBeamFlexuralVerifyExecutionInputSchema.optional().nullable(),
    flexuralCalculationResult: rectangularBeamFlexuralCalculationResultSchema
      .optional()
      .nullable(),
    shearVerifyExecutionInput: rectangularBeamShearVerifyExecutionInputSchema
      .optional()
      .nullable(),
    shearCalculationResult: rectangularBeamShearCalculationResultSchema
      .optional()
      .nullable(),
  }),
  moduleBase.extend({
    type: z.literal('PILLAR'),
    params: pillarParamsSchema
      .nullable()
      .optional()
      .transform(nullToObjectTransform),
    flexuralVerifyExecutionInput: pillarFlexuralVerifyExecutionInputSchema
      .optional()
      .nullable(),
    flexuralCalculationResult: pillarFlexuralCalculationResultSchema
      .optional()
      .nullable(),
    shearVerifyExecutionInput: pillarShearVerifyExecutionInputSchema
      .optional()
      .nullable(),
    shearCalculationResult: pillarShearCalculationResultSchema
      .optional()
      .nullable(),
  }),
  moduleBase.extend({
    type: z.literal('SLAB'),
    params: slabParamsSchema
      .nullable()
      .optional()
      .transform(nullToObjectTransform),
    // Flexural verification M+ (positive moment / span)
    flexuralVerifyExecutionInputMPlus: slabFlexuralVerifyExecutionInputSchema
      .optional()
      .nullable(),
    flexuralVerifyExecutionResultMPlus: slabFlexuralCalculationResultSchema
      .optional()
      .nullable(),
    // Flexural verification M- (negative moment / support)
    flexuralVerifyExecutionInputMMinus: slabFlexuralVerifyExecutionInputSchema
      .optional()
      .nullable(),
    flexuralVerifyExecutionResultMMinus: slabFlexuralCalculationResultSchema
      .optional()
      .nullable(),
    // Shear verification
    shearVerifyExecutionInput: slabShearVerifyExecutionInputSchema
      .optional()
      .nullable(),
    shearVerifyExecutionResult: slabShearCalculationResultSchema
      .optional()
      .nullable(),
    // Interface slip verification
    interfaceSlipVerifyExecutionInput:
      slabInterfaceSlipVerifyExecutionInputSchema.optional().nullable(),
    interfaceSlipCalculationResult: slabInterfaceSlipCalculationResultSchema
      .optional()
      .nullable(),
    // Legacy calculations result (deprecated, kept for backwards compatibility)
    calculationsResult: slabCalculationsResultSchema.optional().nullable(),
  }),
  moduleBase.extend({
    type: z.literal('ANTI_OVERTURNING'),
    params: masonryAntiOverturningParamsSchema.nullable().optional(),
    seismicDemandCalculationResult:
      masonryAntiOverturningSeismicDemandCalculationResultSchema
        .optional()
        .nullable()
        .transform(nullToUndefinedTransform),
    preInterventionCalculationResult:
      masonryAntiOverturningPreInterventionCalculationResultSchema
        .optional()
        .nullable()
        .transform(nullToUndefinedTransform),
    compositeReinforcementSystemVerifyInput:
      masonryAntiOverturningCompositeReinforcementSystemVerifyInputSchema
        .optional()
        .nullable()
        .transform(nullToUndefinedTransform),
    postInterventionCalculationResult:
      masonryAntiOverturningPostInterventionCalculationResultSchema
        .optional()
        .nullable(),
  }),
  moduleBase.extend({
    type: z.literal('WOOD'),
    params: woodParamsSchema
      .nullable()
      .optional()
      .transform(nullToObjectTransform),
  }),
  moduleBase.extend({
    type: z.literal('T_BEAM'),
    params: tBeamParamsSchema
      .nullable()
      .optional()
      .transform(nullToObjectTransform),
    flexuralVerifyExecutionInput: tBeamFlexuralVerifyExecutionInputSchema
      .optional()
      .nullable(),
    flexuralVerifyExecutionResult: tBeamFlexuralCalculationResultSchema
      .optional()
      .nullable(),
  }),
  moduleBase.extend({
    type: z.literal('CRM'),
    preIntervention: crmPreInterventionSchema.nullable().optional(),
    postIntervention: crmPostInterventionSchema
      .optional()
      .nullable()
      .transform(nullToUndefinedTransform),
  }),
  moduleBase.extend({
    type: z.literal('FRCM_COLUMN'),
    params: frcmColumnParamsSchema.nullable().optional(),
    confinementReinforcementVerifyInput: confinementReinforcementVerifyInput
      .optional()
      .nullable()
      .transform(nullToUndefinedTransform),
    confinementReinforcementCalculationResult:
      confinementReinforcementCalculationResult
        .optional()
        .nullable()
        .transform(nullToUndefinedTransform),
  }),
  moduleBase.extend({
    type: z.literal('MASCHI_MURARI'),
    params: maschiMurariParamsSchema.nullable().optional(),
    shearReinforcementVerifyExecutionInput:
      maschiMurariShearReinforcementVerifyExecutionSchema.optional().nullable(),
    shearReinforcementCalculationResult:
      maschiMurariShearReinforcementCalculationResultSchema
        .optional()
        .nullable(),
    flexuralReinforcementVerifyExecutionInput:
      maschiMurariFlexuralReinforcementVerifyExecutionSchema
        .optional()
        .nullable(),
    flexuralReinforcementCalculationResult:
      maschiMurariFlexuralReinforcementCalculationResultSchema
        .optional()
        .nullable(),
  }),
  moduleBase.extend({
    type: z.literal('UNKNOWN'),
  }),
])

export const moduleWithParamsSchema = moduleSchema

export type ModuleWithParams = z.infer<typeof moduleWithParamsSchema>

export type ModuleWithParamsRectangularBeam = Extract<
  ModuleWithParams,
  { type: 'RECTANGULAR_BEAM' }
>
export type ModuleWithParamsPillar = Extract<
  ModuleWithParams,
  { type: 'PILLAR' }
>
export type ModuleWithParamsWood = Extract<ModuleWithParams, { type: 'WOOD' }>

export type ModuleWithParamsSlab = Extract<ModuleWithParams, { type: 'SLAB' }>

export type ModuleWithParamsAntiOverturning = Extract<
  ModuleWithParams,
  { type: 'ANTI_OVERTURNING' }
>

export type ModuleWithParamsTBeam = Extract<
  ModuleWithParams,
  { type: 'T_BEAM' }
>

export type ModuleWithParamsFrcmColumn = Extract<
  ModuleWithParams,
  { type: 'FRCM_COLUMN' }
>

export type ModuleWithParamsMaschiMurari = Extract<
  ModuleWithParams,
  { type: 'MASCHI_MURARI' }
>

export type ModuleWithParamsCrm = Extract<ModuleWithParams, { type: 'CRM' }>

export type ModuleType = ModuleWithParams['type']
