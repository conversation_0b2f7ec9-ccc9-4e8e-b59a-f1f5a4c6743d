module.exports = [
"[project]/.next-internal/server/app/[locale]/dashboard/projects/[projectId]/modules/[moduleId]/page/actions.js [app-rsc] (server actions loader, ecmascript)", ((__turbopack_context__, module, exports) => {

}),
"[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js Server Component)", ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/favicon.ico.mjs { IMAGE => \"[project]/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}),
"[project]/src/app/[locale]/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)", ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/[locale]/layout.tsx [app-rsc] (ecmascript)"));
}),
"[project]/src/app/[locale]/not-found.tsx [app-rsc] (ecmascript, Next.js Server Component)", ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/[locale]/not-found.tsx [app-rsc] (ecmascript)"));
}),
"[project]/src/app/[locale]/dashboard/layout.tsx [app-rsc] (ecmascript, Next.js Server Component)", ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/[locale]/dashboard/layout.tsx [app-rsc] (ecmascript)"));
}),
"[project]/src/app/[locale]/dashboard/projects/[projectId]/error.tsx [app-rsc] (ecmascript, Next.js Server Component)", ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/[locale]/dashboard/projects/[projectId]/error.tsx [app-rsc] (ecmascript)"));
}),
"[project]/src/app/[locale]/dashboard/projects/[projectId]/modules/[moduleId]/error.tsx [app-rsc] (ecmascript, Next.js Server Component)", ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/[locale]/dashboard/projects/[projectId]/modules/[moduleId]/error.tsx [app-rsc] (ecmascript)"));
}),
"[project]/src/lib/utils.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "cn",
    ()=>cn
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/clsx/dist/clsx.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/tailwind-merge/dist/bundle-mjs.mjs [app-rsc] (ecmascript)");
;
;
function cn(...inputs) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$tailwind$2d$merge$2f$dist$2f$bundle$2d$mjs$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["twMerge"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$clsx$2f$dist$2f$clsx$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["clsx"])(inputs));
}
}),
"[project]/src/components/ui/breadcrumb.tsx [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "Breadcrumb",
    ()=>Breadcrumb,
    "BreadcrumbEllipsis",
    ()=>BreadcrumbEllipsis,
    "BreadcrumbItem",
    ()=>BreadcrumbItem,
    "BreadcrumbLink",
    ()=>BreadcrumbLink,
    "BreadcrumbList",
    ()=>BreadcrumbList,
    "BreadcrumbPage",
    ()=>BreadcrumbPage,
    "BreadcrumbSeparator",
    ()=>BreadcrumbSeparator
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-slot/dist/index.mjs [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-right.js [app-rsc] (ecmascript) <export default as ChevronRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$ellipsis$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__MoreHorizontal$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/ellipsis.js [app-rsc] (ecmascript) <export default as MoreHorizontal>");
;
;
;
;
function Breadcrumb({ ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
        "aria-label": "breadcrumb",
        "data-slot": "breadcrumb",
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/breadcrumb.tsx",
        lineNumber: 7,
        columnNumber: 10
    }, this);
}
function BreadcrumbList({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("ol", {
        "data-slot": "breadcrumb-list",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cn"])('text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5', className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/breadcrumb.tsx",
        lineNumber: 12,
        columnNumber: 5
    }, this);
}
function BreadcrumbItem({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
        "data-slot": "breadcrumb-item",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cn"])('inline-flex items-center gap-1.5', className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/breadcrumb.tsx",
        lineNumber: 25,
        columnNumber: 5
    }, this);
}
function BreadcrumbLink({ asChild, className, ...props }) {
    const Comp = asChild ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$slot$2f$dist$2f$index$2e$mjs__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Slot"] : 'a';
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(Comp, {
        "data-slot": "breadcrumb-link",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cn"])('hover:text-foreground transition-colors', className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/breadcrumb.tsx",
        lineNumber: 43,
        columnNumber: 5
    }, this);
}
function BreadcrumbPage({ className, ...props }) {
    return(// biome-ignore lint/a11y/useFocusableInteractive: from shadcn/ui/components/breadcrumb
    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
        "data-slot": "breadcrumb-page",
        role: "link",
        "aria-disabled": "true",
        "aria-current": "page",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cn"])('text-foreground font-normal', className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/breadcrumb.tsx",
        lineNumber: 54,
        columnNumber: 5
    }, this));
}
function BreadcrumbSeparator({ children, className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
        "data-slot": "breadcrumb-separator",
        role: "presentation",
        "aria-hidden": "true",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cn"])('[&>svg]:size-3.5', className),
        ...props,
        children: children ?? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__["ChevronRight"], {}, void 0, false, {
            fileName: "[project]/src/components/ui/breadcrumb.tsx",
            lineNumber: 78,
            columnNumber: 20
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/components/ui/breadcrumb.tsx",
        lineNumber: 71,
        columnNumber: 5
    }, this);
}
function BreadcrumbEllipsis({ className, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
        "data-slot": "breadcrumb-ellipsis",
        role: "presentation",
        "aria-hidden": "true",
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["cn"])('flex size-9 items-center justify-center', className),
        ...props,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$ellipsis$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__MoreHorizontal$3e$__["MoreHorizontal"], {
                className: "size-4"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/breadcrumb.tsx",
                lineNumber: 95,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: "sr-only",
                children: "More"
            }, void 0, false, {
                fileName: "[project]/src/components/ui/breadcrumb.tsx",
                lineNumber: 96,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/breadcrumb.tsx",
        lineNumber: 88,
        columnNumber: 5
    }, this);
}
;
}),
"[project]/src/components/common/atlas/breadcrumbs.tsx [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "Breadcrumbs",
    ()=>Breadcrumbs
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/breadcrumb.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react.js [app-rsc] (ecmascript)");
;
;
;
const Breadcrumbs = ({ content })=>{
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Breadcrumb"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BreadcrumbList"], {
            children: content.map(({ label, href }, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Fragment"], {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BreadcrumbItem"], {
                            children: href ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BreadcrumbLink"], {
                                href: href,
                                children: label
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/breadcrumbs.tsx",
                                lineNumber: 22,
                                columnNumber: 17
                            }, ("TURBOPACK compile-time value", void 0)) : label
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/breadcrumbs.tsx",
                            lineNumber: 20,
                            columnNumber: 13
                        }, ("TURBOPACK compile-time value", void 0)),
                        index !== content.length - 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$breadcrumb$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["BreadcrumbSeparator"], {}, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/breadcrumbs.tsx",
                            lineNumber: 27,
                            columnNumber: 46
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, href ?? `${label}-${index}`, true, {
                    fileName: "[project]/src/components/common/atlas/breadcrumbs.tsx",
                    lineNumber: 19,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0)))
        }, void 0, false, {
            fileName: "[project]/src/components/common/atlas/breadcrumbs.tsx",
            lineNumber: 17,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/breadcrumbs.tsx",
        lineNumber: 16,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
}),
"[project]/src/components/common/atlas/module-detail/module-detail-content.tsx [app-rsc] (client reference proxy) <module evaluation>", ((__turbopack_context__) => {
"use strict";

// This file is generated by next-core EcmascriptClientReferenceModule.
__turbopack_context__.s([
    "ModuleDetailContent",
    ()=>ModuleDetailContent,
    "editModuleModalOpenAtom",
    ()=>editModuleModalOpenAtom,
    "moduleToDeleteAtom",
    ()=>moduleToDeleteAtom
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const ModuleDetailContent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ModuleDetailContent() from the server but ModuleDetailContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/common/atlas/module-detail/module-detail-content.tsx <module evaluation>", "ModuleDetailContent");
const editModuleModalOpenAtom = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call editModuleModalOpenAtom() from the server but editModuleModalOpenAtom is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/common/atlas/module-detail/module-detail-content.tsx <module evaluation>", "editModuleModalOpenAtom");
const moduleToDeleteAtom = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call moduleToDeleteAtom() from the server but moduleToDeleteAtom is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/common/atlas/module-detail/module-detail-content.tsx <module evaluation>", "moduleToDeleteAtom");
}),
"[project]/src/components/common/atlas/module-detail/module-detail-content.tsx [app-rsc] (client reference proxy)", ((__turbopack_context__) => {
"use strict";

// This file is generated by next-core EcmascriptClientReferenceModule.
__turbopack_context__.s([
    "ModuleDetailContent",
    ()=>ModuleDetailContent,
    "editModuleModalOpenAtom",
    ()=>editModuleModalOpenAtom,
    "moduleToDeleteAtom",
    ()=>moduleToDeleteAtom
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const ModuleDetailContent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call ModuleDetailContent() from the server but ModuleDetailContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/common/atlas/module-detail/module-detail-content.tsx", "ModuleDetailContent");
const editModuleModalOpenAtom = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call editModuleModalOpenAtom() from the server but editModuleModalOpenAtom is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/common/atlas/module-detail/module-detail-content.tsx", "editModuleModalOpenAtom");
const moduleToDeleteAtom = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call moduleToDeleteAtom() from the server but moduleToDeleteAtom is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/common/atlas/module-detail/module-detail-content.tsx", "moduleToDeleteAtom");
}),
"[project]/src/components/common/atlas/module-detail/module-detail-content.tsx [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$module$2d$detail$2d$content$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/module-detail-content.tsx [app-rsc] (client reference proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$module$2d$detail$2d$content$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/module-detail-content.tsx [app-rsc] (client reference proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$module$2d$detail$2d$content$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__);
}),
"[project]/src/components/ui/separator.tsx [app-rsc] (client reference proxy) <module evaluation>", ((__turbopack_context__) => {
"use strict";

// This file is generated by next-core EcmascriptClientReferenceModule.
__turbopack_context__.s([
    "Separator",
    ()=>Separator
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const Separator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/separator.tsx <module evaluation>", "Separator");
}),
"[project]/src/components/ui/separator.tsx [app-rsc] (client reference proxy)", ((__turbopack_context__) => {
"use strict";

// This file is generated by next-core EcmascriptClientReferenceModule.
__turbopack_context__.s([
    "Separator",
    ()=>Separator
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-turbopack-server.js [app-rsc] (ecmascript)");
;
const Separator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$server$2d$dom$2d$turbopack$2d$server$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["registerClientReference"])(function() {
    throw new Error("Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.");
}, "[project]/src/components/ui/separator.tsx", "Separator");
}),
"[project]/src/components/ui/separator.tsx [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/ui/separator.tsx [app-rsc] (client reference proxy) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__ = __turbopack_context__.i("[project]/src/components/ui/separator.tsx [app-rsc] (client reference proxy)");
;
__turbopack_context__.n(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$rsc$5d$__$28$client__reference__proxy$29$__);
}),
"[project]/src/lib/api/apiErrors.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "handleApiError",
    ()=>handleApiError
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-rsc] (ecmascript)");
;
const httpCodeMap = {
    400: '400_BAD_REQUEST',
    401: '401_UNAUTHORIZED',
    403: '403_FORBIDDEN',
    404: '404_NOT_FOUND',
    409: '409_CONFLICT',
    422: '422_UNPROCESSABLE_ENTITY',
    429: '429_TOO_MANY_REQUESTS',
    500: '500_INTERNAL_SERVER_ERROR',
    502: '502_BAD_GATEWAY',
    503: '503_SERVICE_UNAVAILABLE',
    504: '504_GATEWAY_TIMEOUT'
};
const handleApiError = (error)=>{
    console.log('handleApiError', error);
    if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].isAxiosError(error)) {
        const apiError = error.response?.data;
        const message = typeof apiError?.message === 'string' ? apiError.message : error.message || 'error.unknown';
        const code = error.response?.status && httpCodeMap[error.response?.status] ? httpCodeMap[error.response?.status] : 'UNKNOWN_ERROR';
        return {
            type: 'ApiError',
            message,
            code
        };
    }
    if (error instanceof Error) {
        return {
            type: 'ApiError',
            message: error.message || 'error.unknown',
            code: 'UNKNOWN_ERROR'
        };
    }
    return {
        type: 'ApiError',
        message: 'error.unknown',
        code: 'UNKNOWN_ERROR'
    };
};
}),
"[project]/src/functions/zod/date-transform.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "dateTransform",
    ()=>dateTransform
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-rsc] (ecmascript) <export * as z>");
;
const dateTransform = (val, ctx)=>{
    if (!val) {
        return undefined;
    }
    const date = new Date(val);
    if (Number.isNaN(date.getTime())) {
        ctx.addIssue({
            code: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].ZodIssueCode.custom,
            message: 'Invalid date format'
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].NEVER;
    }
    return date;
};
}),
"[project]/src/functions/zod/null-to-object-transform.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "nullToObjectTransform",
    ()=>nullToObjectTransform
]);
function nullToObjectTransform(val) {
    return val ?? {};
}
}),
"[project]/src/functions/zod/null-to-undefined-transform.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "nullToUndefinedTransform",
    ()=>nullToUndefinedTransform
]);
function nullToUndefinedTransform(val) {
    return val ?? undefined;
}
}),
"[project]/src/constants/product.tsx [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "PRODUCT_FIBER_TYPE",
    ()=>PRODUCT_FIBER_TYPE,
    "productFiberType",
    ()=>productFiberType
]);
var productFiberType = /*#__PURE__*/ function(productFiberType) {
    productFiberType["BASALT"] = "basalt";
    productFiberType["CARBON"] = "carbon";
    productFiberType["GALVANIZED_STEEL"] = "galvanized_steel";
    productFiberType["GLASS"] = "glass";
    productFiberType["PREFORMED_CARBON"] = "preformed_carbon";
    productFiberType["STEEL"] = "steel";
    productFiberType["STEEL_STRAIGHT"] = "steel-straight";
    return productFiberType;
}({});
const PRODUCT_FIBER_TYPE = Object.values(productFiberType);
}),
"[project]/src/lib/api/products/schemas/product.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "paginatedProductSchema",
    ()=>paginatedProductSchema,
    "productSchema",
    ()=>productSchema
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$product$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/product.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/functions/zod/null-to-undefined-transform.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-rsc] (ecmascript) <export * as z>");
;
;
;
// Helper schema for fields that can be either a number or an object with parsedValue
const numericOrParsedValueSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].union([
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        source: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        parsedValue: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()
    })
]).transform((val)=>{
    if (typeof val === 'number') {
        return val;
    }
    return val.parsedValue;
}).nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]);
const productSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
    name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    fiberType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$product$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PRODUCT_FIBER_TYPE"]).nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    availableWidths: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()).optional().nullable(),
    orientation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    thickness: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    tensileStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    elasticModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    characteristicCylindricalCompressiveStrengthFrcMatrix: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    documentationLink: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    productType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    ultimateStrain: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    designDeformation: numericOrParsedValueSchema,
    unitStrengthOfTheMesh: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    width: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    density: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    maxResistance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    weight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    crossSectionArea: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    diameter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    pullOutResistance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    connectorsNumberAlongLength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    designStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    maxLayerNumber: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    systemDeformation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    adhesionToConcrete: numericOrParsedValueSchema,
    shearStress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    toughnessClass: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    cylindricCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    characteristicTensileStrength: numericOrParsedValueSchema,
    specificWeight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    facingPerformance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object().optional().nullable(),
    reinforcementTensileStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    reinforcementUltimateStrain: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    conventionalStressLimit: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].record(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()).nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    conventionalStrainLimit: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].record(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()).nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    matrixBreachStress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"])
});
const paginatedProductSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    content: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(productSchema),
    pageable: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        pageNumber: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        pageSize: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        sort: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
            empty: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean(),
            unsorted: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean(),
            sorted: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean()
        }),
        offset: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        unpaged: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean(),
        paged: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean()
    }),
    last: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean(),
    totalElements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    totalPages: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    first: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean(),
    sort: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        empty: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean(),
        unsorted: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean(),
        sorted: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean()
    }),
    size: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    number: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    numberOfElements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    empty: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean()
});
}),
"[project]/src/constants/module.tsx [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "BINDER_MIXTURE_INJECTIONS",
    ()=>BINDER_MIXTURE_INJECTIONS,
    "BUILDING_TYPE",
    ()=>BUILDING_TYPE,
    "CRM_MASONRY_TYPE",
    ()=>CRM_MASONRY_TYPE,
    "ENHANCEMENT_CHARACTERISTICS",
    ()=>ENHANCEMENT_CHARACTERISTICS,
    "FACING_MATERIAL",
    ()=>FACING_MATERIAL,
    "FRM_GEOMETRY_TOPOLOGY",
    ()=>FRM_GEOMETRY_TOPOLOGY,
    "INFILL_WALL_TOPOLOGY",
    ()=>INFILL_WALL_TOPOLOGY,
    "MODULE_BINDER_MIXTURE_INJECTIONS",
    ()=>MODULE_BINDER_MIXTURE_INJECTIONS,
    "MODULE_BUILDING_TYPE",
    ()=>MODULE_BUILDING_TYPE,
    "MODULE_CATEGORY",
    ()=>MODULE_CATEGORY,
    "MODULE_CRM_MASONRY_TYPE",
    ()=>MODULE_CRM_MASONRY_TYPE,
    "MODULE_ENHANCEMENT_CHARACTERISTICS",
    ()=>MODULE_ENHANCEMENT_CHARACTERISTICS,
    "MODULE_EXECUTION_CLASS",
    ()=>MODULE_EXECUTION_CLASS,
    "MODULE_FACING_MATERIAL",
    ()=>MODULE_FACING_MATERIAL,
    "MODULE_FRM_GEOMETRY_TOPOLOGY",
    ()=>MODULE_FRM_GEOMETRY_TOPOLOGY,
    "MODULE_GEOMETRY_EXPOSURE",
    ()=>MODULE_GEOMETRY_EXPOSURE,
    "MODULE_INFILL_WALL_TOPOLOGY",
    ()=>MODULE_INFILL_WALL_TOPOLOGY,
    "MODULE_LOAD_RESISTING_CATEGORY",
    ()=>MODULE_LOAD_RESISTING_CATEGORY,
    "MODULE_MATERIAL_CLASS",
    ()=>MODULE_MATERIAL_CLASS,
    "MODULE_MATERIAL_KNOWLEDGE_LEVEL",
    ()=>MODULE_MATERIAL_KNOWLEDGE_LEVEL,
    "MODULE_POLARITY",
    ()=>MODULE_POLARITY,
    "MODULE_REINFORCEMENT_APPLICATION_TYPE",
    ()=>MODULE_REINFORCEMENT_APPLICATION_TYPE,
    "MODULE_REINFORCEMENT_ARRANGEMENT",
    ()=>MODULE_REINFORCEMENT_ARRANGEMENT,
    "MODULE_REINFORCEMENT_FAILURE_MODE",
    ()=>MODULE_REINFORCEMENT_FAILURE_MODE,
    "MODULE_SUBSOIL_CATEGORY",
    ()=>MODULE_SUBSOIL_CATEGORY,
    "MODULE_TOPOGRAPHIC_CATEGORY",
    ()=>MODULE_TOPOGRAPHIC_CATEGORY,
    "MODULE_TYPES",
    ()=>MODULE_TYPES,
    "PANEL_WIDTH_DEFAULT",
    ()=>PANEL_WIDTH_DEFAULT,
    "REINFORCEMENT_APPLICATION_TYPE",
    ()=>REINFORCEMENT_APPLICATION_TYPE,
    "REINFORCEMENT_ARRANGEMENT",
    ()=>REINFORCEMENT_ARRANGEMENT,
    "REINFORCEMENT_FAILURE_MODE",
    ()=>REINFORCEMENT_FAILURE_MODE,
    "REINFORCEMENT_LAYOUTS",
    ()=>REINFORCEMENT_LAYOUTS,
    "SUBSOIL_CATEGORY",
    ()=>SUBSOIL_CATEGORY,
    "TOPOGRAPHIC_CATEGORY",
    ()=>TOPOGRAPHIC_CATEGORY,
    "averageCompressiveStrengthValues",
    ()=>averageCompressiveStrengthValues,
    "averageNormalElasticityModulusValues",
    ()=>averageNormalElasticityModulusValues,
    "averageShearElasticityModulusValues",
    ()=>averageShearElasticityModulusValues,
    "averageShearStrengthIrregularMasonryValues",
    ()=>averageShearStrengthIrregularMasonryValues,
    "averageShearStrengthRegularMasonryValues",
    ()=>averageShearStrengthRegularMasonryValues,
    "bindingCoefficientValues",
    ()=>bindingCoefficientValues,
    "categorizedModuleTypes",
    ()=>categorizedModuleTypes,
    "characteristicCompressiveStrengthValues",
    ()=>characteristicCompressiveStrengthValues,
    "characteristicNormalElasticityModulusValues",
    ()=>characteristicNormalElasticityModulusValues,
    "characteristicShearElasticityModulusValues",
    ()=>characteristicShearElasticityModulusValues,
    "characteristicShearStrengthValues",
    ()=>characteristicShearStrengthValues,
    "correctiveCoefficientValues",
    ()=>correctiveCoefficientValues,
    "executionClass",
    ()=>executionClass,
    "loadResistingCategory",
    ()=>loadResistingCategory,
    "masonryDensityValues",
    ()=>masonryDensityValues,
    "masonryStrengthSafetyFactorMapping",
    ()=>masonryStrengthSafetyFactorMapping,
    "maxAmplficationCoefficientValues",
    ()=>maxAmplficationCoefficientValues,
    "moduleGeometryExposure",
    ()=>moduleGeometryExposure,
    "moduleMaterialClass",
    ()=>moduleMaterialClass,
    "moduleMaterialKnowledgeLevel",
    ()=>moduleMaterialKnowledgeLevel,
    "moduleMaterialKnowledgeLevelValues",
    ()=>moduleMaterialKnowledgeLevelValues,
    "modulePolarity",
    ()=>modulePolarity,
    "postInterventionFirstSideReinforcedPlasterCoefficientValues",
    ()=>postInterventionFirstSideReinforcedPlasterCoefficientValues,
    "specificWeightValues",
    ()=>specificWeightValues,
    "ssCoefficientValues",
    ()=>ssCoefficientValues,
    "topographicCoefficientValues",
    ()=>topographicCoefficientValues
]);
const PANEL_WIDTH_DEFAULT = 1000;
var modulePolarity = /*#__PURE__*/ function(modulePolarity) {
    modulePolarity["POSITIVE"] = "POSITIVE";
    modulePolarity["NEGATIVE"] = "NEGATIVE";
    return modulePolarity;
}({});
const MODULE_POLARITY = Object.values(modulePolarity);
const MODULE_TYPES = [
    'RECTANGULAR_BEAM',
    'PILLAR',
    'T_BEAM',
    'SLAB',
    'ANTI_OVERTURNING',
    'COLUMN',
    'MASONRY',
    'T_BEAM',
    'WOOD',
    'CRM',
    'FRCM_COLUMN',
    'MASCHI_MURARI'
];
const MODULE_CATEGORY = [
    'REINFORCED_CONCRETE',
    'MASONRY',
    'WOOD',
    'FRCM_COLUMN',
    'MASCHI_MURARI'
];
const categorizedModuleTypes = [
    {
        category: 'REINFORCED_CONCRETE',
        types: [
            {
                name: 'RECTANGULAR_BEAM',
                enabled: true
            },
            {
                name: 'PILLAR',
                enabled: true
            },
            {
                name: 'T_BEAM',
                enabled: true
            },
            {
                name: 'SLAB',
                enabled: true
            }
        ],
        enabled: true
    },
    {
        category: 'MASONRY',
        types: [
            {
                name: 'ANTI_OVERTURNING',
                enabled: true
            },
            {
                name: 'CRM',
                enabled: true
            },
            {
                name: 'FRCM_COLUMN',
                enabled: true
            },
            {
                name: 'MASCHI_MURARI',
                enabled: true
            }
        ],
        enabled: true
    },
    {
        category: 'WOOD',
        types: [
            {
                name: 'WOOD',
                enabled: true
            }
        ],
        enabled: true
    }
];
const REINFORCEMENT_LAYOUTS = [
    'OPEN_STIRRUP',
    'CLOSED_STIRRUP'
];
var moduleGeometryExposure = /*#__PURE__*/ function(moduleGeometryExposure) {
    moduleGeometryExposure["INTERNAL"] = "INTERNAL";
    moduleGeometryExposure["EXTERNAL"] = "EXTERNAL";
    moduleGeometryExposure["AGGRESSIVE"] = "AGGRESSIVE";
    return moduleGeometryExposure;
}({});
const MODULE_GEOMETRY_EXPOSURE = Object.values(moduleGeometryExposure);
var moduleMaterialKnowledgeLevel = /*#__PURE__*/ function(moduleMaterialKnowledgeLevel) {
    moduleMaterialKnowledgeLevel["LC1"] = "LC1";
    moduleMaterialKnowledgeLevel["LC2"] = "LC2";
    moduleMaterialKnowledgeLevel["LC3"] = "LC3";
    return moduleMaterialKnowledgeLevel;
}({});
const moduleMaterialKnowledgeLevelValues = {
    LC1: 1.35,
    LC2: 1.2,
    LC3: 1
};
const MODULE_MATERIAL_KNOWLEDGE_LEVEL = Object.values(moduleMaterialKnowledgeLevel);
var moduleMaterialClass = /*#__PURE__*/ function(moduleMaterialClass) {
    moduleMaterialClass["DUCTILE"] = "DUCTILE";
    moduleMaterialClass["BRITTLE"] = "BRITTLE";
    return moduleMaterialClass;
}({});
const MODULE_MATERIAL_CLASS = Object.values(moduleMaterialClass);
var SUBSOIL_CATEGORY = /*#__PURE__*/ function(SUBSOIL_CATEGORY) {
    SUBSOIL_CATEGORY["A"] = "A";
    SUBSOIL_CATEGORY["B"] = "B";
    SUBSOIL_CATEGORY["C"] = "C";
    SUBSOIL_CATEGORY["D"] = "D";
    SUBSOIL_CATEGORY["E"] = "E";
    return SUBSOIL_CATEGORY;
}({});
const ssCoefficientValues = {
    A: 1,
    B: 1.2,
    C: 1.41,
    D: 1.68,
    E: 1.47
};
const MODULE_SUBSOIL_CATEGORY = Object.values(SUBSOIL_CATEGORY);
var TOPOGRAPHIC_CATEGORY = /*#__PURE__*/ function(TOPOGRAPHIC_CATEGORY) {
    TOPOGRAPHIC_CATEGORY["T1"] = "T1";
    TOPOGRAPHIC_CATEGORY["T2"] = "T2";
    TOPOGRAPHIC_CATEGORY["T3"] = "T3";
    TOPOGRAPHIC_CATEGORY["T4"] = "T4";
    return TOPOGRAPHIC_CATEGORY;
}({});
const topographicCoefficientValues = {
    T1: 1,
    T2: 1.2,
    T3: 1.2,
    T4: 1.4
};
const MODULE_TOPOGRAPHIC_CATEGORY = Object.values(TOPOGRAPHIC_CATEGORY);
var BUILDING_TYPE = /*#__PURE__*/ function(BUILDING_TYPE) {
    BUILDING_TYPE["MASONRY"] = "MASONRY";
    BUILDING_TYPE["REINFORCED_CONCRETE"] = "REINFORCED_CONCRETE";
    return BUILDING_TYPE;
}({});
const MODULE_BUILDING_TYPE = Object.values(BUILDING_TYPE);
var INFILL_WALL_TOPOLOGY = /*#__PURE__*/ function(INFILL_WALL_TOPOLOGY) {
    INFILL_WALL_TOPOLOGY["SINGLE"] = "SINGLE";
    INFILL_WALL_TOPOLOGY["DOUBLE"] = "DOUBLE";
    return INFILL_WALL_TOPOLOGY;
}({});
const MODULE_INFILL_WALL_TOPOLOGY = Object.values(INFILL_WALL_TOPOLOGY);
var FACING_MATERIAL = /*#__PURE__*/ function(FACING_MATERIAL) {
    FACING_MATERIAL["BRICK"] = "BRICK";
    FACING_MATERIAL["TUFF"] = "TUFF";
    FACING_MATERIAL["STONE"] = "STONE";
    return FACING_MATERIAL;
}({});
const MODULE_FACING_MATERIAL = Object.values(FACING_MATERIAL);
var executionClass = /*#__PURE__*/ function(executionClass) {
    executionClass["ONE"] = "ONE";
    executionClass["TWO"] = "TWO";
    return executionClass;
}({});
const MODULE_EXECUTION_CLASS = Object.values(executionClass);
var loadResistingCategory = /*#__PURE__*/ function(loadResistingCategory) {
    loadResistingCategory["MASONRY_CAT_I_AND_MORTAR_WITH_GUARANTEED_PERFORMANCE"] = "MASONRY_CAT_I_AND_MORTAR_WITH_GUARANTEED_PERFORMANCE";
    loadResistingCategory["MASONRY_CAT_I_AND_MORTAR_WITH_PRESCRIBED_PERFORMANCE"] = "MASONRY_CAT_I_AND_MORTAR_WITH_PRESCRIBED_PERFORMANCE";
    loadResistingCategory["MASONRY_CAT_II_AND_ANY_TYPE_OF_MORTAR"] = "MASONRY_CAT_II_AND_ANY_TYPE_OF_MORTAR";
    return loadResistingCategory;
}({});
const MODULE_LOAD_RESISTING_CATEGORY = Object.values(loadResistingCategory);
const masonryStrengthSafetyFactorMapping = {
    ["MASONRY_CAT_I_AND_MORTAR_WITH_GUARANTEED_PERFORMANCE"]: {
        ["ONE"]: 2,
        ["TWO"]: 2.5
    },
    ["MASONRY_CAT_I_AND_MORTAR_WITH_PRESCRIBED_PERFORMANCE"]: {
        ["ONE"]: 2.2,
        ["TWO"]: 2.7
    },
    ["MASONRY_CAT_II_AND_ANY_TYPE_OF_MORTAR"]: {
        ["ONE"]: 2.5,
        ["TWO"]: 3
    }
};
var CRM_MASONRY_TYPE = /*#__PURE__*/ function(CRM_MASONRY_TYPE) {
    CRM_MASONRY_TYPE["MURATURA_IN_PIETRAME_DISORDINATA"] = "MURATURA_IN_PIETRAME_DISORDINATA";
    CRM_MASONRY_TYPE["MURATURA_A_CONCI_SBOZZATI"] = "MURATURA_A_CONCI_SBOZZATI";
    CRM_MASONRY_TYPE["MURATURA_IN_PIETRA_A_SPACCO"] = "MURATURA_IN_PIETRA_A_SPACCO";
    CRM_MASONRY_TYPE["MURATURA_IRREGOLARE_DI_PIETRA_TENERA"] = "MURATURA_IRREGOLARE_DI_PIETRA_TENERA";
    CRM_MASONRY_TYPE["MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA"] = "MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA";
    CRM_MASONRY_TYPE["MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI"] = "MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI";
    CRM_MASONRY_TYPE["MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE"] = "MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE";
    CRM_MASONRY_TYPE["MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA"] = "MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA";
    return CRM_MASONRY_TYPE;
}({});
const MODULE_CRM_MASONRY_TYPE = Object.values(CRM_MASONRY_TYPE);
var ENHANCEMENT_CHARACTERISTICS = /*#__PURE__*/ function(ENHANCEMENT_CHARACTERISTICS) {
    ENHANCEMENT_CHARACTERISTICS["MALTA_BUONA"] = "MALTA_BUONA";
    ENHANCEMENT_CHARACTERISTICS["NON_PRESENTI"] = "NON_PRESENTI";
    return ENHANCEMENT_CHARACTERISTICS;
}({});
const MODULE_ENHANCEMENT_CHARACTERISTICS = Object.values(ENHANCEMENT_CHARACTERISTICS);
const correctiveCoefficientValues = {
    ["MALTA_BUONA"]: {
        ["MURATURA_IN_PIETRAME_DISORDINATA"]: 1.5,
        ["MURATURA_A_CONCI_SBOZZATI"]: 1.4,
        ["MURATURA_IN_PIETRA_A_SPACCO"]: 1.3,
        ["MURATURA_IRREGOLARE_DI_PIETRA_TENERA"]: 1.5,
        ["MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA"]: 1.6,
        ["MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI"]: 1.2,
        ["MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE"]: 0,
        ["MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA"]: 1.2
    },
    ["NON_PRESENTI"]: {
        ["MURATURA_IN_PIETRAME_DISORDINATA"]: 1,
        ["MURATURA_A_CONCI_SBOZZATI"]: 1,
        ["MURATURA_IN_PIETRA_A_SPACCO"]: 1,
        ["MURATURA_IRREGOLARE_DI_PIETRA_TENERA"]: 1,
        ["MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA"]: 1,
        ["MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI"]: 1,
        ["MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE"]: 1,
        ["MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA"]: 1
    }
};
const averageCompressiveStrengthValues = {
    ["MURATURA_IN_PIETRAME_DISORDINATA"]: {
        LC1: 1.0,
        LC2: 1.5,
        LC3: 2.0
    },
    ["MURATURA_A_CONCI_SBOZZATI"]: {
        LC1: 2.0,
        LC2: 2.0,
        LC3: 2.0
    },
    ["MURATURA_IN_PIETRA_A_SPACCO"]: {
        LC1: 2.6,
        LC2: 3.2,
        LC3: 3.8
    },
    ["MURATURA_IRREGOLARE_DI_PIETRA_TENERA"]: {
        LC1: 1.4,
        LC2: 1.8,
        LC3: 2.2
    },
    ["MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA"]: {
        LC1: 2.0,
        LC2: 2.6,
        LC3: 3.2
    },
    ["MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI"]: {
        LC1: 5.8,
        LC2: 7.0,
        LC3: 8.2
    },
    ["MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE"]: {
        LC1: 2.6,
        LC2: 3.45,
        LC3: 4.3
    },
    ["MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA"]: {
        LC1: 5.0,
        LC2: 6.5,
        LC3: 8.0
    }
};
const averageShearStrengthRegularMasonryValues = {
    ["MURATURA_IN_PIETRAME_DISORDINATA"]: {
        LC1: 0.018,
        LC2: 0.025,
        LC3: 0.032
    },
    ["MURATURA_A_CONCI_SBOZZATI"]: {
        LC1: 0.035,
        LC2: 0.043,
        LC3: 0.051
    },
    ["MURATURA_IN_PIETRA_A_SPACCO"]: {
        LC1: 0.056,
        LC2: 0.065,
        LC3: 0.074
    },
    ["MURATURA_IRREGOLARE_DI_PIETRA_TENERA"]: {
        LC1: 0.028,
        LC2: 0.035,
        LC3: 0.042
    },
    ["MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA"]: {
        LC1: 0.04,
        LC2: 0.06,
        LC3: 0.08
    },
    ["MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI"]: {
        LC1: 0.09,
        LC2: 0.105,
        LC3: 0.12
    },
    ["MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE"]: {
        LC1: 0.05,
        LC2: 0.09,
        LC3: 0.13
    },
    ["MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA"]: {
        LC1: 0.08,
        LC2: 0.125,
        LC3: 0.17
    }
};
const averageShearStrengthIrregularMasonryValues = {
    ["MURATURA_IN_PIETRAME_DISORDINATA"]: {
        LC1: 0.0,
        LC2: 0.0,
        LC3: 0.0
    },
    ["MURATURA_A_CONCI_SBOZZATI"]: {
        LC1: 0.0,
        LC2: 0.0,
        LC3: 0.0
    },
    ["MURATURA_IN_PIETRA_A_SPACCO"]: {
        LC1: 0.0,
        LC2: 0.0,
        LC3: 0.0
    },
    ["MURATURA_IRREGOLARE_DI_PIETRA_TENERA"]: {
        LC1: 0.0,
        LC2: 0.0,
        LC3: 0.0
    },
    ["MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA"]: {
        LC1: 0.1,
        LC2: 0.145,
        LC3: 0.19
    },
    ["MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI"]: {
        LC1: 0.18,
        LC2: 0.23,
        LC3: 0.28
    },
    ["MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE"]: {
        LC1: 0.13,
        LC2: 0.2,
        LC3: 0.27
    },
    ["MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA"]: {
        LC1: 0.2,
        LC2: 0.28,
        LC3: 0.36
    }
};
const averageNormalElasticityModulusValues = {
    ["MURATURA_IN_PIETRAME_DISORDINATA"]: {
        LC1: 870,
        LC2: 870,
        LC3: 870
    },
    ["MURATURA_A_CONCI_SBOZZATI"]: {
        LC1: 1230,
        LC2: 1230,
        LC3: 1230
    },
    ["MURATURA_IN_PIETRA_A_SPACCO"]: {
        LC1: 1740,
        LC2: 1740,
        LC3: 1740
    },
    ["MURATURA_IRREGOLARE_DI_PIETRA_TENERA"]: {
        LC1: 1080,
        LC2: 1080,
        LC3: 1080
    },
    ["MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA"]: {
        LC1: 1410,
        LC2: 1410,
        LC3: 1410
    },
    ["MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI"]: {
        LC1: 2850,
        LC2: 2850,
        LC3: 2850
    },
    ["MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE"]: {
        LC1: 1500,
        LC2: 1500,
        LC3: 1500
    },
    ["MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA"]: {
        LC1: 4550,
        LC2: 4550,
        LC3: 4550
    }
};
const averageShearElasticityModulusValues = {
    ["MURATURA_IN_PIETRAME_DISORDINATA"]: {
        LC1: 290,
        LC2: 290,
        LC3: 290
    },
    ["MURATURA_A_CONCI_SBOZZATI"]: {
        LC1: 410,
        LC2: 410,
        LC3: 410
    },
    ["MURATURA_IN_PIETRA_A_SPACCO"]: {
        LC1: 580,
        LC2: 580,
        LC3: 580
    },
    ["MURATURA_IRREGOLARE_DI_PIETRA_TENERA"]: {
        LC1: 360,
        LC2: 360,
        LC3: 360
    },
    ["MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA"]: {
        LC1: 450,
        LC2: 450,
        LC3: 450
    },
    ["MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI"]: {
        LC1: 950,
        LC2: 950,
        LC3: 950
    },
    ["MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE"]: {
        LC1: 500,
        LC2: 500,
        LC3: 500
    },
    ["MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA"]: {
        LC1: 1137.5,
        LC2: 1137.5,
        LC3: 1137.5
    }
};
const postInterventionFirstSideReinforcedPlasterCoefficientValues = {
    ["MURATURA_IN_PIETRAME_DISORDINATA"]: 2.5,
    ["MURATURA_A_CONCI_SBOZZATI"]: 2,
    ["MURATURA_IN_PIETRA_A_SPACCO"]: 1.5,
    ["MURATURA_IRREGOLARE_DI_PIETRA_TENERA"]: 1.7,
    ["MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA"]: 1.5,
    ["MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI"]: 1.2,
    ["MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE"]: 1.5,
    ["MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA"]: 1.3
};
const bindingCoefficientValues = {
    ["MURATURA_IN_PIETRAME_DISORDINATA"]: 2,
    ["MURATURA_A_CONCI_SBOZZATI"]: 1.7,
    ["MURATURA_IN_PIETRA_A_SPACCO"]: 1.5,
    ["MURATURA_IRREGOLARE_DI_PIETRA_TENERA"]: 1.4,
    ["MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA"]: 1.2,
    ["MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI"]: 1.2,
    ["MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE"]: 1.2,
    ["MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA"]: 0
};
const maxAmplficationCoefficientValues = {
    ["MURATURA_IN_PIETRAME_DISORDINATA"]: 3.5,
    ["MURATURA_A_CONCI_SBOZZATI"]: 3,
    ["MURATURA_IN_PIETRA_A_SPACCO"]: 2.4,
    ["MURATURA_IRREGOLARE_DI_PIETRA_TENERA"]: 2,
    ["MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA"]: 1.8,
    ["MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI"]: 1.4,
    ["MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE"]: 1.8,
    ["MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA"]: 1.3
};
const specificWeightValues = {
    ["MURATURA_IN_PIETRAME_DISORDINATA"]: 19,
    ["MURATURA_A_CONCI_SBOZZATI"]: 20,
    ["MURATURA_IN_PIETRA_A_SPACCO"]: 21,
    ["MURATURA_IRREGOLARE_DI_PIETRA_TENERA"]: 14.5,
    ["MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA"]: 14.5,
    ["MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI"]: 22,
    ["MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE"]: 18,
    ["MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA"]: 15
};
var REINFORCEMENT_APPLICATION_TYPE = /*#__PURE__*/ function(REINFORCEMENT_APPLICATION_TYPE) {
    REINFORCEMENT_APPLICATION_TYPE["APPLICAZIONE_SU_ENTRAMBI_I_LATI"] = "APPLICAZIONE_SU_ENTRAMBI_I_LATI";
    REINFORCEMENT_APPLICATION_TYPE["APPLICAZIONE_SU_UN_SOLO_LATO"] = "APPLICAZIONE_SU_UN_SOLO_LATO";
    return REINFORCEMENT_APPLICATION_TYPE;
}({});
const MODULE_REINFORCEMENT_APPLICATION_TYPE = Object.values(REINFORCEMENT_APPLICATION_TYPE);
var BINDER_MIXTURE_INJECTIONS = /*#__PURE__*/ function(BINDER_MIXTURE_INJECTIONS) {
    BINDER_MIXTURE_INJECTIONS["YES"] = "YES";
    BINDER_MIXTURE_INJECTIONS["NO"] = "NO";
    return BINDER_MIXTURE_INJECTIONS;
}({});
const MODULE_BINDER_MIXTURE_INJECTIONS = Object.values(BINDER_MIXTURE_INJECTIONS);
var FRM_GEOMETRY_TOPOLOGY = /*#__PURE__*/ function(FRM_GEOMETRY_TOPOLOGY) {
    FRM_GEOMETRY_TOPOLOGY["RECTANGULAR"] = "RECTANGULAR";
    FRM_GEOMETRY_TOPOLOGY["CIRCULAR"] = "CIRCULAR";
    return FRM_GEOMETRY_TOPOLOGY;
}({});
const MODULE_FRM_GEOMETRY_TOPOLOGY = Object.values(FRM_GEOMETRY_TOPOLOGY);
const characteristicCompressiveStrengthValues = {
    ["MURATURA_IN_PIETRAME_DISORDINATA"]: {
        min: 1,
        max: 2
    },
    ["MURATURA_A_CONCI_SBOZZATI"]: {
        min: 2,
        max: 2
    },
    ["MURATURA_IN_PIETRA_A_SPACCO"]: {
        min: 2.6,
        max: 3.8
    },
    ["MURATURA_IRREGOLARE_DI_PIETRA_TENERA"]: {
        min: 1.4,
        max: 2.2
    },
    ["MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA"]: {
        min: 2,
        max: 3.2
    },
    ["MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI"]: {
        min: 5.8,
        max: 8.2
    },
    ["MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE"]: {
        min: 2.6,
        max: 4.3
    },
    ["MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA"]: {
        min: 5,
        max: 8
    },
    CUSTOM: {
        min: 5,
        max: 5
    }
};
const characteristicShearStrengthValues = {
    ["MURATURA_IN_PIETRAME_DISORDINATA"]: {
        min: 0.018,
        max: 0.032
    },
    ["MURATURA_A_CONCI_SBOZZATI"]: {
        min: 0.035,
        max: 0.051
    },
    ["MURATURA_IN_PIETRA_A_SPACCO"]: {
        min: 0.056,
        max: 0.074
    },
    ["MURATURA_IRREGOLARE_DI_PIETRA_TENERA"]: {
        min: 0.028,
        max: 0.042
    },
    ["MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA"]: {
        min: 0.04,
        max: 0.06
    },
    ["MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI"]: {
        min: 0.09,
        max: 0.12
    },
    ["MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE"]: {
        min: 0.05,
        max: 0.13
    },
    ["MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA"]: {
        min: 0.08,
        max: 0.17
    },
    CUSTOM: {
        min: 0.05,
        max: 0.05
    }
};
const characteristicNormalElasticityModulusValues = {
    ["MURATURA_IN_PIETRAME_DISORDINATA"]: {
        min: 690,
        max: 1050
    },
    ["MURATURA_A_CONCI_SBOZZATI"]: {
        min: 1020,
        max: 1440
    },
    ["MURATURA_IN_PIETRA_A_SPACCO"]: {
        min: 1500,
        max: 1980
    },
    ["MURATURA_IRREGOLARE_DI_PIETRA_TENERA"]: {
        min: 900,
        max: 1260
    },
    ["MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA"]: {
        min: 1200,
        max: 1620
    },
    ["MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI"]: {
        min: 2400,
        max: 3300
    },
    ["MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE"]: {
        min: 1200,
        max: 1800
    },
    ["MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA"]: {
        min: 3500,
        max: 5600
    },
    CUSTOM: {
        min: 4550,
        max: 4550
    }
};
const characteristicShearElasticityModulusValues = {
    ["MURATURA_IN_PIETRAME_DISORDINATA"]: {
        min: 230,
        max: 350
    },
    ["MURATURA_A_CONCI_SBOZZATI"]: {
        min: 340,
        max: 480
    },
    ["MURATURA_IN_PIETRA_A_SPACCO"]: {
        min: 500,
        max: 600
    },
    ["MURATURA_IRREGOLARE_DI_PIETRA_TENERA"]: {
        min: 300,
        max: 420
    },
    ["MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA"]: {
        min: 400,
        max: 500
    },
    ["MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI"]: {
        min: 800,
        max: 1100
    },
    ["MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE"]: {
        min: 400,
        max: 600
    },
    ["MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA"]: {
        min: 875,
        max: 1400
    },
    CUSTOM: {
        min: 1137.5,
        max: 1137.5
    }
};
const masonryDensityValues = {
    ["MURATURA_IN_PIETRAME_DISORDINATA"]: 19,
    ["MURATURA_A_CONCI_SBOZZATI"]: 20,
    ["MURATURA_IN_PIETRA_A_SPACCO"]: 21,
    ["MURATURA_IRREGOLARE_DI_PIETRA_TENERA"]: 16,
    ["MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA"]: 16,
    ["MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI"]: 22,
    ["MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE"]: 18,
    ["MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA"]: 15,
    CUSTOM: 18
};
var REINFORCEMENT_ARRANGEMENT = /*#__PURE__*/ function(REINFORCEMENT_ARRANGEMENT) {
    REINFORCEMENT_ARRANGEMENT["CONTINUE"] = "CONTINUE";
    REINFORCEMENT_ARRANGEMENT["DISCONTINUE"] = "DISCONTINUE";
    return REINFORCEMENT_ARRANGEMENT;
}({});
const MODULE_REINFORCEMENT_ARRANGEMENT = Object.values(REINFORCEMENT_ARRANGEMENT);
var REINFORCEMENT_FAILURE_MODE = /*#__PURE__*/ function(REINFORCEMENT_FAILURE_MODE) {
    REINFORCEMENT_FAILURE_MODE["DISTACCO_INTERMEDIO"] = "DISTACCO_INTERMEDIO";
    REINFORCEMENT_FAILURE_MODE["DISTACCO_DI_ESTREMITA"] = "DISTACCO_DI_ESTREMITA";
    return REINFORCEMENT_FAILURE_MODE;
}({});
const MODULE_REINFORCEMENT_FAILURE_MODE = Object.values(REINFORCEMENT_FAILURE_MODE);
}),
"[project]/src/types/schemas/product-form-schema.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "productFormSchema",
    ()=>productFormSchema
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$product$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/product.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-rsc] (ecmascript) <export * as z>");
;
;
const productCustomSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
    sourceType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal('CUSTOM'),
    name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional().nullable(),
    thickness: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    tensileStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    elasticModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    networkDeformation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    matrixBreakdownVoltage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    conventionalLimitVoltage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    fiberType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$product$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["PRODUCT_FIBER_TYPE"])
});
const productDatabaseSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    sourceType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal('DATABASE'),
    id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
    name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional().nullable()
});
const productFormSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].discriminatedUnion('sourceType', [
    productCustomSchema,
    productDatabaseSchema
]);
}),
"[project]/src/types/schemas/pillar-form.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "pillarFlexuralCalculationSchema",
    ()=>pillarFlexuralCalculationSchema,
    "pillarGeneralSchema",
    ()=>pillarGeneralSchema,
    "pillarGeometrySchema",
    ()=>pillarGeometrySchema,
    "pillarMaterialSchema",
    ()=>pillarMaterialSchema,
    "pillarParamsCheckSchema",
    ()=>pillarParamsCheckSchema,
    "pillarReinforcementSchema",
    ()=>pillarReinforcementSchema,
    "pillarShearCalculationSchema",
    ()=>pillarShearCalculationSchema
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$product$2d$form$2d$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/product-form-schema.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-rsc] (ecmascript) <export * as z>");
;
;
;
const pillarGeneralSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    initialDeformation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()
});
const pillarGeometrySchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    width: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    height: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    topConcreteCover: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    bottomConcreteCover: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    effectiveDepth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    exposure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_GEOMETRY_EXPOSURE"])
});
const pillarReinforcementSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    top: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        diameter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
        quantity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
        area: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()
    }),
    bottom: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        diameter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
        quantity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
        area: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()
    }),
    transverse: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        diameter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
        legs: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
        area: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        stirrupSpacing: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
        stirrupInclination: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(-360).max(360),
        cornerRadius: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive()
    })
});
const pillarMaterialSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    knowledgeLevel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_MATERIAL_KNOWLEDGE_LEVEL"]),
    confidenceFactor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
    concreteClass: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        cubeCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        cylinderCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        averageCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        averageTensileStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        elasticModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        designCompressiveStrengthForBrittleMechanisms: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        designCompressiveStrengthForDuctileMechanisms: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()
    }),
    steelGrade: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        yieldStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        tensileStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        elongationPercentage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        elasticModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        designYieldStrengthForBrittleMechanisms: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        designYieldStrengthForDuctileMechanisms: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()
    })
});
const pillarParamsCheckSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    initialDeformation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    geometry: pillarGeometrySchema,
    reinforcementBar: pillarReinforcementSchema,
    materialProperties: pillarMaterialSchema
});
const pillarFlexuralCalculationSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    calculationType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal('FLEXURAL_VERIFY'),
    input: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        stripWidth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        layersNumber: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        bendingMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        product: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$product$2d$form$2d$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productFormSchema"]
    })
});
const pillarShearCalculationSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    calculationType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal('SHEAR_VERIFY'),
    input: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        product: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$product$2d$form$2d$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productFormSchema"]
    })
});
}),
"[project]/src/lib/api/modules/schemas/pillar-params.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "pillarFlexuralCalculationResultSchema",
    ()=>pillarFlexuralCalculationResultSchema,
    "pillarFlexuralVerifyExecutionInputSchema",
    ()=>pillarFlexuralVerifyExecutionInputSchema,
    "pillarParamsSchema",
    ()=>pillarParamsSchema,
    "pillarShearCalculationResultSchema",
    ()=>pillarShearCalculationResultSchema,
    "pillarShearVerifyExecutionInputSchema",
    ()=>pillarShearVerifyExecutionInputSchema
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$object$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/functions/zod/null-to-object-transform.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/functions/zod/null-to-undefined-transform.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$products$2f$schemas$2f$product$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/products/schemas/product.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$pillar$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/pillar-form.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-rsc] (ecmascript) <export * as z>");
;
;
;
;
;
const pillarParamsSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    initialDeformation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    geometry: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$pillar$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pillarGeometrySchema"].optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$object$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToObjectTransform"]),
    reinforcementBar: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$pillar$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pillarReinforcementSchema"].optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$object$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToObjectTransform"]),
    materialProperties: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$pillar$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pillarMaterialSchema"].optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$object$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToObjectTransform"])
});
const pillarFlexuralVerifyExecutionInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    stripWidth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    layersNumber: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    bendingMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    product: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$products$2f$schemas$2f$product$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productSchema"]
});
const pillarFlexuralCalculationResultSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    resistantMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    equilibrium: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    checkResult: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean()
});
const pillarShearVerifyExecutionInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    product: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$products$2f$schemas$2f$product$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productSchema"]
});
const pillarShearCalculationResultSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    frpShearContribution: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    shearCapacity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    checkResult: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean()
});
}),
"[project]/src/types/schemas/rectangular-beam-from.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "rectangularBeamFlexuralCalculationSchema",
    ()=>rectangularBeamFlexuralCalculationSchema,
    "rectangularBeamFormSchema",
    ()=>rectangularBeamFormSchema,
    "rectangularBeamGeneralSchema",
    ()=>rectangularBeamGeneralSchema,
    "rectangularBeamGeometrySchema",
    ()=>rectangularBeamGeometrySchema,
    "rectangularBeamMaterialSchema",
    ()=>rectangularBeamMaterialSchema,
    "rectangularBeamParamsCheckSchema",
    ()=>rectangularBeamParamsCheckSchema,
    "rectangularBeamReinforcementSchema",
    ()=>rectangularBeamReinforcementSchema,
    "rectangularBeamShearCalculationSchema",
    ()=>rectangularBeamShearCalculationSchema
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$product$2d$form$2d$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/product-form-schema.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-rsc] (ecmascript) <export * as z>");
;
;
;
const rectangularBeamGeneralSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    initialDeformation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    polarity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_POLARITY"])
});
const rectangularBeamGeometrySchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    width: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    height: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    topConcreteCover: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    bottomConcreteCover: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    effectiveDepth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    exposure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_GEOMETRY_EXPOSURE"])
});
const rectangularBeamReinforcementSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    top: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        diameter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
        quantity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
        area: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()
    }),
    bottom: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        diameter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
        quantity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
        area: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()
    }),
    transverse: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        diameter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
        legs: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
        area: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        stirrupSpacing: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
        stirrupInclination: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(-360).max(360),
        cornerRadius: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive()
    })
});
const rectangularBeamMaterialSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    knowledgeLevel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_MATERIAL_KNOWLEDGE_LEVEL"]),
    confidenceFactor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
    concreteClass: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        cubeCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        cylinderCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        averageCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        averageTensileStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        elasticModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        designCompressiveStrengthForBrittleMechanisms: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        designCompressiveStrengthForDuctileMechanisms: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()
    }),
    steelGrade: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        yieldStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        tensileStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        elongationPercentage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        elasticModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        designYieldStrengthForBrittleMechanisms: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        designYieldStrengthForDuctileMechanisms: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()
    })
});
const rectangularBeamFormSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    initialDeformation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
    polarity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_POLARITY"]).optional(),
    geometry: rectangularBeamGeometrySchema.optional(),
    reinforcementBar: rectangularBeamReinforcementSchema.optional(),
    materialProperties: rectangularBeamMaterialSchema.optional()
});
const rectangularBeamParamsCheckSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    initialDeformation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    polarity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_POLARITY"]),
    geometry: rectangularBeamGeometrySchema,
    reinforcementBar: rectangularBeamReinforcementSchema,
    materialProperties: rectangularBeamMaterialSchema
});
const rectangularBeamFlexuralCalculationSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    calculationType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal('FLEXURAL_VERIFY'),
    input: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        stripWidth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        layersNumber: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        bendingMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        product: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$product$2d$form$2d$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productFormSchema"]
    })
});
const rectangularBeamShearCalculationSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    calculationType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal('SHEAR_VERIFY'),
    input: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        reinforcementLayout: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["REINFORCEMENT_LAYOUTS"]),
        webHeight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
        stripWidth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
        stripSpacingAlongElementAxis: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
        stripSpacingOrthogonalElementAxis: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        layersNumber: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
        stripInclination: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0).max(90),
        concreteStrutInclination: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(22).max(45),
        appliedShearForce: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
        product: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$product$2d$form$2d$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productFormSchema"]
    })
});
}),
"[project]/src/lib/api/modules/schemas/rectangular-beam-params.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "rectangularBeamCalculationCheck",
    ()=>rectangularBeamCalculationCheck,
    "rectangularBeamFlexuralCalculationResultSchema",
    ()=>rectangularBeamFlexuralCalculationResultSchema,
    "rectangularBeamFlexuralVerifyExecutionInputSchema",
    ()=>rectangularBeamFlexuralVerifyExecutionInputSchema,
    "rectangularBeamParamsSchema",
    ()=>rectangularBeamParamsSchema,
    "rectangularBeamShearCalculationResultSchema",
    ()=>rectangularBeamShearCalculationResultSchema,
    "rectangularBeamShearVerifyExecutionInputSchema",
    ()=>rectangularBeamShearVerifyExecutionInputSchema
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$object$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/functions/zod/null-to-object-transform.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/functions/zod/null-to-undefined-transform.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$products$2f$schemas$2f$product$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/products/schemas/product.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$rectangular$2d$beam$2d$from$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/rectangular-beam-from.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-rsc] (ecmascript) <export * as z>");
;
;
;
;
;
;
const rectangularBeamParamsSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    initialDeformation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    polarity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_POLARITY"]).optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    geometry: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$rectangular$2d$beam$2d$from$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["rectangularBeamGeometrySchema"].optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$object$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToObjectTransform"]),
    reinforcementBar: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$rectangular$2d$beam$2d$from$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["rectangularBeamReinforcementSchema"].optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$object$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToObjectTransform"]),
    materialProperties: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$rectangular$2d$beam$2d$from$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["rectangularBeamMaterialSchema"].optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$object$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToObjectTransform"])
});
const rectangularBeamFlexuralVerifyExecutionInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    stripWidth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    layersNumber: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    bendingMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    product: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$products$2f$schemas$2f$product$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productSchema"]
});
const rectangularBeamShearVerifyExecutionInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    reinforcementLayout: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["REINFORCEMENT_LAYOUTS"]),
    webHeight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    stripWidth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    stripSpacingAlongElementAxis: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    stripSpacingOrthogonalElementAxis: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    layersNumber: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    stripInclination: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0).max(90),
    concreteStrutInclination: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(22).max(45),
    appliedShearForce: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    product: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$products$2f$schemas$2f$product$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productSchema"]
});
const rectangularBeamFlexuralCalculationResultSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    momentCapacity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    equilibrium: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    checkResult: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean()
});
const rectangularBeamShearCalculationResultSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    frpShearContribution: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    shearCapacity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    checkResult: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean()
});
const rectangularBeamCalculationCheck = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    flexuralCalculationResult: rectangularBeamFlexuralCalculationResultSchema,
    shearCalculationResult: rectangularBeamShearCalculationResultSchema
});
}),
"[project]/src/lib/api/modules/schemas/slab-params.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "slabCalculationCheck",
    ()=>slabCalculationCheck,
    "slabCalculationsResultSchema",
    ()=>slabCalculationsResultSchema,
    "slabFlexuralCalculationResultSchema",
    ()=>slabFlexuralCalculationResultSchema,
    "slabFlexuralNegativeCalculationResultSchema",
    ()=>slabFlexuralNegativeCalculationResultSchema,
    "slabFlexuralPositiveCalculationResultSchema",
    ()=>slabFlexuralPositiveCalculationResultSchema,
    "slabFlexuralVerifyExecutionInputSchema",
    ()=>slabFlexuralVerifyExecutionInputSchema,
    "slabInterfaceSlipCalculationResultSchema",
    ()=>slabInterfaceSlipCalculationResultSchema,
    "slabInterfaceSlipVerifyExecutionInputSchema",
    ()=>slabInterfaceSlipVerifyExecutionInputSchema,
    "slabNegativeMomentCalculationResultSchema",
    ()=>slabNegativeMomentCalculationResultSchema,
    "slabParamsSchema",
    ()=>slabParamsSchema,
    "slabShearCalculationResultSchema",
    ()=>slabShearCalculationResultSchema,
    "slabShearVerifyExecutionInputSchema",
    ()=>slabShearVerifyExecutionInputSchema
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-rsc] (ecmascript) <export * as z>");
;
const slabParamsSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    geometry: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        joistFormwork: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
            'T_SHAPED',
            'RECTANGULAR_WITHOUT_SLAB'
        ]).optional(),
        joistBase: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        joistWebHeight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        existingSlabHeight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        joistSpacing: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        bottomRebarCover: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        topRebarCover: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        structuralScheme: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
            'SIMPLY_SUPPORTED',
            'CANTILEVER'
        ]).optional(),
        totalSlabThickness: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        effectiveDepth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional()
    }).optional().nullable(),
    slabRebar: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        spanBottomRebar: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
            diameter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            quantity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            area: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional()
        }).optional(),
        spanTopRebar: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
            diameter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            quantity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            area: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional()
        }).optional(),
        supportBottomRebar: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
            diameter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            quantity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            area: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional()
        }).optional(),
        supportTopRebar: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
            diameter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            quantity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            area: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional()
        }).optional(),
        additionalRebar: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
            diameter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            quantity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            area: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional()
        }).optional(),
        additionalSteelElasticModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional()
    }).optional().nullable(),
    materialProperties: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        concreteClassKnowledgeLevel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
            'LC1',
            'LC2',
            'LC3'
        ]).optional(),
        steelGradeKnowledgeLevel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
            'LC1',
            'LC2',
            'LC3'
        ]).optional(),
        concreteMaterialClass: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
            'DUCTILE',
            'BRITTLE'
        ]).optional(),
        steelMaterialClass: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
            'DUCTILE',
            'BRITTLE'
        ]).optional(),
        concreteClass: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
            id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            cubeCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            cylinderCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            averageCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            averageTensileStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            elasticModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            designCompressiveStrengthForBrittleMechanisms: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            designTensileStrengthForBrittleMechanisms: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            designCompressiveStrengthForDuctileMechanisms: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional()
        }).optional(),
        steelGrade: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
            id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
            yieldStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            tensileStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            elongationPercentage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            elasticModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            designYieldStrengthForBrittleMechanisms: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            designYieldStrengthForDuctileMechanisms: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional()
        }).optional()
    }).optional().nullable(),
    slabFrcReinforcement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        frcReinforcementType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
        toughnessClassAndFibersType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
        frcSlabThickness: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        frcElasticModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        characteristicCylindricalCompressiveStrengthFrcMatrix: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        frcCharacteristicTensileStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        specificWeight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        supportAdditionalRebar: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
            diameter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            quantity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
            elasticModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional()
        }).optional()
    }).optional().nullable()
});
const slabFlexuralPositiveCalculationResultSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    designMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    resistantMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    utilizationRatio: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    neutralAxisDepth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    compressionZoneHeight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    isVerified: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().optional().nullable()
});
const slabFlexuralNegativeCalculationResultSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    designMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    resistantMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    utilizationRatio: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    neutralAxisDepth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    compressionZoneHeight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    isVerified: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().optional().nullable()
});
const slabShearCalculationResultSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    // Common properties
    sectionFillType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional().nullable(),
    inputShearForce: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    // Unreinforced section results
    unreinforcedSectionEffectiveDepth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    unreinforcedTensionAreaMinimumWidth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    unreinforcedSizeEffectFactor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    unreinforcedCoefficient: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    unreinforcedTensileLongitudinalReinforcementArea: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    unreinforcedTensileReinforcementRatio: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    unreinforcedShearCapacity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    // Reinforced section results
    reinforcedSectionEffectiveDepth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    reinforcedTensionAreaMinimumWidth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    reinforcedSizeEffectFactor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    reinforcedMeanCharacteristicCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    reinforcedCoefficient: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    reinforcedTensileLongitudinalReinforcementArea: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    reinforcedTensileReinforcementRatio: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    reinforcedSectionShearResistance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    // Verification result
    shearVerificationResult: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional().nullable(),
    isShearVerificationSatisfied: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().optional().nullable()
});
const slabNegativeMomentCalculationResultSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    designMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    resistantMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    utilizationRatio: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    effectiveDepth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    reinforcementArea: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    isVerified: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().optional().nullable()
});
const slabCalculationsResultSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    flexuralPositive: slabFlexuralPositiveCalculationResultSchema.optional(),
    flexuralNegative: slabFlexuralNegativeCalculationResultSchema.optional(),
    shear: slabShearCalculationResultSchema.optional(),
    negativeMoment: slabNegativeMomentCalculationResultSchema.optional()
});
const slabFlexuralVerifyExecutionInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    stripWidth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
    bendingMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
    spanVerification: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().optional(),
    product: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        // Backend uses 'product' not 'productInput'
        id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
        name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
        sourceType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
            'DATABASE',
            'CUSTOM'
        ]).optional(),
        thickness: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
        elasticModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        cylindricCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        characteristicTensileStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        specificWeight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
        adhesionToConcrete: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        fiberType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
        tensileStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        orientation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional().nullable(),
        systemDeformation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional()
    }).optional()
}).optional();
const slabShearVerifyExecutionInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    sectionFillType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    shearForce: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
    isCantilever: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().optional(),
    product: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        // Backend uses 'product' not 'productInput'
        id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
        name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
        sourceType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
            'DATABASE',
            'CUSTOM'
        ]).optional(),
        thickness: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
        elasticModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        cylindricCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        characteristicTensileStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        specificWeight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
        adhesionToConcrete: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        fiberType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
        tensileStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        orientation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional().nullable(),
        systemDeformation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional()
    }).optional()
}).optional();
const slabInterfaceSlipVerifyExecutionInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    stripWidth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
    shearForce: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
    frcBondStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
    product: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        // Backend uses 'product' not 'productInput'
        id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
        name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
        sourceType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
            'DATABASE',
            'CUSTOM'
        ]).optional(),
        thickness: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
        elasticModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        cylindricCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        characteristicTensileStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        specificWeight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
        adhesionToConcrete: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        fiberType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
        tensileStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
        orientation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional().nullable(),
        systemDeformation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional()
    }).optional()
}).optional();
const slabFlexuralCalculationResultSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    // Common properties
    sectionType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional().nullable(),
    // Unreinforced section results
    unreinforcedBottomSteelStrain: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    unreinforcedTopSteelStrain: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    unreinforcedBottomSteelStress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    unreinforcedTopSteelStress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    unreinforcedNeutralAxisDistance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    unreinforcedTranslationalEquilibrium: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    unreinforcedResistanceMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    // Reinforced section results
    maximumBendingMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    reinforcedBottomSteelStrain: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    reinforcedTopSteelStrain: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    reinforcedSupplementarySteelStrain: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    reinforcedBottomSteelStress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    reinforcedTopSteelStress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    reinforcedSupplementarySteelStress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    reinforcedNeutralAxisDistance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    reinforcedTranslationalEquilibrium: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    reinforcedSectionResistanceMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    // Verification results
    checkResult: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().optional().nullable(),
    checkValue: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable()
});
const slabInterfaceSlipCalculationResultSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    // Calculation results
    negativeMomentAreaHomogenizationCoefficient: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    neutralAxisCompressedFlangeDistance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    translationalEquilibrium: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    staticMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    neutralAxisInertiaMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    interfaceShearStress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    concreteToConcreteFrictionCoefficient: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    frcBondStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    // Verification result
    checkResult: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().optional().nullable(),
    // Input values
    inputShearForce: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    inputStripWidth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable()
});
const slabCalculationCheck = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    flexuralVerifyExecutionResultMPlus: slabFlexuralCalculationResultSchema.optional(),
    flexuralVerifyExecutionResultMMinus: slabFlexuralCalculationResultSchema.optional(),
    shearVerifyExecutionResult: slabShearCalculationResultSchema.optional(),
    interfaceSlipCalculationResult: slabInterfaceSlipCalculationResultSchema.optional()
}).refine((data)=>data.flexuralVerifyExecutionResultMPlus !== undefined || data.flexuralVerifyExecutionResultMMinus !== undefined || data.shearVerifyExecutionResult !== undefined || data.interfaceSlipCalculationResult !== undefined, {
    message: 'At least one calculation must be completed to generate a report'
});
}),
"[project]/src/lib/api/modules/schemas/wood-params.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "woodGeometryPropertiesSchema",
    ()=>woodGeometryPropertiesSchema,
    "woodMaterialPropertiesSchema",
    ()=>woodMaterialPropertiesSchema,
    "woodParamsSchema",
    ()=>woodParamsSchema,
    "woodPreInterventionCheckSchema",
    ()=>woodPreInterventionCheckSchema
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$object$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/functions/zod/null-to-object-transform.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/functions/zod/null-to-undefined-transform.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-rsc] (ecmascript) <export * as z>");
;
;
;
const woodMaterialPropertiesSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    category: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
    woodName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional(),
    characteristicBendingStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    characteristicShearStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    characteristicTensileStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    characteristicCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    meanDensity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    meanShearModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    elasticityModulusParallelToGrain: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    meanElasticityModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    partialMaterialFactor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()
});
const woodGeometryPropertiesSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    beamSectionWidth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    beamSectionHeight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    beamSpacing: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    beamSpan: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    sectionModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    inertiaMomentAboutY: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    serviceClass: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
    loadDuration: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
    correctionFactor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    deformabilityFactor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    designBendingStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    designShearStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    elasticityInstantaneousModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    longTermElasticityModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()
});
const woodPreInterventionCheckSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    maximumBendingMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    maximumShearForce: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    designBendingStress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    designBendingStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    bendingCheck: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    designShearStress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    designShearStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    shearCheck: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    permanentLoadPerLinearMeter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    imposedLoadPerLinearMeter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    instantaneousDeflectionPermanentLoad: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    instantaneousDeflectionImposedLoad: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    instantaneousDeflectionTotalLoads: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    deformabilityCheck: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    combinationFactor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    finalDeflectionTotalLoads: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    finalCheckResult: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()
});
const woodParamsSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    initialDeformation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    materialProperties: woodMaterialPropertiesSchema.optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$object$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToObjectTransform"]),
    geometry: woodGeometryPropertiesSchema.optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$object$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToObjectTransform"]),
    preIntervationCheck: woodPreInterventionCheckSchema.optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$object$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToObjectTransform"]),
    // Add postIntervationCheck to match API response structure
    postIntervationCheck: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].any().optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$object$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToObjectTransform"])
});
}),
"[project]/src/types/schemas/masonry/antioverturning-form.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "compositeReinforcementSystemInputSchema",
    ()=>compositeReinforcementSystemInputSchema,
    "masonryAntiOverturningBuildingCharacteristicsSchema",
    ()=>masonryAntiOverturningBuildingCharacteristicsSchema,
    "masonryAntiOverturningCompositeReinforcementSystemConnectorInputSchema",
    ()=>masonryAntiOverturningCompositeReinforcementSystemConnectorInputSchema,
    "masonryAntiOverturningCompositeReinforcementSystemConnectorVerifyInputSchema",
    ()=>masonryAntiOverturningCompositeReinforcementSystemConnectorVerifyInputSchema,
    "masonryAntiOverturningCompositeReinforcementSystemMatrixInputSchema",
    ()=>masonryAntiOverturningCompositeReinforcementSystemMatrixInputSchema,
    "masonryAntiOverturningCompositeReinforcementSystemMatrixVerifyInputSchema",
    ()=>masonryAntiOverturningCompositeReinforcementSystemMatrixVerifyInputSchema,
    "masonryAntiOverturningCompositeReinforcementSystemMeshInputSchema",
    ()=>masonryAntiOverturningCompositeReinforcementSystemMeshInputSchema,
    "masonryAntiOverturningCompositeReinforcementSystemMeshVerifyInputSchema",
    ()=>masonryAntiOverturningCompositeReinforcementSystemMeshVerifyInputSchema,
    "masonryAntiOverturningCompositeReinforcementSystemSchema",
    ()=>masonryAntiOverturningCompositeReinforcementSystemSchema,
    "masonryAntiOverturningCompositeReinforcementSystemVerifyInputSchema",
    ()=>masonryAntiOverturningCompositeReinforcementSystemVerifyInputSchema,
    "masonryAntiOverturningInfillGeometrySchema",
    ()=>masonryAntiOverturningInfillGeometrySchema,
    "masonryAntiOverturningMaterialPropertiesSchema",
    ()=>masonryAntiOverturningMaterialPropertiesSchema,
    "masonryAntiOverturningParamsFormSchema",
    ()=>masonryAntiOverturningParamsFormSchema,
    "masonryAntiOverturningSiteCharacteristicsSchema",
    ()=>masonryAntiOverturningSiteCharacteristicsSchema
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/functions/zod/null-to-undefined-transform.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$products$2f$schemas$2f$product$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/products/schemas/product.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-rsc] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$product$2d$form$2d$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/product-form-schema.ts [app-rsc] (ecmascript)");
;
;
;
;
;
const masonryAntiOverturningSiteCharacteristicsSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    seismicAccelerationAtSlv: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    amplificationFactorAtSlv: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    subsoilCategory: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_SUBSOIL_CATEGORY"]),
    ssCoefficient: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    topographicCategory: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_TOPOGRAPHIC_CATEGORY"]),
    stCoefficient: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    subsoilCoefficient: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable()
});
const masonryAntiOverturningBuildingCharacteristicsSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    buildingType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_BUILDING_TYPE"]),
    totalBuildingHeight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    buildingFundamentalPeriod: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    parameterA: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    parameterB: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    parameterAp: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()
});
const masonryAntiOverturningMaterialPropertiesSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    infillWallTypology: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_INFILL_WALL_TOPOLOGY"]),
    facingMaterial: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_FACING_MATERIAL"]),
    knowledgeLevel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_MATERIAL_KNOWLEDGE_LEVEL"]),
    executionClass: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_EXECUTION_CLASS"]),
    loadResistingCategory: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_LOAD_RESISTING_CATEGORY"]),
    characteristicCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    infillWallElasticModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    masonrySpecificWeightExternalFacing: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    masonrySpecificWeightInternalFacing: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    plasterSpecificWeight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    confidenceFactor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    masonryStrengthSafetyFactor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    designCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    ultimateMasonryStrain: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()
});
const masonryAntiOverturningInfillGeometrySchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    panelWidth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    externalFacingThickness: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    internalFacingThickness: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    singleSidePlasterThickness: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    netPanelHeight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    panelHeightFromGroundLevel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    panelCentroidFromGroundLevel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    fundamentalPeriodPanel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()
});
const masonryAntiOverturningCompositeReinforcementSystemMeshInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    reinforcedSidesNumber: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    resistantAreaPerMeter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive().optional().nullable(),
    meshProduct: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$product$2d$form$2d$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productFormSchema"]
});
const masonryAntiOverturningCompositeReinforcementSystemConnectorInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    connectorSpacing: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    connectorProduct: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$product$2d$form$2d$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productFormSchema"]
});
const masonryAntiOverturningCompositeReinforcementSystemMatrixInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    compositeSystemThickness: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    matrixProduct: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$product$2d$form$2d$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productFormSchema"]
});
const masonryAntiOverturningCompositeReinforcementSystemMeshVerifyInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    reinforcedSidesNumber: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    resistantAreaPerMeter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive().optional().nullable(),
    meshProduct: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$products$2f$schemas$2f$product$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productSchema"]
});
const masonryAntiOverturningCompositeReinforcementSystemConnectorVerifyInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    connectorSpacing: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    connectorProduct: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$products$2f$schemas$2f$product$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productSchema"]
});
const masonryAntiOverturningCompositeReinforcementSystemMatrixVerifyInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    compositeSystemThickness: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    matrixProduct: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$products$2f$schemas$2f$product$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productSchema"]
});
const masonryAntiOverturningCompositeReinforcementSystemVerifyInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    meshInput: masonryAntiOverturningCompositeReinforcementSystemMeshVerifyInputSchema,
    connectorInput: masonryAntiOverturningCompositeReinforcementSystemConnectorVerifyInputSchema,
    matrixInput: masonryAntiOverturningCompositeReinforcementSystemMatrixVerifyInputSchema
});
const compositeReinforcementSystemInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    meshInput: masonryAntiOverturningCompositeReinforcementSystemMeshInputSchema,
    connectorInput: masonryAntiOverturningCompositeReinforcementSystemConnectorInputSchema,
    matrixInput: masonryAntiOverturningCompositeReinforcementSystemMatrixInputSchema
});
const masonryAntiOverturningCompositeReinforcementSystemSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    calculationType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal('POST_INTERVENTION_VERIFY'),
    input: compositeReinforcementSystemInputSchema
});
const masonryAntiOverturningParamsFormSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    siteCharacteristics: masonryAntiOverturningSiteCharacteristicsSchema.optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    buildingCharacteristics: masonryAntiOverturningBuildingCharacteristicsSchema.optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    materialProperties: masonryAntiOverturningMaterialPropertiesSchema.optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    infillGeometry: masonryAntiOverturningInfillGeometrySchema.optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"])
});
}),
"[project]/src/types/schemas/masonry/crm-form.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "crmFormSchema",
    ()=>crmFormSchema,
    "crmPostInterventionSchema",
    ()=>crmPostInterventionSchema,
    "crmPreInterventionSchema",
    ()=>crmPreInterventionSchema,
    "crmProductInputConnectorSchema",
    ()=>crmProductInputConnectorSchema,
    "crmProductInputMatrixSchema",
    ()=>crmProductInputMatrixSchema,
    "crmProductInputMeshSchema",
    ()=>crmProductInputMeshSchema,
    "designStrengthPostInterventionCalculationResultSchema",
    ()=>designStrengthPostInterventionCalculationResultSchema,
    "designStrengthPostInterventionInputSchema",
    ()=>designStrengthPostInterventionInputSchema,
    "designStrengthPostInterventionVerifyInputSchema",
    ()=>designStrengthPostInterventionVerifyInputSchema,
    "designStrengthPreInterventionCalculationResultSchema",
    ()=>designStrengthPreInterventionCalculationResultSchema,
    "existingMasonryCharacteristicsParamsSchema",
    ()=>existingMasonryCharacteristicsParamsSchema,
    "inPlaneShearStrengthCalculationResultSchema",
    ()=>inPlaneShearStrengthCalculationResultSchema,
    "masonryCrmDesignStrengthPostInterventionConnectorVerifyInputSchema",
    ()=>masonryCrmDesignStrengthPostInterventionConnectorVerifyInputSchema,
    "masonryCrmDesignStrengthPostInterventionMatrixVerifyInputSchema",
    ()=>masonryCrmDesignStrengthPostInterventionMatrixVerifyInputSchema,
    "masonryCrmDesignStrengthPostInterventionMeshVerifyInputSchema",
    ()=>masonryCrmDesignStrengthPostInterventionMeshVerifyInputSchema,
    "masonryCrmDesignStrengthPostInterventionVerifyInputSchema",
    ()=>masonryCrmDesignStrengthPostInterventionVerifyInputSchema,
    "planeBendingStrengthCalculationResultSchema",
    ()=>planeBendingStrengthCalculationResultSchema,
    "reinforcedMasonryCharacteristicsParamsSchema",
    ()=>reinforcedMasonryCharacteristicsParamsSchema
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/functions/zod/null-to-undefined-transform.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$products$2f$schemas$2f$product$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/products/schemas/product.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/index.js [app-rsc] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$product$2d$form$2d$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/product-form-schema.ts [app-rsc] (ecmascript)");
;
;
;
;
;
const existingMasonryCharacteristicsParamsSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    panelWidth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().positive(),
    panelThickness: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().positive(),
    panelHeight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().positive(),
    masonryType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_CRM_MASONRY_TYPE"]),
    knowledgeLevel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_MATERIAL_KNOWLEDGE_LEVEL"]),
    confidenceFactor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    averageCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    averageShearStrengthRegularMasonry: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    averageShearStrengthIrregularMasonry: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    averageNormalElasticityModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    averageShearElasticityModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    specificWeight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    enhancementCharacteristics: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_ENHANCEMENT_CHARACTERISTICS"]),
    correctionCoefficient: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    amplifiedAverageCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    amplifiedAverageShearStrengthRegularMasonry: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    amplifiedAverageShearStrengthIrregularMasonry: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    amplifiedAverageNormalElasticityModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    amplifiedAverageShearElasticityModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable()
});
const planeBendingStrengthCalculationResultSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    compressedFlangeNeutralAxisDistance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    inOrOutplaneBendingMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable()
});
const inPlaneShearStrengthCalculationResultSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    verticalStress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    wallSlendernessCorrectionCoefficient: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    shearStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable()
});
const designStrengthPreInterventionCalculationResultSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    executionClass: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_EXECUTION_CLASS"]),
    // structuralElementsCategory is the same as loadResistingCategory but for CRM
    structuralElementsCategory: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_LOAD_RESISTING_CATEGORY"]),
    masonryStrengthSafetyFactor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    designCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    designShearStrengthRegularMasonry: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    designShearStrengthIrregularMasonry: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    designNormalElasticityModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    designShearElasticityModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    ultimateCompressiveStrainLinearBehavior: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    ultimateCompressiveStrain: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    firstCoefficient: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    panelSelfWeight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    inPlaneBendingStrengthCalculationResult: planeBendingStrengthCalculationResultSchema.optional().nullable(),
    outOfPlaneBendingStrengthCalculationResult: planeBendingStrengthCalculationResultSchema.optional().nullable(),
    inPlaneShearStrengthCalculationResult: inPlaneShearStrengthCalculationResultSchema.optional().nullable()
});
const crmPreInterventionSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    existingMasonryCharacteristicsParams: existingMasonryCharacteristicsParamsSchema.optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    designStrengthPreInterventionCalculationResult: designStrengthPreInterventionCalculationResultSchema.optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"])
});
const reinforcedMasonryCharacteristicsParamsSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    reinforcementApplicationType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_REINFORCEMENT_APPLICATION_TYPE"]),
    singleFaceApplicationReductionCoefficient: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    reinforcedPlasterCoefficient: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    binderMixturesInjections: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_BINDER_MIXTURE_INJECTIONS"]),
    correctionCoefficient: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    overallAmplificationCoefficient: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    amplifiedAverageCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    amplifiedAverageShearStrengthRegularMasonry: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    amplifiedAverageShearStrengthIrregularMasonry: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    amplifiedAverageNormalElasticityModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    amplifiedAverageShearElasticityModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable()
});
const crmProductInputMeshSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    meshProduct: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$product$2d$form$2d$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productFormSchema"]
});
const crmProductInputConnectorSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    connectorProduct: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$product$2d$form$2d$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productFormSchema"]
});
const crmProductInputMatrixSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    matrixProduct: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$product$2d$form$2d$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productFormSchema"]
});
const masonryCrmDesignStrengthPostInterventionMeshVerifyInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    meshProduct: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$products$2f$schemas$2f$product$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productSchema"]
});
const masonryCrmDesignStrengthPostInterventionConnectorVerifyInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    connectorProduct: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$products$2f$schemas$2f$product$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productSchema"]
});
const masonryCrmDesignStrengthPostInterventionMatrixVerifyInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    matrixProduct: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$products$2f$schemas$2f$product$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productSchema"]
});
const masonryCrmDesignStrengthPostInterventionVerifyInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    meshInput: masonryCrmDesignStrengthPostInterventionMeshVerifyInputSchema,
    connectorInput: masonryCrmDesignStrengthPostInterventionConnectorVerifyInputSchema,
    matrixInput: masonryCrmDesignStrengthPostInterventionMatrixVerifyInputSchema,
    reinforcementTotalThickness: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable()
});
const designStrengthPostInterventionVerifyInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    meshInput: crmProductInputMeshSchema,
    connectorInput: crmProductInputConnectorSchema,
    matrixInput: crmProductInputMatrixSchema,
    reinforcementTotalThickness: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable()
});
const designStrengthPostInterventionInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    calculationType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].literal('DESIGN_STRENGTH_VERIFY'),
    input: designStrengthPostInterventionVerifyInputSchema
});
const designStrengthPostInterventionCalculationResultSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    panelThickness: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    designCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    designShearStrengthRegularMasonry: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    designShearStrengthIrregularMasonry: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    designNormalElasticityModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    designShearElasticityModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    ultimateCompressiveStrainLinearBehavior: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    secondCoefficient: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    panelSelfWeight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    inPlaneBendingStrengthCalculationResult: planeBendingStrengthCalculationResultSchema.optional().nullable(),
    outOfPlaneBendingStrengthCalculationResult: planeBendingStrengthCalculationResultSchema.optional().nullable(),
    inPlaneShearStrengthCalculationResult: inPlaneShearStrengthCalculationResultSchema.optional().nullable()
});
const crmPostInterventionSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    reinforcedMasonryCharacteristicsParams: reinforcedMasonryCharacteristicsParamsSchema.optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    designStrengthPostInterventionVerifyInput: masonryCrmDesignStrengthPostInterventionVerifyInputSchema.optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    designStrengthPostInterventionCalculationResult: designStrengthPostInterventionCalculationResultSchema.optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"])
});
const crmFormSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    preIntervention: crmPreInterventionSchema.optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    postIntervention: crmPostInterventionSchema.optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"])
});
}),
"[project]/src/types/schemas/masonry/frcm-column-form.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "confinementCheckResultSchema",
    ()=>confinementCheckResultSchema,
    "confinementReinforcementCalculationResult",
    ()=>confinementReinforcementCalculationResult,
    "confinementReinforcementInput",
    ()=>confinementReinforcementInput,
    "confinementReinforcementVerifyInput",
    ()=>confinementReinforcementVerifyInput,
    "frcmColumnConfinementReinforcementInput",
    ()=>frcmColumnConfinementReinforcementInput,
    "frcmColumnGeometrySchema",
    ()=>frcmColumnGeometrySchema,
    "frcmColumnMasonryCharacteristicsSchema",
    ()=>frcmColumnMasonryCharacteristicsSchema,
    "frcmColumnParamsSchema",
    ()=>frcmColumnParamsSchema,
    "frcmColumnStressSchema",
    ()=>frcmColumnStressSchema,
    "nonReinforcedSectionSchema",
    ()=>nonReinforcedSectionSchema,
    "reinforcedSectionSchema",
    ()=>reinforcedSectionSchema
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/functions/zod/null-to-undefined-transform.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$products$2f$schemas$2f$product$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/products/schemas/product.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/index.js [app-rsc] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$product$2d$form$2d$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/product-form-schema.ts [app-rsc] (ecmascript)");
;
;
;
;
;
const frcmColumnGeometrySchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    topology: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_FRM_GEOMETRY_TOPOLOGY"]),
    largerSizeOrColumnDiameter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    smallerSize: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    crossSectionArea: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    crossSectionDiagonal: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    cornerRoundingRadius: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number()
});
const frcmColumnMasonryCharacteristicsSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    material: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_FACING_MATERIAL"]),
    enhancementCharacteristics: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_CRM_MASONRY_TYPE"]),
    knowledgeLevel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_MATERIAL_KNOWLEDGE_LEVEL"]).optional().nullable(),
    confidenceFactor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    executionClass: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_EXECUTION_CLASS"]),
    loadResistantCategory: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_LOAD_RESISTING_CATEGORY"]),
    masonrySafetyFactor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    characteristicCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    designCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    characteristicShearStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    designShearStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    normalElasticityModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    shearElasticityModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    masonryDensity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    exposureType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_GEOMETRY_EXPOSURE"]).optional().nullable(),
    conversionFactor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable()
});
const frcmColumnStressSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    normalStressCenteredStressing: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable()
});
const frcmColumnParamsSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    geometry: frcmColumnGeometrySchema.nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    masonryCharacteristics: frcmColumnMasonryCharacteristicsSchema.nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    stress: frcmColumnStressSchema.nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"])
});
const confinementReinforcementVerifyInput = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    product: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$products$2f$schemas$2f$product$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productSchema"],
    reinforcedArrangement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["REINFORCEMENT_ARRANGEMENT"]),
    singleWidthBand: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    stepsOfTheBand: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    clearDistanceBetweenStripes: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    minimalTransversalDimension: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    numberOfReinforcementLayers: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    matrixThicknessOfTheSingleLayer: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number()
});
const confinementReinforcementInput = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    product: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$product$2d$form$2d$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productFormSchema"],
    reinforcedArrangement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["REINFORCEMENT_ARRANGEMENT"]),
    singleWidthBand: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    stepsOfTheBand: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    clearDistanceBetweenStripes: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    minimalTransversalDimension: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    numberOfReinforcementLayers: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    matrixThicknessOfTheSingleLayer: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number()
});
const frcmColumnConfinementReinforcementInput = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    calculationType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].literal('CONFINEMENT_VERIFY'),
    input: confinementReinforcementInput
});
const nonReinforcedSectionSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    normalStressStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    designAxialResistance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    check: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].boolean().optional().nullable()
});
const reinforcedSectionSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    coefficientOfResistanceIncrease: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    confinedColumnDesignResistanceWithFrcm: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    designAxialResistanceOfTheConfinedColumnWithFrcm: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    check: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].boolean().optional().nullable()
});
const confinementCheckResultSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    nonReinforcedSection: nonReinforcedSectionSchema.optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    reinforcedSection: reinforcedSectionSchema.optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"])
});
const confinementReinforcementCalculationResult = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    matrixGeometricPercentage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    reinforcementGeometricPercentage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    coefficicentOfHorizontalEfficiency: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    coefficicentOfVerticalEfficiency: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    coefficientOfOverallEfficiency: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    coefficientOfEffectivenessOfTheMatrix: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    computationalStrainOfTheComposite: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    confiningPressure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    effectiveConfiningPressure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    confinementCheck: confinementCheckResultSchema.optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"])
});
}),
"[project]/src/types/schemas/masonry/maschi-murari-form.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "extremityDetachementCheckSchema",
    ()=>extremityDetachementCheckSchema,
    "flexuralReinforcementVerifyExecutionSchema",
    ()=>flexuralReinforcementVerifyExecutionSchema,
    "inPlaneFlexuralCheckSchema",
    ()=>inPlaneFlexuralCheckSchema,
    "inPlaneFlexuralHypothesis",
    ()=>inPlaneFlexuralHypothesis,
    "inPlaneFlexuralNonReinforcedSection",
    ()=>inPlaneFlexuralNonReinforcedSection,
    "inPlaneFlexuralReinforcedSection",
    ()=>inPlaneFlexuralReinforcedSection,
    "inPlaneShearCheckSchema",
    ()=>inPlaneShearCheckSchema,
    "maschiMurariActionsSchema",
    ()=>maschiMurariActionsSchema,
    "maschiMurariFlexuralReinforcementCalculationResultSchema",
    ()=>maschiMurariFlexuralReinforcementCalculationResultSchema,
    "maschiMurariFlexuralReinforcementExecutionSchema",
    ()=>maschiMurariFlexuralReinforcementExecutionSchema,
    "maschiMurariFlexuralReinforcementVerifyExecutionSchema",
    ()=>maschiMurariFlexuralReinforcementVerifyExecutionSchema,
    "maschiMurariMaterialPropertiesSchema",
    ()=>maschiMurariMaterialPropertiesSchema,
    "maschiMurariPanelGeometrySchema",
    ()=>maschiMurariPanelGeometrySchema,
    "maschiMurariParamsSchema",
    ()=>maschiMurariParamsSchema,
    "maschiMurariShearReinforcementCalculationResultSchema",
    ()=>maschiMurariShearReinforcementCalculationResultSchema,
    "maschiMurariShearReinforcementExecutionSchema",
    ()=>maschiMurariShearReinforcementExecutionSchema,
    "maschiMurariShearReinforcementVerifyExecutionSchema",
    ()=>maschiMurariShearReinforcementVerifyExecutionSchema,
    "nonReinforcedSectionShearSchema",
    ()=>nonReinforcedSectionShearSchema,
    "outOfPlaneFlexuralCheckSchema",
    ()=>outOfPlaneFlexuralCheckSchema,
    "outOfPlaneFlexuralRegionHypothesis",
    ()=>outOfPlaneFlexuralRegionHypothesis,
    "outOfPlaneNonReinforcedSection",
    ()=>outOfPlaneNonReinforcedSection,
    "outOfPlaneReinforcedSection",
    ()=>outOfPlaneReinforcedSection,
    "reinforcedSectionShearSchema",
    ()=>reinforcedSectionShearSchema,
    "shearCheckSchema",
    ()=>shearCheckSchema,
    "shearReinforcementExecutionSchema",
    ()=>shearReinforcementExecutionSchema
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/functions/zod/null-to-undefined-transform.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$products$2f$schemas$2f$product$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/products/schemas/product.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/index.js [app-rsc] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$product$2d$form$2d$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/product-form-schema.ts [app-rsc] (ecmascript)");
;
;
;
;
;
const maschiMurariPanelGeometrySchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    height: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().positive().optional().nullable(),
    width: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().positive().optional().nullable(),
    thickness: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().positive().optional().nullable()
});
const maschiMurariMaterialPropertiesSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    structuralElementsNature: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_FACING_MATERIAL"]),
    masonryType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_CRM_MASONRY_TYPE"]),
    knowledgeLevel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_MATERIAL_KNOWLEDGE_LEVEL"]),
    confidenceFactor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    executionClass: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_EXECUTION_CLASS"]),
    loadResistantCategory: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_LOAD_RESISTING_CATEGORY"]),
    masonrySafetyFactor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    characteristicCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    designCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    characteristicShearStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    designShearStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    elasticModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    shearModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    ultimateCompressiveStrainLinearBehaviour: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    ultimateCompressiveStrain: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    exposure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_GEOMETRY_EXPOSURE"]),
    conversionFactor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable()
});
const maschiMurariActionsSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    appliedNormalStress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    inPlaneBendingMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    outOfPlaneBendingMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    inPlaneAppliedShear: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    outOfPlaneAppliedShear: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable()
});
const maschiMurariParamsSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    panelGeometry: maschiMurariPanelGeometrySchema.nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    materialProperties: maschiMurariMaterialPropertiesSchema.nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    actions: maschiMurariActionsSchema.nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"])
});
const maschiMurariShearReinforcementVerifyExecutionSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    product: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$products$2f$schemas$2f$product$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productSchema"],
    reinforcementFailureMode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["REINFORCEMENT_FAILURE_MODE"]),
    designReinforcementStress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    designReinforcementStrain: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    reinforcedArrangement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["REINFORCEMENT_ARRANGEMENT"]),
    singleStripWidth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    stripSpacing: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    totalReinforcementWidthPerpendicularShearDirection: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    compressedEdgeReinforcementFiberDistance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    layersNumber: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    reinforcedSidesNumber: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number()
});
const maschiMurariFlexuralReinforcementVerifyExecutionSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    // For in-plane bending
    product: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$products$2f$schemas$2f$product$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productSchema"],
    sectionFailureMode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["REINFORCEMENT_FAILURE_MODE"]),
    designReinforcementStress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    designReinforcementStrain: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    reinforcedArrangement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["REINFORCEMENT_ARRANGEMENT"]),
    singleStripWidth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    stripSpacing: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    reinforcementTotalWidthAlongLength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    compressedEdgeReinforcementFiberDistance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    layersNumber: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    reinforcedSidesNumber: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    totalEquivalentThickness: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    firstCoefficient: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    secondCoefficient: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    // Only for out-of-plane bending
    outOfPlanUnitWidthPanel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    outOfPlanAppliedDesignAxialStress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    outOfPlaneReinforcementFailureMode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["REINFORCEMENT_FAILURE_MODE"]),
    outOfPlaneDesignReinforcementStress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    outOfPlaneDesignReinforcementStrain: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    outOfPlaneReinforcementTotalWidthAlongLength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    outOfPlaneResistingArea: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number()
});
const flexuralReinforcementVerifyExecutionSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    // For in-plane bending
    product: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$product$2d$form$2d$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productFormSchema"],
    sectionFailureMode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["REINFORCEMENT_FAILURE_MODE"]),
    designReinforcementStress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    designReinforcementStrain: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    reinforcedArrangement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["REINFORCEMENT_ARRANGEMENT"]),
    singleStripWidth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    stripSpacing: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    reinforcementTotalWidthAlongLength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    compressedEdgeReinforcementFiberDistance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    layersNumber: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    reinforcedSidesNumber: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    totalEquivalentThickness: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    firstCoefficient: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    secondCoefficient: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    // Only for out-of-plane bending
    outOfPlanUnitWidthPanel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    outOfPlanAppliedDesignAxialStress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    outOfPlaneReinforcementFailureMode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["REINFORCEMENT_FAILURE_MODE"]),
    outOfPlaneDesignReinforcementStress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    outOfPlaneDesignReinforcementStrain: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    outOfPlaneReinforcementTotalWidthAlongLength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    outOfPlaneResistingArea: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number()
});
const shearReinforcementExecutionSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    product: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$product$2d$form$2d$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productFormSchema"],
    reinforcementFailureMode: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["REINFORCEMENT_FAILURE_MODE"]),
    designReinforcementStress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    designReinforcementStrain: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    reinforcedArrangement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["REINFORCEMENT_ARRANGEMENT"]),
    singleStripWidth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    stripSpacing: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    totalReinforcementWidthPerpendicularShearDirection: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    compressedEdgeReinforcementFiberDistance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    layersNumber: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    reinforcedSidesNumber: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number()
});
const maschiMurariFlexuralReinforcementExecutionSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    calculationType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].literal('FLEXURAL_VERIFY'),
    input: flexuralReinforcementVerifyExecutionSchema
});
const maschiMurariShearReinforcementExecutionSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    calculationType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].literal('SHEAR_VERIFY'),
    input: shearReinforcementExecutionSchema
});
const nonReinforcedSectionShearSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    inPlaneAppliedShear: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    verticalStress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    correctionFactorBasedOnWallSlenderness: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    shearResistanceNotReinforcedMasonry: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    check: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].boolean()
});
const reinforcedSectionShearSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    shearResistanceReinforcementContribution: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    totalShearResistance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    firstCheck: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].boolean(),
    shearResistanceFromMasonryDiagonalCompression: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    secondCheck: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].boolean()
});
const inPlaneShearCheckSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    nonReinforcedSection: nonReinforcedSectionShearSchema.nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    reinforcedSection: reinforcedSectionShearSchema.nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"])
});
const maschiMurariShearReinforcementCalculationResultSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    inPlaneShearCheck: inPlaneShearCheckSchema.nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"])
});
const inPlaneFlexuralHypothesis = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    neutralAxisCompressedEdgeDistance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    reinforcementOrMasonryStrain: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    check: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].boolean()
});
const inPlaneFlexuralReinforcedSection = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    hypothesisOne: inPlaneFlexuralHypothesis.optional().nullable(),
    hypothesisTwo: inPlaneFlexuralHypothesis.optional().nullable(),
    hypothesisThree: inPlaneFlexuralHypothesis.optional().nullable(),
    momentCapacity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    check: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].boolean()
});
const inPlaneFlexuralNonReinforcedSection = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    inPlaneBendingMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    neutralAxisCompressedEdgeDistance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    inPlaneFlexuralCapacity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    check: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].boolean()
});
const inPlaneFlexuralCheckSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    nonReinforcedSection: inPlaneFlexuralNonReinforcedSection.optional().nullable(),
    reinforcedSection: inPlaneFlexuralReinforcedSection.optional().nullable()
});
const outOfPlaneFlexuralRegionHypothesis = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    neutralAxisCompressedEdgeDistance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    reinforcementOrMasonryStrain: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    resultantCompressiveForceMasonry: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    resultantTensileForceFrcm: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    designBendingMomentReinforcedSection: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    check: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].boolean()
});
const outOfPlaneReinforcedSection = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    regionHypothesisTwo: outOfPlaneFlexuralRegionHypothesis.optional().nullable(),
    regionHypothesisOne: outOfPlaneFlexuralRegionHypothesis.optional().nullable(),
    momentCapacity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    check: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].boolean()
});
const outOfPlaneNonReinforcedSection = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    appliedDesignBendingMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    neutralAxisCompressedEdgeDistance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    resultantCompressiveForceMasonry: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    designBendingMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    check: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].boolean()
});
const outOfPlaneFlexuralCheckSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    nonReinforcedSection: outOfPlaneNonReinforcedSection.optional().nullable(),
    reinforcedSection: outOfPlaneReinforcedSection.optional().nullable()
});
const shearCheckSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    appliedDesignSpecificShearForce: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    averageNormalStress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    resultantCompressiveForceMasonry: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    resultantTensileForceFrcm: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    check: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].boolean()
});
const extremityDetachementCheckSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    appliedSpecificBendingMoment30FromEdge: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number().optional().nullable(),
    reinforcementDesignStrainForEndDebonding: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    neutralAxisCompressedEdgeDistance: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    masonryStrain: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    resultantCompressiveForceMasonry: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    resultantTensileForceFrcm: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    designMomentCapacityReinforcedSection: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].number(),
    check: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].boolean()
});
const maschiMurariFlexuralReinforcementCalculationResultSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$index$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].object({
    inPlaneFlexuralCheck: inPlaneFlexuralCheckSchema.nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    outOfPlaneFlexuralCheck: outOfPlaneFlexuralCheckSchema.nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    shearCheck: shearCheckSchema.nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    extremityDetachementCheck: extremityDetachementCheckSchema.nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"])
});
}),
"[project]/src/lib/api/modules/schemas/masonry-antioverturning-params.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "masonryAntiOverturningParamsSchema",
    ()=>masonryAntiOverturningParamsSchema,
    "masonryAntiOverturningPostInterventionCalculationResultSchema",
    ()=>masonryAntiOverturningPostInterventionCalculationResultSchema,
    "masonryAntiOverturningPreInterventionCalculationResultSchema",
    ()=>masonryAntiOverturningPreInterventionCalculationResultSchema,
    "masonryAntiOverturningSeismicDemandCalculationResultSchema",
    ()=>masonryAntiOverturningSeismicDemandCalculationResultSchema,
    "perimeterAndWidespreadInterventionCalculationResultResultSchema",
    ()=>perimeterAndWidespreadInterventionCalculationResultResultSchema,
    "regionHypothesisSchema",
    ()=>regionHypothesisSchema,
    "widespreadInterventionCalculationResultSchema",
    ()=>widespreadInterventionCalculationResultSchema
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/functions/zod/null-to-undefined-transform.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$antioverturning$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/masonry/antioverturning-form.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-rsc] (ecmascript) <export * as z>");
;
;
;
const masonryAntiOverturningParamsSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    siteCharacteristics: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$antioverturning$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["masonryAntiOverturningSiteCharacteristicsSchema"].optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    buildingCharacteristics: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$antioverturning$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["masonryAntiOverturningBuildingCharacteristicsSchema"].optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    materialProperties: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$antioverturning$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["masonryAntiOverturningMaterialPropertiesSchema"].optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    infillGeometry: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$antioverturning$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["masonryAntiOverturningInfillGeometrySchema"].optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"])
});
const masonryAntiOverturningSeismicDemandCalculationResultSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    maximumAcceleration: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    masonryAndPlasterWeight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    overturningSeismicForce: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()
});
const masonryAntiOverturningPreInterventionCalculationResultSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    overturningMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    stabilizingMomentExternalFacing: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    check: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean(),
    seismicRiskIndicator: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    seismicAccelerationCorrespondingToRiskIndicator: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()
});
const regionHypothesisSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    neutralAxisDistanceFromCompressedFlange: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    masonryDeformation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    masonryResultantCompressiveStresses: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    frcmResultantTensileStresses: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    designResistingMomentReinforcedSection: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    hypothesisCheck: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().optional().nullable()
});
const perimeterAndWidespreadInterventionCalculationResultResultSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    overturningMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    stabilizingMomentconnectors: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    totalStabilizingMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    check: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().optional().nullable()
});
const widespreadInterventionCalculationResultSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    panelFundamentalPeriod: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    maximumAcceleration: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    overturningSeismicForce: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    actingMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    midspanAxialStress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    normalStress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    unreinforcedSectionResistingMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    regionOneHypothesis: regionHypothesisSchema.optional().nullable(),
    regionTwoHypothesis: regionHypothesisSchema.optional().nullable(),
    specificResistingMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable(),
    check: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean().optional().nullable()
});
const masonryAntiOverturningPostInterventionCalculationResultSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    perimeterAndWidespreadInterventionCalculationResult: perimeterAndWidespreadInterventionCalculationResultResultSchema.optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    widespreadInterventionCalculationResult: widespreadInterventionCalculationResultSchema.optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"])
});
}),
"[project]/src/types/schemas/t-beam-form.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "tBeamFlexuralCalculationSchema",
    ()=>tBeamFlexuralCalculationSchema,
    "tBeamFormSchema",
    ()=>tBeamFormSchema,
    "tBeamGeneralSchema",
    ()=>tBeamGeneralSchema,
    "tBeamGeometrySchema",
    ()=>tBeamGeometrySchema,
    "tBeamMaterialSchema",
    ()=>tBeamMaterialSchema,
    "tBeamParamsCheckSchema",
    ()=>tBeamParamsCheckSchema,
    "tBeamRebarSchema",
    ()=>tBeamRebarSchema
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$product$2d$form$2d$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/product-form-schema.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-rsc] (ecmascript) <export * as z>");
;
;
;
const tBeamGeneralSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    initialDeformation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    polarity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_POLARITY"])
});
const tBeamGeometrySchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    primaryHeight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    primaryWidth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    secondaryHeight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    secondaryWidth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    concreteCover1: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    concreteCover2: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    totalHeight: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
    effectiveDepth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    exposure: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_GEOMETRY_EXPOSURE"])
});
const tBeamRebarSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    compressionRebars: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        diameter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
        quantity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
        area: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()
    }),
    tensionRebars: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        diameter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
        quantity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().positive(),
        area: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()
    })
});
const tBeamMaterialSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    knowledgeLevel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_MATERIAL_KNOWLEDGE_LEVEL"]),
    confidenceFactor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
    concreteClass: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        cubeCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        cylinderCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        averageCompressiveStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        averageTensileStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        elasticModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        designCompressiveStrengthForBrittleMechanisms: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        designCompressiveStrengthForDuctileMechanisms: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()
    }),
    steelGrade: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
        yieldStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        tensileStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        elongationPercentage: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        elasticModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        designYieldStrengthForBrittleMechanisms: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        designYieldStrengthForDuctileMechanisms: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number()
    })
});
const tBeamFormSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    initialDeformation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional(),
    polarity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_POLARITY"]).optional(),
    geometry: tBeamGeometrySchema.optional(),
    tBeamRebar: tBeamRebarSchema.optional(),
    materialProperties: tBeamMaterialSchema.optional()
});
const tBeamParamsCheckSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    initialDeformation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    polarity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_POLARITY"]),
    geometry: tBeamGeometrySchema,
    tBeamRebar: tBeamRebarSchema,
    materialProperties: tBeamMaterialSchema
});
const tBeamFlexuralCalculationSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    calculationType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal('FLEXURAL_VERIFY'),
    input: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        stripWidth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        layersNumber: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        bendingMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        product: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$product$2d$form$2d$schema$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productFormSchema"]
    })
});
}),
"[project]/src/lib/api/modules/schemas/t-beam-params.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "tBeamCalculationCheck",
    ()=>tBeamCalculationCheck,
    "tBeamFlexuralCalculationResultSchema",
    ()=>tBeamFlexuralCalculationResultSchema,
    "tBeamFlexuralVerifyExecutionInputSchema",
    ()=>tBeamFlexuralVerifyExecutionInputSchema,
    "tBeamParamsSchema",
    ()=>tBeamParamsSchema
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/constants/module.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$object$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/functions/zod/null-to-object-transform.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/functions/zod/null-to-undefined-transform.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$products$2f$schemas$2f$product$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/products/schemas/product.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$t$2d$beam$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/t-beam-form.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-rsc] (ecmascript) <export * as z>");
;
;
;
;
;
;
const tBeamParamsSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    initialDeformation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    polarity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$constants$2f$module$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["MODULE_POLARITY"]).optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    geometry: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$t$2d$beam$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["tBeamGeometrySchema"].optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$object$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToObjectTransform"]),
    tBeamRebar: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$t$2d$beam$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["tBeamRebarSchema"].optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$object$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToObjectTransform"]),
    materialProperties: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$t$2d$beam$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["tBeamMaterialSchema"].optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$object$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToObjectTransform"])
});
const tBeamFlexuralVerifyExecutionInputSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    stripWidth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    layersNumber: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    bendingMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    product: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$products$2f$schemas$2f$product$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["productSchema"]
});
const tBeamFlexuralCalculationResultSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    momentCapacity: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    equilibrium: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    checkResult: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean()
});
const tBeamCalculationCheck = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    flexuralVerifyExecutionResult: tBeamFlexuralCalculationResultSchema
});
}),
"[project]/src/lib/api/modules/schemas/modules.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "moduleBase",
    ()=>moduleBase,
    "moduleSchema",
    ()=>moduleSchema,
    "moduleWithParamsSchema",
    ()=>moduleWithParamsSchema
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$date$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/functions/zod/date-transform.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$object$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/functions/zod/null-to-object-transform.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/functions/zod/null-to-undefined-transform.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$pillar$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/modules/schemas/pillar-params.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$rectangular$2d$beam$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/modules/schemas/rectangular-beam-params.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$slab$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/modules/schemas/slab-params.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$wood$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/modules/schemas/wood-params.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$antioverturning$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/masonry/antioverturning-form.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$crm$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/masonry/crm-form.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$frcm$2d$column$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/masonry/frcm-column-form.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$maschi$2d$murari$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/masonry/maschi-murari-form.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-rsc] (ecmascript) <export * as z>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$masonry$2d$antioverturning$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/modules/schemas/masonry-antioverturning-params.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$t$2d$beam$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/modules/schemas/t-beam-params.ts [app-rsc] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const moduleBase = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
    name: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
    description: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    createdAt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$date$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dateTransform"]),
    lastModified: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$date$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dateTransform"])
});
const moduleSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].discriminatedUnion('type', [
    moduleBase.extend({
        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal('RECTANGULAR_BEAM'),
        params: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$rectangular$2d$beam$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["rectangularBeamParamsSchema"].nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$object$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToObjectTransform"]),
        flexuralVerifyExecutionInput: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$rectangular$2d$beam$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["rectangularBeamFlexuralVerifyExecutionInputSchema"].optional().nullable(),
        flexuralCalculationResult: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$rectangular$2d$beam$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["rectangularBeamFlexuralCalculationResultSchema"].optional().nullable(),
        shearVerifyExecutionInput: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$rectangular$2d$beam$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["rectangularBeamShearVerifyExecutionInputSchema"].optional().nullable(),
        shearCalculationResult: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$rectangular$2d$beam$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["rectangularBeamShearCalculationResultSchema"].optional().nullable()
    }),
    moduleBase.extend({
        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal('PILLAR'),
        params: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$pillar$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pillarParamsSchema"].nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$object$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToObjectTransform"]),
        flexuralVerifyExecutionInput: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$pillar$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pillarFlexuralVerifyExecutionInputSchema"].optional().nullable(),
        flexuralCalculationResult: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$pillar$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pillarFlexuralCalculationResultSchema"].optional().nullable(),
        shearVerifyExecutionInput: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$pillar$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pillarShearVerifyExecutionInputSchema"].optional().nullable(),
        shearCalculationResult: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$pillar$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pillarShearCalculationResultSchema"].optional().nullable()
    }),
    moduleBase.extend({
        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal('SLAB'),
        params: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$slab$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["slabParamsSchema"].nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$object$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToObjectTransform"]),
        // Flexural verification M+ (positive moment / span)
        flexuralVerifyExecutionInputMPlus: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$slab$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["slabFlexuralVerifyExecutionInputSchema"].optional().nullable(),
        flexuralVerifyExecutionResultMPlus: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$slab$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["slabFlexuralCalculationResultSchema"].optional().nullable(),
        // Flexural verification M- (negative moment / support)
        flexuralVerifyExecutionInputMMinus: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$slab$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["slabFlexuralVerifyExecutionInputSchema"].optional().nullable(),
        flexuralVerifyExecutionResultMMinus: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$slab$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["slabFlexuralCalculationResultSchema"].optional().nullable(),
        // Shear verification
        shearVerifyExecutionInput: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$slab$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["slabShearVerifyExecutionInputSchema"].optional().nullable(),
        shearVerifyExecutionResult: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$slab$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["slabShearCalculationResultSchema"].optional().nullable(),
        // Interface slip verification
        interfaceSlipVerifyExecutionInput: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$slab$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["slabInterfaceSlipVerifyExecutionInputSchema"].optional().nullable(),
        interfaceSlipCalculationResult: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$slab$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["slabInterfaceSlipCalculationResultSchema"].optional().nullable(),
        // Legacy calculations result (deprecated, kept for backwards compatibility)
        calculationsResult: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$slab$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["slabCalculationsResultSchema"].optional().nullable()
    }),
    moduleBase.extend({
        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal('ANTI_OVERTURNING'),
        params: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$masonry$2d$antioverturning$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["masonryAntiOverturningParamsSchema"].nullable().optional(),
        seismicDemandCalculationResult: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$masonry$2d$antioverturning$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["masonryAntiOverturningSeismicDemandCalculationResultSchema"].optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
        preInterventionCalculationResult: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$masonry$2d$antioverturning$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["masonryAntiOverturningPreInterventionCalculationResultSchema"].optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
        compositeReinforcementSystemVerifyInput: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$antioverturning$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["masonryAntiOverturningCompositeReinforcementSystemVerifyInputSchema"].optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
        postInterventionCalculationResult: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$masonry$2d$antioverturning$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["masonryAntiOverturningPostInterventionCalculationResultSchema"].optional().nullable()
    }),
    moduleBase.extend({
        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal('WOOD'),
        params: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$wood$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["woodParamsSchema"].nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$object$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToObjectTransform"])
    }),
    moduleBase.extend({
        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal('T_BEAM'),
        params: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$t$2d$beam$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["tBeamParamsSchema"].nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$object$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToObjectTransform"]),
        flexuralVerifyExecutionInput: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$t$2d$beam$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["tBeamFlexuralVerifyExecutionInputSchema"].optional().nullable(),
        flexuralVerifyExecutionResult: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$t$2d$beam$2d$params$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["tBeamFlexuralCalculationResultSchema"].optional().nullable()
    }),
    moduleBase.extend({
        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal('CRM'),
        preIntervention: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$crm$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["crmPreInterventionSchema"].nullable().optional(),
        postIntervention: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$crm$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["crmPostInterventionSchema"].optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"])
    }),
    moduleBase.extend({
        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal('FRCM_COLUMN'),
        params: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$frcm$2d$column$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["frcmColumnParamsSchema"].nullable().optional(),
        confinementReinforcementVerifyInput: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$frcm$2d$column$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["confinementReinforcementVerifyInput"].optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
        confinementReinforcementCalculationResult: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$frcm$2d$column$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["confinementReinforcementCalculationResult"].optional().nullable().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"])
    }),
    moduleBase.extend({
        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal('MASCHI_MURARI'),
        params: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$maschi$2d$murari$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["maschiMurariParamsSchema"].nullable().optional(),
        shearReinforcementVerifyExecutionInput: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$maschi$2d$murari$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["maschiMurariShearReinforcementVerifyExecutionSchema"].optional().nullable(),
        shearReinforcementCalculationResult: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$maschi$2d$murari$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["maschiMurariShearReinforcementCalculationResultSchema"].optional().nullable(),
        flexuralReinforcementVerifyExecutionInput: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$maschi$2d$murari$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["maschiMurariFlexuralReinforcementVerifyExecutionSchema"].optional().nullable(),
        flexuralReinforcementCalculationResult: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$masonry$2f$maschi$2d$murari$2d$form$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["maschiMurariFlexuralReinforcementCalculationResultSchema"].optional().nullable()
    }),
    moduleBase.extend({
        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].literal('UNKNOWN')
    })
]);
const moduleWithParamsSchema = moduleSchema;
}),
"[project]/src/lib/api/httpClient.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "apiClient",
    ()=>apiClient
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-rsc] (ecmascript)");
;
const apiClient = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: ("TURBOPACK compile-time value", "http://localhost:8000"),
    headers: {
        'Content-Type': 'application/json'
    }
});
}),
"[project]/src/lib/api/modules/endpoints/get-module.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "fetch",
    ()=>fetch,
    "getModuleById",
    ()=>getModuleById
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$apiErrors$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/apiErrors.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$modules$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/modules/schemas/modules.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fp$2d$ts$2f$es6$2f$function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/fp-ts/es6/function.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fp$2d$ts$2f$es6$2f$TaskEither$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/fp-ts/es6/TaskEither.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$httpClient$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/httpClient.ts [app-rsc] (ecmascript)");
;
;
;
;
;
const validate = (data)=>{
    const parse = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$modules$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["moduleWithParamsSchema"].safeParse(data);
    if (!parse.success) {
        throw {
            type: 'ValidationError',
            code: 'MALFORMED_RESPONSE',
            message: parse.error.message
        };
    }
    return parse.data;
};
const fetch = (token, id, moduleId)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fp$2d$ts$2f$es6$2f$TaskEither$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["tryCatch"])(()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$httpClient$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["apiClient"].get(`/api/v2/projects/${id}/modules/${moduleId}`, {
            ...token && {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            }
        }), (e)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$apiErrors$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["handleApiError"])(e));
const getModuleById = (token, projectId, moduleId)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fp$2d$ts$2f$es6$2f$function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(fetch(token, projectId, moduleId), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fp$2d$ts$2f$es6$2f$TaskEither$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((a)=>a.data), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fp$2d$ts$2f$es6$2f$TaskEither$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(validate));
}),
"[project]/src/lib/query/modules/get-module.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "getModuleQuery",
    ()=>getModuleQuery
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$endpoints$2f$get$2d$module$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/modules/endpoints/get-module.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fp$2d$ts$2f$es6$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/fp-ts/es6/Either.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fp$2d$ts$2f$es6$2f$function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/fp-ts/es6/function.js [app-rsc] (ecmascript)");
;
;
;
const getModuleQuery = async (session, projectId, moduleId)=>{
    const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$endpoints$2f$get$2d$module$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getModuleById"])(session.accessToken, projectId, moduleId)();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fp$2d$ts$2f$es6$2f$function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(result, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fp$2d$ts$2f$es6$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fold"])((err)=>{
        throw err;
    }, (data)=>data));
};
}),
"[project]/src/lib/api/common/paginated-schema.tsx [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "paginatedSchema",
    ()=>paginatedSchema
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-rsc] (ecmascript) <export * as z>");
;
const paginatedSchema = {
    pageable: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        pageNumber: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        pageSize: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        sort: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
            empty: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean(),
            sorted: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean(),
            unsorted: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean()
        }),
        offset: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
        paged: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean(),
        unpaged: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean()
    }),
    last: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean(),
    totalElements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    totalPages: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    size: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    number: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    first: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean(),
    numberOfElements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number(),
    sort: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
        empty: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean(),
        sorted: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean(),
        unsorted: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean()
    }),
    empty: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].boolean()
};
}),
"[project]/src/lib/api/projects/schemas/projects.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "paginatedProjectSchema",
    ()=>paginatedProjectSchema,
    "projectSchema",
    ()=>projectSchema
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$date$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/functions/zod/date-transform.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/functions/zod/null-to-undefined-transform.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$common$2f$paginated$2d$schema$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/common/paginated-schema.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$modules$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/modules/schemas/modules.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-rsc] (ecmascript) <export * as z>");
;
;
;
;
;
const projectSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
    address: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
    baseTenderAmount: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    baseTenderCurrency: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    company: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
    constructionSiteName: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
    createdAt: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$date$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dateTransform"]),
    lastModified: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$date$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["dateTransform"]),
    machiningSurfaceSize: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().nullable().optional().transform((val)=>val ? Number(val) : undefined),
    plannedWorkDescription: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().nullable().optional().transform(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$functions$2f$zod$2f$null$2d$to$2d$undefined$2d$transform$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["nullToUndefinedTransform"]),
    planner: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
    userId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
    processingType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string(),
    modules: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$modules$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["moduleSchema"])
});
const paginatedProjectSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$common$2f$paginated$2d$schema$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["paginatedSchema"],
    content: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].array(projectSchema)
});
}),
"[project]/src/lib/api/projects/endpoints/get-project.ts [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "fetch",
    ()=>fetch,
    "getProjectById",
    ()=>getProjectById
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$apiErrors$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/apiErrors.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$projects$2f$schemas$2f$projects$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/projects/schemas/projects.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fp$2d$ts$2f$es6$2f$function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/fp-ts/es6/function.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fp$2d$ts$2f$es6$2f$TaskEither$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/fp-ts/es6/TaskEither.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$httpClient$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/httpClient.ts [app-rsc] (ecmascript)");
;
;
;
;
;
const validate = (data)=>{
    const parse = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$projects$2f$schemas$2f$projects$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["projectSchema"].safeParse(data);
    if (!parse.success) {
        throw {
            type: 'ValidationError',
            code: 'MALFORMED_RESPONSE',
            message: parse.error.message
        };
    }
    return parse.data;
};
const fetch = (token, id)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fp$2d$ts$2f$es6$2f$TaskEither$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["tryCatch"])(()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$httpClient$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["apiClient"].get(`/api/v2/projects/${id}`, {
            ...token && {
                headers: {
                    Authorization: `Bearer ${token}`
                }
            }
        }), (e)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$apiErrors$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["handleApiError"])(e));
const getProjectById = (token, projectId)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fp$2d$ts$2f$es6$2f$function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(fetch(token, projectId), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fp$2d$ts$2f$es6$2f$TaskEither$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])((a)=>a.data), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fp$2d$ts$2f$es6$2f$TaskEither$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["map"])(validate));
}),
"[project]/src/lib/query/projects/get-project.tsx [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "getProjectQuery",
    ()=>getProjectQuery
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$projects$2f$endpoints$2f$get$2d$project$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/projects/endpoints/get-project.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fp$2d$ts$2f$es6$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/fp-ts/es6/Either.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fp$2d$ts$2f$es6$2f$function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/fp-ts/es6/function.js [app-rsc] (ecmascript)");
;
;
;
const getProjectQuery = async (session, projectId)=>{
    const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$projects$2f$endpoints$2f$get$2d$project$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getProjectById"])(session.accessToken, projectId)();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fp$2d$ts$2f$es6$2f$function$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["pipe"])(result, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$fp$2d$ts$2f$es6$2f$Either$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["fold"])((err)=>{
        throw err;
    }, (data)=>data));
};
}),
"[project]/src/components/pages/modules/module-detail-page.tsx [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "ModuleDetailPage",
    ()=>ModuleDetailPage
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/auth.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$breadcrumbs$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/breadcrumbs.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$module$2d$detail$2d$content$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/module-detail-content.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/separator.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$sidebar$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/sidebar.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$modules$2f$get$2d$module$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/query/modules/get-module.ts [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$projects$2f$get$2d$project$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/query/projects/get-project.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getTranslations$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__getTranslations$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/server/react-server/getTranslations.js [app-rsc] (ecmascript) <export default as getTranslations>");
;
;
;
;
;
;
;
;
;
const ModuleDetailPage = async (props)=>{
    const params = await props.params;
    const { projectId, moduleId } = params;
    const tBread = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getTranslations$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__getTranslations$3e$__["getTranslations"])('breadcrumbs');
    const session = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["auth"])();
    if (!session?.user) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$auth$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["signIn"])();
    }
    const project = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$projects$2f$get$2d$project$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getProjectQuery"])(session, projectId);
    const module = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$modules$2f$get$2d$module$2e$ts__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["getModuleQuery"])(session, projectId, moduleId);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Fragment"], {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                className: "flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center gap-2 px-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$sidebar$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["SidebarTrigger"], {
                            className: "-ml-1"
                        }, void 0, false, {
                            fileName: "[project]/src/components/pages/modules/module-detail-page.tsx",
                            lineNumber: 34,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Separator"], {
                            orientation: "vertical",
                            className: "mr-2 h-4"
                        }, void 0, false, {
                            fileName: "[project]/src/components/pages/modules/module-detail-page.tsx",
                            lineNumber: 35,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$breadcrumbs$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["Breadcrumbs"], {
                            content: [
                                {
                                    label: tBread('dashboard'),
                                    href: '/dashboard'
                                },
                                {
                                    label: tBread('project', {
                                        name: project.constructionSiteName ?? ''
                                    }),
                                    href: `/dashboard/projects/${projectId}`
                                },
                                {
                                    label: tBread('module', {
                                        name: module.name ?? ''
                                    })
                                }
                            ]
                        }, void 0, false, {
                            fileName: "[project]/src/components/pages/modules/module-detail-page.tsx",
                            lineNumber: 36,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/pages/modules/module-detail-page.tsx",
                    lineNumber: 33,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/pages/modules/module-detail-page.tsx",
                lineNumber: 32,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex flex-1 flex-col gap-4 p-4 pt-0",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$module$2d$detail$2d$content$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ModuleDetailContent"], {
                    session: session,
                    projectId: projectId,
                    initialDataProject: project,
                    moduleId: moduleId,
                    initialDataModule: module
                }, void 0, false, {
                    fileName: "[project]/src/components/pages/modules/module-detail-page.tsx",
                    lineNumber: 55,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/pages/modules/module-detail-page.tsx",
                lineNumber: 54,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true);
};
}),
"[project]/src/app/[locale]/dashboard/projects/[projectId]/modules/[moduleId]/page.tsx [app-rsc] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__,
    "generateMetadata",
    ()=>generateMetadata
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pages$2f$modules$2f$module$2d$detail$2d$page$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/pages/modules/module-detail-page.tsx [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getTranslations$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__getTranslations$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/server/react-server/getTranslations.js [app-rsc] (ecmascript) <export default as getTranslations>");
;
;
const generateMetadata = async ({ params })=>{
    const { locale } = await params;
    const t = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$server$2f$react$2d$server$2f$getTranslations$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__$3c$export__default__as__getTranslations$3e$__["getTranslations"])({
        locale,
        namespace: 'pages.module-detail.meta'
    });
    return {
        title: t('title'),
        description: t('description')
    };
};
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$pages$2f$modules$2f$module$2d$detail$2d$page$2e$tsx__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["ModuleDetailPage"];
}),
"[project]/src/app/[locale]/dashboard/projects/[projectId]/modules/[moduleId]/page.tsx [app-rsc] (ecmascript, Next.js Server Component)", ((__turbopack_context__) => {

__turbopack_context__.n(__turbopack_context__.i("[project]/src/app/[locale]/dashboard/projects/[projectId]/modules/[moduleId]/page.tsx [app-rsc] (ecmascript)"));
}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)", ((__turbopack_context__, module, exports) => {

const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}),
];

//# sourceMappingURL=%5Broot-of-the-server%5D__7904901e._.js.map