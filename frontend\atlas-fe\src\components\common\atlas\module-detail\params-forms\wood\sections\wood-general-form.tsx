import { NumberFormInput } from '@atlas/components/common/form/number-form-input'
import { Button } from '@atlas/components/ui/button'
import { Form } from '@atlas/components/ui/form'
import type { Module } from '@atlas/lib/api/modules/schemas/modules'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'
import {
  type WoodGeneralFormInputs,
  woodGeneralSchema,
} from '@atlas/types/schemas/wood-form'
import { zodResolver } from '@hookform/resolvers/zod'

import { Loader2Icon } from 'lucide-react'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useCallback } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'

type Props = {
  session: Session
  projectId: Project['id']
  moduleId: Module['id']
  defaultValues?: Partial<WoodGeneralFormInputs>
  onSave: () => void
}

export const WoodGeneralForm = ({
  session,
  projectId,
  moduleId,
  defaultValues,
  onSave,
}: Props) => {
  const t = useTranslations('forms.project-params.wood.general')
  const tAction = useTranslations('actions.save-project-params.messages')
  const tCommon = useTranslations('actions.common')

  const form = useForm<WoodGeneralFormInputs>({
    resolver: zodResolver(woodGeneralSchema),
    defaultValues: {
      initialDeformation: defaultValues?.initialDeformation ?? 0,
    },
  })

  const { mutate, isPending } = useSaveModuleParamsMutation(
    session.accessToken,
    {
      onSuccess: () => {
        toast.success(tAction('edit.success'))
        // Query invalidation is handled by the mutation hook itself
        onSave()
      },
      onError: error => {
        toast.error(tAction('edit.failure', { error: error.message }))
      },
    },
  )

  const handleFormSubmit = useCallback(
    (body: WoodGeneralFormInputs) => {
      mutate({ projectId, moduleId, body })
    },
    [mutate, projectId, moduleId],
  )

  return (
    <Form {...form}>
      <form
        className="space-y-4 rounded-md border p-4"
        onSubmit={form.handleSubmit(handleFormSubmit)}
      >
        <NumberFormInput
          control={form.control}
          name="initialDeformation"
          t={t}
          required={true}
        />
        <Button
          type="submit"
          className="w-full sm:w-auto"
          disabled={isPending}
          onClick={form.handleSubmit(handleFormSubmit)}
        >
          {isPending && <Loader2Icon className="animate-spin" />}
          {tCommon('save')}
        </Button>
      </form>
    </Form>
  )
}
