{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\r\nimport { twMerge } from 'tailwind-merge'\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["import { cn } from '@atlas/lib/utils'\r\nimport { Slot } from '@radix-ui/react-slot'\r\nimport { ChevronRight, MoreHorizontal } from 'lucide-react'\r\nimport type * as React from 'react'\r\n\r\nfunction Breadcrumb({ ...props }: React.ComponentProps<'nav'>) {\r\n  return <nav aria-label=\"breadcrumb\" data-slot=\"breadcrumb\" {...props} />\r\n}\r\n\r\nfunction BreadcrumbList({ className, ...props }: React.ComponentProps<'ol'>) {\r\n  return (\r\n    <ol\r\n      data-slot=\"breadcrumb-list\"\r\n      className={cn(\r\n        'text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbItem({ className, ...props }: React.ComponentProps<'li'>) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-item\"\r\n      className={cn('inline-flex items-center gap-1.5', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbLink({\r\n  asChild,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'a'> & {\r\n  asChild?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : 'a'\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"breadcrumb-link\"\r\n      className={cn('hover:text-foreground transition-colors', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbPage({ className, ...props }: React.ComponentProps<'span'>) {\r\n  return (\r\n    // biome-ignore lint/a11y/useFocusableInteractive: from shadcn/ui/components/breadcrumb\r\n    <span\r\n      data-slot=\"breadcrumb-page\"\r\n      role=\"link\"\r\n      aria-disabled=\"true\"\r\n      aria-current=\"page\"\r\n      className={cn('text-foreground font-normal', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbSeparator({\r\n  children,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'li'>) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-separator\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn('[&>svg]:size-3.5', className)}\r\n      {...props}\r\n    >\r\n      {children ?? <ChevronRight />}\r\n    </li>\r\n  )\r\n}\r\n\r\nfunction BreadcrumbEllipsis({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'span'>) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-ellipsis\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn('flex size-9 items-center justify-center', className)}\r\n      {...props}\r\n    >\r\n      <MoreHorizontal className=\"size-4\" />\r\n      <span className=\"sr-only\">More</span>\r\n    </span>\r\n  )\r\n}\r\n\r\nexport {\r\n  Breadcrumb,\r\n  BreadcrumbList,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n  BreadcrumbEllipsis,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;;;;;AAGA,SAAS,WAAW,EAAE,GAAG,OAAoC;IAC3D,qBAAO,8OAAC;QAAI,cAAW;QAAa,aAAU;QAAc,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,4FACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,OAAO,EACP,SAAS,EACT,GAAG,OAGJ;IACC,MAAM,OAAO,UAAU,wKAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAqC;IAC3E,OACE,uFAAuF;kBACvF,8OAAC;QACC,aAAU;QACV,MAAK;QACL,iBAAc;QACd,gBAAa;QACb,WAAW,IAAA,yHAAE,EAAC,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,IAAA,yHAAE,EAAC,oBAAoB;QACjC,GAAG,KAAK;kBAER,0BAAY,8OAAC,sOAAY;;;;;;;;;;AAGhC;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,IAAA,yHAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;0BAET,8OAAC,kOAAc;gBAAC,WAAU;;;;;;0BAC1B,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/breadcrumbs.tsx"], "sourcesContent": ["import {\r\n  B<PERSON><PERSON>rumb,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbSeparator,\r\n} from '@atlas/components/ui/breadcrumb'\r\nimport { Fragment } from 'react'\r\n\r\ntype Props = {\r\n  content: { label: string; href?: string }[]\r\n}\r\n\r\nexport const Breadcrumbs = ({ content }: Props) => {\r\n  return (\r\n    <Breadcrumb>\r\n      <BreadcrumbList>\r\n        {content.map(({ label, href }, index) => (\r\n          <Fragment key={href ?? `${label}-${index}`}>\r\n            <BreadcrumbItem>\r\n              {href ? (\r\n                <BreadcrumbLink href={href}>{label}</BreadcrumbLink>\r\n              ) : (\r\n                label\r\n              )}\r\n            </BreadcrumbItem>\r\n            {index !== content.length - 1 && <BreadcrumbSeparator />}\r\n          </Fragment>\r\n        ))}\r\n      </BreadcrumbList>\r\n    </Breadcrumb>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAOA;;;;AAMO,MAAM,cAAc,CAAC,EAAE,OAAO,EAAS;IAC5C,qBACE,8OAAC,oJAAU;kBACT,cAAA,8OAAC,wJAAc;sBACZ,QAAQ,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,sBAC7B,8OAAC,iNAAQ;;sCACP,8OAAC,wJAAc;sCACZ,qBACC,8OAAC,wJAAc;gCAAC,MAAM;0CAAO;;;;;2EAE7B;;;;;;wBAGH,UAAU,QAAQ,MAAM,GAAG,mBAAK,8OAAC,6JAAmB;;;;;;mBARxC,QAAQ,GAAG,MAAM,CAAC,EAAE,OAAO;;;;;;;;;;;;;;;AAcpD", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/module-detail-content.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ModuleDetailContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call ModuleDetailContent() from the server but ModuleDetailContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/common/atlas/module-detail/module-detail-content.tsx <module evaluation>\",\n    \"ModuleDetailContent\",\n);\nexport const editModuleModalOpenAtom = registerClientReference(\n    function() { throw new Error(\"Attempted to call editModuleModalOpenAtom() from the server but editModuleModalOpenAtom is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/common/atlas/module-detail/module-detail-content.tsx <module evaluation>\",\n    \"editModuleModalOpenAtom\",\n);\nexport const moduleToDeleteAtom = registerClientReference(\n    function() { throw new Error(\"Attempted to call moduleToDeleteAtom() from the server but moduleToDeleteAtom is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/common/atlas/module-detail/module-detail-content.tsx <module evaluation>\",\n    \"moduleToDeleteAtom\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;;;AACvE;;AACO,MAAM,sBAAsB,IAAA,wQAAuB,EACtD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,qGACA;AAEG,MAAM,0BAA0B,IAAA,wQAAuB,EAC1D;IAAa,MAAM,IAAI,MAAM;AAA8P,GAC3R,qGACA;AAEG,MAAM,qBAAqB,IAAA,wQAAuB,EACrD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,qGACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 263, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/module-detail-content.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ModuleDetailContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call ModuleDetailContent() from the server but ModuleDetailContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/common/atlas/module-detail/module-detail-content.tsx\",\n    \"ModuleDetailContent\",\n);\nexport const editModuleModalOpenAtom = registerClientReference(\n    function() { throw new Error(\"Attempted to call editModuleModalOpenAtom() from the server but editModuleModalOpenAtom is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/common/atlas/module-detail/module-detail-content.tsx\",\n    \"editModuleModalOpenAtom\",\n);\nexport const moduleToDeleteAtom = registerClientReference(\n    function() { throw new Error(\"Attempted to call moduleToDeleteAtom() from the server but moduleToDeleteAtom is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/common/atlas/module-detail/module-detail-content.tsx\",\n    \"moduleToDeleteAtom\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;;;AACvE;;AACO,MAAM,sBAAsB,IAAA,wQAAuB,EACtD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,iFACA;AAEG,MAAM,0BAA0B,IAAA,wQAAuB,EAC1D;IAAa,MAAM,IAAI,MAAM;AAA8P,GAC3R,iFACA;AAEG,MAAM,qBAAqB,IAAA,wQAAuB,EACrD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,iFACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 295, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/separator.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Separator = registerClientReference(\n    function() { throw new Error(\"Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/separator.tsx <module evaluation>\",\n    \"Separator\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,YAAY,IAAA,wQAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,iEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/separator.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Separator = registerClientReference(\n    function() { throw new Error(\"Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/separator.tsx\",\n    \"Separator\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,YAAY,IAAA,wQAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 323, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/api/apiErrors.ts"], "sourcesContent": ["import type { ApiError, ApiErrorCode } from '@atlas/types'\r\nimport axios from 'axios'\r\n\r\nconst httpCodeMap: Record<number, ApiErrorCode> = {\r\n  400: '400_BAD_REQUEST',\r\n  401: '401_UNAUTHORIZED',\r\n  403: '403_FORBIDDEN',\r\n  404: '404_NOT_FOUND',\r\n  409: '409_CONFLICT',\r\n  422: '422_UNPROCESSABLE_ENTITY',\r\n  429: '429_TOO_MANY_REQUESTS',\r\n  500: '500_INTERNAL_SERVER_ERROR',\r\n  502: '502_BAD_GATEWAY',\r\n  503: '503_SERVICE_UNAVAILABLE',\r\n  504: '504_GATEWAY_TIMEOUT',\r\n}\r\n\r\nexport const handleApiError = (error: unknown): ApiError => {\r\n  console.log('handleApiError', error)\r\n  if (axios.isAxiosError(error)) {\r\n    const apiError = error.response?.data\r\n\r\n    const message =\r\n      typeof apiError?.message === 'string'\r\n        ? apiError.message\r\n        : error.message || 'error.unknown'\r\n\r\n    const code =\r\n      error.response?.status && httpCodeMap[error.response?.status]\r\n        ? httpCodeMap[error.response?.status]\r\n        : 'UNKNOWN_ERROR'\r\n\r\n    return {\r\n      type: 'ApiError',\r\n      message,\r\n      code,\r\n    }\r\n  }\r\n\r\n  if (error instanceof Error) {\r\n    return {\r\n      type: 'ApiError',\r\n      message: error.message || 'error.unknown',\r\n      code: 'UNKNOWN_ERROR',\r\n    }\r\n  }\r\n\r\n  return {\r\n    type: 'ApiError',\r\n    message: 'error.unknown',\r\n    code: 'UNKNOWN_ERROR',\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;;AAEA,MAAM,cAA4C;IAChD,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACP;AAEO,MAAM,iBAAiB,CAAC;IAC7B,QAAQ,GAAG,CAAC,kBAAkB;IAC9B,IAAI,gJAAK,CAAC,YAAY,CAAC,QAAQ;QAC7B,MAAM,WAAW,MAAM,QAAQ,EAAE;QAEjC,MAAM,UACJ,OAAO,UAAU,YAAY,WACzB,SAAS,OAAO,GAChB,MAAM,OAAO,IAAI;QAEvB,MAAM,OACJ,MAAM,QAAQ,EAAE,UAAU,WAAW,CAAC,MAAM,QAAQ,EAAE,OAAO,GACzD,WAAW,CAAC,MAAM,QAAQ,EAAE,OAAO,GACnC;QAEN,OAAO;YACL,MAAM;YACN;YACA;QACF;IACF;IAEA,IAAI,iBAAiB,OAAO;QAC1B,OAAO;YACL,MAAM;YACN,SAAS,MAAM,OAAO,IAAI;YAC1B,MAAM;QACR;IACF;IAEA,OAAO;QACL,MAAM;QACN,SAAS;QACT,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/functions/zod/date-transform.ts"], "sourcesContent": ["import { type RefinementCtx, z } from 'zod'\r\n\r\nexport const dateTransform = (\r\n  val: string | null | undefined,\r\n  ctx: RefinementCtx,\r\n): Date | undefined => {\r\n  if (!val) {\r\n    return undefined\r\n  }\r\n  const date = new Date(val)\r\n  if (Number.isNaN(date.getTime())) {\r\n    ctx.addIssue({\r\n      code: z.ZodIssueCode.custom,\r\n      message: 'Invalid date format',\r\n    })\r\n    return z.NEVER\r\n  }\r\n  return date\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,gBAAgB,CAC3B,KACA;IAEA,IAAI,CAAC,KAAK;QACR,OAAO;IACT;IACA,MAAM,OAAO,IAAI,KAAK;IACtB,IAAI,OAAO,KAAK,CAAC,KAAK,OAAO,KAAK;QAChC,IAAI,QAAQ,CAAC;YACX,MAAM,kLAAC,CAAC,YAAY,CAAC,MAAM;YAC3B,SAAS;QACX;QACA,OAAO,kLAAC,CAAC,KAAK;IAChB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 403, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/functions/zod/null-to-object-transform.ts"], "sourcesContent": ["export function nullToObjectTransform<T>(val: T | null | undefined): T {\r\n  return val ?? ({} as T)\r\n}\r\n"], "names": [], "mappings": ";;;;AAAO,SAAS,sBAAyB,GAAyB;IAChE,OAAO,OAAQ,CAAC;AAClB", "debugId": null}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/functions/zod/null-to-undefined-transform.ts"], "sourcesContent": ["export function nullToUndefinedTransform<T>(\r\n  val: T | null | undefined,\r\n): T | undefined {\r\n  return val ?? undefined\r\n}\r\n"], "names": [], "mappings": ";;;;AAAO,SAAS,yBACd,GAAyB;IAEzB,OAAO,OAAO;AAChB", "debugId": null}}, {"offset": {"line": 425, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/constants/product.tsx"], "sourcesContent": ["export enum productFiberType {\r\n  BASALT = 'basalt',\r\n  CARBON = 'carbon',\r\n  GALVANIZED_STEEL = 'galvanized_steel',\r\n  GLASS = 'glass',\r\n  PREFORMED_CARBON = 'preformed_carbon',\r\n  STEEL = 'steel',\r\n  STEEL_STRAIGHT = 'steel-straight',\r\n}\r\n\r\nexport type ProductFiberType =\r\n  (typeof productFiberType)[keyof typeof productFiberType]\r\n\r\nexport const PRODUCT_FIBER_TYPE = Object.values(productFiberType)\r\n"], "names": [], "mappings": ";;;;;;AAAO,IAAA,AAAK,0CAAA;;;;;;;;WAAA;;AAaL,MAAM,qBAAqB,OAAO,MAAM,CAAC", "debugId": null}}, {"offset": {"line": 446, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/api/products/schemas/product.ts"], "sourcesContent": ["import { PRODUCT_FIBER_TYPE } from '@atlas/constants/product'\r\nimport { nullToUndefinedTransform } from '@atlas/functions/zod/null-to-undefined-transform'\r\nimport { z } from 'zod'\r\n\r\n// Helper schema for fields that can be either a number or an object with parsedValue\r\nconst numericOrParsedValueSchema = z\r\n  .union([\r\n    z.number(),\r\n    z.object({\r\n      source: z.string(),\r\n      parsedValue: z.number(),\r\n    }),\r\n  ])\r\n  .transform(val => {\r\n    if (typeof val === 'number') {\r\n      return val\r\n    }\r\n    return val.parsedValue\r\n  })\r\n  .nullable()\r\n  .optional()\r\n  .transform(nullToUndefinedTransform)\r\n\r\nexport const productSchema = z.object({\r\n  id: z.string(),\r\n  name: z.string().nullable().optional().transform(nullToUndefinedTransform),\r\n  fiberType: z\r\n    .enum(PRODUCT_FIBER_TYPE)\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  availableWidths: z.array(z.number()).optional().nullable(),\r\n  orientation: z\r\n    .string()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  thickness: z\r\n    .number()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  tensileStrength: z\r\n    .number()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  elasticModulus: z\r\n    .number()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  characteristicCylindricalCompressiveStrengthFrcMatrix: z\r\n    .number()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  documentationLink: z\r\n    .string()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  productType: z\r\n    .string()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  ultimateStrain: z\r\n    .number()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  designDeformation: numericOrParsedValueSchema,\r\n  unitStrengthOfTheMesh: z\r\n    .number()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  width: z.number().nullable().optional().transform(nullToUndefinedTransform),\r\n  density: z.number().nullable().optional().transform(nullToUndefinedTransform),\r\n  maxResistance: z\r\n    .number()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  weight: z.number().nullable().optional().transform(nullToUndefinedTransform),\r\n  crossSectionArea: z\r\n    .number()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  diameter: z\r\n    .number()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  pullOutResistance: z\r\n    .number()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  connectorsNumberAlongLength: z\r\n    .number()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  designStrength: z\r\n    .number()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  maxLayerNumber: z\r\n    .number()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  systemDeformation: z\r\n    .number()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  adhesionToConcrete: numericOrParsedValueSchema,\r\n  shearStress: z\r\n    .number()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  toughnessClass: z\r\n    .string()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  cylindricCompressiveStrength: z\r\n    .number()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  characteristicTensileStrength: numericOrParsedValueSchema,\r\n  specificWeight: z\r\n    .number()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  facingPerformance: z.object().optional().nullable(),\r\n  reinforcementTensileStrength: z\r\n    .number()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  reinforcementUltimateStrain: z\r\n    .number()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  conventionalStressLimit: z\r\n    .record(z.string(), z.number())\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  conventionalStrainLimit: z\r\n    .record(z.string(), z.number())\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  matrixBreachStress: z\r\n    .number()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n})\r\n\r\nexport const paginatedProductSchema = z.object({\r\n  content: z.array(productSchema),\r\n  pageable: z.object({\r\n    pageNumber: z.number(),\r\n    pageSize: z.number(),\r\n    sort: z.object({\r\n      empty: z.boolean(),\r\n      unsorted: z.boolean(),\r\n      sorted: z.boolean(),\r\n    }),\r\n    offset: z.number(),\r\n    unpaged: z.boolean(),\r\n    paged: z.boolean(),\r\n  }),\r\n  last: z.boolean(),\r\n  totalElements: z.number(),\r\n  totalPages: z.number(),\r\n  first: z.boolean(),\r\n  sort: z.object({\r\n    empty: z.boolean(),\r\n    unsorted: z.boolean(),\r\n    sorted: z.boolean(),\r\n  }),\r\n  size: z.number(),\r\n  number: z.number(),\r\n  numberOfElements: z.number(),\r\n  empty: z.boolean(),\r\n})\r\n\r\nexport type Product = z.infer<typeof productSchema>\r\nexport type PaginatedProducts = z.infer<typeof paginatedProductSchema>\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;;;;AAEA,qFAAqF;AACrF,MAAM,6BAA6B,kLAAC,CACjC,KAAK,CAAC;IACL,kLAAC,CAAC,MAAM;IACR,kLAAC,CAAC,MAAM,CAAC;QACP,QAAQ,kLAAC,CAAC,MAAM;QAChB,aAAa,kLAAC,CAAC,MAAM;IACvB;CACD,EACA,SAAS,CAAC,CAAA;IACT,IAAI,OAAO,QAAQ,UAAU;QAC3B,OAAO;IACT;IACA,OAAO,IAAI,WAAW;AACxB,GACC,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;AAE9B,MAAM,gBAAgB,kLAAC,CAAC,MAAM,CAAC;IACpC,IAAI,kLAAC,CAAC,MAAM;IACZ,MAAM,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAC,2LAAwB;IACzE,WAAW,kLAAC,CACT,IAAI,CAAC,kJAAkB,EACvB,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,iBAAiB,kLAAC,CAAC,KAAK,CAAC,kLAAC,CAAC,MAAM,IAAI,QAAQ,GAAG,QAAQ;IACxD,aAAa,kLAAC,CACX,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,WAAW,kLAAC,CACT,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,iBAAiB,kLAAC,CACf,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,gBAAgB,kLAAC,CACd,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,uDAAuD,kLAAC,CACrD,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,mBAAmB,kLAAC,CACjB,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,aAAa,kLAAC,CACX,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,gBAAgB,kLAAC,CACd,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,mBAAmB;IACnB,uBAAuB,kLAAC,CACrB,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,OAAO,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAC,2LAAwB;IAC1E,SAAS,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAC,2LAAwB;IAC5E,eAAe,kLAAC,CACb,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,QAAQ,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAC,2LAAwB;IAC3E,kBAAkB,kLAAC,CAChB,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,UAAU,kLAAC,CACR,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,mBAAmB,kLAAC,CACjB,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,6BAA6B,kLAAC,CAC3B,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,gBAAgB,kLAAC,CACd,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,gBAAgB,kLAAC,CACd,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,mBAAmB,kLAAC,CACjB,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,oBAAoB;IACpB,aAAa,kLAAC,CACX,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,gBAAgB,kLAAC,CACd,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,8BAA8B,kLAAC,CAC5B,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,+BAA+B;IAC/B,gBAAgB,kLAAC,CACd,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,mBAAmB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACjD,8BAA8B,kLAAC,CAC5B,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,6BAA6B,kLAAC,CAC3B,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,yBAAyB,kLAAC,CACvB,MAAM,CAAC,kLAAC,CAAC,MAAM,IAAI,kLAAC,CAAC,MAAM,IAC3B,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,yBAAyB,kLAAC,CACvB,MAAM,CAAC,kLAAC,CAAC,MAAM,IAAI,kLAAC,CAAC,MAAM,IAC3B,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,oBAAoB,kLAAC,CAClB,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;AACvC;AAEO,MAAM,yBAAyB,kLAAC,CAAC,MAAM,CAAC;IAC7C,SAAS,kLAAC,CAAC,KAAK,CAAC;IACjB,UAAU,kLAAC,CAAC,MAAM,CAAC;QACjB,YAAY,kLAAC,CAAC,MAAM;QACpB,UAAU,kLAAC,CAAC,MAAM;QAClB,MAAM,kLAAC,CAAC,MAAM,CAAC;YACb,OAAO,kLAAC,CAAC,OAAO;YAChB,UAAU,kLAAC,CAAC,OAAO;YACnB,QAAQ,kLAAC,CAAC,OAAO;QACnB;QACA,QAAQ,kLAAC,CAAC,MAAM;QAChB,SAAS,kLAAC,CAAC,OAAO;QAClB,OAAO,kLAAC,CAAC,OAAO;IAClB;IACA,MAAM,kLAAC,CAAC,OAAO;IACf,eAAe,kLAAC,CAAC,MAAM;IACvB,YAAY,kLAAC,CAAC,MAAM;IACpB,OAAO,kLAAC,CAAC,OAAO;IAChB,MAAM,kLAAC,CAAC,MAAM,CAAC;QACb,OAAO,kLAAC,CAAC,OAAO;QAChB,UAAU,kLAAC,CAAC,OAAO;QACnB,QAAQ,kLAAC,CAAC,OAAO;IACnB;IACA,MAAM,kLAAC,CAAC,MAAM;IACd,QAAQ,kLAAC,CAAC,MAAM;IAChB,kBAAkB,kLAAC,CAAC,MAAM;IAC1B,OAAO,kLAAC,CAAC,OAAO;AAClB", "debugId": null}}, {"offset": {"line": 542, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/constants/module.tsx"], "sourcesContent": ["export const PANEL_WIDTH_DEFAULT = 1000\r\n\r\nexport enum modulePolarity {\r\n  POSITIVE = 'POSITIVE',\r\n  NEGATIVE = 'NEGATIVE',\r\n}\r\n\r\nexport const MODULE_POLARITY = Object.values(modulePolarity)\r\n\r\nexport type ModulePolarity =\r\n  (typeof modulePolarity)[keyof typeof modulePolarity]\r\n\r\nexport const MODULE_TYPES = [\r\n  'RECTANGULAR_BEAM',\r\n  'PILLAR',\r\n  'T_BEAM',\r\n  'SLAB',\r\n  'ANTI_OVERTURNING',\r\n  'COLUMN',\r\n  'MASONRY',\r\n  'T_BEAM',\r\n  'WOOD',\r\n  'CRM',\r\n  'FRCM_COLUMN',\r\n  'MASCHI_MURARI',\r\n] as const\r\nexport type ModuleType = (typeof MODULE_TYPES)[number]\r\n\r\nexport const MODULE_CATEGORY = [\r\n  'REINFORCED_CONCRETE',\r\n  'MASONRY',\r\n  'WOOD',\r\n  'FRCM_COLUMN',\r\n  'MASCHI_MURARI',\r\n] as const\r\nexport type ModuleCategory = (typeof MODULE_CATEGORY)[number]\r\n\r\nexport type ModuleCategoryType = {\r\n  category: ModuleCategory\r\n  types: { name: ModuleType; enabled: boolean }[]\r\n  enabled: boolean\r\n}\r\n\r\nexport const categorizedModuleTypes: ModuleCategoryType[] = [\r\n  {\r\n    category: 'REINFORCED_CONCRETE',\r\n    types: [\r\n      { name: 'RECTANGULAR_BEAM', enabled: true },\r\n      { name: 'PILLAR', enabled: true },\r\n      { name: 'T_BEAM', enabled: true },\r\n      { name: 'SLAB', enabled: true },\r\n    ],\r\n    enabled: true,\r\n  },\r\n  {\r\n    category: 'MASONRY',\r\n    types: [\r\n      { name: 'ANTI_OVERTURNING', enabled: true },\r\n      { name: 'CRM', enabled: true },\r\n      { name: 'FRCM_COLUMN', enabled: true },\r\n      { name: 'MASCHI_MURARI', enabled: true },\r\n    ],\r\n    enabled: true,\r\n  },\r\n  {\r\n    category: 'WOOD',\r\n    types: [{ name: 'WOOD', enabled: true }],\r\n    enabled: true,\r\n  },\r\n]\r\n\r\nexport const REINFORCEMENT_LAYOUTS = ['OPEN_STIRRUP', 'CLOSED_STIRRUP'] as const\r\nexport type ReinforcementLayout = (typeof REINFORCEMENT_LAYOUTS)[number]\r\n\r\nexport enum moduleGeometryExposure {\r\n  INTERNAL = 'INTERNAL',\r\n  EXTERNAL = 'EXTERNAL',\r\n  AGGRESSIVE = 'AGGRESSIVE',\r\n}\r\n\r\nexport const MODULE_GEOMETRY_EXPOSURE = Object.values(moduleGeometryExposure)\r\n\r\nexport enum moduleMaterialKnowledgeLevel {\r\n  LC1 = 'LC1',\r\n  LC2 = 'LC2',\r\n  LC3 = 'LC3',\r\n}\r\n\r\nexport type ModuleMaterialKnowledgeLevel =\r\n  (typeof moduleMaterialKnowledgeLevel)[keyof typeof moduleMaterialKnowledgeLevel]\r\n\r\nexport const moduleMaterialKnowledgeLevelValues: Record<\r\n  ModuleMaterialKnowledgeLevel,\r\n  number\r\n> = {\r\n  LC1: 1.35,\r\n  LC2: 1.2,\r\n  LC3: 1,\r\n}\r\n\r\nexport const MODULE_MATERIAL_KNOWLEDGE_LEVEL = Object.values(\r\n  moduleMaterialKnowledgeLevel,\r\n)\r\n\r\nexport enum moduleMaterialClass {\r\n  DUCTILE = 'DUCTILE',\r\n  BRITTLE = 'BRITTLE',\r\n}\r\n\r\nexport type ModuleMaterialClass =\r\n  (typeof moduleMaterialClass)[keyof typeof moduleMaterialClass]\r\n\r\nexport const MODULE_MATERIAL_CLASS = Object.values(moduleMaterialClass)\r\n\r\nexport enum SUBSOIL_CATEGORY {\r\n  A = 'A',\r\n  B = 'B',\r\n  C = 'C',\r\n  D = 'D',\r\n  E = 'E',\r\n}\r\n\r\n// coefficient Ss\r\nexport const ssCoefficientValues: Record<SUBSOIL_CATEGORY, number> = {\r\n  A: 1,\r\n  B: 1.2,\r\n  C: 1.41,\r\n  D: 1.68,\r\n  E: 1.47,\r\n}\r\n\r\nexport const MODULE_SUBSOIL_CATEGORY = Object.values(SUBSOIL_CATEGORY)\r\n\r\nexport enum TOPOGRAPHIC_CATEGORY {\r\n  T1 = 'T1',\r\n  T2 = 'T2',\r\n  T3 = 'T3',\r\n  T4 = 'T4',\r\n}\r\n\r\n// coefficient St\r\nexport const topographicCoefficientValues: Record<\r\n  TOPOGRAPHIC_CATEGORY,\r\n  number\r\n> = {\r\n  T1: 1,\r\n  T2: 1.2,\r\n  T3: 1.2,\r\n  T4: 1.4,\r\n}\r\nexport const MODULE_TOPOGRAPHIC_CATEGORY = Object.values(TOPOGRAPHIC_CATEGORY)\r\n\r\nexport enum BUILDING_TYPE {\r\n  MASONRY = 'MASONRY',\r\n  REINFORCED_CONCRETE = 'REINFORCED_CONCRETE',\r\n}\r\n\r\nexport const MODULE_BUILDING_TYPE = Object.values(BUILDING_TYPE)\r\n\r\nexport enum INFILL_WALL_TOPOLOGY {\r\n  SINGLE = 'SINGLE',\r\n  DOUBLE = 'DOUBLE',\r\n}\r\n\r\nexport const MODULE_INFILL_WALL_TOPOLOGY = Object.values(INFILL_WALL_TOPOLOGY)\r\n\r\nexport enum FACING_MATERIAL {\r\n  BRICK = 'BRICK',\r\n  TUFF = 'TUFF',\r\n  STONE = 'STONE',\r\n}\r\n\r\nexport const MODULE_FACING_MATERIAL = Object.values(FACING_MATERIAL)\r\n\r\nexport enum executionClass {\r\n  ONE = 'ONE',\r\n  TWO = 'TWO',\r\n}\r\n\r\nexport const MODULE_EXECUTION_CLASS = Object.values(executionClass)\r\n\r\nexport enum loadResistingCategory {\r\n  MASONRY_CAT_I_AND_MORTAR_WITH_GUARANTEED_PERFORMANCE = 'MASONRY_CAT_I_AND_MORTAR_WITH_GUARANTEED_PERFORMANCE',\r\n  MASONRY_CAT_I_AND_MORTAR_WITH_PRESCRIBED_PERFORMANCE = 'MASONRY_CAT_I_AND_MORTAR_WITH_PRESCRIBED_PERFORMANCE',\r\n  MASONRY_CAT_II_AND_ANY_TYPE_OF_MORTAR = 'MASONRY_CAT_II_AND_ANY_TYPE_OF_MORTAR',\r\n}\r\n\r\nexport const MODULE_LOAD_RESISTING_CATEGORY = Object.values(\r\n  loadResistingCategory,\r\n)\r\n\r\nexport const masonryStrengthSafetyFactorMapping: Record<\r\n  string,\r\n  Record<string, number>\r\n> = {\r\n  [loadResistingCategory.MASONRY_CAT_I_AND_MORTAR_WITH_GUARANTEED_PERFORMANCE]:\r\n    {\r\n      [executionClass.ONE]: 2,\r\n      [executionClass.TWO]: 2.5,\r\n    },\r\n  [loadResistingCategory.MASONRY_CAT_I_AND_MORTAR_WITH_PRESCRIBED_PERFORMANCE]:\r\n    {\r\n      [executionClass.ONE]: 2.2,\r\n      [executionClass.TWO]: 2.7,\r\n    },\r\n  [loadResistingCategory.MASONRY_CAT_II_AND_ANY_TYPE_OF_MORTAR]: {\r\n    [executionClass.ONE]: 2.5,\r\n    [executionClass.TWO]: 3,\r\n  },\r\n}\r\n\r\nexport enum CRM_MASONRY_TYPE {\r\n  MURATURA_IN_PIETRAME_DISORDINATA = 'MURATURA_IN_PIETRAME_DISORDINATA',\r\n  MURATURA_A_CONCI_SBOZZATI = 'MURATURA_A_CONCI_SBOZZATI',\r\n  MURATURA_IN_PIETRA_A_SPACCO = 'MURATURA_IN_PIETRA_A_SPACCO',\r\n  MURATURA_IRREGOLARE_DI_PIETRA_TENERA = 'MURATURA_IRREGOLARE_DI_PIETRA_TENERA',\r\n  MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA = 'MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA',\r\n  MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI = 'MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI',\r\n  MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE = 'MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE',\r\n  MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA = 'MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA',\r\n}\r\n\r\nexport const MODULE_CRM_MASONRY_TYPE = Object.values(CRM_MASONRY_TYPE)\r\n\r\nexport enum ENHANCEMENT_CHARACTERISTICS {\r\n  MALTA_BUONA = 'MALTA_BUONA',\r\n  NON_PRESENTI = 'NON_PRESENTI',\r\n}\r\n\r\nexport const MODULE_ENHANCEMENT_CHARACTERISTICS = Object.values(\r\n  ENHANCEMENT_CHARACTERISTICS,\r\n)\r\n\r\n// corrective Coefficient, depend on enhancement characteristics + masonry type\r\n// Coefficienti correttivi\tMalta buona\tRicorsi o listature\tConnessione trasversale\t - non presenti -\r\n//\r\n// Muratura in pietrame disordinata\t1.5\t1.3\t1.5\t1\r\n// Muratura a conci sbozzati con paramenti di spessore disomodeneo\t1.4\t1.2\t1.5\t1\r\n// Muratura in pietra a spacco con buona tessitura\t1.3\t1.1\t1.3\t1\r\n// Muratura irregolare di pietra tenera\t1.5\t1.2\t1.3\t1\r\n// Muratura a conci regolari di pietra tenera\t1.6\t0\t1.2\t1\r\n// Muratura a blocchi lapidei squadrati\t1.2\t0\t1.2\t1\r\n// Muratura in mattoni pieni e malta di calce\t0\t0\t1.3\t1\r\n// Muratura in mattoni semipieni con malta cementizia\t1.2\t0\t0\t1 0\r\nexport const correctiveCoefficientValues: Record<\r\n  ENHANCEMENT_CHARACTERISTICS,\r\n  Record<CRM_MASONRY_TYPE, number>\r\n> = {\r\n  [ENHANCEMENT_CHARACTERISTICS.MALTA_BUONA]: {\r\n    [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: 1.5,\r\n    [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: 1.4,\r\n    [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: 1.3,\r\n    [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: 1.5,\r\n    [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: 1.6,\r\n    [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: 1.2,\r\n    [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: 0,\r\n    [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: 1.2,\r\n  },\r\n  [ENHANCEMENT_CHARACTERISTICS.NON_PRESENTI]: {\r\n    [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: 1,\r\n    [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: 1,\r\n    [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: 1,\r\n    [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: 1,\r\n    [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: 1,\r\n    [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: 1,\r\n    [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: 1,\r\n    [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: 1,\r\n  },\r\n}\r\n\r\n// averageCompressiveStrength values in MPa, depend on masonry type + material knowledge level:\r\n//\r\n// \tTipologia muratura\tfm [MPa]                                    LC1\tLC2\tLC3\r\n// Muratura in pietrame disordinata\t                                1.00\t1.50\t2.00\r\n// Muratura a conci sbozzati con paramenti di spessore disomodeneo\t2.00\t2.00\t2.00\r\n// Muratura in pietra a spacco con buona tessitura\t                2.60\t3.20\t3.80\r\n// Muratura irregolare di pietra tenera\t                            1.40\t1.80\t2.20\r\n// Muratura a conci regolari di pietra tenera\t                      2.00\t2.60\t3.20\r\n// Muratura a blocchi lapidei squadrati\t                            5.80\t7.00\t8.20\r\n// Muratura in mattoni pieni e malta di calce\t                      2.60\t3.45\t4.30\r\n// Muratura in mattoni semipieni con malta cementizia\t              5.00\t6.50\t8.00\r\nexport const averageCompressiveStrengthValues: Record<\r\n  CRM_MASONRY_TYPE,\r\n  Record<ModuleMaterialKnowledgeLevel, number>\r\n> = {\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: {\r\n    LC1: 1.0,\r\n    LC2: 1.5,\r\n    LC3: 2.0,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: {\r\n    LC1: 2.0,\r\n    LC2: 2.0,\r\n    LC3: 2.0,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: {\r\n    LC1: 2.6,\r\n    LC2: 3.2,\r\n    LC3: 3.8,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: {\r\n    LC1: 1.4,\r\n    LC2: 1.8,\r\n    LC3: 2.2,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: {\r\n    LC1: 2.0,\r\n    LC2: 2.6,\r\n    LC3: 3.2,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: {\r\n    LC1: 5.8,\r\n    LC2: 7.0,\r\n    LC3: 8.2,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: {\r\n    LC1: 2.6,\r\n    LC2: 3.45,\r\n    LC3: 4.3,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: {\r\n    LC1: 5.0,\r\n    LC2: 6.5,\r\n    LC3: 8.0,\r\n  },\r\n}\r\n\r\n// averageShearStrengthRegularMasonry values in MPa, depend on masonry type + material knowledge level:\r\n// Tipologia muratura\tτ0 [MPa]\t\tLC1\tLC2\tLC3\r\n// Muratura in pietrame disordinata\t0.018\t0.025\t0.032\r\n// Muratura a conci sbozzati con paramenti di spessore disomodeneo\t0.035\t0.043\t0.051\r\n// Muratura in pietra a spacco con buona tessitura\t0.056\t0.065\t0.074\r\n// Muratura irregolare di pietra tenera\t0.028\t0.035\t0.042\r\n// Muratura a conci regolari di pietra tenera\t0.040\t0.060\t0.080\r\n// Muratura a blocchi lapidei squadrati\t0.090\t0.105\t0.120\r\n// Muratura in mattoni pieni e malta di calce\t0.050\t0.090\t0.130\r\n// Muratura in mattoni semipieni con malta cementizia\t0.080\t0.125\t0.170\r\nexport const averageShearStrengthRegularMasonryValues: Record<\r\n  CRM_MASONRY_TYPE,\r\n  Record<ModuleMaterialKnowledgeLevel, number>\r\n> = {\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: {\r\n    LC1: 0.018,\r\n    LC2: 0.025,\r\n    LC3: 0.032,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: {\r\n    LC1: 0.035,\r\n    LC2: 0.043,\r\n    LC3: 0.051,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: {\r\n    LC1: 0.056,\r\n    LC2: 0.065,\r\n    LC3: 0.074,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: {\r\n    LC1: 0.028,\r\n    LC2: 0.035,\r\n    LC3: 0.042,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: {\r\n    LC1: 0.04,\r\n    LC2: 0.06,\r\n    LC3: 0.08,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: {\r\n    LC1: 0.09,\r\n    LC2: 0.105,\r\n    LC3: 0.12,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: {\r\n    LC1: 0.05,\r\n    LC2: 0.09,\r\n    LC3: 0.13,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: {\r\n    LC1: 0.08,\r\n    LC2: 0.125,\r\n    LC3: 0.17,\r\n  },\r\n}\r\n\r\n// averageShearStrengthIrregularMasonry values in MPa, depend on masonry type + material knowledge level:\r\n//Tipologia muratura\tfv0 [MPa]\r\n// \tLC1\tLC2\tLC3\r\n// Muratura in pietrame disordinata\t0.000\t0.000\t0.000\r\n// Muratura a conci sbozzati con paramenti di spessore disomodeneo\t0.000\t0.000\t0.000\r\n// Muratura in pietra a spacco con buona tessitura\t0.000\t0.000\t0.000\r\n// Muratura irregolare di pietra tenera\t0.000\t0.000\t0.000\r\n// Muratura a conci regolari di pietra tenera\t0.100\t0.145\t0.190\r\n// Muratura a blocchi lapidei squadrati\t0.180\t0.230\t0.280\r\n// Muratura in mattoni pieni e malta di calce\t0.130\t0.200\t0.270\r\n// Muratura in mattoni semipieni con malta cementizia\t0.200\t0.280\t0.360\r\nexport const averageShearStrengthIrregularMasonryValues: Record<\r\n  CRM_MASONRY_TYPE,\r\n  Record<ModuleMaterialKnowledgeLevel, number>\r\n> = {\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: {\r\n    LC1: 0.0,\r\n    LC2: 0.0,\r\n    LC3: 0.0,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: {\r\n    LC1: 0.0,\r\n    LC2: 0.0,\r\n    LC3: 0.0,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: {\r\n    LC1: 0.0,\r\n    LC2: 0.0,\r\n    LC3: 0.0,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: {\r\n    LC1: 0.0,\r\n    LC2: 0.0,\r\n    LC3: 0.0,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: {\r\n    LC1: 0.1,\r\n    LC2: 0.145,\r\n    LC3: 0.19,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: {\r\n    LC1: 0.18,\r\n    LC2: 0.23,\r\n    LC3: 0.28,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: {\r\n    LC1: 0.13,\r\n    LC2: 0.2,\r\n    LC3: 0.27,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: {\r\n    LC1: 0.2,\r\n    LC2: 0.28,\r\n    LC3: 0.36,\r\n  },\r\n}\r\n\r\n// averageNormalElasticModulus values in MPa, depend on masonry type + material knowledge level:\r\n// Tipologia muratura\tE [MPa]\r\n// \tLC1\tLC2\tLC3\r\n// Muratura in pietrame disordinata\t870\t870\t870\r\n// Muratura a conci sbozzati con paramenti di spessore disomodeneo\t1230\t1230\t1230\r\n// Muratura in pietra a spacco con buona tessitura\t1740\t1740\t1740\r\n// Muratura irregolare di pietra tenera\t1080\t1080\t1080\r\n// Muratura a conci regolari di pietra tenera\t1410\t1410\t1410\r\n// Muratura a blocchi lapidei squadrati\t2850\t2850\t2850\r\n// Muratura in mattoni pieni e malta di calce\t1500\t1500\t1500\r\n// Muratura in mattoni semipieni con malta cementizia\t4550\t4550\t4550\r\nexport const averageNormalElasticityModulusValues: Record<\r\n  CRM_MASONRY_TYPE,\r\n  Record<ModuleMaterialKnowledgeLevel, number>\r\n> = {\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: {\r\n    LC1: 870,\r\n    LC2: 870,\r\n    LC3: 870,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: {\r\n    LC1: 1230,\r\n    LC2: 1230,\r\n    LC3: 1230,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: {\r\n    LC1: 1740,\r\n    LC2: 1740,\r\n    LC3: 1740,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: {\r\n    LC1: 1080,\r\n    LC2: 1080,\r\n    LC3: 1080,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: {\r\n    LC1: 1410,\r\n    LC2: 1410,\r\n    LC3: 1410,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: {\r\n    LC1: 2850,\r\n    LC2: 2850,\r\n    LC3: 2850,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: {\r\n    LC1: 1500,\r\n    LC2: 1500,\r\n    LC3: 1500,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: {\r\n    LC1: 4550,\r\n    LC2: 4550,\r\n    LC3: 4550,\r\n  },\r\n}\r\n\r\n// averageShearElasticityModulus values in MPa, depend on masonry type + material knowledge level:\r\n// Tipologia muratura\tG [MPa]\r\n// \tLC1\tLC2\tLC3\r\n// Muratura in pietrame disordinata\t290\t290\t290\r\n// Muratura a conci sbozzati con paramenti di spessore disomodeneo\t410\t410\t410\r\n// Muratura in pietra a spacco con buona tessitura\t580\t580\t580\r\n// Muratura irregolare di pietra tenera\t360\t360\t360\r\n// Muratura a conci regolari di pietra tenera\t450\t450\t450\r\n// Muratura a blocchi lapidei squadrati\t950\t950\t950\r\n// Muratura in mattoni pieni e malta di calce\t500\t500\t500\r\n// Muratura in mattoni semipieni con malta cementizia\t1137.5\t1137.5\t1137.5\r\nexport const averageShearElasticityModulusValues: Record<\r\n  CRM_MASONRY_TYPE,\r\n  Record<ModuleMaterialKnowledgeLevel, number>\r\n> = {\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: {\r\n    LC1: 290,\r\n    LC2: 290,\r\n    LC3: 290,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: {\r\n    LC1: 410,\r\n    LC2: 410,\r\n    LC3: 410,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: {\r\n    LC1: 580,\r\n    LC2: 580,\r\n    LC3: 580,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: {\r\n    LC1: 360,\r\n    LC2: 360,\r\n    LC3: 360,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: {\r\n    LC1: 450,\r\n    LC2: 450,\r\n    LC3: 450,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: {\r\n    LC1: 950,\r\n    LC2: 950,\r\n    LC3: 950,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: {\r\n    LC1: 500,\r\n    LC2: 500,\r\n    LC3: 500,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: {\r\n    LC1: 1137.5,\r\n    LC2: 1137.5,\r\n    LC3: 1137.5,\r\n  },\r\n}\r\n\r\n// postInterventionFirstSideReinforcedPlasterCoefficient values, depend on masonry type:\r\nexport const postInterventionFirstSideReinforcedPlasterCoefficientValues: Record<\r\n  CRM_MASONRY_TYPE,\r\n  number\r\n> = {\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: 2.5,\r\n  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: 2,\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: 1.5,\r\n  [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: 1.7,\r\n  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: 1.5,\r\n  [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: 1.2,\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: 1.5,\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: 1.3,\r\n}\r\n\r\n// bindingCoefficient values, depend on masonry type:\r\nexport const bindingCoefficientValues: Record<CRM_MASONRY_TYPE, number> = {\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: 2,\r\n  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: 1.7,\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: 1.5,\r\n  [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: 1.4,\r\n  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: 1.2,\r\n  [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: 1.2,\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: 1.2,\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: 0,\r\n}\r\n\r\n// maxAmplficationCoefficient values, depend on masonry type:\r\nexport const maxAmplficationCoefficientValues: Record<\r\n  CRM_MASONRY_TYPE,\r\n  number\r\n> = {\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: 3.5,\r\n  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: 3,\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: 2.4,\r\n  [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: 2,\r\n  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: 1.8,\r\n  [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: 1.4,\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: 1.8,\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: 1.3,\r\n}\r\n\r\n// specificWeight values in kN/m3, depend on masonry type:\r\n// Tipologia muratura\tγ [kN/m3]\r\n// Muratura in pietrame disordinata\t19\r\n// Muratura a conci sbozzati con paramenti di spessore disomodeneo\t20\r\n// Muratura in pietra a spacco con buona tessitura\t21\r\n// Muratura irregolare di pietra tenera\t14.5\r\n// Muratura a conci regolari di pietra tenera\t14.5\r\n// Muratura a blocchi lapidei squadrati\t22\r\n// Muratura in mattoni pieni e malta di calce\t18\r\n// Muratura in mattoni semipieni con malta cementizia\t15\r\nexport const specificWeightValues: Record<CRM_MASONRY_TYPE, number> = {\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: 19,\r\n  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: 20,\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: 21,\r\n  [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: 14.5,\r\n  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: 14.5,\r\n  [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: 22,\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: 18,\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: 15,\r\n}\r\n\r\nexport enum REINFORCEMENT_APPLICATION_TYPE {\r\n  APPLICAZIONE_SU_ENTRAMBI_I_LATI = 'APPLICAZIONE_SU_ENTRAMBI_I_LATI',\r\n  APPLICAZIONE_SU_UN_SOLO_LATO = 'APPLICAZIONE_SU_UN_SOLO_LATO',\r\n}\r\n\r\nexport const MODULE_REINFORCEMENT_APPLICATION_TYPE = Object.values(\r\n  REINFORCEMENT_APPLICATION_TYPE,\r\n)\r\n\r\nexport enum BINDER_MIXTURE_INJECTIONS {\r\n  YES = 'YES',\r\n  NO = 'NO',\r\n}\r\n\r\nexport const MODULE_BINDER_MIXTURE_INJECTIONS = Object.values(\r\n  BINDER_MIXTURE_INJECTIONS,\r\n)\r\n\r\nexport enum FRM_GEOMETRY_TOPOLOGY {\r\n  RECTANGULAR = 'RECTANGULAR',\r\n  CIRCULAR = 'CIRCULAR',\r\n}\r\n\r\nexport const MODULE_FRM_GEOMETRY_TOPOLOGY = Object.values(FRM_GEOMETRY_TOPOLOGY)\r\n\r\n// characteristicCompressiveStrength values in MPa, depend on masonry type + custom (for user defined):\r\nexport const characteristicCompressiveStrengthValues: Record<\r\n  CRM_MASONRY_TYPE | 'CUSTOM',\r\n  { min: number; max: number }\r\n> = {\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: { min: 1, max: 2 },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: { min: 2, max: 2 },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: { min: 2.6, max: 3.8 },\r\n  [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: {\r\n    min: 1.4,\r\n    max: 2.2,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: {\r\n    min: 2,\r\n    max: 3.2,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: {\r\n    min: 5.8,\r\n    max: 8.2,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: {\r\n    min: 2.6,\r\n    max: 4.3,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: {\r\n    min: 5,\r\n    max: 8,\r\n  },\r\n  CUSTOM: { min: 5, max: 5 },\r\n}\r\n\r\n// characteristicShearStrength values in MPa, depend on masonry type + custom (for user defined):\r\nexport const characteristicShearStrengthValues: Record<\r\n  CRM_MASONRY_TYPE | 'CUSTOM',\r\n  { min: number; max: number }\r\n> = {\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: {\r\n    min: 0.018,\r\n    max: 0.032,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: { min: 0.035, max: 0.051 },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: { min: 0.056, max: 0.074 },\r\n  [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: {\r\n    min: 0.028,\r\n    max: 0.042,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: {\r\n    min: 0.04,\r\n    max: 0.06,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: {\r\n    min: 0.09,\r\n    max: 0.12,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: {\r\n    min: 0.05,\r\n    max: 0.13,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: {\r\n    min: 0.08,\r\n    max: 0.17,\r\n  },\r\n  CUSTOM: { min: 0.05, max: 0.05 },\r\n}\r\n\r\n// characteristicNormalElasticModulus values in MPa, depend on masonry type + custom (for user defined):\r\n// Em [MPa]\r\nexport const characteristicNormalElasticityModulusValues: Record<\r\n  CRM_MASONRY_TYPE | 'CUSTOM',\r\n  { min: number; max: number }\r\n> = {\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: { min: 690, max: 1050 },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: { min: 1020, max: 1440 },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: { min: 1500, max: 1980 },\r\n  [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: {\r\n    min: 900,\r\n    max: 1260,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: {\r\n    min: 1200,\r\n    max: 1620,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: {\r\n    min: 2400,\r\n    max: 3300,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: {\r\n    min: 1200,\r\n    max: 1800,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: {\r\n    min: 3500,\r\n    max: 5600,\r\n  },\r\n  CUSTOM: { min: 4550, max: 4550 },\r\n}\r\n\r\n// characteristicShearElasticModulus values in MPa, depend on masonry type + custom (for user defined):\r\n// Gm [MPa]\r\nexport const characteristicShearElasticityModulusValues: Record<\r\n  CRM_MASONRY_TYPE | 'CUSTOM',\r\n  { min: number; max: number }\r\n> = {\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: { min: 230, max: 350 },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: { min: 340, max: 480 },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: { min: 500, max: 600 },\r\n  [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: {\r\n    min: 300,\r\n    max: 420,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: {\r\n    min: 400,\r\n    max: 500,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: {\r\n    min: 800,\r\n    max: 1100,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: {\r\n    min: 400,\r\n    max: 600,\r\n  },\r\n  [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: {\r\n    min: 875,\r\n    max: 1400,\r\n  },\r\n  CUSTOM: { min: 1137.5, max: 1137.5 },\r\n}\r\n\r\n// masonry density:\r\n// w [kN/m3]\r\nexport const masonryDensityValues: Record<CRM_MASONRY_TYPE | 'CUSTOM', number> =\r\n  {\r\n    [CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA]: 19,\r\n    [CRM_MASONRY_TYPE.MURATURA_A_CONCI_SBOZZATI]: 20,\r\n    [CRM_MASONRY_TYPE.MURATURA_IN_PIETRA_A_SPACCO]: 21,\r\n    [CRM_MASONRY_TYPE.MURATURA_IRREGOLARE_DI_PIETRA_TENERA]: 16,\r\n    [CRM_MASONRY_TYPE.MURATURA_A_CONCI_REGOLARI_DI_PIETRA_TENERA]: 16,\r\n    [CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI]: 22,\r\n    [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_PIENI_E_MALTA_DI_CALCE]: 18,\r\n    [CRM_MASONRY_TYPE.MURATURA_IN_MATTONI_SEMIPIENI_CON_MALTA_CEMENTIZIA]: 15,\r\n    CUSTOM: 18,\r\n  }\r\n\r\nexport enum REINFORCEMENT_ARRANGEMENT {\r\n  CONTINUE = 'CONTINUE',\r\n  DISCONTINUE = 'DISCONTINUE',\r\n}\r\n\r\nexport const MODULE_REINFORCEMENT_ARRANGEMENT = Object.values(\r\n  REINFORCEMENT_ARRANGEMENT,\r\n)\r\n\r\nexport enum REINFORCEMENT_FAILURE_MODE {\r\n  DISTACCO_INTERMEDIO = 'DISTACCO_INTERMEDIO',\r\n  DISTACCO_DI_ESTREMITA = 'DISTACCO_DI_ESTREMITA',\r\n}\r\n\r\nexport const MODULE_REINFORCEMENT_FAILURE_MODE = Object.values(\r\n  REINFORCEMENT_FAILURE_MODE,\r\n)\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,MAAM,sBAAsB;AAE5B,IAAA,AAAK,wCAAA;;;WAAA;;AAKL,MAAM,kBAAkB,OAAO,MAAM,CAAC;AAKtC,MAAM,eAAe;IAC1B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAGM,MAAM,kBAAkB;IAC7B;IACA;IACA;IACA;IACA;CACD;AASM,MAAM,yBAA+C;IAC1D;QACE,UAAU;QACV,OAAO;YACL;gBAAE,MAAM;gBAAoB,SAAS;YAAK;YAC1C;gBAAE,MAAM;gBAAU,SAAS;YAAK;YAChC;gBAAE,MAAM;gBAAU,SAAS;YAAK;YAChC;gBAAE,MAAM;gBAAQ,SAAS;YAAK;SAC/B;QACD,SAAS;IACX;IACA;QACE,UAAU;QACV,OAAO;YACL;gBAAE,MAAM;gBAAoB,SAAS;YAAK;YAC1C;gBAAE,MAAM;gBAAO,SAAS;YAAK;YAC7B;gBAAE,MAAM;gBAAe,SAAS;YAAK;YACrC;gBAAE,MAAM;gBAAiB,SAAS;YAAK;SACxC;QACD,SAAS;IACX;IACA;QACE,UAAU;QACV,OAAO;YAAC;gBAAE,MAAM;gBAAQ,SAAS;YAAK;SAAE;QACxC,SAAS;IACX;CACD;AAEM,MAAM,wBAAwB;IAAC;IAAgB;CAAiB;AAGhE,IAAA,AAAK,gDAAA;;;;WAAA;;AAML,MAAM,2BAA2B,OAAO,MAAM,CAAC;AAE/C,IAAA,AAAK,sDAAA;;;;WAAA;;AASL,MAAM,qCAGT;IACF,KAAK;IACL,KAAK;IACL,KAAK;AACP;AAEO,MAAM,kCAAkC,OAAO,MAAM,CAC1D;AAGK,IAAA,AAAK,6CAAA;;;WAAA;;AAQL,MAAM,wBAAwB,OAAO,MAAM,CAAC;AAE5C,IAAA,AAAK,0CAAA;;;;;;WAAA;;AASL,MAAM,sBAAwD;IACnE,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;AACL;AAEO,MAAM,0BAA0B,OAAO,MAAM,CAAC;AAE9C,IAAA,AAAK,8CAAA;;;;;WAAA;;AAQL,MAAM,+BAGT;IACF,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN;AACO,MAAM,8BAA8B,OAAO,MAAM,CAAC;AAElD,IAAA,AAAK,uCAAA;;;WAAA;;AAKL,MAAM,uBAAuB,OAAO,MAAM,CAAC;AAE3C,IAAA,AAAK,8CAAA;;;WAAA;;AAKL,MAAM,8BAA8B,OAAO,MAAM,CAAC;AAElD,IAAA,AAAK,yCAAA;;;;WAAA;;AAML,MAAM,yBAAyB,OAAO,MAAM,CAAC;AAE7C,IAAA,AAAK,wCAAA;;;WAAA;;AAKL,MAAM,yBAAyB,OAAO,MAAM,CAAC;AAE7C,IAAA,AAAK,+CAAA;;;;WAAA;;AAML,MAAM,iCAAiC,OAAO,MAAM,CACzD;AAGK,MAAM,qCAGT;IACF,wDAA4E,EAC1E;QACE,OAAoB,EAAE;QACtB,OAAoB,EAAE;IACxB;IACF,wDAA4E,EAC1E;QACE,OAAoB,EAAE;QACtB,OAAoB,EAAE;IACxB;IACF,yCAA6D,EAAE;QAC7D,OAAoB,EAAE;QACtB,OAAoB,EAAE;IACxB;AACF;AAEO,IAAA,AAAK,0CAAA;;;;;;;;;WAAA;;AAWL,MAAM,0BAA0B,OAAO,MAAM,CAAC;AAE9C,IAAA,AAAK,qDAAA;;;WAAA;;AAKL,MAAM,qCAAqC,OAAO,MAAM,CAC7D;AAcK,MAAM,8BAGT;IACF,eAAyC,EAAE;QACzC,oCAAmD,EAAE;QACrD,6BAA4C,EAAE;QAC9C,+BAA8C,EAAE;QAChD,wCAAuD,EAAE;QACzD,8CAA6D,EAAE;QAC/D,wCAAuD,EAAE;QACzD,8CAA6D,EAAE;QAC/D,sDAAqE,EAAE;IACzE;IACA,gBAA0C,EAAE;QAC1C,oCAAmD,EAAE;QACrD,6BAA4C,EAAE;QAC9C,+BAA8C,EAAE;QAChD,wCAAuD,EAAE;QACzD,8CAA6D,EAAE;QAC/D,wCAAuD,EAAE;QACzD,8CAA6D,EAAE;QAC/D,sDAAqE,EAAE;IACzE;AACF;AAaO,MAAM,mCAGT;IACF,oCAAmD,EAAE;QACnD,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,6BAA4C,EAAE;QAC5C,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,+BAA8C,EAAE;QAC9C,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,wCAAuD,EAAE;QACvD,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,8CAA6D,EAAE;QAC7D,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,wCAAuD,EAAE;QACvD,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,8CAA6D,EAAE;QAC7D,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,sDAAqE,EAAE;QACrE,KAAK;QACL,KAAK;QACL,KAAK;IACP;AACF;AAYO,MAAM,2CAGT;IACF,oCAAmD,EAAE;QACnD,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,6BAA4C,EAAE;QAC5C,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,+BAA8C,EAAE;QAC9C,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,wCAAuD,EAAE;QACvD,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,8CAA6D,EAAE;QAC7D,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,wCAAuD,EAAE;QACvD,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,8CAA6D,EAAE;QAC7D,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,sDAAqE,EAAE;QACrE,KAAK;QACL,KAAK;QACL,KAAK;IACP;AACF;AAaO,MAAM,6CAGT;IACF,oCAAmD,EAAE;QACnD,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,6BAA4C,EAAE;QAC5C,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,+BAA8C,EAAE;QAC9C,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,wCAAuD,EAAE;QACvD,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,8CAA6D,EAAE;QAC7D,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,wCAAuD,EAAE;QACvD,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,8CAA6D,EAAE;QAC7D,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,sDAAqE,EAAE;QACrE,KAAK;QACL,KAAK;QACL,KAAK;IACP;AACF;AAaO,MAAM,uCAGT;IACF,oCAAmD,EAAE;QACnD,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,6BAA4C,EAAE;QAC5C,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,+BAA8C,EAAE;QAC9C,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,wCAAuD,EAAE;QACvD,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,8CAA6D,EAAE;QAC7D,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,wCAAuD,EAAE;QACvD,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,8CAA6D,EAAE;QAC7D,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,sDAAqE,EAAE;QACrE,KAAK;QACL,KAAK;QACL,KAAK;IACP;AACF;AAaO,MAAM,sCAGT;IACF,oCAAmD,EAAE;QACnD,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,6BAA4C,EAAE;QAC5C,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,+BAA8C,EAAE;QAC9C,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,wCAAuD,EAAE;QACvD,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,8CAA6D,EAAE;QAC7D,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,wCAAuD,EAAE;QACvD,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,8CAA6D,EAAE;QAC7D,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,sDAAqE,EAAE;QACrE,KAAK;QACL,KAAK;QACL,KAAK;IACP;AACF;AAGO,MAAM,8DAGT;IACF,oCAAmD,EAAE;IACrD,6BAA4C,EAAE;IAC9C,+BAA8C,EAAE;IAChD,wCAAuD,EAAE;IACzD,8CAA6D,EAAE;IAC/D,wCAAuD,EAAE;IACzD,8CAA6D,EAAE;IAC/D,sDAAqE,EAAE;AACzE;AAGO,MAAM,2BAA6D;IACxE,oCAAmD,EAAE;IACrD,6BAA4C,EAAE;IAC9C,+BAA8C,EAAE;IAChD,wCAAuD,EAAE;IACzD,8CAA6D,EAAE;IAC/D,wCAAuD,EAAE;IACzD,8CAA6D,EAAE;IAC/D,sDAAqE,EAAE;AACzE;AAGO,MAAM,mCAGT;IACF,oCAAmD,EAAE;IACrD,6BAA4C,EAAE;IAC9C,+BAA8C,EAAE;IAChD,wCAAuD,EAAE;IACzD,8CAA6D,EAAE;IAC/D,wCAAuD,EAAE;IACzD,8CAA6D,EAAE;IAC/D,sDAAqE,EAAE;AACzE;AAYO,MAAM,uBAAyD;IACpE,oCAAmD,EAAE;IACrD,6BAA4C,EAAE;IAC9C,+BAA8C,EAAE;IAChD,wCAAuD,EAAE;IACzD,8CAA6D,EAAE;IAC/D,wCAAuD,EAAE;IACzD,8CAA6D,EAAE;IAC/D,sDAAqE,EAAE;AACzE;AAEO,IAAA,AAAK,wDAAA;;;WAAA;;AAKL,MAAM,wCAAwC,OAAO,MAAM,CAChE;AAGK,IAAA,AAAK,mDAAA;;;WAAA;;AAKL,MAAM,mCAAmC,OAAO,MAAM,CAC3D;AAGK,IAAA,AAAK,+CAAA;;;WAAA;;AAKL,MAAM,+BAA+B,OAAO,MAAM,CAAC;AAGnD,MAAM,0CAGT;IACF,oCAAmD,EAAE;QAAE,KAAK;QAAG,KAAK;IAAE;IACtE,6BAA4C,EAAE;QAAE,KAAK;QAAG,KAAK;IAAE;IAC/D,+BAA8C,EAAE;QAAE,KAAK;QAAK,KAAK;IAAI;IACrE,wCAAuD,EAAE;QACvD,KAAK;QACL,KAAK;IACP;IACA,8CAA6D,EAAE;QAC7D,KAAK;QACL,KAAK;IACP;IACA,wCAAuD,EAAE;QACvD,KAAK;QACL,KAAK;IACP;IACA,8CAA6D,EAAE;QAC7D,KAAK;QACL,KAAK;IACP;IACA,sDAAqE,EAAE;QACrE,KAAK;QACL,KAAK;IACP;IACA,QAAQ;QAAE,KAAK;QAAG,KAAK;IAAE;AAC3B;AAGO,MAAM,oCAGT;IACF,oCAAmD,EAAE;QACnD,KAAK;QACL,KAAK;IACP;IACA,6BAA4C,EAAE;QAAE,KAAK;QAAO,KAAK;IAAM;IACvE,+BAA8C,EAAE;QAAE,KAAK;QAAO,KAAK;IAAM;IACzE,wCAAuD,EAAE;QACvD,KAAK;QACL,KAAK;IACP;IACA,8CAA6D,EAAE;QAC7D,KAAK;QACL,KAAK;IACP;IACA,wCAAuD,EAAE;QACvD,KAAK;QACL,KAAK;IACP;IACA,8CAA6D,EAAE;QAC7D,KAAK;QACL,KAAK;IACP;IACA,sDAAqE,EAAE;QACrE,KAAK;QACL,KAAK;IACP;IACA,QAAQ;QAAE,KAAK;QAAM,KAAK;IAAK;AACjC;AAIO,MAAM,8CAGT;IACF,oCAAmD,EAAE;QAAE,KAAK;QAAK,KAAK;IAAK;IAC3E,6BAA4C,EAAE;QAAE,KAAK;QAAM,KAAK;IAAK;IACrE,+BAA8C,EAAE;QAAE,KAAK;QAAM,KAAK;IAAK;IACvE,wCAAuD,EAAE;QACvD,KAAK;QACL,KAAK;IACP;IACA,8CAA6D,EAAE;QAC7D,KAAK;QACL,KAAK;IACP;IACA,wCAAuD,EAAE;QACvD,KAAK;QACL,KAAK;IACP;IACA,8CAA6D,EAAE;QAC7D,KAAK;QACL,KAAK;IACP;IACA,sDAAqE,EAAE;QACrE,KAAK;QACL,KAAK;IACP;IACA,QAAQ;QAAE,KAAK;QAAM,KAAK;IAAK;AACjC;AAIO,MAAM,6CAGT;IACF,oCAAmD,EAAE;QAAE,KAAK;QAAK,KAAK;IAAI;IAC1E,6BAA4C,EAAE;QAAE,KAAK;QAAK,KAAK;IAAI;IACnE,+BAA8C,EAAE;QAAE,KAAK;QAAK,KAAK;IAAI;IACrE,wCAAuD,EAAE;QACvD,KAAK;QACL,KAAK;IACP;IACA,8CAA6D,EAAE;QAC7D,KAAK;QACL,KAAK;IACP;IACA,wCAAuD,EAAE;QACvD,KAAK;QACL,KAAK;IACP;IACA,8CAA6D,EAAE;QAC7D,KAAK;QACL,KAAK;IACP;IACA,sDAAqE,EAAE;QACrE,KAAK;QACL,KAAK;IACP;IACA,QAAQ;QAAE,KAAK;QAAQ,KAAK;IAAO;AACrC;AAIO,MAAM,uBACX;IACE,oCAAmD,EAAE;IACrD,6BAA4C,EAAE;IAC9C,+BAA8C,EAAE;IAChD,wCAAuD,EAAE;IACzD,8CAA6D,EAAE;IAC/D,wCAAuD,EAAE;IACzD,8CAA6D,EAAE;IAC/D,sDAAqE,EAAE;IACvE,QAAQ;AACV;AAEK,IAAA,AAAK,mDAAA;;;WAAA;;AAKL,MAAM,mCAAmC,OAAO,MAAM,CAC3D;AAGK,IAAA,AAAK,oDAAA;;;WAAA;;AAKL,MAAM,oCAAoC,OAAO,MAAM,CAC5D", "debugId": null}}, {"offset": {"line": 1340, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/types/schemas/product-form-schema.ts"], "sourcesContent": ["import { PRODUCT_FIBER_TYPE } from '@atlas/constants/product'\r\nimport { z } from 'zod'\r\n\r\nconst productCustomSchema = z.object({\r\n  id: z.string(),\r\n  sourceType: z.literal('CUSTOM'),\r\n  name: z.string().optional().nullable(),\r\n  thickness: z.number().optional().nullable(),\r\n  tensileStrength: z.number().optional().nullable(),\r\n  elasticModulus: z.number().optional().nullable(),\r\n  networkDeformation: z.number().optional().nullable(),\r\n  matrixBreakdownVoltage: z.number().optional().nullable(),\r\n  conventionalLimitVoltage: z.number().optional().nullable(),\r\n  fiberType: z.enum(PRODUCT_FIBER_TYPE),\r\n})\r\n\r\nconst productDatabaseSchema = z.object({\r\n  sourceType: z.literal('DATABASE'),\r\n  id: z.string(),\r\n  name: z.string().optional().nullable(),\r\n})\r\n\r\nexport const productFormSchema = z.discriminatedUnion('sourceType', [\r\n  productCustomSchema,\r\n  productDatabaseSchema,\r\n])\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,sBAAsB,kLAAC,CAAC,MAAM,CAAC;IACnC,IAAI,kLAAC,CAAC,MAAM;IACZ,YAAY,kLAAC,CAAC,OAAO,CAAC;IACtB,MAAM,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACpC,WAAW,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACzC,iBAAiB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC/C,gBAAgB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC9C,oBAAoB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAClD,wBAAwB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACtD,0BAA0B,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACxD,WAAW,kLAAC,CAAC,IAAI,CAAC,kJAAkB;AACtC;AAEA,MAAM,wBAAwB,kLAAC,CAAC,MAAM,CAAC;IACrC,YAAY,kLAAC,CAAC,OAAO,CAAC;IACtB,IAAI,kLAAC,CAAC,MAAM;IACZ,MAAM,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;AACtC;AAEO,MAAM,oBAAoB,kLAAC,CAAC,kBAAkB,CAAC,cAAc;IAClE;IACA;CACD", "debugId": null}}, {"offset": {"line": 1373, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/types/schemas/pillar-form.ts"], "sourcesContent": ["import {\r\n  MODULE_GEOMETRY_EXPOSURE,\r\n  MODULE_MATERIAL_KNOWLEDGE_LEVEL,\r\n} from '@atlas/constants/module'\r\nimport { productFormSchema } from '@atlas/types/schemas/product-form-schema'\r\nimport { z } from 'zod'\r\n\r\nexport const pillarGeneralSchema = z.object({\r\n  initialDeformation: z.number(),\r\n})\r\n\r\nexport const pillarGeometrySchema = z.object({\r\n  width: z.number().positive(),\r\n  height: z.number().positive(),\r\n  topConcreteCover: z.number().positive(),\r\n  bottomConcreteCover: z.number().positive(),\r\n  effectiveDepth: z.number(),\r\n  exposure: z.enum(MODULE_GEOMETRY_EXPOSURE),\r\n})\r\n\r\nexport const pillarReinforcementSchema = z.object({\r\n  top: z.object({\r\n    diameter: z.number().positive(),\r\n    quantity: z.number().positive(),\r\n    area: z.number(),\r\n  }),\r\n  bottom: z.object({\r\n    diameter: z.number().positive(),\r\n    quantity: z.number().positive(),\r\n    area: z.number(),\r\n  }),\r\n  transverse: z.object({\r\n    diameter: z.number().positive(),\r\n    legs: z.number().positive(),\r\n    area: z.number(),\r\n    stirrupSpacing: z.number().positive(),\r\n    stirrupInclination: z.number().min(-360).max(360),\r\n    cornerRadius: z.number().positive(),\r\n  }),\r\n})\r\n\r\nexport const pillarMaterialSchema = z.object({\r\n  knowledgeLevel: z.enum(MODULE_MATERIAL_KNOWLEDGE_LEVEL),\r\n  confidenceFactor: z.number().optional(),\r\n  concreteClass: z.object({\r\n    id: z.string(),\r\n    name: z.string(),\r\n    cubeCompressiveStrength: z.number(),\r\n    cylinderCompressiveStrength: z.number(),\r\n    averageCompressiveStrength: z.number(),\r\n    averageTensileStrength: z.number(),\r\n    elasticModulus: z.number(),\r\n    designCompressiveStrengthForBrittleMechanisms: z.number(),\r\n    designCompressiveStrengthForDuctileMechanisms: z.number(),\r\n  }),\r\n  steelGrade: z.object({\r\n    id: z.string(),\r\n    name: z.string(),\r\n    yieldStrength: z.number(),\r\n    tensileStrength: z.number(),\r\n    elongationPercentage: z.number(),\r\n    elasticModulus: z.number(),\r\n    designYieldStrengthForBrittleMechanisms: z.number(),\r\n    designYieldStrengthForDuctileMechanisms: z.number(),\r\n  }),\r\n})\r\n\r\nexport const pillarParamsCheckSchema = z.object({\r\n  initialDeformation: z.number(),\r\n  geometry: pillarGeometrySchema,\r\n  reinforcementBar: pillarReinforcementSchema,\r\n  materialProperties: pillarMaterialSchema,\r\n})\r\n\r\nexport const pillarFlexuralCalculationSchema = z.object({\r\n  calculationType: z.literal('FLEXURAL_VERIFY'),\r\n  input: z.object({\r\n    stripWidth: z.number(),\r\n    layersNumber: z.number(),\r\n    bendingMoment: z.number(),\r\n    product: productFormSchema,\r\n  }),\r\n})\r\n\r\nexport const pillarShearCalculationSchema = z.object({\r\n  calculationType: z.literal('SHEAR_VERIFY'),\r\n  input: z.object({\r\n    product: productFormSchema,\r\n  }),\r\n})\r\n\r\nexport type PillarGeneralFormInputs = z.infer<typeof pillarGeneralSchema>\r\nexport type PillarGeometryFormInputs = z.infer<typeof pillarGeometrySchema>\r\nexport type PillarReinforcementFormInputs = z.infer<\r\n  typeof pillarReinforcementSchema\r\n>\r\nexport type PillarMaterialFormInputs = z.infer<typeof pillarMaterialSchema>\r\nexport type PillarFlexuralCalculationInput = z.infer<\r\n  typeof pillarFlexuralCalculationSchema\r\n>\r\nexport type PillarShearCalculationInput = z.infer<\r\n  typeof pillarShearCalculationSchema\r\n>\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAIA;AACA;;;;AAEO,MAAM,sBAAsB,kLAAC,CAAC,MAAM,CAAC;IAC1C,oBAAoB,kLAAC,CAAC,MAAM;AAC9B;AAEO,MAAM,uBAAuB,kLAAC,CAAC,MAAM,CAAC;IAC3C,OAAO,kLAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,QAAQ,kLAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,kBAAkB,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACrC,qBAAqB,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACxC,gBAAgB,kLAAC,CAAC,MAAM;IACxB,UAAU,kLAAC,CAAC,IAAI,CAAC,uJAAwB;AAC3C;AAEO,MAAM,4BAA4B,kLAAC,CAAC,MAAM,CAAC;IAChD,KAAK,kLAAC,CAAC,MAAM,CAAC;QACZ,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,MAAM,kLAAC,CAAC,MAAM;IAChB;IACA,QAAQ,kLAAC,CAAC,MAAM,CAAC;QACf,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,MAAM,kLAAC,CAAC,MAAM;IAChB;IACA,YAAY,kLAAC,CAAC,MAAM,CAAC;QACnB,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,MAAM,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACzB,MAAM,kLAAC,CAAC,MAAM;QACd,gBAAgB,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACnC,oBAAoB,kLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC;QAC7C,cAAc,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACnC;AACF;AAEO,MAAM,uBAAuB,kLAAC,CAAC,MAAM,CAAC;IAC3C,gBAAgB,kLAAC,CAAC,IAAI,CAAC,8JAA+B;IACtD,kBAAkB,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACrC,eAAe,kLAAC,CAAC,MAAM,CAAC;QACtB,IAAI,kLAAC,CAAC,MAAM;QACZ,MAAM,kLAAC,CAAC,MAAM;QACd,yBAAyB,kLAAC,CAAC,MAAM;QACjC,6BAA6B,kLAAC,CAAC,MAAM;QACrC,4BAA4B,kLAAC,CAAC,MAAM;QACpC,wBAAwB,kLAAC,CAAC,MAAM;QAChC,gBAAgB,kLAAC,CAAC,MAAM;QACxB,+CAA+C,kLAAC,CAAC,MAAM;QACvD,+CAA+C,kLAAC,CAAC,MAAM;IACzD;IACA,YAAY,kLAAC,CAAC,MAAM,CAAC;QACnB,IAAI,kLAAC,CAAC,MAAM;QACZ,MAAM,kLAAC,CAAC,MAAM;QACd,eAAe,kLAAC,CAAC,MAAM;QACvB,iBAAiB,kLAAC,CAAC,MAAM;QACzB,sBAAsB,kLAAC,CAAC,MAAM;QAC9B,gBAAgB,kLAAC,CAAC,MAAM;QACxB,yCAAyC,kLAAC,CAAC,MAAM;QACjD,yCAAyC,kLAAC,CAAC,MAAM;IACnD;AACF;AAEO,MAAM,0BAA0B,kLAAC,CAAC,MAAM,CAAC;IAC9C,oBAAoB,kLAAC,CAAC,MAAM;IAC5B,UAAU;IACV,kBAAkB;IAClB,oBAAoB;AACtB;AAEO,MAAM,kCAAkC,kLAAC,CAAC,MAAM,CAAC;IACtD,iBAAiB,kLAAC,CAAC,OAAO,CAAC;IAC3B,OAAO,kLAAC,CAAC,MAAM,CAAC;QACd,YAAY,kLAAC,CAAC,MAAM;QACpB,cAAc,kLAAC,CAAC,MAAM;QACtB,eAAe,kLAAC,CAAC,MAAM;QACvB,SAAS,yKAAiB;IAC5B;AACF;AAEO,MAAM,+BAA+B,kLAAC,CAAC,MAAM,CAAC;IACnD,iBAAiB,kLAAC,CAAC,OAAO,CAAC;IAC3B,OAAO,kLAAC,CAAC,MAAM,CAAC;QACd,SAAS,yKAAiB;IAC5B;AACF", "debugId": null}}, {"offset": {"line": 1476, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/api/modules/schemas/pillar-params.ts"], "sourcesContent": ["import { nullToObjectTransform } from '@atlas/functions/zod/null-to-object-transform'\r\nimport { nullToUndefinedTransform } from '@atlas/functions/zod/null-to-undefined-transform'\r\nimport { productSchema } from '@atlas/lib/api/products/schemas/product'\r\nimport {\r\n  pillarGeometrySchema,\r\n  pillarMaterialSchema,\r\n  pillarReinforcementSchema,\r\n} from '@atlas/types/schemas/pillar-form'\r\nimport { z } from 'zod'\r\n\r\nexport const pillarParamsSchema = z.object({\r\n  initialDeformation: z\r\n    .number()\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToUndefinedTransform),\r\n  geometry: pillarGeometrySchema\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToObjectTransform),\r\n  reinforcementBar: pillarReinforcementSchema\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToObjectTransform),\r\n  materialProperties: pillarMaterialSchema\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToObjectTransform),\r\n})\r\n\r\nexport const pillarFlexuralVerifyExecutionInputSchema = z.object({\r\n  stripWidth: z.number(),\r\n  layersNumber: z.number(),\r\n  bendingMoment: z.number(),\r\n  product: productSchema,\r\n})\r\n\r\nexport const pillarFlexuralCalculationResultSchema = z.object({\r\n  resistantMoment: z.number(),\r\n  equilibrium: z.number(),\r\n  checkResult: z.boolean(),\r\n})\r\n\r\nexport const pillarShearVerifyExecutionInputSchema = z.object({\r\n  product: productSchema,\r\n})\r\n\r\nexport const pillarShearCalculationResultSchema = z.object({\r\n  frpShearContribution: z.number(),\r\n  shearCapacity: z.number(),\r\n  checkResult: z.boolean(),\r\n})\r\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;AAKA;;;;;;AAEO,MAAM,qBAAqB,kLAAC,CAAC,MAAM,CAAC;IACzC,oBAAoB,kLAAC,CAClB,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,UAAU,iKAAoB,CAC3B,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,qLAAqB;IAClC,kBAAkB,sKAAyB,CACxC,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,qLAAqB;IAClC,oBAAoB,iKAAoB,CACrC,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,qLAAqB;AACpC;AAEO,MAAM,2CAA2C,kLAAC,CAAC,MAAM,CAAC;IAC/D,YAAY,kLAAC,CAAC,MAAM;IACpB,cAAc,kLAAC,CAAC,MAAM;IACtB,eAAe,kLAAC,CAAC,MAAM;IACvB,SAAS,oKAAa;AACxB;AAEO,MAAM,wCAAwC,kLAAC,CAAC,MAAM,CAAC;IAC5D,iBAAiB,kLAAC,CAAC,MAAM;IACzB,aAAa,kLAAC,CAAC,MAAM;IACrB,aAAa,kLAAC,CAAC,OAAO;AACxB;AAEO,MAAM,wCAAwC,kLAAC,CAAC,MAAM,CAAC;IAC5D,SAAS,oKAAa;AACxB;AAEO,MAAM,qCAAqC,kLAAC,CAAC,MAAM,CAAC;IACzD,sBAAsB,kLAAC,CAAC,MAAM;IAC9B,eAAe,kLAAC,CAAC,MAAM;IACvB,aAAa,kLAAC,CAAC,OAAO;AACxB", "debugId": null}}, {"offset": {"line": 1527, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/types/schemas/rectangular-beam-from.ts"], "sourcesContent": ["import {\r\n  MODULE_GEOMETRY_EXPOSURE,\r\n  MODULE_MATERIAL_KNOWLEDGE_LEVEL,\r\n  MODULE_POLARITY,\r\n  REINFORCEMENT_LAYOUTS,\r\n} from '@atlas/constants/module'\r\nimport { productFormSchema } from '@atlas/types/schemas/product-form-schema'\r\nimport { z } from 'zod'\r\n\r\nexport const rectangularBeamGeneralSchema = z.object({\r\n  initialDeformation: z.number(),\r\n  polarity: z.enum(MODULE_POLARITY),\r\n})\r\n\r\nexport const rectangularBeamGeometrySchema = z.object({\r\n  width: z.number().positive(),\r\n  height: z.number().positive(),\r\n  topConcreteCover: z.number().positive(),\r\n  bottomConcreteCover: z.number().positive(),\r\n  effectiveDepth: z.number(),\r\n  exposure: z.enum(MODULE_GEOMETRY_EXPOSURE),\r\n})\r\n\r\nexport const rectangularBeamReinforcementSchema = z.object({\r\n  top: z.object({\r\n    diameter: z.number().positive(),\r\n    quantity: z.number().positive(),\r\n    area: z.number(),\r\n  }),\r\n  bottom: z.object({\r\n    diameter: z.number().positive(),\r\n    quantity: z.number().positive(),\r\n    area: z.number(),\r\n  }),\r\n  transverse: z.object({\r\n    diameter: z.number().positive(),\r\n    legs: z.number().positive(),\r\n    area: z.number(),\r\n    stirrupSpacing: z.number().positive(),\r\n    stirrupInclination: z.number().min(-360).max(360),\r\n    cornerRadius: z.number().positive(),\r\n  }),\r\n})\r\n\r\nexport const rectangularBeamMaterialSchema = z.object({\r\n  knowledgeLevel: z.enum(MODULE_MATERIAL_KNOWLEDGE_LEVEL),\r\n  confidenceFactor: z.number().optional(),\r\n  concreteClass: z.object({\r\n    id: z.string(),\r\n    name: z.string(),\r\n    cubeCompressiveStrength: z.number(),\r\n    cylinderCompressiveStrength: z.number(),\r\n    averageCompressiveStrength: z.number(),\r\n    averageTensileStrength: z.number(),\r\n    elasticModulus: z.number(),\r\n    designCompressiveStrengthForBrittleMechanisms: z.number(),\r\n    designCompressiveStrengthForDuctileMechanisms: z.number(),\r\n  }),\r\n  steelGrade: z.object({\r\n    id: z.string(),\r\n    name: z.string(),\r\n    yieldStrength: z.number(),\r\n    tensileStrength: z.number(),\r\n    elongationPercentage: z.number(),\r\n    elasticModulus: z.number(),\r\n    designYieldStrengthForBrittleMechanisms: z.number(),\r\n    designYieldStrengthForDuctileMechanisms: z.number(),\r\n  }),\r\n})\r\n\r\nexport const rectangularBeamFormSchema = z.object({\r\n  initialDeformation: z.number().optional(),\r\n  polarity: z.enum(MODULE_POLARITY).optional(),\r\n  geometry: rectangularBeamGeometrySchema.optional(),\r\n  reinforcementBar: rectangularBeamReinforcementSchema.optional(),\r\n  materialProperties: rectangularBeamMaterialSchema.optional(),\r\n})\r\n\r\nexport const rectangularBeamParamsCheckSchema = z.object({\r\n  initialDeformation: z.number(),\r\n  polarity: z.enum(MODULE_POLARITY),\r\n  geometry: rectangularBeamGeometrySchema,\r\n  reinforcementBar: rectangularBeamReinforcementSchema,\r\n  materialProperties: rectangularBeamMaterialSchema,\r\n})\r\n\r\nexport const rectangularBeamFlexuralCalculationSchema = z.object({\r\n  calculationType: z.literal('FLEXURAL_VERIFY'),\r\n  input: z.object({\r\n    stripWidth: z.number(),\r\n    layersNumber: z.number(),\r\n    bendingMoment: z.number(),\r\n    product: productFormSchema,\r\n  }),\r\n})\r\n\r\nexport const rectangularBeamShearCalculationSchema = z.object({\r\n  calculationType: z.literal('SHEAR_VERIFY'),\r\n  input: z.object({\r\n    reinforcementLayout: z.literal(REINFORCEMENT_LAYOUTS),\r\n    webHeight: z.number().positive(),\r\n    stripWidth: z.number().positive(),\r\n    stripSpacingAlongElementAxis: z.number().positive(),\r\n    stripSpacingOrthogonalElementAxis: z.number(),\r\n    layersNumber: z.number().positive(),\r\n    stripInclination: z.number().min(0).max(90),\r\n    concreteStrutInclination: z.number().min(22).max(45),\r\n    appliedShearForce: z.number().positive(),\r\n    product: productFormSchema,\r\n  }),\r\n})\r\n\r\nexport type RectangularBeamForm = z.infer<typeof rectangularBeamFormSchema>\r\nexport type RectangularBeamGeneralFormInputs = z.infer<\r\n  typeof rectangularBeamGeneralSchema\r\n>\r\nexport type RectangularBeamGeometryFormInputs = z.infer<\r\n  typeof rectangularBeamGeometrySchema\r\n>\r\nexport type RectangularBeamReinforcementFormInputs = z.infer<\r\n  typeof rectangularBeamReinforcementSchema\r\n>\r\nexport type RectangularBeamMaterialFormInputs = z.infer<\r\n  typeof rectangularBeamMaterialSchema\r\n>\r\nexport type RectangularBeamFlexuralCalculationInput = z.infer<\r\n  typeof rectangularBeamFlexuralCalculationSchema\r\n>\r\nexport type RectangularBeamShearCalculationInput = z.infer<\r\n  typeof rectangularBeamShearCalculationSchema\r\n>\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAMA;AACA;;;;AAEO,MAAM,+BAA+B,kLAAC,CAAC,MAAM,CAAC;IACnD,oBAAoB,kLAAC,CAAC,MAAM;IAC5B,UAAU,kLAAC,CAAC,IAAI,CAAC,8IAAe;AAClC;AAEO,MAAM,gCAAgC,kLAAC,CAAC,MAAM,CAAC;IACpD,OAAO,kLAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,QAAQ,kLAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,kBAAkB,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACrC,qBAAqB,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACxC,gBAAgB,kLAAC,CAAC,MAAM;IACxB,UAAU,kLAAC,CAAC,IAAI,CAAC,uJAAwB;AAC3C;AAEO,MAAM,qCAAqC,kLAAC,CAAC,MAAM,CAAC;IACzD,KAAK,kLAAC,CAAC,MAAM,CAAC;QACZ,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,MAAM,kLAAC,CAAC,MAAM;IAChB;IACA,QAAQ,kLAAC,CAAC,MAAM,CAAC;QACf,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,MAAM,kLAAC,CAAC,MAAM;IAChB;IACA,YAAY,kLAAC,CAAC,MAAM,CAAC;QACnB,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,MAAM,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACzB,MAAM,kLAAC,CAAC,MAAM;QACd,gBAAgB,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACnC,oBAAoB,kLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC;QAC7C,cAAc,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACnC;AACF;AAEO,MAAM,gCAAgC,kLAAC,CAAC,MAAM,CAAC;IACpD,gBAAgB,kLAAC,CAAC,IAAI,CAAC,8JAA+B;IACtD,kBAAkB,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACrC,eAAe,kLAAC,CAAC,MAAM,CAAC;QACtB,IAAI,kLAAC,CAAC,MAAM;QACZ,MAAM,kLAAC,CAAC,MAAM;QACd,yBAAyB,kLAAC,CAAC,MAAM;QACjC,6BAA6B,kLAAC,CAAC,MAAM;QACrC,4BAA4B,kLAAC,CAAC,MAAM;QACpC,wBAAwB,kLAAC,CAAC,MAAM;QAChC,gBAAgB,kLAAC,CAAC,MAAM;QACxB,+CAA+C,kLAAC,CAAC,MAAM;QACvD,+CAA+C,kLAAC,CAAC,MAAM;IACzD;IACA,YAAY,kLAAC,CAAC,MAAM,CAAC;QACnB,IAAI,kLAAC,CAAC,MAAM;QACZ,MAAM,kLAAC,CAAC,MAAM;QACd,eAAe,kLAAC,CAAC,MAAM;QACvB,iBAAiB,kLAAC,CAAC,MAAM;QACzB,sBAAsB,kLAAC,CAAC,MAAM;QAC9B,gBAAgB,kLAAC,CAAC,MAAM;QACxB,yCAAyC,kLAAC,CAAC,MAAM;QACjD,yCAAyC,kLAAC,CAAC,MAAM;IACnD;AACF;AAEO,MAAM,4BAA4B,kLAAC,CAAC,MAAM,CAAC;IAChD,oBAAoB,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACvC,UAAU,kLAAC,CAAC,IAAI,CAAC,8IAAe,EAAE,QAAQ;IAC1C,UAAU,8BAA8B,QAAQ;IAChD,kBAAkB,mCAAmC,QAAQ;IAC7D,oBAAoB,8BAA8B,QAAQ;AAC5D;AAEO,MAAM,mCAAmC,kLAAC,CAAC,MAAM,CAAC;IACvD,oBAAoB,kLAAC,CAAC,MAAM;IAC5B,UAAU,kLAAC,CAAC,IAAI,CAAC,8IAAe;IAChC,UAAU;IACV,kBAAkB;IAClB,oBAAoB;AACtB;AAEO,MAAM,2CAA2C,kLAAC,CAAC,MAAM,CAAC;IAC/D,iBAAiB,kLAAC,CAAC,OAAO,CAAC;IAC3B,OAAO,kLAAC,CAAC,MAAM,CAAC;QACd,YAAY,kLAAC,CAAC,MAAM;QACpB,cAAc,kLAAC,CAAC,MAAM;QACtB,eAAe,kLAAC,CAAC,MAAM;QACvB,SAAS,yKAAiB;IAC5B;AACF;AAEO,MAAM,wCAAwC,kLAAC,CAAC,MAAM,CAAC;IAC5D,iBAAiB,kLAAC,CAAC,OAAO,CAAC;IAC3B,OAAO,kLAAC,CAAC,MAAM,CAAC;QACd,qBAAqB,kLAAC,CAAC,OAAO,CAAC,oJAAqB;QACpD,WAAW,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAC9B,YAAY,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAC/B,8BAA8B,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACjD,mCAAmC,kLAAC,CAAC,MAAM;QAC3C,cAAc,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACjC,kBAAkB,kLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;QACxC,0BAA0B,kLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,GAAG,CAAC;QACjD,mBAAmB,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACtC,SAAS,yKAAiB;IAC5B;AACF", "debugId": null}}, {"offset": {"line": 1650, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/api/modules/schemas/rectangular-beam-params.ts"], "sourcesContent": ["import { MODULE_POLARITY, REINFORCEMENT_LAYOUTS } from '@atlas/constants/module'\r\nimport { nullToObjectTransform } from '@atlas/functions/zod/null-to-object-transform'\r\nimport { nullToUndefinedTransform } from '@atlas/functions/zod/null-to-undefined-transform'\r\nimport { productSchema } from '@atlas/lib/api/products/schemas/product'\r\nimport {\r\n  rectangularBeamGeometrySchema,\r\n  rectangularBeamMaterialSchema,\r\n  rectangularBeamReinforcementSchema,\r\n} from '@atlas/types/schemas/rectangular-beam-from'\r\nimport { z } from 'zod'\r\n\r\nexport const rectangularBeamParamsSchema = z.object({\r\n  initialDeformation: z\r\n    .number()\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToUndefinedTransform),\r\n  polarity: z\r\n    .enum(MODULE_POLARITY)\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToUndefinedTransform),\r\n  geometry: rectangularBeamGeometrySchema\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToObjectTransform),\r\n  reinforcementBar: rectangularBeamReinforcementSchema\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToObjectTransform),\r\n  materialProperties: rectangularBeamMaterialSchema\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToObjectTransform),\r\n})\r\n\r\nexport const rectangularBeamFlexuralVerifyExecutionInputSchema = z.object({\r\n  stripWidth: z.number(),\r\n  layersNumber: z.number(),\r\n  bendingMoment: z.number(),\r\n  product: productSchema,\r\n})\r\n\r\nexport const rectangularBeamShearVerifyExecutionInputSchema = z.object({\r\n  reinforcementLayout: z.literal(REINFORCEMENT_LAYOUTS),\r\n  webHeight: z.number().positive(),\r\n  stripWidth: z.number().positive(),\r\n  stripSpacingAlongElementAxis: z.number().positive(),\r\n  stripSpacingOrthogonalElementAxis: z.number(),\r\n  layersNumber: z.number().positive(),\r\n  stripInclination: z.number().min(0).max(90),\r\n  concreteStrutInclination: z.number().min(22).max(45),\r\n  appliedShearForce: z.number().positive(),\r\n  product: productSchema,\r\n})\r\n\r\nexport const rectangularBeamFlexuralCalculationResultSchema = z.object({\r\n  momentCapacity: z.number(),\r\n  equilibrium: z.number(),\r\n  checkResult: z.boolean(),\r\n})\r\n\r\nexport const rectangularBeamShearCalculationResultSchema = z.object({\r\n  frpShearContribution: z.number(),\r\n  shearCapacity: z.number(),\r\n  checkResult: z.boolean(),\r\n})\r\n\r\nexport const rectangularBeamCalculationCheck = z.object({\r\n  flexuralCalculationResult: rectangularBeamFlexuralCalculationResultSchema,\r\n  shearCalculationResult: rectangularBeamShearCalculationResultSchema,\r\n})\r\n\r\nexport type RectangularBeamFlexuralCalculationResult = z.infer<\r\n  typeof rectangularBeamFlexuralCalculationResultSchema\r\n>\r\nexport type RectangularBeamShearCalculationResult = z.infer<\r\n  typeof rectangularBeamShearCalculationResultSchema\r\n>\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AAKA;;;;;;;AAEO,MAAM,8BAA8B,kLAAC,CAAC,MAAM,CAAC;IAClD,oBAAoB,kLAAC,CAClB,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,UAAU,kLAAC,CACR,IAAI,CAAC,8IAAe,EACpB,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,UAAU,uLAA6B,CACpC,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,qLAAqB;IAClC,kBAAkB,4LAAkC,CACjD,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,qLAAqB;IAClC,oBAAoB,uLAA6B,CAC9C,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,qLAAqB;AACpC;AAEO,MAAM,oDAAoD,kLAAC,CAAC,MAAM,CAAC;IACxE,YAAY,kLAAC,CAAC,MAAM;IACpB,cAAc,kLAAC,CAAC,MAAM;IACtB,eAAe,kLAAC,CAAC,MAAM;IACvB,SAAS,oKAAa;AACxB;AAEO,MAAM,iDAAiD,kLAAC,CAAC,MAAM,CAAC;IACrE,qBAAqB,kLAAC,CAAC,OAAO,CAAC,oJAAqB;IACpD,WAAW,kLAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,YAAY,kLAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,8BAA8B,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACjD,mCAAmC,kLAAC,CAAC,MAAM;IAC3C,cAAc,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACjC,kBAAkB,kLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IACxC,0BAA0B,kLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,GAAG,CAAC;IACjD,mBAAmB,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACtC,SAAS,oKAAa;AACxB;AAEO,MAAM,iDAAiD,kLAAC,CAAC,MAAM,CAAC;IACrE,gBAAgB,kLAAC,CAAC,MAAM;IACxB,aAAa,kLAAC,CAAC,MAAM;IACrB,aAAa,kLAAC,CAAC,OAAO;AACxB;AAEO,MAAM,8CAA8C,kLAAC,CAAC,MAAM,CAAC;IAClE,sBAAsB,kLAAC,CAAC,MAAM;IAC9B,eAAe,kLAAC,CAAC,MAAM;IACvB,aAAa,kLAAC,CAAC,OAAO;AACxB;AAEO,MAAM,kCAAkC,kLAAC,CAAC,MAAM,CAAC;IACtD,2BAA2B;IAC3B,wBAAwB;AAC1B", "debugId": null}}, {"offset": {"line": 1719, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/api/modules/schemas/slab-params.ts"], "sourcesContent": ["import { z } from 'zod'\r\n\r\nexport const slabParamsSchema = z.object({\r\n  geometry: z\r\n    .object({\r\n      joistFormwork: z\r\n        .enum(['T_SHAPED', 'RECTANGULAR_WITHOUT_SLAB'])\r\n        .optional(),\r\n      joistBase: z.number().optional(),\r\n      joistWebHeight: z.number().optional(),\r\n      existingSlabHeight: z.number().optional(),\r\n      joistSpacing: z.number().optional(),\r\n      bottomRebarCover: z.number().optional(),\r\n      topRebarCover: z.number().optional(),\r\n      structuralScheme: z.enum(['SIMPLY_SUPPORTED', 'CANTILEVER']).optional(),\r\n      totalSlabThickness: z.number().optional(),\r\n      effectiveDepth: z.number().optional(),\r\n    })\r\n    .optional()\r\n    .nullable(),\r\n\r\n  slabRebar: z\r\n    .object({\r\n      spanBottomRebar: z\r\n        .object({\r\n          diameter: z.number().optional(),\r\n          quantity: z.number().optional(),\r\n          area: z.number().optional(),\r\n        })\r\n        .optional(),\r\n      spanTopRebar: z\r\n        .object({\r\n          diameter: z.number().optional(),\r\n          quantity: z.number().optional(),\r\n          area: z.number().optional(),\r\n        })\r\n        .optional(),\r\n      supportBottomRebar: z\r\n        .object({\r\n          diameter: z.number().optional(),\r\n          quantity: z.number().optional(),\r\n          area: z.number().optional(),\r\n        })\r\n        .optional(),\r\n      supportTopRebar: z\r\n        .object({\r\n          diameter: z.number().optional(),\r\n          quantity: z.number().optional(),\r\n          area: z.number().optional(),\r\n        })\r\n        .optional(),\r\n      additionalRebar: z\r\n        .object({\r\n          diameter: z.number().optional(),\r\n          quantity: z.number().optional(),\r\n          area: z.number().optional(),\r\n        })\r\n        .optional(),\r\n      additionalSteelElasticModulus: z.number().optional(),\r\n    })\r\n    .optional()\r\n    .nullable(),\r\n\r\n  materialProperties: z\r\n    .object({\r\n      concreteClassKnowledgeLevel: z.enum(['LC1', 'LC2', 'LC3']).optional(),\r\n      steelGradeKnowledgeLevel: z.enum(['LC1', 'LC2', 'LC3']).optional(),\r\n      concreteMaterialClass: z.enum(['DUCTILE', 'BRITTLE']).optional(),\r\n      steelMaterialClass: z.enum(['DUCTILE', 'BRITTLE']).optional(),\r\n      concreteClass: z\r\n        .object({\r\n          id: z.string().optional(),\r\n          name: z.string().optional(),\r\n          cubeCompressiveStrength: z.number().optional(),\r\n          cylinderCompressiveStrength: z.number().optional(),\r\n          averageCompressiveStrength: z.number().optional(),\r\n          averageTensileStrength: z.number().optional(),\r\n          elasticModulus: z.number().optional(),\r\n          designCompressiveStrengthForBrittleMechanisms: z.number().optional(),\r\n          designTensileStrengthForBrittleMechanisms: z.number().optional(),\r\n          designCompressiveStrengthForDuctileMechanisms: z.number().optional(),\r\n        })\r\n        .optional(),\r\n      steelGrade: z\r\n        .object({\r\n          id: z.string().optional(),\r\n          name: z.string().optional(),\r\n          yieldStrength: z.number().optional(),\r\n          tensileStrength: z.number().optional(),\r\n          elongationPercentage: z.number().optional(),\r\n          elasticModulus: z.number().optional(),\r\n          designYieldStrengthForBrittleMechanisms: z.number().optional(),\r\n          designYieldStrengthForDuctileMechanisms: z.number().optional(),\r\n        })\r\n        .optional(),\r\n    })\r\n    .optional()\r\n    .nullable(),\r\n\r\n  slabFrcReinforcement: z\r\n    .object({\r\n      frcReinforcementType: z.string().optional(),\r\n      toughnessClassAndFibersType: z.string().optional(),\r\n      frcSlabThickness: z.number().optional(),\r\n      frcElasticModulus: z.number().optional(),\r\n      characteristicCylindricalCompressiveStrengthFrcMatrix: z\r\n        .number()\r\n        .optional(),\r\n      frcCharacteristicTensileStrength: z.number().optional(),\r\n      specificWeight: z.number().optional(),\r\n      supportAdditionalRebar: z\r\n        .object({\r\n          diameter: z.number().optional(),\r\n          quantity: z.number().optional(),\r\n          elasticModulus: z.number().optional(),\r\n        })\r\n        .optional(),\r\n    })\r\n    .optional()\r\n    .nullable(),\r\n})\r\n\r\nexport const slabFlexuralPositiveCalculationResultSchema = z.object({\r\n  designMoment: z.number().optional().nullable(),\r\n  resistantMoment: z.number().optional().nullable(),\r\n  utilizationRatio: z.number().optional().nullable(),\r\n  neutralAxisDepth: z.number().optional().nullable(),\r\n  compressionZoneHeight: z.number().optional().nullable(),\r\n  isVerified: z.boolean().optional().nullable(),\r\n})\r\n\r\nexport const slabFlexuralNegativeCalculationResultSchema = z.object({\r\n  designMoment: z.number().optional().nullable(),\r\n  resistantMoment: z.number().optional().nullable(),\r\n  utilizationRatio: z.number().optional().nullable(),\r\n  neutralAxisDepth: z.number().optional().nullable(),\r\n  compressionZoneHeight: z.number().optional().nullable(),\r\n  isVerified: z.boolean().optional().nullable(),\r\n})\r\n\r\nexport const slabShearCalculationResultSchema = z.object({\r\n  // Common properties\r\n  sectionFillType: z.string().optional().nullable(),\r\n  inputShearForce: z.number().optional().nullable(),\r\n\r\n  // Unreinforced section results\r\n  unreinforcedSectionEffectiveDepth: z.number().optional().nullable(),\r\n  unreinforcedTensionAreaMinimumWidth: z.number().optional().nullable(),\r\n  unreinforcedSizeEffectFactor: z.number().optional().nullable(),\r\n  unreinforcedCoefficient: z.number().optional().nullable(),\r\n  unreinforcedTensileLongitudinalReinforcementArea: z\r\n    .number()\r\n    .optional()\r\n    .nullable(),\r\n  unreinforcedTensileReinforcementRatio: z.number().optional().nullable(),\r\n  unreinforcedShearCapacity: z.number().optional().nullable(),\r\n\r\n  // Reinforced section results\r\n  reinforcedSectionEffectiveDepth: z.number().optional().nullable(),\r\n  reinforcedTensionAreaMinimumWidth: z.number().optional().nullable(),\r\n  reinforcedSizeEffectFactor: z.number().optional().nullable(),\r\n  reinforcedMeanCharacteristicCompressiveStrength: z\r\n    .number()\r\n    .optional()\r\n    .nullable(),\r\n  reinforcedCoefficient: z.number().optional().nullable(),\r\n  reinforcedTensileLongitudinalReinforcementArea: z\r\n    .number()\r\n    .optional()\r\n    .nullable(),\r\n  reinforcedTensileReinforcementRatio: z.number().optional().nullable(),\r\n  reinforcedSectionShearResistance: z.number().optional().nullable(),\r\n\r\n  // Verification result\r\n  shearVerificationResult: z.string().optional().nullable(),\r\n  isShearVerificationSatisfied: z.boolean().optional().nullable(),\r\n})\r\n\r\nexport const slabNegativeMomentCalculationResultSchema = z.object({\r\n  designMoment: z.number().optional().nullable(),\r\n  resistantMoment: z.number().optional().nullable(),\r\n  utilizationRatio: z.number().optional().nullable(),\r\n  effectiveDepth: z.number().optional().nullable(),\r\n  reinforcementArea: z.number().optional().nullable(),\r\n  isVerified: z.boolean().optional().nullable(),\r\n})\r\n\r\nexport const slabCalculationsResultSchema = z.object({\r\n  flexuralPositive: slabFlexuralPositiveCalculationResultSchema.optional(),\r\n  flexuralNegative: slabFlexuralNegativeCalculationResultSchema.optional(),\r\n  shear: slabShearCalculationResultSchema.optional(),\r\n  negativeMoment: slabNegativeMomentCalculationResultSchema.optional(),\r\n})\r\n\r\nexport const slabFlexuralVerifyExecutionInputSchema = z\r\n  .object({\r\n    stripWidth: z.number().optional(),\r\n    bendingMoment: z.number().optional(),\r\n    spanVerification: z.boolean().optional(), // Backend uses 'spanVerification' not 'isSpanVerification'\r\n    product: z\r\n      .object({\r\n        // Backend uses 'product' not 'productInput'\r\n        id: z.string().optional(),\r\n        name: z.string().optional(),\r\n        sourceType: z.enum(['DATABASE', 'CUSTOM']).optional(),\r\n        thickness: z.number().optional().nullable(), // Backend uses 'thickness' not 'frcSlabThickness'\r\n        elasticModulus: z.number().optional(),\r\n        cylindricCompressiveStrength: z.number().optional(),\r\n        characteristicTensileStrength: z.number().optional(),\r\n        specificWeight: z.number().optional().nullable(),\r\n        adhesionToConcrete: z.number().optional(),\r\n        fiberType: z.string().optional(),\r\n        tensileStrength: z.number().optional(),\r\n        orientation: z.string().optional().nullable(),\r\n        systemDeformation: z.number().optional(),\r\n      })\r\n      .optional(),\r\n  })\r\n  .optional()\r\n\r\nexport const slabShearVerifyExecutionInputSchema = z\r\n  .object({\r\n    sectionFillType: z.string().optional(),\r\n    shearForce: z.number().optional(),\r\n    isCantilever: z.boolean().optional(),\r\n    product: z\r\n      .object({\r\n        // Backend uses 'product' not 'productInput'\r\n        id: z.string().optional(),\r\n        name: z.string().optional(),\r\n        sourceType: z.enum(['DATABASE', 'CUSTOM']).optional(),\r\n        thickness: z.number().optional().nullable(), // Backend uses 'thickness' not 'frcSlabThickness'\r\n        elasticModulus: z.number().optional(),\r\n        cylindricCompressiveStrength: z.number().optional(),\r\n        characteristicTensileStrength: z.number().optional(),\r\n        specificWeight: z.number().optional().nullable(),\r\n        adhesionToConcrete: z.number().optional(),\r\n        fiberType: z.string().optional(),\r\n        tensileStrength: z.number().optional(),\r\n        orientation: z.string().optional().nullable(),\r\n        systemDeformation: z.number().optional(),\r\n      })\r\n      .optional(),\r\n  })\r\n  .optional()\r\n\r\nexport const slabInterfaceSlipVerifyExecutionInputSchema = z\r\n  .object({\r\n    stripWidth: z.number().optional(),\r\n    shearForce: z.number().optional(),\r\n    frcBondStrength: z.number().optional(),\r\n    product: z\r\n      .object({\r\n        // Backend uses 'product' not 'productInput'\r\n        id: z.string().optional(),\r\n        name: z.string().optional(),\r\n        sourceType: z.enum(['DATABASE', 'CUSTOM']).optional(),\r\n        thickness: z.number().optional().nullable(), // Backend uses 'thickness' not 'frcSlabThickness'\r\n        elasticModulus: z.number().optional(),\r\n        cylindricCompressiveStrength: z.number().optional(),\r\n        characteristicTensileStrength: z.number().optional(),\r\n        specificWeight: z.number().optional().nullable(),\r\n        adhesionToConcrete: z.number().optional(),\r\n        fiberType: z.string().optional(),\r\n        tensileStrength: z.number().optional(),\r\n        orientation: z.string().optional().nullable(),\r\n        systemDeformation: z.number().optional(),\r\n      })\r\n      .optional(),\r\n  })\r\n  .optional()\r\n\r\nexport const slabFlexuralCalculationResultSchema = z.object({\r\n  // Common properties\r\n  sectionType: z.string().optional().nullable(),\r\n\r\n  // Unreinforced section results\r\n  unreinforcedBottomSteelStrain: z.number().optional().nullable(),\r\n  unreinforcedTopSteelStrain: z.number().optional().nullable(),\r\n  unreinforcedBottomSteelStress: z.number().optional().nullable(),\r\n  unreinforcedTopSteelStress: z.number().optional().nullable(),\r\n  unreinforcedNeutralAxisDistance: z.number().optional().nullable(),\r\n  unreinforcedTranslationalEquilibrium: z.number().optional().nullable(),\r\n  unreinforcedResistanceMoment: z.number().optional().nullable(),\r\n\r\n  // Reinforced section results\r\n  maximumBendingMoment: z.number().optional().nullable(),\r\n  reinforcedBottomSteelStrain: z.number().optional().nullable(),\r\n  reinforcedTopSteelStrain: z.number().optional().nullable(),\r\n  reinforcedSupplementarySteelStrain: z.number().optional().nullable(),\r\n  reinforcedBottomSteelStress: z.number().optional().nullable(),\r\n  reinforcedTopSteelStress: z.number().optional().nullable(),\r\n  reinforcedSupplementarySteelStress: z.number().optional().nullable(),\r\n  reinforcedNeutralAxisDistance: z.number().optional().nullable(),\r\n  reinforcedTranslationalEquilibrium: z.number().optional().nullable(),\r\n  reinforcedSectionResistanceMoment: z.number().optional().nullable(),\r\n\r\n  // Verification results\r\n  checkResult: z.boolean().optional().nullable(),\r\n  checkValue: z.number().optional().nullable(),\r\n})\r\n\r\nexport const slabInterfaceSlipCalculationResultSchema = z.object({\r\n  // Calculation results\r\n  negativeMomentAreaHomogenizationCoefficient: z.number().optional().nullable(),\r\n  neutralAxisCompressedFlangeDistance: z.number().optional().nullable(),\r\n  translationalEquilibrium: z.number().optional().nullable(),\r\n  staticMoment: z.number().optional().nullable(),\r\n  neutralAxisInertiaMoment: z.number().optional().nullable(),\r\n  interfaceShearStress: z.number().optional().nullable(),\r\n  concreteToConcreteFrictionCoefficient: z.number().optional().nullable(),\r\n  frcBondStrength: z.number().optional().nullable(),\r\n\r\n  // Verification result\r\n  checkResult: z.boolean().optional().nullable(),\r\n\r\n  // Input values\r\n  inputShearForce: z.number().optional().nullable(),\r\n  inputStripWidth: z.number().optional().nullable(),\r\n})\r\n\r\nexport type SlabParams = z.infer<typeof slabParamsSchema>\r\nexport type SlabFlexuralPositiveCalculationResult = z.infer<\r\n  typeof slabFlexuralPositiveCalculationResultSchema\r\n>\r\nexport type SlabFlexuralNegativeCalculationResult = z.infer<\r\n  typeof slabFlexuralNegativeCalculationResultSchema\r\n>\r\nexport type SlabShearCalculationResult = z.infer<\r\n  typeof slabShearCalculationResultSchema\r\n>\r\nexport type SlabInterfaceSlipCalculationResult = z.infer<\r\n  typeof slabInterfaceSlipCalculationResultSchema\r\n>\r\nexport type SlabNegativeMomentCalculationResult = z.infer<\r\n  typeof slabNegativeMomentCalculationResultSchema\r\n>\r\nexport type SlabCalculationsResult = z.infer<\r\n  typeof slabCalculationsResultSchema\r\n>\r\n\r\n// Schema to check if at least one calculation has been completed\r\n// Used to enable the report generation button\r\nexport const slabCalculationCheck = z\r\n  .object({\r\n    flexuralVerifyExecutionResultMPlus:\r\n      slabFlexuralCalculationResultSchema.optional(),\r\n    flexuralVerifyExecutionResultMMinus:\r\n      slabFlexuralCalculationResultSchema.optional(),\r\n    shearVerifyExecutionResult: slabShearCalculationResultSchema.optional(),\r\n    interfaceSlipCalculationResult:\r\n      slabInterfaceSlipCalculationResultSchema.optional(),\r\n  })\r\n  .refine(\r\n    data =>\r\n      data.flexuralVerifyExecutionResultMPlus !== undefined ||\r\n      data.flexuralVerifyExecutionResultMMinus !== undefined ||\r\n      data.shearVerifyExecutionResult !== undefined ||\r\n      data.interfaceSlipCalculationResult !== undefined,\r\n    {\r\n      message:\r\n        'At least one calculation must be completed to generate a report',\r\n    },\r\n  )\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAEO,MAAM,mBAAmB,kLAAC,CAAC,MAAM,CAAC;IACvC,UAAU,kLAAC,CACR,MAAM,CAAC;QACN,eAAe,kLAAC,CACb,IAAI,CAAC;YAAC;YAAY;SAA2B,EAC7C,QAAQ;QACX,WAAW,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAC9B,gBAAgB,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACnC,oBAAoB,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACvC,cAAc,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACjC,kBAAkB,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACrC,eAAe,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAClC,kBAAkB,kLAAC,CAAC,IAAI,CAAC;YAAC;YAAoB;SAAa,EAAE,QAAQ;QACrE,oBAAoB,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACvC,gBAAgB,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACrC,GACC,QAAQ,GACR,QAAQ;IAEX,WAAW,kLAAC,CACT,MAAM,CAAC;QACN,iBAAiB,kLAAC,CACf,MAAM,CAAC;YACN,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;YAC7B,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;YAC7B,MAAM,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAC3B,GACC,QAAQ;QACX,cAAc,kLAAC,CACZ,MAAM,CAAC;YACN,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;YAC7B,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;YAC7B,MAAM,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAC3B,GACC,QAAQ;QACX,oBAAoB,kLAAC,CAClB,MAAM,CAAC;YACN,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;YAC7B,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;YAC7B,MAAM,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAC3B,GACC,QAAQ;QACX,iBAAiB,kLAAC,CACf,MAAM,CAAC;YACN,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;YAC7B,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;YAC7B,MAAM,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAC3B,GACC,QAAQ;QACX,iBAAiB,kLAAC,CACf,MAAM,CAAC;YACN,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;YAC7B,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;YAC7B,MAAM,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAC3B,GACC,QAAQ;QACX,+BAA+B,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACpD,GACC,QAAQ,GACR,QAAQ;IAEX,oBAAoB,kLAAC,CAClB,MAAM,CAAC;QACN,6BAA6B,kLAAC,CAAC,IAAI,CAAC;YAAC;YAAO;YAAO;SAAM,EAAE,QAAQ;QACnE,0BAA0B,kLAAC,CAAC,IAAI,CAAC;YAAC;YAAO;YAAO;SAAM,EAAE,QAAQ;QAChE,uBAAuB,kLAAC,CAAC,IAAI,CAAC;YAAC;YAAW;SAAU,EAAE,QAAQ;QAC9D,oBAAoB,kLAAC,CAAC,IAAI,CAAC;YAAC;YAAW;SAAU,EAAE,QAAQ;QAC3D,eAAe,kLAAC,CACb,MAAM,CAAC;YACN,IAAI,kLAAC,CAAC,MAAM,GAAG,QAAQ;YACvB,MAAM,kLAAC,CAAC,MAAM,GAAG,QAAQ;YACzB,yBAAyB,kLAAC,CAAC,MAAM,GAAG,QAAQ;YAC5C,6BAA6B,kLAAC,CAAC,MAAM,GAAG,QAAQ;YAChD,4BAA4B,kLAAC,CAAC,MAAM,GAAG,QAAQ;YAC/C,wBAAwB,kLAAC,CAAC,MAAM,GAAG,QAAQ;YAC3C,gBAAgB,kLAAC,CAAC,MAAM,GAAG,QAAQ;YACnC,+CAA+C,kLAAC,CAAC,MAAM,GAAG,QAAQ;YAClE,2CAA2C,kLAAC,CAAC,MAAM,GAAG,QAAQ;YAC9D,+CAA+C,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACpE,GACC,QAAQ;QACX,YAAY,kLAAC,CACV,MAAM,CAAC;YACN,IAAI,kLAAC,CAAC,MAAM,GAAG,QAAQ;YACvB,MAAM,kLAAC,CAAC,MAAM,GAAG,QAAQ;YACzB,eAAe,kLAAC,CAAC,MAAM,GAAG,QAAQ;YAClC,iBAAiB,kLAAC,CAAC,MAAM,GAAG,QAAQ;YACpC,sBAAsB,kLAAC,CAAC,MAAM,GAAG,QAAQ;YACzC,gBAAgB,kLAAC,CAAC,MAAM,GAAG,QAAQ;YACnC,yCAAyC,kLAAC,CAAC,MAAM,GAAG,QAAQ;YAC5D,yCAAyC,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAC9D,GACC,QAAQ;IACb,GACC,QAAQ,GACR,QAAQ;IAEX,sBAAsB,kLAAC,CACpB,MAAM,CAAC;QACN,sBAAsB,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACzC,6BAA6B,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAChD,kBAAkB,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACrC,mBAAmB,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACtC,uDAAuD,kLAAC,CACrD,MAAM,GACN,QAAQ;QACX,kCAAkC,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACrD,gBAAgB,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACnC,wBAAwB,kLAAC,CACtB,MAAM,CAAC;YACN,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;YAC7B,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;YAC7B,gBAAgB,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACrC,GACC,QAAQ;IACb,GACC,QAAQ,GACR,QAAQ;AACb;AAEO,MAAM,8CAA8C,kLAAC,CAAC,MAAM,CAAC;IAClE,cAAc,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC5C,iBAAiB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC/C,kBAAkB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAChD,kBAAkB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAChD,uBAAuB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACrD,YAAY,kLAAC,CAAC,OAAO,GAAG,QAAQ,GAAG,QAAQ;AAC7C;AAEO,MAAM,8CAA8C,kLAAC,CAAC,MAAM,CAAC;IAClE,cAAc,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC5C,iBAAiB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC/C,kBAAkB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAChD,kBAAkB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAChD,uBAAuB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACrD,YAAY,kLAAC,CAAC,OAAO,GAAG,QAAQ,GAAG,QAAQ;AAC7C;AAEO,MAAM,mCAAmC,kLAAC,CAAC,MAAM,CAAC;IACvD,oBAAoB;IACpB,iBAAiB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC/C,iBAAiB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAE/C,+BAA+B;IAC/B,mCAAmC,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACjE,qCAAqC,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACnE,8BAA8B,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC5D,yBAAyB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACvD,kDAAkD,kLAAC,CAChD,MAAM,GACN,QAAQ,GACR,QAAQ;IACX,uCAAuC,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACrE,2BAA2B,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAEzD,6BAA6B;IAC7B,iCAAiC,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC/D,mCAAmC,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACjE,4BAA4B,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC1D,iDAAiD,kLAAC,CAC/C,MAAM,GACN,QAAQ,GACR,QAAQ;IACX,uBAAuB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACrD,gDAAgD,kLAAC,CAC9C,MAAM,GACN,QAAQ,GACR,QAAQ;IACX,qCAAqC,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACnE,kCAAkC,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAEhE,sBAAsB;IACtB,yBAAyB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACvD,8BAA8B,kLAAC,CAAC,OAAO,GAAG,QAAQ,GAAG,QAAQ;AAC/D;AAEO,MAAM,4CAA4C,kLAAC,CAAC,MAAM,CAAC;IAChE,cAAc,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC5C,iBAAiB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC/C,kBAAkB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAChD,gBAAgB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC9C,mBAAmB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACjD,YAAY,kLAAC,CAAC,OAAO,GAAG,QAAQ,GAAG,QAAQ;AAC7C;AAEO,MAAM,+BAA+B,kLAAC,CAAC,MAAM,CAAC;IACnD,kBAAkB,4CAA4C,QAAQ;IACtE,kBAAkB,4CAA4C,QAAQ;IACtE,OAAO,iCAAiC,QAAQ;IAChD,gBAAgB,0CAA0C,QAAQ;AACpE;AAEO,MAAM,yCAAyC,kLAAC,CACpD,MAAM,CAAC;IACN,YAAY,kLAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,eAAe,kLAAC,CAAC,MAAM,GAAG,QAAQ;IAClC,kBAAkB,kLAAC,CAAC,OAAO,GAAG,QAAQ;IACtC,SAAS,kLAAC,CACP,MAAM,CAAC;QACN,4CAA4C;QAC5C,IAAI,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACvB,MAAM,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACzB,YAAY,kLAAC,CAAC,IAAI,CAAC;YAAC;YAAY;SAAS,EAAE,QAAQ;QACnD,WAAW,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;QACzC,gBAAgB,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACnC,8BAA8B,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACjD,+BAA+B,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAClD,gBAAgB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;QAC9C,oBAAoB,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACvC,WAAW,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAC9B,iBAAiB,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACpC,aAAa,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;QAC3C,mBAAmB,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACxC,GACC,QAAQ;AACb,GACC,QAAQ;AAEJ,MAAM,sCAAsC,kLAAC,CACjD,MAAM,CAAC;IACN,iBAAiB,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACpC,YAAY,kLAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,cAAc,kLAAC,CAAC,OAAO,GAAG,QAAQ;IAClC,SAAS,kLAAC,CACP,MAAM,CAAC;QACN,4CAA4C;QAC5C,IAAI,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACvB,MAAM,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACzB,YAAY,kLAAC,CAAC,IAAI,CAAC;YAAC;YAAY;SAAS,EAAE,QAAQ;QACnD,WAAW,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;QACzC,gBAAgB,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACnC,8BAA8B,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACjD,+BAA+B,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAClD,gBAAgB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;QAC9C,oBAAoB,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACvC,WAAW,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAC9B,iBAAiB,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACpC,aAAa,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;QAC3C,mBAAmB,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACxC,GACC,QAAQ;AACb,GACC,QAAQ;AAEJ,MAAM,8CAA8C,kLAAC,CACzD,MAAM,CAAC;IACN,YAAY,kLAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,YAAY,kLAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,iBAAiB,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACpC,SAAS,kLAAC,CACP,MAAM,CAAC;QACN,4CAA4C;QAC5C,IAAI,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACvB,MAAM,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACzB,YAAY,kLAAC,CAAC,IAAI,CAAC;YAAC;YAAY;SAAS,EAAE,QAAQ;QACnD,WAAW,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;QACzC,gBAAgB,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACnC,8BAA8B,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACjD,+BAA+B,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAClD,gBAAgB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;QAC9C,oBAAoB,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACvC,WAAW,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAC9B,iBAAiB,kLAAC,CAAC,MAAM,GAAG,QAAQ;QACpC,aAAa,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;QAC3C,mBAAmB,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACxC,GACC,QAAQ;AACb,GACC,QAAQ;AAEJ,MAAM,sCAAsC,kLAAC,CAAC,MAAM,CAAC;IAC1D,oBAAoB;IACpB,aAAa,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAE3C,+BAA+B;IAC/B,+BAA+B,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC7D,4BAA4B,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC1D,+BAA+B,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC7D,4BAA4B,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC1D,iCAAiC,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC/D,sCAAsC,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACpE,8BAA8B,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAE5D,6BAA6B;IAC7B,sBAAsB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACpD,6BAA6B,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC3D,0BAA0B,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACxD,oCAAoC,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAClE,6BAA6B,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC3D,0BAA0B,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACxD,oCAAoC,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAClE,+BAA+B,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC7D,oCAAoC,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAClE,mCAAmC,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAEjE,uBAAuB;IACvB,aAAa,kLAAC,CAAC,OAAO,GAAG,QAAQ,GAAG,QAAQ;IAC5C,YAAY,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;AAC5C;AAEO,MAAM,2CAA2C,kLAAC,CAAC,MAAM,CAAC;IAC/D,sBAAsB;IACtB,6CAA6C,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC3E,qCAAqC,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACnE,0BAA0B,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACxD,cAAc,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC5C,0BAA0B,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACxD,sBAAsB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACpD,uCAAuC,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACrE,iBAAiB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAE/C,sBAAsB;IACtB,aAAa,kLAAC,CAAC,OAAO,GAAG,QAAQ,GAAG,QAAQ;IAE5C,eAAe;IACf,iBAAiB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC/C,iBAAiB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;AACjD;AAwBO,MAAM,uBAAuB,kLAAC,CAClC,MAAM,CAAC;IACN,oCACE,oCAAoC,QAAQ;IAC9C,qCACE,oCAAoC,QAAQ;IAC9C,4BAA4B,iCAAiC,QAAQ;IACrE,gCACE,yCAAyC,QAAQ;AACrD,GACC,MAAM,CACL,CAAA,OACE,KAAK,kCAAkC,KAAK,aAC5C,KAAK,mCAAmC,KAAK,aAC7C,KAAK,0BAA0B,KAAK,aACpC,KAAK,8BAA8B,KAAK,WAC1C;IACE,SACE;AACJ", "debugId": null}}, {"offset": {"line": 2032, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/api/modules/schemas/wood-params.ts"], "sourcesContent": ["import { nullToObjectTransform } from '@atlas/functions/zod/null-to-object-transform'\r\nimport { nullToUndefinedTransform } from '@atlas/functions/zod/null-to-undefined-transform'\r\nimport { z } from 'zod'\r\n\r\n// Wood material properties schema - matches your API response\r\nexport const woodMaterialPropertiesSchema = z.object({\r\n  category: z.string(), // \"CONIFER_AND_POPLAR_WOOD\"\r\n  woodName: z.string().optional(), // Wood name from API\r\n  characteristicBendingStrength: z.number(), // 24.0\r\n  characteristicShearStrength: z.number(), // 2.5\r\n  characteristicTensileStrength: z.number(), // 14.0\r\n  characteristicCompressiveStrength: z.number(), // 21.0\r\n  meanDensity: z.number(), // 380.0\r\n  meanShearModulus: z.number(), // 690.0\r\n  elasticityModulusParallelToGrain: z.number(), // 11000.0\r\n  meanElasticityModulus: z.number(), // 11000.0\r\n  partialMaterialFactor: z.number(), // 1.3\r\n})\r\n\r\n// Wood geometry schema - matches your API response\r\nexport const woodGeometryPropertiesSchema = z.object({\r\n  beamSectionWidth: z.number(), // 200.0\r\n  beamSectionHeight: z.number(), // 300.0\r\n  beamSpacing: z.number(), // 600.0\r\n  beamSpan: z.number(), // 4000.0\r\n  sectionModulus: z.number(), // 3000000.0\r\n  inertiaMomentAboutY: z.number(), // 450000000.0\r\n  serviceClass: z.string(), // \"SERVICE_CLASS_1\"\r\n  loadDuration: z.string(), // \"MEDIA\"\r\n  correctionFactor: z.number(), // 1.0\r\n  deformabilityFactor: z.number(), // 0.8\r\n  designBendingStrength: z.number(), // 18.5\r\n  designShearStrength: z.number(), // 1.9\r\n  elasticityInstantaneousModulus: z.number(), // 11000.0\r\n  longTermElasticityModulus: z.number(), // 7333.0\r\n})\r\n\r\n// Pre-intervention check schema - matches your API response\r\nexport const woodPreInterventionCheckSchema = z.object({\r\n  maximumBendingMoment: z.number(), // 50.0\r\n  maximumShearForce: z.number(), // 15.0\r\n  designBendingStress: z.number(), // 16.7\r\n  designBendingStrength: z.number(), // 18.5\r\n  bendingCheck: z.number(), // 0.9\r\n  designShearStress: z.number(), // 1.5\r\n  designShearStrength: z.number(), // 1.9\r\n  shearCheck: z.number(), // 0.79\r\n  permanentLoadPerLinearMeter: z.number(), // 2.5\r\n  imposedLoadPerLinearMeter: z.number(), // 4.0\r\n  instantaneousDeflectionPermanentLoad: z.number(), // 8.5\r\n  instantaneousDeflectionImposedLoad: z.number(), // 13.6\r\n  instantaneousDeflectionTotalLoads: z.number(), // 22.1\r\n  deformabilityCheck: z.number(), // 1.38\r\n  combinationFactor: z.number(), // 0.3\r\n  finalDeflectionTotalLoads: z.number(), // 26.2\r\n  finalCheckResult: z.number(), // 1.64\r\n})\r\n\r\n// Complete wood params schema - matches your full API response\r\nexport const woodParamsSchema = z.object({\r\n  initialDeformation: z\r\n    .number()\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToUndefinedTransform),\r\n  materialProperties: woodMaterialPropertiesSchema\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToObjectTransform),\r\n  geometry: woodGeometryPropertiesSchema\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToObjectTransform),\r\n  preIntervationCheck: woodPreInterventionCheckSchema\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToObjectTransform),\r\n  // Add postIntervationCheck to match API response structure\r\n  postIntervationCheck: z\r\n    .any()\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToObjectTransform),\r\n})\r\n\r\nexport type WoodMaterialProperties = z.infer<\r\n  typeof woodMaterialPropertiesSchema\r\n>\r\nexport type WoodGeometryProperties = z.infer<\r\n  typeof woodGeometryPropertiesSchema\r\n>\r\nexport type WoodPreInterventionCheck = z.infer<\r\n  typeof woodPreInterventionCheckSchema\r\n>\r\nexport type WoodParams = z.infer<typeof woodParamsSchema>\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;;;;AAGO,MAAM,+BAA+B,kLAAC,CAAC,MAAM,CAAC;IACnD,UAAU,kLAAC,CAAC,MAAM;IAClB,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;IAC7B,+BAA+B,kLAAC,CAAC,MAAM;IACvC,6BAA6B,kLAAC,CAAC,MAAM;IACrC,+BAA+B,kLAAC,CAAC,MAAM;IACvC,mCAAmC,kLAAC,CAAC,MAAM;IAC3C,aAAa,kLAAC,CAAC,MAAM;IACrB,kBAAkB,kLAAC,CAAC,MAAM;IAC1B,kCAAkC,kLAAC,CAAC,MAAM;IAC1C,uBAAuB,kLAAC,CAAC,MAAM;IAC/B,uBAAuB,kLAAC,CAAC,MAAM;AACjC;AAGO,MAAM,+BAA+B,kLAAC,CAAC,MAAM,CAAC;IACnD,kBAAkB,kLAAC,CAAC,MAAM;IAC1B,mBAAmB,kLAAC,CAAC,MAAM;IAC3B,aAAa,kLAAC,CAAC,MAAM;IACrB,UAAU,kLAAC,CAAC,MAAM;IAClB,gBAAgB,kLAAC,CAAC,MAAM;IACxB,qBAAqB,kLAAC,CAAC,MAAM;IAC7B,cAAc,kLAAC,CAAC,MAAM;IACtB,cAAc,kLAAC,CAAC,MAAM;IACtB,kBAAkB,kLAAC,CAAC,MAAM;IAC1B,qBAAqB,kLAAC,CAAC,MAAM;IAC7B,uBAAuB,kLAAC,CAAC,MAAM;IAC/B,qBAAqB,kLAAC,CAAC,MAAM;IAC7B,gCAAgC,kLAAC,CAAC,MAAM;IACxC,2BAA2B,kLAAC,CAAC,MAAM;AACrC;AAGO,MAAM,iCAAiC,kLAAC,CAAC,MAAM,CAAC;IACrD,sBAAsB,kLAAC,CAAC,MAAM;IAC9B,mBAAmB,kLAAC,CAAC,MAAM;IAC3B,qBAAqB,kLAAC,CAAC,MAAM;IAC7B,uBAAuB,kLAAC,CAAC,MAAM;IAC/B,cAAc,kLAAC,CAAC,MAAM;IACtB,mBAAmB,kLAAC,CAAC,MAAM;IAC3B,qBAAqB,kLAAC,CAAC,MAAM;IAC7B,YAAY,kLAAC,CAAC,MAAM;IACpB,6BAA6B,kLAAC,CAAC,MAAM;IACrC,2BAA2B,kLAAC,CAAC,MAAM;IACnC,sCAAsC,kLAAC,CAAC,MAAM;IAC9C,oCAAoC,kLAAC,CAAC,MAAM;IAC5C,mCAAmC,kLAAC,CAAC,MAAM;IAC3C,oBAAoB,kLAAC,CAAC,MAAM;IAC5B,mBAAmB,kLAAC,CAAC,MAAM;IAC3B,2BAA2B,kLAAC,CAAC,MAAM;IACnC,kBAAkB,kLAAC,CAAC,MAAM;AAC5B;AAGO,MAAM,mBAAmB,kLAAC,CAAC,MAAM,CAAC;IACvC,oBAAoB,kLAAC,CAClB,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,oBAAoB,6BACjB,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,qLAAqB;IAClC,UAAU,6BACP,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,qLAAqB;IAClC,qBAAqB,+BAClB,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,qLAAqB;IAClC,2DAA2D;IAC3D,sBAAsB,kLAAC,CACpB,GAAG,GACH,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,qLAAqB;AACpC", "debugId": null}}, {"offset": {"line": 2108, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/types/schemas/masonry/antioverturning-form.ts"], "sourcesContent": ["import {\r\n  MODULE_BUILDING_TYPE,\r\n  MODULE_EXECUTION_CLASS,\r\n  MODULE_FACING_MATERIAL,\r\n  MODULE_INFILL_WALL_TOPOLOGY,\r\n  MODULE_LOAD_RESISTING_CATEGORY,\r\n  MODULE_MATERIAL_KNOWLEDGE_LEVEL,\r\n  MODULE_SUBSOIL_CATEGORY,\r\n  MODULE_TOPOGRAPHIC_CATEGORY,\r\n} from '@atlas/constants/module'\r\nimport { nullToUndefinedTransform } from '@atlas/functions/zod/null-to-undefined-transform'\r\nimport type {\r\n  masonryAntiOverturningPostInterventionCalculationResultSchema,\r\n  masonryAntiOverturningPreInterventionCalculationResultSchema,\r\n  masonryAntiOverturningSeismicDemandCalculationResultSchema,\r\n  perimeterAndWidespreadInterventionCalculationResultResultSchema,\r\n  regionHypothesisSchema,\r\n  widespreadInterventionCalculationResultSchema,\r\n} from '@atlas/lib/api/modules/schemas/masonry-antioverturning-params'\r\nimport { productSchema } from '@atlas/lib/api/products/schemas/product'\r\nimport { z } from 'zod'\r\nimport { productFormSchema } from '../product-form-schema'\r\n\r\nexport const masonryAntiOverturningSiteCharacteristicsSchema = z.object({\r\n  seismicAccelerationAtSlv: z.number().optional().nullable(),\r\n  amplificationFactorAtSlv: z.number(),\r\n  subsoilCategory: z.enum(MODULE_SUBSOIL_CATEGORY),\r\n  ssCoefficient: z.number().optional().nullable(),\r\n  topographicCategory: z.enum(MODULE_TOPOGRAPHIC_CATEGORY),\r\n  stCoefficient: z.number().optional().nullable(),\r\n  subsoilCoefficient: z.number().optional().nullable(),\r\n})\r\n\r\nexport const masonryAntiOverturningBuildingCharacteristicsSchema = z.object({\r\n  buildingType: z.enum(MODULE_BUILDING_TYPE),\r\n  totalBuildingHeight: z.number().positive(),\r\n  buildingFundamentalPeriod: z.number(),\r\n  parameterA: z.number(),\r\n  parameterB: z.number(),\r\n  parameterAp: z.number(),\r\n})\r\n\r\nexport const masonryAntiOverturningMaterialPropertiesSchema = z.object({\r\n  infillWallTypology: z.enum(MODULE_INFILL_WALL_TOPOLOGY),\r\n  facingMaterial: z.enum(MODULE_FACING_MATERIAL),\r\n  knowledgeLevel: z.enum(MODULE_MATERIAL_KNOWLEDGE_LEVEL),\r\n  executionClass: z.enum(MODULE_EXECUTION_CLASS),\r\n  loadResistingCategory: z.enum(MODULE_LOAD_RESISTING_CATEGORY),\r\n  characteristicCompressiveStrength: z.number(),\r\n  infillWallElasticModulus: z.number(),\r\n  masonrySpecificWeightExternalFacing: z.number(),\r\n  masonrySpecificWeightInternalFacing: z.number(),\r\n  plasterSpecificWeight: z.number(),\r\n  confidenceFactor: z.number(),\r\n  masonryStrengthSafetyFactor: z.number(),\r\n  designCompressiveStrength: z.number(),\r\n  ultimateMasonryStrain: z.number(),\r\n})\r\n\r\nexport const masonryAntiOverturningInfillGeometrySchema = z.object({\r\n  panelWidth: z.number().positive(),\r\n  externalFacingThickness: z.number().positive(),\r\n  internalFacingThickness: z.number(),\r\n  singleSidePlasterThickness: z.number().positive(),\r\n  netPanelHeight: z.number().positive(),\r\n  panelHeightFromGroundLevel: z.number(),\r\n  panelCentroidFromGroundLevel: z.number(),\r\n  fundamentalPeriodPanel: z.number(),\r\n})\r\n\r\nexport const masonryAntiOverturningCompositeReinforcementSystemMeshInputSchema =\r\n  z.object({\r\n    reinforcedSidesNumber: z.number(),\r\n    resistantAreaPerMeter: z.number().positive().optional().nullable(),\r\n    meshProduct: productFormSchema,\r\n  })\r\n\r\nexport const masonryAntiOverturningCompositeReinforcementSystemConnectorInputSchema =\r\n  z.object({\r\n    connectorSpacing: z.number(),\r\n    connectorProduct: productFormSchema,\r\n  })\r\n\r\nexport const masonryAntiOverturningCompositeReinforcementSystemMatrixInputSchema =\r\n  z.object({\r\n    compositeSystemThickness: z.number(),\r\n    matrixProduct: productFormSchema,\r\n  })\r\n\r\nexport const masonryAntiOverturningCompositeReinforcementSystemMeshVerifyInputSchema =\r\n  z.object({\r\n    reinforcedSidesNumber: z.number(),\r\n    resistantAreaPerMeter: z.number().positive().optional().nullable(),\r\n    meshProduct: productSchema,\r\n  })\r\n\r\nexport const masonryAntiOverturningCompositeReinforcementSystemConnectorVerifyInputSchema =\r\n  z.object({\r\n    connectorSpacing: z.number(),\r\n    connectorProduct: productSchema,\r\n  })\r\n\r\nexport const masonryAntiOverturningCompositeReinforcementSystemMatrixVerifyInputSchema =\r\n  z.object({\r\n    compositeSystemThickness: z.number(),\r\n    matrixProduct: productSchema,\r\n  })\r\n\r\nexport const masonryAntiOverturningCompositeReinforcementSystemVerifyInputSchema =\r\n  z.object({\r\n    meshInput:\r\n      masonryAntiOverturningCompositeReinforcementSystemMeshVerifyInputSchema,\r\n    connectorInput:\r\n      masonryAntiOverturningCompositeReinforcementSystemConnectorVerifyInputSchema,\r\n    matrixInput:\r\n      masonryAntiOverturningCompositeReinforcementSystemMatrixVerifyInputSchema,\r\n  })\r\n\r\nexport const compositeReinforcementSystemInputSchema = z.object({\r\n  meshInput: masonryAntiOverturningCompositeReinforcementSystemMeshInputSchema,\r\n  connectorInput:\r\n    masonryAntiOverturningCompositeReinforcementSystemConnectorInputSchema,\r\n  matrixInput:\r\n    masonryAntiOverturningCompositeReinforcementSystemMatrixInputSchema,\r\n})\r\n\r\nexport const masonryAntiOverturningCompositeReinforcementSystemSchema =\r\n  z.object({\r\n    calculationType: z.literal('POST_INTERVENTION_VERIFY'),\r\n    input: compositeReinforcementSystemInputSchema,\r\n  })\r\n\r\nexport const masonryAntiOverturningParamsFormSchema = z.object({\r\n  siteCharacteristics: masonryAntiOverturningSiteCharacteristicsSchema\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToUndefinedTransform),\r\n  buildingCharacteristics: masonryAntiOverturningBuildingCharacteristicsSchema\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToUndefinedTransform),\r\n  materialProperties: masonryAntiOverturningMaterialPropertiesSchema\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToUndefinedTransform),\r\n  infillGeometry: masonryAntiOverturningInfillGeometrySchema\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToUndefinedTransform),\r\n})\r\n\r\nexport type MasonryAntiOverturningParamsFormSchemaInputs = z.infer<\r\n  typeof masonryAntiOverturningParamsFormSchema\r\n>\r\n\r\nexport type MasonryAntiOverturningSiteCharacteristicsSchemaInputs = z.infer<\r\n  typeof masonryAntiOverturningSiteCharacteristicsSchema\r\n>\r\nexport type MasonryAntiOverturningBuildingCharacteristicsSchemaInputs = z.infer<\r\n  typeof masonryAntiOverturningBuildingCharacteristicsSchema\r\n>\r\nexport type MasonryAntiOverturningMaterialPropertiesSchemaInputs = z.infer<\r\n  typeof masonryAntiOverturningMaterialPropertiesSchema\r\n>\r\nexport type MasonryAntiOverturningInfillGeometrySchemaInputs = z.infer<\r\n  typeof masonryAntiOverturningInfillGeometrySchema\r\n>\r\nexport type MasonryAntiOverturningCompositeReinforcementSystemMeshInputSchemaInputs =\r\n  z.infer<\r\n    typeof masonryAntiOverturningCompositeReinforcementSystemMeshInputSchema\r\n  >\r\n\r\nexport type MasonryAntiOverturningCompositeReinforcementSystemConnectorInputSchemaInputs =\r\n  z.infer<\r\n    typeof masonryAntiOverturningCompositeReinforcementSystemConnectorInputSchema\r\n  >\r\n\r\nexport type MasonryAntiOverturningCompositeReinforcementSystemMatrixInputSchemaInputs =\r\n  z.infer<\r\n    typeof masonryAntiOverturningCompositeReinforcementSystemMatrixInputSchema\r\n  >\r\n\r\nexport type CompositeReinforcementSystemInputSchemaInputs = z.infer<\r\n  typeof compositeReinforcementSystemInputSchema\r\n>\r\n\r\nexport type MasonryAntiOverturningCompositeReinforcementSystemSchemaInputs =\r\n  z.infer<typeof masonryAntiOverturningCompositeReinforcementSystemSchema>\r\n\r\nexport type MasonryAntiOverturningSeismicDemandCalculationResultSchemaInputs =\r\n  z.infer<typeof masonryAntiOverturningSeismicDemandCalculationResultSchema>\r\n\r\nexport type MasonryAntiOverturningPreInterventionCalculationResultSchemaInputs =\r\n  z.infer<typeof masonryAntiOverturningPreInterventionCalculationResultSchema>\r\n\r\nexport type MasonryAntiOverturningPostInterventionCalculationResultSchemaInputs =\r\n  z.infer<typeof masonryAntiOverturningPostInterventionCalculationResultSchema>\r\n\r\nexport type PerimeterAndWidespreadInterventionCalculationResultSchemaInputs =\r\n  z.infer<\r\n    typeof perimeterAndWidespreadInterventionCalculationResultResultSchema\r\n  >\r\n\r\nexport type WidespreadInterventionCalculationResultSchemaInputs = z.infer<\r\n  typeof widespreadInterventionCalculationResultSchema\r\n>\r\n\r\nexport type RegionHypothesisSchemaInputs = z.infer<\r\n  typeof regionHypothesisSchema\r\n>\r\n\r\nexport type MasonryAntiOverturningCompositeReinforcementSystemMeshVerifyInputSchemaInputs =\r\n  z.infer<\r\n    typeof masonryAntiOverturningCompositeReinforcementSystemMeshVerifyInputSchema\r\n  >\r\n\r\nexport type MasonryAntiOverturningCompositeReinforcementSystemConnectorVerifyInputSchemaInputs =\r\n  z.infer<\r\n    typeof masonryAntiOverturningCompositeReinforcementSystemConnectorVerifyInputSchema\r\n  >\r\n\r\nexport type MasonryAntiOverturningCompositeReinforcementSystemMatrixVerifyInputSchemaInputs =\r\n  z.infer<\r\n    typeof masonryAntiOverturningCompositeReinforcementSystemMatrixVerifyInputSchema\r\n  >\r\n\r\nexport type MasonryAntiOverturningCompositeReinforcementSystemVerifyInputSchemaInputs =\r\n  z.infer<\r\n    typeof masonryAntiOverturningCompositeReinforcementSystemVerifyInputSchema\r\n  >\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAUA;AASA;AACA;AACA;;;;;;AAEO,MAAM,kDAAkD,kLAAC,CAAC,MAAM,CAAC;IACtE,0BAA0B,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACxD,0BAA0B,kLAAC,CAAC,MAAM;IAClC,iBAAiB,kLAAC,CAAC,IAAI,CAAC,sJAAuB;IAC/C,eAAe,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC7C,qBAAqB,kLAAC,CAAC,IAAI,CAAC,0JAA2B;IACvD,eAAe,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC7C,oBAAoB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;AACpD;AAEO,MAAM,sDAAsD,kLAAC,CAAC,MAAM,CAAC;IAC1E,cAAc,kLAAC,CAAC,IAAI,CAAC,mJAAoB;IACzC,qBAAqB,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACxC,2BAA2B,kLAAC,CAAC,MAAM;IACnC,YAAY,kLAAC,CAAC,MAAM;IACpB,YAAY,kLAAC,CAAC,MAAM;IACpB,aAAa,kLAAC,CAAC,MAAM;AACvB;AAEO,MAAM,iDAAiD,kLAAC,CAAC,MAAM,CAAC;IACrE,oBAAoB,kLAAC,CAAC,IAAI,CAAC,0JAA2B;IACtD,gBAAgB,kLAAC,CAAC,IAAI,CAAC,qJAAsB;IAC7C,gBAAgB,kLAAC,CAAC,IAAI,CAAC,8JAA+B;IACtD,gBAAgB,kLAAC,CAAC,IAAI,CAAC,qJAAsB;IAC7C,uBAAuB,kLAAC,CAAC,IAAI,CAAC,6JAA8B;IAC5D,mCAAmC,kLAAC,CAAC,MAAM;IAC3C,0BAA0B,kLAAC,CAAC,MAAM;IAClC,qCAAqC,kLAAC,CAAC,MAAM;IAC7C,qCAAqC,kLAAC,CAAC,MAAM;IAC7C,uBAAuB,kLAAC,CAAC,MAAM;IAC/B,kBAAkB,kLAAC,CAAC,MAAM;IAC1B,6BAA6B,kLAAC,CAAC,MAAM;IACrC,2BAA2B,kLAAC,CAAC,MAAM;IACnC,uBAAuB,kLAAC,CAAC,MAAM;AACjC;AAEO,MAAM,6CAA6C,kLAAC,CAAC,MAAM,CAAC;IACjE,YAAY,kLAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,yBAAyB,kLAAC,CAAC,MAAM,GAAG,QAAQ;IAC5C,yBAAyB,kLAAC,CAAC,MAAM;IACjC,4BAA4B,kLAAC,CAAC,MAAM,GAAG,QAAQ;IAC/C,gBAAgB,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACnC,4BAA4B,kLAAC,CAAC,MAAM;IACpC,8BAA8B,kLAAC,CAAC,MAAM;IACtC,wBAAwB,kLAAC,CAAC,MAAM;AAClC;AAEO,MAAM,oEACX,kLAAC,CAAC,MAAM,CAAC;IACP,uBAAuB,kLAAC,CAAC,MAAM;IAC/B,uBAAuB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ;IAChE,aAAa,yKAAiB;AAChC;AAEK,MAAM,yEACX,kLAAC,CAAC,MAAM,CAAC;IACP,kBAAkB,kLAAC,CAAC,MAAM;IAC1B,kBAAkB,yKAAiB;AACrC;AAEK,MAAM,sEACX,kLAAC,CAAC,MAAM,CAAC;IACP,0BAA0B,kLAAC,CAAC,MAAM;IAClC,eAAe,yKAAiB;AAClC;AAEK,MAAM,0EACX,kLAAC,CAAC,MAAM,CAAC;IACP,uBAAuB,kLAAC,CAAC,MAAM;IAC/B,uBAAuB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ;IAChE,aAAa,oKAAa;AAC5B;AAEK,MAAM,+EACX,kLAAC,CAAC,MAAM,CAAC;IACP,kBAAkB,kLAAC,CAAC,MAAM;IAC1B,kBAAkB,oKAAa;AACjC;AAEK,MAAM,4EACX,kLAAC,CAAC,MAAM,CAAC;IACP,0BAA0B,kLAAC,CAAC,MAAM;IAClC,eAAe,oKAAa;AAC9B;AAEK,MAAM,sEACX,kLAAC,CAAC,MAAM,CAAC;IACP,WACE;IACF,gBACE;IACF,aACE;AACJ;AAEK,MAAM,0CAA0C,kLAAC,CAAC,MAAM,CAAC;IAC9D,WAAW;IACX,gBACE;IACF,aACE;AACJ;AAEO,MAAM,2DACX,kLAAC,CAAC,MAAM,CAAC;IACP,iBAAiB,kLAAC,CAAC,OAAO,CAAC;IAC3B,OAAO;AACT;AAEK,MAAM,yCAAyC,kLAAC,CAAC,MAAM,CAAC;IAC7D,qBAAqB,gDAClB,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,yBAAyB,oDACtB,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,oBAAoB,+CACjB,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,gBAAgB,2CACb,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;AACvC", "debugId": null}}, {"offset": {"line": 2241, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/types/schemas/masonry/crm-form.ts"], "sourcesContent": ["import {\r\n  MODULE_BINDER_MIXTURE_INJECTIONS,\r\n  MODULE_CRM_MASONRY_TYPE,\r\n  MODULE_ENHANCEMENT_CHARACTERISTICS,\r\n  MODULE_EXECUTION_CLASS,\r\n  MODULE_LOAD_RESISTING_CATEGORY,\r\n  MODULE_MATERIAL_KNOWLEDGE_LEVEL,\r\n  MODULE_REINFORCEMENT_APPLICATION_TYPE,\r\n} from '@atlas/constants/module'\r\nimport { nullToUndefinedTransform } from '@atlas/functions/zod/null-to-undefined-transform'\r\nimport { productSchema } from '@atlas/lib/api/products/schemas/product'\r\nimport z from 'zod'\r\nimport { productFormSchema } from '../product-form-schema'\r\n\r\nexport const existingMasonryCharacteristicsParamsSchema = z.object({\r\n  panelWidth: z.number().positive(),\r\n  panelThickness: z.number().positive(),\r\n  panelHeight: z.number().positive(),\r\n  masonryType: z.enum(MODULE_CRM_MASONRY_TYPE),\r\n  knowledgeLevel: z.enum(MODULE_MATERIAL_KNOWLEDGE_LEVEL),\r\n  confidenceFactor: z.number().optional().nullable(),\r\n  averageCompressiveStrength: z.number().optional().nullable(),\r\n  averageShearStrengthRegularMasonry: z.number().optional().nullable(),\r\n  averageShearStrengthIrregularMasonry: z.number().optional().nullable(),\r\n  averageNormalElasticityModulus: z.number().optional().nullable(),\r\n  averageShearElasticityModulus: z.number().optional().nullable(),\r\n  specificWeight: z.number().optional().nullable(),\r\n  enhancementCharacteristics: z.enum(MODULE_ENHANCEMENT_CHARACTERISTICS),\r\n  correctionCoefficient: z.number().optional().nullable(),\r\n  amplifiedAverageCompressiveStrength: z.number().optional().nullable(),\r\n  amplifiedAverageShearStrengthRegularMasonry: z.number().optional().nullable(),\r\n  amplifiedAverageShearStrengthIrregularMasonry: z\r\n    .number()\r\n    .optional()\r\n    .nullable(),\r\n  amplifiedAverageNormalElasticityModulus: z.number().optional().nullable(),\r\n  amplifiedAverageShearElasticityModulus: z.number().optional().nullable(),\r\n})\r\n\r\nexport const planeBendingStrengthCalculationResultSchema = z.object({\r\n  compressedFlangeNeutralAxisDistance: z.number().optional().nullable(),\r\n  inOrOutplaneBendingMoment: z.number().optional().nullable(),\r\n})\r\n\r\nexport const inPlaneShearStrengthCalculationResultSchema = z.object({\r\n  verticalStress: z.number().optional().nullable(),\r\n  wallSlendernessCorrectionCoefficient: z.number().optional().nullable(),\r\n  shearStrength: z.number().optional().nullable(),\r\n})\r\n\r\nexport const designStrengthPreInterventionCalculationResultSchema = z.object({\r\n  executionClass: z.enum(MODULE_EXECUTION_CLASS),\r\n  // structuralElementsCategory is the same as loadResistingCategory but for CRM\r\n  structuralElementsCategory: z.enum(MODULE_LOAD_RESISTING_CATEGORY),\r\n  masonryStrengthSafetyFactor: z.number().optional().nullable(),\r\n  designCompressiveStrength: z.number().optional().nullable(),\r\n  designShearStrengthRegularMasonry: z.number().optional().nullable(),\r\n  designShearStrengthIrregularMasonry: z.number().optional().nullable(),\r\n  designNormalElasticityModulus: z.number().optional().nullable(),\r\n  designShearElasticityModulus: z.number().optional().nullable(),\r\n  ultimateCompressiveStrainLinearBehavior: z.number().optional().nullable(),\r\n  ultimateCompressiveStrain: z.number().optional().nullable(),\r\n  firstCoefficient: z.number().optional().nullable(),\r\n  panelSelfWeight: z.number().optional().nullable(),\r\n  inPlaneBendingStrengthCalculationResult:\r\n    planeBendingStrengthCalculationResultSchema.optional().nullable(),\r\n  outOfPlaneBendingStrengthCalculationResult:\r\n    planeBendingStrengthCalculationResultSchema.optional().nullable(),\r\n  inPlaneShearStrengthCalculationResult:\r\n    inPlaneShearStrengthCalculationResultSchema.optional().nullable(),\r\n})\r\n\r\nexport const crmPreInterventionSchema = z.object({\r\n  existingMasonryCharacteristicsParams:\r\n    existingMasonryCharacteristicsParamsSchema\r\n      .optional()\r\n      .nullable()\r\n      .transform(nullToUndefinedTransform),\r\n  designStrengthPreInterventionCalculationResult:\r\n    designStrengthPreInterventionCalculationResultSchema\r\n      .optional()\r\n      .nullable()\r\n      .transform(nullToUndefinedTransform),\r\n})\r\n\r\nexport const reinforcedMasonryCharacteristicsParamsSchema = z.object({\r\n  reinforcementApplicationType: z.enum(MODULE_REINFORCEMENT_APPLICATION_TYPE),\r\n  singleFaceApplicationReductionCoefficient: z.number(),\r\n  reinforcedPlasterCoefficient: z.number().optional().nullable(),\r\n  binderMixturesInjections: z.enum(MODULE_BINDER_MIXTURE_INJECTIONS),\r\n  correctionCoefficient: z.number().optional().nullable(),\r\n  overallAmplificationCoefficient: z.number().optional().nullable(),\r\n  amplifiedAverageCompressiveStrength: z.number().optional().nullable(),\r\n  amplifiedAverageShearStrengthRegularMasonry: z.number().optional().nullable(),\r\n  amplifiedAverageShearStrengthIrregularMasonry: z\r\n    .number()\r\n    .optional()\r\n    .nullable(),\r\n  amplifiedAverageNormalElasticityModulus: z.number().optional().nullable(),\r\n  amplifiedAverageShearElasticityModulus: z.number().optional().nullable(),\r\n})\r\n\r\nexport const crmProductInputMeshSchema = z.object({\r\n  meshProduct: productFormSchema,\r\n})\r\n\r\nexport const crmProductInputConnectorSchema = z.object({\r\n  connectorProduct: productFormSchema,\r\n})\r\n\r\nexport const crmProductInputMatrixSchema = z.object({\r\n  matrixProduct: productFormSchema,\r\n})\r\n\r\nexport const masonryCrmDesignStrengthPostInterventionMeshVerifyInputSchema =\r\n  z.object({\r\n    meshProduct: productSchema,\r\n  })\r\nexport const masonryCrmDesignStrengthPostInterventionConnectorVerifyInputSchema =\r\n  z.object({\r\n    connectorProduct: productSchema,\r\n  })\r\nexport const masonryCrmDesignStrengthPostInterventionMatrixVerifyInputSchema =\r\n  z.object({\r\n    matrixProduct: productSchema,\r\n  })\r\n\r\nexport const masonryCrmDesignStrengthPostInterventionVerifyInputSchema =\r\n  z.object({\r\n    meshInput: masonryCrmDesignStrengthPostInterventionMeshVerifyInputSchema,\r\n    connectorInput:\r\n      masonryCrmDesignStrengthPostInterventionConnectorVerifyInputSchema,\r\n    matrixInput:\r\n      masonryCrmDesignStrengthPostInterventionMatrixVerifyInputSchema,\r\n    reinforcementTotalThickness: z.number().optional().nullable(),\r\n  })\r\n\r\nexport const designStrengthPostInterventionVerifyInputSchema = z.object({\r\n  meshInput: crmProductInputMeshSchema,\r\n  connectorInput: crmProductInputConnectorSchema,\r\n  matrixInput: crmProductInputMatrixSchema,\r\n  reinforcementTotalThickness: z.number().optional().nullable(),\r\n})\r\n\r\nexport const designStrengthPostInterventionInputSchema = z.object({\r\n  calculationType: z.literal('DESIGN_STRENGTH_VERIFY'),\r\n  input: designStrengthPostInterventionVerifyInputSchema,\r\n})\r\n\r\nexport const designStrengthPostInterventionCalculationResultSchema = z.object({\r\n  panelThickness: z.number().optional().nullable(),\r\n  designCompressiveStrength: z.number().optional().nullable(),\r\n  designShearStrengthRegularMasonry: z.number().optional().nullable(),\r\n  designShearStrengthIrregularMasonry: z.number().optional().nullable(),\r\n  designNormalElasticityModulus: z.number().optional().nullable(),\r\n  designShearElasticityModulus: z.number().optional().nullable(),\r\n  ultimateCompressiveStrainLinearBehavior: z.number().optional().nullable(),\r\n  secondCoefficient: z.number().optional().nullable(),\r\n  panelSelfWeight: z.number().optional().nullable(),\r\n  inPlaneBendingStrengthCalculationResult:\r\n    planeBendingStrengthCalculationResultSchema.optional().nullable(),\r\n  outOfPlaneBendingStrengthCalculationResult:\r\n    planeBendingStrengthCalculationResultSchema.optional().nullable(),\r\n  inPlaneShearStrengthCalculationResult:\r\n    inPlaneShearStrengthCalculationResultSchema.optional().nullable(),\r\n})\r\n\r\nexport const crmPostInterventionSchema = z.object({\r\n  reinforcedMasonryCharacteristicsParams:\r\n    reinforcedMasonryCharacteristicsParamsSchema\r\n      .optional()\r\n      .nullable()\r\n      .transform(nullToUndefinedTransform),\r\n  designStrengthPostInterventionVerifyInput:\r\n    masonryCrmDesignStrengthPostInterventionVerifyInputSchema\r\n      .optional()\r\n      .nullable()\r\n      .transform(nullToUndefinedTransform),\r\n  designStrengthPostInterventionCalculationResult:\r\n    designStrengthPostInterventionCalculationResultSchema\r\n      .optional()\r\n      .nullable()\r\n      .transform(nullToUndefinedTransform),\r\n})\r\n\r\nexport const crmFormSchema = z.object({\r\n  preIntervention: crmPreInterventionSchema\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToUndefinedTransform),\r\n  postIntervention: crmPostInterventionSchema\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToUndefinedTransform),\r\n})\r\n\r\nexport type MasonryCrmDesignStrengthPostInterventionVerifyInputSchemaInputs =\r\n  z.infer<typeof masonryCrmDesignStrengthPostInterventionVerifyInputSchema>\r\nexport type DesignStrengthPostInterventionCalculationResultSchemaInputs =\r\n  z.infer<typeof designStrengthPostInterventionCalculationResultSchema>\r\nexport type DesignStrengthPostInterventionInputSchemaInputs = z.infer<\r\n  typeof designStrengthPostInterventionInputSchema\r\n>\r\nexport type DesignStrengthPostInterventionVerifyInputSchemaInputs = z.infer<\r\n  typeof designStrengthPostInterventionVerifyInputSchema\r\n>\r\nexport type ExistingMasonryCharacteristicsParamsSchemaInputs = z.infer<\r\n  typeof existingMasonryCharacteristicsParamsSchema\r\n>\r\n\r\nexport type PlaneBendingStrengthCalculationResultSchemaInputs = z.infer<\r\n  typeof planeBendingStrengthCalculationResultSchema\r\n>\r\n\r\nexport type InPlaneShearStrengthCalculationResultSchemaInputs = z.infer<\r\n  typeof inPlaneShearStrengthCalculationResultSchema\r\n>\r\n\r\nexport type DesignStrengthPreInterventionCalculationResultSchemaInputs =\r\n  z.infer<typeof designStrengthPreInterventionCalculationResultSchema>\r\n\r\nexport type ReinforcedMasonryCharacteristicsParamsSchemaInputs = z.infer<\r\n  typeof reinforcedMasonryCharacteristicsParamsSchema\r\n>\r\n\r\nexport type CrmPreInterventionSchemaInputs = z.infer<\r\n  typeof crmPreInterventionSchema\r\n>\r\n\r\nexport type CrmFormSchemaInputs = z.infer<typeof crmFormSchema>\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AASA;AACA;AACA;AACA;;;;;;AAEO,MAAM,6CAA6C,uJAAC,CAAC,MAAM,CAAC;IACjE,YAAY,uJAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,gBAAgB,uJAAC,CAAC,MAAM,GAAG,QAAQ;IACnC,aAAa,uJAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,aAAa,uJAAC,CAAC,IAAI,CAAC,sJAAuB;IAC3C,gBAAgB,uJAAC,CAAC,IAAI,CAAC,8JAA+B;IACtD,kBAAkB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAChD,4BAA4B,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC1D,oCAAoC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAClE,sCAAsC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACpE,gCAAgC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC9D,+BAA+B,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC7D,gBAAgB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC9C,4BAA4B,uJAAC,CAAC,IAAI,CAAC,iKAAkC;IACrE,uBAAuB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACrD,qCAAqC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACnE,6CAA6C,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC3E,+CAA+C,uJAAC,CAC7C,MAAM,GACN,QAAQ,GACR,QAAQ;IACX,yCAAyC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACvE,wCAAwC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;AACxE;AAEO,MAAM,8CAA8C,uJAAC,CAAC,MAAM,CAAC;IAClE,qCAAqC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACnE,2BAA2B,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;AAC3D;AAEO,MAAM,8CAA8C,uJAAC,CAAC,MAAM,CAAC;IAClE,gBAAgB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC9C,sCAAsC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACpE,eAAe,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;AAC/C;AAEO,MAAM,uDAAuD,uJAAC,CAAC,MAAM,CAAC;IAC3E,gBAAgB,uJAAC,CAAC,IAAI,CAAC,qJAAsB;IAC7C,8EAA8E;IAC9E,4BAA4B,uJAAC,CAAC,IAAI,CAAC,6JAA8B;IACjE,6BAA6B,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC3D,2BAA2B,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACzD,mCAAmC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACjE,qCAAqC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACnE,+BAA+B,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC7D,8BAA8B,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC5D,yCAAyC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACvE,2BAA2B,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACzD,kBAAkB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAChD,iBAAiB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC/C,yCACE,4CAA4C,QAAQ,GAAG,QAAQ;IACjE,4CACE,4CAA4C,QAAQ,GAAG,QAAQ;IACjE,uCACE,4CAA4C,QAAQ,GAAG,QAAQ;AACnE;AAEO,MAAM,2BAA2B,uJAAC,CAAC,MAAM,CAAC;IAC/C,sCACE,2CACG,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACvC,gDACE,qDACG,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;AACzC;AAEO,MAAM,+CAA+C,uJAAC,CAAC,MAAM,CAAC;IACnE,8BAA8B,uJAAC,CAAC,IAAI,CAAC,oKAAqC;IAC1E,2CAA2C,uJAAC,CAAC,MAAM;IACnD,8BAA8B,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC5D,0BAA0B,uJAAC,CAAC,IAAI,CAAC,+JAAgC;IACjE,uBAAuB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACrD,iCAAiC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC/D,qCAAqC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACnE,6CAA6C,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC3E,+CAA+C,uJAAC,CAC7C,MAAM,GACN,QAAQ,GACR,QAAQ;IACX,yCAAyC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACvE,wCAAwC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;AACxE;AAEO,MAAM,4BAA4B,uJAAC,CAAC,MAAM,CAAC;IAChD,aAAa,yKAAiB;AAChC;AAEO,MAAM,iCAAiC,uJAAC,CAAC,MAAM,CAAC;IACrD,kBAAkB,yKAAiB;AACrC;AAEO,MAAM,8BAA8B,uJAAC,CAAC,MAAM,CAAC;IAClD,eAAe,yKAAiB;AAClC;AAEO,MAAM,gEACX,uJAAC,CAAC,MAAM,CAAC;IACP,aAAa,oKAAa;AAC5B;AACK,MAAM,qEACX,uJAAC,CAAC,MAAM,CAAC;IACP,kBAAkB,oKAAa;AACjC;AACK,MAAM,kEACX,uJAAC,CAAC,MAAM,CAAC;IACP,eAAe,oKAAa;AAC9B;AAEK,MAAM,4DACX,uJAAC,CAAC,MAAM,CAAC;IACP,WAAW;IACX,gBACE;IACF,aACE;IACF,6BAA6B,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;AAC7D;AAEK,MAAM,kDAAkD,uJAAC,CAAC,MAAM,CAAC;IACtE,WAAW;IACX,gBAAgB;IAChB,aAAa;IACb,6BAA6B,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;AAC7D;AAEO,MAAM,4CAA4C,uJAAC,CAAC,MAAM,CAAC;IAChE,iBAAiB,uJAAC,CAAC,OAAO,CAAC;IAC3B,OAAO;AACT;AAEO,MAAM,wDAAwD,uJAAC,CAAC,MAAM,CAAC;IAC5E,gBAAgB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC9C,2BAA2B,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACzD,mCAAmC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACjE,qCAAqC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACnE,+BAA+B,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC7D,8BAA8B,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC5D,yCAAyC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACvE,mBAAmB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACjD,iBAAiB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC/C,yCACE,4CAA4C,QAAQ,GAAG,QAAQ;IACjE,4CACE,4CAA4C,QAAQ,GAAG,QAAQ;IACjE,uCACE,4CAA4C,QAAQ,GAAG,QAAQ;AACnE;AAEO,MAAM,4BAA4B,uJAAC,CAAC,MAAM,CAAC;IAChD,wCACE,6CACG,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACvC,2CACE,0DACG,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACvC,iDACE,sDACG,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;AACzC;AAEO,MAAM,gBAAgB,uJAAC,CAAC,MAAM,CAAC;IACpC,iBAAiB,yBACd,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,kBAAkB,0BACf,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;AACvC", "debugId": null}}, {"offset": {"line": 2415, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/types/schemas/masonry/frcm-column-form.ts"], "sourcesContent": ["import {\r\n  MODULE_CRM_MASONRY_TYPE,\r\n  MODULE_EXECUTION_CLASS,\r\n  MODULE_FACING_MATERIAL,\r\n  MODULE_FRM_GEOMETRY_TOPOLOGY,\r\n  MODULE_GEOMETRY_EXPOSURE,\r\n  MODULE_LOAD_RESISTING_CATEGORY,\r\n  MODULE_MATERIAL_KNOWLEDGE_LEVEL,\r\n  REINFORCEMENT_ARRANGEMENT,\r\n} from '@atlas/constants/module'\r\nimport { nullToUndefinedTransform } from '@atlas/functions/zod/null-to-undefined-transform'\r\nimport { productSchema } from '@atlas/lib/api/products/schemas/product'\r\nimport z from 'zod'\r\nimport { productFormSchema } from '../product-form-schema'\r\n\r\nexport const frcmColumnGeometrySchema = z.object({\r\n  topology: z.enum(MODULE_FRM_GEOMETRY_TOPOLOGY),\r\n  largerSizeOrColumnDiameter: z.number(),\r\n  smallerSize: z.number(),\r\n  crossSectionArea: z.number(),\r\n  crossSectionDiagonal: z.number(),\r\n  cornerRoundingRadius: z.number(),\r\n})\r\n\r\nexport const frcmColumnMasonryCharacteristicsSchema = z.object({\r\n  material: z.enum(MODULE_FACING_MATERIAL),\r\n  enhancementCharacteristics: z.enum(MODULE_CRM_MASONRY_TYPE),\r\n  knowledgeLevel: z.enum(MODULE_MATERIAL_KNOWLEDGE_LEVEL).optional().nullable(),\r\n  confidenceFactor: z.number().optional().nullable(),\r\n  executionClass: z.enum(MODULE_EXECUTION_CLASS),\r\n  loadResistantCategory: z.enum(MODULE_LOAD_RESISTING_CATEGORY),\r\n  masonrySafetyFactor: z.number().optional().nullable(),\r\n  characteristicCompressiveStrength: z.number().optional().nullable(),\r\n  designCompressiveStrength: z.number().optional().nullable(),\r\n  characteristicShearStrength: z.number().optional().nullable(),\r\n  designShearStrength: z.number().optional().nullable(),\r\n  normalElasticityModulus: z.number().optional().nullable(),\r\n  shearElasticityModulus: z.number().optional().nullable(),\r\n  masonryDensity: z.number().optional().nullable(),\r\n  exposureType: z.enum(MODULE_GEOMETRY_EXPOSURE).optional().nullable(),\r\n  conversionFactor: z.number().optional().nullable(),\r\n})\r\n\r\nexport const frcmColumnStressSchema = z.object({\r\n  normalStressCenteredStressing: z.number().optional().nullable(),\r\n})\r\n\r\nexport const frcmColumnParamsSchema = z.object({\r\n  geometry: frcmColumnGeometrySchema\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  masonryCharacteristics: frcmColumnMasonryCharacteristicsSchema\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  stress: frcmColumnStressSchema\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n})\r\n\r\nexport const confinementReinforcementVerifyInput = z.object({\r\n  product: productSchema,\r\n  reinforcedArrangement: z.enum(REINFORCEMENT_ARRANGEMENT),\r\n  singleWidthBand: z.number(),\r\n  stepsOfTheBand: z.number(),\r\n  clearDistanceBetweenStripes: z.number(),\r\n  minimalTransversalDimension: z.number(),\r\n  numberOfReinforcementLayers: z.number(),\r\n  matrixThicknessOfTheSingleLayer: z.number(),\r\n})\r\n\r\nexport const confinementReinforcementInput = z.object({\r\n  product: productFormSchema,\r\n  reinforcedArrangement: z.enum(REINFORCEMENT_ARRANGEMENT),\r\n  singleWidthBand: z.number(),\r\n  stepsOfTheBand: z.number(),\r\n  clearDistanceBetweenStripes: z.number(),\r\n  minimalTransversalDimension: z.number(),\r\n  numberOfReinforcementLayers: z.number(),\r\n  matrixThicknessOfTheSingleLayer: z.number(),\r\n})\r\n\r\nexport const frcmColumnConfinementReinforcementInput = z.object({\r\n  calculationType: z.literal('CONFINEMENT_VERIFY'),\r\n  input: confinementReinforcementInput,\r\n})\r\n\r\nexport const nonReinforcedSectionSchema = z.object({\r\n  normalStressStrength: z.number().optional().nullable(),\r\n  designAxialResistance: z.number().optional().nullable(),\r\n  check: z.boolean().optional().nullable(),\r\n})\r\n\r\nexport const reinforcedSectionSchema = z.object({\r\n  coefficientOfResistanceIncrease: z.number().optional().nullable(),\r\n  confinedColumnDesignResistanceWithFrcm: z.number().optional().nullable(),\r\n  designAxialResistanceOfTheConfinedColumnWithFrcm: z\r\n    .number()\r\n    .optional()\r\n    .nullable(),\r\n  check: z.boolean().optional().nullable(),\r\n})\r\n\r\nexport const confinementCheckResultSchema = z.object({\r\n  nonReinforcedSection: nonReinforcedSectionSchema\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToUndefinedTransform),\r\n  reinforcedSection: reinforcedSectionSchema\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToUndefinedTransform),\r\n})\r\n\r\nexport const confinementReinforcementCalculationResult = z.object({\r\n  matrixGeometricPercentage: z.number().optional().nullable(),\r\n  reinforcementGeometricPercentage: z.number().optional().nullable(),\r\n  coefficicentOfHorizontalEfficiency: z.number().optional().nullable(),\r\n  coefficicentOfVerticalEfficiency: z.number().optional().nullable(),\r\n  coefficientOfOverallEfficiency: z.number().optional().nullable(),\r\n  coefficientOfEffectivenessOfTheMatrix: z.number().optional().nullable(),\r\n  computationalStrainOfTheComposite: z.number().optional().nullable(),\r\n  confiningPressure: z.number().optional().nullable(),\r\n  effectiveConfiningPressure: z.number().optional().nullable(),\r\n  confinementCheck: confinementCheckResultSchema\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToUndefinedTransform),\r\n})\r\n\r\nexport type ConfinementReinforcementVerifyInputSchema = z.infer<\r\n  typeof confinementReinforcementVerifyInput\r\n>\r\n\r\nexport type ConfinementReinforcementInputSchema = z.infer<\r\n  typeof confinementReinforcementInput\r\n>\r\nexport type ConfinementReinforcementCalculationResultSchema = z.infer<\r\n  typeof confinementReinforcementCalculationResult\r\n>\r\nexport type frcmColumnConfinementReinforcementSchema = z.infer<\r\n  typeof frcmColumnConfinementReinforcementInput\r\n>\r\n\r\nexport type FrcmColumnParamsSchemaInput = z.infer<typeof frcmColumnParamsSchema>\r\nexport type FrcmColumnGeometryInput = z.infer<typeof frcmColumnGeometrySchema>\r\nexport type FrcmColumnMasonryCharacteristicsInput = z.infer<\r\n  typeof frcmColumnMasonryCharacteristicsSchema\r\n>\r\nexport type FrcmColumnStressInput = z.infer<typeof frcmColumnStressSchema>\r\n\r\nexport type NonReinforcedSectionSchema = z.infer<\r\n  typeof nonReinforcedSectionSchema\r\n>\r\nexport type ReinforcedSectionSchema = z.infer<typeof reinforcedSectionSchema>\r\nexport type ConfinementCheckResultSchema = z.infer<\r\n  typeof confinementCheckResultSchema\r\n>\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAUA;AACA;AACA;AACA;;;;;;AAEO,MAAM,2BAA2B,uJAAC,CAAC,MAAM,CAAC;IAC/C,UAAU,uJAAC,CAAC,IAAI,CAAC,2JAA4B;IAC7C,4BAA4B,uJAAC,CAAC,MAAM;IACpC,aAAa,uJAAC,CAAC,MAAM;IACrB,kBAAkB,uJAAC,CAAC,MAAM;IAC1B,sBAAsB,uJAAC,CAAC,MAAM;IAC9B,sBAAsB,uJAAC,CAAC,MAAM;AAChC;AAEO,MAAM,yCAAyC,uJAAC,CAAC,MAAM,CAAC;IAC7D,UAAU,uJAAC,CAAC,IAAI,CAAC,qJAAsB;IACvC,4BAA4B,uJAAC,CAAC,IAAI,CAAC,sJAAuB;IAC1D,gBAAgB,uJAAC,CAAC,IAAI,CAAC,8JAA+B,EAAE,QAAQ,GAAG,QAAQ;IAC3E,kBAAkB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAChD,gBAAgB,uJAAC,CAAC,IAAI,CAAC,qJAAsB;IAC7C,uBAAuB,uJAAC,CAAC,IAAI,CAAC,6JAA8B;IAC5D,qBAAqB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACnD,mCAAmC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACjE,2BAA2B,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACzD,6BAA6B,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC3D,qBAAqB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACnD,yBAAyB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACvD,wBAAwB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACtD,gBAAgB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC9C,cAAc,uJAAC,CAAC,IAAI,CAAC,uJAAwB,EAAE,QAAQ,GAAG,QAAQ;IAClE,kBAAkB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;AAClD;AAEO,MAAM,yBAAyB,uJAAC,CAAC,MAAM,CAAC;IAC7C,+BAA+B,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;AAC/D;AAEO,MAAM,yBAAyB,uJAAC,CAAC,MAAM,CAAC;IAC7C,UAAU,yBACP,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,wBAAwB,uCACrB,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,QAAQ,uBACL,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;AACvC;AAEO,MAAM,sCAAsC,uJAAC,CAAC,MAAM,CAAC;IAC1D,SAAS,oKAAa;IACtB,uBAAuB,uJAAC,CAAC,IAAI,CAAC,wJAAyB;IACvD,iBAAiB,uJAAC,CAAC,MAAM;IACzB,gBAAgB,uJAAC,CAAC,MAAM;IACxB,6BAA6B,uJAAC,CAAC,MAAM;IACrC,6BAA6B,uJAAC,CAAC,MAAM;IACrC,6BAA6B,uJAAC,CAAC,MAAM;IACrC,iCAAiC,uJAAC,CAAC,MAAM;AAC3C;AAEO,MAAM,gCAAgC,uJAAC,CAAC,MAAM,CAAC;IACpD,SAAS,yKAAiB;IAC1B,uBAAuB,uJAAC,CAAC,IAAI,CAAC,wJAAyB;IACvD,iBAAiB,uJAAC,CAAC,MAAM;IACzB,gBAAgB,uJAAC,CAAC,MAAM;IACxB,6BAA6B,uJAAC,CAAC,MAAM;IACrC,6BAA6B,uJAAC,CAAC,MAAM;IACrC,6BAA6B,uJAAC,CAAC,MAAM;IACrC,iCAAiC,uJAAC,CAAC,MAAM;AAC3C;AAEO,MAAM,0CAA0C,uJAAC,CAAC,MAAM,CAAC;IAC9D,iBAAiB,uJAAC,CAAC,OAAO,CAAC;IAC3B,OAAO;AACT;AAEO,MAAM,6BAA6B,uJAAC,CAAC,MAAM,CAAC;IACjD,sBAAsB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACpD,uBAAuB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACrD,OAAO,uJAAC,CAAC,OAAO,GAAG,QAAQ,GAAG,QAAQ;AACxC;AAEO,MAAM,0BAA0B,uJAAC,CAAC,MAAM,CAAC;IAC9C,iCAAiC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC/D,wCAAwC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACtE,kDAAkD,uJAAC,CAChD,MAAM,GACN,QAAQ,GACR,QAAQ;IACX,OAAO,uJAAC,CAAC,OAAO,GAAG,QAAQ,GAAG,QAAQ;AACxC;AAEO,MAAM,+BAA+B,uJAAC,CAAC,MAAM,CAAC;IACnD,sBAAsB,2BACnB,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,mBAAmB,wBAChB,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;AACvC;AAEO,MAAM,4CAA4C,uJAAC,CAAC,MAAM,CAAC;IAChE,2BAA2B,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACzD,kCAAkC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAChE,oCAAoC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAClE,kCAAkC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAChE,gCAAgC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC9D,uCAAuC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACrE,mCAAmC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACjE,mBAAmB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACjD,4BAA4B,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC1D,kBAAkB,6BACf,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;AACvC", "debugId": null}}, {"offset": {"line": 2538, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/types/schemas/masonry/maschi-murari-form.ts"], "sourcesContent": ["import {\r\n  MODULE_CRM_MASONRY_TYPE,\r\n  MODULE_EXECUTION_CLASS,\r\n  MODULE_FACING_MATERIAL,\r\n  MODULE_GEOMETRY_EXPOSURE,\r\n  MODULE_LOAD_RESISTING_CATEGORY,\r\n  MODULE_MATERIAL_KNOWLEDGE_LEVEL,\r\n  REI<PERSON>ORCEMENT_ARRANGEMENT,\r\n  REINFORCEMENT_FAILURE_MODE,\r\n} from '@atlas/constants/module'\r\nimport { nullToUndefinedTransform } from '@atlas/functions/zod/null-to-undefined-transform'\r\nimport { productSchema } from '@atlas/lib/api/products/schemas/product'\r\nimport z from 'zod'\r\nimport { productFormSchema } from '../product-form-schema'\r\n\r\nexport const maschiMurariPanelGeometrySchema = z.object({\r\n  height: z.number().positive().optional().nullable(),\r\n  width: z.number().positive().optional().nullable(),\r\n  thickness: z.number().positive().optional().nullable(),\r\n})\r\n\r\nexport const maschiMurariMaterialPropertiesSchema = z.object({\r\n  structuralElementsNature: z.enum(MODULE_FACING_MATERIAL),\r\n  masonryType: z.enum(MODULE_CRM_MASONRY_TYPE),\r\n  knowledgeLevel: z.enum(MODULE_MATERIAL_KNOWLEDGE_LEVEL),\r\n  confidenceFactor: z.number().optional().nullable(),\r\n  executionClass: z.enum(MODULE_EXECUTION_CLASS),\r\n  loadResistantCategory: z.enum(MODULE_LOAD_RESISTING_CATEGORY),\r\n  masonrySafetyFactor: z.number().optional().nullable(),\r\n  characteristicCompressiveStrength: z.number().optional().nullable(),\r\n  designCompressiveStrength: z.number().optional().nullable(),\r\n  characteristicShearStrength: z.number().optional().nullable(),\r\n  designShearStrength: z.number().optional().nullable(),\r\n  elasticModulus: z.number().optional().nullable(),\r\n  shearModulus: z.number().optional().nullable(),\r\n  ultimateCompressiveStrainLinearBehaviour: z.number().optional().nullable(),\r\n  ultimateCompressiveStrain: z.number().optional().nullable(),\r\n  exposure: z.enum(MODULE_GEOMETRY_EXPOSURE),\r\n  conversionFactor: z.number().optional().nullable(),\r\n})\r\n\r\nexport const maschiMurariActionsSchema = z.object({\r\n  appliedNormalStress: z.number().optional().nullable(),\r\n  inPlaneBendingMoment: z.number().optional().nullable(),\r\n  outOfPlaneBendingMoment: z.number().optional().nullable(),\r\n  inPlaneAppliedShear: z.number().optional().nullable(),\r\n  outOfPlaneAppliedShear: z.number().optional().nullable(),\r\n})\r\n\r\nexport const maschiMurariParamsSchema = z.object({\r\n  panelGeometry: maschiMurariPanelGeometrySchema\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  materialProperties: maschiMurariMaterialPropertiesSchema\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  actions: maschiMurariActionsSchema\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n})\r\n\r\nexport const maschiMurariShearReinforcementVerifyExecutionSchema = z.object({\r\n  product: productSchema,\r\n  reinforcementFailureMode: z.enum(REINFORCEMENT_FAILURE_MODE),\r\n  designReinforcementStress: z.number(),\r\n  designReinforcementStrain: z.number(),\r\n  reinforcedArrangement: z.enum(REINFORCEMENT_ARRANGEMENT),\r\n  singleStripWidth: z.number(),\r\n  stripSpacing: z.number(),\r\n  totalReinforcementWidthPerpendicularShearDirection: z.number(),\r\n  compressedEdgeReinforcementFiberDistance: z.number(),\r\n  layersNumber: z.number(),\r\n  reinforcedSidesNumber: z.number(),\r\n})\r\n\r\nexport const maschiMurariFlexuralReinforcementVerifyExecutionSchema = z.object({\r\n  // For in-plane bending\r\n  product: productSchema,\r\n  sectionFailureMode: z.enum(REINFORCEMENT_FAILURE_MODE),\r\n  designReinforcementStress: z.number(),\r\n  designReinforcementStrain: z.number(),\r\n  reinforcedArrangement: z.enum(REINFORCEMENT_ARRANGEMENT),\r\n  singleStripWidth: z.number(),\r\n  stripSpacing: z.number(),\r\n  reinforcementTotalWidthAlongLength: z.number(),\r\n  compressedEdgeReinforcementFiberDistance: z.number(),\r\n  layersNumber: z.number(),\r\n  reinforcedSidesNumber: z.number(),\r\n  totalEquivalentThickness: z.number(),\r\n  firstCoefficient: z.number(),\r\n  secondCoefficient: z.number(),\r\n\r\n  // Only for out-of-plane bending\r\n  outOfPlanUnitWidthPanel: z.number(),\r\n  outOfPlanAppliedDesignAxialStress: z.number(),\r\n  outOfPlaneReinforcementFailureMode: z.enum(REINFORCEMENT_FAILURE_MODE),\r\n  outOfPlaneDesignReinforcementStress: z.number(),\r\n  outOfPlaneDesignReinforcementStrain: z.number(),\r\n  outOfPlaneReinforcementTotalWidthAlongLength: z.number(),\r\n  outOfPlaneResistingArea: z.number(),\r\n})\r\n\r\nexport const flexuralReinforcementVerifyExecutionSchema = z.object({\r\n  // For in-plane bending\r\n  product: productFormSchema,\r\n  sectionFailureMode: z.enum(REINFORCEMENT_FAILURE_MODE),\r\n  designReinforcementStress: z.number(),\r\n  designReinforcementStrain: z.number(),\r\n  reinforcedArrangement: z.enum(REINFORCEMENT_ARRANGEMENT),\r\n  singleStripWidth: z.number(),\r\n  stripSpacing: z.number(),\r\n  reinforcementTotalWidthAlongLength: z.number(),\r\n  compressedEdgeReinforcementFiberDistance: z.number(),\r\n  layersNumber: z.number(),\r\n  reinforcedSidesNumber: z.number(),\r\n  totalEquivalentThickness: z.number(),\r\n  firstCoefficient: z.number(),\r\n  secondCoefficient: z.number(),\r\n\r\n  // Only for out-of-plane bending\r\n  outOfPlanUnitWidthPanel: z.number(),\r\n  outOfPlanAppliedDesignAxialStress: z.number(),\r\n  outOfPlaneReinforcementFailureMode: z.enum(REINFORCEMENT_FAILURE_MODE),\r\n  outOfPlaneDesignReinforcementStress: z.number(),\r\n  outOfPlaneDesignReinforcementStrain: z.number(),\r\n  outOfPlaneReinforcementTotalWidthAlongLength: z.number(),\r\n  outOfPlaneResistingArea: z.number(),\r\n})\r\n\r\nexport const shearReinforcementExecutionSchema = z.object({\r\n  product: productFormSchema,\r\n  reinforcementFailureMode: z.enum(REINFORCEMENT_FAILURE_MODE),\r\n  designReinforcementStress: z.number(),\r\n  designReinforcementStrain: z.number(),\r\n  reinforcedArrangement: z.enum(REINFORCEMENT_ARRANGEMENT),\r\n  singleStripWidth: z.number(),\r\n  stripSpacing: z.number(),\r\n  totalReinforcementWidthPerpendicularShearDirection: z.number(),\r\n  compressedEdgeReinforcementFiberDistance: z.number(),\r\n  layersNumber: z.number(),\r\n  reinforcedSidesNumber: z.number(),\r\n})\r\n\r\nexport const maschiMurariFlexuralReinforcementExecutionSchema = z.object({\r\n  calculationType: z.literal('FLEXURAL_VERIFY'),\r\n  input: flexuralReinforcementVerifyExecutionSchema,\r\n})\r\n\r\nexport const maschiMurariShearReinforcementExecutionSchema = z.object({\r\n  calculationType: z.literal('SHEAR_VERIFY'),\r\n  input: shearReinforcementExecutionSchema,\r\n})\r\n\r\nexport const nonReinforcedSectionShearSchema = z.object({\r\n  inPlaneAppliedShear: z.number(),\r\n  verticalStress: z.number(),\r\n  correctionFactorBasedOnWallSlenderness: z.number(),\r\n  shearResistanceNotReinforcedMasonry: z.number(),\r\n  check: z.boolean(),\r\n})\r\n\r\nexport const reinforcedSectionShearSchema = z.object({\r\n  shearResistanceReinforcementContribution: z.number(),\r\n  totalShearResistance: z.number(),\r\n  firstCheck: z.boolean(),\r\n  shearResistanceFromMasonryDiagonalCompression: z.number(),\r\n  secondCheck: z.boolean(),\r\n})\r\n\r\nexport const inPlaneShearCheckSchema = z.object({\r\n  nonReinforcedSection: nonReinforcedSectionShearSchema\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  reinforcedSection: reinforcedSectionShearSchema\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n})\r\n\r\nexport const maschiMurariShearReinforcementCalculationResultSchema = z.object({\r\n  inPlaneShearCheck: inPlaneShearCheckSchema\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n})\r\n\r\nexport const inPlaneFlexuralHypothesis = z.object({\r\n  neutralAxisCompressedEdgeDistance: z.number(),\r\n  reinforcementOrMasonryStrain: z.number(),\r\n  check: z.boolean(),\r\n})\r\n\r\nexport const inPlaneFlexuralReinforcedSection = z.object({\r\n  hypothesisOne: inPlaneFlexuralHypothesis.optional().nullable(),\r\n  hypothesisTwo: inPlaneFlexuralHypothesis.optional().nullable(),\r\n  hypothesisThree: inPlaneFlexuralHypothesis.optional().nullable(),\r\n  momentCapacity: z.number(),\r\n  check: z.boolean(),\r\n})\r\n\r\nexport const inPlaneFlexuralNonReinforcedSection = z.object({\r\n  inPlaneBendingMoment: z.number(),\r\n  neutralAxisCompressedEdgeDistance: z.number(),\r\n  inPlaneFlexuralCapacity: z.number(),\r\n  check: z.boolean(),\r\n})\r\n\r\nexport const inPlaneFlexuralCheckSchema = z.object({\r\n  nonReinforcedSection: inPlaneFlexuralNonReinforcedSection\r\n    .optional()\r\n    .nullable(),\r\n  reinforcedSection: inPlaneFlexuralReinforcedSection.optional().nullable(),\r\n})\r\n\r\nexport const outOfPlaneFlexuralRegionHypothesis = z.object({\r\n  neutralAxisCompressedEdgeDistance: z.number(),\r\n  reinforcementOrMasonryStrain: z.number(),\r\n  resultantCompressiveForceMasonry: z.number(),\r\n  resultantTensileForceFrcm: z.number(),\r\n  designBendingMomentReinforcedSection: z.number(),\r\n  check: z.boolean(),\r\n})\r\n\r\nexport const outOfPlaneReinforcedSection = z.object({\r\n  regionHypothesisTwo: outOfPlaneFlexuralRegionHypothesis.optional().nullable(),\r\n  regionHypothesisOne: outOfPlaneFlexuralRegionHypothesis.optional().nullable(),\r\n  momentCapacity: z.number(),\r\n  check: z.boolean(),\r\n})\r\n\r\nexport const outOfPlaneNonReinforcedSection = z.object({\r\n  appliedDesignBendingMoment: z.number(),\r\n  neutralAxisCompressedEdgeDistance: z.number(),\r\n  resultantCompressiveForceMasonry: z.number(),\r\n  designBendingMoment: z.number(),\r\n  check: z.boolean(),\r\n})\r\n\r\nexport const outOfPlaneFlexuralCheckSchema = z.object({\r\n  nonReinforcedSection: outOfPlaneNonReinforcedSection.optional().nullable(),\r\n  reinforcedSection: outOfPlaneReinforcedSection.optional().nullable(),\r\n})\r\n\r\nexport const shearCheckSchema = z.object({\r\n  appliedDesignSpecificShearForce: z.number(),\r\n  averageNormalStress: z.number(),\r\n  resultantCompressiveForceMasonry: z.number(),\r\n  resultantTensileForceFrcm: z.number(),\r\n  check: z.boolean(),\r\n})\r\n\r\nexport const extremityDetachementCheckSchema = z.object({\r\n  appliedSpecificBendingMoment30FromEdge: z.number().optional().nullable(),\r\n  reinforcementDesignStrainForEndDebonding: z.number(),\r\n  neutralAxisCompressedEdgeDistance: z.number(),\r\n  masonryStrain: z.number(),\r\n  resultantCompressiveForceMasonry: z.number(),\r\n  resultantTensileForceFrcm: z.number(),\r\n  designMomentCapacityReinforcedSection: z.number(),\r\n  check: z.boolean(),\r\n})\r\n\r\nexport const maschiMurariFlexuralReinforcementCalculationResultSchema =\r\n  z.object({\r\n    inPlaneFlexuralCheck: inPlaneFlexuralCheckSchema\r\n      .nullable()\r\n      .optional()\r\n      .transform(nullToUndefinedTransform),\r\n    outOfPlaneFlexuralCheck: outOfPlaneFlexuralCheckSchema\r\n      .nullable()\r\n      .optional()\r\n      .transform(nullToUndefinedTransform),\r\n    shearCheck: shearCheckSchema\r\n      .nullable()\r\n      .optional()\r\n      .transform(nullToUndefinedTransform),\r\n    extremityDetachementCheck: extremityDetachementCheckSchema\r\n      .nullable()\r\n      .optional()\r\n      .transform(nullToUndefinedTransform),\r\n  })\r\n\r\nexport type MaschiMurariFlexuralReinforcementCalculationResultSchema = z.infer<\r\n  typeof maschiMurariFlexuralReinforcementCalculationResultSchema\r\n>\r\n\r\nexport type InPlaneFlexuralCheckSchema = z.infer<\r\n  typeof inPlaneFlexuralCheckSchema\r\n>\r\nexport type OutOfPlaneFlexuralCheckSchema = z.infer<\r\n  typeof outOfPlaneFlexuralCheckSchema\r\n>\r\nexport type ShearCheckSchema = z.infer<typeof shearCheckSchema>\r\nexport type ExtremityDetachementCheckSchema = z.infer<\r\n  typeof extremityDetachementCheckSchema\r\n>\r\nexport type OutOfPlaneNonReinforcedSectionSchema = z.infer<\r\n  typeof outOfPlaneNonReinforcedSection\r\n>\r\n\r\nexport type MaschiMurariShearReinforcementCalculationResultSchema = z.infer<\r\n  typeof maschiMurariShearReinforcementCalculationResultSchema\r\n>\r\n\r\nexport type NonReinforcedSectionShearSchema = z.infer<\r\n  typeof nonReinforcedSectionShearSchema\r\n>\r\nexport type ReinforcedSectionShearSchema = z.infer<\r\n  typeof reinforcedSectionShearSchema\r\n>\r\nexport type InPlaneShearCheckSchema = z.infer<typeof inPlaneShearCheckSchema>\r\nexport type OutOfPlaneFlexuralRegionHypothesis = z.infer<\r\n  typeof outOfPlaneFlexuralRegionHypothesis\r\n>\r\n\r\nexport type MaschiMurariShearReinforcementVerifyExecutionSchemaInput = z.infer<\r\n  typeof maschiMurariShearReinforcementVerifyExecutionSchema\r\n>\r\nexport type MaschiMurariShearReinforcementExecutionSchemaInput = z.infer<\r\n  typeof maschiMurariShearReinforcementExecutionSchema\r\n>\r\n\r\nexport type MaschiMurariFlexuralReinforcementVerifyExecutionSchemaInput =\r\n  z.infer<typeof maschiMurariFlexuralReinforcementVerifyExecutionSchema>\r\n\r\nexport type MaschiMurariFlexuralReinforcementExecutionSchemaInput = z.infer<\r\n  typeof maschiMurariFlexuralReinforcementExecutionSchema\r\n>\r\n\r\nexport type MaschiMurariParamsSchemaInputs = z.infer<\r\n  typeof maschiMurariParamsSchema\r\n>\r\nexport type MaschiMurariPanelGeometrySchemaInputs = z.infer<\r\n  typeof maschiMurariPanelGeometrySchema\r\n>\r\nexport type MaschiMurariMaterialPropertiesSchemaInputs = z.infer<\r\n  typeof maschiMurariMaterialPropertiesSchema\r\n>\r\nexport type MaschiMurariActionsSchemaInputs = z.infer<\r\n  typeof maschiMurariActionsSchema\r\n>\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAUA;AACA;AACA;AACA;;;;;;AAEO,MAAM,kCAAkC,uJAAC,CAAC,MAAM,CAAC;IACtD,QAAQ,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ;IACjD,OAAO,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ;IAChD,WAAW,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,QAAQ;AACtD;AAEO,MAAM,uCAAuC,uJAAC,CAAC,MAAM,CAAC;IAC3D,0BAA0B,uJAAC,CAAC,IAAI,CAAC,qJAAsB;IACvD,aAAa,uJAAC,CAAC,IAAI,CAAC,sJAAuB;IAC3C,gBAAgB,uJAAC,CAAC,IAAI,CAAC,8JAA+B;IACtD,kBAAkB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAChD,gBAAgB,uJAAC,CAAC,IAAI,CAAC,qJAAsB;IAC7C,uBAAuB,uJAAC,CAAC,IAAI,CAAC,6JAA8B;IAC5D,qBAAqB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACnD,mCAAmC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACjE,2BAA2B,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACzD,6BAA6B,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC3D,qBAAqB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACnD,gBAAgB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC9C,cAAc,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC5C,0CAA0C,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACxE,2BAA2B,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACzD,UAAU,uJAAC,CAAC,IAAI,CAAC,uJAAwB;IACzC,kBAAkB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;AAClD;AAEO,MAAM,4BAA4B,uJAAC,CAAC,MAAM,CAAC;IAChD,qBAAqB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACnD,sBAAsB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACpD,yBAAyB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACvD,qBAAqB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACnD,wBAAwB,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;AACxD;AAEO,MAAM,2BAA2B,uJAAC,CAAC,MAAM,CAAC;IAC/C,eAAe,gCACZ,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,oBAAoB,qCACjB,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,SAAS,0BACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;AACvC;AAEO,MAAM,sDAAsD,uJAAC,CAAC,MAAM,CAAC;IAC1E,SAAS,oKAAa;IACtB,0BAA0B,uJAAC,CAAC,IAAI,CAAC,yJAA0B;IAC3D,2BAA2B,uJAAC,CAAC,MAAM;IACnC,2BAA2B,uJAAC,CAAC,MAAM;IACnC,uBAAuB,uJAAC,CAAC,IAAI,CAAC,wJAAyB;IACvD,kBAAkB,uJAAC,CAAC,MAAM;IAC1B,cAAc,uJAAC,CAAC,MAAM;IACtB,oDAAoD,uJAAC,CAAC,MAAM;IAC5D,0CAA0C,uJAAC,CAAC,MAAM;IAClD,cAAc,uJAAC,CAAC,MAAM;IACtB,uBAAuB,uJAAC,CAAC,MAAM;AACjC;AAEO,MAAM,yDAAyD,uJAAC,CAAC,MAAM,CAAC;IAC7E,uBAAuB;IACvB,SAAS,oKAAa;IACtB,oBAAoB,uJAAC,CAAC,IAAI,CAAC,yJAA0B;IACrD,2BAA2B,uJAAC,CAAC,MAAM;IACnC,2BAA2B,uJAAC,CAAC,MAAM;IACnC,uBAAuB,uJAAC,CAAC,IAAI,CAAC,wJAAyB;IACvD,kBAAkB,uJAAC,CAAC,MAAM;IAC1B,cAAc,uJAAC,CAAC,MAAM;IACtB,oCAAoC,uJAAC,CAAC,MAAM;IAC5C,0CAA0C,uJAAC,CAAC,MAAM;IAClD,cAAc,uJAAC,CAAC,MAAM;IACtB,uBAAuB,uJAAC,CAAC,MAAM;IAC/B,0BAA0B,uJAAC,CAAC,MAAM;IAClC,kBAAkB,uJAAC,CAAC,MAAM;IAC1B,mBAAmB,uJAAC,CAAC,MAAM;IAE3B,gCAAgC;IAChC,yBAAyB,uJAAC,CAAC,MAAM;IACjC,mCAAmC,uJAAC,CAAC,MAAM;IAC3C,oCAAoC,uJAAC,CAAC,IAAI,CAAC,yJAA0B;IACrE,qCAAqC,uJAAC,CAAC,MAAM;IAC7C,qCAAqC,uJAAC,CAAC,MAAM;IAC7C,8CAA8C,uJAAC,CAAC,MAAM;IACtD,yBAAyB,uJAAC,CAAC,MAAM;AACnC;AAEO,MAAM,6CAA6C,uJAAC,CAAC,MAAM,CAAC;IACjE,uBAAuB;IACvB,SAAS,yKAAiB;IAC1B,oBAAoB,uJAAC,CAAC,IAAI,CAAC,yJAA0B;IACrD,2BAA2B,uJAAC,CAAC,MAAM;IACnC,2BAA2B,uJAAC,CAAC,MAAM;IACnC,uBAAuB,uJAAC,CAAC,IAAI,CAAC,wJAAyB;IACvD,kBAAkB,uJAAC,CAAC,MAAM;IAC1B,cAAc,uJAAC,CAAC,MAAM;IACtB,oCAAoC,uJAAC,CAAC,MAAM;IAC5C,0CAA0C,uJAAC,CAAC,MAAM;IAClD,cAAc,uJAAC,CAAC,MAAM;IACtB,uBAAuB,uJAAC,CAAC,MAAM;IAC/B,0BAA0B,uJAAC,CAAC,MAAM;IAClC,kBAAkB,uJAAC,CAAC,MAAM;IAC1B,mBAAmB,uJAAC,CAAC,MAAM;IAE3B,gCAAgC;IAChC,yBAAyB,uJAAC,CAAC,MAAM;IACjC,mCAAmC,uJAAC,CAAC,MAAM;IAC3C,oCAAoC,uJAAC,CAAC,IAAI,CAAC,yJAA0B;IACrE,qCAAqC,uJAAC,CAAC,MAAM;IAC7C,qCAAqC,uJAAC,CAAC,MAAM;IAC7C,8CAA8C,uJAAC,CAAC,MAAM;IACtD,yBAAyB,uJAAC,CAAC,MAAM;AACnC;AAEO,MAAM,oCAAoC,uJAAC,CAAC,MAAM,CAAC;IACxD,SAAS,yKAAiB;IAC1B,0BAA0B,uJAAC,CAAC,IAAI,CAAC,yJAA0B;IAC3D,2BAA2B,uJAAC,CAAC,MAAM;IACnC,2BAA2B,uJAAC,CAAC,MAAM;IACnC,uBAAuB,uJAAC,CAAC,IAAI,CAAC,wJAAyB;IACvD,kBAAkB,uJAAC,CAAC,MAAM;IAC1B,cAAc,uJAAC,CAAC,MAAM;IACtB,oDAAoD,uJAAC,CAAC,MAAM;IAC5D,0CAA0C,uJAAC,CAAC,MAAM;IAClD,cAAc,uJAAC,CAAC,MAAM;IACtB,uBAAuB,uJAAC,CAAC,MAAM;AACjC;AAEO,MAAM,mDAAmD,uJAAC,CAAC,MAAM,CAAC;IACvE,iBAAiB,uJAAC,CAAC,OAAO,CAAC;IAC3B,OAAO;AACT;AAEO,MAAM,gDAAgD,uJAAC,CAAC,MAAM,CAAC;IACpE,iBAAiB,uJAAC,CAAC,OAAO,CAAC;IAC3B,OAAO;AACT;AAEO,MAAM,kCAAkC,uJAAC,CAAC,MAAM,CAAC;IACtD,qBAAqB,uJAAC,CAAC,MAAM;IAC7B,gBAAgB,uJAAC,CAAC,MAAM;IACxB,wCAAwC,uJAAC,CAAC,MAAM;IAChD,qCAAqC,uJAAC,CAAC,MAAM;IAC7C,OAAO,uJAAC,CAAC,OAAO;AAClB;AAEO,MAAM,+BAA+B,uJAAC,CAAC,MAAM,CAAC;IACnD,0CAA0C,uJAAC,CAAC,MAAM;IAClD,sBAAsB,uJAAC,CAAC,MAAM;IAC9B,YAAY,uJAAC,CAAC,OAAO;IACrB,+CAA+C,uJAAC,CAAC,MAAM;IACvD,aAAa,uJAAC,CAAC,OAAO;AACxB;AAEO,MAAM,0BAA0B,uJAAC,CAAC,MAAM,CAAC;IAC9C,sBAAsB,gCACnB,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,mBAAmB,6BAChB,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;AACvC;AAEO,MAAM,wDAAwD,uJAAC,CAAC,MAAM,CAAC;IAC5E,mBAAmB,wBAChB,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;AACvC;AAEO,MAAM,4BAA4B,uJAAC,CAAC,MAAM,CAAC;IAChD,mCAAmC,uJAAC,CAAC,MAAM;IAC3C,8BAA8B,uJAAC,CAAC,MAAM;IACtC,OAAO,uJAAC,CAAC,OAAO;AAClB;AAEO,MAAM,mCAAmC,uJAAC,CAAC,MAAM,CAAC;IACvD,eAAe,0BAA0B,QAAQ,GAAG,QAAQ;IAC5D,eAAe,0BAA0B,QAAQ,GAAG,QAAQ;IAC5D,iBAAiB,0BAA0B,QAAQ,GAAG,QAAQ;IAC9D,gBAAgB,uJAAC,CAAC,MAAM;IACxB,OAAO,uJAAC,CAAC,OAAO;AAClB;AAEO,MAAM,sCAAsC,uJAAC,CAAC,MAAM,CAAC;IAC1D,sBAAsB,uJAAC,CAAC,MAAM;IAC9B,mCAAmC,uJAAC,CAAC,MAAM;IAC3C,yBAAyB,uJAAC,CAAC,MAAM;IACjC,OAAO,uJAAC,CAAC,OAAO;AAClB;AAEO,MAAM,6BAA6B,uJAAC,CAAC,MAAM,CAAC;IACjD,sBAAsB,oCACnB,QAAQ,GACR,QAAQ;IACX,mBAAmB,iCAAiC,QAAQ,GAAG,QAAQ;AACzE;AAEO,MAAM,qCAAqC,uJAAC,CAAC,MAAM,CAAC;IACzD,mCAAmC,uJAAC,CAAC,MAAM;IAC3C,8BAA8B,uJAAC,CAAC,MAAM;IACtC,kCAAkC,uJAAC,CAAC,MAAM;IAC1C,2BAA2B,uJAAC,CAAC,MAAM;IACnC,sCAAsC,uJAAC,CAAC,MAAM;IAC9C,OAAO,uJAAC,CAAC,OAAO;AAClB;AAEO,MAAM,8BAA8B,uJAAC,CAAC,MAAM,CAAC;IAClD,qBAAqB,mCAAmC,QAAQ,GAAG,QAAQ;IAC3E,qBAAqB,mCAAmC,QAAQ,GAAG,QAAQ;IAC3E,gBAAgB,uJAAC,CAAC,MAAM;IACxB,OAAO,uJAAC,CAAC,OAAO;AAClB;AAEO,MAAM,iCAAiC,uJAAC,CAAC,MAAM,CAAC;IACrD,4BAA4B,uJAAC,CAAC,MAAM;IACpC,mCAAmC,uJAAC,CAAC,MAAM;IAC3C,kCAAkC,uJAAC,CAAC,MAAM;IAC1C,qBAAqB,uJAAC,CAAC,MAAM;IAC7B,OAAO,uJAAC,CAAC,OAAO;AAClB;AAEO,MAAM,gCAAgC,uJAAC,CAAC,MAAM,CAAC;IACpD,sBAAsB,+BAA+B,QAAQ,GAAG,QAAQ;IACxE,mBAAmB,4BAA4B,QAAQ,GAAG,QAAQ;AACpE;AAEO,MAAM,mBAAmB,uJAAC,CAAC,MAAM,CAAC;IACvC,iCAAiC,uJAAC,CAAC,MAAM;IACzC,qBAAqB,uJAAC,CAAC,MAAM;IAC7B,kCAAkC,uJAAC,CAAC,MAAM;IAC1C,2BAA2B,uJAAC,CAAC,MAAM;IACnC,OAAO,uJAAC,CAAC,OAAO;AAClB;AAEO,MAAM,kCAAkC,uJAAC,CAAC,MAAM,CAAC;IACtD,wCAAwC,uJAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACtE,0CAA0C,uJAAC,CAAC,MAAM;IAClD,mCAAmC,uJAAC,CAAC,MAAM;IAC3C,eAAe,uJAAC,CAAC,MAAM;IACvB,kCAAkC,uJAAC,CAAC,MAAM;IAC1C,2BAA2B,uJAAC,CAAC,MAAM;IACnC,uCAAuC,uJAAC,CAAC,MAAM;IAC/C,OAAO,uJAAC,CAAC,OAAO;AAClB;AAEO,MAAM,2DACX,uJAAC,CAAC,MAAM,CAAC;IACP,sBAAsB,2BACnB,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,yBAAyB,8BACtB,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,YAAY,iBACT,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,2BAA2B,gCACxB,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;AACvC", "debugId": null}}, {"offset": {"line": 2815, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/api/modules/schemas/masonry-antioverturning-params.ts"], "sourcesContent": ["import { nullToUndefinedTransform } from '@atlas/functions/zod/null-to-undefined-transform'\r\nimport {\r\n  masonryAntiOverturningBuildingCharacteristicsSchema,\r\n  masonryAntiOverturningInfillGeometrySchema,\r\n  masonryAntiOverturningMaterialPropertiesSchema,\r\n  masonryAntiOverturningSiteCharacteristicsSchema,\r\n} from '@atlas/types/schemas/masonry/antioverturning-form'\r\nimport { z } from 'zod'\r\n\r\nexport const masonryAntiOverturningParamsSchema = z.object({\r\n  siteCharacteristics: masonryAntiOverturningSiteCharacteristicsSchema\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToUndefinedTransform),\r\n  buildingCharacteristics: masonryAntiOverturningBuildingCharacteristicsSchema\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToUndefinedTransform),\r\n  materialProperties: masonryAntiOverturningMaterialPropertiesSchema\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToUndefinedTransform),\r\n  infillGeometry: masonryAntiOverturningInfillGeometrySchema\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToUndefinedTransform),\r\n})\r\n\r\nexport const masonryAntiOverturningSeismicDemandCalculationResultSchema =\r\n  z.object({\r\n    maximumAcceleration: z.number(),\r\n    masonryAndPlasterWeight: z.number(),\r\n    overturningSeismicForce: z.number(),\r\n  })\r\n\r\nexport const masonryAntiOverturningPreInterventionCalculationResultSchema =\r\n  z.object({\r\n    overturningMoment: z.number(),\r\n    stabilizingMomentExternalFacing: z.number(),\r\n    check: z.boolean(),\r\n    seismicRiskIndicator: z.number(),\r\n    seismicAccelerationCorrespondingToRiskIndicator: z.number(),\r\n  })\r\n\r\nexport const regionHypothesisSchema = z.object({\r\n  neutralAxisDistanceFromCompressedFlange: z.number().optional().nullable(),\r\n  masonryDeformation: z.number().optional().nullable(),\r\n  masonryResultantCompressiveStresses: z.number().optional().nullable(),\r\n  frcmResultantTensileStresses: z.number().optional().nullable(),\r\n  designResistingMomentReinforcedSection: z.number().optional().nullable(),\r\n  hypothesisCheck: z.boolean().optional().nullable(),\r\n})\r\n\r\nexport const perimeterAndWidespreadInterventionCalculationResultResultSchema =\r\n  z.object({\r\n    overturningMoment: z.number().optional().nullable(),\r\n    stabilizingMomentconnectors: z.number().optional().nullable(),\r\n    totalStabilizingMoment: z.number().optional().nullable(),\r\n    check: z.boolean().optional().nullable(),\r\n  })\r\n\r\nexport const widespreadInterventionCalculationResultSchema = z.object({\r\n  panelFundamentalPeriod: z.number().optional().nullable(),\r\n  maximumAcceleration: z.number().optional().nullable(),\r\n  overturningSeismicForce: z.number().optional().nullable(),\r\n  actingMoment: z.number().optional().nullable(),\r\n  midspanAxialStress: z.number().optional().nullable(),\r\n  normalStress: z.number().optional().nullable(),\r\n  unreinforcedSectionResistingMoment: z.number().optional().nullable(),\r\n  regionOneHypothesis: regionHypothesisSchema.optional().nullable(),\r\n  regionTwoHypothesis: regionHypothesisSchema.optional().nullable(),\r\n  specificResistingMoment: z.number().optional().nullable(),\r\n  check: z.boolean().optional().nullable(),\r\n})\r\n\r\nexport const masonryAntiOverturningPostInterventionCalculationResultSchema =\r\n  z.object({\r\n    perimeterAndWidespreadInterventionCalculationResult:\r\n      perimeterAndWidespreadInterventionCalculationResultResultSchema\r\n        .optional()\r\n        .nullable()\r\n        .transform(nullToUndefinedTransform),\r\n    widespreadInterventionCalculationResult:\r\n      widespreadInterventionCalculationResultSchema\r\n        .optional()\r\n        .nullable()\r\n        .transform(nullToUndefinedTransform),\r\n  })\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;AAMA;;;;AAEO,MAAM,qCAAqC,kLAAC,CAAC,MAAM,CAAC;IACzD,qBAAqB,gNAA+C,CACjE,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,yBAAyB,oNAAmD,CACzE,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,oBAAoB,+MAA8C,CAC/D,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,gBAAgB,2MAA0C,CACvD,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;AACvC;AAEO,MAAM,6DACX,kLAAC,CAAC,MAAM,CAAC;IACP,qBAAqB,kLAAC,CAAC,MAAM;IAC7B,yBAAyB,kLAAC,CAAC,MAAM;IACjC,yBAAyB,kLAAC,CAAC,MAAM;AACnC;AAEK,MAAM,+DACX,kLAAC,CAAC,MAAM,CAAC;IACP,mBAAmB,kLAAC,CAAC,MAAM;IAC3B,iCAAiC,kLAAC,CAAC,MAAM;IACzC,OAAO,kLAAC,CAAC,OAAO;IAChB,sBAAsB,kLAAC,CAAC,MAAM;IAC9B,iDAAiD,kLAAC,CAAC,MAAM;AAC3D;AAEK,MAAM,yBAAyB,kLAAC,CAAC,MAAM,CAAC;IAC7C,yCAAyC,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACvE,oBAAoB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAClD,qCAAqC,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACnE,8BAA8B,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC5D,wCAAwC,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACtE,iBAAiB,kLAAC,CAAC,OAAO,GAAG,QAAQ,GAAG,QAAQ;AAClD;AAEO,MAAM,kEACX,kLAAC,CAAC,MAAM,CAAC;IACP,mBAAmB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACjD,6BAA6B,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC3D,wBAAwB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACtD,OAAO,kLAAC,CAAC,OAAO,GAAG,QAAQ,GAAG,QAAQ;AACxC;AAEK,MAAM,gDAAgD,kLAAC,CAAC,MAAM,CAAC;IACpE,wBAAwB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACtD,qBAAqB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACnD,yBAAyB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACvD,cAAc,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC5C,oBAAoB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAClD,cAAc,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAC5C,oCAAoC,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IAClE,qBAAqB,uBAAuB,QAAQ,GAAG,QAAQ;IAC/D,qBAAqB,uBAAuB,QAAQ,GAAG,QAAQ;IAC/D,yBAAyB,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;IACvD,OAAO,kLAAC,CAAC,OAAO,GAAG,QAAQ,GAAG,QAAQ;AACxC;AAEO,MAAM,gEACX,kLAAC,CAAC,MAAM,CAAC;IACP,qDACE,gEACG,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACvC,yCACE,8CACG,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;AACzC", "debugId": null}}, {"offset": {"line": 2890, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/types/schemas/t-beam-form.ts"], "sourcesContent": ["import {\r\n  MODULE_GEOMETRY_EXPOSURE,\r\n  MODULE_MATERIAL_KNOWLEDGE_LEVEL,\r\n  MODULE_POLARITY,\r\n} from '@atlas/constants/module'\r\nimport { productFormSchema } from '@atlas/types/schemas/product-form-schema'\r\nimport { z } from 'zod'\r\n\r\nexport const tBeamGeneralSchema = z.object({\r\n  initialDeformation: z.number(),\r\n  polarity: z.enum(MODULE_POLARITY),\r\n})\r\n\r\nexport const tBeamGeometrySchema = z.object({\r\n  primaryHeight: z.number().positive(), // B (mm)\r\n  primaryWidth: z.number().positive(), // hs (mm)\r\n  secondaryHeight: z.number().positive(), // b (mm)\r\n  secondaryWidth: z.number().positive(), // hw (mm)\r\n  concreteCover1: z.number().positive(), // c1 (mm)\r\n  concreteCover2: z.number().positive(), // c2 (mm)\r\n  totalHeight: z.number().positive(), // H (mm) Total height\r\n  effectiveDepth: z.number(), // Calculated effective depth\r\n  exposure: z.enum(MODULE_GEOMETRY_EXPOSURE),\r\n})\r\n\r\nexport const tBeamRebarSchema = z.object({\r\n  compressionRebars: z.object({\r\n    diameter: z.number().positive(),\r\n    quantity: z.number().positive(),\r\n    area: z.number(),\r\n  }),\r\n  tensionRebars: z.object({\r\n    diameter: z.number().positive(),\r\n    quantity: z.number().positive(),\r\n    area: z.number(),\r\n  }),\r\n})\r\n\r\nexport const tBeamMaterialSchema = z.object({\r\n  knowledgeLevel: z.enum(MODULE_MATERIAL_KNOWLEDGE_LEVEL),\r\n  confidenceFactor: z.number().optional(),\r\n  concreteClass: z.object({\r\n    id: z.string(),\r\n    name: z.string(),\r\n    cubeCompressiveStrength: z.number(),\r\n    cylinderCompressiveStrength: z.number(),\r\n    averageCompressiveStrength: z.number(),\r\n    averageTensileStrength: z.number(),\r\n    elasticModulus: z.number(),\r\n    designCompressiveStrengthForBrittleMechanisms: z.number(),\r\n    designCompressiveStrengthForDuctileMechanisms: z.number(),\r\n  }),\r\n  steelGrade: z.object({\r\n    id: z.string(),\r\n    name: z.string(),\r\n    yieldStrength: z.number(),\r\n    tensileStrength: z.number(),\r\n    elongationPercentage: z.number(),\r\n    elasticModulus: z.number(),\r\n    designYieldStrengthForBrittleMechanisms: z.number(),\r\n    designYieldStrengthForDuctileMechanisms: z.number(),\r\n  }),\r\n})\r\n\r\nexport const tBeamFormSchema = z.object({\r\n  initialDeformation: z.number().optional(),\r\n  polarity: z.enum(MODULE_POLARITY).optional(),\r\n  geometry: tBeamGeometrySchema.optional(),\r\n  tBeamRebar: tBeamRebarSchema.optional(),\r\n  materialProperties: tBeamMaterialSchema.optional(),\r\n})\r\n\r\nexport const tBeamParamsCheckSchema = z.object({\r\n  initialDeformation: z.number(),\r\n  polarity: z.enum(MODULE_POLARITY),\r\n  geometry: tBeamGeometrySchema,\r\n  tBeamRebar: tBeamRebarSchema,\r\n  materialProperties: tBeamMaterialSchema,\r\n})\r\n\r\nexport const tBeamFlexuralCalculationSchema = z.object({\r\n  calculationType: z.literal('FLEXURAL_VERIFY'),\r\n  input: z.object({\r\n    stripWidth: z.number(),\r\n    layersNumber: z.number(),\r\n    bendingMoment: z.number(),\r\n    product: productFormSchema,\r\n  }),\r\n})\r\n\r\nexport type TBeamForm = z.infer<typeof tBeamFormSchema>\r\nexport type TBeamGeneralFormInputs = z.infer<typeof tBeamGeneralSchema>\r\nexport type TBeamGeometryFormInputs = z.infer<typeof tBeamGeometrySchema>\r\nexport type TBeamRebarFormInputs = z.infer<typeof tBeamRebarSchema>\r\nexport type TBeamMaterialFormInputs = z.infer<typeof tBeamMaterialSchema>\r\nexport type TBeamFlexuralCalculationInput = z.infer<\r\n  typeof tBeamFlexuralCalculationSchema\r\n>\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AAKA;AACA;;;;AAEO,MAAM,qBAAqB,kLAAC,CAAC,MAAM,CAAC;IACzC,oBAAoB,kLAAC,CAAC,MAAM;IAC5B,UAAU,kLAAC,CAAC,IAAI,CAAC,8IAAe;AAClC;AAEO,MAAM,sBAAsB,kLAAC,CAAC,MAAM,CAAC;IAC1C,eAAe,kLAAC,CAAC,MAAM,GAAG,QAAQ;IAClC,cAAc,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACjC,iBAAiB,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACpC,gBAAgB,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACnC,gBAAgB,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACnC,gBAAgB,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACnC,aAAa,kLAAC,CAAC,MAAM,GAAG,QAAQ;IAChC,gBAAgB,kLAAC,CAAC,MAAM;IACxB,UAAU,kLAAC,CAAC,IAAI,CAAC,uJAAwB;AAC3C;AAEO,MAAM,mBAAmB,kLAAC,CAAC,MAAM,CAAC;IACvC,mBAAmB,kLAAC,CAAC,MAAM,CAAC;QAC1B,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,MAAM,kLAAC,CAAC,MAAM;IAChB;IACA,eAAe,kLAAC,CAAC,MAAM,CAAC;QACtB,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,UAAU,kLAAC,CAAC,MAAM,GAAG,QAAQ;QAC7B,MAAM,kLAAC,CAAC,MAAM;IAChB;AACF;AAEO,MAAM,sBAAsB,kLAAC,CAAC,MAAM,CAAC;IAC1C,gBAAgB,kLAAC,CAAC,IAAI,CAAC,8JAA+B;IACtD,kBAAkB,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACrC,eAAe,kLAAC,CAAC,MAAM,CAAC;QACtB,IAAI,kLAAC,CAAC,MAAM;QACZ,MAAM,kLAAC,CAAC,MAAM;QACd,yBAAyB,kLAAC,CAAC,MAAM;QACjC,6BAA6B,kLAAC,CAAC,MAAM;QACrC,4BAA4B,kLAAC,CAAC,MAAM;QACpC,wBAAwB,kLAAC,CAAC,MAAM;QAChC,gBAAgB,kLAAC,CAAC,MAAM;QACxB,+CAA+C,kLAAC,CAAC,MAAM;QACvD,+CAA+C,kLAAC,CAAC,MAAM;IACzD;IACA,YAAY,kLAAC,CAAC,MAAM,CAAC;QACnB,IAAI,kLAAC,CAAC,MAAM;QACZ,MAAM,kLAAC,CAAC,MAAM;QACd,eAAe,kLAAC,CAAC,MAAM;QACvB,iBAAiB,kLAAC,CAAC,MAAM;QACzB,sBAAsB,kLAAC,CAAC,MAAM;QAC9B,gBAAgB,kLAAC,CAAC,MAAM;QACxB,yCAAyC,kLAAC,CAAC,MAAM;QACjD,yCAAyC,kLAAC,CAAC,MAAM;IACnD;AACF;AAEO,MAAM,kBAAkB,kLAAC,CAAC,MAAM,CAAC;IACtC,oBAAoB,kLAAC,CAAC,MAAM,GAAG,QAAQ;IACvC,UAAU,kLAAC,CAAC,IAAI,CAAC,8IAAe,EAAE,QAAQ;IAC1C,UAAU,oBAAoB,QAAQ;IACtC,YAAY,iBAAiB,QAAQ;IACrC,oBAAoB,oBAAoB,QAAQ;AAClD;AAEO,MAAM,yBAAyB,kLAAC,CAAC,MAAM,CAAC;IAC7C,oBAAoB,kLAAC,CAAC,MAAM;IAC5B,UAAU,kLAAC,CAAC,IAAI,CAAC,8IAAe;IAChC,UAAU;IACV,YAAY;IACZ,oBAAoB;AACtB;AAEO,MAAM,iCAAiC,kLAAC,CAAC,MAAM,CAAC;IACrD,iBAAiB,kLAAC,CAAC,OAAO,CAAC;IAC3B,OAAO,kLAAC,CAAC,MAAM,CAAC;QACd,YAAY,kLAAC,CAAC,MAAM;QACpB,cAAc,kLAAC,CAAC,MAAM;QACtB,eAAe,kLAAC,CAAC,MAAM;QACvB,SAAS,yKAAiB;IAC5B;AACF", "debugId": null}}, {"offset": {"line": 2991, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/api/modules/schemas/t-beam-params.ts"], "sourcesContent": ["import { MODULE_POLARITY } from '@atlas/constants/module'\r\nimport { nullToObjectTransform } from '@atlas/functions/zod/null-to-object-transform'\r\nimport { nullToUndefinedTransform } from '@atlas/functions/zod/null-to-undefined-transform'\r\nimport { productSchema } from '@atlas/lib/api/products/schemas/product'\r\nimport {\r\n  tBeamGeometrySchema,\r\n  tBeamMaterialSchema,\r\n  tBeamRebarSchema,\r\n} from '@atlas/types/schemas/t-beam-form'\r\nimport { z } from 'zod'\r\n\r\nexport const tBeamParamsSchema = z.object({\r\n  initialDeformation: z\r\n    .number()\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToUndefinedTransform),\r\n  polarity: z\r\n    .enum(MODULE_POLARITY)\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToUndefinedTransform),\r\n  geometry: tBeamGeometrySchema\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToObjectTransform),\r\n  tBeamRebar: tBeamRebarSchema\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToObjectTransform),\r\n  materialProperties: tBeamMaterialSchema\r\n    .optional()\r\n    .nullable()\r\n    .transform(nullToObjectTransform),\r\n})\r\n\r\nexport const tBeamFlexuralVerifyExecutionInputSchema = z.object({\r\n  stripWidth: z.number(),\r\n  layersNumber: z.number(),\r\n  bendingMoment: z.number(),\r\n  product: productSchema,\r\n})\r\n\r\nexport const tBeamFlexuralCalculationResultSchema = z.object({\r\n  momentCapacity: z.number(),\r\n  equilibrium: z.number(),\r\n  checkResult: z.boolean(),\r\n})\r\n\r\nexport const tBeamCalculationCheck = z.object({\r\n  flexuralVerifyExecutionResult: tBeamFlexuralCalculationResultSchema,\r\n})\r\n\r\nexport type TBeamFlexuralCalculationResult = z.infer<\r\n  typeof tBeamFlexuralCalculationResultSchema\r\n>\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AAKA;;;;;;;AAEO,MAAM,oBAAoB,kLAAC,CAAC,MAAM,CAAC;IACxC,oBAAoB,kLAAC,CAClB,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,UAAU,kLAAC,CACR,IAAI,CAAC,8IAAe,EACpB,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,UAAU,mKAAmB,CAC1B,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,qLAAqB;IAClC,YAAY,gKAAgB,CACzB,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,qLAAqB;IAClC,oBAAoB,mKAAmB,CACpC,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,qLAAqB;AACpC;AAEO,MAAM,0CAA0C,kLAAC,CAAC,MAAM,CAAC;IAC9D,YAAY,kLAAC,CAAC,MAAM;IACpB,cAAc,kLAAC,CAAC,MAAM;IACtB,eAAe,kLAAC,CAAC,MAAM;IACvB,SAAS,oKAAa;AACxB;AAEO,MAAM,uCAAuC,kLAAC,CAAC,MAAM,CAAC;IAC3D,gBAAgB,kLAAC,CAAC,MAAM;IACxB,aAAa,kLAAC,CAAC,MAAM;IACrB,aAAa,kLAAC,CAAC,OAAO;AACxB;AAEO,MAAM,wBAAwB,kLAAC,CAAC,MAAM,CAAC;IAC5C,+BAA+B;AACjC", "debugId": null}}, {"offset": {"line": 3038, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/api/modules/schemas/modules.ts"], "sourcesContent": ["import { dateTransform } from '@atlas/functions/zod/date-transform'\r\nimport { nullToObjectTransform } from '@atlas/functions/zod/null-to-object-transform'\r\nimport { nullToUndefinedTransform } from '@atlas/functions/zod/null-to-undefined-transform'\r\nimport {\r\n  pillarFlexuralCalculationResultSchema,\r\n  pillarFlexuralVerifyExecutionInputSchema,\r\n  pillarParamsSchema,\r\n  pillarShearCalculationResultSchema,\r\n  pillarShearVerifyExecutionInputSchema,\r\n} from '@atlas/lib/api/modules/schemas/pillar-params'\r\nimport {\r\n  rectangularBeamFlexuralCalculationResultSchema,\r\n  rectangularBeamFlexuralVerifyExecutionInputSchema,\r\n  rectangularBeamParamsSchema,\r\n  rectangularBeamShearCalculationResultSchema,\r\n  rectangularBeamShearVerifyExecutionInputSchema,\r\n} from '@atlas/lib/api/modules/schemas/rectangular-beam-params'\r\nimport {\r\n  slabCalculationsResultSchema,\r\n  slabFlexuralCalculationResultSchema,\r\n  slabFlexuralVerifyExecutionInputSchema,\r\n  slabInterfaceSlipCalculationResultSchema,\r\n  slabInterfaceSlipVerifyExecutionInputSchema,\r\n  slabParamsSchema,\r\n  slabShearCalculationResultSchema,\r\n  slabShearVerifyExecutionInputSchema,\r\n} from '@atlas/lib/api/modules/schemas/slab-params'\r\nimport { woodParamsSchema } from '@atlas/lib/api/modules/schemas/wood-params'\r\nimport { masonryAntiOverturningCompositeReinforcementSystemVerifyInputSchema } from '@atlas/types/schemas/masonry/antioverturning-form'\r\nimport {\r\n  crmPostInterventionSchema,\r\n  crmPreInterventionSchema,\r\n} from '@atlas/types/schemas/masonry/crm-form'\r\nimport {\r\n  confinementReinforcementCalculationResult,\r\n  confinementReinforcementVerifyInput,\r\n  frcmColumnParamsSchema,\r\n} from '@atlas/types/schemas/masonry/frcm-column-form'\r\nimport {\r\n  maschiMurariFlexuralReinforcementCalculationResultSchema,\r\n  maschiMurariFlexuralReinforcementVerifyExecutionSchema,\r\n  maschiMurariParamsSchema,\r\n  maschiMurariShearReinforcementCalculationResultSchema,\r\n  maschiMurariShearReinforcementVerifyExecutionSchema,\r\n} from '@atlas/types/schemas/masonry/maschi-murari-form'\r\nimport { z } from 'zod'\r\nimport {\r\n  masonryAntiOverturningParamsSchema,\r\n  masonryAntiOverturningPostInterventionCalculationResultSchema,\r\n  masonryAntiOverturningPreInterventionCalculationResultSchema,\r\n  masonryAntiOverturningSeismicDemandCalculationResultSchema,\r\n} from './masonry-antioverturning-params'\r\nimport {\r\n  tBeamFlexuralCalculationResultSchema,\r\n  tBeamFlexuralVerifyExecutionInputSchema,\r\n  tBeamParamsSchema,\r\n} from './t-beam-params'\r\n\r\nexport const moduleBase = z.object({\r\n  id: z.string(),\r\n  name: z.string(),\r\n  description: z\r\n    .string()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  createdAt: z.string().nullable().optional().transform(dateTransform),\r\n  lastModified: z.string().nullable().optional().transform(dateTransform),\r\n})\r\n\r\nexport type Module = z.infer<typeof moduleSchema>\r\n\r\nexport const moduleSchema = z.discriminatedUnion('type', [\r\n  moduleBase.extend({\r\n    type: z.literal('RECTANGULAR_BEAM'),\r\n    params: rectangularBeamParamsSchema\r\n      .nullable()\r\n      .optional()\r\n      .transform(nullToObjectTransform),\r\n    flexuralVerifyExecutionInput:\r\n      rectangularBeamFlexuralVerifyExecutionInputSchema.optional().nullable(),\r\n    flexuralCalculationResult: rectangularBeamFlexuralCalculationResultSchema\r\n      .optional()\r\n      .nullable(),\r\n    shearVerifyExecutionInput: rectangularBeamShearVerifyExecutionInputSchema\r\n      .optional()\r\n      .nullable(),\r\n    shearCalculationResult: rectangularBeamShearCalculationResultSchema\r\n      .optional()\r\n      .nullable(),\r\n  }),\r\n  moduleBase.extend({\r\n    type: z.literal('PILLAR'),\r\n    params: pillarParamsSchema\r\n      .nullable()\r\n      .optional()\r\n      .transform(nullToObjectTransform),\r\n    flexuralVerifyExecutionInput: pillarFlexuralVerifyExecutionInputSchema\r\n      .optional()\r\n      .nullable(),\r\n    flexuralCalculationResult: pillarFlexuralCalculationResultSchema\r\n      .optional()\r\n      .nullable(),\r\n    shearVerifyExecutionInput: pillarShearVerifyExecutionInputSchema\r\n      .optional()\r\n      .nullable(),\r\n    shearCalculationResult: pillarShearCalculationResultSchema\r\n      .optional()\r\n      .nullable(),\r\n  }),\r\n  moduleBase.extend({\r\n    type: z.literal('SLAB'),\r\n    params: slabParamsSchema\r\n      .nullable()\r\n      .optional()\r\n      .transform(nullToObjectTransform),\r\n    // Flexural verification M+ (positive moment / span)\r\n    flexuralVerifyExecutionInputMPlus: slabFlexuralVerifyExecutionInputSchema\r\n      .optional()\r\n      .nullable(),\r\n    flexuralVerifyExecutionResultMPlus: slabFlexuralCalculationResultSchema\r\n      .optional()\r\n      .nullable(),\r\n    // Flexural verification M- (negative moment / support)\r\n    flexuralVerifyExecutionInputMMinus: slabFlexuralVerifyExecutionInputSchema\r\n      .optional()\r\n      .nullable(),\r\n    flexuralVerifyExecutionResultMMinus: slabFlexuralCalculationResultSchema\r\n      .optional()\r\n      .nullable(),\r\n    // Shear verification\r\n    shearVerifyExecutionInput: slabShearVerifyExecutionInputSchema\r\n      .optional()\r\n      .nullable(),\r\n    shearVerifyExecutionResult: slabShearCalculationResultSchema\r\n      .optional()\r\n      .nullable(),\r\n    // Interface slip verification\r\n    interfaceSlipVerifyExecutionInput:\r\n      slabInterfaceSlipVerifyExecutionInputSchema.optional().nullable(),\r\n    interfaceSlipCalculationResult: slabInterfaceSlipCalculationResultSchema\r\n      .optional()\r\n      .nullable(),\r\n    // Legacy calculations result (deprecated, kept for backwards compatibility)\r\n    calculationsResult: slabCalculationsResultSchema.optional().nullable(),\r\n  }),\r\n  moduleBase.extend({\r\n    type: z.literal('ANTI_OVERTURNING'),\r\n    params: masonryAntiOverturningParamsSchema.nullable().optional(),\r\n    seismicDemandCalculationResult:\r\n      masonryAntiOverturningSeismicDemandCalculationResultSchema\r\n        .optional()\r\n        .nullable()\r\n        .transform(nullToUndefinedTransform),\r\n    preInterventionCalculationResult:\r\n      masonryAntiOverturningPreInterventionCalculationResultSchema\r\n        .optional()\r\n        .nullable()\r\n        .transform(nullToUndefinedTransform),\r\n    compositeReinforcementSystemVerifyInput:\r\n      masonryAntiOverturningCompositeReinforcementSystemVerifyInputSchema\r\n        .optional()\r\n        .nullable()\r\n        .transform(nullToUndefinedTransform),\r\n    postInterventionCalculationResult:\r\n      masonryAntiOverturningPostInterventionCalculationResultSchema\r\n        .optional()\r\n        .nullable(),\r\n  }),\r\n  moduleBase.extend({\r\n    type: z.literal('WOOD'),\r\n    params: woodParamsSchema\r\n      .nullable()\r\n      .optional()\r\n      .transform(nullToObjectTransform),\r\n  }),\r\n  moduleBase.extend({\r\n    type: z.literal('T_BEAM'),\r\n    params: tBeamParamsSchema\r\n      .nullable()\r\n      .optional()\r\n      .transform(nullToObjectTransform),\r\n    flexuralVerifyExecutionInput: tBeamFlexuralVerifyExecutionInputSchema\r\n      .optional()\r\n      .nullable(),\r\n    flexuralVerifyExecutionResult: tBeamFlexuralCalculationResultSchema\r\n      .optional()\r\n      .nullable(),\r\n  }),\r\n  moduleBase.extend({\r\n    type: z.literal('CRM'),\r\n    preIntervention: crmPreInterventionSchema.nullable().optional(),\r\n    postIntervention: crmPostInterventionSchema\r\n      .optional()\r\n      .nullable()\r\n      .transform(nullToUndefinedTransform),\r\n  }),\r\n  moduleBase.extend({\r\n    type: z.literal('FRCM_COLUMN'),\r\n    params: frcmColumnParamsSchema.nullable().optional(),\r\n    confinementReinforcementVerifyInput: confinementReinforcementVerifyInput\r\n      .optional()\r\n      .nullable()\r\n      .transform(nullToUndefinedTransform),\r\n    confinementReinforcementCalculationResult:\r\n      confinementReinforcementCalculationResult\r\n        .optional()\r\n        .nullable()\r\n        .transform(nullToUndefinedTransform),\r\n  }),\r\n  moduleBase.extend({\r\n    type: z.literal('MASCHI_MURARI'),\r\n    params: maschiMurariParamsSchema.nullable().optional(),\r\n    shearReinforcementVerifyExecutionInput:\r\n      maschiMurariShearReinforcementVerifyExecutionSchema.optional().nullable(),\r\n    shearReinforcementCalculationResult:\r\n      maschiMurariShearReinforcementCalculationResultSchema\r\n        .optional()\r\n        .nullable(),\r\n    flexuralReinforcementVerifyExecutionInput:\r\n      maschiMurariFlexuralReinforcementVerifyExecutionSchema\r\n        .optional()\r\n        .nullable(),\r\n    flexuralReinforcementCalculationResult:\r\n      maschiMurariFlexuralReinforcementCalculationResultSchema\r\n        .optional()\r\n        .nullable(),\r\n  }),\r\n  moduleBase.extend({\r\n    type: z.literal('UNKNOWN'),\r\n  }),\r\n])\r\n\r\nexport const moduleWithParamsSchema = moduleSchema\r\n\r\nexport type ModuleWithParams = z.infer<typeof moduleWithParamsSchema>\r\n\r\nexport type ModuleWithParamsRectangularBeam = Extract<\r\n  ModuleWithParams,\r\n  { type: 'RECTANGULAR_BEAM' }\r\n>\r\nexport type ModuleWithParamsPillar = Extract<\r\n  ModuleWithParams,\r\n  { type: 'PILLAR' }\r\n>\r\nexport type ModuleWithParamsWood = Extract<ModuleWithParams, { type: 'WOOD' }>\r\n\r\nexport type ModuleWithParamsSlab = Extract<ModuleWithParams, { type: 'SLAB' }>\r\n\r\nexport type ModuleWithParamsAntiOverturning = Extract<\r\n  ModuleWithParams,\r\n  { type: 'ANTI_OVERTURNING' }\r\n>\r\n\r\nexport type ModuleWithParamsTBeam = Extract<\r\n  ModuleWithParams,\r\n  { type: 'T_BEAM' }\r\n>\r\n\r\nexport type ModuleWithParamsFrcmColumn = Extract<\r\n  ModuleWithParams,\r\n  { type: 'FRCM_COLUMN' }\r\n>\r\n\r\nexport type ModuleWithParamsMaschiMurari = Extract<\r\n  ModuleWithParams,\r\n  { type: 'MASCHI_MURARI' }\r\n>\r\n\r\nexport type ModuleWithParamsCrm = Extract<ModuleWithParams, { type: 'CRM' }>\r\n\r\nexport type ModuleType = ModuleWithParams['type']\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AACA;AAOA;AAOA;AAUA;AACA;AACA;AAIA;AAKA;AAOA;AACA;AAMA;;;;;;;;;;;;;;;AAMO,MAAM,aAAa,kLAAC,CAAC,MAAM,CAAC;IACjC,IAAI,kLAAC,CAAC,MAAM;IACZ,MAAM,kLAAC,CAAC,MAAM;IACd,aAAa,kLAAC,CACX,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,WAAW,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAC,6JAAa;IACnE,cAAc,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAC,6JAAa;AACxE;AAIO,MAAM,eAAe,kLAAC,CAAC,kBAAkB,CAAC,QAAQ;IACvD,WAAW,MAAM,CAAC;QAChB,MAAM,kLAAC,CAAC,OAAO,CAAC;QAChB,QAAQ,uMAA2B,CAChC,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,qLAAqB;QAClC,8BACE,6NAAiD,CAAC,QAAQ,GAAG,QAAQ;QACvE,2BAA2B,0NAA8C,CACtE,QAAQ,GACR,QAAQ;QACX,2BAA2B,0NAA8C,CACtE,QAAQ,GACR,QAAQ;QACX,wBAAwB,uNAA2C,CAChE,QAAQ,GACR,QAAQ;IACb;IACA,WAAW,MAAM,CAAC;QAChB,MAAM,kLAAC,CAAC,OAAO,CAAC;QAChB,QAAQ,iLAAkB,CACvB,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,qLAAqB;QAClC,8BAA8B,uMAAwC,CACnE,QAAQ,GACR,QAAQ;QACX,2BAA2B,oMAAqC,CAC7D,QAAQ,GACR,QAAQ;QACX,2BAA2B,oMAAqC,CAC7D,QAAQ,GACR,QAAQ;QACX,wBAAwB,iMAAkC,CACvD,QAAQ,GACR,QAAQ;IACb;IACA,WAAW,MAAM,CAAC;QAChB,MAAM,kLAAC,CAAC,OAAO,CAAC;QAChB,QAAQ,6KAAgB,CACrB,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,qLAAqB;QAClC,oDAAoD;QACpD,mCAAmC,mMAAsC,CACtE,QAAQ,GACR,QAAQ;QACX,oCAAoC,gMAAmC,CACpE,QAAQ,GACR,QAAQ;QACX,uDAAuD;QACvD,oCAAoC,mMAAsC,CACvE,QAAQ,GACR,QAAQ;QACX,qCAAqC,gMAAmC,CACrE,QAAQ,GACR,QAAQ;QACX,qBAAqB;QACrB,2BAA2B,gMAAmC,CAC3D,QAAQ,GACR,QAAQ;QACX,4BAA4B,6LAAgC,CACzD,QAAQ,GACR,QAAQ;QACX,8BAA8B;QAC9B,mCACE,wMAA2C,CAAC,QAAQ,GAAG,QAAQ;QACjE,gCAAgC,qMAAwC,CACrE,QAAQ,GACR,QAAQ;QACX,4EAA4E;QAC5E,oBAAoB,yLAA4B,CAAC,QAAQ,GAAG,QAAQ;IACtE;IACA,WAAW,MAAM,CAAC;QAChB,MAAM,kLAAC,CAAC,OAAO,CAAC;QAChB,QAAQ,qNAAkC,CAAC,QAAQ,GAAG,QAAQ;QAC9D,gCACE,6OAA0D,CACvD,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;QACvC,kCACE,+OAA4D,CACzD,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;QACvC,yCACE,oOAAmE,CAChE,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;QACvC,mCACE,gPAA6D,CAC1D,QAAQ,GACR,QAAQ;IACf;IACA,WAAW,MAAM,CAAC;QAChB,MAAM,kLAAC,CAAC,OAAO,CAAC;QAChB,QAAQ,6KAAgB,CACrB,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,qLAAqB;IACpC;IACA,WAAW,MAAM,CAAC;QAChB,MAAM,kLAAC,CAAC,OAAO,CAAC;QAChB,QAAQ,mLAAiB,CACtB,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,qLAAqB;QAClC,8BAA8B,yMAAuC,CAClE,QAAQ,GACR,QAAQ;QACX,+BAA+B,sMAAoC,CAChE,QAAQ,GACR,QAAQ;IACb;IACA,WAAW,MAAM,CAAC;QAChB,MAAM,kLAAC,CAAC,OAAO,CAAC;QAChB,iBAAiB,6KAAwB,CAAC,QAAQ,GAAG,QAAQ;QAC7D,kBAAkB,8KAAyB,CACxC,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACvC;IACA,WAAW,MAAM,CAAC;QAChB,MAAM,kLAAC,CAAC,OAAO,CAAC;QAChB,QAAQ,sLAAsB,CAAC,QAAQ,GAAG,QAAQ;QAClD,qCAAqC,mMAAmC,CACrE,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;QACrC,2CACE,yMAAyC,CACtC,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACzC;IACA,WAAW,MAAM,CAAC;QAChB,MAAM,kLAAC,CAAC,OAAO,CAAC;QAChB,QAAQ,0LAAwB,CAAC,QAAQ,GAAG,QAAQ;QACpD,wCACE,qNAAmD,CAAC,QAAQ,GAAG,QAAQ;QACzE,qCACE,uNAAqD,CAClD,QAAQ,GACR,QAAQ;QACb,2CACE,wNAAsD,CACnD,QAAQ,GACR,QAAQ;QACb,wCACE,0NAAwD,CACrD,QAAQ,GACR,QAAQ;IACf;IACA,WAAW,MAAM,CAAC;QAChB,MAAM,kLAAC,CAAC,OAAO,CAAC;IAClB;CACD;AAEM,MAAM,yBAAyB", "debugId": null}}, {"offset": {"line": 3162, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/api/httpClient.ts"], "sourcesContent": ["import axios from 'axios'\r\n\r\nexport const apiClient = axios.create({\r\n  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL,\r\n  headers: { 'Content-Type': 'application/json' },\r\n})\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,YAAY,gJAAK,CAAC,MAAM,CAAC;IACpC,OAAO;IACP,SAAS;QAAE,gBAAgB;IAAmB;AAChD", "debugId": null}}, {"offset": {"line": 3178, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/api/modules/endpoints/get-module.ts"], "sourcesContent": ["import { handleApiError } from '@atlas/lib/api/apiErrors'\r\nimport {\r\n  type Module,\r\n  type ModuleWithParams,\r\n  moduleWithParamsSchema,\r\n} from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { ApiError, ValidationError } from '@atlas/types'\r\nimport type { AxiosResponse } from 'axios'\r\nimport { pipe } from 'fp-ts/function'\r\nimport { map, type TaskEither, tryCatch } from 'fp-ts/TaskEither'\r\nimport { apiClient } from '../../httpClient'\r\n\r\nconst validate = (data: unknown): ModuleWithParams => {\r\n  const parse = moduleWithParamsSchema.safeParse(data)\r\n  if (!parse.success) {\r\n    throw {\r\n      type: 'ValidationError',\r\n      code: 'MALFORMED_RESPONSE',\r\n      message: parse.error.message,\r\n    } as ValidationError\r\n  }\r\n  return parse.data\r\n}\r\n\r\nexport const fetch = (\r\n  token: string,\r\n  id: Project['id'],\r\n  moduleId: Module['id'],\r\n): TaskEither<ApiError, AxiosResponse> =>\r\n  tryCatch(\r\n    () =>\r\n      apiClient.get(`/api/v2/projects/${id}/modules/${moduleId}`, {\r\n        ...(token && {\r\n          headers: { Authorization: `Bearer ${token}` },\r\n        }),\r\n      }),\r\n    e => handleApiError(e),\r\n  )\r\n\r\nexport const getModuleById = (\r\n  token: string,\r\n  projectId: Project['id'],\r\n  moduleId: Module['id'],\r\n): TaskEither<ApiError | ValidationError, ModuleWithParams> =>\r\n  pipe(\r\n    fetch(token, projectId, moduleId),\r\n    map(a => a.data),\r\n    map(validate),\r\n  )\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAQA;AACA;AACA;;;;;;AAEA,MAAM,WAAW,CAAC;IAChB,MAAM,QAAQ,4KAAsB,CAAC,SAAS,CAAC;IAC/C,IAAI,CAAC,MAAM,OAAO,EAAE;QAClB,MAAM;YACJ,MAAM;YACN,MAAM;YACN,SAAS,MAAM,KAAK,CAAC,OAAO;QAC9B;IACF;IACA,OAAO,MAAM,IAAI;AACnB;AAEO,MAAM,QAAQ,CACnB,OACA,IACA,WAEA,IAAA,yJAAQ,EACN,IACE,4IAAS,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,GAAG,SAAS,EAAE,UAAU,EAAE;YAC1D,GAAI,SAAS;gBACX,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C,CAAC;QACH,IACF,CAAA,IAAK,IAAA,gJAAc,EAAC;AAGjB,MAAM,gBAAgB,CAC3B,OACA,WACA,WAEA,IAAA,mJAAI,EACF,MAAM,OAAO,WAAW,WACxB,IAAA,oJAAG,EAAC,CAAA,IAAK,EAAE,IAAI,GACf,IAAA,oJAAG,EAAC", "debugId": null}}, {"offset": {"line": 3217, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/query/modules/get-module.ts"], "sourcesContent": ["import { getModuleById } from '@atlas/lib/api/modules/endpoints/get-module'\r\nimport type {\r\n  Mo<PERSON>le,\r\n  ModuleWithParams,\r\n} from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { fold } from 'fp-ts/Either'\r\nimport { pipe } from 'fp-ts/function'\r\nimport type { Session } from 'next-auth'\r\n\r\nexport const getModuleQuery = async (\r\n  session: Session,\r\n  projectId: Project['id'],\r\n  moduleId: Module['id'],\r\n): Promise<ModuleWithParams> => {\r\n  const result = await getModuleById(session.accessToken, projectId, moduleId)()\r\n\r\n  return pipe(\r\n    result,\r\n    fold(\r\n      err => {\r\n        throw err\r\n      },\r\n      data => data,\r\n    ),\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAMA;AACA;;;;AAGO,MAAM,iBAAiB,OAC5B,SACA,WACA;IAEA,MAAM,SAAS,MAAM,IAAA,2KAAa,EAAC,QAAQ,WAAW,EAAE,WAAW;IAEnE,OAAO,IAAA,mJAAI,EACT,QACA,IAAA,iJAAI,EACF,CAAA;QACE,MAAM;IACR,GACA,CAAA,OAAQ;AAGd", "debugId": null}}, {"offset": {"line": 3237, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/api/common/paginated-schema.tsx"], "sourcesContent": ["import { z } from 'zod'\r\n\r\nexport const paginatedSchema = {\r\n  pageable: z.object({\r\n    pageNumber: z.number(),\r\n    pageSize: z.number(),\r\n    sort: z.object({\r\n      empty: z.boolean(),\r\n      sorted: z.boolean(),\r\n      unsorted: z.boolean(),\r\n    }),\r\n    offset: z.number(),\r\n    paged: z.boolean(),\r\n    unpaged: z.boolean(),\r\n  }),\r\n  last: z.boolean(),\r\n  totalElements: z.number(),\r\n  totalPages: z.number(),\r\n  size: z.number(),\r\n  number: z.number(),\r\n  first: z.boolean(),\r\n  numberOfElements: z.number(),\r\n  sort: z.object({\r\n    empty: z.boolean(),\r\n    sorted: z.boolean(),\r\n    unsorted: z.boolean(),\r\n  }),\r\n  empty: z.boolean(),\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;AAEO,MAAM,kBAAkB;IAC7B,UAAU,kLAAC,CAAC,MAAM,CAAC;QACjB,YAAY,kLAAC,CAAC,MAAM;QACpB,UAAU,kLAAC,CAAC,MAAM;QAClB,MAAM,kLAAC,CAAC,MAAM,CAAC;YACb,OAAO,kLAAC,CAAC,OAAO;YAChB,QAAQ,kLAAC,CAAC,OAAO;YACjB,UAAU,kLAAC,CAAC,OAAO;QACrB;QACA,QAAQ,kLAAC,CAAC,MAAM;QAChB,OAAO,kLAAC,CAAC,OAAO;QAChB,SAAS,kLAAC,CAAC,OAAO;IACpB;IACA,MAAM,kLAAC,CAAC,OAAO;IACf,eAAe,kLAAC,CAAC,MAAM;IACvB,YAAY,kLAAC,CAAC,MAAM;IACpB,MAAM,kLAAC,CAAC,MAAM;IACd,QAAQ,kLAAC,CAAC,MAAM;IAChB,OAAO,kLAAC,CAAC,OAAO;IAChB,kBAAkB,kLAAC,CAAC,MAAM;IAC1B,MAAM,kLAAC,CAAC,MAAM,CAAC;QACb,OAAO,kLAAC,CAAC,OAAO;QAChB,QAAQ,kLAAC,CAAC,OAAO;QACjB,UAAU,kLAAC,CAAC,OAAO;IACrB;IACA,OAAO,kLAAC,CAAC,OAAO;AAClB", "debugId": null}}, {"offset": {"line": 3274, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/api/projects/schemas/projects.ts"], "sourcesContent": ["import { dateTransform } from '@atlas/functions/zod/date-transform'\r\nimport { nullToUndefinedTransform } from '@atlas/functions/zod/null-to-undefined-transform'\r\nimport { paginatedSchema } from '@atlas/lib/api/common/paginated-schema'\r\nimport { moduleSchema } from '@atlas/lib/api/modules/schemas/modules'\r\nimport { z } from 'zod'\r\n\r\nexport const projectSchema = z.object({\r\n  id: z.string(),\r\n  address: z.string(),\r\n  baseTenderAmount: z\r\n    .number()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  baseTenderCurrency: z\r\n    .string()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  company: z.string(),\r\n  constructionSiteName: z.string(),\r\n  createdAt: z.string().nullable().optional().transform(dateTransform),\r\n  lastModified: z.string().nullable().optional().transform(dateTransform),\r\n  machiningSurfaceSize: z\r\n    .string()\r\n    .nullable()\r\n    .optional()\r\n    .transform(val => (val ? Number(val) : undefined)), // TODO: deve essere number\r\n  plannedWorkDescription: z\r\n    .string()\r\n    .nullable()\r\n    .optional()\r\n    .transform(nullToUndefinedTransform),\r\n  planner: z.string(),\r\n  userId: z.string(),\r\n  processingType: z.string(),\r\n  modules: z.array(moduleSchema),\r\n})\r\n\r\nexport const paginatedProjectSchema = z.object({\r\n  ...paginatedSchema,\r\n  content: z.array(projectSchema),\r\n})\r\n\r\nexport type Project = z.infer<typeof projectSchema>\r\nexport type PaginatedProjects = z.infer<typeof paginatedProjectSchema>\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEO,MAAM,gBAAgB,kLAAC,CAAC,MAAM,CAAC;IACpC,IAAI,kLAAC,CAAC,MAAM;IACZ,SAAS,kLAAC,CAAC,MAAM;IACjB,kBAAkB,kLAAC,CAChB,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,oBAAoB,kLAAC,CAClB,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,SAAS,kLAAC,CAAC,MAAM;IACjB,sBAAsB,kLAAC,CAAC,MAAM;IAC9B,WAAW,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAC,6JAAa;IACnE,cAAc,kLAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ,GAAG,SAAS,CAAC,6JAAa;IACtE,sBAAsB,kLAAC,CACpB,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,CAAA,MAAQ,MAAM,OAAO,OAAO;IACzC,wBAAwB,kLAAC,CACtB,MAAM,GACN,QAAQ,GACR,QAAQ,GACR,SAAS,CAAC,2LAAwB;IACrC,SAAS,kLAAC,CAAC,MAAM;IACjB,QAAQ,kLAAC,CAAC,MAAM;IAChB,gBAAgB,kLAAC,CAAC,MAAM;IACxB,SAAS,kLAAC,CAAC,KAAK,CAAC,kKAAY;AAC/B;AAEO,MAAM,yBAAyB,kLAAC,CAAC,MAAM,CAAC;IAC7C,GAAG,sKAAe;IAClB,SAAS,kLAAC,CAAC,KAAK,CAAC;AACnB", "debugId": null}}, {"offset": {"line": 3314, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/api/projects/endpoints/get-project.ts"], "sourcesContent": ["import { handleApiError } from '@atlas/lib/api/apiErrors'\r\nimport {\r\n  type Project,\r\n  projectSchema,\r\n} from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { ApiError, ValidationError } from '@atlas/types'\r\nimport type { AxiosResponse } from 'axios'\r\nimport { pipe } from 'fp-ts/function'\r\nimport { map, type TaskEither, tryCatch } from 'fp-ts/TaskEither'\r\nimport { apiClient } from '../../httpClient'\r\n\r\nconst validate = (data: unknown): Project => {\r\n  const parse = projectSchema.safeParse(data)\r\n  if (!parse.success) {\r\n    throw {\r\n      type: 'ValidationError',\r\n      code: 'MALFORMED_RESPONSE',\r\n      message: parse.error.message,\r\n    } as ValidationError\r\n  }\r\n  return parse.data\r\n}\r\n\r\nexport const fetch = (\r\n  token: string,\r\n  id: Project['id'],\r\n): TaskEither<ApiError, AxiosResponse> =>\r\n  tryCatch(\r\n    () =>\r\n      apiClient.get(`/api/v2/projects/${id}`, {\r\n        ...(token && {\r\n          headers: { Authorization: `Bearer ${token}` },\r\n        }),\r\n      }),\r\n    e => handleApiError(e),\r\n  )\r\n\r\nexport const getProjectById = (\r\n  token: string,\r\n  projectId: Project['id'],\r\n): TaskEither<ApiError | ValidationError, Project> =>\r\n  pipe(\r\n    fetch(token, projectId),\r\n    map(a => a.data),\r\n    map(validate),\r\n  )\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAMA;AACA;AACA;;;;;;AAEA,MAAM,WAAW,CAAC;IAChB,MAAM,QAAQ,qKAAa,CAAC,SAAS,CAAC;IACtC,IAAI,CAAC,MAAM,OAAO,EAAE;QAClB,MAAM;YACJ,MAAM;YACN,MAAM;YACN,SAAS,MAAM,KAAK,CAAC,OAAO;QAC9B;IACF;IACA,OAAO,MAAM,IAAI;AACnB;AAEO,MAAM,QAAQ,CACnB,OACA,KAEA,IAAA,yJAAQ,EACN,IACE,4IAAS,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,IAAI,EAAE;YACtC,GAAI,SAAS;gBACX,SAAS;oBAAE,eAAe,CAAC,OAAO,EAAE,OAAO;gBAAC;YAC9C,CAAC;QACH,IACF,CAAA,IAAK,IAAA,gJAAc,EAAC;AAGjB,MAAM,iBAAiB,CAC5B,OACA,YAEA,IAAA,mJAAI,EACF,MAAM,OAAO,YACb,IAAA,oJAAG,EAAC,CAAA,IAAK,EAAE,IAAI,GACf,IAAA,oJAAG,EAAC", "debugId": null}}, {"offset": {"line": 3353, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/query/projects/get-project.tsx"], "sourcesContent": ["import { getProjectById } from '@atlas/lib/api/projects/endpoints/get-project'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { fold } from 'fp-ts/Either'\r\nimport { pipe } from 'fp-ts/function'\r\nimport type { Session } from 'next-auth'\r\n\r\nexport const getProjectQuery = async (\r\n  session: Session,\r\n  projectId: Project['id'],\r\n): Promise<Project> => {\r\n  const result = await getProjectById(session.accessToken, projectId)()\r\n\r\n  return pipe(\r\n    result,\r\n    fold(\r\n      err => {\r\n        throw err\r\n      },\r\n      data => data,\r\n    ),\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;;;;AAGO,MAAM,kBAAkB,OAC7B,SACA;IAEA,MAAM,SAAS,MAAM,IAAA,8KAAc,EAAC,QAAQ,WAAW,EAAE;IAEzD,OAAO,IAAA,mJAAI,EACT,QACA,IAAA,iJAAI,EACF,CAAA;QACE,MAAM;IACR,GACA,CAAA,OAAQ;AAGd", "debugId": null}}, {"offset": {"line": 3373, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/pages/modules/module-detail-page.tsx"], "sourcesContent": ["import { auth, signIn } from '@atlas/auth'\r\nimport { Breadcrumbs } from '@atlas/components/common/atlas/breadcrumbs'\r\nimport { ModuleDetailContent } from '@atlas/components/common/atlas/module-detail/module-detail-content'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport { SidebarTrigger } from '@atlas/components/ui/sidebar'\r\nimport { getModuleQuery } from '@atlas/lib/query/modules/get-module'\r\nimport { getProjectQuery } from '@atlas/lib/query/projects/get-project'\r\nimport { getTranslations } from 'next-intl/server'\r\n\r\ntype Props = {\r\n  params: Promise<{\r\n    projectId: string\r\n    moduleId: string\r\n  }>\r\n}\r\n\r\nexport const ModuleDetailPage = async (props: Props) => {\r\n  const params = await props.params\r\n  const { projectId, moduleId } = params\r\n  const tBread = await getTranslations('breadcrumbs')\r\n  const session = await auth()\r\n\r\n  if (!session?.user) {\r\n    return signIn()\r\n  }\r\n\r\n  const project = await getProjectQuery(session, projectId)\r\n  const module = await getModuleQuery(session, projectId, moduleId)\r\n\r\n  return (\r\n    <>\r\n      <header className=\"flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12\">\r\n        <div className=\"flex items-center gap-2 px-4\">\r\n          <SidebarTrigger className=\"-ml-1\" />\r\n          <Separator orientation=\"vertical\" className=\"mr-2 h-4\" />\r\n          <Breadcrumbs\r\n            content={[\r\n              { label: tBread('dashboard'), href: '/dashboard' },\r\n              {\r\n                label: tBread('project', {\r\n                  name: project.constructionSiteName ?? '',\r\n                }),\r\n                href: `/dashboard/projects/${projectId}`,\r\n              },\r\n              {\r\n                label: tBread('module', {\r\n                  name: module.name ?? '',\r\n                }),\r\n              },\r\n            ]}\r\n          />\r\n        </div>\r\n      </header>\r\n      <div className=\"flex flex-1 flex-col gap-4 p-4 pt-0\">\r\n        <ModuleDetailContent\r\n          session={session}\r\n          projectId={projectId}\r\n          initialDataProject={project}\r\n          moduleId={moduleId}\r\n          initialDataModule={module}\r\n        />\r\n      </div>\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AASO,MAAM,mBAAmB,OAAO;IACrC,MAAM,SAAS,MAAM,MAAM,MAAM;IACjC,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG;IAChC,MAAM,SAAS,MAAM,IAAA,2QAAe,EAAC;IACrC,MAAM,UAAU,MAAM,IAAA,mHAAI;IAE1B,IAAI,CAAC,SAAS,MAAM;QAClB,OAAO,IAAA,qHAAM;IACf;IAEA,MAAM,UAAU,MAAM,IAAA,qKAAe,EAAC,SAAS;IAC/C,MAAM,SAAS,MAAM,IAAA,iKAAc,EAAC,SAAS,WAAW;IAExD,qBACE;;0BACE,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,qJAAc;4BAAC,WAAU;;;;;;sCAC1B,8OAAC,kJAAS;4BAAC,aAAY;4BAAW,WAAU;;;;;;sCAC5C,8OAAC,mKAAW;4BACV,SAAS;gCACP;oCAAE,OAAO,OAAO;oCAAc,MAAM;gCAAa;gCACjD;oCACE,OAAO,OAAO,WAAW;wCACvB,MAAM,QAAQ,oBAAoB,IAAI;oCACxC;oCACA,MAAM,CAAC,oBAAoB,EAAE,WAAW;gCAC1C;gCACA;oCACE,OAAO,OAAO,UAAU;wCACtB,MAAM,OAAO,IAAI,IAAI;oCACvB;gCACF;6BACD;;;;;;;;;;;;;;;;;0BAIP,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,+MAAmB;oBAClB,SAAS;oBACT,WAAW;oBACX,oBAAoB;oBACpB,UAAU;oBACV,mBAAmB;;;;;;;;;;;;;AAK7B", "debugId": null}}, {"offset": {"line": 3486, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/app/%5Blocale%5D/dashboard/projects/%5BprojectId%5D/modules/%5BmoduleId%5D/page.tsx"], "sourcesContent": ["import { ModuleDetailPage } from '@atlas/components/pages/modules/module-detail-page'\r\nimport { getTranslations } from 'next-intl/server'\r\n\r\nexport const generateMetadata = async ({\r\n  params,\r\n}: {\r\n  params: Promise<{ locale: string }>\r\n}) => {\r\n  const { locale } = await params\r\n  const t = await getTranslations({\r\n    locale,\r\n    namespace: 'pages.module-detail.meta',\r\n  })\r\n\r\n  return {\r\n    title: t('title'),\r\n    description: t('description'),\r\n  }\r\n}\r\n\r\nexport default ModuleDetailPage\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,MAAM,mBAAmB,OAAO,EACrC,MAAM,EAGP;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,IAAI,MAAM,IAAA,2QAAe,EAAC;QAC9B;QACA,WAAW;IACb;IAEA,OAAO;QACL,OAAO,EAAE;QACT,aAAa,EAAE;IACjB;AACF;uCAEe,sLAAgB", "debugId": null}}]}