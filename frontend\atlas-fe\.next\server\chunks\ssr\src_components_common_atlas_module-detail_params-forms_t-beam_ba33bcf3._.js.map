{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-general-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { MODULE_POLARITY, modulePolarity } from '@atlas/constants/module'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type TBeamGeneralFormInputs,\r\n  tBeamGeneralSchema,\r\n} from '@atlas/types/schemas/t-beam-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<TBeamGeneralFormInputs>\r\n  onSave: () => void\r\n}\r\n\r\nexport const TBeamGeneralForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.t-beam.general')\r\n  const tPolarity = useTranslations('forms.common.polarity')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n  const form = useForm<TBeamGeneralFormInputs>({\r\n    resolver: zodResolver(tBeamGeneralSchema),\r\n    defaultValues: {\r\n      initialDeformation: defaultValues?.initialDeformation ?? 0,\r\n      polarity: defaultValues?.polarity ?? modulePolarity.POSITIVE,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: TBeamGeneralFormInputs) => {\r\n      mutate({ projectId, moduleId, body })\r\n    },\r\n    [mutate, projectId, moduleId],\r\n  )\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"polarity\"\r\n          options={MODULE_POLARITY}\r\n          optionLabelFn={p => tPolarity(p)}\r\n          t={t}\r\n          description\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"initialDeformation\"\r\n          t={t}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAGA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AAUO,MAAM,mBAAmB,CAAC,EAC/B,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,YAAY,IAAA,yNAAe,EAAC;IAClC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,cAAc,IAAA,wMAAc;IAClC,MAAM,OAAO,IAAA,yKAAO,EAAyB;QAC3C,UAAU,IAAA,6KAAW,EAAC,kKAAkB;QACxC,eAAe;YACb,oBAAoB,eAAe,sBAAsB;YACzD,UAAU,eAAe,YAAY,6IAAc,CAAC,QAAQ;QAC9D;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAW;wBAAE;wBAAW;oBAAS;iBAAE;YAChD;YACA;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,OAAO;YAAE;YAAW;YAAU;QAAK;IACrC,GACA;QAAC;QAAQ;QAAW;KAAS;IAG/B,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,8IAAe;oBACxB,eAAe,CAAA,IAAK,UAAU;oBAC9B,GAAG;oBACH,WAAW;;;;;;8BAEb,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,8OAAC,4IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,8OAAC,oOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-geometry-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { PolarityAwareNumberFormInput } from '@atlas/components/common/form/polarity-aware-number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport {\r\n  MODULE_GEOMETRY_EXPOSURE,\r\n  type ModulePolarity,\r\n  moduleGeometryExposure,\r\n} from '@atlas/constants/module'\r\nimport {\r\n  calculateTBeamEffectiveDepth,\r\n  calculateTBeamTotalHeight,\r\n} from '@atlas/functions/forms/t-beam-form-calculations'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type TBeamGeometryFormInputs as FormSchema,\r\n  tBeamGeometrySchema,\r\n} from '@atlas/types/schemas/t-beam-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FormSchema>\r\n  polarity?: ModulePolarity\r\n  onSave: () => void\r\n}\r\n\r\nexport const TBeamGeometryForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  polarity,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.t-beam.geometry')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n  const form = useForm<FormSchema>({\r\n    resolver: zodResolver(tBeamGeometrySchema),\r\n    defaultValues: {\r\n      primaryHeight: defaultValues?.primaryHeight,\r\n      primaryWidth: defaultValues?.primaryWidth,\r\n      secondaryWidth: defaultValues?.secondaryWidth,\r\n      secondaryHeight: defaultValues?.secondaryHeight,\r\n      concreteCover1: defaultValues?.concreteCover1,\r\n      concreteCover2: defaultValues?.concreteCover2,\r\n      totalHeight: defaultValues?.totalHeight,\r\n      effectiveDepth: defaultValues?.effectiveDepth,\r\n      exposure: defaultValues?.exposure ?? moduleGeometryExposure.INTERNAL,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (geometry: FormSchema) => {\r\n      mutate({ projectId, moduleId, body: { geometry } })\r\n    },\r\n    [mutate, projectId, moduleId],\r\n  )\r\n\r\n  const [\r\n    _primaryWidth,\r\n    primaryHeight,\r\n    secondaryHeight,\r\n    _concreteCover2,\r\n    concreteCover1,\r\n  ] = form.watch([\r\n    'primaryWidth',\r\n    'primaryHeight',\r\n    'secondaryHeight',\r\n    'concreteCover2',\r\n    'concreteCover1',\r\n  ])\r\n\r\n  // Calculate total height when web height and flange thickness change\r\n  useEffect(() => {\r\n    if (primaryHeight && secondaryHeight) {\r\n      form.setValue(\r\n        'totalHeight',\r\n        calculateTBeamTotalHeight(primaryHeight, secondaryHeight),\r\n      )\r\n    }\r\n  }, [primaryHeight, secondaryHeight, form])\r\n\r\n  // Calculate effective depth based on polarity\r\n  useEffect(() => {\r\n    const totalHeight = calculateTBeamTotalHeight(\r\n      primaryHeight || 0,\r\n      secondaryHeight || 0,\r\n    )\r\n    if (totalHeight && concreteCover1) {\r\n      form.setValue(\r\n        'effectiveDepth',\r\n        calculateTBeamEffectiveDepth(totalHeight, concreteCover1),\r\n      )\r\n    }\r\n  }, [primaryHeight, secondaryHeight, concreteCover1, form])\r\n\r\n  return (\r\n    <div className=\"flex flex-col justify-center gap-2\">\r\n      <Image\r\n        src={\r\n          polarity === 'NEGATIVE'\r\n            ? '/assets/t_beam/T_beam_geometry_M-.png'\r\n            : '/assets/t_beam/T_beam_geometry_M+.png'\r\n        }\r\n        alt=\"T-beam geometry\"\r\n        height={500}\r\n        width={500}\r\n        className=\"mx-auto rounded-md object-contain\"\r\n        priority\r\n      />\r\n      <Form {...form}>\r\n        <form\r\n          className=\"space-y-4 rounded-md border p-4 grow\"\r\n          onSubmit={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          <PolarityAwareNumberFormInput\r\n            control={form.control}\r\n            name=\"primaryHeight\"\r\n            t={t}\r\n            polarity={polarity}\r\n          />\r\n          <PolarityAwareNumberFormInput\r\n            control={form.control}\r\n            name=\"primaryWidth\"\r\n            t={t}\r\n            polarity={polarity}\r\n          />\r\n          <PolarityAwareNumberFormInput\r\n            control={form.control}\r\n            name=\"secondaryWidth\"\r\n            t={t}\r\n            polarity={polarity}\r\n          />\r\n          <PolarityAwareNumberFormInput\r\n            control={form.control}\r\n            name=\"secondaryHeight\"\r\n            t={t}\r\n            polarity={polarity}\r\n          />\r\n          <PolarityAwareNumberFormInput\r\n            control={form.control}\r\n            name=\"concreteCover1\"\r\n            t={t}\r\n            polarity={polarity}\r\n          />\r\n          <PolarityAwareNumberFormInput\r\n            control={form.control}\r\n            name=\"concreteCover2\"\r\n            t={t}\r\n            polarity={polarity}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"totalHeight\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"effectiveDepth\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <SelectFormFixedInput\r\n            control={form.control}\r\n            name=\"exposure\"\r\n            options={MODULE_GEOMETRY_EXPOSURE}\r\n            optionLabelFn={p => t(`exposure.${p}`)}\r\n            t={t}\r\n          />\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full sm:w-auto\"\r\n            disabled={isPending}\r\n            onClick={form.handleSubmit(handleFormSubmit)}\r\n          >\r\n            {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n            {tCommon('save')}\r\n          </Button>\r\n        </form>\r\n      </Form>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAKA;AAMA;AACA;AAIA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AAWO,MAAM,oBAAoB,CAAC,EAChC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,QAAQ,EACR,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,cAAc,IAAA,wMAAc;IAClC,MAAM,OAAO,IAAA,yKAAO,EAAa;QAC/B,UAAU,IAAA,6KAAW,EAAC,mKAAmB;QACzC,eAAe;YACb,eAAe,eAAe;YAC9B,cAAc,eAAe;YAC7B,gBAAgB,eAAe;YAC/B,iBAAiB,eAAe;YAChC,gBAAgB,eAAe;YAC/B,gBAAgB,eAAe;YAC/B,aAAa,eAAe;YAC5B,gBAAgB,eAAe;YAC/B,UAAU,eAAe,YAAY,qJAAsB,CAAC,QAAQ;QACtE;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAW;wBAAE;wBAAW;oBAAS;iBAAE;YAChD;YACA;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,OAAO;YAAE;YAAW;YAAU,MAAM;gBAAE;YAAS;QAAE;IACnD,GACA;QAAC;QAAQ;QAAW;KAAS;IAG/B,MAAM,CACJ,eACA,eACA,iBACA,iBACA,eACD,GAAG,KAAK,KAAK,CAAC;QACb;QACA;QACA;QACA;QACA;KACD;IAED,qEAAqE;IACrE,IAAA,kNAAS,EAAC;QACR,IAAI,iBAAiB,iBAAiB;YACpC,KAAK,QAAQ,CACX,eACA,IAAA,2LAAyB,EAAC,eAAe;QAE7C;IACF,GAAG;QAAC;QAAe;QAAiB;KAAK;IAEzC,8CAA8C;IAC9C,IAAA,kNAAS,EAAC;QACR,MAAM,cAAc,IAAA,2LAAyB,EAC3C,iBAAiB,GACjB,mBAAmB;QAErB,IAAI,eAAe,gBAAgB;YACjC,KAAK,QAAQ,CACX,kBACA,IAAA,8LAA4B,EAAC,aAAa;QAE9C;IACF,GAAG;QAAC;QAAe;QAAiB;QAAgB;KAAK;IAEzD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,wIAAK;gBACJ,KACE,aAAa,aACT,0CACA;gBAEN,KAAI;gBACJ,QAAQ;gBACR,OAAO;gBACP,WAAU;gBACV,QAAQ;;;;;;0BAEV,8OAAC,wIAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,8OAAC;oBACC,WAAU;oBACV,UAAU,KAAK,YAAY,CAAC;;sCAE5B,8OAAC,oNAA4B;4BAC3B,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,oNAA4B;4BAC3B,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,oNAA4B;4BAC3B,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,oNAA4B;4BAC3B,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,oNAA4B;4BAC3B,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,oNAA4B;4BAC3B,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,gMAAoB;4BACnB,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS,uJAAwB;4BACjC,eAAe,CAAA,IAAK,EAAE,CAAC,SAAS,EAAE,GAAG;4BACrC,GAAG;;;;;;sCAEL,8OAAC,4IAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU;4BACV,SAAS,KAAK,YAAY,CAAC;;gCAE1B,2BAAa,8OAAC,oOAAW;oCAAC,WAAU;;;;;;gCACpC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;AAMrB", "debugId": null}}, {"offset": {"line": 420, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-concrete-class-form-section.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { StringFormInput } from '@atlas/components/common/form/string-form-input'\r\nimport { moduleMaterialKnowledgeLevelValues } from '@atlas/constants/module'\r\nimport {\r\n  calculateTBeamDesignStrengthForBrittleMechanisms,\r\n  calculateTBeamDesignStrengthForDuctileMechanisms,\r\n} from '@atlas/functions/forms/t-beam-form-calculations'\r\nimport { useReinforcedConcreteMaterials } from '@atlas/lib/query/materials/use-reinforced-concrete-materials'\r\nimport type { TBeamMaterialFormInputs } from '@atlas/types/schemas/t-beam-form'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useMemo } from 'react'\r\nimport { useFormContext } from 'react-hook-form'\r\n\r\ntype Props = {\r\n  session: Session\r\n}\r\n\r\nexport function TBeamConcreteClassFormSection({ session }: Props) {\r\n  const t = useTranslations('forms.project-params.t-beam.materialProperties')\r\n\r\n  const form = useFormContext<TBeamMaterialFormInputs>()\r\n\r\n  const {\r\n    data: concreteMaterials,\r\n    isError: concreteMaterialsError,\r\n    isLoading: concreteMaterialsLoading,\r\n  } = useReinforcedConcreteMaterials({ session, body: { size: 100 } })\r\n\r\n  const customOption = { value: 'custom', label: t('concreteClass.custom') }\r\n\r\n  const concreteMaterialsOptions = [\r\n    ...(concreteMaterials?.content.map(m => ({\r\n      value: m.id,\r\n      label: m.name,\r\n    })) ?? []),\r\n    customOption,\r\n  ]\r\n\r\n  const knowledgeLevel = form.watch('knowledgeLevel')\r\n  const concreteClassId = form.watch('concreteClass.id')\r\n  const averageCompressiveStrength = form.watch(\r\n    'concreteClass.averageCompressiveStrength',\r\n  )\r\n\r\n  const isCustomSelected = concreteClassId === 'custom'\r\n\r\n  const selectedConcreteMaterial = useMemo(\r\n    () => concreteMaterials?.content.find(m => m.id === concreteClassId),\r\n    [concreteClassId, concreteMaterials],\r\n  )\r\n\r\n  useEffect(() => {\r\n    if (!selectedConcreteMaterial || isCustomSelected) {\r\n      return\r\n    }\r\n    const selected = selectedConcreteMaterial\r\n    if (form.getValues('concreteClass.name') !== selected.name) {\r\n      form.setValue('concreteClass.name', selected.name)\r\n    }\r\n    if (\r\n      form.getValues('concreteClass.cubeCompressiveStrength') !==\r\n      selected.cubeCompressiveStrength\r\n    ) {\r\n      form.setValue(\r\n        'concreteClass.cubeCompressiveStrength',\r\n        selected.cubeCompressiveStrength,\r\n      )\r\n    }\r\n    if (\r\n      form.getValues('concreteClass.cylinderCompressiveStrength') !==\r\n      selected.cylinderCompressiveStrength\r\n    ) {\r\n      form.setValue(\r\n        'concreteClass.cylinderCompressiveStrength',\r\n        selected.cylinderCompressiveStrength,\r\n      )\r\n    }\r\n    if (\r\n      form.getValues('concreteClass.averageCompressiveStrength') !==\r\n      selected.averageCompressiveStrength\r\n    ) {\r\n      form.setValue(\r\n        'concreteClass.averageCompressiveStrength',\r\n        selected.averageCompressiveStrength,\r\n      )\r\n    }\r\n    if (\r\n      form.getValues('concreteClass.averageTensileStrength') !==\r\n      selected.averageTensileStrength\r\n    ) {\r\n      form.setValue(\r\n        'concreteClass.averageTensileStrength',\r\n        selected.averageTensileStrength,\r\n      )\r\n    }\r\n    if (\r\n      form.getValues('concreteClass.elasticModulus') !== selected.elasticModulus\r\n    ) {\r\n      form.setValue('concreteClass.elasticModulus', selected.elasticModulus)\r\n    }\r\n  }, [form, isCustomSelected, selectedConcreteMaterial])\r\n\r\n  useEffect(() => {\r\n    if (\r\n      knowledgeLevel &&\r\n      averageCompressiveStrength !== undefined &&\r\n      averageCompressiveStrength !== null\r\n    ) {\r\n      const confidenceFactor =\r\n        moduleMaterialKnowledgeLevelValues[knowledgeLevel]\r\n      form.setValue(\r\n        'concreteClass.designCompressiveStrengthForBrittleMechanisms',\r\n        calculateTBeamDesignStrengthForBrittleMechanisms(\r\n          averageCompressiveStrength,\r\n          confidenceFactor,\r\n        ),\r\n      )\r\n      form.setValue(\r\n        'concreteClass.designCompressiveStrengthForDuctileMechanisms',\r\n        calculateTBeamDesignStrengthForDuctileMechanisms(\r\n          averageCompressiveStrength,\r\n          confidenceFactor,\r\n        ),\r\n      )\r\n    }\r\n  }, [knowledgeLevel, averageCompressiveStrength, form])\r\n\r\n  return (\r\n    <>\r\n      <SelectFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.id\"\r\n        options={concreteMaterialsOptions}\r\n        t={t}\r\n        loading={concreteMaterialsLoading}\r\n        requestError={concreteMaterialsError}\r\n        errorMessage={t('concreteClass.error')}\r\n      />\r\n      {isCustomSelected && (\r\n        <StringFormInput\r\n          control={form.control}\r\n          name=\"concreteClass.name\"\r\n          t={t}\r\n        />\r\n      )}\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.cubeCompressiveStrength\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.cylinderCompressiveStrength\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.averageCompressiveStrength\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.averageTensileStrength\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.elasticModulus\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.designCompressiveStrengthForDuctileMechanisms\"\r\n        t={t}\r\n        disabled={true}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.designCompressiveStrengthForBrittleMechanisms\"\r\n        t={t}\r\n        disabled={true}\r\n      />\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAIA;AAGA;AACA;AACA;;;;;;;;;;;AAMO,SAAS,8BAA8B,EAAE,OAAO,EAAS;IAC9D,MAAM,IAAI,IAAA,yNAAe,EAAC;IAE1B,MAAM,OAAO,IAAA,gLAAc;IAE3B,MAAM,EACJ,MAAM,iBAAiB,EACvB,SAAS,sBAAsB,EAC/B,WAAW,wBAAwB,EACpC,GAAG,IAAA,gNAA8B,EAAC;QAAE;QAAS,MAAM;YAAE,MAAM;QAAI;IAAE;IAElE,MAAM,eAAe;QAAE,OAAO;QAAU,OAAO,EAAE;IAAwB;IAEzE,MAAM,2BAA2B;WAC3B,mBAAmB,QAAQ,IAAI,CAAA,IAAK,CAAC;gBACvC,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,IAAI;YACf,CAAC,MAAM,EAAE;QACT;KACD;IAED,MAAM,iBAAiB,KAAK,KAAK,CAAC;IAClC,MAAM,kBAAkB,KAAK,KAAK,CAAC;IACnC,MAAM,6BAA6B,KAAK,KAAK,CAC3C;IAGF,MAAM,mBAAmB,oBAAoB;IAE7C,MAAM,2BAA2B,IAAA,gNAAO,EACtC,IAAM,mBAAmB,QAAQ,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK,kBACpD;QAAC;QAAiB;KAAkB;IAGtC,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,4BAA4B,kBAAkB;YACjD;QACF;QACA,MAAM,WAAW;QACjB,IAAI,KAAK,SAAS,CAAC,0BAA0B,SAAS,IAAI,EAAE;YAC1D,KAAK,QAAQ,CAAC,sBAAsB,SAAS,IAAI;QACnD;QACA,IACE,KAAK,SAAS,CAAC,6CACf,SAAS,uBAAuB,EAChC;YACA,KAAK,QAAQ,CACX,yCACA,SAAS,uBAAuB;QAEpC;QACA,IACE,KAAK,SAAS,CAAC,iDACf,SAAS,2BAA2B,EACpC;YACA,KAAK,QAAQ,CACX,6CACA,SAAS,2BAA2B;QAExC;QACA,IACE,KAAK,SAAS,CAAC,gDACf,SAAS,0BAA0B,EACnC;YACA,KAAK,QAAQ,CACX,4CACA,SAAS,0BAA0B;QAEvC;QACA,IACE,KAAK,SAAS,CAAC,4CACf,SAAS,sBAAsB,EAC/B;YACA,KAAK,QAAQ,CACX,wCACA,SAAS,sBAAsB;QAEnC;QACA,IACE,KAAK,SAAS,CAAC,oCAAoC,SAAS,cAAc,EAC1E;YACA,KAAK,QAAQ,CAAC,gCAAgC,SAAS,cAAc;QACvE;IACF,GAAG;QAAC;QAAM;QAAkB;KAAyB;IAErD,IAAA,kNAAS,EAAC;QACR,IACE,kBACA,+BAA+B,aAC/B,+BAA+B,MAC/B;YACA,MAAM,mBACJ,iKAAkC,CAAC,eAAe;YACpD,KAAK,QAAQ,CACX,+DACA,IAAA,kNAAgD,EAC9C,4BACA;YAGJ,KAAK,QAAQ,CACX,+DACA,IAAA,kNAAgD,EAC9C,4BACA;QAGN;IACF,GAAG;QAAC;QAAgB;QAA4B;KAAK;IAErD,qBACE;;0BACE,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,SAAS;gBACT,GAAG;gBACH,SAAS;gBACT,cAAc;gBACd,cAAc,EAAE;;;;;;YAEjB,kCACC,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;;;;;;0BAGP,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU;;;;;;0BAEZ,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU;;;;;;;;AAIlB", "debugId": null}}, {"offset": {"line": 612, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-steel-grade-form-section.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { StringFormInput } from '@atlas/components/common/form/string-form-input'\r\nimport { moduleMaterialKnowledgeLevelValues } from '@atlas/constants/module'\r\nimport {\r\n  calculateTBeamDesignYieldStrengthForBrittleMechanisms,\r\n  calculateTBeamDesignYieldStrengthForDuctileMechanisms,\r\n} from '@atlas/functions/forms/t-beam-form-calculations'\r\nimport { useSteelGradesMaterials } from '@atlas/lib/query/materials/use-steel-grades-materials'\r\nimport type { TBeamMaterialFormInputs } from '@atlas/types/schemas/t-beam-form'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useMemo } from 'react'\r\nimport { useFormContext } from 'react-hook-form'\r\n\r\nexport function TBeamSteelGradeFormSection({ session }: { session: Session }) {\r\n  const t = useTranslations('forms.project-params.t-beam.materialProperties')\r\n  const form = useFormContext<TBeamMaterialFormInputs>()\r\n\r\n  const {\r\n    data: steelMaterials,\r\n    isError: steelMaterialsError,\r\n    isLoading: steelMaterialsLoading,\r\n  } = useSteelGradesMaterials({ session, body: { size: 100 } })\r\n\r\n  const customOption = { value: 'custom', label: t('steelGrade.custom') }\r\n  const steelMaterialsOptions = [\r\n    ...(steelMaterials?.content.map(m => ({ value: m.id, label: m.name })) ??\r\n      []),\r\n    customOption,\r\n  ]\r\n\r\n  const knowledgeLevel = form.watch('knowledgeLevel')\r\n  const steelGradeId = form.watch('steelGrade.id')\r\n  const yieldStrength = form.watch('steelGrade.yieldStrength')\r\n\r\n  const isCustomSelected = steelGradeId === 'custom'\r\n\r\n  const selectedSteelMaterial = useMemo(\r\n    () => steelMaterials?.content.find(m => m.id === steelGradeId),\r\n    [steelGradeId, steelMaterials],\r\n  )\r\n\r\n  useEffect(() => {\r\n    if (!selectedSteelMaterial || isCustomSelected) {\r\n      return\r\n    }\r\n    const selected = selectedSteelMaterial\r\n    if (form.getValues('steelGrade.name') !== selected.name) {\r\n      form.setValue('steelGrade.name', selected.name)\r\n    }\r\n    if (form.getValues('steelGrade.yieldStrength') !== selected.yieldStrength) {\r\n      form.setValue('steelGrade.yieldStrength', selected.yieldStrength)\r\n    }\r\n    if (\r\n      form.getValues('steelGrade.tensileStrength') !== selected.tensileStrength\r\n    ) {\r\n      form.setValue('steelGrade.tensileStrength', selected.tensileStrength)\r\n    }\r\n    if (\r\n      form.getValues('steelGrade.elongationPercentage') !==\r\n      selected.elongationPercentage\r\n    ) {\r\n      form.setValue(\r\n        'steelGrade.elongationPercentage',\r\n        selected.elongationPercentage,\r\n      )\r\n    }\r\n    if (\r\n      form.getValues('steelGrade.elasticModulus') !== selected.elasticModulus\r\n    ) {\r\n      form.setValue('steelGrade.elasticModulus', selected.elasticModulus)\r\n    }\r\n  }, [form, isCustomSelected, selectedSteelMaterial])\r\n\r\n  useEffect(() => {\r\n    if (\r\n      knowledgeLevel &&\r\n      yieldStrength !== undefined &&\r\n      yieldStrength !== null\r\n    ) {\r\n      const confidenceFactor =\r\n        moduleMaterialKnowledgeLevelValues[knowledgeLevel]\r\n      form.setValue(\r\n        'steelGrade.designYieldStrengthForDuctileMechanisms',\r\n        calculateTBeamDesignYieldStrengthForDuctileMechanisms(\r\n          yieldStrength,\r\n          confidenceFactor,\r\n        ),\r\n      )\r\n      form.setValue(\r\n        'steelGrade.designYieldStrengthForBrittleMechanisms',\r\n        calculateTBeamDesignYieldStrengthForBrittleMechanisms(\r\n          yieldStrength,\r\n          confidenceFactor,\r\n        ),\r\n      )\r\n    }\r\n  }, [knowledgeLevel, yieldStrength, form])\r\n\r\n  return (\r\n    <>\r\n      <SelectFormInput\r\n        control={form.control}\r\n        name=\"steelGrade.id\"\r\n        options={steelMaterialsOptions}\r\n        t={t}\r\n        loading={steelMaterialsLoading}\r\n        requestError={steelMaterialsError}\r\n        errorMessage={t('steelGrade.error')}\r\n      />\r\n      {isCustomSelected && (\r\n        <StringFormInput control={form.control} name=\"steelGrade.name\" t={t} />\r\n      )}\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"steelGrade.yieldStrength\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"steelGrade.elasticModulus\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"steelGrade.designYieldStrengthForDuctileMechanisms\"\r\n        t={t}\r\n        disabled={true}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"steelGrade.designYieldStrengthForBrittleMechanisms\"\r\n        t={t}\r\n        disabled={true}\r\n      />\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAIA;AAGA;AACA;AACA;;;;;;;;;;;AAEO,SAAS,2BAA2B,EAAE,OAAO,EAAwB;IAC1E,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,OAAO,IAAA,gLAAc;IAE3B,MAAM,EACJ,MAAM,cAAc,EACpB,SAAS,mBAAmB,EAC5B,WAAW,qBAAqB,EACjC,GAAG,IAAA,kMAAuB,EAAC;QAAE;QAAS,MAAM;YAAE,MAAM;QAAI;IAAE;IAE3D,MAAM,eAAe;QAAE,OAAO;QAAU,OAAO,EAAE;IAAqB;IACtE,MAAM,wBAAwB;WACxB,gBAAgB,QAAQ,IAAI,CAAA,IAAK,CAAC;gBAAE,OAAO,EAAE,EAAE;gBAAE,OAAO,EAAE,IAAI;YAAC,CAAC,MAClE,EAAE;QACJ;KACD;IAED,MAAM,iBAAiB,KAAK,KAAK,CAAC;IAClC,MAAM,eAAe,KAAK,KAAK,CAAC;IAChC,MAAM,gBAAgB,KAAK,KAAK,CAAC;IAEjC,MAAM,mBAAmB,iBAAiB;IAE1C,MAAM,wBAAwB,IAAA,gNAAO,EACnC,IAAM,gBAAgB,QAAQ,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK,eACjD;QAAC;QAAc;KAAe;IAGhC,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,yBAAyB,kBAAkB;YAC9C;QACF;QACA,MAAM,WAAW;QACjB,IAAI,KAAK,SAAS,CAAC,uBAAuB,SAAS,IAAI,EAAE;YACvD,KAAK,QAAQ,CAAC,mBAAmB,SAAS,IAAI;QAChD;QACA,IAAI,KAAK,SAAS,CAAC,gCAAgC,SAAS,aAAa,EAAE;YACzE,KAAK,QAAQ,CAAC,4BAA4B,SAAS,aAAa;QAClE;QACA,IACE,KAAK,SAAS,CAAC,kCAAkC,SAAS,eAAe,EACzE;YACA,KAAK,QAAQ,CAAC,8BAA8B,SAAS,eAAe;QACtE;QACA,IACE,KAAK,SAAS,CAAC,uCACf,SAAS,oBAAoB,EAC7B;YACA,KAAK,QAAQ,CACX,mCACA,SAAS,oBAAoB;QAEjC;QACA,IACE,KAAK,SAAS,CAAC,iCAAiC,SAAS,cAAc,EACvE;YACA,KAAK,QAAQ,CAAC,6BAA6B,SAAS,cAAc;QACpE;IACF,GAAG;QAAC;QAAM;QAAkB;KAAsB;IAElD,IAAA,kNAAS,EAAC;QACR,IACE,kBACA,kBAAkB,aAClB,kBAAkB,MAClB;YACA,MAAM,mBACJ,iKAAkC,CAAC,eAAe;YACpD,KAAK,QAAQ,CACX,sDACA,IAAA,uNAAqD,EACnD,eACA;YAGJ,KAAK,QAAQ,CACX,sDACA,IAAA,uNAAqD,EACnD,eACA;QAGN;IACF,GAAG;QAAC;QAAgB;QAAe;KAAK;IAExC,qBACE;;0BACE,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,SAAS;gBACT,GAAG;gBACH,SAAS;gBACT,cAAc;gBACd,cAAc,EAAE;;;;;;YAEjB,kCACC,8OAAC,kLAAe;gBAAC,SAAS,KAAK,OAAO;gBAAE,MAAK;gBAAkB,GAAG;;;;;;0BAEpE,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU;;;;;;0BAEZ,8OAAC,kLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU;;;;;;;;AAIlB", "debugId": null}}, {"offset": {"line": 771, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-material-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport {\r\n  MODULE_MATERIAL_KNOWLEDGE_LEVEL,\r\n  moduleMaterialKnowledgeLevel,\r\n  moduleMaterialKnowledgeLevelValues,\r\n} from '@atlas/constants/module'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type TBeamMaterialFormInputs as FormSchema,\r\n  tBeamMaterialSchema,\r\n} from '@atlas/types/schemas/t-beam-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\nimport { TBeamConcreteClassFormSection } from './t-beam-concrete-class-form-section'\r\nimport { TBeamSteelGradeFormSection } from './t-beam-steel-grade-form-section'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FormSchema>\r\n  onSave: () => void\r\n}\r\n\r\nexport const TBeamMaterialForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.t-beam.materialProperties')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n  const form = useForm<FormSchema>({\r\n    resolver: zodResolver(tBeamMaterialSchema),\r\n    defaultValues: {\r\n      knowledgeLevel:\r\n        defaultValues?.knowledgeLevel ?? moduleMaterialKnowledgeLevel.LC1,\r\n      confidenceFactor: defaultValues?.knowledgeLevel\r\n        ? moduleMaterialKnowledgeLevelValues[defaultValues?.knowledgeLevel]\r\n        : moduleMaterialKnowledgeLevelValues[moduleMaterialKnowledgeLevel.LC1],\r\n      concreteClass: {\r\n        id: defaultValues?.concreteClass?.id,\r\n        name: defaultValues?.concreteClass?.name,\r\n        cubeCompressiveStrength:\r\n          defaultValues?.concreteClass?.cubeCompressiveStrength,\r\n        cylinderCompressiveStrength:\r\n          defaultValues?.concreteClass?.cylinderCompressiveStrength,\r\n        averageCompressiveStrength:\r\n          defaultValues?.concreteClass?.averageCompressiveStrength,\r\n        averageTensileStrength:\r\n          defaultValues?.concreteClass?.averageTensileStrength,\r\n        elasticModulus: defaultValues?.concreteClass?.elasticModulus,\r\n        designCompressiveStrengthForBrittleMechanisms:\r\n          defaultValues?.concreteClass\r\n            ?.designCompressiveStrengthForBrittleMechanisms,\r\n        designCompressiveStrengthForDuctileMechanisms:\r\n          defaultValues?.concreteClass\r\n            ?.designCompressiveStrengthForDuctileMechanisms,\r\n      },\r\n      steelGrade: {\r\n        id: defaultValues?.steelGrade?.id,\r\n        name: defaultValues?.steelGrade?.name,\r\n        yieldStrength: defaultValues?.steelGrade?.yieldStrength,\r\n        tensileStrength: defaultValues?.steelGrade?.tensileStrength,\r\n        elongationPercentage: defaultValues?.steelGrade?.elongationPercentage,\r\n        elasticModulus: defaultValues?.steelGrade?.elasticModulus,\r\n        designYieldStrengthForBrittleMechanisms:\r\n          defaultValues?.steelGrade?.designYieldStrengthForBrittleMechanisms,\r\n        designYieldStrengthForDuctileMechanisms:\r\n          defaultValues?.steelGrade?.designYieldStrengthForDuctileMechanisms,\r\n      },\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (materialProperties: FormSchema) => {\r\n      mutate({ projectId, moduleId, body: { materialProperties } })\r\n    },\r\n    [mutate, projectId, moduleId],\r\n  )\r\n\r\n  const knowledgeLevel = form.watch('knowledgeLevel')\r\n\r\n  useEffect(() => {\r\n    if (knowledgeLevel) {\r\n      form.setValue(\r\n        'confidenceFactor',\r\n        moduleMaterialKnowledgeLevelValues[knowledgeLevel],\r\n      )\r\n    }\r\n  }, [knowledgeLevel, form])\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"knowledgeLevel\"\r\n          options={MODULE_MATERIAL_KNOWLEDGE_LEVEL}\r\n          optionLabelFn={p => t(`knowledgeLevel.${p}`)}\r\n          t={t}\r\n          description\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"confidenceFactor\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <Separator />\r\n        <TBeamConcreteClassFormSection session={session} />\r\n        <Separator />\r\n        <TBeamSteelGradeFormSection session={session} />\r\n        <Button type=\"submit\" className=\"w-full sm:w-auto\" disabled={isPending}>\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AAUO,MAAM,oBAAoB,CAAC,EAChC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,cAAc,IAAA,wMAAc;IAClC,MAAM,OAAO,IAAA,yKAAO,EAAa;QAC/B,UAAU,IAAA,6KAAW,EAAC,mKAAmB;QACzC,eAAe;YACb,gBACE,eAAe,kBAAkB,2JAA4B,CAAC,GAAG;YACnE,kBAAkB,eAAe,iBAC7B,iKAAkC,CAAC,eAAe,eAAe,GACjE,iKAAkC,CAAC,2JAA4B,CAAC,GAAG,CAAC;YACxE,eAAe;gBACb,IAAI,eAAe,eAAe;gBAClC,MAAM,eAAe,eAAe;gBACpC,yBACE,eAAe,eAAe;gBAChC,6BACE,eAAe,eAAe;gBAChC,4BACE,eAAe,eAAe;gBAChC,wBACE,eAAe,eAAe;gBAChC,gBAAgB,eAAe,eAAe;gBAC9C,+CACE,eAAe,eACX;gBACN,+CACE,eAAe,eACX;YACR;YACA,YAAY;gBACV,IAAI,eAAe,YAAY;gBAC/B,MAAM,eAAe,YAAY;gBACjC,eAAe,eAAe,YAAY;gBAC1C,iBAAiB,eAAe,YAAY;gBAC5C,sBAAsB,eAAe,YAAY;gBACjD,gBAAgB,eAAe,YAAY;gBAC3C,yCACE,eAAe,YAAY;gBAC7B,yCACE,eAAe,YAAY;YAC/B;QACF;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAW;wBAAE;wBAAW;oBAAS;iBAAE;YAChD;YACA;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,OAAO;YAAE;YAAW;YAAU,MAAM;gBAAE;YAAmB;QAAE;IAC7D,GACA;QAAC;QAAQ;QAAW;KAAS;IAG/B,MAAM,iBAAiB,KAAK,KAAK,CAAC;IAElC,IAAA,kNAAS,EAAC;QACR,IAAI,gBAAgB;YAClB,KAAK,QAAQ,CACX,oBACA,iKAAkC,CAAC,eAAe;QAEtD;IACF,GAAG;QAAC;QAAgB;KAAK;IAEzB,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,8OAAC,gMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,8JAA+B;oBACxC,eAAe,CAAA,IAAK,EAAE,CAAC,eAAe,EAAE,GAAG;oBAC3C,GAAG;oBACH,WAAW;;;;;;8BAEb,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kJAAS;;;;;8BACV,8OAAC,2RAA6B;oBAAC,SAAS;;;;;;8BACxC,8OAAC,kJAAS;;;;;8BACV,8OAAC,qRAA0B;oBAAC,SAAS;;;;;;8BACrC,8OAAC,4IAAM;oBAAC,MAAK;oBAAS,WAAU;oBAAmB,UAAU;;wBAC1D,2BAAa,8OAAC,oOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 973, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-reinforcement-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { PolarityAwareNumberFormInput } from '@atlas/components/common/form/polarity-aware-number-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport type { ModulePolarity } from '@atlas/constants/module'\r\nimport { calculateTBeamArmorArea } from '@atlas/functions/forms/t-beam-form-calculations'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport {\r\n  type TBeamRebarFormInputs as FormSchema,\r\n  tBeamRebarSchema,\r\n} from '@atlas/types/schemas/t-beam-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FormSchema>\r\n  onSave: () => void\r\n  polarity: ModulePolarity\r\n}\r\n\r\nexport const TBeamReinforcementForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  onSave,\r\n  polarity,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.t-beam.tBeamRebar')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n  const form = useForm<FormSchema>({\r\n    resolver: zodResolver(tBeamRebarSchema),\r\n    defaultValues: {\r\n      compressionRebars: {\r\n        diameter: defaultValues?.compressionRebars?.diameter,\r\n        quantity: defaultValues?.compressionRebars?.quantity,\r\n        area: defaultValues?.compressionRebars?.area,\r\n      },\r\n      tensionRebars: {\r\n        diameter: defaultValues?.tensionRebars?.diameter,\r\n        quantity: defaultValues?.tensionRebars?.quantity,\r\n        area: defaultValues?.tensionRebars?.area,\r\n      },\r\n    },\r\n  })\r\n\r\n  const [compressionRebars, tensionRebars] = form.watch([\r\n    'compressionRebars',\r\n    'tensionRebars',\r\n  ])\r\n\r\n  useEffect(() => {\r\n    if (compressionRebars.diameter && compressionRebars.quantity) {\r\n      form.setValue(\r\n        'compressionRebars.area',\r\n        calculateTBeamArmorArea(\r\n          compressionRebars.diameter,\r\n          compressionRebars.quantity,\r\n        ),\r\n      )\r\n    }\r\n  }, [compressionRebars.diameter, compressionRebars.quantity, form])\r\n\r\n  useEffect(() => {\r\n    if (tensionRebars.diameter && tensionRebars.quantity) {\r\n      form.setValue(\r\n        'tensionRebars.area',\r\n        calculateTBeamArmorArea(tensionRebars.diameter, tensionRebars.quantity),\r\n      )\r\n    }\r\n  }, [tensionRebars.diameter, tensionRebars.quantity, form])\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (tBeamRebar: FormSchema) => {\r\n      mutate({ projectId, moduleId, body: { tBeamRebar } })\r\n    },\r\n    [mutate, projectId, moduleId],\r\n  )\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <h1 className=\"text-md font-bold\">\r\n          {t(`compressionRebars.subtitlePolarity.${polarity}`)}\r\n        </h1>\r\n        <p className={cn('text-muted-foreground text-sm')}>\r\n          {t('compressionRebars.description')}\r\n        </p>\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"compressionRebars.diameter\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"compressionRebars.quantity\"\r\n          t={t}\r\n        />\r\n        <PolarityAwareNumberFormInput\r\n          control={form.control}\r\n          name=\"compressionRebars.area\"\r\n          t={t}\r\n          polarity={polarity}\r\n          disabled={true}\r\n        />\r\n        <Separator />\r\n        <h1 className=\"text-md font-bold\">\r\n          {t(`tensionRebars.subtitlePolarity.${polarity}`)}\r\n        </h1>\r\n        <p className={cn('text-muted-foreground text-sm')}>\r\n          {t('tensionRebars.description')}\r\n        </p>\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"tensionRebars.diameter\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"tensionRebars.quantity\"\r\n          t={t}\r\n        />\r\n        <PolarityAwareNumberFormInput\r\n          control={form.control}\r\n          name=\"tensionRebars.area\"\r\n          t={t}\r\n          polarity={polarity}\r\n          disabled={true}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAEA;AAGA;AACA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;AAWO,MAAM,yBAAyB,CAAC,EACrC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACN,QAAQ,EACF;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,cAAc,IAAA,wMAAc;IAClC,MAAM,OAAO,IAAA,yKAAO,EAAa;QAC/B,UAAU,IAAA,6KAAW,EAAC,gKAAgB;QACtC,eAAe;YACb,mBAAmB;gBACjB,UAAU,eAAe,mBAAmB;gBAC5C,UAAU,eAAe,mBAAmB;gBAC5C,MAAM,eAAe,mBAAmB;YAC1C;YACA,eAAe;gBACb,UAAU,eAAe,eAAe;gBACxC,UAAU,eAAe,eAAe;gBACxC,MAAM,eAAe,eAAe;YACtC;QACF;IACF;IAEA,MAAM,CAAC,mBAAmB,cAAc,GAAG,KAAK,KAAK,CAAC;QACpD;QACA;KACD;IAED,IAAA,kNAAS,EAAC;QACR,IAAI,kBAAkB,QAAQ,IAAI,kBAAkB,QAAQ,EAAE;YAC5D,KAAK,QAAQ,CACX,0BACA,IAAA,yLAAuB,EACrB,kBAAkB,QAAQ,EAC1B,kBAAkB,QAAQ;QAGhC;IACF,GAAG;QAAC,kBAAkB,QAAQ;QAAE,kBAAkB,QAAQ;QAAE;KAAK;IAEjE,IAAA,kNAAS,EAAC;QACR,IAAI,cAAc,QAAQ,IAAI,cAAc,QAAQ,EAAE;YACpD,KAAK,QAAQ,CACX,sBACA,IAAA,yLAAuB,EAAC,cAAc,QAAQ,EAAE,cAAc,QAAQ;QAE1E;IACF,GAAG;QAAC,cAAc,QAAQ;QAAE,cAAc,QAAQ;QAAE;KAAK;IAEzD,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAW;wBAAE;wBAAW;oBAAS;iBAAE;YAChD;YACA;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,OAAO;YAAE;YAAW;YAAU,MAAM;gBAAE;YAAW;QAAE;IACrD,GACA;QAAC;QAAQ;QAAW;KAAS;IAG/B,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,8OAAC;oBAAG,WAAU;8BACX,EAAE,CAAC,mCAAmC,EAAE,UAAU;;;;;;8BAErD,8OAAC;oBAAE,WAAW,IAAA,yHAAE,EAAC;8BACd,EAAE;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,8OAAC,oNAA4B;oBAC3B,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;oBACV,UAAU;;;;;;8BAEZ,8OAAC,kJAAS;;;;;8BACV,8OAAC;oBAAG,WAAU;8BACX,EAAE,CAAC,+BAA+B,EAAE,UAAU;;;;;;8BAEjD,8OAAC;oBAAE,WAAW,IAAA,yHAAE,EAAC;8BACd,EAAE;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,8OAAC,oNAA4B;oBAC3B,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;oBACV,UAAU;;;;;;8BAEZ,8OAAC,4IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,8OAAC,oOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 1223, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-result-card.tsx"], "sourcesContent": ["import {\r\n  <PERSON>,\r\n  Card<PERSON>ontent,\r\n  <PERSON><PERSON><PERSON>er,\r\n  <PERSON>Title,\r\n} from '@atlas/components/ui/card'\r\nimport type { TBeamFlexuralCalculationResult } from '@atlas/lib/api/modules/schemas/t-beam-params'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  results: TBeamFlexuralCalculationResult\r\n}\r\n\r\nexport const TBeamFlexuralResultCard = ({ results }: Props) => {\r\n  const t = useTranslations('forms.calculations.t-beam.flexural.results')\r\n\r\n  const resultItems = [\r\n    { key: 'momentCapacity', value: results.momentCapacity },\r\n    { key: 'equilibrium', value: results.equilibrium },\r\n    { key: 'checkResult', value: results.checkResult ? t('pass') : t('fail') },\r\n  ]\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n          {resultItems.map(item => (\r\n            <div\r\n              key={item.key}\r\n              className=\"flex flex-col space-y-1 p-3 bg-muted rounded-md\"\r\n            >\r\n              <span className=\"text-sm font-medium text-muted-foreground\">\r\n                {t(`${item.key}.label`)}\r\n              </span>\r\n              <span className=\"text-lg font-semibold\">\r\n                {typeof item.value === 'number'\r\n                  ? item.value.toFixed(4)\r\n                  : item.value}\r\n                {typeof item.value === 'number' && (\r\n                  <span className=\"text-sm text-muted-foreground ml-1\">\r\n                    {t(`${item.key}.unit`)}\r\n                  </span>\r\n                )}\r\n              </span>\r\n            </div>\r\n          ))}\r\n        </div>\r\n        <div\r\n          className={`p-4 rounded-md border-2 ${\r\n            results.checkResult\r\n              ? 'border-green-500 bg-green-50 text-green-800'\r\n              : 'border-red-500 bg-red-50 text-red-800'\r\n          }`}\r\n        >\r\n          <p className=\"font-semibold\">\r\n            {results.checkResult\r\n              ? t('verification.pass')\r\n              : t('verification.fail')}\r\n          </p>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAOA;;;;AAMO,MAAM,0BAA0B,CAAC,EAAE,OAAO,EAAS;IACxD,MAAM,IAAI,IAAA,yNAAe,EAAC;IAE1B,MAAM,cAAc;QAClB;YAAE,KAAK;YAAkB,OAAO,QAAQ,cAAc;QAAC;QACvD;YAAE,KAAK;YAAe,OAAO,QAAQ,WAAW;QAAC;QACjD;YAAE,KAAK;YAAe,OAAO,QAAQ,WAAW,GAAG,EAAE,UAAU,EAAE;QAAQ;KAC1E;IAED,qBACE,8OAAC,wIAAI;;0BACH,8OAAC,8IAAU;0BACT,cAAA,8OAAC,6IAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,8OAAC,+IAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAA,qBACf,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCAAK,WAAU;kDACb,EAAE,GAAG,KAAK,GAAG,CAAC,MAAM,CAAC;;;;;;kDAExB,8OAAC;wCAAK,WAAU;;4CACb,OAAO,KAAK,KAAK,KAAK,WACnB,KAAK,KAAK,CAAC,OAAO,CAAC,KACnB,KAAK,KAAK;4CACb,OAAO,KAAK,KAAK,KAAK,0BACrB,8OAAC;gDAAK,WAAU;0DACb,EAAE,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC;;;;;;;;;;;;;+BAZtB,KAAK,GAAG;;;;;;;;;;kCAmBnB,8OAAC;wBACC,WAAW,CAAC,wBAAwB,EAClC,QAAQ,WAAW,GACf,gDACA,yCACJ;kCAEF,cAAA,8OAAC;4BAAE,WAAU;sCACV,QAAQ,WAAW,GAChB,EAAE,uBACF,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAMlB", "debugId": null}}, {"offset": {"line": 1341, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-calculation.tsx"], "sourcesContent": ["import { TBeamFlexuralResultCard } from '@atlas/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-result-card'\r\nimport { CustomProductSection } from '@atlas/components/common/atlas/product/custom-product-section'\r\nimport { ProductSummaryCard } from '@atlas/components/common/atlas/product/product-summary-card'\r\nimport { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { productFiberType } from '@atlas/constants/product'\r\nimport type { ModuleWithParamsTBeam } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useModuleCalculation } from '@atlas/lib/mutation/modules/use-module-calculation'\r\nimport { useProductsByCategory } from '@atlas/lib/query/products/use-products-by-category'\r\nimport {\r\n  type TBeamFlexuralCalculationInput,\r\n  tBeamFlexuralCalculationSchema,\r\n} from '@atlas/types/schemas/t-beam-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useMemo } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  module: ModuleWithParamsTBeam\r\n  projectId: Project['id']\r\n}\r\n\r\nexport const TBeamFlexuralCalculation = ({\r\n  session,\r\n  module,\r\n  projectId,\r\n}: Props) => {\r\n  const t = useTranslations('forms.calculations.t-beam.flexural')\r\n  const tAction = useTranslations('actions.calculations.t-beam.flexural')\r\n  const tCommon = useTranslations('actions.common')\r\n  const { flexuralVerifyExecutionInput, flexuralVerifyExecutionResult } = module\r\n  const form = useForm<TBeamFlexuralCalculationInput>({\r\n    resolver: zodResolver(tBeamFlexuralCalculationSchema),\r\n    defaultValues: {\r\n      calculationType: 'FLEXURAL_VERIFY',\r\n      input: {\r\n        product: {\r\n          id: flexuralVerifyExecutionInput?.product.id,\r\n          name: flexuralVerifyExecutionInput?.product.name,\r\n          sourceType:\r\n            flexuralVerifyExecutionInput?.product.id === 'custom'\r\n              ? 'CUSTOM'\r\n              : 'DATABASE',\r\n          thickness: flexuralVerifyExecutionInput?.product.thickness,\r\n          tensileStrength:\r\n            flexuralVerifyExecutionInput?.product.tensileStrength,\r\n          elasticModulus: flexuralVerifyExecutionInput?.product.elasticModulus,\r\n          fiberType:\r\n            flexuralVerifyExecutionInput?.product.fiberType ??\r\n            productFiberType.CARBON,\r\n        },\r\n        stripWidth: flexuralVerifyExecutionInput?.stripWidth ?? 1,\r\n        layersNumber: flexuralVerifyExecutionInput?.layersNumber ?? 2,\r\n        bendingMoment: flexuralVerifyExecutionInput?.bendingMoment ?? 10,\r\n      },\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useModuleCalculation(session.accessToken, {\r\n    onSuccess: () => {\r\n      toast.success(tAction('calculate.success'))\r\n    },\r\n    onError: error => {\r\n      toast.error(tAction('calculate.failure', { error: error.message }))\r\n    },\r\n  })\r\n\r\n  const handleFormSubmit = (body: TBeamFlexuralCalculationInput) => {\r\n    const res = mutate({ projectId, moduleId: module.id, body })\r\n    console.log(res)\r\n  }\r\n\r\n  const {\r\n    data: products,\r\n    isError: errorGettingProducts,\r\n    isLoading: isLoadingProducts,\r\n  } = useProductsByCategory(session, 'T_BEAM', 0, 100)\r\n\r\n  const productsOptions = [\r\n    ...(products?.content.map(m => ({\r\n      value: m.id,\r\n      label: m.name ?? t('product.unnamed'),\r\n    })) ?? []),\r\n    { value: 'custom', label: t('product.custom') },\r\n  ]\r\n\r\n  const [productId] = form.watch(['input.product.id'])\r\n  const selectedProduct = useMemo(\r\n    () => products?.content.find(p => p.id === productId),\r\n    [productId, products],\r\n  )\r\n\r\n  useEffect(() => {\r\n    if (!productId) {\r\n      return\r\n    }\r\n    if (productId === 'custom') {\r\n      form.setValue('input.product.sourceType', 'CUSTOM')\r\n    }\r\n\r\n    if (selectedProduct) {\r\n      form.setValue('input.product', {\r\n        ...selectedProduct,\r\n        sourceType: 'DATABASE',\r\n      })\r\n    }\r\n  }, [form, productId, selectedProduct])\r\n\r\n  return (\r\n    <div className=\"flex flex-col justify-center gap-4\">\r\n      <Form {...form}>\r\n        <form\r\n          className=\"space-y-4 rounded-md border p-4\"\r\n          onSubmit={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          <h1 className=\"text-3xl font-bold\">{t('heading')}</h1>\r\n          <Image\r\n            src=\"/assets/t_beam/T_beam_flexural_verify.jpg\"\r\n            alt=\"flexural verify\"\r\n            height={250}\r\n            width={500}\r\n            className=\"mx-auto rounded-md object-contain\"\r\n            priority\r\n          />\r\n          <h1 className=\"text-xl font-bold\">{t('sub-heading')}</h1>\r\n          <SelectFormInput\r\n            control={form.control}\r\n            name=\"input.product.id\"\r\n            options={productsOptions}\r\n            t={t}\r\n            loading={isLoadingProducts}\r\n            requestError={errorGettingProducts}\r\n            errorMessage={t('products.error')}\r\n          />\r\n          {productId === 'custom' && <CustomProductSection />}\r\n          {selectedProduct && <ProductSummaryCard product={selectedProduct} />}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.stripWidth\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.layersNumber\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.bendingMoment\"\r\n            t={t}\r\n          />\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full sm:w-auto\"\r\n            disabled={isPending}\r\n          >\r\n            {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n            {tCommon('calculate')}\r\n          </Button>\r\n        </form>\r\n      </Form>\r\n      {flexuralVerifyExecutionResult && (\r\n        <TBeamFlexuralResultCard results={flexuralVerifyExecutionResult} />\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;AAQO,MAAM,2BAA2B,CAAC,EACvC,OAAO,EACP,MAAM,EACN,SAAS,EACH;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,EAAE,4BAA4B,EAAE,6BAA6B,EAAE,GAAG;IACxE,MAAM,OAAO,IAAA,yKAAO,EAAgC;QAClD,UAAU,IAAA,6KAAW,EAAC,8KAA8B;QACpD,eAAe;YACb,iBAAiB;YACjB,OAAO;gBACL,SAAS;oBACP,IAAI,8BAA8B,QAAQ;oBAC1C,MAAM,8BAA8B,QAAQ;oBAC5C,YACE,8BAA8B,QAAQ,OAAO,WACzC,WACA;oBACN,WAAW,8BAA8B,QAAQ;oBACjD,iBACE,8BAA8B,QAAQ;oBACxC,gBAAgB,8BAA8B,QAAQ;oBACtD,WACE,8BAA8B,QAAQ,aACtC,gJAAgB,CAAC,MAAM;gBAC3B;gBACA,YAAY,8BAA8B,cAAc;gBACxD,cAAc,8BAA8B,gBAAgB;gBAC5D,eAAe,8BAA8B,iBAAiB;YAChE;QACF;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,0LAAoB,EAAC,QAAQ,WAAW,EAAE;QACtE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;QACxB;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,qBAAqB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAClE;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,MAAM,OAAO;YAAE;YAAW,UAAU,OAAO,EAAE;YAAE;QAAK;QAC1D,QAAQ,GAAG,CAAC;IACd;IAEA,MAAM,EACJ,MAAM,QAAQ,EACd,SAAS,oBAAoB,EAC7B,WAAW,iBAAiB,EAC7B,GAAG,IAAA,8LAAqB,EAAC,SAAS,UAAU,GAAG;IAEhD,MAAM,kBAAkB;WAClB,UAAU,QAAQ,IAAI,CAAA,IAAK,CAAC;gBAC9B,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,IAAI,IAAI,EAAE;YACrB,CAAC,MAAM,EAAE;QACT;YAAE,OAAO;YAAU,OAAO,EAAE;QAAkB;KAC/C;IAED,MAAM,CAAC,UAAU,GAAG,KAAK,KAAK,CAAC;QAAC;KAAmB;IACnD,MAAM,kBAAkB,IAAA,gNAAO,EAC7B,IAAM,UAAU,QAAQ,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK,YAC3C;QAAC;QAAW;KAAS;IAGvB,IAAA,kNAAS,EAAC;QACR,IAAI,CAAC,WAAW;YACd;QACF;QACA,IAAI,cAAc,UAAU;YAC1B,KAAK,QAAQ,CAAC,4BAA4B;QAC5C;QAEA,IAAI,iBAAiB;YACnB,KAAK,QAAQ,CAAC,iBAAiB;gBAC7B,GAAG,eAAe;gBAClB,YAAY;YACd;QACF;IACF,GAAG;QAAC;QAAM;QAAW;KAAgB;IAErC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,wIAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,8OAAC;oBACC,WAAU;oBACV,UAAU,KAAK,YAAY,CAAC;;sCAE5B,8OAAC;4BAAG,WAAU;sCAAsB,EAAE;;;;;;sCACtC,8OAAC,wIAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,QAAQ;4BACR,OAAO;4BACP,WAAU;4BACV,QAAQ;;;;;;sCAEV,8OAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS;4BACT,GAAG;4BACH,SAAS;4BACT,cAAc;4BACd,cAAc,EAAE;;;;;;wBAEjB,cAAc,0BAAY,8OAAC,wMAAoB;;;;;wBAC/C,iCAAmB,8OAAC,oMAAkB;4BAAC,SAAS;;;;;;sCACjD,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,8OAAC,4IAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU;;gCAET,2BAAa,8OAAC,oOAAW;oCAAC,WAAU;;;;;;gCACpC,QAAQ;;;;;;;;;;;;;;;;;;YAId,+CACC,8OAAC,+QAAuB;gBAAC,SAAS;;;;;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 1599, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-calculations.tsx"], "sourcesContent": ["import { TBeamFlexuralCalculation } from '@atlas/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-calculation'\r\nimport {\r\n  Ta<PERSON>,\r\n  Ta<PERSON>Content,\r\n  Ta<PERSON>List,\r\n  TabsTrigger,\r\n} from '@atlas/components/ui/tabs'\r\nimport type { ModuleWithParamsTBeam } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  session: Session\r\n  module: ModuleWithParamsTBeam\r\n  projectId: Project['id']\r\n}\r\n\r\nexport const TBeamCalculations = ({ session, module, projectId }: Props) => {\r\n  const t = useTranslations('forms.calculations.t-beam')\r\n\r\n  return (\r\n    <>\r\n      <h3 className=\"text-lg font-medium py-4\">{t('title')}</h3>\r\n      <Tabs defaultValue=\"flexural\">\r\n        <TabsList>\r\n          <TabsTrigger value=\"flexural\">{t('flexural.label')}</TabsTrigger>\r\n        </TabsList>\r\n        <TabsContent value=\"flexural\">\r\n          <TBeamFlexuralCalculation\r\n            session={session}\r\n            module={module}\r\n            projectId={projectId}\r\n          />\r\n        </TabsContent>\r\n      </Tabs>\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AASA;;;;;AAQO,MAAM,oBAAoB,CAAC,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAS;IACrE,MAAM,IAAI,IAAA,yNAAe,EAAC;IAE1B,qBACE;;0BACE,8OAAC;gBAAG,WAAU;0BAA4B,EAAE;;;;;;0BAC5C,8OAAC,wIAAI;gBAAC,cAAa;;kCACjB,8OAAC,4IAAQ;kCACP,cAAA,8OAAC,+IAAW;4BAAC,OAAM;sCAAY,EAAE;;;;;;;;;;;kCAEnC,8OAAC,+IAAW;wBAAC,OAAM;kCACjB,cAAA,8OAAC,6QAAwB;4BACvB,SAAS;4BACT,QAAQ;4BACR,WAAW;;;;;;;;;;;;;;;;;;;AAMvB", "debugId": null}}, {"offset": {"line": 1669, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx"], "sourcesContent": ["import { ModuleReportGenerationSection } from '@atlas/components/common/atlas/module-detail/module-report-generation-section'\r\nimport { TBeamGeneralForm } from '@atlas/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-general-form'\r\nimport { TBeamGeometryForm } from '@atlas/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-geometry-form'\r\nimport { TBeamMaterialForm } from '@atlas/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-material-form'\r\nimport { TBeamReinforcementForm } from '@atlas/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-reinforcement-form'\r\nimport { TBeamCalculations } from '@atlas/components/common/atlas/module-detail/params-forms/t-beam/t-beam-calculations'\r\nimport {\r\n  Accordion as AccordionComponent,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from '@atlas/components/ui/accordion'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport { modulePolarity } from '@atlas/constants/module'\r\nimport type {\r\n  Module,\r\n  ModuleWithParamsTBeam,\r\n} from '@atlas/lib/api/modules/schemas/modules'\r\nimport { tBeamCalculationCheck } from '@atlas/lib/api/modules/schemas/t-beam-params'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { tBeamParamsCheckSchema } from '@atlas/types/schemas/t-beam-form'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useMemo, useState } from 'react'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  module: ModuleWithParamsTBeam\r\n}\r\n\r\nexport const TBeamParamsForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  module,\r\n}: Props) => {\r\n  const { params } = module\r\n  const [openItems, setOpenItems] = useState(['0'])\r\n  const t = useTranslations('forms.project-params.t-beam')\r\n\r\n  const handleItemSaved = useCallback((id: string) => {\r\n    const nextId = String(Number(id) + 1)\r\n    setOpenItems(old => {\r\n      const temp = old.filter(v => v !== id)\r\n      return old.includes(nextId) ? temp : [...temp, nextId]\r\n    })\r\n  }, [])\r\n\r\n  const { success } = useMemo(\r\n    () => tBeamParamsCheckSchema.safeParse(params),\r\n    [params],\r\n  )\r\n\r\n  const enableReport = useMemo(() => {\r\n    const result = tBeamCalculationCheck.safeParse(module)\r\n    return result.success\r\n  }, [module])\r\n\r\n  return (\r\n    <div>\r\n      <AccordionComponent\r\n        type=\"multiple\"\r\n        value={openItems}\r\n        onValueChange={setOpenItems}\r\n      >\r\n        <AccordionItem value=\"0\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">{t('general.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <TBeamGeneralForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={{\r\n                initialDeformation: params?.initialDeformation,\r\n                polarity: params?.polarity,\r\n              }}\r\n              onSave={() => handleItemSaved('0')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"1\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">{t('geometry.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <TBeamGeometryForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={params?.geometry}\r\n              polarity={params.polarity ?? modulePolarity.POSITIVE}\r\n              onSave={() => handleItemSaved('1')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"2\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">{t('tBeamRebar.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <TBeamReinforcementForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={params?.tBeamRebar}\r\n              polarity={params.polarity ?? modulePolarity.POSITIVE}\r\n              onSave={() => handleItemSaved('2')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"3\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('materialProperties.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <TBeamMaterialForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={params?.materialProperties}\r\n              onSave={() => handleItemSaved('3')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n      </AccordionComponent>\r\n\r\n      {success && (\r\n        <>\r\n          <Separator />\r\n          <TBeamCalculations\r\n            module={module}\r\n            session={session}\r\n            projectId={projectId}\r\n          />\r\n        </>\r\n      )}\r\n\r\n      {enableReport && (\r\n        <>\r\n          <Separator />\r\n          <ModuleReportGenerationSection\r\n            session={session}\r\n            projectId={projectId}\r\n            moduleId={moduleId}\r\n            enabled={enableReport}\r\n          />\r\n        </>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AAKA;AAEA;AAEA;AACA;;;;;;;;;;;;;;;AASO,MAAM,kBAAkB,CAAC,EAC9B,OAAO,EACP,SAAS,EACT,QAAQ,EACR,MAAM,EACA;IACN,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;QAAC;KAAI;IAChD,MAAM,IAAI,IAAA,yNAAe,EAAC;IAE1B,MAAM,kBAAkB,IAAA,oNAAW,EAAC,CAAC;QACnC,MAAM,SAAS,OAAO,OAAO,MAAM;QACnC,aAAa,CAAA;YACX,MAAM,OAAO,IAAI,MAAM,CAAC,CAAA,IAAK,MAAM;YACnC,OAAO,IAAI,QAAQ,CAAC,UAAU,OAAO;mBAAI;gBAAM;aAAO;QACxD;IACF,GAAG,EAAE;IAEL,MAAM,EAAE,OAAO,EAAE,GAAG,IAAA,gNAAO,EACzB,IAAM,sKAAsB,CAAC,SAAS,CAAC,SACvC;QAAC;KAAO;IAGV,MAAM,eAAe,IAAA,gNAAO,EAAC;QAC3B,MAAM,SAAS,uLAAqB,CAAC,SAAS,CAAC;QAC/C,OAAO,OAAO,OAAO;IACvB,GAAG;QAAC;KAAO;IAEX,qBACE,8OAAC;;0BACC,8OAAC,kJAAkB;gBACjB,MAAK;gBACL,OAAO;gBACP,eAAe;;kCAEf,8OAAC,sJAAa;wBAAC,OAAM;;0CACnB,8OAAC,yJAAgB;0CACf,cAAA,8OAAC;oCAAG,WAAU;8CAAuB,EAAE;;;;;;;;;;;0CAEzC,8OAAC,yJAAgB;0CACf,cAAA,8OAAC,yPAAgB;oCACf,SAAS;oCACT,WAAW;oCACX,UAAU;oCACV,eAAe;wCACb,oBAAoB,QAAQ;wCAC5B,UAAU,QAAQ;oCACpB;oCACA,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;kCAIpC,8OAAC,sJAAa;wBAAC,OAAM;;0CACnB,8OAAC,yJAAgB;0CACf,cAAA,8OAAC;oCAAG,WAAU;8CAAuB,EAAE;;;;;;;;;;;0CAEzC,8OAAC,yJAAgB;0CACf,cAAA,8OAAC,2PAAiB;oCAChB,SAAS;oCACT,WAAW;oCACX,UAAU;oCACV,eAAe,QAAQ;oCACvB,UAAU,OAAO,QAAQ,IAAI,6IAAc,CAAC,QAAQ;oCACpD,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;kCAIpC,8OAAC,sJAAa;wBAAC,OAAM;;0CACnB,8OAAC,yJAAgB;0CACf,cAAA,8OAAC;oCAAG,WAAU;8CAAuB,EAAE;;;;;;;;;;;0CAEzC,8OAAC,yJAAgB;0CACf,cAAA,8OAAC,qQAAsB;oCACrB,SAAS;oCACT,WAAW;oCACX,UAAU;oCACV,eAAe,QAAQ;oCACvB,UAAU,OAAO,QAAQ,IAAI,6IAAc,CAAC,QAAQ;oCACpD,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;kCAIpC,8OAAC,sJAAa;wBAAC,OAAM;;0CACnB,8OAAC,yJAAgB;0CACf,cAAA,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;;;;;;0CAGP,8OAAC,yJAAgB;0CACf,cAAA,8OAAC,2PAAiB;oCAChB,SAAS;oCACT,WAAW;oCACX,UAAU;oCACV,eAAe,QAAQ;oCACvB,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;YAMrC,yBACC;;kCACE,8OAAC,kJAAS;;;;;kCACV,8OAAC,2OAAiB;wBAChB,QAAQ;wBACR,SAAS;wBACT,WAAW;;;;;;;;YAKhB,8BACC;;kCACE,8OAAC,kJAAS;;;;;kCACV,8OAAC,uOAA6B;wBAC5B,SAAS;wBACT,WAAW;wBACX,UAAU;wBACV,SAAS;;;;;;;;;;;;;;AAMrB", "debugId": null}}]}