import { getProductsByType } from '@atlas/lib/api/products/endpoints/get-products-by-type'
import type { PaginatedProducts } from '@atlas/lib/api/products/schemas/product'
import { fold } from 'fp-ts/Either'
import { pipe } from 'fp-ts/function'
import type { Session } from 'next-auth'

export const getProductsByTypeFunction = async (
  session: Session,
  productType: string,
  page = 0,
  pageSize = 100,
): Promise<PaginatedProducts> => {
  console.log('🚀 Fetching products by type:', { productType, page, pageSize })

  const result = await getProductsByType(
    session.accessToken,
    productType,
    page,
    pageSize,
  )()

  console.log('📦 API result received')

  return pipe(
    result,
    fold(
      err => {
        console.error('❌ Error fetching products:', err)
        throw err
      },
      data => {
        console.log(
          '✅ Products fetched successfully:',
          data.content.length,
          'items',
        )
        return data
      },
    ),
  )
}
