{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-result-form.tsx"], "sourcesContent": ["import {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport {\r\n  type DesignStrengthPostInterventionCalculationResultSchemaInputs,\r\n  designStrengthPostInterventionCalculationResultSchema,\r\n} from '@atlas/types/schemas/masonry/crm-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Separator } from '@radix-ui/react-dropdown-menu'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useForm } from 'react-hook-form'\r\n\r\ntype Props = {\r\n  designStrengthPostInterventionCalculationResult: DesignStrengthPostInterventionCalculationResultSchemaInputs\r\n}\r\n\r\nexport const CrmPostInterventionDesignStrengthCalculationResultForm = ({\r\n  designStrengthPostInterventionCalculationResult,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.calculations.crm.postIntervention.designStrengthPostInterventionCalculationResult',\r\n  )\r\n\r\n  const form =\r\n    useForm<DesignStrengthPostInterventionCalculationResultSchemaInputs>({\r\n      resolver: zodResolver(\r\n        designStrengthPostInterventionCalculationResultSchema,\r\n      ),\r\n      defaultValues: {\r\n        panelThickness:\r\n          designStrengthPostInterventionCalculationResult?.panelThickness ?? 0,\r\n        designCompressiveStrength:\r\n          designStrengthPostInterventionCalculationResult?.designCompressiveStrength ??\r\n          0,\r\n        designShearStrengthRegularMasonry:\r\n          designStrengthPostInterventionCalculationResult?.designShearStrengthRegularMasonry ??\r\n          0,\r\n        designShearStrengthIrregularMasonry:\r\n          designStrengthPostInterventionCalculationResult?.designShearStrengthIrregularMasonry ??\r\n          0,\r\n        designNormalElasticityModulus:\r\n          designStrengthPostInterventionCalculationResult?.designNormalElasticityModulus ??\r\n          0,\r\n        designShearElasticityModulus:\r\n          designStrengthPostInterventionCalculationResult?.designShearElasticityModulus ??\r\n          0,\r\n        ultimateCompressiveStrainLinearBehavior:\r\n          designStrengthPostInterventionCalculationResult?.ultimateCompressiveStrainLinearBehavior ??\r\n          0,\r\n        secondCoefficient:\r\n          designStrengthPostInterventionCalculationResult?.secondCoefficient ??\r\n          0,\r\n        panelSelfWeight:\r\n          designStrengthPostInterventionCalculationResult?.panelSelfWeight ?? 0,\r\n        inPlaneBendingStrengthCalculationResult:\r\n          designStrengthPostInterventionCalculationResult?.inPlaneBendingStrengthCalculationResult ?? {\r\n            compressedFlangeNeutralAxisDistance: 0,\r\n            inOrOutplaneBendingMoment: 0,\r\n          },\r\n        outOfPlaneBendingStrengthCalculationResult:\r\n          designStrengthPostInterventionCalculationResult?.outOfPlaneBendingStrengthCalculationResult ?? {\r\n            compressedFlangeNeutralAxisDistance: 0,\r\n            inOrOutplaneBendingMoment: 0,\r\n          },\r\n        inPlaneShearStrengthCalculationResult:\r\n          designStrengthPostInterventionCalculationResult?.inPlaneShearStrengthCalculationResult ?? {\r\n            shearStrength: 0,\r\n            verticalStress: 0,\r\n            wallSlendernessCorrectionCoefficient: 0,\r\n          },\r\n      },\r\n    })\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <div>\r\n          <span className=\"font-medium\">{t('panelThickness.label')}:</span>{' '}\r\n          <span>{form.getValues('panelThickness')}</span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t('panelThickness.description')}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('designCompressiveStrength.label')}:\r\n          </span>{' '}\r\n          <span>{form.getValues('designCompressiveStrength')}</span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t('designCompressiveStrength.description')}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('designShearStrengthRegularMasonry.label')}:\r\n          </span>{' '}\r\n          <span>{form.getValues('designShearStrengthRegularMasonry')}</span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t('designShearStrengthRegularMasonry.description')}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('designShearStrengthIrregularMasonry.label')}:\r\n          </span>{' '}\r\n          <span>{form.getValues('designShearStrengthIrregularMasonry')}</span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t('designShearStrengthIrregularMasonry.description')}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('designNormalElasticityModulus.label')}:\r\n          </span>{' '}\r\n          <span>{form.getValues('designNormalElasticityModulus')}</span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t('designNormalElasticityModulus.description')}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('designShearElasticityModulus.label')}:\r\n          </span>{' '}\r\n          <span>{form.getValues('designShearElasticityModulus')}</span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t('designShearElasticityModulus.description')}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('ultimateCompressiveStrainLinearBehavior.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {form.getValues('ultimateCompressiveStrainLinearBehavior')}\r\n          </span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t('ultimateCompressiveStrainLinearBehavior.description')}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">{t('secondCoefficient.label')}:</span>{' '}\r\n          <span>{form.getValues('secondCoefficient')}</span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t('secondCoefficient.description')}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">{t('panelSelfWeight.label')}:</span>{' '}\r\n          <span>{form.getValues('panelSelfWeight')}</span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t('panelSelfWeight.description')}\r\n          </p>\r\n        </div>\r\n\r\n        <Separator />\r\n        <h1 className=\"text-xl font-bold\">\r\n          {t('inPlaneBendingStrengthCalculationResult.title')}\r\n        </h1>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'inPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {form.getValues(\r\n              'inPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance',\r\n            )}\r\n          </span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t(\r\n              'inPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance.description',\r\n            )}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'inPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {form.getValues(\r\n              'inPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment',\r\n            )}\r\n          </span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t(\r\n              'inPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment.description',\r\n            )}\r\n          </p>\r\n        </div>\r\n\r\n        <Separator />\r\n        <h1 className=\"text-xl font-bold\">\r\n          {t('outOfPlaneBendingStrengthCalculationResult.title')}\r\n        </h1>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'outOfPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {form.getValues(\r\n              'outOfPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance',\r\n            )}\r\n          </span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t(\r\n              'outOfPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance.description',\r\n            )}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'outOfPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {form.getValues(\r\n              'outOfPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment',\r\n            )}\r\n          </span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t(\r\n              'outOfPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment.description',\r\n            )}\r\n          </p>\r\n        </div>\r\n\r\n        <Separator />\r\n        <h1 className=\"text-xl font-bold\">\r\n          {t('inPlaneShearStrengthCalculationResult.title')}\r\n        </h1>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('inPlaneShearStrengthCalculationResult.verticalStress.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {form.getValues(\r\n              'inPlaneShearStrengthCalculationResult.verticalStress',\r\n            )}\r\n          </span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t(\r\n              'inPlaneShearStrengthCalculationResult.verticalStress.description',\r\n            )}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('inPlaneShearStrengthCalculationResult.shearStrength.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {form.getValues(\r\n              'inPlaneShearStrengthCalculationResult.shearStrength',\r\n            )}\r\n          </span>\r\n          <p className=\"text-muted-foreground text-sm\">\r\n            {t(\r\n              'inPlaneShearStrengthCalculationResult.shearStrength.description',\r\n            )}\r\n          </p>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAMA;AAIA;AACA;AACA;AACA;;;;;;;;;AAMO,MAAM,yDAAyD;QAAC,EACrE,+CAA+C,EACzC;;IACN,MAAM,IAAI,IAAA,4NAAe,EACvB;QAUM,iEAEA,4EAGA,oFAGA,sFAGA,gFAGA,+EAGA,0FAGA,oEAGA,kEAEA,0FAKA,6FAKA;IA1CR,MAAM,OACJ,IAAA,4KAAO,EAA8D;QACnE,UAAU,IAAA,gLAAW,EACnB,6MAAqD;QAEvD,eAAe;YACb,gBACE,CAAA,kEAAA,4DAAA,sEAAA,gDAAiD,cAAc,cAA/D,6EAAA,kEAAmE;YACrE,2BACE,CAAA,6EAAA,4DAAA,sEAAA,gDAAiD,yBAAyB,cAA1E,wFAAA,6EACA;YACF,mCACE,CAAA,qFAAA,4DAAA,sEAAA,gDAAiD,iCAAiC,cAAlF,gGAAA,qFACA;YACF,qCACE,CAAA,uFAAA,4DAAA,sEAAA,gDAAiD,mCAAmC,cAApF,kGAAA,uFACA;YACF,+BACE,CAAA,iFAAA,4DAAA,sEAAA,gDAAiD,6BAA6B,cAA9E,4FAAA,iFACA;YACF,8BACE,CAAA,gFAAA,4DAAA,sEAAA,gDAAiD,4BAA4B,cAA7E,2FAAA,gFACA;YACF,yCACE,CAAA,2FAAA,4DAAA,sEAAA,gDAAiD,uCAAuC,cAAxF,sGAAA,2FACA;YACF,mBACE,CAAA,qEAAA,4DAAA,sEAAA,gDAAiD,iBAAiB,cAAlE,gFAAA,qEACA;YACF,iBACE,CAAA,mEAAA,4DAAA,sEAAA,gDAAiD,eAAe,cAAhE,8EAAA,mEAAoE;YACtE,yCACE,CAAA,2FAAA,4DAAA,sEAAA,gDAAiD,uCAAuC,cAAxF,sGAAA,2FAA4F;gBAC1F,qCAAqC;gBACrC,2BAA2B;YAC7B;YACF,4CACE,CAAA,8FAAA,4DAAA,sEAAA,gDAAiD,0CAA0C,cAA3F,yGAAA,8FAA+F;gBAC7F,qCAAqC;gBACrC,2BAA2B;YAC7B;YACF,uCACE,CAAA,yFAAA,4DAAA,sEAAA,gDAAiD,qCAAqC,cAAtF,oGAAA,yFAA0F;gBACxF,eAAe;gBACf,gBAAgB;gBAChB,sCAAsC;YACxC;QACJ;IACF;IAEF,qBACE,6LAAC,2IAAI;;0BACH,6LAAC,iJAAU;0BACT,cAAA,6LAAC,gJAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,6LAAC,kJAAW;gBAAC,WAAU;;kCACrB,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAAwB;;;;;;;4BAAS;0CAClE,6LAAC;0CAAM,KAAK,SAAS,CAAC;;;;;;0CACtB,6LAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;kCAGP,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAmC;;;;;;;4BAChC;0CACR,6LAAC;0CAAM,KAAK,SAAS,CAAC;;;;;;0CACtB,6LAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;kCAGP,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA2C;;;;;;;4BACxC;0CACR,6LAAC;0CAAM,KAAK,SAAS,CAAC;;;;;;0CACtB,6LAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;kCAGP,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA6C;;;;;;;4BAC1C;0CACR,6LAAC;0CAAM,KAAK,SAAS,CAAC;;;;;;0CACtB,6LAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;kCAGP,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAuC;;;;;;;4BACpC;0CACR,6LAAC;0CAAM,KAAK,SAAS,CAAC;;;;;;0CACtB,6LAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;kCAGP,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAsC;;;;;;;4BACnC;0CACR,6LAAC;0CAAM,KAAK,SAAS,CAAC;;;;;;0CACtB,6LAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;kCAGP,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAAiD;;;;;;;4BAC9C;0CACR,6LAAC;0CACE,KAAK,SAAS,CAAC;;;;;;0CAElB,6LAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;kCAGP,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAA2B;;;;;;;4BAAS;0CACrE,6LAAC;0CAAM,KAAK,SAAS,CAAC;;;;;;0CACtB,6LAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;kCAGP,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCAAe,EAAE;oCAAyB;;;;;;;4BAAS;0CACnE,6LAAC;0CAAM,KAAK,SAAS,CAAC;;;;;;0CACtB,6LAAC;gCAAE,WAAU;0CACV,EAAE;;;;;;;;;;;;kCAIP,6LAAC,4LAAS;;;;;kCACV,6LAAC;wBAAG,WAAU;kCACX,EAAE;;;;;;kCAEL,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EACC;oCACA;;;;;;;4BAEI;0CACR,6LAAC;0CACE,KAAK,SAAS,CACb;;;;;;0CAGJ,6LAAC;gCAAE,WAAU;0CACV,EACC;;;;;;;;;;;;kCAIN,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EACC;oCACA;;;;;;;4BAEI;0CACR,6LAAC;0CACE,KAAK,SAAS,CACb;;;;;;0CAGJ,6LAAC;gCAAE,WAAU;0CACV,EACC;;;;;;;;;;;;kCAKN,6LAAC,4LAAS;;;;;kCACV,6LAAC;wBAAG,WAAU;kCACX,EAAE;;;;;;kCAEL,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EACC;oCACA;;;;;;;4BAEI;0CACR,6LAAC;0CACE,KAAK,SAAS,CACb;;;;;;0CAGJ,6LAAC;gCAAE,WAAU;0CACV,EACC;;;;;;;;;;;;kCAIN,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EACC;oCACA;;;;;;;4BAEI;0CACR,6LAAC;0CACE,KAAK,SAAS,CACb;;;;;;0CAGJ,6LAAC;gCAAE,WAAU;0CACV,EACC;;;;;;;;;;;;kCAKN,6LAAC,4LAAS;;;;;kCACV,6LAAC;wBAAG,WAAU;kCACX,EAAE;;;;;;kCAEL,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA8D;;;;;;;4BAC3D;0CACR,6LAAC;0CACE,KAAK,SAAS,CACb;;;;;;0CAGJ,6LAAC;gCAAE,WAAU;0CACV,EACC;;;;;;;;;;;;kCAIN,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA6D;;;;;;;4BAC1D;0CACR,6LAAC;0CACE,KAAK,SAAS,CACb;;;;;;0CAGJ,6LAAC;gCAAE,WAAU;0CACV,EACC;;;;;;;;;;;;;;;;;;;;;;;;AAOd;GApQa;;QAGD,4NAAe;QAKvB,4KAAO;;;KARE", "debugId": null}}, {"offset": {"line": 666, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-design-strength-calculation-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport type { ModuleWithParamsCrm } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useModuleCalculation } from '@atlas/lib/mutation/modules/use-module-calculation'\r\nimport { useProductsByCategory } from '@atlas/lib/query/products/use-products-by-category'\r\nimport {\r\n  type DesignStrengthPostInterventionInputSchemaInputs,\r\n  designStrengthPostInterventionInputSchema,\r\n} from '@atlas/types/schemas/masonry/crm-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Separator } from '@radix-ui/react-dropdown-menu'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useMemo } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\nimport { CrmPostInterventionDesignStrengthCalculationResultForm } from './crm-post-intervention-design-strength-calculation-result-form'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  module: ModuleWithParamsCrm\r\n}\r\n\r\nconst _MESH_OPTIONS_FILTER = [\r\n  'Kimitech WALLMESH HR (Certificata CVT)',\r\n  'Kimitech WALLMESH HR-HD (Marcata CE)',\r\n  'Kimitech WALLMESH MR (Certificata CVT)',\r\n  'Kimitech WALLMESH MR-HD (Marcata CE)',\r\n]\r\n\r\nconst _CONNECTOR_OPTIONS_FILTER = [\r\n  'Kimisteel INOX X-BAR da 8 mm (Marcata CE)',\r\n  'Kimisteel INOX X-BAR da 12 mm (Marcata CE)',\r\n  'Kimisteel INOX X-BAR da 10 mm (Marcata CE)',\r\n  'Kimisteel PLUG VR (Certificata CVT)',\r\n  'Kimisteel PLUG VR HD (Marcata CE)',\r\n]\r\nconst _MATRIX_OPTIONS_FILTER = [\r\n  'Basic MALTA M15',\r\n  'Basic MALTA R3',\r\n  'Betonfix FB',\r\n  'Tectoria M15',\r\n]\r\nexport const CrmPostInterventionDesignStrengthCalculationForm = ({\r\n  session,\r\n  projectId,\r\n  module,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.calculations.crm.postIntervention.designStrengthPostInterventionCalculationResult',\r\n  )\r\n  const tAction = useTranslations(\r\n    'actions.calculations.anti-overturning.composite-reinforcement-system',\r\n  )\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  const designStrengthPostInterventionCalculationResult =\r\n    module.postIntervention?.designStrengthPostInterventionCalculationResult\r\n\r\n  const meshProduct =\r\n    module?.postIntervention?.designStrengthPostInterventionVerifyInput\r\n      ?.meshInput?.meshProduct\r\n  const connectorProduct =\r\n    module?.postIntervention?.designStrengthPostInterventionVerifyInput\r\n      ?.connectorInput?.connectorProduct\r\n  const matrixProduct =\r\n    module?.postIntervention?.designStrengthPostInterventionVerifyInput\r\n      ?.matrixInput?.matrixProduct\r\n\r\n  const form = useForm<DesignStrengthPostInterventionInputSchemaInputs>({\r\n    resolver: zodResolver(designStrengthPostInterventionInputSchema),\r\n    defaultValues: {\r\n      calculationType: 'DESIGN_STRENGTH_VERIFY',\r\n      input: {\r\n        meshInput: {\r\n          meshProduct: {\r\n            id: meshProduct?.id ?? '',\r\n            name: meshProduct?.name,\r\n            sourceType: 'DATABASE',\r\n          },\r\n        },\r\n        connectorInput: {\r\n          connectorProduct: {\r\n            id: connectorProduct?.id ?? '',\r\n            name: connectorProduct?.name,\r\n            sourceType: 'DATABASE',\r\n          },\r\n        },\r\n        matrixInput: {\r\n          matrixProduct: {\r\n            id: matrixProduct?.id ?? '',\r\n            name: matrixProduct?.name ?? '',\r\n            sourceType: 'DATABASE',\r\n          },\r\n        },\r\n        reinforcementTotalThickness:\r\n          module?.postIntervention?.designStrengthPostInterventionVerifyInput\r\n            ?.reinforcementTotalThickness ?? 0,\r\n      },\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useModuleCalculation(session.accessToken, {\r\n    onSuccess: () => {\r\n      toast.success(tAction('calculate.success'))\r\n    },\r\n    onError: error => {\r\n      toast.error(tAction('calculate.failure', { error: error.message }))\r\n    },\r\n  })\r\n\r\n  const handleFormSubmit = (\r\n    body: DesignStrengthPostInterventionInputSchemaInputs,\r\n  ) => {\r\n    mutate({ projectId, moduleId: module.id, body })\r\n  }\r\n\r\n  // LATER CHANGE TO CRM TYPE\r\n  const {\r\n    data: productsAll,\r\n    isError: errorGettingProducts,\r\n    isLoading: isLoadingProducts,\r\n  } = useProductsByCategory(session, 'CRM', 0, 100)\r\n\r\n  const productsMeshOptions = [\r\n    ...(productsAll?.content ?? [])\r\n      .filter(m => m.productType === 'MESH')\r\n      .map(m => ({\r\n        value: m.id,\r\n        label: m.name ?? t('product.unnamed'),\r\n      })),\r\n    // temporary disable\r\n    // { value: 'custom', label: t('product.custom') },\r\n  ]\r\n\r\n  const productsConnectorOptions = [\r\n    ...(productsAll?.content\r\n      .filter(m => m.productType === 'CONNECTOR')\r\n      .map(m => ({\r\n        value: m.id,\r\n        label: m.name ?? t('product.unnamed'),\r\n      })) ?? []),\r\n    // temporary disable\r\n    // { value: 'custom', label: t('product.custom') },\r\n  ]\r\n\r\n  const productsMatrixOptions = [\r\n    ...(productsAll?.content\r\n      .filter(m => m.productType === 'MATRIX')\r\n      .map(m => ({\r\n        value: m.id,\r\n        label: m.name ?? t('product.unnamed'),\r\n      })) ?? []),\r\n    // temporary disable\r\n    // { value: 'custom', label: t('product.custom') },\r\n  ]\r\n\r\n  const [productMeshId] = form.watch(['input.meshInput.meshProduct.id'])\r\n  const [productConnectorId] = form.watch([\r\n    'input.connectorInput.connectorProduct.id',\r\n  ])\r\n  const [productMatrixId] = form.watch(['input.matrixInput.matrixProduct.id'])\r\n\r\n  const selectedProductMesh = useMemo(\r\n    () =>\r\n      productsAll?.content\r\n        .filter(m => m.productType === 'MESH')\r\n        .find(p => p.id === productMeshId),\r\n    [productMeshId, productsAll],\r\n  )\r\n\r\n  const selectedProductConnector = useMemo(\r\n    () =>\r\n      productsAll?.content\r\n        .filter(m => m.productType === 'CONNECTOR')\r\n        .find(p => p.id === productConnectorId),\r\n    [productConnectorId, productsAll],\r\n  )\r\n\r\n  const selectedProductMatrix = useMemo(\r\n    () =>\r\n      productsAll?.content\r\n        .filter(m => m.productType === 'MATRIX')\r\n        .find(p => p.id === productMatrixId),\r\n    [productMatrixId, productsAll],\r\n  )\r\n\r\n  useEffect(() => {\r\n    if (selectedProductMesh) {\r\n      form.setValue('input.meshInput.meshProduct', {\r\n        id: selectedProductMesh.id,\r\n        name: selectedProductMesh.name,\r\n        sourceType: 'DATABASE',\r\n      })\r\n    }\r\n\r\n    if (selectedProductConnector) {\r\n      form.setValue('input.connectorInput.connectorProduct', {\r\n        id: selectedProductConnector.id,\r\n        name: selectedProductConnector.name,\r\n        sourceType: 'DATABASE',\r\n      })\r\n    }\r\n\r\n    if (selectedProductMatrix) {\r\n      form.setValue('input.matrixInput.matrixProduct', {\r\n        id: selectedProductMatrix.id,\r\n        name: selectedProductMatrix.name,\r\n        sourceType: 'DATABASE',\r\n      })\r\n    }\r\n  }, [\r\n    form,\r\n    selectedProductMesh,\r\n    selectedProductConnector,\r\n    selectedProductMatrix,\r\n  ])\r\n\r\n  return (\r\n    <div className=\"flex flex-col justify-center gap-4\">\r\n      <Form {...form}>\r\n        <form\r\n          className=\"space-y-4 rounded-md border p-4\"\r\n          onSubmit={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          <Image\r\n            src=\"/assets/masonry-anti-overturning/antibaltamento-verify.jpg\"\r\n            alt=\"composite reinforcement system verify\"\r\n            height={250}\r\n            width={500}\r\n            className=\"mx-auto rounded-md object-contain\"\r\n            priority\r\n          />\r\n          {/* Mesh Section */}\r\n          <h1 className=\"text-xl font-bold\">{t('mesh-sub-heading')}</h1>\r\n          <SelectFormInput\r\n            control={form.control}\r\n            name=\"input.meshInput.meshProduct.id\"\r\n            options={productsMeshOptions}\r\n            t={t}\r\n            loading={isLoadingProducts}\r\n            requestError={errorGettingProducts}\r\n            // get the error message from the form state if any\r\n            errorMessage={\r\n              form.formState.errors.input?.meshInput?.meshProduct?.id?.message\r\n            }\r\n          />\r\n          <Separator />\r\n          {/* Connector Section */}\r\n          <h1 className=\"text-xl font-bold\">{t('connector-sub-heading')}</h1>\r\n          <SelectFormInput\r\n            control={form.control}\r\n            name=\"input.connectorInput.connectorProduct.id\"\r\n            options={productsConnectorOptions}\r\n            t={t}\r\n            loading={isLoadingProducts}\r\n            requestError={errorGettingProducts}\r\n            errorMessage={\r\n              form.formState.errors.input?.connectorInput?.connectorProduct?.id\r\n                ?.message\r\n            }\r\n          />\r\n          <Separator />\r\n          {/* Matrix Section */}\r\n          <h1 className=\"text-xl font-bold\">{t('matrix-sub-heading')}</h1>\r\n          <SelectFormInput\r\n            control={form.control}\r\n            name=\"input.matrixInput.matrixProduct.id\"\r\n            options={productsMatrixOptions}\r\n            t={t}\r\n            loading={isLoadingProducts}\r\n            requestError={errorGettingProducts}\r\n            errorMessage={\r\n              form.formState.errors.input?.matrixInput?.matrixProduct?.id\r\n                ?.message\r\n            }\r\n          />\r\n          <Separator />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.reinforcementTotalThickness\"\r\n            t={t}\r\n          />\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full sm:w-auto\"\r\n            disabled={isPending}\r\n          >\r\n            {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n            {tCommon('calculate')}\r\n          </Button>\r\n        </form>\r\n      </Form>\r\n      {designStrengthPostInterventionCalculationResult && (\r\n        <CrmPostInterventionDesignStrengthCalculationResultForm\r\n          designStrengthPostInterventionCalculationResult={\r\n            designStrengthPostInterventionCalculationResult\r\n          }\r\n        />\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAGA;AACA;AACA;AAIA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AAQA,MAAM,uBAAuB;IAC3B;IACA;IACA;IACA;CACD;AAED,MAAM,4BAA4B;IAChC;IACA;IACA;IACA;IACA;CACD;AACD,MAAM,yBAAyB;IAC7B;IACA;IACA;IACA;CACD;AACM,MAAM,mDAAmD;QAAC,EAC/D,OAAO,EACP,SAAS,EACT,MAAM,EACA;QAUJ,0BAGA,8EAAA,oEAAA,2BAGA,mFAAA,qEAAA,2BAGA,gFAAA,qEAAA,2BA8BM,qEAAA,2BAoJI,uDAAA,oDAAA,wCAAA,8BAcA,iEAAA,8DAAA,6CAAA,+BAeA,2DAAA,wDAAA,0CAAA;;IAjOZ,MAAM,IAAI,IAAA,4NAAe,EACvB;IAEF,MAAM,UAAU,IAAA,4NAAe,EAC7B;IAEF,MAAM,UAAU,IAAA,4NAAe,EAAC;IAEhC,MAAM,mDACJ,2BAAA,OAAO,gBAAgB,cAAvB,+CAAA,yBAAyB,+CAA+C;IAE1E,MAAM,cACJ,mBAAA,8BAAA,4BAAA,OAAQ,gBAAgB,cAAxB,iDAAA,qEAAA,0BAA0B,yCAAyC,cAAnE,0FAAA,+EAAA,mEACI,SAAS,cADb,mGAAA,6EACe,WAAW;IAC5B,MAAM,mBACJ,mBAAA,8BAAA,4BAAA,OAAQ,gBAAgB,cAAxB,iDAAA,sEAAA,0BAA0B,yCAAyC,cAAnE,2FAAA,oFAAA,oEACI,cAAc,cADlB,wGAAA,kFACoB,gBAAgB;IACtC,MAAM,gBACJ,mBAAA,8BAAA,4BAAA,OAAQ,gBAAgB,cAAxB,iDAAA,sEAAA,0BAA0B,yCAAyC,cAAnE,2FAAA,iFAAA,oEACI,WAAW,cADf,qGAAA,+EACiB,aAAa;QASlB,iBAOA,sBAOA,mBACE,qBAKR;IA3BR,MAAM,OAAO,IAAA,4KAAO,EAAkD;QACpE,UAAU,IAAA,gLAAW,EAAC,iMAAyC;QAC/D,eAAe;YACb,iBAAiB;YACjB,OAAO;gBACL,WAAW;oBACT,aAAa;wBACX,IAAI,CAAA,kBAAA,wBAAA,kCAAA,YAAa,EAAE,cAAf,6BAAA,kBAAmB;wBACvB,IAAI,EAAE,wBAAA,kCAAA,YAAa,IAAI;wBACvB,YAAY;oBACd;gBACF;gBACA,gBAAgB;oBACd,kBAAkB;wBAChB,IAAI,CAAA,uBAAA,6BAAA,uCAAA,iBAAkB,EAAE,cAApB,kCAAA,uBAAwB;wBAC5B,IAAI,EAAE,6BAAA,uCAAA,iBAAkB,IAAI;wBAC5B,YAAY;oBACd;gBACF;gBACA,aAAa;oBACX,eAAe;wBACb,IAAI,CAAA,oBAAA,0BAAA,oCAAA,cAAe,EAAE,cAAjB,+BAAA,oBAAqB;wBACzB,MAAM,CAAA,sBAAA,0BAAA,oCAAA,cAAe,IAAI,cAAnB,iCAAA,sBAAuB;wBAC7B,YAAY;oBACd;gBACF;gBACA,6BACE,CAAA,iGAAA,mBAAA,8BAAA,4BAAA,OAAQ,gBAAgB,cAAxB,iDAAA,sEAAA,0BAA0B,yCAAyC,cAAnE,0FAAA,oEACI,2BAA2B,cAD/B,4GAAA,iGACmC;YACvC;QACF;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,6LAAoB,EAAC,QAAQ,WAAW,EAAE;QACtE,SAAS;qFAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;YACxB;;QACA,OAAO;qFAAE,CAAA;gBACP,oJAAK,CAAC,KAAK,CAAC,QAAQ,qBAAqB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAClE;;IACF;IAEA,MAAM,mBAAmB,CACvB;QAEA,OAAO;YAAE;YAAW,UAAU,OAAO,EAAE;YAAE;QAAK;IAChD;IAEA,2BAA2B;IAC3B,MAAM,EACJ,MAAM,WAAW,EACjB,SAAS,oBAAoB,EAC7B,WAAW,iBAAiB,EAC7B,GAAG,IAAA,iMAAqB,EAAC,SAAS,OAAO,GAAG;QAGvC;IADN,MAAM,sBAAsB;WACvB,CAAC,CAAA,uBAAA,wBAAA,kCAAA,YAAa,OAAO,cAApB,kCAAA,uBAAwB,EAAE,EAC3B,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK,QAC9B,GAAG,CAAC,CAAA;gBAEI;mBAFE;gBACT,OAAO,EAAE,EAAE;gBACX,OAAO,CAAA,UAAA,EAAE,IAAI,cAAN,qBAAA,UAAU,EAAE;YACrB;;KAGH;QAGK;IADN,MAAM,2BAA2B;WAC3B,CAAA,kCAAA,wBAAA,kCAAA,YAAa,OAAO,CACrB,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK,aAC9B,GAAG,CAAC,CAAA;gBAEI;mBAFE;gBACT,OAAO,EAAE,EAAE;gBACX,OAAO,CAAA,UAAA,EAAE,IAAI,cAAN,qBAAA,UAAU,EAAE;YACrB;wBALE,6CAAA,kCAKK,EAAE;KAGZ;QAGK;IADN,MAAM,wBAAwB;WACxB,CAAA,mCAAA,wBAAA,kCAAA,YAAa,OAAO,CACrB,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK,UAC9B,GAAG,CAAC,CAAA;gBAEI;mBAFE;gBACT,OAAO,EAAE,EAAE;gBACX,OAAO,CAAA,UAAA,EAAE,IAAI,cAAN,qBAAA,UAAU,EAAE;YACrB;wBALE,8CAAA,mCAKK,EAAE;KAGZ;IAED,MAAM,CAAC,cAAc,GAAG,KAAK,KAAK,CAAC;QAAC;KAAiC;IACrE,MAAM,CAAC,mBAAmB,GAAG,KAAK,KAAK,CAAC;QACtC;KACD;IACD,MAAM,CAAC,gBAAgB,GAAG,KAAK,KAAK,CAAC;QAAC;KAAqC;IAE3E,MAAM,sBAAsB,IAAA,wKAAO;yFACjC,IACE,wBAAA,kCAAA,YAAa,OAAO,CACjB,MAAM;iGAAC,CAAA,IAAK,EAAE,WAAW,KAAK;gGAC9B,IAAI;iGAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;wFACxB;QAAC;QAAe;KAAY;IAG9B,MAAM,2BAA2B,IAAA,wKAAO;8FACtC,IACE,wBAAA,kCAAA,YAAa,OAAO,CACjB,MAAM;sGAAC,CAAA,IAAK,EAAE,WAAW,KAAK;qGAC9B,IAAI;sGAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;6FACxB;QAAC;QAAoB;KAAY;IAGnC,MAAM,wBAAwB,IAAA,wKAAO;2FACnC,IACE,wBAAA,kCAAA,YAAa,OAAO,CACjB,MAAM;mGAAC,CAAA,IAAK,EAAE,WAAW,KAAK;kGAC9B,IAAI;mGAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;0FACxB;QAAC;QAAiB;KAAY;IAGhC,IAAA,0KAAS;sEAAC;YACR,IAAI,qBAAqB;gBACvB,KAAK,QAAQ,CAAC,+BAA+B;oBAC3C,IAAI,oBAAoB,EAAE;oBAC1B,MAAM,oBAAoB,IAAI;oBAC9B,YAAY;gBACd;YACF;YAEA,IAAI,0BAA0B;gBAC5B,KAAK,QAAQ,CAAC,yCAAyC;oBACrD,IAAI,yBAAyB,EAAE;oBAC/B,MAAM,yBAAyB,IAAI;oBACnC,YAAY;gBACd;YACF;YAEA,IAAI,uBAAuB;gBACzB,KAAK,QAAQ,CAAC,mCAAmC;oBAC/C,IAAI,sBAAsB,EAAE;oBAC5B,MAAM,sBAAsB,IAAI;oBAChC,YAAY;gBACd;YACF;QACF;qEAAG;QACD;QACA;QACA;QACA;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,2IAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,6LAAC;oBACC,WAAU;oBACV,UAAU,KAAK,YAAY,CAAC;;sCAE5B,6LAAC,2IAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,QAAQ;4BACR,OAAO;4BACP,WAAU;4BACV,QAAQ;;;;;;sCAGV,6LAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS;4BACT,GAAG;4BACH,SAAS;4BACT,cAAc;4BACd,mDAAmD;4BACnD,YAAY,GACV,+BAAA,KAAK,SAAS,CAAC,MAAM,CAAC,KAAK,cAA3B,oDAAA,yCAAA,6BAA6B,SAAS,cAAtC,8DAAA,qDAAA,uCAAwC,WAAW,cAAnD,0EAAA,wDAAA,mDAAqD,EAAE,cAAvD,4EAAA,sDAAyD,OAAO;;;;;;sCAGpE,6LAAC,4LAAS;;;;;sCAEV,6LAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS;4BACT,GAAG;4BACH,SAAS;4BACT,cAAc;4BACd,YAAY,GACV,gCAAA,KAAK,SAAS,CAAC,MAAM,CAAC,KAAK,cAA3B,qDAAA,8CAAA,8BAA6B,cAAc,cAA3C,mEAAA,+DAAA,4CAA6C,gBAAgB,cAA7D,oFAAA,kEAAA,6DAA+D,EAAE,cAAjE,sFAAA,gEACI,OAAO;;;;;;sCAGf,6LAAC,4LAAS;;;;;sCAEV,6LAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS;4BACT,GAAG;4BACH,SAAS;4BACT,cAAc;4BACd,YAAY,GACV,gCAAA,KAAK,SAAS,CAAC,MAAM,CAAC,KAAK,cAA3B,qDAAA,2CAAA,8BAA6B,WAAW,cAAxC,gEAAA,yDAAA,yCAA0C,aAAa,cAAvD,8EAAA,4DAAA,uDAAyD,EAAE,cAA3D,gFAAA,0DACI,OAAO;;;;;;sCAGf,6LAAC,4LAAS;;;;;sCACV,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,6LAAC,+IAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU;;gCAET,2BAAa,6LAAC,uOAAW;oCAAC,WAAU;;;;;;gCACpC,QAAQ;;;;;;;;;;;;;;;;;;YAId,iEACC,6LAAC,qXAAsD;gBACrD,iDACE;;;;;;;;;;;;AAMZ;GAnQa;;QAKD,4NAAe;QAGT,4NAAe;QAGf,4NAAe;QAelB,4KAAO;QAiCU,6LAAoB;QAoB9C,iMAAqB;;;KA/Ed", "debugId": null}}, {"offset": {"line": 1061, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/reinforced-masonry-characteristics-params-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport {\r\n  BINDER_MIXTURE_INJECTIONS,\r\n  bindingCoefficientValues,\r\n  CRM_MASONRY_TYPE,\r\n  ENHANCEMENT_CHARACTERISTICS,\r\n  MODULE_BINDER_MIXTURE_INJECTIONS,\r\n  MODULE_REINFORCEMENT_APPLICATION_TYPE,\r\n  maxAmplficationCoefficientValues,\r\n  postInterventionFirstSideReinforcedPlasterCoefficientValues,\r\n  REINFORCEMENT_APPLICATION_TYPE,\r\n} from '@atlas/constants/module'\r\nimport type { ModuleWithParamsCrm } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type CrmFormSchemaInputs,\r\n  type ReinforcedMasonryCharacteristicsParamsSchemaInputs,\r\n  reinforcedMasonryCharacteristicsParamsSchema,\r\n} from '@atlas/types/schemas/masonry/crm-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  module: ModuleWithParamsCrm\r\n  onSave: () => void\r\n}\r\n\r\nexport const ReinforcedMasonryCharacteristicsParamsForm = ({\r\n  session,\r\n  projectId,\r\n  module,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.project-params.crm.crmPostIntervention.reinforcedMasonryCharacteristics',\r\n  )\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const moduleId = module.id\r\n  const reinforcedMasonryCharacteristicsParams =\r\n    module?.postIntervention?.reinforcedMasonryCharacteristicsParams\r\n  const form = useForm<ReinforcedMasonryCharacteristicsParamsSchemaInputs>({\r\n    resolver: zodResolver(reinforcedMasonryCharacteristicsParamsSchema),\r\n    defaultValues: {\r\n      reinforcementApplicationType:\r\n        reinforcedMasonryCharacteristicsParams?.reinforcementApplicationType ??\r\n        REINFORCEMENT_APPLICATION_TYPE.APPLICAZIONE_SU_ENTRAMBI_I_LATI,\r\n      singleFaceApplicationReductionCoefficient:\r\n        reinforcedMasonryCharacteristicsParams?.singleFaceApplicationReductionCoefficient ??\r\n        30,\r\n      reinforcedPlasterCoefficient:\r\n        reinforcedMasonryCharacteristicsParams?.reinforcedPlasterCoefficient ??\r\n        0,\r\n      binderMixturesInjections:\r\n        reinforcedMasonryCharacteristicsParams?.binderMixturesInjections ??\r\n        BINDER_MIXTURE_INJECTIONS.YES,\r\n      correctionCoefficient:\r\n        reinforcedMasonryCharacteristicsParams?.correctionCoefficient ?? 0,\r\n      overallAmplificationCoefficient:\r\n        reinforcedMasonryCharacteristicsParams?.overallAmplificationCoefficient ??\r\n        0,\r\n      amplifiedAverageCompressiveStrength:\r\n        reinforcedMasonryCharacteristicsParams?.amplifiedAverageCompressiveStrength ??\r\n        0,\r\n      amplifiedAverageShearStrengthRegularMasonry:\r\n        reinforcedMasonryCharacteristicsParams?.amplifiedAverageShearStrengthRegularMasonry ??\r\n        0,\r\n      amplifiedAverageShearStrengthIrregularMasonry:\r\n        reinforcedMasonryCharacteristicsParams?.amplifiedAverageShearStrengthIrregularMasonry ??\r\n        0,\r\n      amplifiedAverageNormalElasticityModulus:\r\n        reinforcedMasonryCharacteristicsParams?.amplifiedAverageNormalElasticityModulus ??\r\n        0,\r\n      amplifiedAverageShearElasticityModulus:\r\n        reinforcedMasonryCharacteristicsParams?.amplifiedAverageShearElasticityModulus ??\r\n        0,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const reinforcementApplicationType = form.watch(\r\n    'reinforcementApplicationType',\r\n  )\r\n\r\n  const binderMixtureInjections = form.watch('binderMixturesInjections')\r\n\r\n  const masonryType =\r\n    module.preIntervention?.existingMasonryCharacteristicsParams?.masonryType ??\r\n    CRM_MASONRY_TYPE.MURATURA_A_BLOCCHI_LAPIDEI_SQUADRATI\r\n\r\n  const preInterventionEnhancementCharacteristics =\r\n    module.preIntervention?.existingMasonryCharacteristicsParams\r\n      ?.enhancementCharacteristics ?? ENHANCEMENT_CHARACTERISTICS.NON_PRESENTI\r\n  const preInterventionCorrectionCoefficient =\r\n    module.preIntervention?.existingMasonryCharacteristicsParams\r\n      ?.correctionCoefficient ?? 1\r\n  const averageCompressiveStrengthPreIntervention =\r\n    module.preIntervention?.existingMasonryCharacteristicsParams\r\n      ?.averageCompressiveStrength ?? 0\r\n\r\n  useEffect(() => {\r\n    const singleFaceApplicationReductionCoefficient =\r\n      reinforcementApplicationType ===\r\n      REINFORCEMENT_APPLICATION_TYPE.APPLICAZIONE_SU_ENTRAMBI_I_LATI\r\n        ? 0\r\n        : 30\r\n    form.setValue(\r\n      'singleFaceApplicationReductionCoefficient',\r\n      singleFaceApplicationReductionCoefficient,\r\n    )\r\n\r\n    // get the coefficient based on the masonry type:\r\n    // ((VLOOKUP('ANTE-INTERVENTO'!D13,Parametri!B38:J45,7,0)-1)/100*(100-D11))+1\r\n    const reinforcedPlasterCoefficient =\r\n      ((postInterventionFirstSideReinforcedPlasterCoefficientValues[\r\n        masonryType\r\n      ] -\r\n        1) /\r\n        100) *\r\n        (100 - singleFaceApplicationReductionCoefficient) +\r\n      1\r\n    form.setValue('reinforcedPlasterCoefficient', reinforcedPlasterCoefficient)\r\n\r\n    // correctionCoefficient =IF(C13=\"SI\",VLOOKUP('ANTE-INTERVENTO'!D13,Parametri!B38:J45,6,0),1)\r\n    const correctionCoefficient =\r\n      binderMixtureInjections === BINDER_MIXTURE_INJECTIONS.YES\r\n        ? bindingCoefficientValues[masonryType]\r\n        : 1\r\n    form.setValue('correctionCoefficient', correctionCoefficient)\r\n\r\n    // =IF('ANTE-INTERVENTO'!C21=\"non presenti\",\r\n    //    MIN(D13*D12,VLOOKUP('ANTE-INTERVENTO'!D13,Parametri!B38:J45,9,0)),\r\n    //    IF('ANTE-INTERVENTO'!C21=\"Malta buona\",\r\n    //      IF(C13=\"NO\",\r\n    //        MIN('ANTE-INTERVENTO'!D21*D12,VLOOKUP('ANTE-INTERVENTO'!D13,Parametri!B38:J45,9,0)),\r\n    //        MIN(MAX('ANTE-INTERVENTO'!D21,D13)*D12,cerva.cert('ANTE-INTERVENTO'!D13,Parametri!B38:J45,9,0)))))\r\n    const overallAmplificationCoefficient =\r\n      preInterventionEnhancementCharacteristics ===\r\n      ENHANCEMENT_CHARACTERISTICS.NON_PRESENTI\r\n        ? Math.min(\r\n            correctionCoefficient * reinforcedPlasterCoefficient,\r\n            maxAmplficationCoefficientValues[masonryType],\r\n          )\r\n        : preInterventionEnhancementCharacteristics ===\r\n            ENHANCEMENT_CHARACTERISTICS.MALTA_BUONA\r\n          ? binderMixtureInjections === BINDER_MIXTURE_INJECTIONS.NO\r\n            ? Math.min(\r\n                preInterventionCorrectionCoefficient *\r\n                  reinforcedPlasterCoefficient,\r\n                maxAmplficationCoefficientValues[masonryType],\r\n              )\r\n            : Math.min(\r\n                Math.max(\r\n                  preInterventionCorrectionCoefficient,\r\n                  correctionCoefficient,\r\n                ) * reinforcedPlasterCoefficient,\r\n                maxAmplficationCoefficientValues[masonryType],\r\n              )\r\n          : // we added this \"1\" for values other then Malta Buona and Non Presenti, which is not our case now\r\n            1\r\n\r\n    form.setValue(\r\n      'overallAmplificationCoefficient',\r\n      overallAmplificationCoefficient,\r\n    )\r\n\r\n    // amplifiedAverageCompressiveStrength = overallAmplificationCoefficient * 'ANTE-INTERVENTO:amplifiedAverageCompressiveStrength\r\n    const amplifiedAverageCompressiveStrength =\r\n      overallAmplificationCoefficient *\r\n      averageCompressiveStrengthPreIntervention\r\n    form.setValue(\r\n      'amplifiedAverageCompressiveStrength',\r\n      amplifiedAverageCompressiveStrength,\r\n    )\r\n\r\n    // amplifiedAverageShearStrengthRegularMasonry = overallAmplificationCoefficient * 'ANTE-INTERVENTO:amplifiedAverageShearStrengthRegularMasonry\r\n    const amplifiedAverageShearStrengthRegularMasonry =\r\n      overallAmplificationCoefficient *\r\n      (module.preIntervention?.existingMasonryCharacteristicsParams\r\n        ?.averageShearStrengthRegularMasonry ?? 0)\r\n    form.setValue(\r\n      'amplifiedAverageShearStrengthRegularMasonry',\r\n      amplifiedAverageShearStrengthRegularMasonry,\r\n    )\r\n\r\n    // amplifiedAverageShearStrengthIrregularMasonry = overallAmplificationCoefficient * 'ANTE-INTERVENTO:amplifiedAverageShearStrengthIrregularMasonry\r\n    const amplifiedAverageShearStrengthIrregularMasonry =\r\n      overallAmplificationCoefficient *\r\n      (module.preIntervention?.existingMasonryCharacteristicsParams\r\n        ?.averageShearStrengthIrregularMasonry ?? 0)\r\n    form.setValue(\r\n      'amplifiedAverageShearStrengthIrregularMasonry',\r\n      amplifiedAverageShearStrengthIrregularMasonry,\r\n    )\r\n\r\n    // amplifiedAverageNormalElasticityModulus = overallAmplificationCoefficient * 'ANTE-INTERVENTO:amplifiedAverageNormalElasticityModulus\r\n    const amplifiedAverageNormalElasticityModulus =\r\n      overallAmplificationCoefficient *\r\n      (module.preIntervention?.existingMasonryCharacteristicsParams\r\n        ?.averageNormalElasticityModulus ?? 0)\r\n    form.setValue(\r\n      'amplifiedAverageNormalElasticityModulus',\r\n      amplifiedAverageNormalElasticityModulus,\r\n    )\r\n\r\n    // amplifiedAverageShearElasticityModulus = overallAmplificationCoefficient * 'ANTE-INTERVENTO:amplifiedAverageShearElasticityModulus\r\n    const amplifiedAverageShearElasticityModulus =\r\n      overallAmplificationCoefficient *\r\n      (module.preIntervention?.existingMasonryCharacteristicsParams\r\n        ?.averageShearElasticityModulus ?? 0)\r\n    form.setValue(\r\n      'amplifiedAverageShearElasticityModulus',\r\n      amplifiedAverageShearElasticityModulus,\r\n    )\r\n  }, [\r\n    form,\r\n    reinforcementApplicationType,\r\n    masonryType,\r\n    binderMixtureInjections,\r\n    preInterventionEnhancementCharacteristics,\r\n    preInterventionCorrectionCoefficient,\r\n    averageCompressiveStrengthPreIntervention,\r\n    module.preIntervention?.existingMasonryCharacteristicsParams\r\n      ?.averageNormalElasticityModulus,\r\n    module.preIntervention?.existingMasonryCharacteristicsParams\r\n      ?.averageShearElasticityModulus,\r\n    module.preIntervention?.existingMasonryCharacteristicsParams\r\n      ?.averageShearStrengthIrregularMasonry,\r\n    module.preIntervention?.existingMasonryCharacteristicsParams\r\n      ?.averageShearStrengthRegularMasonry,\r\n  ])\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: ReinforcedMasonryCharacteristicsParamsSchemaInputs) => {\r\n      const updatedModule: CrmFormSchemaInputs = {\r\n        ...module,\r\n        preIntervention: module.preIntervention ?? undefined,\r\n        postIntervention: {\r\n          reinforcedMasonryCharacteristicsParams: body,\r\n          designStrengthPostInterventionVerifyInput:\r\n            module.postIntervention\r\n              ?.designStrengthPostInterventionVerifyInput ?? undefined,\r\n          designStrengthPostInterventionCalculationResult:\r\n            module.postIntervention\r\n              ?.designStrengthPostInterventionCalculationResult ?? undefined,\r\n        },\r\n      }\r\n\r\n      mutate({ projectId, moduleId, body: updatedModule })\r\n    },\r\n    [mutate, projectId, moduleId, module],\r\n  )\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <Image\r\n          src=\"/assets/masonry-crm/crm_post_intervento.jpg\"\r\n          alt=\"site characteristics\"\r\n          height={250}\r\n          width={500}\r\n          className=\"mx-auto rounded-md object-contain\"\r\n          priority\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"reinforcementApplicationType\"\r\n          options={MODULE_REINFORCEMENT_APPLICATION_TYPE}\r\n          optionLabelFn={p => t(`reinforcementApplicationType.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"singleFaceApplicationReductionCoefficient\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"reinforcedPlasterCoefficient\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"binderMixturesInjections\"\r\n          options={MODULE_BINDER_MIXTURE_INJECTIONS}\r\n          optionLabelFn={p => t(`binderMixturesInjections.${p}`)}\r\n          t={t}\r\n        />\r\n\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"correctionCoefficient\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"overallAmplificationCoefficient\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"amplifiedAverageCompressiveStrength\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"amplifiedAverageShearStrengthRegularMasonry\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"amplifiedAverageShearStrengthIrregularMasonry\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"amplifiedAverageNormalElasticityModulus\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"amplifiedAverageShearElasticityModulus\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAaA;AACA;AAKA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;AASO,MAAM,6CAA6C;QAAC,EACzD,OAAO,EACP,SAAS,EACT,MAAM,EACN,MAAM,EACA;QAUJ,0BA8DA,8DAAA,yBAIA,+DAAA,0BAGA,+DAAA,0BAGA,+DAAA,0BA6HA,+DAAA,0BAEA,+DAAA,0BAEA,+DAAA,0BAEA,+DAAA;;IApNF,MAAM,IAAI,IAAA,4NAAe,EACvB;IAEF,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,cAAc,IAAA,2MAAc;IAElC,MAAM,WAAW,OAAO,EAAE;IAC1B,MAAM,yCACJ,mBAAA,8BAAA,2BAAA,OAAQ,gBAAgB,cAAxB,+CAAA,yBAA0B,sCAAsC;QAK5D,sEAGA,mFAGA,sEAGA,kEAGA,+DAEA,yEAGA,6EAGA,qFAGA,uFAGA,iFAGA;IAjCN,MAAM,OAAO,IAAA,4KAAO,EAAqD;QACvE,UAAU,IAAA,gLAAW,EAAC,oMAA4C;QAClE,eAAe;YACb,8BACE,CAAA,uEAAA,mDAAA,6DAAA,uCAAwC,4BAA4B,cAApE,kFAAA,uEACA,gKAA8B,CAAC,+BAA+B;YAChE,2CACE,CAAA,oFAAA,mDAAA,6DAAA,uCAAwC,yCAAyC,cAAjF,+FAAA,oFACA;YACF,8BACE,CAAA,uEAAA,mDAAA,6DAAA,uCAAwC,4BAA4B,cAApE,kFAAA,uEACA;YACF,0BACE,CAAA,mEAAA,mDAAA,6DAAA,uCAAwC,wBAAwB,cAAhE,8EAAA,mEACA,2JAAyB,CAAC,GAAG;YAC/B,uBACE,CAAA,gEAAA,mDAAA,6DAAA,uCAAwC,qBAAqB,cAA7D,2EAAA,gEAAiE;YACnE,iCACE,CAAA,0EAAA,mDAAA,6DAAA,uCAAwC,+BAA+B,cAAvE,qFAAA,0EACA;YACF,qCACE,CAAA,8EAAA,mDAAA,6DAAA,uCAAwC,mCAAmC,cAA3E,yFAAA,8EACA;YACF,6CACE,CAAA,sFAAA,mDAAA,6DAAA,uCAAwC,2CAA2C,cAAnF,iGAAA,sFACA;YACF,+CACE,CAAA,wFAAA,mDAAA,6DAAA,uCAAwC,6CAA6C,cAArF,mGAAA,wFACA;YACF,yCACE,CAAA,kFAAA,mDAAA,6DAAA,uCAAwC,uCAAuC,cAA/E,6FAAA,kFACA;YACF,wCACE,CAAA,iFAAA,mDAAA,6DAAA,uCAAwC,sCAAsC,cAA9E,4FAAA,iFACA;QACJ;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,mNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,SAAS;sFAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;gBACtB,MAAM,YAAY,iBAAiB,CAAC;oBAClC,UAAU;wBAAC;wBAAW;4BAAE;4BAAW;wBAAS;qBAAE;gBAChD;gBACA;YACF;;QACA,OAAO;sFAAE,CAAA;gBACP,oJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAC7D;;IACF;IAGF,MAAM,+BAA+B,KAAK,KAAK,CAC7C;IAGF,MAAM,0BAA0B,KAAK,KAAK,CAAC;QAGzC;IADF,MAAM,cACJ,CAAA,4EAAA,0BAAA,OAAO,eAAe,cAAtB,+CAAA,+DAAA,wBAAwB,oCAAoC,cAA5D,mFAAA,6DAA8D,WAAW,cAAzE,sFAAA,2EACA,kJAAgB,CAAC,oCAAoC;QAGrD;IADF,MAAM,4CACJ,CAAA,2FAAA,2BAAA,OAAO,eAAe,cAAtB,gDAAA,gEAAA,yBAAwB,oCAAoC,cAA5D,oFAAA,8DACI,0BAA0B,cAD9B,qGAAA,0FACkC,6JAA2B,CAAC,YAAY;QAE1E;IADF,MAAM,uCACJ,CAAA,sFAAA,2BAAA,OAAO,eAAe,cAAtB,gDAAA,gEAAA,yBAAwB,oCAAoC,cAA5D,oFAAA,8DACI,qBAAqB,cADzB,gGAAA,qFAC6B;QAE7B;IADF,MAAM,4CACJ,CAAA,2FAAA,2BAAA,OAAO,eAAe,cAAtB,gDAAA,gEAAA,yBAAwB,oCAAoC,cAA5D,oFAAA,8DACI,0BAA0B,cAD9B,qGAAA,0FACkC;IAEpC,IAAA,0KAAS;gEAAC;gBA8EL,8DAAA,yBAUA,+DAAA,0BAUA,+DAAA,0BAUA,+DAAA;YA3GH,MAAM,4CACJ,iCACA,gKAA8B,CAAC,+BAA+B,GAC1D,IACA;YACN,KAAK,QAAQ,CACX,6CACA;YAGF,iDAAiD;YACjD,6EAA6E;YAC7E,MAAM,+BACJ,AAAC,CAAC,6LAA2D,CAC3D,YACD,GACC,CAAC,IACD,MACA,CAAC,MAAM,yCAAyC,IAClD;YACF,KAAK,QAAQ,CAAC,gCAAgC;YAE9C,6FAA6F;YAC7F,MAAM,wBACJ,4BAA4B,2JAAyB,CAAC,GAAG,GACrD,0JAAwB,CAAC,YAAY,GACrC;YACN,KAAK,QAAQ,CAAC,yBAAyB;YAEvC,4CAA4C;YAC5C,wEAAwE;YACxE,6CAA6C;YAC7C,oBAAoB;YACpB,8FAA8F;YAC9F,4GAA4G;YAC5G,MAAM,kCACJ,8CACA,6JAA2B,CAAC,YAAY,GACpC,KAAK,GAAG,CACN,wBAAwB,8BACxB,kKAAgC,CAAC,YAAY,IAE/C,8CACE,6JAA2B,CAAC,WAAW,GACvC,4BAA4B,2JAAyB,CAAC,EAAE,GACtD,KAAK,GAAG,CACN,uCACE,8BACF,kKAAgC,CAAC,YAAY,IAE/C,KAAK,GAAG,CACN,KAAK,GAAG,CACN,sCACA,yBACE,8BACJ,kKAAgC,CAAC,YAAY,IAGjD;YAER,KAAK,QAAQ,CACX,mCACA;YAGF,+HAA+H;YAC/H,MAAM,sCACJ,kCACA;YACF,KAAK,QAAQ,CACX,uCACA;gBAMC;YAHH,+IAA+I;YAC/I,MAAM,8CACJ,kCACA,CAAC,CAAA,mGAAA,0BAAA,OAAO,eAAe,cAAtB,+CAAA,+DAAA,wBAAwB,oCAAoC,cAA5D,mFAAA,6DACG,kCAAkC,cADrC,6GAAA,kGACyC,CAAC;YAC7C,KAAK,QAAQ,CACX,+CACA;gBAMC;YAHH,mJAAmJ;YACnJ,MAAM,gDACJ,kCACA,CAAC,CAAA,qGAAA,2BAAA,OAAO,eAAe,cAAtB,gDAAA,gEAAA,yBAAwB,oCAAoC,cAA5D,oFAAA,8DACG,oCAAoC,cADvC,+GAAA,oGAC2C,CAAC;YAC/C,KAAK,QAAQ,CACX,iDACA;gBAMC;YAHH,uIAAuI;YACvI,MAAM,0CACJ,kCACA,CAAC,CAAA,+FAAA,2BAAA,OAAO,eAAe,cAAtB,gDAAA,gEAAA,yBAAwB,oCAAoC,cAA5D,oFAAA,8DACG,8BAA8B,cADjC,yGAAA,8FACqC,CAAC;YACzC,KAAK,QAAQ,CACX,2CACA;gBAMC;YAHH,qIAAqI;YACrI,MAAM,yCACJ,kCACA,CAAC,CAAA,8FAAA,2BAAA,OAAO,eAAe,cAAtB,gDAAA,gEAAA,yBAAwB,oCAAoC,cAA5D,oFAAA,8DACG,6BAA6B,cADhC,wGAAA,6FACoC,CAAC;YACxC,KAAK,QAAQ,CACX,0CACA;QAEJ;+DAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;SACA,2BAAA,OAAO,eAAe,cAAtB,gDAAA,gEAAA,yBAAwB,oCAAoC,cAA5D,oFAAA,8DACI,8BAA8B;SAClC,2BAAA,OAAO,eAAe,cAAtB,gDAAA,gEAAA,yBAAwB,oCAAoC,cAA5D,oFAAA,8DACI,6BAA6B;SACjC,2BAAA,OAAO,eAAe,cAAtB,gDAAA,gEAAA,yBAAwB,oCAAoC,cAA5D,oFAAA,8DACI,oCAAoC;SACxC,2BAAA,OAAO,eAAe,cAAtB,gDAAA,gEAAA,yBAAwB,oCAAoC,cAA5D,oFAAA,8DACI,kCAAkC;KACvC;IAED,MAAM,mBAAmB,IAAA,4KAAW;oFAClC,CAAC;gBAOO,0BAGA;gBAPa,yBAIb,oEAGA;YATN,MAAM,gBAAqC;gBACzC,GAAG,MAAM;gBACT,iBAAiB,CAAA,0BAAA,OAAO,eAAe,cAAtB,qCAAA,0BAA0B;gBAC3C,kBAAkB;oBAChB,wCAAwC;oBACxC,2CACE,CAAA,sEAAA,2BAAA,OAAO,gBAAgB,cAAvB,+CAAA,yBACI,yCAAyC,cAD7C,gFAAA,qEACiD;oBACnD,iDACE,CAAA,4EAAA,4BAAA,OAAO,gBAAgB,cAAvB,gDAAA,0BACI,+CAA+C,cADnD,sFAAA,2EACuD;gBAC3D;YACF;YAEA,OAAO;gBAAE;gBAAW;gBAAU,MAAM;YAAc;QACpD;mFACA;QAAC;QAAQ;QAAW;QAAU;KAAO;IAGvC,qBACE,6LAAC,2IAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,6LAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,6LAAC,2IAAK;oBACJ,KAAI;oBACJ,KAAI;oBACJ,QAAQ;oBACR,OAAO;oBACP,WAAU;oBACV,QAAQ;;;;;;8BAEV,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,uKAAqC;oBAC9C,eAAe,CAAA,IAAK,EAAE,AAAC,gCAAiC,OAAF;oBACtD,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,kKAAgC;oBACzC,eAAe,CAAA,IAAK,EAAE,AAAC,4BAA6B,OAAF;oBAClD,GAAG;;;;;;8BAGL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,+IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,6LAAC,uOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB;GAlVa;;QAMD,4NAAe;QAGT,4NAAe;QACf,4NAAe;QACX,2MAAc;QAKrB,4KAAO;QAsCU,mNAA2B;;;KAtD9C", "debugId": null}}, {"offset": {"line": 1419, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/post-intervention/crm-post-intervention-form.tsx"], "sourcesContent": ["import {\r\n  Accordion as AccordionComponent,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from '@atlas/components/ui/accordion'\r\nimport type { ModuleWithParamsCrm } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useState } from 'react'\r\nimport { CrmPostInterventionDesignStrengthCalculationForm } from './crm-post-intervention-design-strength-calculation-form'\r\nimport { ReinforcedMasonryCharacteristicsParamsForm } from './reinforced-masonry-characteristics-params-form'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  module: ModuleWithParamsCrm\r\n}\r\n\r\nexport const CrmPostInterventionForm = ({\r\n  session,\r\n  projectId,\r\n  module,\r\n}: Props) => {\r\n  const [openItems, setOpenItems] = useState(['0'])\r\n  const t = useTranslations('forms.project-params.crm.crmPostIntervention')\r\n\r\n  const reinforcedMasonryCharacteristics =\r\n    module?.postIntervention?.reinforcedMasonryCharacteristicsParams\r\n  const handleItemSaved = useCallback((id: string) => {\r\n    const nextId = String(Number(id) + 1)\r\n    setOpenItems(old => {\r\n      const temp = old.filter(v => v !== id)\r\n      return old.includes(nextId) ? temp : [...temp, nextId]\r\n    })\r\n  }, [])\r\n\r\n  return (\r\n    <div>\r\n      <AccordionComponent\r\n        type=\"multiple\"\r\n        value={openItems}\r\n        onValueChange={setOpenItems}\r\n      >\r\n        <AccordionItem value=\"0\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('reinforcedMasonryCharacteristics.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <ReinforcedMasonryCharacteristicsParamsForm\r\n              session={session}\r\n              projectId={projectId}\r\n              module={module}\r\n              onSave={() => handleItemSaved('0')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"1\" disabled={!reinforcedMasonryCharacteristics}>\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('designStrengthPostInterventionCalculationResult.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <CrmPostInterventionDesignStrengthCalculationForm\r\n              session={session}\r\n              projectId={projectId}\r\n              module={module}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n      </AccordionComponent>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AASA;AACA;AACA;AACA;;;;;;;;AAQO,MAAM,0BAA0B;QAAC,EACtC,OAAO,EACP,SAAS,EACT,MAAM,EACA;QAKJ;;IAJF,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAC;QAAC;KAAI;IAChD,MAAM,IAAI,IAAA,4NAAe,EAAC;IAE1B,MAAM,mCACJ,mBAAA,8BAAA,2BAAA,OAAQ,gBAAgB,cAAxB,+CAAA,yBAA0B,sCAAsC;IAClE,MAAM,kBAAkB,IAAA,4KAAW;gEAAC,CAAC;YACnC,MAAM,SAAS,OAAO,OAAO,MAAM;YACnC;wEAAa,CAAA;oBACX,MAAM,OAAO,IAAI,MAAM;qFAAC,CAAA,IAAK,MAAM;;oBACnC,OAAO,IAAI,QAAQ,CAAC,UAAU,OAAO;2BAAI;wBAAM;qBAAO;gBACxD;;QACF;+DAAG,EAAE;IAEL,qBACE,6LAAC;kBACC,cAAA,6LAAC,qJAAkB;YACjB,MAAK;YACL,OAAO;YACP,eAAe;;8BAEf,6LAAC,yJAAa;oBAAC,OAAM;;sCACnB,6LAAC,4JAAgB;sCACf,cAAA,6LAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;;;;;;sCAGP,6LAAC,4JAAgB;sCACf,cAAA,6LAAC,iVAA0C;gCACzC,SAAS;gCACT,WAAW;gCACX,QAAQ;gCACR,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;8BAIpC,6LAAC,yJAAa;oBAAC,OAAM;oBAAI,UAAU,CAAC;;sCAClC,6LAAC,4JAAgB;sCACf,cAAA,6LAAC;gCAAG,WAAU;0CACX,EAAE;;;;;;;;;;;sCAGP,6LAAC,4JAAgB;sCACf,cAAA,6LAAC,qWAAgD;gCAC/C,SAAS;gCACT,WAAW;gCACX,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOtB;GAzDa;;QAMD,4NAAe;;;KANd", "debugId": null}}, {"offset": {"line": 1572, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-card.tsx"], "sourcesContent": ["import {\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardTitle,\r\n} from '@atlas/components/ui/card'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport type { DesignStrengthPreInterventionCalculationResultSchemaInputs } from '@atlas/types/schemas/masonry/crm-form'\r\nimport { Separator } from '@radix-ui/react-separator'\r\nimport { useLocale, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  designStrengthPreInterventionCalculationResult: DesignStrengthPreInterventionCalculationResultSchemaInputs\r\n}\r\n\r\nexport function DesignStrengthPreInterventionCalculationResultCard({\r\n  designStrengthPreInterventionCalculationResult,\r\n}: Props) {\r\n  const t = useTranslations(\r\n    'forms.project-params.crm.crmPreIntervention.designStrengthPreInterventionCalculationResult',\r\n  )\r\n  const _tAction = useTranslations('actions.save-project-params.messages')\r\n  const _tCommon = useTranslations('actions.common')\r\n\r\n  const _locale = useLocale()\r\n\r\n  const excludeKeys = [\r\n    'executionClass',\r\n    'structuralElementsCategory',\r\n    'inPlaneBendingStrengthCalculationResult',\r\n    'outOfPlaneBendingStrengthCalculationResult',\r\n    'inPlaneShearStrengthCalculationResult',\r\n  ]\r\n  // form is not available in this component, so we cannot use NumberFormInput directly here\r\n  // Instead, render the values as disabled inputs for display only\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        {/* Primitive values */}\r\n        {Object.entries(designStrengthPreInterventionCalculationResult)\r\n          .filter(\r\n            ([key, value]) =>\r\n              !excludeKeys.includes(key) &&\r\n              (typeof value === 'string' ||\r\n                typeof value === 'number' ||\r\n                typeof value === 'boolean'),\r\n          )\r\n          .map(([key, value]) => (\r\n            <div key={key}>\r\n              <span className=\"font-medium\">{t(`${key}.label`)}:</span>{' '}\r\n              <span>{String(value)}</span>\r\n              <p\r\n                data-slot=\"form-description\"\r\n                id={key}\r\n                className={cn('text-muted-foreground text-sm')}\r\n              >\r\n                {t(`${key}.description`)}\r\n              </p>\r\n            </div>\r\n          ))}\r\n\r\n        {/* inPlaneBendingStrengthCalculationResult */}\r\n        <Separator />\r\n        <h1 className=\"text-xl font-bold\">\r\n          {t('inPlaneBendingStrengthCalculationResult.title')}\r\n        </h1>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'inPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {designStrengthPreInterventionCalculationResult\r\n              .inPlaneBendingStrengthCalculationResult\r\n              ?.compressedFlangeNeutralAxisDistance ?? ''}\r\n          </span>\r\n          <p className={cn('text-muted-foreground text-sm')}>\r\n            {t(\r\n              'inPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance.description',\r\n            )}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'inPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {designStrengthPreInterventionCalculationResult\r\n              .inPlaneBendingStrengthCalculationResult\r\n              ?.inOrOutplaneBendingMoment ?? ''}\r\n          </span>\r\n          <p className={cn('text-muted-foreground text-sm')}>\r\n            {t(\r\n              'inPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment.description',\r\n            )}\r\n          </p>\r\n        </div>\r\n\r\n        {/* outOfPlaneBendingStrengthCalculationResult */}\r\n        <Separator />\r\n        <h1 className=\"text-xl font-bold\">\r\n          {t('outOfPlaneBendingStrengthCalculationResult.title')}\r\n        </h1>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'outOfPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {designStrengthPreInterventionCalculationResult\r\n              .outOfPlaneBendingStrengthCalculationResult\r\n              ?.compressedFlangeNeutralAxisDistance ?? ''}\r\n          </span>\r\n          <p className={cn('text-muted-foreground text-sm')}>\r\n            {t(\r\n              'outOfPlaneBendingStrengthCalculationResult.compressedFlangeNeutralAxisDistance.description',\r\n            )}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'outOfPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {designStrengthPreInterventionCalculationResult\r\n              .outOfPlaneBendingStrengthCalculationResult\r\n              ?.inOrOutplaneBendingMoment ?? ''}\r\n          </span>\r\n          <p className={cn('text-muted-foreground text-sm')}>\r\n            {t(\r\n              'outOfPlaneBendingStrengthCalculationResult.inOrOutplaneBendingMoment.description',\r\n            )}\r\n          </p>\r\n        </div>\r\n\r\n        {/* inPlaneShearStrengthCalculationResult */}\r\n        <Separator />\r\n        <h1 className=\"text-xl font-bold\">\r\n          {t('inPlaneShearStrengthCalculationResult.title')}\r\n        </h1>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('inPlaneShearStrengthCalculationResult.verticalStress.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {designStrengthPreInterventionCalculationResult\r\n              .inPlaneShearStrengthCalculationResult?.verticalStress ?? ''}\r\n          </span>\r\n          <p className={cn('text-muted-foreground text-sm')}>\r\n            {t(\r\n              'inPlaneShearStrengthCalculationResult.verticalStress.description',\r\n            )}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t(\r\n              'inPlaneShearStrengthCalculationResult.wallSlendernessCorrectionCoefficient.label',\r\n            )}\r\n            :\r\n          </span>{' '}\r\n          <span>\r\n            {designStrengthPreInterventionCalculationResult\r\n              .inPlaneShearStrengthCalculationResult\r\n              ?.wallSlendernessCorrectionCoefficient ?? ''}\r\n          </span>\r\n          <p className={cn('text-muted-foreground text-sm')}>\r\n            {t(\r\n              'inPlaneShearStrengthCalculationResult.wallSlendernessCorrectionCoefficient.description',\r\n            )}\r\n          </p>\r\n        </div>\r\n        <div>\r\n          <span className=\"font-medium\">\r\n            {t('inPlaneShearStrengthCalculationResult.shearStrength.label')}:\r\n          </span>{' '}\r\n          <span>\r\n            {designStrengthPreInterventionCalculationResult\r\n              .inPlaneShearStrengthCalculationResult?.shearStrength ?? ''}\r\n          </span>\r\n          <p className={cn('text-muted-foreground text-sm')}>\r\n            {t(\r\n              'inPlaneShearStrengthCalculationResult.shearStrength.description',\r\n            )}\r\n          </p>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAMA;AAEA;AACA;AAAA;;;;;;;AAMO,SAAS,mDAAmD,KAE3D;QAF2D,EACjE,8CAA8C,EACxC,GAF2D;QA8DtD,yFAkBA,0FAwBA,4FAkBA,6FAqBA,uFAiBA,wFAeA;;IA5KX,MAAM,IAAI,IAAA,4NAAe,EACvB;IAEF,MAAM,WAAW,IAAA,4NAAe,EAAC;IACjC,MAAM,WAAW,IAAA,4NAAe,EAAC;IAEjC,MAAM,UAAU,IAAA,kLAAS;IAEzB,MAAM,cAAc;QAClB;QACA;QACA;QACA;QACA;KACD;QA6CU,6HAkBA,mHAwBA,gIAkBA,sHAqBA,sGAiBA,4HAeA;IA7JX,0FAA0F;IAC1F,iEAAiE;IACjE,qBACE,6LAAC,2IAAI;;0BACH,6LAAC,iJAAU;0BACT,cAAA,6LAAC,gJAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,6LAAC,kJAAW;gBAAC,WAAU;;oBAEpB,OAAO,OAAO,CAAC,gDACb,MAAM,CACL;4BAAC,CAAC,KAAK,MAAM;+BACX,CAAC,YAAY,QAAQ,CAAC,QACtB,CAAC,OAAO,UAAU,YAChB,OAAO,UAAU,YACjB,OAAO,UAAU,SAAS;uBAE/B,GAAG,CAAC;4BAAC,CAAC,KAAK,MAAM;6CAChB,6LAAC;;8CACC,6LAAC;oCAAK,WAAU;;wCAAe,EAAE,AAAC,GAAM,OAAJ,KAAI;wCAAS;;;;;;;gCAAS;8CAC1D,6LAAC;8CAAM,OAAO;;;;;;8CACd,6LAAC;oCACC,aAAU;oCACV,IAAI;oCACJ,WAAW,IAAA,4HAAE,EAAC;8CAEb,EAAE,AAAC,GAAM,OAAJ,KAAI;;;;;;;2BARJ;;;;;;kCAcd,6LAAC,qLAAS;;;;;kCACV,6LAAC;wBAAG,WAAU;kCACX,EAAE;;;;;;kCAEL,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EACC;oCACA;;;;;;;4BAEI;0CACR,6LAAC;0CACE,CAAA,+HAAA,0FAAA,+CACE,uCAAuC,cADzC,8GAAA,wFAEG,mCAAmC,cAFtC,yIAAA,8HAE0C;;;;;;0CAE7C,6LAAC;gCAAE,WAAW,IAAA,4HAAE,EAAC;0CACd,EACC;;;;;;;;;;;;kCAIN,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EACC;oCACA;;;;;;;4BAEI;0CACR,6LAAC;0CACE,CAAA,qHAAA,2FAAA,+CACE,uCAAuC,cADzC,+GAAA,yFAEG,yBAAyB,cAF5B,+HAAA,oHAEgC;;;;;;0CAEnC,6LAAC;gCAAE,WAAW,IAAA,4HAAE,EAAC;0CACd,EACC;;;;;;;;;;;;kCAMN,6LAAC,qLAAS;;;;;kCACV,6LAAC;wBAAG,WAAU;kCACX,EAAE;;;;;;kCAEL,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EACC;oCACA;;;;;;;4BAEI;0CACR,6LAAC;0CACE,CAAA,kIAAA,6FAAA,+CACE,0CAA0C,cAD5C,iHAAA,2FAEG,mCAAmC,cAFtC,4IAAA,iIAE0C;;;;;;0CAE7C,6LAAC;gCAAE,WAAW,IAAA,4HAAE,EAAC;0CACd,EACC;;;;;;;;;;;;kCAIN,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EACC;oCACA;;;;;;;4BAEI;0CACR,6LAAC;0CACE,CAAA,wHAAA,8FAAA,+CACE,0CAA0C,cAD5C,kHAAA,4FAEG,yBAAyB,cAF5B,kIAAA,uHAEgC;;;;;;0CAEnC,6LAAC;gCAAE,WAAW,IAAA,4HAAE,EAAC;0CACd,EACC;;;;;;;;;;;;kCAMN,6LAAC,qLAAS;;;;;kCACV,6LAAC;wBAAG,WAAU;kCACX,EAAE;;;;;;kCAEL,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA8D;;;;;;;4BAC3D;0CACR,6LAAC;0CACE,CAAA,wGAAA,wFAAA,+CACE,qCAAqC,cADvC,4GAAA,sFACyC,cAAc,cADvD,kHAAA,uGAC2D;;;;;;0CAE9D,6LAAC;gCAAE,WAAW,IAAA,4HAAE,EAAC;0CACd,EACC;;;;;;;;;;;;kCAIN,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EACC;oCACA;;;;;;;4BAEI;0CACR,6LAAC;0CACE,CAAA,8HAAA,yFAAA,+CACE,qCAAqC,cADvC,6GAAA,uFAEG,oCAAoC,cAFvC,wIAAA,6HAE2C;;;;;;0CAE9C,6LAAC;gCAAE,WAAW,IAAA,4HAAE,EAAC;0CACd,EACC;;;;;;;;;;;;kCAIN,6LAAC;;0CACC,6LAAC;gCAAK,WAAU;;oCACb,EAAE;oCAA6D;;;;;;;4BAC1D;0CACR,6LAAC;0CACE,CAAA,uGAAA,yFAAA,+CACE,qCAAqC,cADvC,6GAAA,uFACyC,aAAa,cADtD,iHAAA,sGAC0D;;;;;;0CAE7D,6LAAC;gCAAE,WAAW,IAAA,4HAAE,EAAC;0CACd,EACC;;;;;;;;;;;;;;;;;;;;;;;;AAOd;GA3LgB;;QAGJ,4NAAe;QAGR,4NAAe;QACf,4NAAe;QAEhB,kLAAS;;;KATX", "debugId": null}}, {"offset": {"line": 1982, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/design-strength-pre-intervention-calculation-result-form.tsx"], "sourcesContent": ["import { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport {\r\n  executionClass,\r\n  loadResistingCategory,\r\n  MODULE_EXECUTION_CLASS,\r\n  MODULE_LOAD_RESISTING_CATEGORY,\r\n  masonryStrengthSafetyFactorMapping,\r\n} from '@atlas/constants/module'\r\nimport type { ModuleWithParamsCrm } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type CrmFormSchemaInputs,\r\n  type DesignStrengthPreInterventionCalculationResultSchemaInputs,\r\n  designStrengthPreInterventionCalculationResultSchema,\r\n} from '@atlas/types/schemas/masonry/crm-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect, useState } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\nimport { DesignStrengthPreInterventionCalculationResultCard } from './design-strength-pre-intervention-calculation-result-card'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  module: ModuleWithParamsCrm\r\n  onSave: () => void\r\n}\r\n\r\nexport const DesignStrengthPreInterventionCalculationResultForm = ({\r\n  session,\r\n  projectId,\r\n  module,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.project-params.crm.crmPreIntervention.designStrengthPreInterventionCalculationResult',\r\n  )\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const [showCalculationResult, setShowCalculationResult] = useState(false)\r\n\r\n  const moduleId = module.id\r\n  const designStrengthPreInterventionCalculationResult =\r\n    module?.preIntervention?.designStrengthPreInterventionCalculationResult\r\n  const existingMasonryCharacteristicsParams =\r\n    module.preIntervention?.existingMasonryCharacteristicsParams\r\n\r\n  const form =\r\n    useForm<DesignStrengthPreInterventionCalculationResultSchemaInputs>({\r\n      resolver: zodResolver(\r\n        designStrengthPreInterventionCalculationResultSchema,\r\n      ),\r\n      defaultValues: {\r\n        executionClass:\r\n          designStrengthPreInterventionCalculationResult?.executionClass ??\r\n          executionClass.ONE,\r\n        structuralElementsCategory:\r\n          designStrengthPreInterventionCalculationResult?.structuralElementsCategory ??\r\n          loadResistingCategory.MASONRY_CAT_II_AND_ANY_TYPE_OF_MORTAR,\r\n        masonryStrengthSafetyFactor:\r\n          designStrengthPreInterventionCalculationResult?.masonryStrengthSafetyFactor ??\r\n          0,\r\n        designCompressiveStrength:\r\n          designStrengthPreInterventionCalculationResult?.designCompressiveStrength ??\r\n          0,\r\n        designShearStrengthRegularMasonry:\r\n          designStrengthPreInterventionCalculationResult?.designShearStrengthRegularMasonry ??\r\n          0,\r\n        designShearStrengthIrregularMasonry:\r\n          designStrengthPreInterventionCalculationResult?.designShearStrengthIrregularMasonry ??\r\n          0,\r\n        designNormalElasticityModulus:\r\n          designStrengthPreInterventionCalculationResult?.designNormalElasticityModulus ??\r\n          0,\r\n        designShearElasticityModulus:\r\n          designStrengthPreInterventionCalculationResult?.designShearElasticityModulus ??\r\n          0,\r\n        ultimateCompressiveStrainLinearBehavior:\r\n          designStrengthPreInterventionCalculationResult?.ultimateCompressiveStrainLinearBehavior ??\r\n          0,\r\n        ultimateCompressiveStrain:\r\n          designStrengthPreInterventionCalculationResult?.ultimateCompressiveStrain ??\r\n          0.0035,\r\n        firstCoefficient:\r\n          designStrengthPreInterventionCalculationResult?.firstCoefficient ?? 0,\r\n        panelSelfWeight:\r\n          designStrengthPreInterventionCalculationResult?.panelSelfWeight ?? 0,\r\n        inPlaneBendingStrengthCalculationResult:\r\n          designStrengthPreInterventionCalculationResult?.inPlaneBendingStrengthCalculationResult ?? {\r\n            compressedFlangeNeutralAxisDistance: 0,\r\n            inOrOutplaneBendingMoment: 0,\r\n          },\r\n        outOfPlaneBendingStrengthCalculationResult:\r\n          designStrengthPreInterventionCalculationResult?.outOfPlaneBendingStrengthCalculationResult ?? {\r\n            compressedFlangeNeutralAxisDistance: 0,\r\n            inOrOutplaneBendingMoment: 0,\r\n          },\r\n        inPlaneShearStrengthCalculationResult:\r\n          designStrengthPreInterventionCalculationResult?.inPlaneShearStrengthCalculationResult ?? {\r\n            shearStrength: 0,\r\n            verticalStress: 0,\r\n            wallSlendernessCorrectionCoefficient: 0,\r\n          },\r\n      },\r\n    })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const structuralElementsCategoryFormValue = form.watch(\r\n    'structuralElementsCategory',\r\n  )\r\n  const executionClassFormValue = form.watch('executionClass')\r\n  const ultimateCompressiveStrainFormValue =\r\n    form.watch('ultimateCompressiveStrain') ?? 0.0035\r\n  useEffect(() => {\r\n    const masonryStrengthSafetyFactor =\r\n      masonryStrengthSafetyFactorMapping[structuralElementsCategoryFormValue][\r\n        executionClassFormValue\r\n      ]\r\n    form.setValue('masonryStrengthSafetyFactor', masonryStrengthSafetyFactor)\r\n\r\n    const divider =\r\n      masonryStrengthSafetyFactor *\r\n        (existingMasonryCharacteristicsParams?.confidenceFactor ?? 0) ===\r\n      0\r\n        ? 1\r\n        : masonryStrengthSafetyFactor *\r\n          (existingMasonryCharacteristicsParams?.confidenceFactor ?? 0)\r\n    // designCompressiveStrength: amplifiedAverageCompressiveStrength / confidenceFactor / masonryStrengthSafetyFactor\r\n    const designCompressiveStrength =\r\n      (existingMasonryCharacteristicsParams?.amplifiedAverageCompressiveStrength ??\r\n        0) / divider\r\n    form.setValue('designCompressiveStrength', designCompressiveStrength)\r\n\r\n    const designShearStrengthRegularMasonry =\r\n      (existingMasonryCharacteristicsParams?.amplifiedAverageShearStrengthRegularMasonry ??\r\n        0) / divider\r\n    form.setValue(\r\n      'designShearStrengthRegularMasonry',\r\n      designShearStrengthRegularMasonry,\r\n    )\r\n\r\n    const designShearStrengthIrregularMasonry =\r\n      (existingMasonryCharacteristicsParams?.amplifiedAverageShearStrengthIrregularMasonry ??\r\n        0) / divider\r\n    form.setValue(\r\n      'designShearStrengthIrregularMasonry',\r\n      designShearStrengthIrregularMasonry,\r\n    )\r\n\r\n    const designNormalElasticityModulus =\r\n      (existingMasonryCharacteristicsParams?.amplifiedAverageNormalElasticityModulus ??\r\n        0) / masonryStrengthSafetyFactor\r\n    form.setValue(\r\n      'designNormalElasticityModulus',\r\n      designNormalElasticityModulus,\r\n    )\r\n\r\n    const designShearElasticityModulus =\r\n      (existingMasonryCharacteristicsParams?.amplifiedAverageShearElasticityModulus ??\r\n        0) / masonryStrengthSafetyFactor\r\n    form.setValue('designShearElasticityModulus', designShearElasticityModulus)\r\n\r\n    const ultimateCompressiveStrainLinearBehavior =\r\n      designCompressiveStrength / designNormalElasticityModulus\r\n    form.setValue(\r\n      'ultimateCompressiveStrainLinearBehavior',\r\n      ultimateCompressiveStrainLinearBehavior,\r\n    )\r\n\r\n    const firstCoefficient =\r\n      ultimateCompressiveStrainLinearBehavior /\r\n      ultimateCompressiveStrainFormValue\r\n    form.setValue('firstCoefficient', firstCoefficient)\r\n\r\n    const panelThickness =\r\n      existingMasonryCharacteristicsParams?.panelThickness ?? 0\r\n    const panelWidth = existingMasonryCharacteristicsParams?.panelWidth ?? 0\r\n\r\n    const panelHeight = existingMasonryCharacteristicsParams?.panelHeight ?? 0\r\n    const specificWeight =\r\n      existingMasonryCharacteristicsParams?.specificWeight ?? 0\r\n    const panelSelfWeight =\r\n      panelWidth * panelHeight * panelThickness * specificWeight * 1.3\r\n\r\n    form.setValue('panelSelfWeight', panelSelfWeight)\r\n\r\n    // inPlaneBendingStrengthCalculationResult, outOfPlaneBendingStrengthCalculationResult, inPlaneShearStrengthCalculationResult are calculated in another form\r\n    // inOrOutplanBendingMoment: =2*(D39*1000)/(D11*1000*D31)*D37/(2*D37-D36)\r\n\r\n    const inPlaneCompressedFlangeNeutralAxisDistance =\r\n      (((2 * (panelSelfWeight * 1000)) /\r\n        (panelThickness * 1000 * designCompressiveStrength)) *\r\n        ultimateCompressiveStrainFormValue) /\r\n      (2 * ultimateCompressiveStrainFormValue -\r\n        ultimateCompressiveStrainLinearBehavior)\r\n\r\n    // inPlaneBendingMoment\r\n    // =(D31*(D11*1000)*D41/2*(D10*1000)*(1-D38)-D41*((1-D38)^2+D38*((D10*1000)/2-D41+2/3*D38*D41)))/1000/1000\r\n\r\n    const firstValue =\r\n      ((designCompressiveStrength *\r\n        (panelThickness * 1000) *\r\n        inPlaneCompressedFlangeNeutralAxisDistance) /\r\n        2) *\r\n      (panelWidth * 1000) *\r\n      (1 - firstCoefficient)\r\n\r\n    const secondValue =\r\n      inPlaneCompressedFlangeNeutralAxisDistance *\r\n      ((1 - firstCoefficient) ** 2 +\r\n        firstCoefficient *\r\n          ((panelWidth * 1000) / 2 -\r\n            inPlaneCompressedFlangeNeutralAxisDistance +\r\n            (2 / 3) *\r\n              firstCoefficient *\r\n              inPlaneCompressedFlangeNeutralAxisDistance))\r\n\r\n    const inPlaneBendingMoment = (firstValue - secondValue) / 1000 / 1000\r\n\r\n    const inPlaneBendingStrengthCalculationResult = {\r\n      compressedFlangeNeutralAxisDistance: Number(\r\n        inPlaneCompressedFlangeNeutralAxisDistance.toFixed(2),\r\n      ),\r\n      inOrOutplaneBendingMoment: Number(inPlaneBendingMoment.toFixed(2)),\r\n    }\r\n    form.setValue(\r\n      'inPlaneBendingStrengthCalculationResult',\r\n      inPlaneBendingStrengthCalculationResult,\r\n    )\r\n\r\n    // outOfPlaneCompressedFlangeNeutralAxisDistance\r\n    // =D39/(0.85*D31*0.7)\r\n    const outOfPlaneCompressedFlangeNeutralAxisDistance =\r\n      panelSelfWeight / (0.85 * designCompressiveStrength * 0.7)\r\n\r\n    // outOfPlaneBendingMoment\r\n    // =D39*(D11*1000/1000/2-0.7*D44/1000/2)\r\n    const outOfPlaneBendingMoment =\r\n      panelSelfWeight *\r\n      ((panelThickness * 1000) / 1000 / 2 -\r\n        (0.7 * outOfPlaneCompressedFlangeNeutralAxisDistance) / 1000 / 2)\r\n\r\n    const outOfPlaneBendingStrengthCalculationResult = {\r\n      compressedFlangeNeutralAxisDistance: Number(\r\n        outOfPlaneCompressedFlangeNeutralAxisDistance.toFixed(2),\r\n      ),\r\n      inOrOutplaneBendingMoment: Number(outOfPlaneBendingMoment.toFixed(2)),\r\n    }\r\n    form.setValue(\r\n      'outOfPlaneBendingStrengthCalculationResult',\r\n      outOfPlaneBendingStrengthCalculationResult,\r\n    )\r\n\r\n    // inPlaneShearStrengthCalculationResult\r\n    // verticalStress = =D39*1000/(D11*1000*D10*1000)\r\n    const verticalStress =\r\n      (panelSelfWeight * 1000) / (panelThickness * 1000 * panelWidth * 1000)\r\n\r\n    // wallSlendernessCorrectionCoefficient =IF(D12/D10<=1,1,IF(D12/D10<=1.5,D12/D10,1.5))\r\n    const wallSlendernessRatio = panelHeight / panelWidth\r\n    const wallSlendernessCorrectionCoefficient =\r\n      wallSlendernessRatio <= 1\r\n        ? 1\r\n        : wallSlendernessRatio <= 1.5\r\n          ? wallSlendernessRatio\r\n          : 1.5\r\n\r\n    // shearStrength =(D10*1000*D11*1000*(1.5*D32/D48)*SQRT(1+D47/(1.5*D32)))/1000\r\n    const shearStrength =\r\n      (panelWidth *\r\n        1000 *\r\n        panelThickness *\r\n        1000 *\r\n        ((1.5 * designShearStrengthRegularMasonry) /\r\n          wallSlendernessCorrectionCoefficient) *\r\n        Math.sqrt(\r\n          1 + verticalStress / (1.5 * designShearStrengthRegularMasonry),\r\n        )) /\r\n      1000\r\n\r\n    const inPlaneShearStrengthCalculationResult = {\r\n      verticalStress: Number(verticalStress.toFixed(2)),\r\n      wallSlendernessCorrectionCoefficient: Number(\r\n        wallSlendernessCorrectionCoefficient.toFixed(2),\r\n      ),\r\n      shearStrength: Number(shearStrength.toFixed(2)),\r\n    }\r\n    form.setValue(\r\n      'inPlaneShearStrengthCalculationResult',\r\n      inPlaneShearStrengthCalculationResult,\r\n    )\r\n  }, [\r\n    structuralElementsCategoryFormValue,\r\n    executionClassFormValue,\r\n    existingMasonryCharacteristicsParams,\r\n    ultimateCompressiveStrainFormValue,\r\n    form,\r\n  ])\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: DesignStrengthPreInterventionCalculationResultSchemaInputs) => {\r\n      const updatedModule: CrmFormSchemaInputs = {\r\n        ...module,\r\n        preIntervention: {\r\n          existingMasonryCharacteristicsParams:\r\n            module.preIntervention?.existingMasonryCharacteristicsParams ??\r\n            undefined,\r\n          designStrengthPreInterventionCalculationResult: body ?? undefined,\r\n        },\r\n        postIntervention: module?.postIntervention ?? undefined,\r\n      }\r\n\r\n      mutate({ projectId, moduleId, body: updatedModule })\r\n    },\r\n    [mutate, projectId, moduleId, module],\r\n  )\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"executionClass\"\r\n          options={MODULE_EXECUTION_CLASS}\r\n          optionLabelFn={p => t(`executionClass.${p}`)}\r\n          t={t}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"structuralElementsCategory\"\r\n          options={MODULE_LOAD_RESISTING_CATEGORY}\r\n          optionLabelFn={p => t(`structuralElementsCategory.${p}`)}\r\n          t={t}\r\n        />\r\n        <Button\r\n          type=\"button\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={() => setShowCalculationResult(true)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('calculate')}\r\n        </Button>\r\n        {showCalculationResult && (\r\n          <DesignStrengthPreInterventionCalculationResultCard\r\n            designStrengthPreInterventionCalculationResult={form.getValues()}\r\n          />\r\n        )}\r\n        <Button\r\n          type=\"submit\"\r\n          className={`w-full sm:w-auto ${!showCalculationResult ? 'hidden' : ''}`}\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('next')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AASA;AACA;AAKA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AASO,MAAM,qDAAqD;QAAC,EACjE,OAAO,EACP,SAAS,EACT,MAAM,EACN,MAAM,EACA;QAYJ,yBAEA;;IAbF,MAAM,IAAI,IAAA,4NAAe,EACvB;IAEF,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,cAAc,IAAA,2MAAc;IAElC,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,IAAA,yKAAQ,EAAC;IAEnE,MAAM,WAAW,OAAO,EAAE;IAC1B,MAAM,iDACJ,mBAAA,8BAAA,0BAAA,OAAQ,eAAe,cAAvB,8CAAA,wBAAyB,8CAA8C;IACzE,MAAM,wCACJ,2BAAA,OAAO,eAAe,cAAtB,+CAAA,yBAAwB,oCAAoC;QAStD,gEAGA,4EAGA,6EAGA,2EAGA,mFAGA,qFAGA,+EAGA,8EAGA,yFAGA,2EAGA,kEAEA,iEAEA,yFAKA,4FAKA;IAnDR,MAAM,OACJ,IAAA,4KAAO,EAA6D;QAClE,UAAU,IAAA,gLAAW,EACnB,4MAAoD;QAEtD,eAAe;YACb,gBACE,CAAA,iEAAA,2DAAA,qEAAA,+CAAgD,cAAc,cAA9D,4EAAA,iEACA,gJAAc,CAAC,GAAG;YACpB,4BACE,CAAA,6EAAA,2DAAA,qEAAA,+CAAgD,0BAA0B,cAA1E,wFAAA,6EACA,uJAAqB,CAAC,qCAAqC;YAC7D,6BACE,CAAA,8EAAA,2DAAA,qEAAA,+CAAgD,2BAA2B,cAA3E,yFAAA,8EACA;YACF,2BACE,CAAA,4EAAA,2DAAA,qEAAA,+CAAgD,yBAAyB,cAAzE,uFAAA,4EACA;YACF,mCACE,CAAA,oFAAA,2DAAA,qEAAA,+CAAgD,iCAAiC,cAAjF,+FAAA,oFACA;YACF,qCACE,CAAA,sFAAA,2DAAA,qEAAA,+CAAgD,mCAAmC,cAAnF,iGAAA,sFACA;YACF,+BACE,CAAA,gFAAA,2DAAA,qEAAA,+CAAgD,6BAA6B,cAA7E,2FAAA,gFACA;YACF,8BACE,CAAA,+EAAA,2DAAA,qEAAA,+CAAgD,4BAA4B,cAA5E,0FAAA,+EACA;YACF,yCACE,CAAA,0FAAA,2DAAA,qEAAA,+CAAgD,uCAAuC,cAAvF,qGAAA,0FACA;YACF,2BACE,CAAA,4EAAA,2DAAA,qEAAA,+CAAgD,yBAAyB,cAAzE,uFAAA,4EACA;YACF,kBACE,CAAA,mEAAA,2DAAA,qEAAA,+CAAgD,gBAAgB,cAAhE,8EAAA,mEAAoE;YACtE,iBACE,CAAA,kEAAA,2DAAA,qEAAA,+CAAgD,eAAe,cAA/D,6EAAA,kEAAmE;YACrE,yCACE,CAAA,0FAAA,2DAAA,qEAAA,+CAAgD,uCAAuC,cAAvF,qGAAA,0FAA2F;gBACzF,qCAAqC;gBACrC,2BAA2B;YAC7B;YACF,4CACE,CAAA,6FAAA,2DAAA,qEAAA,+CAAgD,0CAA0C,cAA1F,wGAAA,6FAA8F;gBAC5F,qCAAqC;gBACrC,2BAA2B;YAC7B;YACF,uCACE,CAAA,wFAAA,2DAAA,qEAAA,+CAAgD,qCAAqC,cAArF,mGAAA,wFAAyF;gBACvF,eAAe;gBACf,gBAAgB;gBAChB,sCAAsC;YACxC;QACJ;IACF;IAEF,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,mNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,SAAS;8FAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;gBACtB,MAAM,YAAY,iBAAiB,CAAC;oBAClC,UAAU;wBAAC;wBAAW;4BAAE;4BAAW;wBAAS;qBAAE;gBAChD;gBACA;YACF;;QACA,OAAO;8FAAE,CAAA;gBACP,oJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAC7D;;IACF;IAGF,MAAM,sCAAsC,KAAK,KAAK,CACpD;IAEF,MAAM,0BAA0B,KAAK,KAAK,CAAC;QAEzC;IADF,MAAM,qCACJ,CAAA,cAAA,KAAK,KAAK,CAAC,0CAAX,yBAAA,cAA2C;IAC7C,IAAA,0KAAS;wEAAC;YACR,MAAM,8BACJ,oKAAkC,CAAC,oCAAoC,CACrE,wBACD;YACH,KAAK,QAAQ,CAAC,+BAA+B;gBAIxC,wDAIE;YANP,MAAM,UACJ,8BACE,CAAC,CAAA,yDAAA,iDAAA,2DAAA,qCAAsC,gBAAgB,cAAtD,oEAAA,yDAA0D,CAAC,MAC9D,IACI,IACA,8BACA,CAAC,CAAA,0DAAA,iDAAA,2DAAA,qCAAsC,gBAAgB,cAAtD,qEAAA,0DAA0D,CAAC;gBAG/D;YAFH,kHAAkH;YAClH,MAAM,4BACJ,CAAC,CAAA,4EAAA,iDAAA,2DAAA,qCAAsC,mCAAmC,cAAzE,uFAAA,4EACC,CAAC,IAAI;YACT,KAAK,QAAQ,CAAC,6BAA6B;gBAGxC;YADH,MAAM,oCACJ,CAAC,CAAA,oFAAA,iDAAA,2DAAA,qCAAsC,2CAA2C,cAAjF,+FAAA,oFACC,CAAC,IAAI;YACT,KAAK,QAAQ,CACX,qCACA;gBAIC;YADH,MAAM,sCACJ,CAAC,CAAA,sFAAA,iDAAA,2DAAA,qCAAsC,6CAA6C,cAAnF,iGAAA,sFACC,CAAC,IAAI;YACT,KAAK,QAAQ,CACX,uCACA;gBAIC;YADH,MAAM,gCACJ,CAAC,CAAA,gFAAA,iDAAA,2DAAA,qCAAsC,uCAAuC,cAA7E,2FAAA,gFACC,CAAC,IAAI;YACT,KAAK,QAAQ,CACX,iCACA;gBAIC;YADH,MAAM,+BACJ,CAAC,CAAA,+EAAA,iDAAA,2DAAA,qCAAsC,sCAAsC,cAA5E,0FAAA,+EACC,CAAC,IAAI;YACT,KAAK,QAAQ,CAAC,gCAAgC;YAE9C,MAAM,0CACJ,4BAA4B;YAC9B,KAAK,QAAQ,CACX,2CACA;YAGF,MAAM,mBACJ,0CACA;YACF,KAAK,QAAQ,CAAC,oBAAoB;gBAGhC;YADF,MAAM,iBACJ,CAAA,uDAAA,iDAAA,2DAAA,qCAAsC,cAAc,cAApD,kEAAA,uDAAwD;gBACvC;YAAnB,MAAM,aAAa,CAAA,mDAAA,iDAAA,2DAAA,qCAAsC,UAAU,cAAhD,8DAAA,mDAAoD;gBAEnD;YAApB,MAAM,cAAc,CAAA,oDAAA,iDAAA,2DAAA,qCAAsC,WAAW,cAAjD,+DAAA,oDAAqD;gBAEvE;YADF,MAAM,iBACJ,CAAA,uDAAA,iDAAA,2DAAA,qCAAsC,cAAc,cAApD,kEAAA,uDAAwD;YAC1D,MAAM,kBACJ,aAAa,cAAc,iBAAiB,iBAAiB;YAE/D,KAAK,QAAQ,CAAC,mBAAmB;YAEjC,4JAA4J;YAC5J,yEAAyE;YAEzE,MAAM,6CACJ,AAAG,IAAI,CAAC,kBAAkB,IAAI,IAC5B,CAAC,iBAAiB,OAAO,yBAAyB,IAClD,qCACF,CAAC,IAAI,qCACH,uCAAuC;YAE3C,uBAAuB;YACvB,0GAA0G;YAE1G,MAAM,aACJ,AAAE,4BACA,CAAC,iBAAiB,IAAI,IACtB,6CACA,IACF,CAAC,aAAa,IAAI,IAClB,CAAC,IAAI,gBAAgB;YAEvB,MAAM,cACJ,6CACA,CAAC,CAAC,IAAI,gBAAgB,KAAK,IACzB,mBACE,CAAC,AAAC,aAAa,OAAQ,IACrB,6CACA,AAAC,IAAI,IACH,mBACA,0CAA0C,CAAC;YAErD,MAAM,uBAAuB,CAAC,aAAa,WAAW,IAAI,OAAO;YAEjE,MAAM,0CAA0C;gBAC9C,qCAAqC,OACnC,2CAA2C,OAAO,CAAC;gBAErD,2BAA2B,OAAO,qBAAqB,OAAO,CAAC;YACjE;YACA,KAAK,QAAQ,CACX,2CACA;YAGF,gDAAgD;YAChD,sBAAsB;YACtB,MAAM,gDACJ,kBAAkB,CAAC,OAAO,4BAA4B,GAAG;YAE3D,0BAA0B;YAC1B,wCAAwC;YACxC,MAAM,0BACJ,kBACA,CAAC,AAAC,iBAAiB,OAAQ,OAAO,IAChC,AAAC,MAAM,gDAAiD,OAAO,CAAC;YAEpE,MAAM,6CAA6C;gBACjD,qCAAqC,OACnC,8CAA8C,OAAO,CAAC;gBAExD,2BAA2B,OAAO,wBAAwB,OAAO,CAAC;YACpE;YACA,KAAK,QAAQ,CACX,8CACA;YAGF,wCAAwC;YACxC,iDAAiD;YACjD,MAAM,iBACJ,AAAC,kBAAkB,OAAQ,CAAC,iBAAiB,OAAO,aAAa,IAAI;YAEvE,sFAAsF;YACtF,MAAM,uBAAuB,cAAc;YAC3C,MAAM,uCACJ,wBAAwB,IACpB,IACA,wBAAwB,MACtB,uBACA;YAER,8EAA8E;YAC9E,MAAM,gBACJ,AAAC,aACC,OACA,iBACA,OACA,CAAC,AAAC,MAAM,oCACN,oCAAoC,IACtC,KAAK,IAAI,CACP,IAAI,iBAAiB,CAAC,MAAM,iCAAiC,KAEjE;YAEF,MAAM,wCAAwC;gBAC5C,gBAAgB,OAAO,eAAe,OAAO,CAAC;gBAC9C,sCAAsC,OACpC,qCAAqC,OAAO,CAAC;gBAE/C,eAAe,OAAO,cAAc,OAAO,CAAC;YAC9C;YACA,KAAK,QAAQ,CACX,yCACA;QAEJ;uEAAG;QACD;QACA;QACA;QACA;QACA;KACD;IAED,MAAM,mBAAmB,IAAA,4KAAW;4FAClC,CAAC;gBAKO;gBAAA,8DAIc;YARpB,MAAM,gBAAqC;gBACzC,GAAG,MAAM;gBACT,iBAAiB;oBACf,sCACE,CAAA,gEAAA,0BAAA,OAAO,eAAe,cAAtB,8CAAA,wBAAwB,oCAAoC,cAA5D,0EAAA,+DACA;oBACF,gDAAgD,iBAAA,kBAAA,OAAQ;gBAC1D;gBACA,kBAAkB,CAAA,2BAAA,mBAAA,6BAAA,OAAQ,gBAAgB,cAAxB,sCAAA,2BAA4B;YAChD;YAEA,OAAO;gBAAE;gBAAW;gBAAU,MAAM;YAAc;QACpD;2FACA;QAAC;QAAQ;QAAW;QAAU;KAAO;IAGvC,qBACE,6LAAC,2IAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,6LAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,wJAAsB;oBAC/B,eAAe,CAAA,IAAK,EAAE,AAAC,kBAAmB,OAAF;oBACxC,GAAG;;;;;;8BAEL,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,gKAA8B;oBACvC,eAAe,CAAA,IAAK,EAAE,AAAC,8BAA+B,OAAF;oBACpD,GAAG;;;;;;8BAEL,6LAAC,+IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,IAAM,yBAAyB;;wBAEvC,2BAAa,6LAAC,uOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;gBAEV,uCACC,6LAAC,wWAAkD;oBACjD,gDAAgD,KAAK,SAAS;;;;;;8BAGlE,6LAAC,+IAAM;oBACL,MAAK;oBACL,WAAW,AAAC,oBAA0D,OAAvC,CAAC,wBAAwB,WAAW;oBACnE,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,6LAAC,uOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB;GAhWa;;QAMD,4NAAe;QAGT,4NAAe;QACf,4NAAe;QACX,2MAAc;QAWhC,4KAAO;QA0DqB,mNAA2B;;;KAhF9C", "debugId": null}}, {"offset": {"line": 2301, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/crm/sections/pre-intervention/existing-masonry-characteristics-params-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport {\r\n  averageCompressiveStrengthValues,\r\n  averageNormalElasticityModulusValues,\r\n  averageShearElasticityModulusValues,\r\n  averageShearStrengthIrregularMasonryValues,\r\n  averageShearStrengthRegularMasonryValues,\r\n  CRM_MASONRY_TYPE,\r\n  correctiveCoefficientValues,\r\n  ENHANCEMENT_CHARACTERISTICS,\r\n  MODULE_CRM_MASONRY_TYPE,\r\n  MODULE_ENHANCEMENT_CHARACTERISTICS,\r\n  MODULE_MATERIAL_KNOWLEDGE_LEVEL,\r\n  moduleMaterialKnowledgeLevel,\r\n  moduleMaterialKnowledgeLevelValues,\r\n  specificWeightValues,\r\n} from '@atlas/constants/module'\r\nimport type { ModuleWithParamsCrm } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type CrmFormSchemaInputs,\r\n  type ExistingMasonryCharacteristicsParamsSchemaInputs,\r\n  existingMasonryCharacteristicsParamsSchema,\r\n} from '@atlas/types/schemas/masonry/crm-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  module: ModuleWithParamsCrm\r\n  onSave: () => void\r\n}\r\n\r\nexport const ExistingMasonryCharacteristicsParamsForm = ({\r\n  session,\r\n  projectId,\r\n  module,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.project-params.crm.crmPreIntervention.existingMasonryCharacteristicsParams',\r\n  )\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n\r\n  const moduleId = module.id\r\n  const existingMasonryCharacteristicsParams =\r\n    module?.preIntervention?.existingMasonryCharacteristicsParams\r\n  const form = useForm<ExistingMasonryCharacteristicsParamsSchemaInputs>({\r\n    resolver: zodResolver(existingMasonryCharacteristicsParamsSchema),\r\n    defaultValues: {\r\n      panelWidth: existingMasonryCharacteristicsParams?.panelWidth ?? 0,\r\n      panelThickness: existingMasonryCharacteristicsParams?.panelThickness ?? 0,\r\n      panelHeight: existingMasonryCharacteristicsParams?.panelHeight ?? 0,\r\n      masonryType:\r\n        existingMasonryCharacteristicsParams?.masonryType ??\r\n        CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA,\r\n      knowledgeLevel:\r\n        existingMasonryCharacteristicsParams?.knowledgeLevel ??\r\n        moduleMaterialKnowledgeLevel.LC1,\r\n      confidenceFactor:\r\n        existingMasonryCharacteristicsParams?.confidenceFactor ??\r\n        moduleMaterialKnowledgeLevelValues.LC1,\r\n      averageCompressiveStrength:\r\n        existingMasonryCharacteristicsParams?.averageCompressiveStrength ?? 0,\r\n      averageShearStrengthRegularMasonry:\r\n        existingMasonryCharacteristicsParams?.averageShearStrengthRegularMasonry ??\r\n        0,\r\n      averageShearStrengthIrregularMasonry:\r\n        existingMasonryCharacteristicsParams?.averageShearStrengthIrregularMasonry ??\r\n        0,\r\n      averageNormalElasticityModulus:\r\n        existingMasonryCharacteristicsParams?.averageNormalElasticityModulus ??\r\n        0,\r\n      averageShearElasticityModulus:\r\n        existingMasonryCharacteristicsParams?.averageShearElasticityModulus ??\r\n        0,\r\n      specificWeight: existingMasonryCharacteristicsParams?.specificWeight ?? 0,\r\n      enhancementCharacteristics:\r\n        existingMasonryCharacteristicsParams?.enhancementCharacteristics ??\r\n        ENHANCEMENT_CHARACTERISTICS.NON_PRESENTI,\r\n      correctionCoefficient:\r\n        existingMasonryCharacteristicsParams?.correctionCoefficient ??\r\n        correctiveCoefficientValues[ENHANCEMENT_CHARACTERISTICS.NON_PRESENTI][\r\n          CRM_MASONRY_TYPE.MURATURA_IN_PIETRAME_DISORDINATA\r\n        ],\r\n      amplifiedAverageCompressiveStrength:\r\n        existingMasonryCharacteristicsParams?.amplifiedAverageCompressiveStrength ??\r\n        0,\r\n      amplifiedAverageShearStrengthRegularMasonry:\r\n        existingMasonryCharacteristicsParams?.amplifiedAverageShearStrengthRegularMasonry ??\r\n        0,\r\n      amplifiedAverageShearStrengthIrregularMasonry:\r\n        existingMasonryCharacteristicsParams?.amplifiedAverageShearStrengthIrregularMasonry ??\r\n        0,\r\n      amplifiedAverageNormalElasticityModulus:\r\n        existingMasonryCharacteristicsParams?.amplifiedAverageNormalElasticityModulus ??\r\n        0,\r\n      amplifiedAverageShearElasticityModulus:\r\n        existingMasonryCharacteristicsParams?.amplifiedAverageShearElasticityModulus ??\r\n        0,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const knowledgeMaterialLevel = form.watch('knowledgeLevel')\r\n  const masonryType = form.watch('masonryType')\r\n  const enhancementCharacteristics = form.watch('enhancementCharacteristics')\r\n\r\n  useEffect(() => {\r\n    const confidenceFactor =\r\n      moduleMaterialKnowledgeLevelValues[knowledgeMaterialLevel]\r\n    form.setValue('confidenceFactor', confidenceFactor)\r\n\r\n    const averageCompressiveStrength =\r\n      averageCompressiveStrengthValues[masonryType][knowledgeMaterialLevel]\r\n    form.setValue('averageCompressiveStrength', averageCompressiveStrength)\r\n    const averageShearStrengthRegularMasonry =\r\n      averageShearStrengthRegularMasonryValues[masonryType][\r\n        knowledgeMaterialLevel\r\n      ]\r\n    form.setValue(\r\n      'averageShearStrengthRegularMasonry',\r\n      averageShearStrengthRegularMasonry,\r\n    )\r\n    const averageShearStrengthIrregularMasonry =\r\n      averageShearStrengthIrregularMasonryValues[masonryType][\r\n        knowledgeMaterialLevel\r\n      ]\r\n    form.setValue(\r\n      'averageShearStrengthIrregularMasonry',\r\n      averageShearStrengthIrregularMasonry,\r\n    )\r\n    const averageNormalElasticityModulus =\r\n      averageNormalElasticityModulusValues[masonryType][knowledgeMaterialLevel]\r\n    form.setValue(\r\n      'averageNormalElasticityModulus',\r\n      averageNormalElasticityModulus,\r\n    )\r\n    const averageShearElasticityModulus =\r\n      averageShearElasticityModulusValues[masonryType][knowledgeMaterialLevel]\r\n    form.setValue(\r\n      'averageShearElasticityModulus',\r\n      averageShearElasticityModulus,\r\n    )\r\n    const specificWeight = specificWeightValues[masonryType]\r\n    form.setValue('specificWeight', specificWeight)\r\n\r\n    const correctionCoefficient =\r\n      correctiveCoefficientValues[enhancementCharacteristics][masonryType]\r\n    form.setValue('correctionCoefficient', correctionCoefficient)\r\n\r\n    // amplified values:\r\n    form.setValue(\r\n      'amplifiedAverageCompressiveStrength',\r\n      averageCompressiveStrength * correctionCoefficient,\r\n    )\r\n    form.setValue(\r\n      'amplifiedAverageShearStrengthRegularMasonry',\r\n      averageShearStrengthRegularMasonry * correctionCoefficient,\r\n    )\r\n    form.setValue(\r\n      'amplifiedAverageShearStrengthIrregularMasonry',\r\n      averageShearStrengthIrregularMasonry * correctionCoefficient,\r\n    )\r\n    form.setValue(\r\n      'amplifiedAverageNormalElasticityModulus',\r\n      averageNormalElasticityModulus * correctionCoefficient,\r\n    )\r\n    form.setValue(\r\n      'amplifiedAverageShearElasticityModulus',\r\n      averageShearElasticityModulus * correctionCoefficient,\r\n    )\r\n  }, [knowledgeMaterialLevel, masonryType, enhancementCharacteristics, form])\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: ExistingMasonryCharacteristicsParamsSchemaInputs) => {\r\n      const updatedModule: CrmFormSchemaInputs = {\r\n        ...module,\r\n        preIntervention: {\r\n          existingMasonryCharacteristicsParams: body,\r\n          designStrengthPreInterventionCalculationResult:\r\n            module.preIntervention\r\n              ?.designStrengthPreInterventionCalculationResult ?? undefined,\r\n        },\r\n        postIntervention: module?.postIntervention ?? undefined,\r\n      }\r\n\r\n      mutate({ projectId, moduleId, body: updatedModule })\r\n    },\r\n    [mutate, projectId, moduleId, module],\r\n  )\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <Image\r\n          src=\"/assets/masonry-crm/crm-ante-intervento.jpg\"\r\n          alt=\"site characteristics\"\r\n          height={250}\r\n          width={500}\r\n          className=\"mx-auto rounded-md object-contain\"\r\n          priority\r\n        />\r\n        <NumberFormInput control={form.control} name=\"panelWidth\" t={t} />\r\n        <NumberFormInput control={form.control} name=\"panelThickness\" t={t} />\r\n        <NumberFormInput control={form.control} name=\"panelHeight\" t={t} />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"masonryType\"\r\n          options={MODULE_CRM_MASONRY_TYPE}\r\n          optionLabelFn={p => t(`masonryType.${p}`)}\r\n          t={t}\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"knowledgeLevel\"\r\n          options={MODULE_MATERIAL_KNOWLEDGE_LEVEL}\r\n          optionLabelFn={p => t(`knowledgeLevel.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"confidenceFactor\"\r\n          t={t}\r\n          disabled={true}\r\n          // value is set automatically based on subsoilCategory\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"averageCompressiveStrength\"\r\n          t={t}\r\n          disabled={true}\r\n          // value is set automatically based on subsoilCategory\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"averageShearStrengthRegularMasonry\"\r\n          t={t}\r\n          disabled={true}\r\n          // value is set automatically based on subsoilCategory\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"averageShearStrengthIrregularMasonry\"\r\n          t={t}\r\n          disabled={true}\r\n          // value is set automatically based on subsoilCategory\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"averageNormalElasticityModulus\"\r\n          t={t}\r\n          disabled={true}\r\n          // value is set automatically based on subsoilCategory\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"averageShearElasticityModulus\"\r\n          t={t}\r\n          disabled={true}\r\n          // value is set automatically based on subsoilCategory\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"specificWeight\"\r\n          t={t}\r\n          disabled={true}\r\n          // value is set automatically based on subsoilCategory\r\n        />\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"enhancementCharacteristics\"\r\n          options={MODULE_ENHANCEMENT_CHARACTERISTICS}\r\n          optionLabelFn={p => t(`enhancementCharacteristics.${p}`)}\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"correctionCoefficient\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"amplifiedAverageCompressiveStrength\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"amplifiedAverageShearStrengthRegularMasonry\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"amplifiedAverageShearStrengthIrregularMasonry\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"amplifiedAverageNormalElasticityModulus\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"amplifiedAverageShearElasticityModulus\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAkBA;AACA;AAKA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;AASO,MAAM,2CAA2C;QAAC,EACvD,OAAO,EACP,SAAS,EACT,MAAM,EACN,MAAM,EACA;QAUJ;;IATF,MAAM,IAAI,IAAA,4NAAe,EACvB;IAEF,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,cAAc,IAAA,2MAAc;IAElC,MAAM,WAAW,OAAO,EAAE;IAC1B,MAAM,uCACJ,mBAAA,8BAAA,0BAAA,OAAQ,eAAe,cAAvB,8CAAA,wBAAyB,oCAAoC;QAI/C,kDACI,sDACH,mDAEX,mDAGA,sDAGA,wDAGA,kEAEA,0EAGA,4EAGA,sEAGA,qEAEc,sDAEd,kEAGA,6DAKA,2EAGA,mFAGA,qFAGA,+EAGA;IAnDN,MAAM,OAAO,IAAA,4KAAO,EAAmD;QACrE,UAAU,IAAA,gLAAW,EAAC,kMAA0C;QAChE,eAAe;YACb,YAAY,CAAA,mDAAA,iDAAA,2DAAA,qCAAsC,UAAU,cAAhD,8DAAA,mDAAoD;YAChE,gBAAgB,CAAA,uDAAA,iDAAA,2DAAA,qCAAsC,cAAc,cAApD,kEAAA,uDAAwD;YACxE,aAAa,CAAA,oDAAA,iDAAA,2DAAA,qCAAsC,WAAW,cAAjD,+DAAA,oDAAqD;YAClE,aACE,CAAA,oDAAA,iDAAA,2DAAA,qCAAsC,WAAW,cAAjD,+DAAA,oDACA,kJAAgB,CAAC,gCAAgC;YACnD,gBACE,CAAA,uDAAA,iDAAA,2DAAA,qCAAsC,cAAc,cAApD,kEAAA,uDACA,8JAA4B,CAAC,GAAG;YAClC,kBACE,CAAA,yDAAA,iDAAA,2DAAA,qCAAsC,gBAAgB,cAAtD,oEAAA,yDACA,oKAAkC,CAAC,GAAG;YACxC,4BACE,CAAA,mEAAA,iDAAA,2DAAA,qCAAsC,0BAA0B,cAAhE,8EAAA,mEAAoE;YACtE,oCACE,CAAA,2EAAA,iDAAA,2DAAA,qCAAsC,kCAAkC,cAAxE,sFAAA,2EACA;YACF,sCACE,CAAA,6EAAA,iDAAA,2DAAA,qCAAsC,oCAAoC,cAA1E,wFAAA,6EACA;YACF,gCACE,CAAA,uEAAA,iDAAA,2DAAA,qCAAsC,8BAA8B,cAApE,kFAAA,uEACA;YACF,+BACE,CAAA,sEAAA,iDAAA,2DAAA,qCAAsC,6BAA6B,cAAnE,iFAAA,sEACA;YACF,gBAAgB,CAAA,uDAAA,iDAAA,2DAAA,qCAAsC,cAAc,cAApD,kEAAA,uDAAwD;YACxE,4BACE,CAAA,mEAAA,iDAAA,2DAAA,qCAAsC,0BAA0B,cAAhE,8EAAA,mEACA,6JAA2B,CAAC,YAAY;YAC1C,uBACE,CAAA,8DAAA,iDAAA,2DAAA,qCAAsC,qBAAqB,cAA3D,yEAAA,8DACA,6JAA2B,CAAC,6JAA2B,CAAC,YAAY,CAAC,CACnE,kJAAgB,CAAC,gCAAgC,CAClD;YACH,qCACE,CAAA,4EAAA,iDAAA,2DAAA,qCAAsC,mCAAmC,cAAzE,uFAAA,4EACA;YACF,6CACE,CAAA,oFAAA,iDAAA,2DAAA,qCAAsC,2CAA2C,cAAjF,+FAAA,oFACA;YACF,+CACE,CAAA,sFAAA,iDAAA,2DAAA,qCAAsC,6CAA6C,cAAnF,iGAAA,sFACA;YACF,yCACE,CAAA,gFAAA,iDAAA,2DAAA,qCAAsC,uCAAuC,cAA7E,2FAAA,gFACA;YACF,wCACE,CAAA,+EAAA,iDAAA,2DAAA,qCAAsC,sCAAsC,cAA5E,0FAAA,+EACA;QACJ;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,mNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,SAAS;oFAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;gBACtB,MAAM,YAAY,iBAAiB,CAAC;oBAClC,UAAU;wBAAC;wBAAW;4BAAE;4BAAW;wBAAS;qBAAE;gBAChD;gBACA;YACF;;QACA,OAAO;oFAAE,CAAA;gBACP,oJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAC7D;;IACF;IAGF,MAAM,yBAAyB,KAAK,KAAK,CAAC;IAC1C,MAAM,cAAc,KAAK,KAAK,CAAC;IAC/B,MAAM,6BAA6B,KAAK,KAAK,CAAC;IAE9C,IAAA,0KAAS;8DAAC;YACR,MAAM,mBACJ,oKAAkC,CAAC,uBAAuB;YAC5D,KAAK,QAAQ,CAAC,oBAAoB;YAElC,MAAM,6BACJ,kKAAgC,CAAC,YAAY,CAAC,uBAAuB;YACvE,KAAK,QAAQ,CAAC,8BAA8B;YAC5C,MAAM,qCACJ,0KAAwC,CAAC,YAAY,CACnD,uBACD;YACH,KAAK,QAAQ,CACX,sCACA;YAEF,MAAM,uCACJ,4KAA0C,CAAC,YAAY,CACrD,uBACD;YACH,KAAK,QAAQ,CACX,wCACA;YAEF,MAAM,iCACJ,sKAAoC,CAAC,YAAY,CAAC,uBAAuB;YAC3E,KAAK,QAAQ,CACX,kCACA;YAEF,MAAM,gCACJ,qKAAmC,CAAC,YAAY,CAAC,uBAAuB;YAC1E,KAAK,QAAQ,CACX,iCACA;YAEF,MAAM,iBAAiB,sJAAoB,CAAC,YAAY;YACxD,KAAK,QAAQ,CAAC,kBAAkB;YAEhC,MAAM,wBACJ,6JAA2B,CAAC,2BAA2B,CAAC,YAAY;YACtE,KAAK,QAAQ,CAAC,yBAAyB;YAEvC,oBAAoB;YACpB,KAAK,QAAQ,CACX,uCACA,6BAA6B;YAE/B,KAAK,QAAQ,CACX,+CACA,qCAAqC;YAEvC,KAAK,QAAQ,CACX,iDACA,uCAAuC;YAEzC,KAAK,QAAQ,CACX,2CACA,iCAAiC;YAEnC,KAAK,QAAQ,CACX,0CACA,gCAAgC;QAEpC;6DAAG;QAAC;QAAwB;QAAa;QAA4B;KAAK;IAE1E,MAAM,mBAAmB,IAAA,4KAAW;kFAClC,CAAC;gBAMO;gBAAA,wEAGc;YARpB,MAAM,gBAAqC;gBACzC,GAAG,MAAM;gBACT,iBAAiB;oBACf,sCAAsC;oBACtC,gDACE,CAAA,0EAAA,0BAAA,OAAO,eAAe,cAAtB,8CAAA,wBACI,8CAA8C,cADlD,oFAAA,yEACsD;gBAC1D;gBACA,kBAAkB,CAAA,2BAAA,mBAAA,6BAAA,OAAQ,gBAAgB,cAAxB,sCAAA,2BAA4B;YAChD;YAEA,OAAO;gBAAE;gBAAW;gBAAU,MAAM;YAAc;QACpD;iFACA;QAAC;QAAQ;QAAW;QAAU;KAAO;IAGvC,qBACE,6LAAC,2IAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,6LAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,6LAAC,2IAAK;oBACJ,KAAI;oBACJ,KAAI;oBACJ,QAAQ;oBACR,OAAO;oBACP,WAAU;oBACV,QAAQ;;;;;;8BAEV,6LAAC,qLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAa,GAAG;;;;;;8BAC7D,6LAAC,qLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAiB,GAAG;;;;;;8BACjE,6LAAC,qLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAc,GAAG;;;;;;8BAC9D,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,yJAAuB;oBAChC,eAAe,CAAA,IAAK,EAAE,AAAC,eAAgB,OAAF;oBACrC,GAAG;;;;;;8BAEL,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,iKAA+B;oBACxC,eAAe,CAAA,IAAK,EAAE,AAAC,kBAAmB,OAAF;oBACxC,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAGZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAGZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAGZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAGZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAGZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAGZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAGZ,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,oKAAkC;oBAC3C,eAAe,CAAA,IAAK,EAAE,AAAC,8BAA+B,OAAF;oBACpD,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAGZ,6LAAC,+IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,6LAAC,uOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB;GAxTa;;QAMD,4NAAe;QAGT,4NAAe;QACf,4NAAe;QACX,2MAAc;QAKrB,4KAAO;QAwDU,mNAA2B;;;KAxE9C", "debugId": null}}, {"offset": {"line": 2715, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/masonry/crm/crm-params-form.tsx"], "sourcesContent": ["import {\r\n  Accordion as AccordionComponent,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from '@atlas/components/ui/accordion'\r\nimport {\r\n  <PERSON><PERSON>,\r\n  <PERSON><PERSON><PERSON>ontent,\r\n  <PERSON><PERSON><PERSON>ist,\r\n  TabsTrigger,\r\n} from '@atlas/components/ui/tabs'\r\nimport type { ModuleWithParamsCrm } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useState } from 'react'\r\nimport { CrmPostInterventionForm } from './sections/post-intervention/crm-post-intervention-form'\r\nimport { DesignStrengthPreInterventionCalculationResultForm } from './sections/pre-intervention/design-strength-pre-intervention-calculation-result-form'\r\nimport { ExistingMasonryCharacteristicsParamsForm } from './sections/pre-intervention/existing-masonry-characteristics-params-form'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  module: ModuleWithParamsCrm\r\n}\r\n\r\nexport const CrmParamsForm = ({ session, projectId, module }: Props) => {\r\n  const [openItems, setOpenItems] = useState(['0'])\r\n  const t = useTranslations('forms.project-params.crm')\r\n\r\n  const [activeTab, setActiveTab] = useState('preIntervention')\r\n\r\n  const handleItemSaved = useCallback((id: string) => {\r\n    if (id === '1') {\r\n      setActiveTab('postIntervention')\r\n      return\r\n    }\r\n\r\n    const nextId = String(Number(id) + 1)\r\n    setOpenItems(old => {\r\n      const temp = old.filter(v => v !== id)\r\n      return old.includes(nextId) ? temp : [...temp, nextId]\r\n    })\r\n  }, [])\r\n\r\n  const isPreInterventionValid =\r\n    !!module?.preIntervention?.designStrengthPreInterventionCalculationResult &&\r\n    !!module?.preIntervention?.existingMasonryCharacteristicsParams\r\n\r\n  return (\r\n    <div>\r\n      <Tabs\r\n        defaultValue=\"preIntervention\"\r\n        value={activeTab}\r\n        onValueChange={setActiveTab}\r\n      >\r\n        <TabsList>\r\n          <TabsTrigger value=\"preIntervention\">\r\n            {t('crmPreIntervention.title')}\r\n          </TabsTrigger>\r\n          <TabsTrigger\r\n            value=\"postIntervention\"\r\n            disabled={!isPreInterventionValid}\r\n          >\r\n            {t('crmPostIntervention.title')}\r\n          </TabsTrigger>\r\n        </TabsList>\r\n        <TabsContent value=\"preIntervention\">\r\n          <AccordionComponent\r\n            type=\"multiple\"\r\n            value={openItems}\r\n            onValueChange={setOpenItems}\r\n          >\r\n            <AccordionItem value=\"0\">\r\n              <AccordionTrigger>\r\n                <h3 className=\"text-lg font-medium\">\r\n                  {t(\r\n                    'crmPreIntervention.existingMasonryCharacteristicsParams.title',\r\n                  )}\r\n                </h3>\r\n              </AccordionTrigger>\r\n              <AccordionContent>\r\n                <ExistingMasonryCharacteristicsParamsForm\r\n                  session={session}\r\n                  projectId={projectId}\r\n                  module={module}\r\n                  onSave={() => handleItemSaved('0')}\r\n                />\r\n              </AccordionContent>\r\n            </AccordionItem>\r\n            <AccordionItem value=\"1\">\r\n              <AccordionTrigger>\r\n                <h3 className=\"text-lg font-medium\">\r\n                  {t(\r\n                    'crmPreIntervention.designStrengthPreInterventionCalculationResult.title',\r\n                  )}\r\n                </h3>\r\n              </AccordionTrigger>\r\n              <AccordionContent>\r\n                <DesignStrengthPreInterventionCalculationResultForm\r\n                  session={session}\r\n                  projectId={projectId}\r\n                  module={module}\r\n                  onSave={() => handleItemSaved('1')}\r\n                />\r\n              </AccordionContent>\r\n            </AccordionItem>\r\n          </AccordionComponent>\r\n        </TabsContent>\r\n        <TabsContent value=\"postIntervention\">\r\n          <CrmPostInterventionForm\r\n            session={session}\r\n            projectId={projectId}\r\n            module={module}\r\n          />\r\n        </TabsContent>\r\n      </Tabs>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAMA;AASA;AACA;AACA;AACA;AACA;;;;;;;;;;AAQO,MAAM,gBAAgB;QAAC,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAS;QAoB7D,yBACA;;IApBJ,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAC;QAAC;KAAI;IAChD,MAAM,IAAI,IAAA,4NAAe,EAAC;IAE1B,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAC;IAE3C,MAAM,kBAAkB,IAAA,4KAAW;sDAAC,CAAC;YACnC,IAAI,OAAO,KAAK;gBACd,aAAa;gBACb;YACF;YAEA,MAAM,SAAS,OAAO,OAAO,MAAM;YACnC;8DAAa,CAAA;oBACX,MAAM,OAAO,IAAI,MAAM;2EAAC,CAAA,IAAK,MAAM;;oBACnC,OAAO,IAAI,QAAQ,CAAC,UAAU,OAAO;2BAAI;wBAAM;qBAAO;gBACxD;;QACF;qDAAG,EAAE;IAEL,MAAM,yBACJ,CAAC,EAAC,mBAAA,8BAAA,0BAAA,OAAQ,eAAe,cAAvB,8CAAA,wBAAyB,8CAA8C,KACzE,CAAC,EAAC,mBAAA,8BAAA,2BAAA,OAAQ,eAAe,cAAvB,+CAAA,yBAAyB,oCAAoC;IAEjE,qBACE,6LAAC;kBACC,cAAA,6LAAC,2IAAI;YACH,cAAa;YACb,OAAO;YACP,eAAe;;8BAEf,6LAAC,+IAAQ;;sCACP,6LAAC,kJAAW;4BAAC,OAAM;sCAChB,EAAE;;;;;;sCAEL,6LAAC,kJAAW;4BACV,OAAM;4BACN,UAAU,CAAC;sCAEV,EAAE;;;;;;;;;;;;8BAGP,6LAAC,kJAAW;oBAAC,OAAM;8BACjB,cAAA,6LAAC,qJAAkB;wBACjB,MAAK;wBACL,OAAO;wBACP,eAAe;;0CAEf,6LAAC,yJAAa;gCAAC,OAAM;;kDACnB,6LAAC,4JAAgB;kDACf,cAAA,6LAAC;4CAAG,WAAU;sDACX,EACC;;;;;;;;;;;kDAIN,6LAAC,4JAAgB;kDACf,cAAA,6LAAC,4UAAwC;4CACvC,SAAS;4CACT,WAAW;4CACX,QAAQ;4CACR,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;0CAIpC,6LAAC,yJAAa;gCAAC,OAAM;;kDACnB,6LAAC,4JAAgB;kDACf,cAAA,6LAAC;4CAAG,WAAU;sDACX,EACC;;;;;;;;;;;kDAIN,6LAAC,4JAAgB;kDACf,cAAA,6LAAC,wWAAkD;4CACjD,SAAS;4CACT,WAAW;4CACX,QAAQ;4CACR,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMxC,6LAAC,kJAAW;oBAAC,OAAM;8BACjB,cAAA,6LAAC,uSAAuB;wBACtB,SAAS;wBACT,WAAW;wBACX,QAAQ;;;;;;;;;;;;;;;;;;;;;;AAMpB;GA7Fa;;QAED,4NAAe;;;KAFd", "debugId": null}}]}