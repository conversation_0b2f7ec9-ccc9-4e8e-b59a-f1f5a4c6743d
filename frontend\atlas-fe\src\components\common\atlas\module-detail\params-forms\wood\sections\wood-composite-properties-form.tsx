import { NumberFormInput } from '@atlas/components/common/form/number-form-input'
import { Button } from '@atlas/components/ui/button'
import { Form } from '@atlas/components/ui/form'
import type { Module } from '@atlas/lib/api/modules/schemas/modules'
import { useProductsQuery } from '@atlas/lib/api/products/use-products-query'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'
import { zodResolver } from '@hookform/resolvers/zod'
import { Loader2Icon } from 'lucide-react'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useCallback, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

const formSchema = z.object({
  frpElasticityModulus: z
    .number()
    .min(0, 'FRP elasticity modulus must be positive'),
  frpDesignMaximumStrain: z
    .number()
    .min(0, 'FRP design maximum strain must be positive'),
  frpCharacteristicStrain: z
    .number()
    .min(0, 'FRP characteristic strain must be positive'),
  frpPartialFactorInUls: z
    .number()
    .min(0, 'FRP partial factor in ULS must be positive'),
  frpMaximumStrainForDebonding: z
    .number()
    .min(0, 'FRP maximum strain for debonding must be positive'),
  loadConditionFactor: z
    .number()
    .min(0, 'Load condition factor must be positive'),
  frpPartialFactorInUlsForDebonding: z
    .number()
    .min(0, 'FRP partial factor in ULS for debonding must be positive'),
  reinforcementToSectionWidthRatio: z
    .number()
    .min(0, 'Reinforcement to section width ratio must be positive'),
  reinforcementToSectionWidthRatioUsefull: z
    .number()
    .min(0, 'Reinforcement to section width ratio useful must be positive'),
  geometricCorrectionFactor: z
    .number()
    .min(0, 'Geometric correction factor must be positive'),
  geometricCorrectionFactorUsefull: z
    .number()
    .min(0, 'Geometric correction factor useful must be positive'),
  experimentalCorrectionFactor: z
    .number()
    .min(0, 'Experimental correction factor must be positive'),
  confidenceFactor: z.number().min(0, 'Confidence factor must be positive'),
  sectionModulus: z.number().min(0, 'Section modulus must be positive'),
  momentOfInertiaAboutY: z
    .number()
    .min(0, 'Moment of inertia about Y must be positive'),
})

type FormSchema = z.infer<typeof formSchema>

type Props = {
  session: Session
  projectId: Project['id']
  moduleId: Module['id']
  defaultValues?: Partial<FormSchema>
  compositeGeometry?: any
  preInterventionData?: any
  materialProperties?: any
  geometryProperties?: any
  onSave?: () => void
  onPropertiesChange?: (properties: any) => void
  initialDeformation?: any
}

export const WoodCompositePropertiesForm = ({
  session,
  projectId,
  moduleId,
  defaultValues,
  compositeGeometry,
  preInterventionData: _preInterventionData,
  materialProperties,
  geometryProperties,
  onSave,
  onPropertiesChange,
  initialDeformation,
}: Props) => {
  const t = useTranslations('forms.project-params.wood.compositeProperties')
  const tCommon = useTranslations('actions.common')

  const { data: productsData } = useProductsQuery('wood', session.accessToken)

  const form = useForm<FormSchema>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      frpElasticityModulus: defaultValues?.frpElasticityModulus || 0,
      frpDesignMaximumStrain: defaultValues?.frpDesignMaximumStrain || 0,
      frpCharacteristicStrain: defaultValues?.frpCharacteristicStrain || 0,
      frpPartialFactorInUls: defaultValues?.frpPartialFactorInUls || 1.1,
      frpMaximumStrainForDebonding:
        defaultValues?.frpMaximumStrainForDebonding || 0,
      loadConditionFactor: defaultValues?.loadConditionFactor || 1.25,
      frpPartialFactorInUlsForDebonding:
        defaultValues?.frpPartialFactorInUlsForDebonding || 1.2,
      reinforcementToSectionWidthRatio:
        defaultValues?.reinforcementToSectionWidthRatio || 0,
      reinforcementToSectionWidthRatioUsefull:
        defaultValues?.reinforcementToSectionWidthRatioUsefull || 0,
      geometricCorrectionFactor: defaultValues?.geometricCorrectionFactor || 0,
      geometricCorrectionFactorUsefull:
        defaultValues?.geometricCorrectionFactorUsefull || 0,
      experimentalCorrectionFactor:
        defaultValues?.experimentalCorrectionFactor || 0.1,
      confidenceFactor: defaultValues?.confidenceFactor || 1.35,
      sectionModulus: defaultValues?.sectionModulus || 0,
      momentOfInertiaAboutY: defaultValues?.momentOfInertiaAboutY || 0,
    },
  })

  // Reset form when defaultValues change (for saved data after refresh)
  useEffect(() => {
    if (defaultValues) {
      form.reset({
        frpElasticityModulus: defaultValues.frpElasticityModulus ?? 0,
        frpDesignMaximumStrain: defaultValues.frpDesignMaximumStrain ?? 0,
        frpCharacteristicStrain: defaultValues.frpCharacteristicStrain ?? 0,
        frpPartialFactorInUls: defaultValues.frpPartialFactorInUls ?? 1.1,
        frpMaximumStrainForDebonding:
          defaultValues.frpMaximumStrainForDebonding ?? 0,
        loadConditionFactor: defaultValues.loadConditionFactor ?? 1.25,
        frpPartialFactorInUlsForDebonding:
          defaultValues.frpPartialFactorInUlsForDebonding ?? 1.2,
        reinforcementToSectionWidthRatio:
          defaultValues.reinforcementToSectionWidthRatio ?? 0,
        reinforcementToSectionWidthRatioUsefull:
          defaultValues.reinforcementToSectionWidthRatioUsefull ?? 0,
        geometricCorrectionFactor: defaultValues.geometricCorrectionFactor ?? 0,
        geometricCorrectionFactorUsefull:
          defaultValues.geometricCorrectionFactorUsefull ?? 0,
        experimentalCorrectionFactor:
          defaultValues.experimentalCorrectionFactor ?? 0.1,
        confidenceFactor: defaultValues.confidenceFactor ?? 1.35,
        sectionModulus: defaultValues.sectionModulus ?? 0,
        momentOfInertiaAboutY: defaultValues.momentOfInertiaAboutY ?? 0,
      })
    }
  }, [defaultValues, form])

  const { mutate, isPending } = useSaveModuleParamsMutation(session.accessToken)

  const handleFormSubmit = useCallback(
    (data: FormSchema) => {
      // Save as part of postIntervationCheck structure with all inherited data
      mutate({
        projectId,
        moduleId,
        body: {
          postIntervationCheck: {
            // Include inherited data from pre-intervention
            initialDeformation: initialDeformation || 0,
            materialProperties: materialProperties || null,
            geometry: geometryProperties || null,
            // Include composite geometry if available
            compositeGeometry: compositeGeometry || null,
            // Include the current form data
            compositeProperties: data,
          },
        } as any,
      })
      onSave?.()
    },
    [
      mutate,
      projectId,
      moduleId,
      onSave,
      materialProperties,
      geometryProperties,
      compositeGeometry,
      initialDeformation,
    ],
  )

  // Calculate derived values when dependencies change
  useEffect(() => {
    if (
      !compositeGeometry ||
      !materialProperties ||
      !geometryProperties ||
      !productsData?.content
    ) {
      return
    }

    const selectedProduct = productsData.content.find(
      p => p.id === compositeGeometry.productId,
    )
    if (!selectedProduct) {
      return
    }

    // Set FRP elasticity modulus from selected product
    form.setValue('frpElasticityModulus', selectedProduct.elasticModulus)

    // Set FRP characteristic strain from selected product
    form.setValue('frpCharacteristicStrain', selectedProduct.systemDeformation)

    // Calculate reinforcement to section width ratio
    // Formula: strip_width (mm) / beamSectionWidth (m) - convert stripWidth to meters

    const reinforcementRatio =
      compositeGeometry.stripWidth / 1000 / geometryProperties.beamSectionWidth
    form.setValue('reinforcementToSectionWidthRatio', reinforcementRatio)

    // Calculate reinforcement to section width ratio useful
    // If ratio < 0.25, use 0.25; otherwise use the ratio (corrected logic)
    const reinforcementRatioUseful =
      reinforcementRatio < 0.25 ? 0.25 : reinforcementRatio
    form.setValue(
      'reinforcementToSectionWidthRatioUsefull',
      reinforcementRatioUseful,
    )

    // Calculate geometric correction factor
    const geometricFactor =
      ((2 - reinforcementRatioUseful) / (1 + reinforcementRatioUseful)) ** 0.5
    form.setValue('geometricCorrectionFactor', geometricFactor)

    // Calculate geometric correction factor useful
    // If factor > 1, use the factor; otherwise use 1 (as per specification)
    const geometricFactorUseful = geometricFactor > 1 ? geometricFactor : 1
    form.setValue('geometricCorrectionFactorUsefull', geometricFactorUseful)

    // Calculate moment of inertia about Y
    const momentOfInertia =
      (geometryProperties.beamSectionWidth *
        geometryProperties.beamSectionHeight ** 3) /
        12 +
      (((((((compositeGeometry.layersNumber *
        compositeGeometry.equivalentThickness) /
        1000) *
        compositeGeometry.stripWidth) /
        1000) *
        selectedProduct.elasticModulus) /
        materialProperties.meanElasticityModulus) *
        geometryProperties.beamSectionHeight ** 2) /
        2
    form.setValue('momentOfInertiaAboutY', momentOfInertia)

    // Calculate section modulus
    const sectionMod =
      momentOfInertia / (geometryProperties.beamSectionHeight / 2)
    form.setValue('sectionModulus', sectionMod)

    // Calculate FRP maximum strain for debonding
    // Formula: load_condition_factor/(frp_elasticity_modulus*frp_partial_factor_in_uls_for_debonding)*√[(frp_elasticity_modulus*2*geometric_correction_factor_usefull*experimental_correction_factor)/(layers_number*equivalent_thickness*confidence_factor)*√(characteristicTensileStrength*characteristicCompressiveStrength)]
    const loadConditionFactor = form.getValues('loadConditionFactor')
    const frpPartialFactorInUlsForDebonding = form.getValues(
      'frpPartialFactorInUlsForDebonding',
    )
    const experimentalCorrectionFactor = form.getValues(
      'experimentalCorrectionFactor',
    )
    const confidenceFactor = form.getValues('confidenceFactor')

    const maxStrainDebonding =
      (loadConditionFactor /
        (selectedProduct.elasticModulus * frpPartialFactorInUlsForDebonding)) *
      Math.sqrt(
        (selectedProduct.elasticModulus *
          2 *
          geometricFactor *
          experimentalCorrectionFactor *
          Math.sqrt(
            materialProperties.characteristicTensileStrength *
              materialProperties.characteristicCompressiveStrength,
          )) /
          (compositeGeometry.layersNumber *
            compositeGeometry.equivalentThickness *
            confidenceFactor),
      )

    form.setValue('frpMaximumStrainForDebonding', maxStrainDebonding)

    // STEP 9: Calculate frp_design_maximum_strain
    // Formula: MIN(environmental_conversion_factor * frp_characteristic_strain / frp_partial_factor_in_uls, frp_maximum_strain_for_debonding)

    // Get the required values
    const environmentalConversionFactor =
      compositeGeometry.environmentalConversionFactor
    const frpCharacteristicStrain = selectedProduct.systemDeformation // This is frp_characteristic_strain
    const frpPartialFactorInUls = form.getValues('frpPartialFactorInUls')

    // Calculate the first part of MIN function
    const designStrainFirstPart =
      (environmentalConversionFactor * frpCharacteristicStrain) /
      frpPartialFactorInUls

    // Calculate the MIN of both values
    const designMaxStrain = Math.min(designStrainFirstPart, maxStrainDebonding)

    form.setValue('frpDesignMaximumStrain', designMaxStrain)

    // Notify parent component of the calculated properties
    if (onPropertiesChange) {
      const currentProperties = {
        frpElasticityModulus: selectedProduct.elasticModulus,
        frpDesignMaximumStrain: designMaxStrain,
        frpCharacteristicStrain: frpCharacteristicStrain,
        frpPartialFactorInUls: form.getValues('frpPartialFactorInUls'),
        frpMaximumStrainForDebonding: maxStrainDebonding,
        loadConditionFactor: form.getValues('loadConditionFactor'),
        frpPartialFactorInUlsForDebonding: form.getValues(
          'frpPartialFactorInUlsForDebonding',
        ),
        reinforcementToSectionWidthRatio: reinforcementRatio,
        reinforcementToSectionWidthRatioUsefull: reinforcementRatioUseful,
        geometricCorrectionFactor: geometricFactor,
        geometricCorrectionFactorUsefull: geometricFactorUseful,
        experimentalCorrectionFactor: form.getValues(
          'experimentalCorrectionFactor',
        ),
        confidenceFactor: form.getValues('confidenceFactor'),
        sectionModulus: sectionMod,
        momentOfInertiaAboutY: momentOfInertia,
      }
      onPropertiesChange(currentProperties)
    }
  }, [
    compositeGeometry,
    materialProperties,
    geometryProperties,
    productsData,
    form,
    onPropertiesChange,
  ])

  // Watch for changes in user input fields and recalculate dependent values
  const _watchedValues = form.watch([
    'frpPartialFactorInUls',
    'loadConditionFactor',
    'frpPartialFactorInUlsForDebonding',
    'experimentalCorrectionFactor',
    'confidenceFactor',
  ])

  useEffect(() => {
    if (
      !compositeGeometry ||
      !materialProperties ||
      !geometryProperties ||
      !productsData?.content
    ) {
      return
    }

    const selectedProduct = productsData.content.find(
      p => p.id === compositeGeometry.productId,
    )
    if (!selectedProduct) {
      return
    }

    // Get current values
    const geometricFactor = form.getValues('geometricCorrectionFactor')
    const loadConditionFactor = form.getValues('loadConditionFactor')
    const frpPartialFactorInUlsForDebonding = form.getValues(
      'frpPartialFactorInUlsForDebonding',
    )
    const experimentalCorrectionFactor = form.getValues(
      'experimentalCorrectionFactor',
    )
    const confidenceFactor = form.getValues('confidenceFactor')
    const frpPartialFactorInUls = form.getValues('frpPartialFactorInUls')

    // Recalculate FRP maximum strain for debonding when user inputs change
    // Using the EXACT formula: (K / (E * gamma)) * Math.sqrt((E * 2 * kappa * eta * Math.sqrt(ft * fc)) / (n * t_eq * Cf))
    const maxStrainDebonding =
      (loadConditionFactor /
        (selectedProduct.elasticModulus * frpPartialFactorInUlsForDebonding)) *
      Math.sqrt(
        (selectedProduct.elasticModulus *
          2 *
          geometricFactor *
          experimentalCorrectionFactor *
          Math.sqrt(
            materialProperties.characteristicTensileStrength *
              materialProperties.characteristicCompressiveStrength,
          )) /
          (compositeGeometry.layersNumber *
            compositeGeometry.equivalentThickness *
            confidenceFactor),
      )

    form.setValue('frpMaximumStrainForDebonding', maxStrainDebonding)

    // Recalculate FRP design maximum strain
    const recalcDesignStrainFirstPart =
      (compositeGeometry.environmentalConversionFactor *
        selectedProduct.systemDeformation) /
      frpPartialFactorInUls
    const designMaxStrain = Math.min(
      recalcDesignStrainFirstPart,
      maxStrainDebonding,
    )

    form.setValue('frpDesignMaximumStrain', designMaxStrain)

    // Notify parent component of the updated properties
    if (onPropertiesChange) {
      const currentProperties = {
        frpElasticityModulus: selectedProduct.elasticModulus,
        frpDesignMaximumStrain: designMaxStrain,
        frpCharacteristicStrain: selectedProduct.systemDeformation,
        frpPartialFactorInUls: frpPartialFactorInUls,
        frpMaximumStrainForDebonding: maxStrainDebonding,
        loadConditionFactor: loadConditionFactor,
        frpPartialFactorInUlsForDebonding: frpPartialFactorInUlsForDebonding,
        reinforcementToSectionWidthRatio: form.getValues(
          'reinforcementToSectionWidthRatio',
        ),
        reinforcementToSectionWidthRatioUsefull: form.getValues(
          'reinforcementToSectionWidthRatioUsefull',
        ),
        geometricCorrectionFactor: geometricFactor,
        geometricCorrectionFactorUsefull: form.getValues(
          'geometricCorrectionFactorUsefull',
        ),
        experimentalCorrectionFactor: experimentalCorrectionFactor,
        confidenceFactor: confidenceFactor,
        sectionModulus: form.getValues('sectionModulus'),
        momentOfInertiaAboutY: form.getValues('momentOfInertiaAboutY'),
      }
      onPropertiesChange(currentProperties)
    }
  }, [
    compositeGeometry,
    materialProperties,
    geometryProperties,
    productsData,
    form,
    onPropertiesChange,
  ])

  return (
    <div className="flex flex-col 2xl:flex-row justify-center gap-2">
      <Form {...form}>
        <form
          className="space-y-4 rounded-md border p-4 grow"
          onSubmit={form.handleSubmit(handleFormSubmit)}
        >
          <NumberFormInput
            control={form.control}
            name="frpElasticityModulus"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />

          <NumberFormInput
            control={form.control}
            name="frpDesignMaximumStrain"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />

          <NumberFormInput
            control={form.control}
            name="frpCharacteristicStrain"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />

          <NumberFormInput
            control={form.control}
            name="frpPartialFactorInUls"
            t={t}
            disabled={true}
          />

          <NumberFormInput
            control={form.control}
            name="frpMaximumStrainForDebonding"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />

          <NumberFormInput
            control={form.control}
            name="loadConditionFactor"
            t={t}
            disabled={true}
          />

          <NumberFormInput
            control={form.control}
            name="frpPartialFactorInUlsForDebonding"
            t={t}
            disabled={true}
          />

          <NumberFormInput
            control={form.control}
            name="reinforcementToSectionWidthRatio"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />

          <NumberFormInput
            control={form.control}
            name="reinforcementToSectionWidthRatioUsefull"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />

          <NumberFormInput
            control={form.control}
            name="geometricCorrectionFactor"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />

          <NumberFormInput
            control={form.control}
            name="geometricCorrectionFactorUsefull"
            t={t}
            disabled={true}
            decimalPlaces={3}
          />

          <NumberFormInput
            control={form.control}
            name="experimentalCorrectionFactor"
            t={t}
            disabled={true}
          />

          <NumberFormInput
            control={form.control}
            name="confidenceFactor"
            t={t}
            disabled={true}
          />

          <NumberFormInput
            control={form.control}
            name="sectionModulus"
            t={t}
            disabled={true}
            decimalPlaces={6}
          />

          <NumberFormInput
            control={form.control}
            name="momentOfInertiaAboutY"
            t={t}
            disabled={true}
            decimalPlaces={6}
          />

          <Button
            type="submit"
            className="w-full sm:w-auto"
            disabled={isPending}
          >
            {isPending && <Loader2Icon className="animate-spin" />}
            {tCommon('save')}
          </Button>
        </form>
      </Form>
    </div>
  )
}
