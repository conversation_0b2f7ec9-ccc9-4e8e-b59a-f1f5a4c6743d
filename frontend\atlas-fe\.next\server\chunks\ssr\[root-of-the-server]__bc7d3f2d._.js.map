{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\r\nimport { twMerge } from 'tailwind-merge'\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/breadcrumb.tsx"], "sourcesContent": ["import { cn } from '@atlas/lib/utils'\r\nimport { Slot } from '@radix-ui/react-slot'\r\nimport { ChevronRight, MoreHorizontal } from 'lucide-react'\r\nimport type * as React from 'react'\r\n\r\nfunction Breadcrumb({ ...props }: React.ComponentProps<'nav'>) {\r\n  return <nav aria-label=\"breadcrumb\" data-slot=\"breadcrumb\" {...props} />\r\n}\r\n\r\nfunction BreadcrumbList({ className, ...props }: React.ComponentProps<'ol'>) {\r\n  return (\r\n    <ol\r\n      data-slot=\"breadcrumb-list\"\r\n      className={cn(\r\n        'text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5',\r\n        className,\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbItem({ className, ...props }: React.ComponentProps<'li'>) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-item\"\r\n      className={cn('inline-flex items-center gap-1.5', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbLink({\r\n  asChild,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'a'> & {\r\n  asChild?: boolean\r\n}) {\r\n  const Comp = asChild ? Slot : 'a'\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"breadcrumb-link\"\r\n      className={cn('hover:text-foreground transition-colors', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbPage({ className, ...props }: React.ComponentProps<'span'>) {\r\n  return (\r\n    // biome-ignore lint/a11y/useFocusableInteractive: from shadcn/ui/components/breadcrumb\r\n    <span\r\n      data-slot=\"breadcrumb-page\"\r\n      role=\"link\"\r\n      aria-disabled=\"true\"\r\n      aria-current=\"page\"\r\n      className={cn('text-foreground font-normal', className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nfunction BreadcrumbSeparator({\r\n  children,\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'li'>) {\r\n  return (\r\n    <li\r\n      data-slot=\"breadcrumb-separator\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn('[&>svg]:size-3.5', className)}\r\n      {...props}\r\n    >\r\n      {children ?? <ChevronRight />}\r\n    </li>\r\n  )\r\n}\r\n\r\nfunction BreadcrumbEllipsis({\r\n  className,\r\n  ...props\r\n}: React.ComponentProps<'span'>) {\r\n  return (\r\n    <span\r\n      data-slot=\"breadcrumb-ellipsis\"\r\n      role=\"presentation\"\r\n      aria-hidden=\"true\"\r\n      className={cn('flex size-9 items-center justify-center', className)}\r\n      {...props}\r\n    >\r\n      <MoreHorizontal className=\"size-4\" />\r\n      <span className=\"sr-only\">More</span>\r\n    </span>\r\n  )\r\n}\r\n\r\nexport {\r\n  Breadcrumb,\r\n  BreadcrumbList,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbPage,\r\n  BreadcrumbSeparator,\r\n  BreadcrumbEllipsis,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AAAA;;;;;AAGA,SAAS,WAAW,EAAE,GAAG,OAAoC;IAC3D,qBAAO,8OAAC;QAAI,cAAW;QAAa,aAAU;QAAc,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,4FACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAmC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,oCAAoC;QACjD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,OAAO,EACP,SAAS,EACT,GAAG,OAGJ;IACC,MAAM,OAAO,UAAU,wKAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EAAE,SAAS,EAAE,GAAG,OAAqC;IAC3E,OACE,uFAAuF;kBACvF,8OAAC;QACC,aAAU;QACV,MAAK;QACL,iBAAc;QACd,gBAAa;QACb,WAAW,IAAA,yHAAE,EAAC,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,QAAQ,EACR,SAAS,EACT,GAAG,OACwB;IAC3B,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,IAAA,yHAAE,EAAC,oBAAoB;QACjC,GAAG,KAAK;kBAER,0BAAY,8OAAC,sOAAY;;;;;;;;;;AAGhC;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,MAAK;QACL,eAAY;QACZ,WAAW,IAAA,yHAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;0BAET,8OAAC,kOAAc;gBAAC,WAAU;;;;;;0BAC1B,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/breadcrumbs.tsx"], "sourcesContent": ["import {\r\n  B<PERSON><PERSON>rumb,\r\n  BreadcrumbItem,\r\n  BreadcrumbLink,\r\n  BreadcrumbList,\r\n  BreadcrumbSeparator,\r\n} from '@atlas/components/ui/breadcrumb'\r\nimport { Fragment } from 'react'\r\n\r\ntype Props = {\r\n  content: { label: string; href?: string }[]\r\n}\r\n\r\nexport const Breadcrumbs = ({ content }: Props) => {\r\n  return (\r\n    <Breadcrumb>\r\n      <BreadcrumbList>\r\n        {content.map(({ label, href }, index) => (\r\n          <Fragment key={href ?? `${label}-${index}`}>\r\n            <BreadcrumbItem>\r\n              {href ? (\r\n                <BreadcrumbLink href={href}>{label}</BreadcrumbLink>\r\n              ) : (\r\n                label\r\n              )}\r\n            </BreadcrumbItem>\r\n            {index !== content.length - 1 && <BreadcrumbSeparator />}\r\n          </Fragment>\r\n        ))}\r\n      </BreadcrumbList>\r\n    </Breadcrumb>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAOA;;;;AAMO,MAAM,cAAc,CAAC,EAAE,OAAO,EAAS;IAC5C,qBACE,8OAAC,oJAAU;kBACT,cAAA,8OAAC,wJAAc;sBACZ,QAAQ,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,sBAC7B,8OAAC,iNAAQ;;sCACP,8OAAC,wJAAc;sCACZ,qBACC,8OAAC,wJAAc;gCAAC,MAAM;0CAAO;;;;;2EAE7B;;;;;;wBAGH,UAAU,QAAQ,MAAM,GAAG,mBAAK,8OAAC,6JAAmB;;;;;;mBARxC,QAAQ,GAAG,MAAM,CAAC,EAAE,OAAO;;;;;;;;;;;;;;;AAcpD", "debugId": null}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/pages/dashboard/dashboard-content/dashboard-projects.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const DashboardProjects = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardProjects() from the server but DashboardProjects is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/pages/dashboard/dashboard-content/dashboard-projects.tsx <module evaluation>\",\n    \"DashboardProjects\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,oBAAoB,IAAA,wQAAuB,EACpD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,yGACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/pages/dashboard/dashboard-content/dashboard-projects.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const DashboardProjects = registerClientReference(\n    function() { throw new Error(\"Attempted to call DashboardProjects() from the server but DashboardProjects is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/pages/dashboard/dashboard-content/dashboard-projects.tsx\",\n    \"DashboardProjects\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,oBAAoB,IAAA,wQAAuB,EACpD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,qFACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 267, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/button.tsx"], "sourcesContent": ["import { cn } from '@atlas/lib/utils'\r\nimport { Slot } from '@radix-ui/react-slot'\r\nimport { cva, type VariantProps } from 'class-variance-authority'\r\nimport type * as React from 'react'\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          'bg-primary text-primary-foreground shadow-xs hover:bg-primary/90',\r\n        destructive:\r\n          'bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60',\r\n        outline:\r\n          'border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50',\r\n        secondary:\r\n          'bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80',\r\n        ghost:\r\n          'hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50',\r\n        link: 'text-primary underline-offset-4 hover:underline',\r\n      },\r\n      size: {\r\n        default: 'h-9 px-4 py-2 has-[>svg]:px-3',\r\n        sm: 'h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5',\r\n        lg: 'h-10 rounded-md px-6 has-[>svg]:px-4',\r\n        icon: 'size-9',\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: 'default',\r\n      size: 'default',\r\n    },\r\n  },\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<'button'> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : 'button'\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;;AAGA,MAAM,iBAAiB,IAAA,uKAAG,EACxB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,wKAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 324, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/separator.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Separator = registerClientReference(\n    function() { throw new Error(\"Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/separator.tsx <module evaluation>\",\n    \"Separator\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,YAAY,IAAA,wQAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,iEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 338, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/separator.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Separator = registerClientReference(\n    function() { throw new Error(\"Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/separator.tsx\",\n    \"Separator\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,YAAY,IAAA,wQAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 352, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 360, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/pages/dashboard/dashboard-page.tsx"], "sourcesContent": ["import { auth, signIn } from '@atlas/auth'\r\nimport { Breadcrumbs } from '@atlas/components/common/atlas/breadcrumbs'\r\nimport { DashboardProjects } from '@atlas/components/pages/dashboard/dashboard-content/dashboard-projects'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport { SidebarTrigger } from '@atlas/components/ui/sidebar'\r\nimport { Link } from '@atlas/i18n/routing'\r\nimport { PlusCircle } from 'lucide-react'\r\nimport { getTranslations } from 'next-intl/server'\r\n\r\nexport const DashboardPage = async () => {\r\n  const session = await auth()\r\n  const tBread = await getTranslations('breadcrumbs')\r\n  const tActions = await getTranslations('actions')\r\n\r\n  if (!session?.user) {\r\n    return signIn()\r\n  }\r\n\r\n  return (\r\n    <>\r\n      <header className=\"flex h-16 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12\">\r\n        <div className=\"flex items-center gap-2 px-4\">\r\n          <SidebarTrigger className=\"-ml-1\" />\r\n          <Separator orientation=\"vertical\" className=\"mr-2 h-4\" />\r\n          <Breadcrumbs content={[{ label: tBread('dashboard') }]} />\r\n        </div>\r\n      </header>\r\n      <div className=\"flex flex-1 flex-col gap-4 p-4 pt-0\">\r\n        <Button className=\"w-40\" asChild>\r\n          <Link href=\"/dashboard/projects/create\">\r\n            <PlusCircle />\r\n            {tActions('create-project.label')}\r\n          </Link>\r\n        </Button>\r\n        <DashboardProjects session={session} />\r\n      </div>\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AAEO,MAAM,gBAAgB;IAC3B,MAAM,UAAU,MAAM,IAAA,mHAAI;IAC1B,MAAM,SAAS,MAAM,IAAA,2QAAe,EAAC;IACrC,MAAM,WAAW,MAAM,IAAA,2QAAe,EAAC;IAEvC,IAAI,CAAC,SAAS,MAAM;QAClB,OAAO,IAAA,qHAAM;IACf;IAEA,qBACE;;0BACE,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,qJAAc;4BAAC,WAAU;;;;;;sCAC1B,8OAAC,kJAAS;4BAAC,aAAY;4BAAW,WAAU;;;;;;sCAC5C,8OAAC,mKAAW;4BAAC,SAAS;gCAAC;oCAAE,OAAO,OAAO;gCAAa;6BAAE;;;;;;;;;;;;;;;;;0BAG1D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4IAAM;wBAAC,WAAU;wBAAO,OAAO;kCAC9B,cAAA,8OAAC,8HAAI;4BAAC,MAAK;;8CACT,8OAAC,gOAAU;;;;;gCACV,SAAS;;;;;;;;;;;;kCAGd,8OAAC,8MAAiB;wBAAC,SAAS;;;;;;;;;;;;;;AAIpC", "debugId": null}}, {"offset": {"line": 481, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/app/%5Blocale%5D/dashboard/page.tsx"], "sourcesContent": ["import { DashboardPage } from '@atlas/components/pages/dashboard/dashboard-page'\r\nimport { getTranslations } from 'next-intl/server'\r\n\r\nexport const generateMetadata = async ({\r\n  params,\r\n}: {\r\n  params: Promise<{ locale: string }>\r\n}) => {\r\n  const { locale } = await params\r\n  const t = await getTranslations({ locale, namespace: 'pages.dashboard.meta' })\r\n\r\n  return {\r\n    title: t('title'),\r\n    description: t('description'),\r\n  }\r\n}\r\n\r\nexport default DashboardPage\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAEO,MAAM,mBAAmB,OAAO,EACrC,MAAM,EAGP;IACC,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,MAAM,IAAI,MAAM,IAAA,2QAAe,EAAC;QAAE;QAAQ,WAAW;IAAuB;IAE5E,OAAO;QACL,OAAO,EAAE;QACT,aAAa,EAAE;IACjB;AACF;uCAEe,8KAAa", "debugId": null}}]}