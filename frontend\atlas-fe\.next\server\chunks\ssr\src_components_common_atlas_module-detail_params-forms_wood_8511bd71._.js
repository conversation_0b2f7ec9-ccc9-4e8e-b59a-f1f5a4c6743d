module.exports = [
"[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "WoodBeamGeometryForm",
    ()=>WoodBeamGeometryForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/select-form-input.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$wood$2d$params$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/modules/schemas/wood-params.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-save-module-params-mutation.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-ssr] (ecmascript) <export * as z>");
;
;
;
;
;
;
;
;
;
;
;
;
;
const formSchema = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$wood$2d$params$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["woodGeometryPropertiesSchema"].extend({
    id: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().optional()
});
const SERVICE_CLASS_OPTIONS = [
    {
        value: 'SERVICE_CLASS_1',
        label: 'Classe di servizio 1'
    },
    {
        value: 'SERVICE_CLASS_2',
        label: 'Classe di servizio 2'
    },
    {
        value: 'SERVICE_CLASS_3',
        label: 'Classe di servizio 3'
    }
];
const LOAD_DURATION_OPTIONS = [
    {
        value: 'PERMANENTE',
        label: 'Permanente'
    },
    {
        value: 'LUNGA',
        label: 'Lunga'
    },
    {
        value: 'MEDIA',
        label: 'Media'
    },
    {
        value: 'BREVE',
        label: 'Breve'
    },
    {
        value: 'INSTANTANEA',
        label: 'Instantanea'
    }
];
const KMOD_TABLE = {
    SERVICE_CLASS_1: {
        PERMANENTE: 0.6,
        LUNGA: 0.7,
        MEDIA: 0.8,
        BREVE: 0.9,
        INSTANTANEA: 1.1
    },
    SERVICE_CLASS_2: {
        PERMANENTE: 0.6,
        LUNGA: 0.7,
        MEDIA: 0.8,
        BREVE: 0.9,
        INSTANTANEA: 1.1
    },
    SERVICE_CLASS_3: {
        PERMANENTE: 0.5,
        LUNGA: 0.55,
        MEDIA: 0.65,
        BREVE: 0.7,
        INSTANTANEA: 0.9
    }
};
const KDEF_VALUES = {
    SERVICE_CLASS_1: 0.6,
    SERVICE_CLASS_2: 0.8,
    SERVICE_CLASS_3: 2.0
};
const WoodBeamGeometryForm = ({ session, projectId, moduleId, defaultValues, materialProperties, onSave })=>{
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.wood.geometric_properties');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.save-project-params.messages');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"])(session.accessToken, {
        onSuccess: ()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success(tAction('edit.success'));
            onSave();
        },
        onError: ()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(tAction('edit.error'));
        }
    });
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["zodResolver"])(formSchema),
        defaultValues: {
            id: defaultValues?.id,
            beamSectionWidth: defaultValues?.beamSectionWidth ?? 0,
            beamSectionHeight: defaultValues?.beamSectionHeight ?? 0,
            beamSpacing: defaultValues?.beamSpacing ?? 0,
            beamSpan: defaultValues?.beamSpan ?? 0,
            sectionModulus: defaultValues?.sectionModulus ?? 0,
            inertiaMomentAboutY: defaultValues?.inertiaMomentAboutY ?? 0,
            serviceClass: defaultValues?.serviceClass ?? 'SERVICE_CLASS_1',
            loadDuration: defaultValues?.loadDuration ?? 'MEDIA',
            correctionFactor: defaultValues?.correctionFactor ?? 0.8,
            deformabilityFactor: defaultValues?.deformabilityFactor ?? 0.6,
            designBendingStrength: defaultValues?.designBendingStrength ?? 0,
            designShearStrength: defaultValues?.designShearStrength ?? 0,
            elasticityInstantaneousModulus: defaultValues?.elasticityInstantaneousModulus ?? 0,
            longTermElasticityModulus: defaultValues?.longTermElasticityModulus ?? 0
        },
        shouldUnregister: false
    });
    const beamSectionWidth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useWatch"])({
        control: form.control,
        name: 'beamSectionWidth'
    });
    const beamSectionHeight = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useWatch"])({
        control: form.control,
        name: 'beamSectionHeight'
    });
    const serviceClass = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useWatch"])({
        control: form.control,
        name: 'serviceClass'
    });
    const loadDuration = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useWatch"])({
        control: form.control,
        name: 'loadDuration'
    });
    const correctionFactor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useWatch"])({
        control: form.control,
        name: 'correctionFactor'
    });
    const deformabilityFactor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useWatch"])({
        control: form.control,
        name: 'deformabilityFactor'
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (beamSectionWidth > 0 && beamSectionHeight > 0) {
            const sectionModulus = beamSectionWidth * Math.pow(beamSectionHeight, 2) / 6;
            const inertiaMoment = beamSectionWidth * Math.pow(beamSectionHeight, 3) / 12;
            form.setValue('sectionModulus', sectionModulus);
            form.setValue('inertiaMomentAboutY', inertiaMoment);
        }
    }, [
        beamSectionWidth,
        beamSectionHeight,
        form
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (serviceClass && loadDuration) {
            const correction = KMOD_TABLE[serviceClass]?.[loadDuration] ?? 0.8;
            form.setValue('correctionFactor', correction);
        }
        if (serviceClass) {
            const deformability = KDEF_VALUES[serviceClass] ?? 0.6;
            form.setValue('deformabilityFactor', deformability);
        }
    }, [
        serviceClass,
        loadDuration,
        form
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const { characteristicBendingStrength = 0, characteristicShearStrength = 0, partialMaterialFactor = 1.3, meanElasticityModulus = 0 } = materialProperties ?? {};
        if (correctionFactor > 0 && partialMaterialFactor > 0 && characteristicBendingStrength > 0 && characteristicShearStrength > 0) {
            const designBendingStrength = characteristicBendingStrength * correctionFactor / partialMaterialFactor;
            const designShearStrength = characteristicShearStrength * correctionFactor / partialMaterialFactor;
            form.setValue('designBendingStrength', designBendingStrength);
            form.setValue('designShearStrength', designShearStrength);
        }
        if (meanElasticityModulus > 0) {
            form.setValue('elasticityInstantaneousModulus', meanElasticityModulus);
            if (deformabilityFactor > 0) {
                const longTermModulus = meanElasticityModulus / (1 + deformabilityFactor);
                form.setValue('longTermElasticityModulus', longTermModulus);
            }
        }
    }, [
        correctionFactor,
        deformabilityFactor,
        materialProperties,
        form
    ]);
    const handleFormSubmit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((data)=>{
        mutate({
            projectId,
            moduleId,
            body: {
                geometry: data
            }
        });
    }, [
        mutate,
        projectId,
        moduleId
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Form"], {
        ...form,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            className: "space-y-4 rounded-md border p-4 grow",
            onSubmit: form.handleSubmit(handleFormSubmit),
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "beamSectionWidth",
                    t: t,
                    required: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form.tsx",
                    lineNumber: 206,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "beamSectionHeight",
                    t: t,
                    required: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form.tsx",
                    lineNumber: 207,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "beamSpacing",
                    t: t,
                    required: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form.tsx",
                    lineNumber: 208,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "beamSpan",
                    t: t,
                    required: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form.tsx",
                    lineNumber: 209,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "sectionModulus",
                    t: t,
                    disabled: true,
                    decimalPlaces: 5
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form.tsx",
                    lineNumber: 212,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "inertiaMomentAboutY",
                    t: t,
                    disabled: true,
                    decimalPlaces: 5
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form.tsx",
                    lineNumber: 219,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SelectFormInput"], {
                    control: form.control,
                    name: "serviceClass",
                    options: SERVICE_CLASS_OPTIONS,
                    t: t,
                    required: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form.tsx",
                    lineNumber: 228,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SelectFormInput"], {
                    control: form.control,
                    name: "loadDuration",
                    options: LOAD_DURATION_OPTIONS,
                    t: t,
                    required: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form.tsx",
                    lineNumber: 235,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "correctionFactor",
                    t: t,
                    disabled: true,
                    decimalPlaces: 3
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form.tsx",
                    lineNumber: 244,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "deformabilityFactor",
                    t: t,
                    disabled: true,
                    decimalPlaces: 3
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form.tsx",
                    lineNumber: 245,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "designBendingStrength",
                    t: t,
                    disabled: true,
                    decimalPlaces: 3
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form.tsx",
                    lineNumber: 246,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "designShearStrength",
                    t: t,
                    disabled: true,
                    decimalPlaces: 3
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form.tsx",
                    lineNumber: 247,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "elasticityInstantaneousModulus",
                    t: t,
                    disabled: true,
                    decimalPlaces: 3
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form.tsx",
                    lineNumber: 248,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "longTermElasticityModulus",
                    t: t,
                    disabled: true,
                    decimalPlaces: 3
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form.tsx",
                    lineNumber: 249,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                    type: "submit",
                    disabled: isPending,
                    children: isPending ? tCommon('saving') : tCommon('save')
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form.tsx",
                    lineNumber: 250,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form.tsx",
            lineNumber: 201,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form.tsx",
        lineNumber: 200,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
}),
"[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-general-form.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "WoodGeneralForm",
    ()=>WoodGeneralForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-save-module-params-mutation.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$wood$2d$form$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/wood-form.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
const WoodGeneralForm = ({ session, projectId, moduleId, defaultValues, onSave })=>{
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.wood.general');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.save-project-params.messages');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["zodResolver"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$wood$2d$form$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["woodGeneralSchema"]),
        defaultValues: {
            initialDeformation: defaultValues?.initialDeformation ?? 0
        }
    });
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"])(session.accessToken, {
        onSuccess: ()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success(tAction('edit.success'));
            // Query invalidation is handled by the mutation hook itself
            onSave();
        },
        onError: (error)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(tAction('edit.failure', {
                error: error.message
            }));
        }
    });
    const handleFormSubmit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((body)=>{
        mutate({
            projectId,
            moduleId,
            body
        });
    }, [
        mutate,
        projectId,
        moduleId
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Form"], {
        ...form,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            className: "space-y-4 rounded-md border p-4",
            onSubmit: form.handleSubmit(handleFormSubmit),
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "initialDeformation",
                    t: t,
                    required: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-general-form.tsx",
                    lineNumber: 73,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                    type: "submit",
                    className: "w-full sm:w-auto",
                    disabled: isPending,
                    onClick: form.handleSubmit(handleFormSubmit),
                    children: [
                        isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                            className: "animate-spin"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-general-form.tsx",
                            lineNumber: 85,
                            columnNumber: 25
                        }, ("TURBOPACK compile-time value", void 0)),
                        tCommon('save')
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-general-form.tsx",
                    lineNumber: 79,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-general-form.tsx",
            lineNumber: 69,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-general-form.tsx",
        lineNumber: 68,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
}),
"[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-geometry-form.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "WoodGeometryForm",
    ()=>WoodGeometryForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/select-form-input.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-save-module-params-mutation.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$materials$2f$use$2d$wood$2d$material$2d$by$2d$name$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/query/materials/use-wood-material-by-name.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$materials$2f$use$2d$wood$2d$names$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/query/materials/use-wood-names-api.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$wood$2d$form$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/wood-form.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-ssr] (ecmascript) <export * as z>");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
// Partial Material Factor dropdown options
const PARTIAL_MATERIAL_FACTOR_OPTIONS = [
    {
        value: '1',
        label: '1'
    },
    {
        value: '5',
        label: '5'
    },
    {
        value: '45',
        label: '45'
    }
];
// Custom form schema that extends the API schema but with string for dropdown
const woodGeometryFormSchema = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$wood$2d$form$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["woodMaterialSchema1"].extend({
    partialMaterialFactor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string()
});
const WoodGeometryForm = ({ session, projectId, moduleId, defaultValues, woodName: _woodName, onSave: _onSave })=>{
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.wood.geometry');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.save-project-params.messages');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const { data: woodNames, isError: woodNamesError, isLoading: woodNamesLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$materials$2f$use$2d$wood$2d$names$2d$api$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useWoodNames"])({
        session
    });
    const woodNamesOptions = [
        ...woodNames?.map((m)=>({
                value: m,
                label: m
            })) ?? []
    ];
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["zodResolver"])(woodGeometryFormSchema),
        defaultValues: {
            // Material Properties fields from woodMaterialSchema1
            id: defaultValues?.id,
            category: defaultValues?.woodName ?? '',
            partialMaterialFactor: defaultValues?.partialMaterialFactor ? defaultValues.partialMaterialFactor.toString() : '1',
            characteristicBendingStrength: defaultValues?.characteristicBendingStrength ?? 0,
            characteristicShearStrength: defaultValues?.characteristicShearStrength ?? 0,
            characteristicTensileStrength: defaultValues?.characteristicTensileStrength ?? 0,
            characteristicCompressiveStrength: defaultValues?.characteristicCompressiveStrength ?? 0,
            meanDensity: defaultValues?.meanDensity ?? 0,
            meanShearModulus: defaultValues?.meanShearModulus ?? 0,
            elasticityModulusParallelToGrain: defaultValues?.elasticityModulusParallelToGrain ?? 0,
            meanElasticityModulus: defaultValues?.meanElasticityModulus ?? 0
        },
        // Prevent form from resetting after successful submission
        shouldUnregister: false
    });
    // Reset form with new defaultValues when they change (component remount)
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (defaultValues) {
            form.reset({
                id: defaultValues.id,
                category: defaultValues.woodName ?? '',
                partialMaterialFactor: defaultValues.partialMaterialFactor ? defaultValues.partialMaterialFactor.toString() : '1',
                characteristicBendingStrength: defaultValues.characteristicBendingStrength ?? 0,
                characteristicShearStrength: defaultValues.characteristicShearStrength ?? 0,
                characteristicTensileStrength: defaultValues.characteristicTensileStrength ?? 0,
                characteristicCompressiveStrength: defaultValues.characteristicCompressiveStrength ?? 0,
                meanDensity: defaultValues.meanDensity ?? 0,
                meanShearModulus: defaultValues.meanShearModulus ?? 0,
                elasticityModulusParallelToGrain: defaultValues.elasticityModulusParallelToGrain ?? 0,
                meanElasticityModulus: defaultValues.meanElasticityModulus ?? 0
            });
        }
    }, [
        defaultValues,
        form
    ]);
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"])(session.accessToken, {
        onSuccess: ()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success(tAction('edit.success'));
        // Query invalidation is handled by the mutation hook itself
        // Don't call onSave() immediately to prevent form clearing
        // The parent component should handle accordion state separately
        // onSave()
        },
        onError: (error)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(tAction('edit.failure', {
                error: error.message
            }));
        }
    });
    // State to store category name from API - initialize from defaultValues if available
    const [categoryName, setCategoryName] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(defaultValues?.woodName || '');
    const handleFormSubmit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((data)=>{
        // Include categoryName from API in the category field
        const dataWithCategory = {
            ...data,
            category: categoryName || data.category,
            woodName: data.category,
            // Convert partialMaterialFactor from string to number for API
            partialMaterialFactor: parseFloat(data.partialMaterialFactor)
        };
        // Send as materialProperties to match the API structure
        mutate({
            projectId,
            moduleId,
            body: {
                materialProperties: dataWithCategory
            }
        });
    }, [
        mutate,
        projectId,
        moduleId,
        categoryName
    ]);
    // Watch for category changes to fetch wood material data
    const selectedCategory = form.watch('category');
    const { data: woodMaterialData } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$query$2f$materials$2f$use$2d$wood$2d$material$2d$by$2d$name$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useWoodMaterialByName"])(session, selectedCategory, !!selectedCategory);
    // Sync categoryName when defaultValues change (component remount or data refresh)
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (defaultValues?.category && !categoryName) {
            setCategoryName(defaultValues.category);
        }
    }, [
        defaultValues?.category,
        categoryName
    ]);
    // Update form fields when wood material data is fetched
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (woodMaterialData) {
            // Store category name from API response
            setCategoryName(woodMaterialData.category);
            // Map API response to form fields
            form.setValue('characteristicBendingStrength', woodMaterialData.flexuralStrength);
            form.setValue('characteristicShearStrength', woodMaterialData.shearStrength);
            form.setValue('characteristicTensileStrength', woodMaterialData.tensileStrengthParallel);
            form.setValue('characteristicCompressiveStrength', woodMaterialData.compressiveStrengthParallel);
            form.setValue('meanDensity', woodMaterialData.meanDensity);
            form.setValue('meanShearModulus', woodMaterialData.shearModulusMean);
            form.setValue('elasticityModulusParallelToGrain', woodMaterialData.elasticModulusCharacteristicParallel);
            form.setValue('meanElasticityModulus', woodMaterialData.elasticModulusMeanParallel);
        }
    }, [
        woodMaterialData,
        form
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col 2xl:flex-row justify-center gap-2",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Form"], {
            ...form,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                className: "space-y-4 rounded-md border p-4 grow",
                onSubmit: form.handleSubmit(handleFormSubmit),
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SelectFormInput"], {
                        control: form.control,
                        name: "category",
                        options: woodNamesOptions,
                        t: t,
                        loading: woodNamesLoading,
                        requestError: woodNamesError,
                        errorMessage: t('category.error'),
                        required: true
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-geometry-form.tsx",
                        lineNumber: 216,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "characteristicBendingStrength",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-geometry-form.tsx",
                        lineNumber: 226,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "characteristicShearStrength",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-geometry-form.tsx",
                        lineNumber: 233,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "characteristicTensileStrength",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-geometry-form.tsx",
                        lineNumber: 240,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "characteristicCompressiveStrength",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-geometry-form.tsx",
                        lineNumber: 247,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "meanDensity",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-geometry-form.tsx",
                        lineNumber: 254,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "meanShearModulus",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-geometry-form.tsx",
                        lineNumber: 261,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "elasticityModulusParallelToGrain",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-geometry-form.tsx",
                        lineNumber: 268,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "meanElasticityModulus",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-geometry-form.tsx",
                        lineNumber: 275,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SelectFormInput"], {
                        control: form.control,
                        name: "partialMaterialFactor",
                        options: PARTIAL_MATERIAL_FACTOR_OPTIONS,
                        t: t,
                        required: true
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-geometry-form.tsx",
                        lineNumber: 282,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                        type: "submit",
                        className: "w-full sm:w-auto",
                        disabled: isPending,
                        onClick: form.handleSubmit(handleFormSubmit),
                        children: [
                            isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                                className: "animate-spin"
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-geometry-form.tsx",
                                lineNumber: 295,
                                columnNumber: 27
                            }, ("TURBOPACK compile-time value", void 0)),
                            tCommon('save')
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-geometry-form.tsx",
                        lineNumber: 289,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-geometry-form.tsx",
                lineNumber: 212,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        }, void 0, false, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-geometry-form.tsx",
            lineNumber: 211,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-geometry-form.tsx",
        lineNumber: 210,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
}),
"[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "WoodPreInterventionCheckForm",
    ()=>WoodPreInterventionCheckForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/separator.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$wood$2d$params$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/modules/schemas/wood-params.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-save-module-params-mutation.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/sonner/dist/index.mjs [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
// Custom form schema that extends the API schema
const woodPreInterventionCheckFormSchema = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$modules$2f$schemas$2f$wood$2d$params$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["woodPreInterventionCheckSchema"];
const WoodPreInterventionCheckForm = ({ session, projectId, moduleId, defaultValues, geometryProperties, onSave, initialDeformation })=>{
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.wood.preIntervationCheck');
    const tAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.save-project-params.messages');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["zodResolver"])(woodPreInterventionCheckFormSchema),
        defaultValues: {
            maximumBendingMoment: defaultValues?.maximumBendingMoment ?? 0,
            maximumShearForce: defaultValues?.maximumShearForce ?? 0,
            designBendingStress: defaultValues?.designBendingStress ?? 0,
            designBendingStrength: defaultValues?.designBendingStrength ?? 0,
            bendingCheck: defaultValues?.bendingCheck ?? 0,
            designShearStress: defaultValues?.designShearStress ?? 0,
            designShearStrength: defaultValues?.designShearStrength ?? 0,
            shearCheck: defaultValues?.shearCheck ?? 0,
            permanentLoadPerLinearMeter: defaultValues?.permanentLoadPerLinearMeter ?? 0,
            imposedLoadPerLinearMeter: defaultValues?.imposedLoadPerLinearMeter ?? 0,
            instantaneousDeflectionPermanentLoad: defaultValues?.instantaneousDeflectionPermanentLoad ?? 0,
            instantaneousDeflectionImposedLoad: defaultValues?.instantaneousDeflectionImposedLoad ?? 0,
            instantaneousDeflectionTotalLoads: defaultValues?.instantaneousDeflectionTotalLoads ?? 0,
            deformabilityCheck: defaultValues?.deformabilityCheck ?? 0,
            combinationFactor: defaultValues?.combinationFactor ?? 0.3,
            finalDeflectionTotalLoads: defaultValues?.finalDeflectionTotalLoads ?? 0,
            finalCheckResult: defaultValues?.finalCheckResult ?? 0
        }
    });
    // Reset form when defaultValues change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (defaultValues) {
            form.reset({
                maximumBendingMoment: defaultValues.maximumBendingMoment ?? 0,
                maximumShearForce: defaultValues.maximumShearForce ?? 0,
                designBendingStress: defaultValues.designBendingStress ?? 0,
                designBendingStrength: defaultValues.designBendingStrength ?? 0,
                bendingCheck: defaultValues.bendingCheck ?? 0,
                designShearStress: defaultValues.designShearStress ?? 0,
                designShearStrength: defaultValues.designShearStrength ?? 0,
                shearCheck: defaultValues.shearCheck ?? 0,
                permanentLoadPerLinearMeter: defaultValues.permanentLoadPerLinearMeter ?? 0,
                imposedLoadPerLinearMeter: defaultValues.imposedLoadPerLinearMeter ?? 0,
                instantaneousDeflectionPermanentLoad: defaultValues.instantaneousDeflectionPermanentLoad ?? 0,
                instantaneousDeflectionImposedLoad: defaultValues.instantaneousDeflectionImposedLoad ?? 0,
                instantaneousDeflectionTotalLoads: defaultValues.instantaneousDeflectionTotalLoads ?? 0,
                deformabilityCheck: defaultValues.deformabilityCheck ?? 0,
                combinationFactor: defaultValues.combinationFactor ?? 0.3,
                finalDeflectionTotalLoads: defaultValues.finalDeflectionTotalLoads ?? 0,
                finalCheckResult: defaultValues.finalCheckResult ?? 0
            });
        }
    }, [
        defaultValues,
        form
    ]);
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"])(session.accessToken, {
        onSuccess: ()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].success(tAction('edit.success'));
            // Query invalidation is handled by the mutation hook itself
            onSave();
        },
        onError: (error)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$sonner$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toast"].error(tAction('edit.failure', {
                error: error.message
            }));
        }
    });
    const handleFormSubmit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((data)=>{
        // Send as preIntervationCheck to match the API structure
        mutate({
            projectId,
            moduleId,
            body: {
                preIntervationCheck: data
            }
        });
    }, [
        mutate,
        projectId,
        moduleId
    ]);
    // Watch form values for calculations
    const [maximumBendingMoment, maximumShearForce, permanentLoadPerLinearMeter, imposedLoadPerLinearMeter, combinationFactor] = form.watch([
        'maximumBendingMoment',
        'maximumShearForce',
        'permanentLoadPerLinearMeter',
        'imposedLoadPerLinearMeter',
        'combinationFactor'
    ]);
    // Helper function to determine check result
    const getCheckResult = (value, threshold, isGreaterThan = true)=>{
        if (isGreaterThan) {
            return value > threshold ? 'Non-satisfy' : 'Satisfy';
        }
        return value < threshold ? 'Non-satisfy' : 'Satisfy';
    };
    // Helper function to get badge variant
    const getBadgeVariant = (result)=>{
        return result === 'Satisfy' ? 'default' : 'destructive';
    };
    // Calculations based on form values and properties from other forms
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (maximumBendingMoment && geometryProperties?.sectionModulus) {
            const designBendingStress = maximumBendingMoment / (geometryProperties.sectionModulus * 1000);
            form.setValue('designBendingStress', designBendingStress);
        }
    }, [
        maximumBendingMoment,
        geometryProperties?.sectionModulus,
        form
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (geometryProperties?.designBendingStrength) {
            form.setValue('designBendingStrength', geometryProperties.designBendingStrength);
        }
    }, [
        geometryProperties?.designBendingStrength,
        form
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const designBendingStress = form.getValues('designBendingStress');
        const designBendingStrength = form.getValues('designBendingStrength');
        if (designBendingStress && designBendingStrength) {
            const bendingCheck = designBendingStress / designBendingStrength;
            form.setValue('bendingCheck', bendingCheck);
        }
    }, [
        form
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (maximumShearForce && geometryProperties?.beamSectionHeight && geometryProperties?.beamSectionWidth) {
            const designShearStress = 3 * 1000 * maximumShearForce / (2 * 1000 * geometryProperties.beamSectionHeight * 1000 * geometryProperties.beamSectionWidth);
            form.setValue('designShearStress', designShearStress);
        }
    }, [
        maximumShearForce,
        geometryProperties?.beamSectionHeight,
        geometryProperties?.beamSectionWidth,
        form
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (geometryProperties?.designShearStrength) {
            form.setValue('designShearStrength', geometryProperties.designShearStrength);
        }
    }, [
        geometryProperties?.designShearStrength,
        form
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const designShearStress = form.getValues('designShearStress');
        const designShearStrength = form.getValues('designShearStrength');
        if (designShearStress && designShearStrength) {
            const shearCheck = designShearStress / designShearStrength;
            form.setValue('shearCheck', shearCheck);
        }
    }, [
        form
    ]);
    // Deflection calculations
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (permanentLoadPerLinearMeter && geometryProperties?.beamSpan && geometryProperties?.elasticityInstantaneousModulus && geometryProperties?.inertiaMomentAboutY) {
            const instantaneousDeflectionPermanentLoad = 100 * (5 * permanentLoadPerLinearMeter * geometryProperties.beamSpan ** 4) / (384 * 1000 * geometryProperties.elasticityInstantaneousModulus * geometryProperties.inertiaMomentAboutY);
            form.setValue('instantaneousDeflectionPermanentLoad', instantaneousDeflectionPermanentLoad);
        }
    }, [
        permanentLoadPerLinearMeter,
        geometryProperties?.beamSpan,
        geometryProperties?.elasticityInstantaneousModulus,
        geometryProperties?.inertiaMomentAboutY,
        form
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (imposedLoadPerLinearMeter && geometryProperties?.beamSpan && geometryProperties?.elasticityInstantaneousModulus && geometryProperties?.inertiaMomentAboutY) {
            const instantaneousDeflectionImposedLoad = 100 * (5 * imposedLoadPerLinearMeter * geometryProperties.beamSpan ** 4) / (384 * 1000 * geometryProperties.elasticityInstantaneousModulus * geometryProperties.inertiaMomentAboutY);
            form.setValue('instantaneousDeflectionImposedLoad', instantaneousDeflectionImposedLoad);
        }
    }, [
        imposedLoadPerLinearMeter,
        geometryProperties?.beamSpan,
        geometryProperties?.elasticityInstantaneousModulus,
        geometryProperties?.inertiaMomentAboutY,
        form
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const instantaneousDeflectionPermanentLoad = form.getValues('instantaneousDeflectionPermanentLoad');
        const instantaneousDeflectionImposedLoad = form.getValues('instantaneousDeflectionImposedLoad');
        if (instantaneousDeflectionPermanentLoad && instantaneousDeflectionImposedLoad) {
            // Note: Adding 0 for the "peli field" as mentioned in requirements
            const instantaneousDeflectionTotalLoads = instantaneousDeflectionPermanentLoad + instantaneousDeflectionImposedLoad + initialDeformation;
            form.setValue('instantaneousDeflectionTotalLoads', instantaneousDeflectionTotalLoads);
        }
    }, [
        form,
        initialDeformation
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const instantaneousDeflectionTotalLoads = form.getValues('instantaneousDeflectionTotalLoads');
        if (instantaneousDeflectionTotalLoads && geometryProperties?.beamSpan) {
            const deformabilityCheck = 100 * geometryProperties.beamSpan / instantaneousDeflectionTotalLoads;
            form.setValue('deformabilityCheck', deformabilityCheck);
        }
    }, [
        geometryProperties?.beamSpan,
        form
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const instantaneousDeflectionPermanentLoad = form.getValues('instantaneousDeflectionPermanentLoad');
        const instantaneousDeflectionImposedLoad = form.getValues('instantaneousDeflectionImposedLoad');
        if (instantaneousDeflectionPermanentLoad && instantaneousDeflectionImposedLoad && combinationFactor && geometryProperties?.deformabilityFactor) {
            // Note: Adding 0 for the "peli field" as mentioned in requirements
            const finalDeflectionTotalLoads = instantaneousDeflectionPermanentLoad * (1 + geometryProperties.deformabilityFactor) + instantaneousDeflectionImposedLoad * (1 + combinationFactor * geometryProperties.deformabilityFactor) + initialDeformation;
            form.setValue('finalDeflectionTotalLoads', finalDeflectionTotalLoads);
        }
    }, [
        combinationFactor,
        geometryProperties?.deformabilityFactor,
        initialDeformation,
        form
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const finalDeflectionTotalLoads = form.getValues('finalDeflectionTotalLoads');
        if (finalDeflectionTotalLoads && geometryProperties?.beamSpan) {
            const finalCheckResult = 100 * geometryProperties.beamSpan / finalDeflectionTotalLoads;
            form.setValue('finalCheckResult', finalCheckResult);
        }
    }, [
        geometryProperties?.beamSpan,
        form
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Form"], {
        ...form,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
            className: "space-y-4 rounded-md border p-4",
            onSubmit: form.handleSubmit(handleFormSubmit),
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "maximumBendingMoment",
                    t: t,
                    required: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                    lineNumber: 366,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                    control: form.control,
                    name: "maximumShearForce",
                    t: t,
                    required: true
                }, void 0, false, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                    lineNumber: 372,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                            lineNumber: 381,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "designBendingStress",
                            t: t,
                            disabled: true,
                            decimalPlaces: 3
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                            lineNumber: 382,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "designBendingStrength",
                            t: t,
                            disabled: true,
                            decimalPlaces: 3
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                            lineNumber: 389,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-start gap-4 flex",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                                    control: form.control,
                                    name: "bendingCheck",
                                    t: t,
                                    disabled: true,
                                    fieldContainerClassName: "flex-1",
                                    decimalPlaces: 3
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                                    lineNumber: 397,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Badge"], {
                                    variant: getBadgeVariant(getCheckResult(form.watch('bendingCheck'), 1)),
                                    className: "mt-7",
                                    children: getCheckResult(form.watch('bendingCheck'), 1)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                                    lineNumber: 405,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                            lineNumber: 396,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                    lineNumber: 380,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                            lineNumber: 418,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "designShearStress",
                            t: t,
                            disabled: true,
                            decimalPlaces: 3
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                            lineNumber: 419,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "designShearStrength",
                            t: t,
                            disabled: true,
                            decimalPlaces: 3
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                            lineNumber: 426,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-start gap-4 flex",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                                    control: form.control,
                                    name: "shearCheck",
                                    t: t,
                                    disabled: true,
                                    fieldContainerClassName: "flex-1",
                                    decimalPlaces: 3
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                                    lineNumber: 434,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Badge"], {
                                    variant: getBadgeVariant(getCheckResult(form.watch('shearCheck'), 1)),
                                    className: "mt-7",
                                    children: getCheckResult(form.watch('shearCheck'), 1)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                                    lineNumber: 442,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                            lineNumber: 433,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                    lineNumber: 417,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                            lineNumber: 455,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "permanentLoadPerLinearMeter",
                            t: t,
                            required: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                            lineNumber: 456,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "imposedLoadPerLinearMeter",
                            t: t,
                            required: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                            lineNumber: 462,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                    lineNumber: 454,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "instantaneousDeflectionPermanentLoad",
                            t: t,
                            disabled: true,
                            decimalPlaces: 3
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                            lineNumber: 472,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "instantaneousDeflectionImposedLoad",
                            t: t,
                            disabled: true,
                            decimalPlaces: 3
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                            lineNumber: 479,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "instantaneousDeflectionTotalLoads",
                            t: t,
                            disabled: true,
                            decimalPlaces: 3
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                            lineNumber: 486,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-start gap-4 flex",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                                    control: form.control,
                                    name: "deformabilityCheck",
                                    t: t,
                                    disabled: true,
                                    fieldContainerClassName: "flex-1",
                                    decimalPlaces: 3
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                                    lineNumber: 494,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Badge"], {
                                    variant: getBadgeVariant(getCheckResult(form.watch('deformabilityCheck'), 300, false)),
                                    className: "mt-7",
                                    children: getCheckResult(form.watch('deformabilityCheck'), 300, false)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                                    lineNumber: 502,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                            lineNumber: 493,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                    lineNumber: 471,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "combinationFactor",
                            t: t,
                            required: false,
                            disabled: true
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                            lineNumber: 515,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                            control: form.control,
                            name: "finalDeflectionTotalLoads",
                            t: t,
                            disabled: true,
                            decimalPlaces: 3
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                            lineNumber: 522,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0)),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-start gap-4 flex",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                                    control: form.control,
                                    name: "finalCheckResult",
                                    t: t,
                                    disabled: true,
                                    fieldContainerClassName: "flex-1",
                                    decimalPlaces: 3
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                                    lineNumber: 530,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0)),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Badge"], {
                                    variant: getBadgeVariant(getCheckResult(form.watch('finalCheckResult'), 200, false)),
                                    className: "mt-7",
                                    children: getCheckResult(form.watch('finalCheckResult'), 200, false)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                                    lineNumber: 538,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                            lineNumber: 529,
                            columnNumber: 11
                        }, ("TURBOPACK compile-time value", void 0))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                    lineNumber: 514,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0)),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                    type: "submit",
                    className: "w-full sm:w-auto",
                    disabled: isPending,
                    children: [
                        isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                            className: "animate-spin"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                            lineNumber: 550,
                            columnNumber: 25
                        }, ("TURBOPACK compile-time value", void 0)),
                        tCommon('save')
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
                    lineNumber: 549,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
            lineNumber: 361,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx",
        lineNumber: 360,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
}),
"[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-geometry-form.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "WoodCompositeGeometryForm",
    ()=>WoodCompositeGeometryForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/select-form-input.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$products$2f$use$2d$products$2d$query$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/products/use-products-query.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-save-module-params-mutation.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-ssr] (ecmascript) <export * as z>");
;
;
;
;
;
;
;
;
;
;
;
;
;
const formSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    productId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].string().min(1, 'Product is required'),
    stripWidth: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0.1, 'Strip width must be greater than 0'),
    equivalentThickness: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0.001, 'Equivalent thickness must be greater than 0'),
    layersNumber: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(1, 'Layers number must be at least 1'),
    expositionType: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].enum([
        'INTERNAL',
        'EXTERNAL',
        'AGGRESSIVE'
    ]),
    environmentalConversionFactor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Environmental conversion factor must be positive')
});
const EXPOSITION_TYPE_OPTIONS = [
    {
        value: 'INTERNAL',
        label: 'Internal'
    },
    {
        value: 'EXTERNAL',
        label: 'External'
    },
    {
        value: 'AGGRESSIVE',
        label: 'Aggressive'
    }
];
const WoodCompositeGeometryForm = ({ session, projectId, moduleId, defaultValues, preInterventionData: _preInterventionData, materialProperties, geometryProperties, onSave, onGeometryChange, initialDeformation })=>{
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.wood.compositeGeometry');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const { data: productsData, isLoading: isLoadingProducts } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$products$2f$use$2d$products$2d$query$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useProductsQuery"])('wood', session.accessToken);
    const productOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        if (!productsData?.content) {
            return [];
        }
        return productsData.content.filter((product)=>product.categories.includes('wood')).map((product)=>({
                value: product.id,
                label: product.name
            }));
    }, [
        productsData
    ]);
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["zodResolver"])(formSchema),
        defaultValues: {
            productId: defaultValues?.productId || '',
            stripWidth: defaultValues?.stripWidth || 0,
            equivalentThickness: defaultValues?.equivalentThickness || 0,
            layersNumber: defaultValues?.layersNumber || 2.25,
            expositionType: defaultValues?.expositionType || 'INTERNAL',
            environmentalConversionFactor: defaultValues?.environmentalConversionFactor || 0.95
        }
    });
    // Reset form when defaultValues change (for saved data after refresh)
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (defaultValues) {
            form.reset({
                productId: defaultValues.productId ?? '',
                stripWidth: defaultValues.stripWidth ?? 0,
                equivalentThickness: defaultValues.equivalentThickness ?? 0,
                layersNumber: defaultValues.layersNumber ?? 2.25,
                expositionType: defaultValues.expositionType ?? 'INTERNAL',
                environmentalConversionFactor: defaultValues.environmentalConversionFactor ?? 0.95
            });
        }
    }, [
        defaultValues,
        form
    ]);
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"])(session.accessToken);
    const handleFormSubmit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((data)=>{
        // Save as part of postIntervationCheck structure with all inherited data
        mutate({
            projectId,
            moduleId,
            body: {
                postIntervationCheck: {
                    // Include inherited data from pre-intervention
                    initialDeformation: initialDeformation || 0,
                    materialProperties: materialProperties || null,
                    geometry: geometryProperties || null,
                    // Include the current form data
                    compositeGeometry: data
                }
            }
        });
        onSave?.();
    }, [
        mutate,
        projectId,
        moduleId,
        onSave,
        materialProperties,
        geometryProperties,
        initialDeformation
    ]);
    // Watch for exposition type changes to update environmental conversion factor
    const expositionType = form.watch('expositionType');
    const selectedProductId = form.watch('productId');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        let factor = 0.85 // default for external and aggressive
        ;
        if (expositionType === 'INTERNAL') {
            factor = 0.95;
        }
        form.setValue('environmentalConversionFactor', factor);
    }, [
        expositionType,
        form
    ]);
    // Update equivalent thickness when product changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (selectedProductId && productsData?.content) {
            const selectedProduct = productsData.content.find((p)=>p.id === selectedProductId);
            if (selectedProduct) {
                form.setValue('equivalentThickness', selectedProduct.thickness);
            }
        }
    }, [
        selectedProductId,
        productsData,
        form
    ]);
    // Watch specific form values and notify parent component of changes
    const stripWidth = form.watch('stripWidth');
    const layersNumber = form.watch('layersNumber');
    const equivalentThickness = form.watch('equivalentThickness');
    const environmentalConversionFactor = form.watch('environmentalConversionFactor');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Only notify parent if we have a valid product selection and the callback exists
        if (onGeometryChange && selectedProductId && stripWidth > 0) {
            const geometryData = {
                productId: selectedProductId,
                stripWidth,
                layersNumber,
                equivalentThickness,
                expositionType,
                environmentalConversionFactor
            };
            onGeometryChange(geometryData);
        }
    }, [
        selectedProductId,
        stripWidth,
        layersNumber,
        equivalentThickness,
        expositionType,
        environmentalConversionFactor,
        onGeometryChange
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col 2xl:flex-row justify-center gap-2",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Form"], {
            ...form,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                className: "space-y-4 rounded-md border p-4 grow",
                onSubmit: form.handleSubmit(handleFormSubmit),
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SelectFormInput"], {
                        control: form.control,
                        name: "productId",
                        options: productOptions,
                        t: t,
                        loading: isLoadingProducts,
                        required: true
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-geometry-form.tsx",
                        lineNumber: 205,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "stripWidth",
                        t: t,
                        required: true
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-geometry-form.tsx",
                        lineNumber: 214,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "equivalentThickness",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-geometry-form.tsx",
                        lineNumber: 221,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "layersNumber",
                        t: t,
                        required: true
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-geometry-form.tsx",
                        lineNumber: 229,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$select$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SelectFormInput"], {
                        control: form.control,
                        name: "expositionType",
                        options: EXPOSITION_TYPE_OPTIONS,
                        t: t,
                        required: true
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-geometry-form.tsx",
                        lineNumber: 236,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "environmentalConversionFactor",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-geometry-form.tsx",
                        lineNumber: 244,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                        type: "submit",
                        className: "w-full sm:w-auto",
                        disabled: isPending,
                        children: [
                            isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                                className: "animate-spin"
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-geometry-form.tsx",
                                lineNumber: 257,
                                columnNumber: 27
                            }, ("TURBOPACK compile-time value", void 0)),
                            tCommon('save')
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-geometry-form.tsx",
                        lineNumber: 252,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-geometry-form.tsx",
                lineNumber: 201,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        }, void 0, false, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-geometry-form.tsx",
            lineNumber: 200,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-geometry-form.tsx",
        lineNumber: 199,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
}),
"[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "WoodCompositePropertiesForm",
    ()=>WoodCompositePropertiesForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$products$2f$use$2d$products$2d$query$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/api/products/use-products-query.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-save-module-params-mutation.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-ssr] (ecmascript) <export * as z>");
;
;
;
;
;
;
;
;
;
;
;
;
const formSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    frpElasticityModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'FRP elasticity modulus must be positive'),
    frpDesignMaximumStrain: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'FRP design maximum strain must be positive'),
    frpCharacteristicStrain: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'FRP characteristic strain must be positive'),
    frpPartialFactorInUls: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'FRP partial factor in ULS must be positive'),
    frpMaximumStrainForDebonding: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'FRP maximum strain for debonding must be positive'),
    loadConditionFactor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Load condition factor must be positive'),
    frpPartialFactorInUlsForDebonding: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'FRP partial factor in ULS for debonding must be positive'),
    reinforcementToSectionWidthRatio: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Reinforcement to section width ratio must be positive'),
    reinforcementToSectionWidthRatioUsefull: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Reinforcement to section width ratio useful must be positive'),
    geometricCorrectionFactor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Geometric correction factor must be positive'),
    geometricCorrectionFactorUsefull: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Geometric correction factor useful must be positive'),
    experimentalCorrectionFactor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Experimental correction factor must be positive'),
    confidenceFactor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Confidence factor must be positive'),
    sectionModulus: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Section modulus must be positive'),
    momentOfInertiaAboutY: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Moment of inertia about Y must be positive')
});
const WoodCompositePropertiesForm = ({ session, projectId, moduleId, defaultValues, compositeGeometry, preInterventionData: _preInterventionData, materialProperties, geometryProperties, onSave, onPropertiesChange, initialDeformation })=>{
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.wood.compositeProperties');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const { data: productsData } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$api$2f$products$2f$use$2d$products$2d$query$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useProductsQuery"])('wood', session.accessToken);
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["zodResolver"])(formSchema),
        defaultValues: {
            frpElasticityModulus: defaultValues?.frpElasticityModulus || 0,
            frpDesignMaximumStrain: defaultValues?.frpDesignMaximumStrain || 0,
            frpCharacteristicStrain: defaultValues?.frpCharacteristicStrain || 0,
            frpPartialFactorInUls: defaultValues?.frpPartialFactorInUls || 1.1,
            frpMaximumStrainForDebonding: defaultValues?.frpMaximumStrainForDebonding || 0,
            loadConditionFactor: defaultValues?.loadConditionFactor || 1.25,
            frpPartialFactorInUlsForDebonding: defaultValues?.frpPartialFactorInUlsForDebonding || 1.2,
            reinforcementToSectionWidthRatio: defaultValues?.reinforcementToSectionWidthRatio || 0,
            reinforcementToSectionWidthRatioUsefull: defaultValues?.reinforcementToSectionWidthRatioUsefull || 0,
            geometricCorrectionFactor: defaultValues?.geometricCorrectionFactor || 0,
            geometricCorrectionFactorUsefull: defaultValues?.geometricCorrectionFactorUsefull || 0,
            experimentalCorrectionFactor: defaultValues?.experimentalCorrectionFactor || 0.1,
            confidenceFactor: defaultValues?.confidenceFactor || 1.35,
            sectionModulus: defaultValues?.sectionModulus || 0,
            momentOfInertiaAboutY: defaultValues?.momentOfInertiaAboutY || 0
        }
    });
    // Reset form when defaultValues change (for saved data after refresh)
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (defaultValues) {
            form.reset({
                frpElasticityModulus: defaultValues.frpElasticityModulus ?? 0,
                frpDesignMaximumStrain: defaultValues.frpDesignMaximumStrain ?? 0,
                frpCharacteristicStrain: defaultValues.frpCharacteristicStrain ?? 0,
                frpPartialFactorInUls: defaultValues.frpPartialFactorInUls ?? 1.1,
                frpMaximumStrainForDebonding: defaultValues.frpMaximumStrainForDebonding ?? 0,
                loadConditionFactor: defaultValues.loadConditionFactor ?? 1.25,
                frpPartialFactorInUlsForDebonding: defaultValues.frpPartialFactorInUlsForDebonding ?? 1.2,
                reinforcementToSectionWidthRatio: defaultValues.reinforcementToSectionWidthRatio ?? 0,
                reinforcementToSectionWidthRatioUsefull: defaultValues.reinforcementToSectionWidthRatioUsefull ?? 0,
                geometricCorrectionFactor: defaultValues.geometricCorrectionFactor ?? 0,
                geometricCorrectionFactorUsefull: defaultValues.geometricCorrectionFactorUsefull ?? 0,
                experimentalCorrectionFactor: defaultValues.experimentalCorrectionFactor ?? 0.1,
                confidenceFactor: defaultValues.confidenceFactor ?? 1.35,
                sectionModulus: defaultValues.sectionModulus ?? 0,
                momentOfInertiaAboutY: defaultValues.momentOfInertiaAboutY ?? 0
            });
        }
    }, [
        defaultValues,
        form
    ]);
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"])(session.accessToken);
    const handleFormSubmit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((data)=>{
        // Save as part of postIntervationCheck structure with all inherited data
        mutate({
            projectId,
            moduleId,
            body: {
                postIntervationCheck: {
                    // Include inherited data from pre-intervention
                    initialDeformation: initialDeformation || 0,
                    materialProperties: materialProperties || null,
                    geometry: geometryProperties || null,
                    // Include composite geometry if available
                    compositeGeometry: compositeGeometry || null,
                    // Include the current form data
                    compositeProperties: data
                }
            }
        });
        onSave?.();
    }, [
        mutate,
        projectId,
        moduleId,
        onSave,
        materialProperties,
        geometryProperties,
        compositeGeometry,
        initialDeformation
    ]);
    // Calculate derived values when dependencies change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!compositeGeometry || !materialProperties || !geometryProperties || !productsData?.content) {
            return;
        }
        const selectedProduct = productsData.content.find((p)=>p.id === compositeGeometry.productId);
        if (!selectedProduct) {
            return;
        }
        // Set FRP elasticity modulus from selected product
        form.setValue('frpElasticityModulus', selectedProduct.elasticModulus);
        // Set FRP characteristic strain from selected product
        form.setValue('frpCharacteristicStrain', selectedProduct.systemDeformation);
        // Calculate reinforcement to section width ratio
        // Formula: strip_width (mm) / beamSectionWidth (m) - convert stripWidth to meters
        const reinforcementRatio = compositeGeometry.stripWidth / 1000 / geometryProperties.beamSectionWidth;
        form.setValue('reinforcementToSectionWidthRatio', reinforcementRatio);
        // Calculate reinforcement to section width ratio useful
        // If ratio < 0.25, use 0.25; otherwise use the ratio (corrected logic)
        const reinforcementRatioUseful = reinforcementRatio < 0.25 ? 0.25 : reinforcementRatio;
        form.setValue('reinforcementToSectionWidthRatioUsefull', reinforcementRatioUseful);
        // Calculate geometric correction factor
        const geometricFactor = ((2 - reinforcementRatioUseful) / (1 + reinforcementRatioUseful)) ** 0.5;
        form.setValue('geometricCorrectionFactor', geometricFactor);
        // Calculate geometric correction factor useful
        // If factor > 1, use the factor; otherwise use 1 (as per specification)
        const geometricFactorUseful = geometricFactor > 1 ? geometricFactor : 1;
        form.setValue('geometricCorrectionFactorUsefull', geometricFactorUseful);
        // Calculate moment of inertia about Y
        const momentOfInertia = geometryProperties.beamSectionWidth * geometryProperties.beamSectionHeight ** 3 / 12 + compositeGeometry.layersNumber * compositeGeometry.equivalentThickness / 1000 * compositeGeometry.stripWidth / 1000 * selectedProduct.elasticModulus / materialProperties.meanElasticityModulus * geometryProperties.beamSectionHeight ** 2 / 2;
        form.setValue('momentOfInertiaAboutY', momentOfInertia);
        // Calculate section modulus
        const sectionMod = momentOfInertia / (geometryProperties.beamSectionHeight / 2);
        form.setValue('sectionModulus', sectionMod);
        // Calculate FRP maximum strain for debonding
        // Formula: load_condition_factor/(frp_elasticity_modulus*frp_partial_factor_in_uls_for_debonding)*√[(frp_elasticity_modulus*2*geometric_correction_factor_usefull*experimental_correction_factor)/(layers_number*equivalent_thickness*confidence_factor)*√(characteristicTensileStrength*characteristicCompressiveStrength)]
        const loadConditionFactor = form.getValues('loadConditionFactor');
        const frpPartialFactorInUlsForDebonding = form.getValues('frpPartialFactorInUlsForDebonding');
        const experimentalCorrectionFactor = form.getValues('experimentalCorrectionFactor');
        const confidenceFactor = form.getValues('confidenceFactor');
        const maxStrainDebonding = loadConditionFactor / (selectedProduct.elasticModulus * frpPartialFactorInUlsForDebonding) * Math.sqrt(selectedProduct.elasticModulus * 2 * geometricFactor * experimentalCorrectionFactor * Math.sqrt(materialProperties.characteristicTensileStrength * materialProperties.characteristicCompressiveStrength) / (compositeGeometry.layersNumber * compositeGeometry.equivalentThickness * confidenceFactor));
        form.setValue('frpMaximumStrainForDebonding', maxStrainDebonding);
        // STEP 9: Calculate frp_design_maximum_strain
        // Formula: MIN(environmental_conversion_factor * frp_characteristic_strain / frp_partial_factor_in_uls, frp_maximum_strain_for_debonding)
        // Get the required values
        const environmentalConversionFactor = compositeGeometry.environmentalConversionFactor;
        const frpCharacteristicStrain = selectedProduct.systemDeformation // This is frp_characteristic_strain
        ;
        const frpPartialFactorInUls = form.getValues('frpPartialFactorInUls');
        // Calculate the first part of MIN function
        const designStrainFirstPart = environmentalConversionFactor * frpCharacteristicStrain / frpPartialFactorInUls;
        // Calculate the MIN of both values
        const designMaxStrain = Math.min(designStrainFirstPart, maxStrainDebonding);
        form.setValue('frpDesignMaximumStrain', designMaxStrain);
        // Notify parent component of the calculated properties
        if (onPropertiesChange) {
            const currentProperties = {
                frpElasticityModulus: selectedProduct.elasticModulus,
                frpDesignMaximumStrain: designMaxStrain,
                frpCharacteristicStrain: frpCharacteristicStrain,
                frpPartialFactorInUls: form.getValues('frpPartialFactorInUls'),
                frpMaximumStrainForDebonding: maxStrainDebonding,
                loadConditionFactor: form.getValues('loadConditionFactor'),
                frpPartialFactorInUlsForDebonding: form.getValues('frpPartialFactorInUlsForDebonding'),
                reinforcementToSectionWidthRatio: reinforcementRatio,
                reinforcementToSectionWidthRatioUsefull: reinforcementRatioUseful,
                geometricCorrectionFactor: geometricFactor,
                geometricCorrectionFactorUsefull: geometricFactorUseful,
                experimentalCorrectionFactor: form.getValues('experimentalCorrectionFactor'),
                confidenceFactor: form.getValues('confidenceFactor'),
                sectionModulus: sectionMod,
                momentOfInertiaAboutY: momentOfInertia
            };
            onPropertiesChange(currentProperties);
        }
    }, [
        compositeGeometry,
        materialProperties,
        geometryProperties,
        productsData,
        form,
        onPropertiesChange
    ]);
    // Watch for changes in user input fields and recalculate dependent values
    const _watchedValues = form.watch([
        'frpPartialFactorInUls',
        'loadConditionFactor',
        'frpPartialFactorInUlsForDebonding',
        'experimentalCorrectionFactor',
        'confidenceFactor'
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!compositeGeometry || !materialProperties || !geometryProperties || !productsData?.content) {
            return;
        }
        const selectedProduct = productsData.content.find((p)=>p.id === compositeGeometry.productId);
        if (!selectedProduct) {
            return;
        }
        // Get current values
        const geometricFactor = form.getValues('geometricCorrectionFactor');
        const loadConditionFactor = form.getValues('loadConditionFactor');
        const frpPartialFactorInUlsForDebonding = form.getValues('frpPartialFactorInUlsForDebonding');
        const experimentalCorrectionFactor = form.getValues('experimentalCorrectionFactor');
        const confidenceFactor = form.getValues('confidenceFactor');
        const frpPartialFactorInUls = form.getValues('frpPartialFactorInUls');
        // Recalculate FRP maximum strain for debonding when user inputs change
        // Using the EXACT formula: (K / (E * gamma)) * Math.sqrt((E * 2 * kappa * eta * Math.sqrt(ft * fc)) / (n * t_eq * Cf))
        const maxStrainDebonding = loadConditionFactor / (selectedProduct.elasticModulus * frpPartialFactorInUlsForDebonding) * Math.sqrt(selectedProduct.elasticModulus * 2 * geometricFactor * experimentalCorrectionFactor * Math.sqrt(materialProperties.characteristicTensileStrength * materialProperties.characteristicCompressiveStrength) / (compositeGeometry.layersNumber * compositeGeometry.equivalentThickness * confidenceFactor));
        form.setValue('frpMaximumStrainForDebonding', maxStrainDebonding);
        // Recalculate FRP design maximum strain
        const recalcDesignStrainFirstPart = compositeGeometry.environmentalConversionFactor * selectedProduct.systemDeformation / frpPartialFactorInUls;
        const designMaxStrain = Math.min(recalcDesignStrainFirstPart, maxStrainDebonding);
        form.setValue('frpDesignMaximumStrain', designMaxStrain);
        // Notify parent component of the updated properties
        if (onPropertiesChange) {
            const currentProperties = {
                frpElasticityModulus: selectedProduct.elasticModulus,
                frpDesignMaximumStrain: designMaxStrain,
                frpCharacteristicStrain: selectedProduct.systemDeformation,
                frpPartialFactorInUls: frpPartialFactorInUls,
                frpMaximumStrainForDebonding: maxStrainDebonding,
                loadConditionFactor: loadConditionFactor,
                frpPartialFactorInUlsForDebonding: frpPartialFactorInUlsForDebonding,
                reinforcementToSectionWidthRatio: form.getValues('reinforcementToSectionWidthRatio'),
                reinforcementToSectionWidthRatioUsefull: form.getValues('reinforcementToSectionWidthRatioUsefull'),
                geometricCorrectionFactor: geometricFactor,
                geometricCorrectionFactorUsefull: form.getValues('geometricCorrectionFactorUsefull'),
                experimentalCorrectionFactor: experimentalCorrectionFactor,
                confidenceFactor: confidenceFactor,
                sectionModulus: form.getValues('sectionModulus'),
                momentOfInertiaAboutY: form.getValues('momentOfInertiaAboutY')
            };
            onPropertiesChange(currentProperties);
        }
    }, [
        compositeGeometry,
        materialProperties,
        geometryProperties,
        productsData,
        form,
        onPropertiesChange
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col 2xl:flex-row justify-center gap-2",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Form"], {
            ...form,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                className: "space-y-4 rounded-md border p-4 grow",
                onSubmit: form.handleSubmit(handleFormSubmit),
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "frpElasticityModulus",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form.tsx",
                        lineNumber: 453,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "frpDesignMaximumStrain",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form.tsx",
                        lineNumber: 461,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "frpCharacteristicStrain",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form.tsx",
                        lineNumber: 469,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "frpPartialFactorInUls",
                        t: t,
                        disabled: true
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form.tsx",
                        lineNumber: 477,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "frpMaximumStrainForDebonding",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form.tsx",
                        lineNumber: 484,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "loadConditionFactor",
                        t: t,
                        disabled: true
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form.tsx",
                        lineNumber: 492,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "frpPartialFactorInUlsForDebonding",
                        t: t,
                        disabled: true
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form.tsx",
                        lineNumber: 499,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "reinforcementToSectionWidthRatio",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form.tsx",
                        lineNumber: 506,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "reinforcementToSectionWidthRatioUsefull",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form.tsx",
                        lineNumber: 514,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "geometricCorrectionFactor",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form.tsx",
                        lineNumber: 522,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "geometricCorrectionFactorUsefull",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form.tsx",
                        lineNumber: 530,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "experimentalCorrectionFactor",
                        t: t,
                        disabled: true
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form.tsx",
                        lineNumber: 538,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "confidenceFactor",
                        t: t,
                        disabled: true
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form.tsx",
                        lineNumber: 545,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "sectionModulus",
                        t: t,
                        disabled: true,
                        decimalPlaces: 6
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form.tsx",
                        lineNumber: 552,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "momentOfInertiaAboutY",
                        t: t,
                        disabled: true,
                        decimalPlaces: 6
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form.tsx",
                        lineNumber: 560,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                        type: "submit",
                        className: "w-full sm:w-auto",
                        disabled: isPending,
                        children: [
                            isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                                className: "animate-spin"
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form.tsx",
                                lineNumber: 573,
                                columnNumber: 27
                            }, ("TURBOPACK compile-time value", void 0)),
                            tCommon('save')
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form.tsx",
                        lineNumber: 568,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form.tsx",
                lineNumber: 449,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        }, void 0, false, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form.tsx",
            lineNumber: 448,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form.tsx",
        lineNumber: 447,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
}),
"[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "WoodPostInterventionCheckForm",
    ()=>WoodPostInterventionCheckForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/form/number-form-input.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/badge.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/form.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/mutation/modules/use-save-module-params-mutation.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@hookform/resolvers/zod/dist/zod.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/loader-circle.js [app-ssr] (ecmascript) <export default as Loader2Icon>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hook-form/dist/index.esm.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__ = __turbopack_context__.i("[project]/node_modules/zod/v4/classic/external.js [app-ssr] (ecmascript) <export * as z>");
;
;
;
;
;
;
;
;
;
;
;
;
const formSchema = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].object({
    maximumBendingMoment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Maximum bending moment must be positive'),
    maximumShearForce: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Maximum shear force must be positive'),
    designBendingStress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Design bending stress must be positive'),
    designBendingStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Design bending strength must be positive'),
    designBendingCheck: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Design bending check must be positive'),
    designShearStress: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Design shear stress must be positive'),
    designShearStrength: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Design shear strength must be positive'),
    designShearCheck: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Design shear check must be positive'),
    permanentLoadPerLinearMeter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Permanent load per linear meter must be positive'),
    imposedLoadPerLinearMeter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Imposed load per linear meter must be positive'),
    instantaneousDeflectionPermanentLoad: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Instantaneous deflection permanent load must be positive'),
    instantaneousDeflectionImposedLoad: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Instantaneous deflection imposed load must be positive'),
    instantaneousDeflectionTotalLoads: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Instantaneous deflection total loads must be positive'),
    instantaneousDeflectionCheck: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Instantaneous deflection check must be positive'),
    combinationFactor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Combination factor must be positive'),
    finalDeflectionTotalLoads: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Final deflection total loads must be positive'),
    finalCheckResult: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zod$2f$v4$2f$classic$2f$external$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__$2a$__as__z$3e$__["z"].number().min(0, 'Final check result must be positive')
});
const WoodPostInterventionCheckForm = ({ session, projectId, moduleId, defaultValues, preInterventionData, materialProperties, geometryProperties, compositeGeometry, compositeProperties, onSave, initialDeformation })=>{
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.wood.resultOfPostIntervationCheck');
    const tCommon = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('actions.common');
    const form = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hook$2d$form$2f$dist$2f$index$2e$esm$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useForm"])({
        resolver: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$hookform$2f$resolvers$2f$zod$2f$dist$2f$zod$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["zodResolver"])(formSchema),
        defaultValues: {
            maximumBendingMoment: defaultValues?.maximumBendingMoment || preInterventionData?.maximumBendingMoment || 0,
            maximumShearForce: defaultValues?.maximumShearForce || preInterventionData?.maximumShearForce || 0,
            designBendingStress: defaultValues?.designBendingStress || 0,
            designBendingStrength: defaultValues?.designBendingStrength || 0,
            designBendingCheck: defaultValues?.designBendingCheck || 0,
            designShearStress: defaultValues?.designShearStress || 0,
            designShearStrength: defaultValues?.designShearStrength || 0,
            designShearCheck: defaultValues?.designShearCheck || 0,
            permanentLoadPerLinearMeter: defaultValues?.permanentLoadPerLinearMeter || preInterventionData?.permanentLoadPerLinearMeter || 0,
            imposedLoadPerLinearMeter: defaultValues?.imposedLoadPerLinearMeter || preInterventionData?.imposedLoadPerLinearMeter || 0,
            instantaneousDeflectionPermanentLoad: defaultValues?.instantaneousDeflectionPermanentLoad || 0,
            instantaneousDeflectionImposedLoad: defaultValues?.instantaneousDeflectionImposedLoad || 0,
            instantaneousDeflectionTotalLoads: defaultValues?.instantaneousDeflectionTotalLoads || 0,
            instantaneousDeflectionCheck: defaultValues?.instantaneousDeflectionCheck || 0,
            combinationFactor: defaultValues?.combinationFactor || preInterventionData?.combinationFactor || 0,
            finalDeflectionTotalLoads: defaultValues?.finalDeflectionTotalLoads || 0,
            finalCheckResult: defaultValues?.finalCheckResult || 0
        }
    });
    // Reset form when defaultValues change (for saved data after refresh)
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (defaultValues && typeof defaultValues === 'object' && Object.keys(defaultValues).length > 0) {
            // Create a complete form data object with all fields
            const formData = {
                maximumBendingMoment: defaultValues.maximumBendingMoment ?? preInterventionData?.maximumBendingMoment ?? 0,
                maximumShearForce: defaultValues.maximumShearForce ?? preInterventionData?.maximumShearForce ?? 0,
                designBendingStress: defaultValues.designBendingStress ?? 0,
                designBendingStrength: defaultValues.designBendingStrength ?? 0,
                designBendingCheck: defaultValues.designBendingCheck ?? 0,
                designShearStress: defaultValues.designShearStress ?? 0,
                designShearStrength: defaultValues.designShearStrength ?? 0,
                designShearCheck: defaultValues.designShearCheck ?? 0,
                permanentLoadPerLinearMeter: defaultValues.permanentLoadPerLinearMeter ?? preInterventionData?.permanentLoadPerLinearMeter ?? 0,
                imposedLoadPerLinearMeter: defaultValues.imposedLoadPerLinearMeter ?? preInterventionData?.imposedLoadPerLinearMeter ?? 0,
                instantaneousDeflectionPermanentLoad: defaultValues.instantaneousDeflectionPermanentLoad ?? 0,
                instantaneousDeflectionImposedLoad: defaultValues.instantaneousDeflectionImposedLoad ?? 0,
                instantaneousDeflectionTotalLoads: defaultValues.instantaneousDeflectionTotalLoads ?? 0,
                instantaneousDeflectionCheck: defaultValues.instantaneousDeflectionCheck ?? 0,
                combinationFactor: defaultValues.combinationFactor ?? preInterventionData?.combinationFactor ?? 0,
                finalDeflectionTotalLoads: defaultValues.finalDeflectionTotalLoads ?? 0,
                finalCheckResult: defaultValues.finalCheckResult ?? 0
            };
            form.reset(formData);
        } else {}
    }, [
        defaultValues,
        preInterventionData,
        form
    ]);
    const { mutate, isPending } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mutation$2f$modules$2f$use$2d$save$2d$module$2d$params$2d$mutation$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSaveModuleParamsMutation"])(session.accessToken);
    // Helper function to determine check result
    const getCheckResult = (value, threshold, checkType = 'lessThanOrEqual')=>{
        if (checkType === 'lessThanOrEqual') {
            // For bending and shear checks: <= threshold = "Satisfy", > threshold = "Non-satisfy"
            return value <= threshold ? 'Satisfy' : 'Non-satisfy';
        }
        // For deflection checks: >= threshold = "Satisfy", < threshold = "Non-satisfy"
        return value >= threshold ? 'Satisfy' : 'Non-satisfy';
    };
    // Helper function to get badge variant
    const getBadgeVariant = (result)=>{
        return result === 'Satisfy' ? 'default' : 'destructive';
    };
    const handleFormSubmit = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((data)=>{
        // Save as part of postIntervationCheck structure with all inherited data
        mutate({
            projectId,
            moduleId,
            body: {
                postIntervationCheck: {
                    // Include inherited data from pre-intervention
                    initialDeformation: initialDeformation || 0,
                    materialProperties: materialProperties || null,
                    geometry: geometryProperties || null,
                    // Include composite data from props
                    compositeGeometry: compositeGeometry || null,
                    compositeProperties: compositeProperties || null,
                    // Include the current form data (only 3 fields now)
                    resultOfPostIntervationCheck: data
                }
            }
        });
        onSave?.();
    }, [
        mutate,
        projectId,
        moduleId,
        onSave,
        materialProperties,
        geometryProperties,
        compositeGeometry,
        compositeProperties,
        initialDeformation
    ]);
    // Calculate derived values when dependencies change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Set values from pre-intervention data first (if available)
        if (preInterventionData) {
            const preIntervention = preInterventionData;
            // Set values from pre-intervention data (only if not already saved)
            if (!defaultValues?.maximumBendingMoment && preIntervention.maximumBendingMoment !== undefined) {
                form.setValue('maximumBendingMoment', preIntervention.maximumBendingMoment);
            }
            if (!defaultValues?.maximumShearForce && preIntervention.maximumShearForce !== undefined) {
                form.setValue('maximumShearForce', preIntervention.maximumShearForce);
            }
            if (!defaultValues?.permanentLoadPerLinearMeter && preIntervention.permanentLoadPerLinearMeter !== undefined) {
                form.setValue('permanentLoadPerLinearMeter', preIntervention.permanentLoadPerLinearMeter);
            }
            if (!defaultValues?.imposedLoadPerLinearMeter && preIntervention.imposedLoadPerLinearMeter !== undefined) {
                form.setValue('imposedLoadPerLinearMeter', preIntervention.imposedLoadPerLinearMeter);
            }
            if (!defaultValues?.combinationFactor && preIntervention.combinationFactor !== undefined) {
                form.setValue('combinationFactor', preIntervention.combinationFactor);
            }
        }
        // Calculate design_bending_stress if we have the required data
        if (preInterventionData) {
            const preIntervention = preInterventionData;
            const maximumBendingMoment = form.getValues('maximumBendingMoment') || preIntervention.maximumBendingMoment || 0;
            // Try to get sectionModulus from composite properties (preferred)
            if (compositeProperties?.sectionModulus && compositeProperties.sectionModulus > 0) {
                const designBendingStress = maximumBendingMoment / (compositeProperties.sectionModulus * 1000);
                form.setValue('designBendingStress', designBendingStress);
            } else if (geometryProperties?.sectionModulus && geometryProperties.sectionModulus > 0) {
                const designBendingStress = maximumBendingMoment / (geometryProperties.sectionModulus * 1000);
                form.setValue('designBendingStress', designBendingStress);
            } else {
                // Set to 0 if we can't calculate (composite properties not ready yet)
                form.setValue('designBendingStress', 0);
            }
        }
        // Calculate design_bending_strength = MIN(pre ma "designBendingStrength", frp_design_maximum_strain*frp_elasticity_modulus)
        if (materialProperties && compositeProperties) {
            // Get designBendingStrength from previous forms (1,2,3,4)
            const preDesignBendingStrength = geometryProperties.designBendingStrength || 0;
            // Get frp_design_maximum_strain and frp_elasticity_modulus from 5th section (Composite Properties)
            const frpDesignMaximumStrain = compositeProperties.frpDesignMaximumStrain || 0;
            const frpElasticityModulus = compositeProperties.frpElasticityModulus || 0;
            // Calculate the MIN of both values
            const designBendingStrength = Math.min(preDesignBendingStrength, frpDesignMaximumStrain * frpElasticityModulus);
            form.setValue('designBendingStrength', designBendingStrength);
            // Calculate designBendingCheck = design_bending_stress / design_bending_strength
            const designBendingStress = form.getValues('designBendingStress') || 0;
            const designBendingCheck = designBendingStrength > 0 ? designBendingStress / designBendingStrength : 0;
            form.setValue('designBendingCheck', designBendingCheck);
        } else {
            // Set to 0 if we don't have the required data
            form.setValue('designBendingStrength', 0);
            form.setValue('designBendingCheck', 0);
        }
        // Calculate shear stress and strength
        if (geometryProperties) {
            const maximumShearForce = form.getValues('maximumShearForce') || preInterventionData?.maximumShearForce || 0;
            // Calculate design_shear_stress = 3*1000*maximum_shear_force/(2*1000*beamSectionHeight*1000*beamSectionWidth)
            const beamSectionHeight = geometryProperties.beamSectionHeight || 0;
            const beamSectionWidth = geometryProperties.beamSectionWidth || 0;
            if (beamSectionHeight > 0 && beamSectionWidth > 0) {
                const designShearStress = 3 * 1000 * maximumShearForce / (2 * 1000 * beamSectionHeight * 1000 * beamSectionWidth);
                form.setValue('designShearStress', designShearStress);
                // design_shear_strength = Pre ma "designShearStrength"
                const designShearStrength = geometryProperties.designShearStrength || 0;
                form.setValue('designShearStrength', designShearStrength);
                // Calculate designShearCheck = design_shear_stress / design_shear_strength
                const designShearCheck = designShearStrength > 0 ? designShearStress / designShearStrength : 0;
                form.setValue('designShearCheck', designShearCheck);
            } else {
                form.setValue('designShearStress', 0);
                form.setValue('designShearStrength', 0);
                form.setValue('designShearCheck', 0);
            }
        } else {
            form.setValue('designShearStress', 0);
            form.setValue('designShearStrength', 0);
            form.setValue('designShearCheck', 0);
        }
        // Calculate deflection values
        if (geometryProperties && compositeProperties) {
            const permanentLoadPerLinearMeter = form.getValues('permanentLoadPerLinearMeter') || preInterventionData?.permanentLoadPerLinearMeter || 0;
            const beamSpan = geometryProperties.beamSpan || 0;
            const elasticityInstantaneousModulus = geometryProperties.elasticityInstantaneousModulus || 0;
            // Get moment of inertia from composite properties
            const momentOfInertiaAboutY = compositeProperties.momentOfInertiaAboutY || 0;
            if (beamSpan > 0 && elasticityInstantaneousModulus > 0 && momentOfInertiaAboutY > 0) {
                // instantaneous_deflection_permanent_load = 100*(5*permanent_load_per_linear_meter*beamSpan^4)/(384*1000*designShearStrength*moment_of_inertia_about_y)
                const instantaneousDeflectionPermanentLoad = 100 * (5 * permanentLoadPerLinearMeter * beamSpan ** 4 / (384 * 1000 * elasticityInstantaneousModulus * momentOfInertiaAboutY));
                form.setValue('instantaneousDeflectionPermanentLoad', instantaneousDeflectionPermanentLoad);
                // instantaneous_deflection_imposed_load = 100*(5*imposed_load_per_linear_meter*beamSpan^4)/(384*1000*designShearStrength*moment_of_inertia_about_y)
                const imposedLoadPerLinearMeter = form.getValues('imposedLoadPerLinearMeter') || preInterventionData?.imposedLoadPerLinearMeter || 0;
                const instantaneousDeflectionImposedLoad = 100 * (5 * imposedLoadPerLinearMeter * beamSpan ** 4) / (384 * 1000 * elasticityInstantaneousModulus * momentOfInertiaAboutY);
                form.setValue('instantaneousDeflectionImposedLoad', instantaneousDeflectionImposedLoad);
                // instantaneous_deflection_total_loads = instantaneous_deflection_permanent_load + instantaneous_deflection_imposed_load + initialDeformation
                const finalInitialDeformation = initialDeformation || 0;
                const instantaneousDeflectionTotalLoads = instantaneousDeflectionPermanentLoad + instantaneousDeflectionImposedLoad + finalInitialDeformation;
                form.setValue('instantaneousDeflectionTotalLoads', instantaneousDeflectionTotalLoads);
                // instantaneousDeflectionCheck = 100*beamSpan / instantaneous_deflection_total_loads (< 300 = "Non-satisfy", >= 300 = "Satisfy")
                const instantaneousDeflectionCheck = instantaneousDeflectionTotalLoads > 0 ? 100 * beamSpan / instantaneousDeflectionTotalLoads : 0;
                form.setValue('instantaneousDeflectionCheck', instantaneousDeflectionCheck);
                // final_deflection_total_loads = instantaneous_deflection_permanent_load*(1+deformabilityFactor)+instantaneous_deflection_imposed_load*(1+combination_factor*deformabilityFactor)+initialDeformation
                const deformabilityFactor = geometryProperties.deformabilityFactor || 0;
                const combinationFactor = form.getValues('combinationFactor') || preInterventionData?.combinationFactor || 0;
                const finalDeflectionTotalLoads = instantaneousDeflectionPermanentLoad * (1 + deformabilityFactor) + instantaneousDeflectionImposedLoad * (1 + combinationFactor * deformabilityFactor) + finalInitialDeformation;
                form.setValue('finalDeflectionTotalLoads', finalDeflectionTotalLoads);
                // finalCheckResult = 100*beamSpan/final_deflection_total_loads (>= 200 = "Satisfy", < 200 = "Non-Satisfy")
                const finalCheckResult = finalDeflectionTotalLoads > 0 ? 100 * beamSpan / finalDeflectionTotalLoads : 0;
                form.setValue('finalCheckResult', finalCheckResult);
            } else {
                form.setValue('instantaneousDeflectionPermanentLoad', 0);
                form.setValue('instantaneousDeflectionImposedLoad', 0);
                form.setValue('instantaneousDeflectionTotalLoads', 0);
                form.setValue('instantaneousDeflectionCheck', 0);
                form.setValue('finalDeflectionTotalLoads', 0);
                form.setValue('finalCheckResult', 0);
            }
        } else {
            form.setValue('instantaneousDeflectionPermanentLoad', 0);
            form.setValue('instantaneousDeflectionImposedLoad', 0);
            form.setValue('instantaneousDeflectionTotalLoads', 0);
            form.setValue('instantaneousDeflectionCheck', 0);
            form.setValue('finalDeflectionTotalLoads', 0);
            form.setValue('finalCheckResult', 0);
        }
    }, [
        preInterventionData,
        compositeProperties,
        geometryProperties,
        materialProperties,
        defaultValues,
        form,
        initialDeformation
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex flex-col 2xl:flex-row justify-center gap-2",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Form"], {
            ...form,
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("form", {
                className: "space-y-4 rounded-md border p-4 grow",
                onSubmit: form.handleSubmit(handleFormSubmit),
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "maximumBendingMoment",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                        lineNumber: 510,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "maximumShearForce",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                        lineNumber: 519,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "designBendingStress",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                        lineNumber: 528,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "designBendingStrength",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                        lineNumber: 537,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-start gap-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                                control: form.control,
                                name: "designBendingCheck",
                                t: t,
                                disabled: true,
                                fieldContainerClassName: "flex-1",
                                decimalPlaces: 3
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                                lineNumber: 547,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Badge"], {
                                variant: getBadgeVariant(getCheckResult(form.watch('designBendingCheck'), 1)),
                                className: "mt-7",
                                children: getCheckResult(form.watch('designBendingCheck'), 1)
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                                lineNumber: 555,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                        lineNumber: 546,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "designShearStress",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                        lineNumber: 566,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "designShearStrength",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                        lineNumber: 575,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-start gap-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                                control: form.control,
                                name: "designShearCheck",
                                t: t,
                                disabled: true,
                                fieldContainerClassName: "flex-1",
                                decimalPlaces: 3
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                                lineNumber: 585,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Badge"], {
                                variant: getBadgeVariant(getCheckResult(form.watch('designShearCheck'), 1)),
                                className: "mt-7",
                                children: getCheckResult(form.watch('designShearCheck'), 1)
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                                lineNumber: 593,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                        lineNumber: 584,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "permanentLoadPerLinearMeter",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                        lineNumber: 604,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "imposedLoadPerLinearMeter",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                        lineNumber: 613,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "instantaneousDeflectionPermanentLoad",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                        lineNumber: 622,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "instantaneousDeflectionImposedLoad",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                        lineNumber: 631,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "instantaneousDeflectionTotalLoads",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                        lineNumber: 640,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-start gap-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                                control: form.control,
                                name: "instantaneousDeflectionCheck",
                                t: t,
                                disabled: true,
                                fieldContainerClassName: "flex-1",
                                decimalPlaces: 3
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                                lineNumber: 650,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Badge"], {
                                variant: getBadgeVariant(getCheckResult(form.watch('instantaneousDeflectionCheck'), 300, 'greaterThanOrEqual')),
                                className: "mt-7",
                                children: getCheckResult(form.watch('instantaneousDeflectionCheck'), 300, 'greaterThanOrEqual')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                                lineNumber: 658,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                        lineNumber: 649,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "combinationFactor",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                        lineNumber: 677,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                        control: form.control,
                        name: "finalDeflectionTotalLoads",
                        t: t,
                        disabled: true,
                        decimalPlaces: 3
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                        lineNumber: 686,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-start gap-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$form$2f$number$2d$form$2d$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["NumberFormInput"], {
                                control: form.control,
                                name: "finalCheckResult",
                                t: t,
                                disabled: true,
                                fieldContainerClassName: "flex-1",
                                decimalPlaces: 3
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                                lineNumber: 696,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Badge"], {
                                variant: getBadgeVariant(getCheckResult(form.watch('finalCheckResult'), 200, 'greaterThanOrEqual')),
                                className: "mt-7",
                                children: getCheckResult(form.watch('finalCheckResult'), 200, 'greaterThanOrEqual')
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                                lineNumber: 704,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                        lineNumber: 695,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                        type: "submit",
                        className: "w-full sm:w-auto",
                        disabled: isPending,
                        children: [
                            isPending && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$loader$2d$circle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Loader2Icon$3e$__["Loader2Icon"], {
                                className: "animate-spin"
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                                lineNumber: 727,
                                columnNumber: 27
                            }, ("TURBOPACK compile-time value", void 0)),
                            tCommon('save')
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                        lineNumber: 722,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
                lineNumber: 505,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        }, void 0, false, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
            lineNumber: 504,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx",
        lineNumber: 503,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
}),
"[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "WoodPostInterventionForm",
    ()=>WoodPostInterventionForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$wood$2f$sections$2f$wood$2d$beam$2d$geometry$2d$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$wood$2f$sections$2f$wood$2d$composite$2d$geometry$2d$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-geometry-form.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$wood$2f$sections$2f$wood$2d$composite$2d$properties$2d$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$wood$2f$sections$2f$wood$2d$general$2d$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-general-form.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$wood$2f$sections$2f$wood$2d$geometry$2d$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-geometry-form.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$wood$2f$sections$2f$wood$2d$post$2d$intervention$2d$check$2d$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/accordion.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/separator.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
const WoodPostInterventionForm = ({ session, projectId, moduleId, module })=>{
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.wood');
    const { params } = module;
    const [refreshTrigger, setRefreshTrigger] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0);
    const [currentCompositeGeometry, setCurrentCompositeGeometry] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(params?.postIntervationCheck?.compositeGeometry || params?.compositeGeometry || null);
    const [currentCompositeProperties, setCurrentCompositeProperties] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(params?.postIntervationCheck?.compositeProperties || params?.compositeProperties || null);
    // Callback to handle composite geometry updates with change detection
    const handleCompositeGeometryChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((newGeometry)=>{
        // Only update if the geometry data has actually changed
        setCurrentCompositeGeometry((prev)=>{
            if (!prev || prev.productId !== newGeometry.productId || prev.stripWidth !== newGeometry.stripWidth || prev.layersNumber !== newGeometry.layersNumber || prev.equivalentThickness !== newGeometry.equivalentThickness || prev.expositionType !== newGeometry.expositionType || prev.environmentalConversionFactor !== newGeometry.environmentalConversionFactor) {
                return newGeometry;
            }
            return prev;
        });
    }, []);
    // Callback to handle composite properties updates with change detection
    const handleCompositePropertiesChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((newProperties)=>{
        // Only update if the properties data has actually changed
        setCurrentCompositeProperties((prev)=>{
            if (!prev || prev.sectionModulus !== newProperties.sectionModulus || prev.momentOfInertiaAboutY !== newProperties.momentOfInertiaAboutY || prev.frpElasticityModulus !== newProperties.frpElasticityModulus || prev.frpDesignMaximumStrain !== newProperties.frpDesignMaximumStrain) {
                return newProperties;
            }
            return prev;
        });
    }, []);
    // Create a dummy onSave function for read-only forms
    const dummyOnSave = ()=>{};
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Accordion"], {
            type: "single",
            collapsible: true,
            className: "w-full",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AccordionItem"], {
                value: "post-intervention",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                        className: "text-lg font-normal",
                        children: t('postForm.Post-Intervention Analysis')
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                        lineNumber: 98,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AccordionContent"], {
                        className: "space-y-6 p-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-md font-semibold mb-4",
                                        children: t('postForm.Initial Deformation - Read Only')
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                                        lineNumber: 104,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "opacity-60 [&_button[type='submit']]:hidden [&_input]:pointer-events-none [&_input]:cursor-not-allowed [&_[data-slot='select-trigger']]:pointer-events-auto [&_[data-slot='select-trigger']]:opacity-100 [&_[data-slot='select-trigger']]:cursor-pointer",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$wood$2f$sections$2f$wood$2d$general$2d$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WoodGeneralForm"], {
                                            session: session,
                                            projectId: projectId,
                                            moduleId: moduleId,
                                            defaultValues: {
                                                initialDeformation: params?.initialDeformation || 0
                                            },
                                            onSave: dummyOnSave
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                                            lineNumber: 108,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                                        lineNumber: 107,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                                lineNumber: 103,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                                lineNumber: 120,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-md font-semibold mb-4",
                                        children: t('postForm.Material Properties - Read Only')
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                                        lineNumber: 124,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "opacity-60 [&_button[type='submit']]:hidden [&_input]:pointer-events-none [&_input]:cursor-not-allowed [&_[data-slot='select-trigger']]:pointer-events-auto [&_[data-slot='select-trigger']]:opacity-100 [&_[data-slot='select-trigger']]:cursor-pointer",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$wood$2f$sections$2f$wood$2d$geometry$2d$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WoodGeometryForm"], {
                                            session: session,
                                            projectId: projectId,
                                            moduleId: moduleId,
                                            defaultValues: params?.materialProperties,
                                            woodName: params?.materialProperties?.woodName,
                                            onSave: dummyOnSave
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                                            lineNumber: 128,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                                        lineNumber: 127,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                                lineNumber: 123,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                                lineNumber: 139,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-md font-semibold mb-4",
                                        children: t('postForm.Geometry - Read Only')
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                                        lineNumber: 143,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "opacity-60 [&_button[type='submit']]:hidden [&_input]:pointer-events-none [&_input]:cursor-not-allowed [&_[data-slot='select-trigger']]:pointer-events-auto [&_[data-slot='select-trigger']]:opacity-100 [&_[data-slot='select-trigger']]:cursor-pointer",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$wood$2f$sections$2f$wood$2d$beam$2d$geometry$2d$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WoodBeamGeometryForm"], {
                                            session: session,
                                            projectId: projectId,
                                            moduleId: moduleId,
                                            defaultValues: params?.geometry,
                                            materialProperties: params?.materialProperties,
                                            onSave: dummyOnSave
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                                            lineNumber: 147,
                                            columnNumber: 17
                                        }, ("TURBOPACK compile-time value", void 0))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                                        lineNumber: 146,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                                lineNumber: 142,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                                lineNumber: 158,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-md font-semibold mb-4",
                                        children: t('compositeGeometry.title')
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                                        lineNumber: 162,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$wood$2f$sections$2f$wood$2d$composite$2d$geometry$2d$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WoodCompositeGeometryForm"], {
                                        session: session,
                                        projectId: projectId,
                                        moduleId: moduleId,
                                        defaultValues: params?.postIntervationCheck?.compositeGeometry && typeof params.postIntervationCheck.compositeGeometry === 'object' ? params.postIntervationCheck.compositeGeometry : params?.compositeGeometry && typeof params.compositeGeometry === 'object' ? params.compositeGeometry : undefined,
                                        preInterventionData: params?.preIntervationCheck,
                                        materialProperties: params?.materialProperties,
                                        geometryProperties: params?.geometry,
                                        onSave: ()=>setRefreshTrigger((prev)=>prev + 1),
                                        onGeometryChange: handleCompositeGeometryChange,
                                        initialDeformation: params?.initialDeformation
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                                        lineNumber: 165,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                                lineNumber: 161,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                                lineNumber: 188,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-md font-semibold mb-4",
                                        children: t('compositeProperties.title')
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                                        lineNumber: 192,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$wood$2f$sections$2f$wood$2d$composite$2d$properties$2d$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WoodCompositePropertiesForm"], {
                                        session: session,
                                        projectId: projectId,
                                        moduleId: moduleId,
                                        defaultValues: params?.postIntervationCheck?.compositeProperties && typeof params.postIntervationCheck.compositeProperties === 'object' ? params.postIntervationCheck.compositeProperties : params?.compositeProperties && typeof params.compositeProperties === 'object' ? params.compositeProperties : undefined,
                                        compositeGeometry: currentCompositeGeometry,
                                        preInterventionData: params?.preIntervationCheck,
                                        materialProperties: params?.materialProperties,
                                        geometryProperties: params?.geometry,
                                        onSave: ()=>setRefreshTrigger((prev)=>prev + 1),
                                        onPropertiesChange: handleCompositePropertiesChange,
                                        initialDeformation: params?.initialDeformation
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                                        lineNumber: 195,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                                lineNumber: 191,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                                lineNumber: 219,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-md font-semibold mb-4",
                                        children: t('resultOfPostIntervationCheck.title')
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                                        lineNumber: 223,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0)),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$wood$2f$sections$2f$wood$2d$post$2d$intervention$2d$check$2d$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WoodPostInterventionCheckForm"], {
                                        session: session,
                                        projectId: projectId,
                                        moduleId: moduleId,
                                        defaultValues: params?.postIntervationCheck?.resultOfPostIntervationCheck && typeof params.postIntervationCheck.resultOfPostIntervationCheck === 'object' ? params.postIntervationCheck.resultOfPostIntervationCheck : params?.resultOfPostIntervationCheck && typeof params.resultOfPostIntervationCheck === 'object' ? params.resultOfPostIntervationCheck : undefined,
                                        preInterventionData: params?.preIntervationCheck,
                                        materialProperties: params?.materialProperties,
                                        geometryProperties: params?.geometry,
                                        compositeGeometry: currentCompositeGeometry,
                                        compositeProperties: currentCompositeProperties,
                                        onSave: ()=>setRefreshTrigger((prev)=>prev + 1),
                                        initialDeformation: params?.initialDeformation
                                    }, `post-intervention-${refreshTrigger}-${currentCompositeProperties?.sectionModulus || 0}`, false, {
                                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                                        lineNumber: 226,
                                        columnNumber: 15
                                    }, ("TURBOPACK compile-time value", void 0))
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                                lineNumber: 222,
                                columnNumber: 13
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                        lineNumber: 101,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
                lineNumber: 97,
                columnNumber: 9
            }, ("TURBOPACK compile-time value", void 0))
        }, void 0, false, {
            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
            lineNumber: 96,
            columnNumber: 7
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx",
        lineNumber: 95,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
}),
"[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "WoodParamsForm",
    ()=>WoodParamsForm
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$wood$2f$sections$2f$wood$2d$beam$2d$geometry$2d$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$wood$2f$sections$2f$wood$2d$general$2d$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-general-form.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$wood$2f$sections$2f$wood$2d$geometry$2d$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-geometry-form.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$wood$2f$sections$2f$wood$2d$pre$2d$intervention$2d$check$2d$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$wood$2f$wood$2d$post$2d$intervention$2d$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/accordion.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/separator.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$wood$2d$form$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/types/schemas/wood-form.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/next-intl/dist/esm/development/react-client/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$module$2d$report$2d$generation$2d$section$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/atlas/module-detail/module-report-generation-section.tsx [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
const WoodParamsForm = ({ session, projectId, moduleId, module })=>{
    const { params } = module;
    const [openItems, setOpenItems] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([
        '0'
    ]);
    const t = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2d$intl$2f$dist$2f$esm$2f$development$2f$react$2d$client$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["useTranslations"])('forms.project-params.wood');
    const handleItemSaved = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((id)=>{
        const nextId = String(Number(id) + 1);
        setOpenItems((old)=>{
            const temp = old.filter((v)=>v !== id);
            return old.includes(nextId) ? temp : [
                ...temp,
                nextId
            ];
        });
    }, []);
    const { success } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$wood$2d$form$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["woodParamsCheckSchema"].safeParse(params), [
        params
    ]);
    const { success: enableReport } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$types$2f$schemas$2f$wood$2d$form$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["woodParamsCheckSchema"].safeParse(module), [
        module
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Accordion"], {
                type: "multiple",
                value: openItems,
                onValueChange: setOpenItems,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AccordionItem"], {
                        value: "0",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-lg font-medium",
                                    children: t('general.title')
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx",
                                    lineNumber: 69,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx",
                                lineNumber: 68,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AccordionContent"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$wood$2f$sections$2f$wood$2d$general$2d$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WoodGeneralForm"], {
                                    session: session,
                                    projectId: projectId,
                                    moduleId: moduleId,
                                    defaultValues: {
                                        initialDeformation: params?.initialDeformation
                                    },
                                    onSave: ()=>handleItemSaved('0')
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx",
                                    lineNumber: 72,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx",
                                lineNumber: 71,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx",
                        lineNumber: 67,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AccordionItem"], {
                        value: "1",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-lg font-medium",
                                    children: t('materialProperties.title')
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx",
                                    lineNumber: 85,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx",
                                lineNumber: 84,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AccordionContent"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$wood$2f$sections$2f$wood$2d$geometry$2d$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WoodGeometryForm"], {
                                    session: session,
                                    projectId: projectId,
                                    moduleId: moduleId,
                                    defaultValues: // Wood material properties are stored in materialProperties field
                                    // Check if params.materialProperties has wood material properties structure
                                    params?.materialProperties && typeof params.materialProperties === 'object' && 'category' in params.materialProperties ? params.materialProperties : undefined // No wood material properties saved yet, start with empty form
                                    ,
                                    onSave: ()=>handleItemSaved('1')
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx",
                                    lineNumber: 90,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx",
                                lineNumber: 89,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx",
                        lineNumber: 83,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AccordionItem"], {
                        value: "2",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-lg font-medium",
                                    children: t('geometry.title')
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx",
                                    lineNumber: 109,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx",
                                lineNumber: 108,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AccordionContent"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$wood$2f$sections$2f$wood$2d$beam$2d$geometry$2d$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WoodBeamGeometryForm"], {
                                    session: session,
                                    projectId: projectId,
                                    moduleId: moduleId,
                                    defaultValues: // Wood geometry properties are stored in geometry field
                                    params?.geometry && typeof params.geometry === 'object' && 'beamSectionWidth' in params.geometry ? params.geometry : undefined // No wood geometry properties saved yet, start with empty form
                                    ,
                                    materialProperties: // Pass material properties for calculations
                                    params?.materialProperties && typeof params.materialProperties === 'object' && 'category' in params.materialProperties ? params.materialProperties : undefined // No material properties available yet
                                    ,
                                    onSave: ()=>handleItemSaved('2')
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx",
                                    lineNumber: 112,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx",
                                lineNumber: 111,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx",
                        lineNumber: 107,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AccordionItem"], {
                        value: "3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AccordionTrigger"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-lg font-medium",
                                    children: t('preIntervationCheck.title')
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx",
                                    lineNumber: 138,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx",
                                lineNumber: 137,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0)),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$accordion$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AccordionContent"], {
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$wood$2f$sections$2f$wood$2d$pre$2d$intervention$2d$check$2d$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WoodPreInterventionCheckForm"], {
                                    session: session,
                                    projectId: projectId,
                                    moduleId: moduleId,
                                    initialDeformation: params?.initialDeformation,
                                    defaultValues: // Wood pre-intervention check properties are stored in preIntervationCheck field
                                    params?.preIntervationCheck && typeof params.preIntervationCheck === 'object' ? params.preIntervationCheck : undefined // No pre-intervention check properties saved yet, start with empty form
                                    ,
                                    geometryProperties: // Pass geometry properties for calculations
                                    params?.geometry && typeof params.geometry === 'object' && 'beamSectionWidth' in params.geometry ? params.geometry : undefined // No geometry properties available yet
                                    ,
                                    onSave: ()=>handleItemSaved('3')
                                }, void 0, false, {
                                    fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx",
                                    lineNumber: 143,
                                    columnNumber: 13
                                }, ("TURBOPACK compile-time value", void 0))
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx",
                                lineNumber: 142,
                                columnNumber: 11
                            }, ("TURBOPACK compile-time value", void 0))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx",
                        lineNumber: 136,
                        columnNumber: 9
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx",
                lineNumber: 62,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            success && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$separator$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Separator"], {}, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx",
                        lineNumber: 170,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$params$2d$forms$2f$wood$2f$wood$2d$post$2d$intervention$2d$form$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WoodPostInterventionForm"], {
                        module: module,
                        session: session,
                        projectId: projectId,
                        moduleId: moduleId
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx",
                        lineNumber: 171,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0)),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "pt-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$atlas$2f$module$2d$detail$2f$module$2d$report$2d$generation$2d$section$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ModuleReportGenerationSection"], {
                            moduleId: moduleId,
                            enabled: enableReport,
                            projectId: projectId,
                            session: session
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx",
                            lineNumber: 178,
                            columnNumber: 23
                        }, ("TURBOPACK compile-time value", void 0))
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx",
                        lineNumber: 177,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                ]
            }, void 0, true)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx",
        lineNumber: 61,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
}),
];

//# sourceMappingURL=src_components_common_atlas_module-detail_params-forms_wood_8511bd71._.js.map