{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport { woodGeometryPropertiesSchema } from '@atlas/lib/api/modules/schemas/wood-params'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\n\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm, useWatch } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\nimport { z } from 'zod'\r\nconst formSchema = woodGeometryPropertiesSchema.extend({\r\n  id: z.string().optional(),\r\n})\r\n\r\ntype FormSchema = z.infer<typeof formSchema>\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FormSchema>\r\n  materialProperties?: {\r\n    characteristicBendingStrength?: number\r\n    characteristicShearStrength?: number\r\n    meanElasticityModulus?: number\r\n    partialMaterialFactor?: number\r\n  }\r\n  onSave: () => void\r\n}\r\n\r\nconst SERVICE_CLASS_OPTIONS = [\r\n  { value: 'SERVICE_CLASS_1', label: 'Classe di servizio 1' },\r\n  { value: 'SERVICE_CLASS_2', label: 'Classe di servizio 2' },\r\n  { value: 'SERVICE_CLASS_3', label: 'Classe di servizio 3' },\r\n]\r\n\r\nconst LOAD_DURATION_OPTIONS = [\r\n  { value: 'PERMANENTE', label: 'Permanente' },\r\n  { value: 'LUNGA', label: 'Lunga' },\r\n  { value: 'MEDIA', label: 'Media' },\r\n  { value: 'BREVE', label: 'Breve' },\r\n  { value: 'INSTANTANEA', label: 'Instantanea' },\r\n]\r\n\r\nconst KMOD_TABLE = {\r\n  SERVICE_CLASS_1: {\r\n    PERMANENTE: 0.6,\r\n    LUNGA: 0.7,\r\n    MEDIA: 0.8,\r\n    BREVE: 0.9,\r\n    INSTANTANEA: 1.1,\r\n  },\r\n  SERVICE_CLASS_2: {\r\n    PERMANENTE: 0.6,\r\n    LUNGA: 0.7,\r\n    MEDIA: 0.8,\r\n    BREVE: 0.9,\r\n    INSTANTANEA: 1.1,\r\n  },\r\n  SERVICE_CLASS_3: {\r\n    PERMANENTE: 0.5,\r\n    LUNGA: 0.55,\r\n    MEDIA: 0.65,\r\n    BREVE: 0.7,\r\n    INSTANTANEA: 0.9,\r\n  },\r\n}\r\n\r\nconst KDEF_VALUES = {\r\n  SERVICE_CLASS_1: 0.6,\r\n  SERVICE_CLASS_2: 0.8,\r\n  SERVICE_CLASS_3: 2.0,\r\n}\r\n\r\nexport const WoodBeamGeometryForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  materialProperties,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.wood.geometric_properties')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: () => {\r\n        toast.success(tAction('edit.success'))\r\n        onSave()\r\n      },\r\n      onError: () => {\r\n        toast.error(tAction('edit.error'))\r\n      },\r\n    },\r\n  )\r\n\r\n  const form = useForm<FormSchema>({\r\n    resolver: zodResolver(formSchema),\r\n    defaultValues: {\r\n      id: defaultValues?.id,\r\n      beamSectionWidth: defaultValues?.beamSectionWidth ?? 0,\r\n      beamSectionHeight: defaultValues?.beamSectionHeight ?? 0,\r\n      beamSpacing: defaultValues?.beamSpacing ?? 0,\r\n      beamSpan: defaultValues?.beamSpan ?? 0,\r\n      sectionModulus: defaultValues?.sectionModulus ?? 0,\r\n      inertiaMomentAboutY: defaultValues?.inertiaMomentAboutY ?? 0,\r\n      serviceClass: defaultValues?.serviceClass ?? 'SERVICE_CLASS_1',\r\n      loadDuration: defaultValues?.loadDuration ?? 'MEDIA',\r\n      correctionFactor: defaultValues?.correctionFactor ?? 0.8,\r\n      deformabilityFactor: defaultValues?.deformabilityFactor ?? 0.6,\r\n      designBendingStrength: defaultValues?.designBendingStrength ?? 0,\r\n      designShearStrength: defaultValues?.designShearStrength ?? 0,\r\n      elasticityInstantaneousModulus:\r\n        defaultValues?.elasticityInstantaneousModulus ?? 0,\r\n      longTermElasticityModulus: defaultValues?.longTermElasticityModulus ?? 0,\r\n    },\r\n    shouldUnregister: false,\r\n  })\r\n\r\n  const beamSectionWidth = useWatch({ control: form.control, name: 'beamSectionWidth' })\r\n  const beamSectionHeight = useWatch({ control: form.control, name: 'beamSectionHeight' })\r\n  const serviceClass = useWatch({ control: form.control, name: 'serviceClass' })\r\n  const loadDuration = useWatch({ control: form.control, name: 'loadDuration' })\r\n  const correctionFactor = useWatch({ control: form.control, name: 'correctionFactor' })\r\n  const deformabilityFactor = useWatch({ control: form.control, name: 'deformabilityFactor' })\r\n\r\n  useEffect(() => {\r\n    if (beamSectionWidth > 0 && beamSectionHeight > 0) {\r\n      const sectionModulus = (beamSectionWidth * Math.pow(beamSectionHeight, 2)) / 6\r\n      const inertiaMoment = (beamSectionWidth * Math.pow(beamSectionHeight, 3)) / 12\r\n      form.setValue('sectionModulus', sectionModulus)\r\n      form.setValue('inertiaMomentAboutY', inertiaMoment)\r\n    }\r\n  }, [beamSectionWidth, beamSectionHeight, form])\r\n\r\n  useEffect(() => {\r\n    if (serviceClass && loadDuration) {\r\n      const correction =\r\n        KMOD_TABLE[serviceClass as keyof typeof KMOD_TABLE]?.[\r\n          loadDuration as keyof (typeof KMOD_TABLE)['SERVICE_CLASS_1']\r\n        ] ?? 0.8\r\n      form.setValue('correctionFactor', correction)\r\n    }\r\n\r\n    if (serviceClass) {\r\n      const deformability = KDEF_VALUES[serviceClass as keyof typeof KDEF_VALUES] ?? 0.6\r\n      form.setValue('deformabilityFactor', deformability)\r\n    }\r\n  }, [serviceClass, loadDuration, form])\r\n\r\n  useEffect(() => {\r\n    const {\r\n      characteristicBendingStrength = 0,\r\n      characteristicShearStrength = 0,\r\n      partialMaterialFactor = 1.3,\r\n      meanElasticityModulus = 0,\r\n    } = materialProperties ?? {}\r\n\r\n    if (\r\n      correctionFactor > 0 &&\r\n      partialMaterialFactor > 0 &&\r\n      characteristicBendingStrength > 0 &&\r\n      characteristicShearStrength > 0\r\n    ) {\r\n      const designBendingStrength =\r\n        (characteristicBendingStrength * correctionFactor) / partialMaterialFactor\r\n      const designShearStrength =\r\n        (characteristicShearStrength * correctionFactor) / partialMaterialFactor\r\n\r\n      form.setValue('designBendingStrength', designBendingStrength)\r\n      form.setValue('designShearStrength', designShearStrength)\r\n    }\r\n\r\n    if (meanElasticityModulus > 0) {\r\n      form.setValue('elasticityInstantaneousModulus', meanElasticityModulus)\r\n      if (deformabilityFactor > 0) {\r\n        const longTermModulus = meanElasticityModulus / (1 + deformabilityFactor)\r\n        form.setValue('longTermElasticityModulus', longTermModulus)\r\n      }\r\n    }\r\n  }, [correctionFactor, deformabilityFactor, materialProperties, form])\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (data: FormSchema) => {\r\n      mutate({ projectId, moduleId, body: { geometry: data as any } })\r\n    },\r\n    [mutate, projectId, moduleId],\r\n  )\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4 grow\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        {/* User Input Fields */}\r\n        <NumberFormInput control={form.control} name=\"beamSectionWidth\" t={t} required />\r\n        <NumberFormInput control={form.control} name=\"beamSectionHeight\" t={t} required />\r\n        <NumberFormInput control={form.control} name=\"beamSpacing\" t={t} required />\r\n        <NumberFormInput control={form.control} name=\"beamSpan\" t={t} required />\r\n\r\n        {/* Calculated Fields */}\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"sectionModulus\"\r\n          t={t}\r\n          disabled\r\n          decimalPlaces={5}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"inertiaMomentAboutY\"\r\n          t={t}\r\n          disabled\r\n          decimalPlaces={5}\r\n        />\r\n\r\n        {/* Dropdown Fields */}\r\n        <SelectFormInput\r\n          control={form.control}\r\n          name=\"serviceClass\"\r\n          options={SERVICE_CLASS_OPTIONS}\r\n          t={t}\r\n          required\r\n        />\r\n        <SelectFormInput\r\n          control={form.control}\r\n          name=\"loadDuration\"\r\n          options={LOAD_DURATION_OPTIONS}\r\n          t={t}\r\n          required\r\n        />\r\n\r\n        {/* More Calculated Fields */}\r\n        <NumberFormInput control={form.control} name=\"correctionFactor\" t={t} disabled decimalPlaces={3} />\r\n        <NumberFormInput control={form.control} name=\"deformabilityFactor\" t={t} disabled decimalPlaces={3} />\r\n        <NumberFormInput control={form.control} name=\"designBendingStrength\" t={t} disabled decimalPlaces={3} />\r\n        <NumberFormInput control={form.control} name=\"designShearStrength\" t={t} disabled decimalPlaces={3} />\r\n        <NumberFormInput control={form.control} name=\"elasticityInstantaneousModulus\" t={t} disabled decimalPlaces={3} />\r\n        <NumberFormInput control={form.control} name=\"longTermElasticityModulus\" t={t} disabled decimalPlaces={3} />\r\n        <Button type=\"submit\" disabled={isPending}>\r\n          {isPending ? tCommon('saving') : tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAEA;AAEA;AACA;AAGA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AACA,MAAM,aAAa,yLAA4B,CAAC,MAAM,CAAC;IACrD,IAAI,kLAAC,CAAC,MAAM,GAAG,QAAQ;AACzB;AAkBA,MAAM,wBAAwB;IAC5B;QAAE,OAAO;QAAmB,OAAO;IAAuB;IAC1D;QAAE,OAAO;QAAmB,OAAO;IAAuB;IAC1D;QAAE,OAAO;QAAmB,OAAO;IAAuB;CAC3D;AAED,MAAM,wBAAwB;IAC5B;QAAE,OAAO;QAAc,OAAO;IAAa;IAC3C;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAe,OAAO;IAAc;CAC9C;AAED,MAAM,aAAa;IACjB,iBAAiB;QACf,YAAY;QACZ,OAAO;QACP,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA,iBAAiB;QACf,YAAY;QACZ,OAAO;QACP,OAAO;QACP,OAAO;QACP,aAAa;IACf;IACA,iBAAiB;QACf,YAAY;QACZ,OAAO;QACP,OAAO;QACP,OAAO;QACP,aAAa;IACf;AACF;AAEA,MAAM,cAAc;IAClB,iBAAiB;IACjB,iBAAiB;IACjB,iBAAiB;AACnB;AAEO,MAAM,uBAAuB,CAAC,EACnC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,kBAAkB,EAClB,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAEhC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB;QACF;QACA,SAAS;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ;QACtB;IACF;IAGF,MAAM,OAAO,IAAA,yKAAO,EAAa;QAC/B,UAAU,IAAA,6KAAW,EAAC;QACtB,eAAe;YACb,IAAI,eAAe;YACnB,kBAAkB,eAAe,oBAAoB;YACrD,mBAAmB,eAAe,qBAAqB;YACvD,aAAa,eAAe,eAAe;YAC3C,UAAU,eAAe,YAAY;YACrC,gBAAgB,eAAe,kBAAkB;YACjD,qBAAqB,eAAe,uBAAuB;YAC3D,cAAc,eAAe,gBAAgB;YAC7C,cAAc,eAAe,gBAAgB;YAC7C,kBAAkB,eAAe,oBAAoB;YACrD,qBAAqB,eAAe,uBAAuB;YAC3D,uBAAuB,eAAe,yBAAyB;YAC/D,qBAAqB,eAAe,uBAAuB;YAC3D,gCACE,eAAe,kCAAkC;YACnD,2BAA2B,eAAe,6BAA6B;QACzE;QACA,kBAAkB;IACpB;IAEA,MAAM,mBAAmB,IAAA,0KAAQ,EAAC;QAAE,SAAS,KAAK,OAAO;QAAE,MAAM;IAAmB;IACpF,MAAM,oBAAoB,IAAA,0KAAQ,EAAC;QAAE,SAAS,KAAK,OAAO;QAAE,MAAM;IAAoB;IACtF,MAAM,eAAe,IAAA,0KAAQ,EAAC;QAAE,SAAS,KAAK,OAAO;QAAE,MAAM;IAAe;IAC5E,MAAM,eAAe,IAAA,0KAAQ,EAAC;QAAE,SAAS,KAAK,OAAO;QAAE,MAAM;IAAe;IAC5E,MAAM,mBAAmB,IAAA,0KAAQ,EAAC;QAAE,SAAS,KAAK,OAAO;QAAE,MAAM;IAAmB;IACpF,MAAM,sBAAsB,IAAA,0KAAQ,EAAC;QAAE,SAAS,KAAK,OAAO;QAAE,MAAM;IAAsB;IAE1F,IAAA,kNAAS,EAAC;QACR,IAAI,mBAAmB,KAAK,oBAAoB,GAAG;YACjD,MAAM,iBAAiB,AAAC,mBAAmB,KAAK,GAAG,CAAC,mBAAmB,KAAM;YAC7E,MAAM,gBAAgB,AAAC,mBAAmB,KAAK,GAAG,CAAC,mBAAmB,KAAM;YAC5E,KAAK,QAAQ,CAAC,kBAAkB;YAChC,KAAK,QAAQ,CAAC,uBAAuB;QACvC;IACF,GAAG;QAAC;QAAkB;QAAmB;KAAK;IAE9C,IAAA,kNAAS,EAAC;QACR,IAAI,gBAAgB,cAAc;YAChC,MAAM,aACJ,UAAU,CAAC,aAAwC,EAAE,CACnD,aACD,IAAI;YACP,KAAK,QAAQ,CAAC,oBAAoB;QACpC;QAEA,IAAI,cAAc;YAChB,MAAM,gBAAgB,WAAW,CAAC,aAAyC,IAAI;YAC/E,KAAK,QAAQ,CAAC,uBAAuB;QACvC;IACF,GAAG;QAAC;QAAc;QAAc;KAAK;IAErC,IAAA,kNAAS,EAAC;QACR,MAAM,EACJ,gCAAgC,CAAC,EACjC,8BAA8B,CAAC,EAC/B,wBAAwB,GAAG,EAC3B,wBAAwB,CAAC,EAC1B,GAAG,sBAAsB,CAAC;QAE3B,IACE,mBAAmB,KACnB,wBAAwB,KACxB,gCAAgC,KAChC,8BAA8B,GAC9B;YACA,MAAM,wBACJ,AAAC,gCAAgC,mBAAoB;YACvD,MAAM,sBACJ,AAAC,8BAA8B,mBAAoB;YAErD,KAAK,QAAQ,CAAC,yBAAyB;YACvC,KAAK,QAAQ,CAAC,uBAAuB;QACvC;QAEA,IAAI,wBAAwB,GAAG;YAC7B,KAAK,QAAQ,CAAC,kCAAkC;YAChD,IAAI,sBAAsB,GAAG;gBAC3B,MAAM,kBAAkB,wBAAwB,CAAC,IAAI,mBAAmB;gBACxE,KAAK,QAAQ,CAAC,6BAA6B;YAC7C;QACF;IACF,GAAG;QAAC;QAAkB;QAAqB;QAAoB;KAAK;IAEpE,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,OAAO;YAAE;YAAW;YAAU,MAAM;gBAAE,UAAU;YAAY;QAAE;IAChE,GACA;QAAC;QAAQ;QAAW;KAAS;IAG/B,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAG5B,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAmB,GAAG;oBAAG,QAAQ;;;;;;8BAC9E,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAoB,GAAG;oBAAG,QAAQ;;;;;;8BAC/E,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAc,GAAG;oBAAG,QAAQ;;;;;;8BACzE,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAW,GAAG;oBAAG,QAAQ;;;;;;8BAGtE,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,QAAQ;oBACR,eAAe;;;;;;8BAEjB,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,QAAQ;oBACR,eAAe;;;;;;8BAIjB,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS;oBACT,GAAG;oBACH,QAAQ;;;;;;8BAEV,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS;oBACT,GAAG;oBACH,QAAQ;;;;;;8BAIV,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAmB,GAAG;oBAAG,QAAQ;oBAAC,eAAe;;;;;;8BAC9F,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAsB,GAAG;oBAAG,QAAQ;oBAAC,eAAe;;;;;;8BACjG,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAwB,GAAG;oBAAG,QAAQ;oBAAC,eAAe;;;;;;8BACnG,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAsB,GAAG;oBAAG,QAAQ;oBAAC,eAAe;;;;;;8BACjG,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAAiC,GAAG;oBAAG,QAAQ;oBAAC,eAAe;;;;;;8BAC5G,8OAAC,kLAAe;oBAAC,SAAS,KAAK,OAAO;oBAAE,MAAK;oBAA4B,GAAG;oBAAG,QAAQ;oBAAC,eAAe;;;;;;8BACvG,8OAAC,4IAAM;oBAAC,MAAK;oBAAS,UAAU;8BAC7B,YAAY,QAAQ,YAAY,QAAQ;;;;;;;;;;;;;;;;;AAKnD", "debugId": null}}, {"offset": {"line": 400, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-general-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type WoodGeneralFormInputs,\r\n  woodGeneralSchema,\r\n} from '@atlas/types/schemas/wood-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\n\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<WoodGeneralFormInputs>\r\n  onSave: () => void\r\n}\r\n\r\nexport const WoodGeneralForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.wood.general')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  const form = useForm<WoodGeneralFormInputs>({\r\n    resolver: zodResolver(woodGeneralSchema),\r\n    defaultValues: {\r\n      initialDeformation: defaultValues?.initialDeformation ?? 0,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: () => {\r\n        toast.success(tAction('edit.success'))\r\n        // Query invalidation is handled by the mutation hook itself\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: WoodGeneralFormInputs) => {\r\n      mutate({ projectId, moduleId, body })\r\n    },\r\n    [mutate, projectId, moduleId],\r\n  )\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"initialDeformation\"\r\n          t={t}\r\n          required={true}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAGA;AACA;AAIA;AAEA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;AAUO,MAAM,kBAAkB,CAAC,EAC9B,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAEhC,MAAM,OAAO,IAAA,yKAAO,EAAwB;QAC1C,UAAU,IAAA,6KAAW,EAAC,4JAAiB;QACvC,eAAe;YACb,oBAAoB,eAAe,sBAAsB;QAC3D;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,4DAA4D;YAC5D;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,OAAO;YAAE;YAAW;YAAU;QAAK;IACrC,GACA;QAAC;QAAQ;QAAW;KAAS;IAG/B,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,4IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,8OAAC,oOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 513, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-geometry-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport { useWoodMaterialByName } from '@atlas/lib/query/materials/use-wood-material-by-name'\r\nimport { useWoodNames } from '@atlas/lib/query/materials/use-wood-names-api'\r\nimport { woodMaterialSchema1 } from '@atlas/types/schemas/wood-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect, useState } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\nimport { z } from 'zod'\r\n\r\n// Partial Material Factor dropdown options\r\nconst PARTIAL_MATERIAL_FACTOR_OPTIONS = [\r\n  { value: '1', label: '1' },\r\n  { value: '5', label: '5' },\r\n  { value: '45', label: '45' },\r\n]\r\n\r\n// Custom form schema that extends the API schema but with string for dropdown\r\nconst woodGeometryFormSchema = woodMaterialSchema1.extend({\r\n  partialMaterialFactor: z.string(), // Override to string for dropdown\r\n})\r\n\r\ntype FormSchema = z.infer<typeof woodGeometryFormSchema>\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FormSchema>\r\n  woodName?: string\r\n  onSave: () => void\r\n}\r\n\r\nexport const WoodGeometryForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  woodName: _woodName,\r\n  onSave: _onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.wood.geometry')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const {\r\n    data: woodNames,\r\n    isError: woodNamesError,\r\n    isLoading: woodNamesLoading,\r\n  } = useWoodNames({ session })\r\n  const woodNamesOptions = [\r\n    ...(woodNames?.map(m => ({ value: m, label: m })) ?? []),\r\n  ]\r\n  const form = useForm<FormSchema>({\r\n    resolver: zodResolver(woodGeometryFormSchema),\r\n    defaultValues: {\r\n      // Material Properties fields from woodMaterialSchema1\r\n      id: defaultValues?.id,\r\n      category: defaultValues?.woodName ?? '',\r\n      partialMaterialFactor: defaultValues?.partialMaterialFactor\r\n        ? defaultValues.partialMaterialFactor.toString()\r\n        : '1',\r\n      characteristicBendingStrength:\r\n        defaultValues?.characteristicBendingStrength ?? 0,\r\n      characteristicShearStrength:\r\n        defaultValues?.characteristicShearStrength ?? 0,\r\n      characteristicTensileStrength:\r\n        defaultValues?.characteristicTensileStrength ?? 0,\r\n      characteristicCompressiveStrength:\r\n        defaultValues?.characteristicCompressiveStrength ?? 0,\r\n      meanDensity: defaultValues?.meanDensity ?? 0,\r\n      meanShearModulus: defaultValues?.meanShearModulus ?? 0,\r\n      elasticityModulusParallelToGrain:\r\n        defaultValues?.elasticityModulusParallelToGrain ?? 0,\r\n      meanElasticityModulus: defaultValues?.meanElasticityModulus ?? 0,\r\n    },\r\n    // Prevent form from resetting after successful submission\r\n    shouldUnregister: false,\r\n  })\r\n\r\n  // Reset form with new defaultValues when they change (component remount)\r\n  useEffect(() => {\r\n    if (defaultValues) {\r\n      form.reset({\r\n        id: defaultValues.id,\r\n        category: defaultValues.woodName ?? '',\r\n        partialMaterialFactor: defaultValues.partialMaterialFactor\r\n          ? defaultValues.partialMaterialFactor.toString()\r\n          : '1',\r\n        characteristicBendingStrength:\r\n          defaultValues.characteristicBendingStrength ?? 0,\r\n        characteristicShearStrength:\r\n          defaultValues.characteristicShearStrength ?? 0,\r\n        characteristicTensileStrength:\r\n          defaultValues.characteristicTensileStrength ?? 0,\r\n        characteristicCompressiveStrength:\r\n          defaultValues.characteristicCompressiveStrength ?? 0,\r\n        meanDensity: defaultValues.meanDensity ?? 0,\r\n        meanShearModulus: defaultValues.meanShearModulus ?? 0,\r\n        elasticityModulusParallelToGrain:\r\n          defaultValues.elasticityModulusParallelToGrain ?? 0,\r\n        meanElasticityModulus: defaultValues.meanElasticityModulus ?? 0,\r\n      })\r\n    }\r\n  }, [defaultValues, form])\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: () => {\r\n        toast.success(tAction('edit.success'))\r\n        // Query invalidation is handled by the mutation hook itself\r\n        // Don't call onSave() immediately to prevent form clearing\r\n        // The parent component should handle accordion state separately\r\n        // onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  // State to store category name from API - initialize from defaultValues if available\r\n  const [categoryName, setCategoryName] = useState<string>(\r\n    defaultValues?.woodName || '',\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (data: FormSchema) => {\r\n      // Include categoryName from API in the category field\r\n      const dataWithCategory = {\r\n        ...data,\r\n        category: categoryName || data.category,\r\n        woodName: data.category,\r\n        // Convert partialMaterialFactor from string to number for API\r\n        partialMaterialFactor: parseFloat(data.partialMaterialFactor),\r\n      }\r\n\r\n      // Send as materialProperties to match the API structure\r\n      mutate({\r\n        projectId,\r\n        moduleId,\r\n        body: { materialProperties: dataWithCategory as any },\r\n      })\r\n    },\r\n    [mutate, projectId, moduleId, categoryName],\r\n  )\r\n\r\n  // Watch for category changes to fetch wood material data\r\n  const selectedCategory = form.watch('category')\r\n\r\n  const { data: woodMaterialData } = useWoodMaterialByName(\r\n    session,\r\n    selectedCategory,\r\n    !!selectedCategory,\r\n  )\r\n\r\n  // Sync categoryName when defaultValues change (component remount or data refresh)\r\n  useEffect(() => {\r\n    if (defaultValues?.category && !categoryName) {\r\n      setCategoryName(defaultValues.category)\r\n    }\r\n  }, [defaultValues?.category, categoryName])\r\n\r\n  // Update form fields when wood material data is fetched\r\n  useEffect(() => {\r\n    if (woodMaterialData) {\r\n      // Store category name from API response\r\n      setCategoryName(woodMaterialData.category)\r\n\r\n      // Map API response to form fields\r\n      form.setValue(\r\n        'characteristicBendingStrength',\r\n        woodMaterialData.flexuralStrength,\r\n      )\r\n      form.setValue(\r\n        'characteristicShearStrength',\r\n        woodMaterialData.shearStrength,\r\n      )\r\n      form.setValue(\r\n        'characteristicTensileStrength',\r\n        woodMaterialData.tensileStrengthParallel,\r\n      )\r\n      form.setValue(\r\n        'characteristicCompressiveStrength',\r\n        woodMaterialData.compressiveStrengthParallel,\r\n      )\r\n      form.setValue('meanDensity', woodMaterialData.meanDensity)\r\n      form.setValue('meanShearModulus', woodMaterialData.shearModulusMean)\r\n      form.setValue(\r\n        'elasticityModulusParallelToGrain',\r\n        woodMaterialData.elasticModulusCharacteristicParallel,\r\n      )\r\n      form.setValue(\r\n        'meanElasticityModulus',\r\n        woodMaterialData.elasticModulusMeanParallel,\r\n      )\r\n    }\r\n  }, [woodMaterialData, form])\r\n\r\n  return (\r\n    <div className=\"flex flex-col 2xl:flex-row justify-center gap-2\">\r\n      <Form {...form}>\r\n        <form\r\n          className=\"space-y-4 rounded-md border p-4 grow\"\r\n          onSubmit={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          <SelectFormInput\r\n            control={form.control}\r\n            name=\"category\"\r\n            options={woodNamesOptions}\r\n            t={t}\r\n            loading={woodNamesLoading}\r\n            requestError={woodNamesError}\r\n            errorMessage={t('category.error')}\r\n            required={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"characteristicBendingStrength\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"characteristicShearStrength\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"characteristicTensileStrength\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"characteristicCompressiveStrength\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"meanDensity\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"meanShearModulus\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"elasticityModulusParallelToGrain\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"meanElasticityModulus\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <SelectFormInput\r\n            control={form.control}\r\n            name=\"partialMaterialFactor\"\r\n            options={PARTIAL_MATERIAL_FACTOR_OPTIONS}\r\n            t={t}\r\n            required={true}\r\n          />\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full sm:w-auto\"\r\n            disabled={isPending}\r\n            onClick={form.handleSubmit(handleFormSubmit)}\r\n          >\r\n            {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n            {tCommon('save')}\r\n          </Button>\r\n        </form>\r\n      </Form>\r\n      {/* <Image\r\n        src={'/assets/wood/wood_material_properties.jpg'}\r\n        alt=\"wood material properties\"\r\n        height={500}\r\n        width={250}\r\n        className=\"mx-auto rounded-md object-contain\"\r\n        priority\r\n      /> */}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AAEA,2CAA2C;AAC3C,MAAM,kCAAkC;IACtC;QAAE,OAAO;QAAK,OAAO;IAAI;IACzB;QAAE,OAAO;QAAK,OAAO;IAAI;IACzB;QAAE,OAAO;QAAM,OAAO;IAAK;CAC5B;AAED,8EAA8E;AAC9E,MAAM,yBAAyB,8JAAmB,CAAC,MAAM,CAAC;IACxD,uBAAuB,kLAAC,CAAC,MAAM;AACjC;AAaO,MAAM,mBAAmB,CAAC,EAC/B,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,UAAU,SAAS,EACnB,QAAQ,OAAO,EACT;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,EACJ,MAAM,SAAS,EACf,SAAS,cAAc,EACvB,WAAW,gBAAgB,EAC5B,GAAG,IAAA,+KAAY,EAAC;QAAE;IAAQ;IAC3B,MAAM,mBAAmB;WACnB,WAAW,IAAI,CAAA,IAAK,CAAC;gBAAE,OAAO;gBAAG,OAAO;YAAE,CAAC,MAAM,EAAE;KACxD;IACD,MAAM,OAAO,IAAA,yKAAO,EAAa;QAC/B,UAAU,IAAA,6KAAW,EAAC;QACtB,eAAe;YACb,sDAAsD;YACtD,IAAI,eAAe;YACnB,UAAU,eAAe,YAAY;YACrC,uBAAuB,eAAe,wBAClC,cAAc,qBAAqB,CAAC,QAAQ,KAC5C;YACJ,+BACE,eAAe,iCAAiC;YAClD,6BACE,eAAe,+BAA+B;YAChD,+BACE,eAAe,iCAAiC;YAClD,mCACE,eAAe,qCAAqC;YACtD,aAAa,eAAe,eAAe;YAC3C,kBAAkB,eAAe,oBAAoB;YACrD,kCACE,eAAe,oCAAoC;YACrD,uBAAuB,eAAe,yBAAyB;QACjE;QACA,0DAA0D;QAC1D,kBAAkB;IACpB;IAEA,yEAAyE;IACzE,IAAA,kNAAS,EAAC;QACR,IAAI,eAAe;YACjB,KAAK,KAAK,CAAC;gBACT,IAAI,cAAc,EAAE;gBACpB,UAAU,cAAc,QAAQ,IAAI;gBACpC,uBAAuB,cAAc,qBAAqB,GACtD,cAAc,qBAAqB,CAAC,QAAQ,KAC5C;gBACJ,+BACE,cAAc,6BAA6B,IAAI;gBACjD,6BACE,cAAc,2BAA2B,IAAI;gBAC/C,+BACE,cAAc,6BAA6B,IAAI;gBACjD,mCACE,cAAc,iCAAiC,IAAI;gBACrD,aAAa,cAAc,WAAW,IAAI;gBAC1C,kBAAkB,cAAc,gBAAgB,IAAI;gBACpD,kCACE,cAAc,gCAAgC,IAAI;gBACpD,uBAAuB,cAAc,qBAAqB,IAAI;YAChE;QACF;IACF,GAAG;QAAC;QAAe;KAAK;IAExB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;QACtB,4DAA4D;QAC5D,2DAA2D;QAC3D,gEAAgE;QAChE,WAAW;QACb;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,qFAAqF;IACrF,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,iNAAQ,EAC9C,eAAe,YAAY;IAG7B,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,sDAAsD;QACtD,MAAM,mBAAmB;YACvB,GAAG,IAAI;YACP,UAAU,gBAAgB,KAAK,QAAQ;YACvC,UAAU,KAAK,QAAQ;YACvB,8DAA8D;YAC9D,uBAAuB,WAAW,KAAK,qBAAqB;QAC9D;QAEA,wDAAwD;QACxD,OAAO;YACL;YACA;YACA,MAAM;gBAAE,oBAAoB;YAAwB;QACtD;IACF,GACA;QAAC;QAAQ;QAAW;QAAU;KAAa;IAG7C,yDAAyD;IACzD,MAAM,mBAAmB,KAAK,KAAK,CAAC;IAEpC,MAAM,EAAE,MAAM,gBAAgB,EAAE,GAAG,IAAA,kMAAqB,EACtD,SACA,kBACA,CAAC,CAAC;IAGJ,kFAAkF;IAClF,IAAA,kNAAS,EAAC;QACR,IAAI,eAAe,YAAY,CAAC,cAAc;YAC5C,gBAAgB,cAAc,QAAQ;QACxC;IACF,GAAG;QAAC,eAAe;QAAU;KAAa;IAE1C,wDAAwD;IACxD,IAAA,kNAAS,EAAC;QACR,IAAI,kBAAkB;YACpB,wCAAwC;YACxC,gBAAgB,iBAAiB,QAAQ;YAEzC,kCAAkC;YAClC,KAAK,QAAQ,CACX,iCACA,iBAAiB,gBAAgB;YAEnC,KAAK,QAAQ,CACX,+BACA,iBAAiB,aAAa;YAEhC,KAAK,QAAQ,CACX,iCACA,iBAAiB,uBAAuB;YAE1C,KAAK,QAAQ,CACX,qCACA,iBAAiB,2BAA2B;YAE9C,KAAK,QAAQ,CAAC,eAAe,iBAAiB,WAAW;YACzD,KAAK,QAAQ,CAAC,oBAAoB,iBAAiB,gBAAgB;YACnE,KAAK,QAAQ,CACX,oCACA,iBAAiB,oCAAoC;YAEvD,KAAK,QAAQ,CACX,yBACA,iBAAiB,0BAA0B;QAE/C;IACF,GAAG;QAAC;QAAkB;KAAK;IAE3B,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,wIAAI;YAAE,GAAG,IAAI;sBACZ,cAAA,8OAAC;gBACC,WAAU;gBACV,UAAU,KAAK,YAAY,CAAC;;kCAE5B,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,SAAS;wBACT,GAAG;wBACH,SAAS;wBACT,cAAc;wBACd,cAAc,EAAE;wBAChB,UAAU;;;;;;kCAEZ,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAEjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAEjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAEjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAEjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAEjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAEjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAEjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAEjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,SAAS;wBACT,GAAG;wBACH,UAAU;;;;;;kCAEZ,8OAAC,4IAAM;wBACL,MAAK;wBACL,WAAU;wBACV,UAAU;wBACV,SAAS,KAAK,YAAY,CAAC;;4BAE1B,2BAAa,8OAAC,oOAAW;gCAAC,WAAU;;;;;;4BACpC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;AAcrB", "debugId": null}}, {"offset": {"line": 853, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { Badge } from '@atlas/components/ui/badge'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport { woodPreInterventionCheckSchema } from '@atlas/lib/api/modules/schemas/wood-params'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\nimport type { z } from 'zod'\r\n\r\n// Custom form schema that extends the API schema\r\nconst woodPreInterventionCheckFormSchema = woodPreInterventionCheckSchema\r\n\r\ntype FormSchema = z.infer<typeof woodPreInterventionCheckFormSchema>\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FormSchema>\r\n  geometryProperties?: any // Geometry properties from form 3\r\n  onSave: () => void\r\n  initialDeformation?: any\r\n}\r\n\r\nexport const WoodPreInterventionCheckForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  geometryProperties,\r\n  onSave,\r\n  initialDeformation,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.wood.preIntervationCheck')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  const form = useForm<FormSchema>({\r\n    resolver: zodResolver(woodPreInterventionCheckFormSchema),\r\n    defaultValues: {\r\n      maximumBendingMoment: defaultValues?.maximumBendingMoment ?? 0,\r\n      maximumShearForce: defaultValues?.maximumShearForce ?? 0,\r\n      designBendingStress: defaultValues?.designBendingStress ?? 0,\r\n      designBendingStrength: defaultValues?.designBendingStrength ?? 0,\r\n      bendingCheck: defaultValues?.bendingCheck ?? 0,\r\n      designShearStress: defaultValues?.designShearStress ?? 0,\r\n      designShearStrength: defaultValues?.designShearStrength ?? 0,\r\n      shearCheck: defaultValues?.shearCheck ?? 0,\r\n      permanentLoadPerLinearMeter:\r\n        defaultValues?.permanentLoadPerLinearMeter ?? 0,\r\n      imposedLoadPerLinearMeter: defaultValues?.imposedLoadPerLinearMeter ?? 0,\r\n      instantaneousDeflectionPermanentLoad:\r\n        defaultValues?.instantaneousDeflectionPermanentLoad ?? 0,\r\n      instantaneousDeflectionImposedLoad:\r\n        defaultValues?.instantaneousDeflectionImposedLoad ?? 0,\r\n      instantaneousDeflectionTotalLoads:\r\n        defaultValues?.instantaneousDeflectionTotalLoads ?? 0,\r\n      deformabilityCheck: defaultValues?.deformabilityCheck ?? 0,\r\n      combinationFactor: defaultValues?.combinationFactor ?? 0.3,\r\n      finalDeflectionTotalLoads: defaultValues?.finalDeflectionTotalLoads ?? 0,\r\n      finalCheckResult: defaultValues?.finalCheckResult ?? 0,\r\n    },\r\n  })\r\n\r\n  // Reset form when defaultValues change\r\n  useEffect(() => {\r\n    if (defaultValues) {\r\n      form.reset({\r\n        maximumBendingMoment: defaultValues.maximumBendingMoment ?? 0,\r\n        maximumShearForce: defaultValues.maximumShearForce ?? 0,\r\n        designBendingStress: defaultValues.designBendingStress ?? 0,\r\n        designBendingStrength: defaultValues.designBendingStrength ?? 0,\r\n        bendingCheck: defaultValues.bendingCheck ?? 0,\r\n        designShearStress: defaultValues.designShearStress ?? 0,\r\n        designShearStrength: defaultValues.designShearStrength ?? 0,\r\n        shearCheck: defaultValues.shearCheck ?? 0,\r\n        permanentLoadPerLinearMeter:\r\n          defaultValues.permanentLoadPerLinearMeter ?? 0,\r\n        imposedLoadPerLinearMeter: defaultValues.imposedLoadPerLinearMeter ?? 0,\r\n        instantaneousDeflectionPermanentLoad:\r\n          defaultValues.instantaneousDeflectionPermanentLoad ?? 0,\r\n        instantaneousDeflectionImposedLoad:\r\n          defaultValues.instantaneousDeflectionImposedLoad ?? 0,\r\n        instantaneousDeflectionTotalLoads:\r\n          defaultValues.instantaneousDeflectionTotalLoads ?? 0,\r\n        deformabilityCheck: defaultValues.deformabilityCheck ?? 0,\r\n        combinationFactor: defaultValues.combinationFactor ?? 0.3,\r\n        finalDeflectionTotalLoads: defaultValues.finalDeflectionTotalLoads ?? 0,\r\n        finalCheckResult: defaultValues.finalCheckResult ?? 0,\r\n      })\r\n    }\r\n  }, [defaultValues, form])\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: () => {\r\n        toast.success(tAction('edit.success'))\r\n        // Query invalidation is handled by the mutation hook itself\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (data: FormSchema) => {\r\n      // Send as preIntervationCheck to match the API structure\r\n      mutate({\r\n        projectId,\r\n        moduleId,\r\n        body: { preIntervationCheck: data } as any,\r\n      })\r\n    },\r\n    [mutate, projectId, moduleId],\r\n  )\r\n\r\n  // Watch form values for calculations\r\n  const [\r\n    maximumBendingMoment,\r\n    maximumShearForce,\r\n    permanentLoadPerLinearMeter,\r\n    imposedLoadPerLinearMeter,\r\n    combinationFactor,\r\n  ] = form.watch([\r\n    'maximumBendingMoment',\r\n    'maximumShearForce',\r\n    'permanentLoadPerLinearMeter',\r\n    'imposedLoadPerLinearMeter',\r\n    'combinationFactor',\r\n  ])\r\n\r\n  // Helper function to determine check result\r\n  const getCheckResult = (\r\n    value: number,\r\n    threshold: number,\r\n    isGreaterThan: boolean = true,\r\n  ) => {\r\n    if (isGreaterThan) {\r\n      return value > threshold ? 'Non-satisfy' : 'Satisfy'\r\n    }\r\n    return value < threshold ? 'Non-satisfy' : 'Satisfy'\r\n  }\r\n\r\n  // Helper function to get badge variant\r\n  const getBadgeVariant = (result: string) => {\r\n    return result === 'Satisfy' ? 'default' : 'destructive'\r\n  }\r\n\r\n  // Calculations based on form values and properties from other forms\r\n  useEffect(() => {\r\n    if (maximumBendingMoment && geometryProperties?.sectionModulus) {\r\n      const designBendingStress =\r\n        maximumBendingMoment / (geometryProperties.sectionModulus * 1000)\r\n      form.setValue('designBendingStress', designBendingStress)\r\n    }\r\n  }, [maximumBendingMoment, geometryProperties?.sectionModulus, form])\r\n\r\n  useEffect(() => {\r\n    if (geometryProperties?.designBendingStrength) {\r\n      form.setValue(\r\n        'designBendingStrength',\r\n        geometryProperties.designBendingStrength,\r\n      )\r\n    }\r\n  }, [geometryProperties?.designBendingStrength, form])\r\n\r\n  useEffect(() => {\r\n    const designBendingStress = form.getValues('designBendingStress')\r\n    const designBendingStrength = form.getValues('designBendingStrength')\r\n    if (designBendingStress && designBendingStrength) {\r\n      const bendingCheck = designBendingStress / designBendingStrength\r\n      form.setValue('bendingCheck', bendingCheck)\r\n    }\r\n  }, [form])\r\n\r\n  useEffect(() => {\r\n    if (\r\n      maximumShearForce &&\r\n      geometryProperties?.beamSectionHeight &&\r\n      geometryProperties?.beamSectionWidth\r\n    ) {\r\n      const designShearStress =\r\n        (3 * 1000 * maximumShearForce) /\r\n        (2 *\r\n          1000 *\r\n          geometryProperties.beamSectionHeight *\r\n          1000 *\r\n          geometryProperties.beamSectionWidth)\r\n      form.setValue('designShearStress', designShearStress)\r\n    }\r\n  }, [\r\n    maximumShearForce,\r\n    geometryProperties?.beamSectionHeight,\r\n    geometryProperties?.beamSectionWidth,\r\n    form,\r\n  ])\r\n\r\n  useEffect(() => {\r\n    if (geometryProperties?.designShearStrength) {\r\n      form.setValue(\r\n        'designShearStrength',\r\n        geometryProperties.designShearStrength,\r\n      )\r\n    }\r\n  }, [geometryProperties?.designShearStrength, form])\r\n\r\n  useEffect(() => {\r\n    const designShearStress = form.getValues('designShearStress')\r\n    const designShearStrength = form.getValues('designShearStrength')\r\n    if (designShearStress && designShearStrength) {\r\n      const shearCheck = designShearStress / designShearStrength\r\n      form.setValue('shearCheck', shearCheck)\r\n    }\r\n  }, [form])\r\n\r\n  // Deflection calculations\r\n  useEffect(() => {\r\n    if (\r\n      permanentLoadPerLinearMeter &&\r\n      geometryProperties?.beamSpan &&\r\n      geometryProperties?.elasticityInstantaneousModulus &&\r\n      geometryProperties?.inertiaMomentAboutY\r\n    ) {\r\n      const instantaneousDeflectionPermanentLoad =\r\n        (100 *\r\n          (5 *\r\n            permanentLoadPerLinearMeter *\r\n            geometryProperties.beamSpan ** 4)) /\r\n        (384 *\r\n          1000 *\r\n          geometryProperties.elasticityInstantaneousModulus *\r\n          geometryProperties.inertiaMomentAboutY)\r\n      form.setValue(\r\n        'instantaneousDeflectionPermanentLoad',\r\n        instantaneousDeflectionPermanentLoad,\r\n      )\r\n    }\r\n  }, [\r\n    permanentLoadPerLinearMeter,\r\n    geometryProperties?.beamSpan,\r\n    geometryProperties?.elasticityInstantaneousModulus,\r\n    geometryProperties?.inertiaMomentAboutY,\r\n    form,\r\n  ])\r\n\r\n  useEffect(() => {\r\n    if (\r\n      imposedLoadPerLinearMeter &&\r\n      geometryProperties?.beamSpan &&\r\n      geometryProperties?.elasticityInstantaneousModulus &&\r\n      geometryProperties?.inertiaMomentAboutY\r\n    ) {\r\n      const instantaneousDeflectionImposedLoad =\r\n        (100 *\r\n          (5 * imposedLoadPerLinearMeter * geometryProperties.beamSpan ** 4)) /\r\n        (384 *\r\n          1000 *\r\n          geometryProperties.elasticityInstantaneousModulus *\r\n          geometryProperties.inertiaMomentAboutY)\r\n      form.setValue(\r\n        'instantaneousDeflectionImposedLoad',\r\n        instantaneousDeflectionImposedLoad,\r\n      )\r\n    }\r\n  }, [\r\n    imposedLoadPerLinearMeter,\r\n    geometryProperties?.beamSpan,\r\n    geometryProperties?.elasticityInstantaneousModulus,\r\n    geometryProperties?.inertiaMomentAboutY,\r\n    form,\r\n  ])\r\n\r\n  useEffect(() => {\r\n    const instantaneousDeflectionPermanentLoad = form.getValues(\r\n      'instantaneousDeflectionPermanentLoad',\r\n    )\r\n    const instantaneousDeflectionImposedLoad = form.getValues(\r\n      'instantaneousDeflectionImposedLoad',\r\n    )\r\n    if (\r\n      instantaneousDeflectionPermanentLoad &&\r\n      instantaneousDeflectionImposedLoad\r\n    ) {\r\n      // Note: Adding 0 for the \"peli field\" as mentioned in requirements\r\n      const instantaneousDeflectionTotalLoads =\r\n        instantaneousDeflectionPermanentLoad +\r\n        instantaneousDeflectionImposedLoad +\r\n        initialDeformation\r\n      form.setValue(\r\n        'instantaneousDeflectionTotalLoads',\r\n        instantaneousDeflectionTotalLoads,\r\n      )\r\n    }\r\n  }, [form, initialDeformation])\r\n\r\n  useEffect(() => {\r\n    const instantaneousDeflectionTotalLoads = form.getValues(\r\n      'instantaneousDeflectionTotalLoads',\r\n    )\r\n    if (instantaneousDeflectionTotalLoads && geometryProperties?.beamSpan) {\r\n      const deformabilityCheck =\r\n        (100 * geometryProperties.beamSpan) / instantaneousDeflectionTotalLoads\r\n      form.setValue('deformabilityCheck', deformabilityCheck)\r\n    }\r\n  }, [geometryProperties?.beamSpan, form])\r\n\r\n  useEffect(() => {\r\n    const instantaneousDeflectionPermanentLoad = form.getValues(\r\n      'instantaneousDeflectionPermanentLoad',\r\n    )\r\n    const instantaneousDeflectionImposedLoad = form.getValues(\r\n      'instantaneousDeflectionImposedLoad',\r\n    )\r\n    if (\r\n      instantaneousDeflectionPermanentLoad &&\r\n      instantaneousDeflectionImposedLoad &&\r\n      combinationFactor &&\r\n      geometryProperties?.deformabilityFactor\r\n    ) {\r\n      // Note: Adding 0 for the \"peli field\" as mentioned in requirements\r\n      const finalDeflectionTotalLoads =\r\n        instantaneousDeflectionPermanentLoad *\r\n          (1 + geometryProperties.deformabilityFactor) +\r\n        instantaneousDeflectionImposedLoad *\r\n          (1 + combinationFactor * geometryProperties.deformabilityFactor) +\r\n        initialDeformation\r\n      form.setValue('finalDeflectionTotalLoads', finalDeflectionTotalLoads)\r\n    }\r\n  }, [\r\n    combinationFactor,\r\n    geometryProperties?.deformabilityFactor,\r\n    initialDeformation,\r\n    form,\r\n  ])\r\n\r\n  useEffect(() => {\r\n    const finalDeflectionTotalLoads = form.getValues(\r\n      'finalDeflectionTotalLoads',\r\n    )\r\n    if (finalDeflectionTotalLoads && geometryProperties?.beamSpan) {\r\n      const finalCheckResult =\r\n        (100 * geometryProperties.beamSpan) / finalDeflectionTotalLoads\r\n      form.setValue('finalCheckResult', finalCheckResult)\r\n    }\r\n  }, [geometryProperties?.beamSpan, form])\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        {/* User Input Fields */}\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"maximumBendingMoment\"\r\n          t={t}\r\n          required={true}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"maximumShearForce\"\r\n          t={t}\r\n          required={true}\r\n        />\r\n\r\n        {/* Calculated Bending Fields */}\r\n        <div className=\"space-y-4\">\r\n          <Separator />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"designBendingStress\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"designBendingStrength\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <div className=\"flex items-start gap-4 flex\">\r\n            <NumberFormInput\r\n              control={form.control}\r\n              name=\"bendingCheck\"\r\n              t={t}\r\n              disabled={true}\r\n              fieldContainerClassName=\"flex-1\"\r\n              decimalPlaces={3}\r\n            />\r\n            <Badge\r\n              variant={getBadgeVariant(\r\n                getCheckResult(form.watch('bendingCheck'), 1),\r\n              )}\r\n              className=\"mt-7\"\r\n            >\r\n              {getCheckResult(form.watch('bendingCheck'), 1)}\r\n            </Badge>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Calculated Shear Fields */}\r\n        <div className=\"space-y-4\">\r\n          <Separator />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"designShearStress\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"designShearStrength\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <div className=\"flex items-start gap-4 flex\">\r\n            <NumberFormInput\r\n              control={form.control}\r\n              name=\"shearCheck\"\r\n              t={t}\r\n              disabled={true}\r\n              fieldContainerClassName=\"flex-1\"\r\n              decimalPlaces={3}\r\n            />\r\n            <Badge\r\n              variant={getBadgeVariant(\r\n                getCheckResult(form.watch('shearCheck'), 1),\r\n              )}\r\n              className=\"mt-7\"\r\n            >\r\n              {getCheckResult(form.watch('shearCheck'), 1)}\r\n            </Badge>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Load Input Fields */}\r\n        <div className=\"space-y-4\">\r\n          <Separator />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"permanentLoadPerLinearMeter\"\r\n            t={t}\r\n            required={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"imposedLoadPerLinearMeter\"\r\n            t={t}\r\n            required={true}\r\n          />\r\n        </div>\r\n\r\n        {/* Deflection Analysis */}\r\n        <div className=\"space-y-4\">\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"instantaneousDeflectionPermanentLoad\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"instantaneousDeflectionImposedLoad\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"instantaneousDeflectionTotalLoads\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <div className=\"flex items-start gap-4 flex\">\r\n            <NumberFormInput\r\n              control={form.control}\r\n              name=\"deformabilityCheck\"\r\n              t={t}\r\n              disabled={true}\r\n              fieldContainerClassName=\"flex-1\"\r\n              decimalPlaces={3}\r\n            />\r\n            <Badge\r\n              variant={getBadgeVariant(\r\n                getCheckResult(form.watch('deformabilityCheck'), 300, false),\r\n              )}\r\n              className=\"mt-7\"\r\n            >\r\n              {getCheckResult(form.watch('deformabilityCheck'), 300, false)}\r\n            </Badge>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Final Analysis */}\r\n        <div className=\"space-y-4\">\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"combinationFactor\"\r\n            t={t}\r\n            required={false}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"finalDeflectionTotalLoads\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n          <div className=\"flex items-start gap-4 flex\">\r\n            <NumberFormInput\r\n              control={form.control}\r\n              name=\"finalCheckResult\"\r\n              t={t}\r\n              disabled={true}\r\n              fieldContainerClassName=\"flex-1\"\r\n              decimalPlaces={3}\r\n            />\r\n            <Badge\r\n              variant={getBadgeVariant(\r\n                getCheckResult(form.watch('finalCheckResult'), 200, false),\r\n              )}\r\n              className=\"mt-7\"\r\n            >\r\n              {getCheckResult(form.watch('finalCheckResult'), 200, false)}\r\n            </Badge>\r\n          </div>\r\n        </div>\r\n\r\n        <Button type=\"submit\" className=\"w-full sm:w-auto\" disabled={isPending}>\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;AAGA,iDAAiD;AACjD,MAAM,qCAAqC,2LAA8B;AAclE,MAAM,+BAA+B,CAAC,EAC3C,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,kBAAkB,EAClB,MAAM,EACN,kBAAkB,EACZ;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAEhC,MAAM,OAAO,IAAA,yKAAO,EAAa;QAC/B,UAAU,IAAA,6KAAW,EAAC;QACtB,eAAe;YACb,sBAAsB,eAAe,wBAAwB;YAC7D,mBAAmB,eAAe,qBAAqB;YACvD,qBAAqB,eAAe,uBAAuB;YAC3D,uBAAuB,eAAe,yBAAyB;YAC/D,cAAc,eAAe,gBAAgB;YAC7C,mBAAmB,eAAe,qBAAqB;YACvD,qBAAqB,eAAe,uBAAuB;YAC3D,YAAY,eAAe,cAAc;YACzC,6BACE,eAAe,+BAA+B;YAChD,2BAA2B,eAAe,6BAA6B;YACvE,sCACE,eAAe,wCAAwC;YACzD,oCACE,eAAe,sCAAsC;YACvD,mCACE,eAAe,qCAAqC;YACtD,oBAAoB,eAAe,sBAAsB;YACzD,mBAAmB,eAAe,qBAAqB;YACvD,2BAA2B,eAAe,6BAA6B;YACvE,kBAAkB,eAAe,oBAAoB;QACvD;IACF;IAEA,uCAAuC;IACvC,IAAA,kNAAS,EAAC;QACR,IAAI,eAAe;YACjB,KAAK,KAAK,CAAC;gBACT,sBAAsB,cAAc,oBAAoB,IAAI;gBAC5D,mBAAmB,cAAc,iBAAiB,IAAI;gBACtD,qBAAqB,cAAc,mBAAmB,IAAI;gBAC1D,uBAAuB,cAAc,qBAAqB,IAAI;gBAC9D,cAAc,cAAc,YAAY,IAAI;gBAC5C,mBAAmB,cAAc,iBAAiB,IAAI;gBACtD,qBAAqB,cAAc,mBAAmB,IAAI;gBAC1D,YAAY,cAAc,UAAU,IAAI;gBACxC,6BACE,cAAc,2BAA2B,IAAI;gBAC/C,2BAA2B,cAAc,yBAAyB,IAAI;gBACtE,sCACE,cAAc,oCAAoC,IAAI;gBACxD,oCACE,cAAc,kCAAkC,IAAI;gBACtD,mCACE,cAAc,iCAAiC,IAAI;gBACrD,oBAAoB,cAAc,kBAAkB,IAAI;gBACxD,mBAAmB,cAAc,iBAAiB,IAAI;gBACtD,2BAA2B,cAAc,yBAAyB,IAAI;gBACtE,kBAAkB,cAAc,gBAAgB,IAAI;YACtD;QACF;IACF,GAAG;QAAC;QAAe;KAAK;IAExB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,4DAA4D;YAC5D;QACF;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAGF,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,yDAAyD;QACzD,OAAO;YACL;YACA;YACA,MAAM;gBAAE,qBAAqB;YAAK;QACpC;IACF,GACA;QAAC;QAAQ;QAAW;KAAS;IAG/B,qCAAqC;IACrC,MAAM,CACJ,sBACA,mBACA,6BACA,2BACA,kBACD,GAAG,KAAK,KAAK,CAAC;QACb;QACA;QACA;QACA;QACA;KACD;IAED,4CAA4C;IAC5C,MAAM,iBAAiB,CACrB,OACA,WACA,gBAAyB,IAAI;QAE7B,IAAI,eAAe;YACjB,OAAO,QAAQ,YAAY,gBAAgB;QAC7C;QACA,OAAO,QAAQ,YAAY,gBAAgB;IAC7C;IAEA,uCAAuC;IACvC,MAAM,kBAAkB,CAAC;QACvB,OAAO,WAAW,YAAY,YAAY;IAC5C;IAEA,oEAAoE;IACpE,IAAA,kNAAS,EAAC;QACR,IAAI,wBAAwB,oBAAoB,gBAAgB;YAC9D,MAAM,sBACJ,uBAAuB,CAAC,mBAAmB,cAAc,GAAG,IAAI;YAClE,KAAK,QAAQ,CAAC,uBAAuB;QACvC;IACF,GAAG;QAAC;QAAsB,oBAAoB;QAAgB;KAAK;IAEnE,IAAA,kNAAS,EAAC;QACR,IAAI,oBAAoB,uBAAuB;YAC7C,KAAK,QAAQ,CACX,yBACA,mBAAmB,qBAAqB;QAE5C;IACF,GAAG;QAAC,oBAAoB;QAAuB;KAAK;IAEpD,IAAA,kNAAS,EAAC;QACR,MAAM,sBAAsB,KAAK,SAAS,CAAC;QAC3C,MAAM,wBAAwB,KAAK,SAAS,CAAC;QAC7C,IAAI,uBAAuB,uBAAuB;YAChD,MAAM,eAAe,sBAAsB;YAC3C,KAAK,QAAQ,CAAC,gBAAgB;QAChC;IACF,GAAG;QAAC;KAAK;IAET,IAAA,kNAAS,EAAC;QACR,IACE,qBACA,oBAAoB,qBACpB,oBAAoB,kBACpB;YACA,MAAM,oBACJ,AAAC,IAAI,OAAO,oBACZ,CAAC,IACC,OACA,mBAAmB,iBAAiB,GACpC,OACA,mBAAmB,gBAAgB;YACvC,KAAK,QAAQ,CAAC,qBAAqB;QACrC;IACF,GAAG;QACD;QACA,oBAAoB;QACpB,oBAAoB;QACpB;KACD;IAED,IAAA,kNAAS,EAAC;QACR,IAAI,oBAAoB,qBAAqB;YAC3C,KAAK,QAAQ,CACX,uBACA,mBAAmB,mBAAmB;QAE1C;IACF,GAAG;QAAC,oBAAoB;QAAqB;KAAK;IAElD,IAAA,kNAAS,EAAC;QACR,MAAM,oBAAoB,KAAK,SAAS,CAAC;QACzC,MAAM,sBAAsB,KAAK,SAAS,CAAC;QAC3C,IAAI,qBAAqB,qBAAqB;YAC5C,MAAM,aAAa,oBAAoB;YACvC,KAAK,QAAQ,CAAC,cAAc;QAC9B;IACF,GAAG;QAAC;KAAK;IAET,0BAA0B;IAC1B,IAAA,kNAAS,EAAC;QACR,IACE,+BACA,oBAAoB,YACpB,oBAAoB,kCACpB,oBAAoB,qBACpB;YACA,MAAM,uCACJ,AAAC,MACC,CAAC,IACC,8BACA,mBAAmB,QAAQ,IAAI,CAAC,IACpC,CAAC,MACC,OACA,mBAAmB,8BAA8B,GACjD,mBAAmB,mBAAmB;YAC1C,KAAK,QAAQ,CACX,wCACA;QAEJ;IACF,GAAG;QACD;QACA,oBAAoB;QACpB,oBAAoB;QACpB,oBAAoB;QACpB;KACD;IAED,IAAA,kNAAS,EAAC;QACR,IACE,6BACA,oBAAoB,YACpB,oBAAoB,kCACpB,oBAAoB,qBACpB;YACA,MAAM,qCACJ,AAAC,MACC,CAAC,IAAI,4BAA4B,mBAAmB,QAAQ,IAAI,CAAC,IACnE,CAAC,MACC,OACA,mBAAmB,8BAA8B,GACjD,mBAAmB,mBAAmB;YAC1C,KAAK,QAAQ,CACX,sCACA;QAEJ;IACF,GAAG;QACD;QACA,oBAAoB;QACpB,oBAAoB;QACpB,oBAAoB;QACpB;KACD;IAED,IAAA,kNAAS,EAAC;QACR,MAAM,uCAAuC,KAAK,SAAS,CACzD;QAEF,MAAM,qCAAqC,KAAK,SAAS,CACvD;QAEF,IACE,wCACA,oCACA;YACA,mEAAmE;YACnE,MAAM,oCACJ,uCACA,qCACA;YACF,KAAK,QAAQ,CACX,qCACA;QAEJ;IACF,GAAG;QAAC;QAAM;KAAmB;IAE7B,IAAA,kNAAS,EAAC;QACR,MAAM,oCAAoC,KAAK,SAAS,CACtD;QAEF,IAAI,qCAAqC,oBAAoB,UAAU;YACrE,MAAM,qBACJ,AAAC,MAAM,mBAAmB,QAAQ,GAAI;YACxC,KAAK,QAAQ,CAAC,sBAAsB;QACtC;IACF,GAAG;QAAC,oBAAoB;QAAU;KAAK;IAEvC,IAAA,kNAAS,EAAC;QACR,MAAM,uCAAuC,KAAK,SAAS,CACzD;QAEF,MAAM,qCAAqC,KAAK,SAAS,CACvD;QAEF,IACE,wCACA,sCACA,qBACA,oBAAoB,qBACpB;YACA,mEAAmE;YACnE,MAAM,4BACJ,uCACE,CAAC,IAAI,mBAAmB,mBAAmB,IAC7C,qCACE,CAAC,IAAI,oBAAoB,mBAAmB,mBAAmB,IACjE;YACF,KAAK,QAAQ,CAAC,6BAA6B;QAC7C;IACF,GAAG;QACD;QACA,oBAAoB;QACpB;QACA;KACD;IAED,IAAA,kNAAS,EAAC;QACR,MAAM,4BAA4B,KAAK,SAAS,CAC9C;QAEF,IAAI,6BAA6B,oBAAoB,UAAU;YAC7D,MAAM,mBACJ,AAAC,MAAM,mBAAmB,QAAQ,GAAI;YACxC,KAAK,QAAQ,CAAC,oBAAoB;QACpC;IACF,GAAG;QAAC,oBAAoB;QAAU;KAAK;IAEvC,qBACE,8OAAC,wIAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,8OAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAG5B,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,8OAAC,kLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAIZ,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kJAAS;;;;;sCACV,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;4BACV,eAAe;;;;;;sCAEjB,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;4BACV,eAAe;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kLAAe;oCACd,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,GAAG;oCACH,UAAU;oCACV,yBAAwB;oCACxB,eAAe;;;;;;8CAEjB,8OAAC,0IAAK;oCACJ,SAAS,gBACP,eAAe,KAAK,KAAK,CAAC,iBAAiB;oCAE7C,WAAU;8CAET,eAAe,KAAK,KAAK,CAAC,iBAAiB;;;;;;;;;;;;;;;;;;8BAMlD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kJAAS;;;;;sCACV,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;4BACV,eAAe;;;;;;sCAEjB,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;4BACV,eAAe;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kLAAe;oCACd,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,GAAG;oCACH,UAAU;oCACV,yBAAwB;oCACxB,eAAe;;;;;;8CAEjB,8OAAC,0IAAK;oCACJ,SAAS,gBACP,eAAe,KAAK,KAAK,CAAC,eAAe;oCAE3C,WAAU;8CAET,eAAe,KAAK,KAAK,CAAC,eAAe;;;;;;;;;;;;;;;;;;8BAMhD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kJAAS;;;;;sCACV,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;;;;;;;8BAKd,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;4BACV,eAAe;;;;;;sCAEjB,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;4BACV,eAAe;;;;;;sCAEjB,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;4BACV,eAAe;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kLAAe;oCACd,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,GAAG;oCACH,UAAU;oCACV,yBAAwB;oCACxB,eAAe;;;;;;8CAEjB,8OAAC,0IAAK;oCACJ,SAAS,gBACP,eAAe,KAAK,KAAK,CAAC,uBAAuB,KAAK;oCAExD,WAAU;8CAET,eAAe,KAAK,KAAK,CAAC,uBAAuB,KAAK;;;;;;;;;;;;;;;;;;8BAM7D,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;4BACV,UAAU;;;;;;sCAEZ,8OAAC,kLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;4BACV,eAAe;;;;;;sCAEjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kLAAe;oCACd,SAAS,KAAK,OAAO;oCACrB,MAAK;oCACL,GAAG;oCACH,UAAU;oCACV,yBAAwB;oCACxB,eAAe;;;;;;8CAEjB,8OAAC,0IAAK;oCACJ,SAAS,gBACP,eAAe,KAAK,KAAK,CAAC,qBAAqB,KAAK;oCAEtD,WAAU;8CAET,eAAe,KAAK,KAAK,CAAC,qBAAqB,KAAK;;;;;;;;;;;;;;;;;;8BAK3D,8OAAC,4IAAM;oBAAC,MAAK;oBAAS,WAAU;oBAAmB,UAAU;;wBAC1D,2BAAa,8OAAC,oOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}, {"offset": {"line": 1474, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-geometry-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport { useProductsQuery } from '@atlas/lib/api/products/use-products-query'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect, useMemo } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { z } from 'zod'\r\n\r\nconst formSchema = z.object({\r\n  productId: z.string().min(1, 'Product is required'),\r\n  stripWidth: z.number().min(0.1, 'Strip width must be greater than 0'),\r\n  equivalentThickness: z\r\n    .number()\r\n    .min(0.001, 'Equivalent thickness must be greater than 0'),\r\n  layersNumber: z.number().min(1, 'Layers number must be at least 1'),\r\n  expositionType: z.enum(['INTERNAL', 'EXTERNAL', 'AGGRESSIVE']),\r\n  environmentalConversionFactor: z\r\n    .number()\r\n    .min(0, 'Environmental conversion factor must be positive'),\r\n})\r\n\r\ntype FormSchema = z.infer<typeof formSchema>\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FormSchema>\r\n  preInterventionData?: any\r\n  materialProperties?: any\r\n  geometryProperties?: any\r\n  onSave?: () => void\r\n  onGeometryChange?: (geometry: Partial<FormSchema>) => void\r\n  initialDeformation?: any\r\n}\r\n\r\nconst EXPOSITION_TYPE_OPTIONS = [\r\n  { value: 'INTERNAL', label: 'Internal' },\r\n  { value: 'EXTERNAL', label: 'External' },\r\n  { value: 'AGGRESSIVE', label: 'Aggressive' },\r\n]\r\n\r\nexport const WoodCompositeGeometryForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  preInterventionData: _preInterventionData,\r\n  materialProperties,\r\n  geometryProperties,\r\n  onSave,\r\n  onGeometryChange,\r\n  initialDeformation,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.wood.compositeGeometry')\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  const { data: productsData, isLoading: isLoadingProducts } = useProductsQuery(\r\n    'wood',\r\n    session.accessToken,\r\n  )\r\n\r\n  const productOptions = useMemo(() => {\r\n    if (!productsData?.content) {\r\n      return []\r\n    }\r\n    return productsData.content\r\n      .filter(product => product.categories.includes('wood'))\r\n      .map(product => ({\r\n        value: product.id,\r\n        label: product.name,\r\n      }))\r\n  }, [productsData])\r\n\r\n  const form = useForm<FormSchema>({\r\n    resolver: zodResolver(formSchema),\r\n    defaultValues: {\r\n      productId: defaultValues?.productId || '',\r\n      stripWidth: defaultValues?.stripWidth || 0,\r\n      equivalentThickness: defaultValues?.equivalentThickness || 0,\r\n      layersNumber: defaultValues?.layersNumber || 2.25,\r\n      expositionType: defaultValues?.expositionType || 'INTERNAL',\r\n      environmentalConversionFactor:\r\n        defaultValues?.environmentalConversionFactor || 0.95,\r\n    },\r\n  })\r\n\r\n  // Reset form when defaultValues change (for saved data after refresh)\r\n  useEffect(() => {\r\n    if (defaultValues) {\r\n      form.reset({\r\n        productId: defaultValues.productId ?? '',\r\n        stripWidth: defaultValues.stripWidth ?? 0,\r\n        equivalentThickness: defaultValues.equivalentThickness ?? 0,\r\n        layersNumber: defaultValues.layersNumber ?? 2.25,\r\n        expositionType: defaultValues.expositionType ?? 'INTERNAL',\r\n        environmentalConversionFactor:\r\n          defaultValues.environmentalConversionFactor ?? 0.95,\r\n      })\r\n    }\r\n  }, [defaultValues, form])\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(session.accessToken)\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (data: FormSchema) => {\r\n      // Save as part of postIntervationCheck structure with all inherited data\r\n      mutate({\r\n        projectId,\r\n        moduleId,\r\n        body: {\r\n          postIntervationCheck: {\r\n            // Include inherited data from pre-intervention\r\n            initialDeformation: initialDeformation || 0,\r\n            materialProperties: materialProperties || null,\r\n            geometry: geometryProperties || null,\r\n            // Include the current form data\r\n            compositeGeometry: data,\r\n          },\r\n        } as any,\r\n      })\r\n      onSave?.()\r\n    },\r\n    [\r\n      mutate,\r\n      projectId,\r\n      moduleId,\r\n      onSave,\r\n      materialProperties,\r\n      geometryProperties,\r\n      initialDeformation,\r\n    ],\r\n  )\r\n\r\n  // Watch for exposition type changes to update environmental conversion factor\r\n  const expositionType = form.watch('expositionType')\r\n  const selectedProductId = form.watch('productId')\r\n\r\n  useEffect(() => {\r\n    let factor = 0.85 // default for external and aggressive\r\n    if (expositionType === 'INTERNAL') {\r\n      factor = 0.95\r\n    }\r\n    form.setValue('environmentalConversionFactor', factor)\r\n  }, [expositionType, form])\r\n\r\n  // Update equivalent thickness when product changes\r\n  useEffect(() => {\r\n    if (selectedProductId && productsData?.content) {\r\n      const selectedProduct = productsData.content.find(\r\n        p => p.id === selectedProductId,\r\n      )\r\n      if (selectedProduct) {\r\n        form.setValue('equivalentThickness', selectedProduct.thickness)\r\n      }\r\n    }\r\n  }, [selectedProductId, productsData, form])\r\n\r\n  // Watch specific form values and notify parent component of changes\r\n  const stripWidth = form.watch('stripWidth')\r\n  const layersNumber = form.watch('layersNumber')\r\n  const equivalentThickness = form.watch('equivalentThickness')\r\n  const environmentalConversionFactor = form.watch(\r\n    'environmentalConversionFactor',\r\n  )\r\n\r\n  useEffect(() => {\r\n    // Only notify parent if we have a valid product selection and the callback exists\r\n    if (onGeometryChange && selectedProductId && stripWidth > 0) {\r\n      const geometryData = {\r\n        productId: selectedProductId,\r\n        stripWidth,\r\n        layersNumber,\r\n        equivalentThickness,\r\n        expositionType,\r\n        environmentalConversionFactor,\r\n      }\r\n      onGeometryChange(geometryData)\r\n    }\r\n  }, [\r\n    selectedProductId,\r\n    stripWidth,\r\n    layersNumber,\r\n    equivalentThickness,\r\n    expositionType,\r\n    environmentalConversionFactor,\r\n    onGeometryChange,\r\n  ])\r\n\r\n  return (\r\n    <div className=\"flex flex-col 2xl:flex-row justify-center gap-2\">\r\n      <Form {...form}>\r\n        <form\r\n          className=\"space-y-4 rounded-md border p-4 grow\"\r\n          onSubmit={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          <SelectFormInput\r\n            control={form.control}\r\n            name=\"productId\"\r\n            options={productOptions}\r\n            t={t}\r\n            loading={isLoadingProducts}\r\n            required={true}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"stripWidth\"\r\n            t={t}\r\n            required={true}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"equivalentThickness\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"layersNumber\"\r\n            t={t}\r\n            required={true}\r\n          />\r\n\r\n          <SelectFormInput\r\n            control={form.control}\r\n            name=\"expositionType\"\r\n            options={EXPOSITION_TYPE_OPTIONS}\r\n            t={t}\r\n            required={true}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"environmentalConversionFactor\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full sm:w-auto\"\r\n            disabled={isPending}\r\n          >\r\n            {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n            {tCommon('save')}\r\n          </Button>\r\n        </form>\r\n      </Form>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;AAEA,MAAM,aAAa,kLAAC,CAAC,MAAM,CAAC;IAC1B,WAAW,kLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,YAAY,kLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK;IAChC,qBAAqB,kLAAC,CACnB,MAAM,GACN,GAAG,CAAC,OAAO;IACd,cAAc,kLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAChC,gBAAgB,kLAAC,CAAC,IAAI,CAAC;QAAC;QAAY;QAAY;KAAa;IAC7D,+BAA+B,kLAAC,CAC7B,MAAM,GACN,GAAG,CAAC,GAAG;AACZ;AAiBA,MAAM,0BAA0B;IAC9B;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAc,OAAO;IAAa;CAC5C;AAEM,MAAM,4BAA4B,CAAC,EACxC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,qBAAqB,oBAAoB,EACzC,kBAAkB,EAClB,kBAAkB,EAClB,MAAM,EACN,gBAAgB,EAChB,kBAAkB,EACZ;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAEhC,MAAM,EAAE,MAAM,YAAY,EAAE,WAAW,iBAAiB,EAAE,GAAG,IAAA,6KAAgB,EAC3E,QACA,QAAQ,WAAW;IAGrB,MAAM,iBAAiB,IAAA,gNAAO,EAAC;QAC7B,IAAI,CAAC,cAAc,SAAS;YAC1B,OAAO,EAAE;QACX;QACA,OAAO,aAAa,OAAO,CACxB,MAAM,CAAC,CAAA,UAAW,QAAQ,UAAU,CAAC,QAAQ,CAAC,SAC9C,GAAG,CAAC,CAAA,UAAW,CAAC;gBACf,OAAO,QAAQ,EAAE;gBACjB,OAAO,QAAQ,IAAI;YACrB,CAAC;IACL,GAAG;QAAC;KAAa;IAEjB,MAAM,OAAO,IAAA,yKAAO,EAAa;QAC/B,UAAU,IAAA,6KAAW,EAAC;QACtB,eAAe;YACb,WAAW,eAAe,aAAa;YACvC,YAAY,eAAe,cAAc;YACzC,qBAAqB,eAAe,uBAAuB;YAC3D,cAAc,eAAe,gBAAgB;YAC7C,gBAAgB,eAAe,kBAAkB;YACjD,+BACE,eAAe,iCAAiC;QACpD;IACF;IAEA,sEAAsE;IACtE,IAAA,kNAAS,EAAC;QACR,IAAI,eAAe;YACjB,KAAK,KAAK,CAAC;gBACT,WAAW,cAAc,SAAS,IAAI;gBACtC,YAAY,cAAc,UAAU,IAAI;gBACxC,qBAAqB,cAAc,mBAAmB,IAAI;gBAC1D,cAAc,cAAc,YAAY,IAAI;gBAC5C,gBAAgB,cAAc,cAAc,IAAI;gBAChD,+BACE,cAAc,6BAA6B,IAAI;YACnD;QACF;IACF,GAAG;QAAC;QAAe;KAAK;IAExB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EAAC,QAAQ,WAAW;IAE7E,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,yEAAyE;QACzE,OAAO;YACL;YACA;YACA,MAAM;gBACJ,sBAAsB;oBACpB,+CAA+C;oBAC/C,oBAAoB,sBAAsB;oBAC1C,oBAAoB,sBAAsB;oBAC1C,UAAU,sBAAsB;oBAChC,gCAAgC;oBAChC,mBAAmB;gBACrB;YACF;QACF;QACA;IACF,GACA;QACE;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAGH,8EAA8E;IAC9E,MAAM,iBAAiB,KAAK,KAAK,CAAC;IAClC,MAAM,oBAAoB,KAAK,KAAK,CAAC;IAErC,IAAA,kNAAS,EAAC;QACR,IAAI,SAAS,KAAK,sCAAsC;;QACxD,IAAI,mBAAmB,YAAY;YACjC,SAAS;QACX;QACA,KAAK,QAAQ,CAAC,iCAAiC;IACjD,GAAG;QAAC;QAAgB;KAAK;IAEzB,mDAAmD;IACnD,IAAA,kNAAS,EAAC;QACR,IAAI,qBAAqB,cAAc,SAAS;YAC9C,MAAM,kBAAkB,aAAa,OAAO,CAAC,IAAI,CAC/C,CAAA,IAAK,EAAE,EAAE,KAAK;YAEhB,IAAI,iBAAiB;gBACnB,KAAK,QAAQ,CAAC,uBAAuB,gBAAgB,SAAS;YAChE;QACF;IACF,GAAG;QAAC;QAAmB;QAAc;KAAK;IAE1C,oEAAoE;IACpE,MAAM,aAAa,KAAK,KAAK,CAAC;IAC9B,MAAM,eAAe,KAAK,KAAK,CAAC;IAChC,MAAM,sBAAsB,KAAK,KAAK,CAAC;IACvC,MAAM,gCAAgC,KAAK,KAAK,CAC9C;IAGF,IAAA,kNAAS,EAAC;QACR,kFAAkF;QAClF,IAAI,oBAAoB,qBAAqB,aAAa,GAAG;YAC3D,MAAM,eAAe;gBACnB,WAAW;gBACX;gBACA;gBACA;gBACA;gBACA;YACF;YACA,iBAAiB;QACnB;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,wIAAI;YAAE,GAAG,IAAI;sBACZ,cAAA,8OAAC;gBACC,WAAU;gBACV,UAAU,KAAK,YAAY,CAAC;;kCAE5B,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,SAAS;wBACT,GAAG;wBACH,SAAS;wBACT,UAAU;;;;;;kCAGZ,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;;;;;;kCAGZ,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAGjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;;;;;;kCAGZ,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,SAAS;wBACT,GAAG;wBACH,UAAU;;;;;;kCAGZ,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAGjB,8OAAC,4IAAM;wBACL,MAAK;wBACL,WAAU;wBACV,UAAU;;4BAET,2BAAa,8OAAC,oOAAW;gCAAC,WAAU;;;;;;4BACpC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;AAMrB", "debugId": null}}, {"offset": {"line": 1766, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport { useProductsQuery } from '@atlas/lib/api/products/use-products-query'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { z } from 'zod'\r\n\r\nconst formSchema = z.object({\r\n  frpElasticityModulus: z\r\n    .number()\r\n    .min(0, 'FRP elasticity modulus must be positive'),\r\n  frpDesignMaximumStrain: z\r\n    .number()\r\n    .min(0, 'FRP design maximum strain must be positive'),\r\n  frpCharacteristicStrain: z\r\n    .number()\r\n    .min(0, 'FRP characteristic strain must be positive'),\r\n  frpPartialFactorInUls: z\r\n    .number()\r\n    .min(0, 'FRP partial factor in ULS must be positive'),\r\n  frpMaximumStrainForDebonding: z\r\n    .number()\r\n    .min(0, 'FRP maximum strain for debonding must be positive'),\r\n  loadConditionFactor: z\r\n    .number()\r\n    .min(0, 'Load condition factor must be positive'),\r\n  frpPartialFactorInUlsForDebonding: z\r\n    .number()\r\n    .min(0, 'FRP partial factor in ULS for debonding must be positive'),\r\n  reinforcementToSectionWidthRatio: z\r\n    .number()\r\n    .min(0, 'Reinforcement to section width ratio must be positive'),\r\n  reinforcementToSectionWidthRatioUsefull: z\r\n    .number()\r\n    .min(0, 'Reinforcement to section width ratio useful must be positive'),\r\n  geometricCorrectionFactor: z\r\n    .number()\r\n    .min(0, 'Geometric correction factor must be positive'),\r\n  geometricCorrectionFactorUsefull: z\r\n    .number()\r\n    .min(0, 'Geometric correction factor useful must be positive'),\r\n  experimentalCorrectionFactor: z\r\n    .number()\r\n    .min(0, 'Experimental correction factor must be positive'),\r\n  confidenceFactor: z.number().min(0, 'Confidence factor must be positive'),\r\n  sectionModulus: z.number().min(0, 'Section modulus must be positive'),\r\n  momentOfInertiaAboutY: z\r\n    .number()\r\n    .min(0, 'Moment of inertia about Y must be positive'),\r\n})\r\n\r\ntype FormSchema = z.infer<typeof formSchema>\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FormSchema>\r\n  compositeGeometry?: any\r\n  preInterventionData?: any\r\n  materialProperties?: any\r\n  geometryProperties?: any\r\n  onSave?: () => void\r\n  onPropertiesChange?: (properties: any) => void\r\n  initialDeformation?: any\r\n}\r\n\r\nexport const WoodCompositePropertiesForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  compositeGeometry,\r\n  preInterventionData: _preInterventionData,\r\n  materialProperties,\r\n  geometryProperties,\r\n  onSave,\r\n  onPropertiesChange,\r\n  initialDeformation,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.wood.compositeProperties')\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  const { data: productsData } = useProductsQuery('wood', session.accessToken)\r\n\r\n  const form = useForm<FormSchema>({\r\n    resolver: zodResolver(formSchema),\r\n    defaultValues: {\r\n      frpElasticityModulus: defaultValues?.frpElasticityModulus || 0,\r\n      frpDesignMaximumStrain: defaultValues?.frpDesignMaximumStrain || 0,\r\n      frpCharacteristicStrain: defaultValues?.frpCharacteristicStrain || 0,\r\n      frpPartialFactorInUls: defaultValues?.frpPartialFactorInUls || 1.1,\r\n      frpMaximumStrainForDebonding:\r\n        defaultValues?.frpMaximumStrainForDebonding || 0,\r\n      loadConditionFactor: defaultValues?.loadConditionFactor || 1.25,\r\n      frpPartialFactorInUlsForDebonding:\r\n        defaultValues?.frpPartialFactorInUlsForDebonding || 1.2,\r\n      reinforcementToSectionWidthRatio:\r\n        defaultValues?.reinforcementToSectionWidthRatio || 0,\r\n      reinforcementToSectionWidthRatioUsefull:\r\n        defaultValues?.reinforcementToSectionWidthRatioUsefull || 0,\r\n      geometricCorrectionFactor: defaultValues?.geometricCorrectionFactor || 0,\r\n      geometricCorrectionFactorUsefull:\r\n        defaultValues?.geometricCorrectionFactorUsefull || 0,\r\n      experimentalCorrectionFactor:\r\n        defaultValues?.experimentalCorrectionFactor || 0.1,\r\n      confidenceFactor: defaultValues?.confidenceFactor || 1.35,\r\n      sectionModulus: defaultValues?.sectionModulus || 0,\r\n      momentOfInertiaAboutY: defaultValues?.momentOfInertiaAboutY || 0,\r\n    },\r\n  })\r\n\r\n  // Reset form when defaultValues change (for saved data after refresh)\r\n  useEffect(() => {\r\n    if (defaultValues) {\r\n      form.reset({\r\n        frpElasticityModulus: defaultValues.frpElasticityModulus ?? 0,\r\n        frpDesignMaximumStrain: defaultValues.frpDesignMaximumStrain ?? 0,\r\n        frpCharacteristicStrain: defaultValues.frpCharacteristicStrain ?? 0,\r\n        frpPartialFactorInUls: defaultValues.frpPartialFactorInUls ?? 1.1,\r\n        frpMaximumStrainForDebonding:\r\n          defaultValues.frpMaximumStrainForDebonding ?? 0,\r\n        loadConditionFactor: defaultValues.loadConditionFactor ?? 1.25,\r\n        frpPartialFactorInUlsForDebonding:\r\n          defaultValues.frpPartialFactorInUlsForDebonding ?? 1.2,\r\n        reinforcementToSectionWidthRatio:\r\n          defaultValues.reinforcementToSectionWidthRatio ?? 0,\r\n        reinforcementToSectionWidthRatioUsefull:\r\n          defaultValues.reinforcementToSectionWidthRatioUsefull ?? 0,\r\n        geometricCorrectionFactor: defaultValues.geometricCorrectionFactor ?? 0,\r\n        geometricCorrectionFactorUsefull:\r\n          defaultValues.geometricCorrectionFactorUsefull ?? 0,\r\n        experimentalCorrectionFactor:\r\n          defaultValues.experimentalCorrectionFactor ?? 0.1,\r\n        confidenceFactor: defaultValues.confidenceFactor ?? 1.35,\r\n        sectionModulus: defaultValues.sectionModulus ?? 0,\r\n        momentOfInertiaAboutY: defaultValues.momentOfInertiaAboutY ?? 0,\r\n      })\r\n    }\r\n  }, [defaultValues, form])\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(session.accessToken)\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (data: FormSchema) => {\r\n      // Save as part of postIntervationCheck structure with all inherited data\r\n      mutate({\r\n        projectId,\r\n        moduleId,\r\n        body: {\r\n          postIntervationCheck: {\r\n            // Include inherited data from pre-intervention\r\n            initialDeformation: initialDeformation || 0,\r\n            materialProperties: materialProperties || null,\r\n            geometry: geometryProperties || null,\r\n            // Include composite geometry if available\r\n            compositeGeometry: compositeGeometry || null,\r\n            // Include the current form data\r\n            compositeProperties: data,\r\n          },\r\n        } as any,\r\n      })\r\n      onSave?.()\r\n    },\r\n    [\r\n      mutate,\r\n      projectId,\r\n      moduleId,\r\n      onSave,\r\n      materialProperties,\r\n      geometryProperties,\r\n      compositeGeometry,\r\n      initialDeformation,\r\n    ],\r\n  )\r\n\r\n  // Calculate derived values when dependencies change\r\n  useEffect(() => {\r\n    if (\r\n      !compositeGeometry ||\r\n      !materialProperties ||\r\n      !geometryProperties ||\r\n      !productsData?.content\r\n    ) {\r\n      return\r\n    }\r\n\r\n    const selectedProduct = productsData.content.find(\r\n      p => p.id === compositeGeometry.productId,\r\n    )\r\n    if (!selectedProduct) {\r\n      return\r\n    }\r\n\r\n    // Set FRP elasticity modulus from selected product\r\n    form.setValue('frpElasticityModulus', selectedProduct.elasticModulus)\r\n\r\n    // Set FRP characteristic strain from selected product\r\n    form.setValue('frpCharacteristicStrain', selectedProduct.systemDeformation)\r\n\r\n    // Calculate reinforcement to section width ratio\r\n    // Formula: strip_width (mm) / beamSectionWidth (m) - convert stripWidth to meters\r\n\r\n    const reinforcementRatio =\r\n      compositeGeometry.stripWidth / 1000 / geometryProperties.beamSectionWidth\r\n    form.setValue('reinforcementToSectionWidthRatio', reinforcementRatio)\r\n\r\n    // Calculate reinforcement to section width ratio useful\r\n    // If ratio < 0.25, use 0.25; otherwise use the ratio (corrected logic)\r\n    const reinforcementRatioUseful =\r\n      reinforcementRatio < 0.25 ? 0.25 : reinforcementRatio\r\n    form.setValue(\r\n      'reinforcementToSectionWidthRatioUsefull',\r\n      reinforcementRatioUseful,\r\n    )\r\n\r\n    // Calculate geometric correction factor\r\n    const geometricFactor =\r\n      ((2 - reinforcementRatioUseful) / (1 + reinforcementRatioUseful)) ** 0.5\r\n    form.setValue('geometricCorrectionFactor', geometricFactor)\r\n\r\n    // Calculate geometric correction factor useful\r\n    // If factor > 1, use the factor; otherwise use 1 (as per specification)\r\n    const geometricFactorUseful = geometricFactor > 1 ? geometricFactor : 1\r\n    form.setValue('geometricCorrectionFactorUsefull', geometricFactorUseful)\r\n\r\n    // Calculate moment of inertia about Y\r\n    const momentOfInertia =\r\n      (geometryProperties.beamSectionWidth *\r\n        geometryProperties.beamSectionHeight ** 3) /\r\n        12 +\r\n      (((((((compositeGeometry.layersNumber *\r\n        compositeGeometry.equivalentThickness) /\r\n        1000) *\r\n        compositeGeometry.stripWidth) /\r\n        1000) *\r\n        selectedProduct.elasticModulus) /\r\n        materialProperties.meanElasticityModulus) *\r\n        geometryProperties.beamSectionHeight ** 2) /\r\n        2\r\n    form.setValue('momentOfInertiaAboutY', momentOfInertia)\r\n\r\n    // Calculate section modulus\r\n    const sectionMod =\r\n      momentOfInertia / (geometryProperties.beamSectionHeight / 2)\r\n    form.setValue('sectionModulus', sectionMod)\r\n\r\n    // Calculate FRP maximum strain for debonding\r\n    // Formula: load_condition_factor/(frp_elasticity_modulus*frp_partial_factor_in_uls_for_debonding)*√[(frp_elasticity_modulus*2*geometric_correction_factor_usefull*experimental_correction_factor)/(layers_number*equivalent_thickness*confidence_factor)*√(characteristicTensileStrength*characteristicCompressiveStrength)]\r\n    const loadConditionFactor = form.getValues('loadConditionFactor')\r\n    const frpPartialFactorInUlsForDebonding = form.getValues(\r\n      'frpPartialFactorInUlsForDebonding',\r\n    )\r\n    const experimentalCorrectionFactor = form.getValues(\r\n      'experimentalCorrectionFactor',\r\n    )\r\n    const confidenceFactor = form.getValues('confidenceFactor')\r\n\r\n    const maxStrainDebonding =\r\n      (loadConditionFactor /\r\n        (selectedProduct.elasticModulus * frpPartialFactorInUlsForDebonding)) *\r\n      Math.sqrt(\r\n        (selectedProduct.elasticModulus *\r\n          2 *\r\n          geometricFactor *\r\n          experimentalCorrectionFactor *\r\n          Math.sqrt(\r\n            materialProperties.characteristicTensileStrength *\r\n              materialProperties.characteristicCompressiveStrength,\r\n          )) /\r\n          (compositeGeometry.layersNumber *\r\n            compositeGeometry.equivalentThickness *\r\n            confidenceFactor),\r\n      )\r\n\r\n    form.setValue('frpMaximumStrainForDebonding', maxStrainDebonding)\r\n\r\n    // STEP 9: Calculate frp_design_maximum_strain\r\n    // Formula: MIN(environmental_conversion_factor * frp_characteristic_strain / frp_partial_factor_in_uls, frp_maximum_strain_for_debonding)\r\n\r\n    // Get the required values\r\n    const environmentalConversionFactor =\r\n      compositeGeometry.environmentalConversionFactor\r\n    const frpCharacteristicStrain = selectedProduct.systemDeformation // This is frp_characteristic_strain\r\n    const frpPartialFactorInUls = form.getValues('frpPartialFactorInUls')\r\n\r\n    // Calculate the first part of MIN function\r\n    const designStrainFirstPart =\r\n      (environmentalConversionFactor * frpCharacteristicStrain) /\r\n      frpPartialFactorInUls\r\n\r\n    // Calculate the MIN of both values\r\n    const designMaxStrain = Math.min(designStrainFirstPart, maxStrainDebonding)\r\n\r\n    form.setValue('frpDesignMaximumStrain', designMaxStrain)\r\n\r\n    // Notify parent component of the calculated properties\r\n    if (onPropertiesChange) {\r\n      const currentProperties = {\r\n        frpElasticityModulus: selectedProduct.elasticModulus,\r\n        frpDesignMaximumStrain: designMaxStrain,\r\n        frpCharacteristicStrain: frpCharacteristicStrain,\r\n        frpPartialFactorInUls: form.getValues('frpPartialFactorInUls'),\r\n        frpMaximumStrainForDebonding: maxStrainDebonding,\r\n        loadConditionFactor: form.getValues('loadConditionFactor'),\r\n        frpPartialFactorInUlsForDebonding: form.getValues(\r\n          'frpPartialFactorInUlsForDebonding',\r\n        ),\r\n        reinforcementToSectionWidthRatio: reinforcementRatio,\r\n        reinforcementToSectionWidthRatioUsefull: reinforcementRatioUseful,\r\n        geometricCorrectionFactor: geometricFactor,\r\n        geometricCorrectionFactorUsefull: geometricFactorUseful,\r\n        experimentalCorrectionFactor: form.getValues(\r\n          'experimentalCorrectionFactor',\r\n        ),\r\n        confidenceFactor: form.getValues('confidenceFactor'),\r\n        sectionModulus: sectionMod,\r\n        momentOfInertiaAboutY: momentOfInertia,\r\n      }\r\n      onPropertiesChange(currentProperties)\r\n    }\r\n  }, [\r\n    compositeGeometry,\r\n    materialProperties,\r\n    geometryProperties,\r\n    productsData,\r\n    form,\r\n    onPropertiesChange,\r\n  ])\r\n\r\n  // Watch for changes in user input fields and recalculate dependent values\r\n  const _watchedValues = form.watch([\r\n    'frpPartialFactorInUls',\r\n    'loadConditionFactor',\r\n    'frpPartialFactorInUlsForDebonding',\r\n    'experimentalCorrectionFactor',\r\n    'confidenceFactor',\r\n  ])\r\n\r\n  useEffect(() => {\r\n    if (\r\n      !compositeGeometry ||\r\n      !materialProperties ||\r\n      !geometryProperties ||\r\n      !productsData?.content\r\n    ) {\r\n      return\r\n    }\r\n\r\n    const selectedProduct = productsData.content.find(\r\n      p => p.id === compositeGeometry.productId,\r\n    )\r\n    if (!selectedProduct) {\r\n      return\r\n    }\r\n\r\n    // Get current values\r\n    const geometricFactor = form.getValues('geometricCorrectionFactor')\r\n    const loadConditionFactor = form.getValues('loadConditionFactor')\r\n    const frpPartialFactorInUlsForDebonding = form.getValues(\r\n      'frpPartialFactorInUlsForDebonding',\r\n    )\r\n    const experimentalCorrectionFactor = form.getValues(\r\n      'experimentalCorrectionFactor',\r\n    )\r\n    const confidenceFactor = form.getValues('confidenceFactor')\r\n    const frpPartialFactorInUls = form.getValues('frpPartialFactorInUls')\r\n\r\n    // Recalculate FRP maximum strain for debonding when user inputs change\r\n    // Using the EXACT formula: (K / (E * gamma)) * Math.sqrt((E * 2 * kappa * eta * Math.sqrt(ft * fc)) / (n * t_eq * Cf))\r\n    const maxStrainDebonding =\r\n      (loadConditionFactor /\r\n        (selectedProduct.elasticModulus * frpPartialFactorInUlsForDebonding)) *\r\n      Math.sqrt(\r\n        (selectedProduct.elasticModulus *\r\n          2 *\r\n          geometricFactor *\r\n          experimentalCorrectionFactor *\r\n          Math.sqrt(\r\n            materialProperties.characteristicTensileStrength *\r\n              materialProperties.characteristicCompressiveStrength,\r\n          )) /\r\n          (compositeGeometry.layersNumber *\r\n            compositeGeometry.equivalentThickness *\r\n            confidenceFactor),\r\n      )\r\n\r\n    form.setValue('frpMaximumStrainForDebonding', maxStrainDebonding)\r\n\r\n    // Recalculate FRP design maximum strain\r\n    const recalcDesignStrainFirstPart =\r\n      (compositeGeometry.environmentalConversionFactor *\r\n        selectedProduct.systemDeformation) /\r\n      frpPartialFactorInUls\r\n    const designMaxStrain = Math.min(\r\n      recalcDesignStrainFirstPart,\r\n      maxStrainDebonding,\r\n    )\r\n\r\n    form.setValue('frpDesignMaximumStrain', designMaxStrain)\r\n\r\n    // Notify parent component of the updated properties\r\n    if (onPropertiesChange) {\r\n      const currentProperties = {\r\n        frpElasticityModulus: selectedProduct.elasticModulus,\r\n        frpDesignMaximumStrain: designMaxStrain,\r\n        frpCharacteristicStrain: selectedProduct.systemDeformation,\r\n        frpPartialFactorInUls: frpPartialFactorInUls,\r\n        frpMaximumStrainForDebonding: maxStrainDebonding,\r\n        loadConditionFactor: loadConditionFactor,\r\n        frpPartialFactorInUlsForDebonding: frpPartialFactorInUlsForDebonding,\r\n        reinforcementToSectionWidthRatio: form.getValues(\r\n          'reinforcementToSectionWidthRatio',\r\n        ),\r\n        reinforcementToSectionWidthRatioUsefull: form.getValues(\r\n          'reinforcementToSectionWidthRatioUsefull',\r\n        ),\r\n        geometricCorrectionFactor: geometricFactor,\r\n        geometricCorrectionFactorUsefull: form.getValues(\r\n          'geometricCorrectionFactorUsefull',\r\n        ),\r\n        experimentalCorrectionFactor: experimentalCorrectionFactor,\r\n        confidenceFactor: confidenceFactor,\r\n        sectionModulus: form.getValues('sectionModulus'),\r\n        momentOfInertiaAboutY: form.getValues('momentOfInertiaAboutY'),\r\n      }\r\n      onPropertiesChange(currentProperties)\r\n    }\r\n  }, [\r\n    compositeGeometry,\r\n    materialProperties,\r\n    geometryProperties,\r\n    productsData,\r\n    form,\r\n    onPropertiesChange,\r\n  ])\r\n\r\n  return (\r\n    <div className=\"flex flex-col 2xl:flex-row justify-center gap-2\">\r\n      <Form {...form}>\r\n        <form\r\n          className=\"space-y-4 rounded-md border p-4 grow\"\r\n          onSubmit={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"frpElasticityModulus\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"frpDesignMaximumStrain\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"frpCharacteristicStrain\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"frpPartialFactorInUls\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"frpMaximumStrainForDebonding\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"loadConditionFactor\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"frpPartialFactorInUlsForDebonding\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"reinforcementToSectionWidthRatio\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"reinforcementToSectionWidthRatioUsefull\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"geometricCorrectionFactor\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"geometricCorrectionFactorUsefull\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"experimentalCorrectionFactor\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"confidenceFactor\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"sectionModulus\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={6}\r\n          />\r\n\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"momentOfInertiaAboutY\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={6}\r\n          />\r\n\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full sm:w-auto\"\r\n            disabled={isPending}\r\n          >\r\n            {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n            {tCommon('save')}\r\n          </Button>\r\n        </form>\r\n      </Form>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;AAEA,MAAM,aAAa,kLAAC,CAAC,MAAM,CAAC;IAC1B,sBAAsB,kLAAC,CACpB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,wBAAwB,kLAAC,CACtB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,yBAAyB,kLAAC,CACvB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,uBAAuB,kLAAC,CACrB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,8BAA8B,kLAAC,CAC5B,MAAM,GACN,GAAG,CAAC,GAAG;IACV,qBAAqB,kLAAC,CACnB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,mCAAmC,kLAAC,CACjC,MAAM,GACN,GAAG,CAAC,GAAG;IACV,kCAAkC,kLAAC,CAChC,MAAM,GACN,GAAG,CAAC,GAAG;IACV,yCAAyC,kLAAC,CACvC,MAAM,GACN,GAAG,CAAC,GAAG;IACV,2BAA2B,kLAAC,CACzB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,kCAAkC,kLAAC,CAChC,MAAM,GACN,GAAG,CAAC,GAAG;IACV,8BAA8B,kLAAC,CAC5B,MAAM,GACN,GAAG,CAAC,GAAG;IACV,kBAAkB,kLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACpC,gBAAgB,kLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAClC,uBAAuB,kLAAC,CACrB,MAAM,GACN,GAAG,CAAC,GAAG;AACZ;AAkBO,MAAM,8BAA8B,CAAC,EAC1C,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,iBAAiB,EACjB,qBAAqB,oBAAoB,EACzC,kBAAkB,EAClB,kBAAkB,EAClB,MAAM,EACN,kBAAkB,EAClB,kBAAkB,EACZ;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAEhC,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,IAAA,6KAAgB,EAAC,QAAQ,QAAQ,WAAW;IAE3E,MAAM,OAAO,IAAA,yKAAO,EAAa;QAC/B,UAAU,IAAA,6KAAW,EAAC;QACtB,eAAe;YACb,sBAAsB,eAAe,wBAAwB;YAC7D,wBAAwB,eAAe,0BAA0B;YACjE,yBAAyB,eAAe,2BAA2B;YACnE,uBAAuB,eAAe,yBAAyB;YAC/D,8BACE,eAAe,gCAAgC;YACjD,qBAAqB,eAAe,uBAAuB;YAC3D,mCACE,eAAe,qCAAqC;YACtD,kCACE,eAAe,oCAAoC;YACrD,yCACE,eAAe,2CAA2C;YAC5D,2BAA2B,eAAe,6BAA6B;YACvE,kCACE,eAAe,oCAAoC;YACrD,8BACE,eAAe,gCAAgC;YACjD,kBAAkB,eAAe,oBAAoB;YACrD,gBAAgB,eAAe,kBAAkB;YACjD,uBAAuB,eAAe,yBAAyB;QACjE;IACF;IAEA,sEAAsE;IACtE,IAAA,kNAAS,EAAC;QACR,IAAI,eAAe;YACjB,KAAK,KAAK,CAAC;gBACT,sBAAsB,cAAc,oBAAoB,IAAI;gBAC5D,wBAAwB,cAAc,sBAAsB,IAAI;gBAChE,yBAAyB,cAAc,uBAAuB,IAAI;gBAClE,uBAAuB,cAAc,qBAAqB,IAAI;gBAC9D,8BACE,cAAc,4BAA4B,IAAI;gBAChD,qBAAqB,cAAc,mBAAmB,IAAI;gBAC1D,mCACE,cAAc,iCAAiC,IAAI;gBACrD,kCACE,cAAc,gCAAgC,IAAI;gBACpD,yCACE,cAAc,uCAAuC,IAAI;gBAC3D,2BAA2B,cAAc,yBAAyB,IAAI;gBACtE,kCACE,cAAc,gCAAgC,IAAI;gBACpD,8BACE,cAAc,4BAA4B,IAAI;gBAChD,kBAAkB,cAAc,gBAAgB,IAAI;gBACpD,gBAAgB,cAAc,cAAc,IAAI;gBAChD,uBAAuB,cAAc,qBAAqB,IAAI;YAChE;QACF;IACF,GAAG;QAAC;QAAe;KAAK;IAExB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EAAC,QAAQ,WAAW;IAE7E,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,yEAAyE;QACzE,OAAO;YACL;YACA;YACA,MAAM;gBACJ,sBAAsB;oBACpB,+CAA+C;oBAC/C,oBAAoB,sBAAsB;oBAC1C,oBAAoB,sBAAsB;oBAC1C,UAAU,sBAAsB;oBAChC,0CAA0C;oBAC1C,mBAAmB,qBAAqB;oBACxC,gCAAgC;oBAChC,qBAAqB;gBACvB;YACF;QACF;QACA;IACF,GACA;QACE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAGH,oDAAoD;IACpD,IAAA,kNAAS,EAAC;QACR,IACE,CAAC,qBACD,CAAC,sBACD,CAAC,sBACD,CAAC,cAAc,SACf;YACA;QACF;QAEA,MAAM,kBAAkB,aAAa,OAAO,CAAC,IAAI,CAC/C,CAAA,IAAK,EAAE,EAAE,KAAK,kBAAkB,SAAS;QAE3C,IAAI,CAAC,iBAAiB;YACpB;QACF;QAEA,mDAAmD;QACnD,KAAK,QAAQ,CAAC,wBAAwB,gBAAgB,cAAc;QAEpE,sDAAsD;QACtD,KAAK,QAAQ,CAAC,2BAA2B,gBAAgB,iBAAiB;QAE1E,iDAAiD;QACjD,kFAAkF;QAElF,MAAM,qBACJ,kBAAkB,UAAU,GAAG,OAAO,mBAAmB,gBAAgB;QAC3E,KAAK,QAAQ,CAAC,oCAAoC;QAElD,wDAAwD;QACxD,uEAAuE;QACvE,MAAM,2BACJ,qBAAqB,OAAO,OAAO;QACrC,KAAK,QAAQ,CACX,2CACA;QAGF,wCAAwC;QACxC,MAAM,kBACJ,CAAC,CAAC,IAAI,wBAAwB,IAAI,CAAC,IAAI,wBAAwB,CAAC,KAAK;QACvE,KAAK,QAAQ,CAAC,6BAA6B;QAE3C,+CAA+C;QAC/C,wEAAwE;QACxE,MAAM,wBAAwB,kBAAkB,IAAI,kBAAkB;QACtE,KAAK,QAAQ,CAAC,oCAAoC;QAElD,sCAAsC;QACtC,MAAM,kBACJ,AAAC,mBAAmB,gBAAgB,GAClC,mBAAmB,iBAAiB,IAAI,IACxC,KACF,AAAO,kBAAkB,YAAY,GACnC,kBAAkB,mBAAmB,GACrC,OACA,kBAAkB,UAAU,GAC5B,OACA,gBAAgB,cAAc,GAC9B,mBAAmB,qBAAqB,GACxC,mBAAmB,iBAAiB,IAAI,IACxC;QACJ,KAAK,QAAQ,CAAC,yBAAyB;QAEvC,4BAA4B;QAC5B,MAAM,aACJ,kBAAkB,CAAC,mBAAmB,iBAAiB,GAAG,CAAC;QAC7D,KAAK,QAAQ,CAAC,kBAAkB;QAEhC,6CAA6C;QAC7C,6TAA6T;QAC7T,MAAM,sBAAsB,KAAK,SAAS,CAAC;QAC3C,MAAM,oCAAoC,KAAK,SAAS,CACtD;QAEF,MAAM,+BAA+B,KAAK,SAAS,CACjD;QAEF,MAAM,mBAAmB,KAAK,SAAS,CAAC;QAExC,MAAM,qBACJ,AAAC,sBACC,CAAC,gBAAgB,cAAc,GAAG,iCAAiC,IACrE,KAAK,IAAI,CACP,AAAC,gBAAgB,cAAc,GAC7B,IACA,kBACA,+BACA,KAAK,IAAI,CACP,mBAAmB,6BAA6B,GAC9C,mBAAmB,iCAAiC,IAExD,CAAC,kBAAkB,YAAY,GAC7B,kBAAkB,mBAAmB,GACrC,gBAAgB;QAGxB,KAAK,QAAQ,CAAC,gCAAgC;QAE9C,8CAA8C;QAC9C,0IAA0I;QAE1I,0BAA0B;QAC1B,MAAM,gCACJ,kBAAkB,6BAA6B;QACjD,MAAM,0BAA0B,gBAAgB,iBAAiB,CAAC,oCAAoC;;QACtG,MAAM,wBAAwB,KAAK,SAAS,CAAC;QAE7C,2CAA2C;QAC3C,MAAM,wBACJ,AAAC,gCAAgC,0BACjC;QAEF,mCAAmC;QACnC,MAAM,kBAAkB,KAAK,GAAG,CAAC,uBAAuB;QAExD,KAAK,QAAQ,CAAC,0BAA0B;QAExC,uDAAuD;QACvD,IAAI,oBAAoB;YACtB,MAAM,oBAAoB;gBACxB,sBAAsB,gBAAgB,cAAc;gBACpD,wBAAwB;gBACxB,yBAAyB;gBACzB,uBAAuB,KAAK,SAAS,CAAC;gBACtC,8BAA8B;gBAC9B,qBAAqB,KAAK,SAAS,CAAC;gBACpC,mCAAmC,KAAK,SAAS,CAC/C;gBAEF,kCAAkC;gBAClC,yCAAyC;gBACzC,2BAA2B;gBAC3B,kCAAkC;gBAClC,8BAA8B,KAAK,SAAS,CAC1C;gBAEF,kBAAkB,KAAK,SAAS,CAAC;gBACjC,gBAAgB;gBAChB,uBAAuB;YACzB;YACA,mBAAmB;QACrB;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;KACD;IAED,0EAA0E;IAC1E,MAAM,iBAAiB,KAAK,KAAK,CAAC;QAChC;QACA;QACA;QACA;QACA;KACD;IAED,IAAA,kNAAS,EAAC;QACR,IACE,CAAC,qBACD,CAAC,sBACD,CAAC,sBACD,CAAC,cAAc,SACf;YACA;QACF;QAEA,MAAM,kBAAkB,aAAa,OAAO,CAAC,IAAI,CAC/C,CAAA,IAAK,EAAE,EAAE,KAAK,kBAAkB,SAAS;QAE3C,IAAI,CAAC,iBAAiB;YACpB;QACF;QAEA,qBAAqB;QACrB,MAAM,kBAAkB,KAAK,SAAS,CAAC;QACvC,MAAM,sBAAsB,KAAK,SAAS,CAAC;QAC3C,MAAM,oCAAoC,KAAK,SAAS,CACtD;QAEF,MAAM,+BAA+B,KAAK,SAAS,CACjD;QAEF,MAAM,mBAAmB,KAAK,SAAS,CAAC;QACxC,MAAM,wBAAwB,KAAK,SAAS,CAAC;QAE7C,uEAAuE;QACvE,uHAAuH;QACvH,MAAM,qBACJ,AAAC,sBACC,CAAC,gBAAgB,cAAc,GAAG,iCAAiC,IACrE,KAAK,IAAI,CACP,AAAC,gBAAgB,cAAc,GAC7B,IACA,kBACA,+BACA,KAAK,IAAI,CACP,mBAAmB,6BAA6B,GAC9C,mBAAmB,iCAAiC,IAExD,CAAC,kBAAkB,YAAY,GAC7B,kBAAkB,mBAAmB,GACrC,gBAAgB;QAGxB,KAAK,QAAQ,CAAC,gCAAgC;QAE9C,wCAAwC;QACxC,MAAM,8BACJ,AAAC,kBAAkB,6BAA6B,GAC9C,gBAAgB,iBAAiB,GACnC;QACF,MAAM,kBAAkB,KAAK,GAAG,CAC9B,6BACA;QAGF,KAAK,QAAQ,CAAC,0BAA0B;QAExC,oDAAoD;QACpD,IAAI,oBAAoB;YACtB,MAAM,oBAAoB;gBACxB,sBAAsB,gBAAgB,cAAc;gBACpD,wBAAwB;gBACxB,yBAAyB,gBAAgB,iBAAiB;gBAC1D,uBAAuB;gBACvB,8BAA8B;gBAC9B,qBAAqB;gBACrB,mCAAmC;gBACnC,kCAAkC,KAAK,SAAS,CAC9C;gBAEF,yCAAyC,KAAK,SAAS,CACrD;gBAEF,2BAA2B;gBAC3B,kCAAkC,KAAK,SAAS,CAC9C;gBAEF,8BAA8B;gBAC9B,kBAAkB;gBAClB,gBAAgB,KAAK,SAAS,CAAC;gBAC/B,uBAAuB,KAAK,SAAS,CAAC;YACxC;YACA,mBAAmB;QACrB;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,wIAAI;YAAE,GAAG,IAAI;sBACZ,cAAA,8OAAC;gBACC,WAAU;gBACV,UAAU,KAAK,YAAY,CAAC;;kCAE5B,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAGjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAGjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAGjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;;;;;;kCAGZ,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAGjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;;;;;;kCAGZ,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;;;;;;kCAGZ,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAGjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAGjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAGjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAGjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;;;;;;kCAGZ,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;;;;;;kCAGZ,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAGjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAGjB,8OAAC,4IAAM;wBACL,MAAK;wBACL,WAAU;wBACV,UAAU;;4BAET,2BAAa,8OAAC,oOAAW;gCAAC,WAAU;;;;;;4BACpC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;AAMrB", "debugId": null}}, {"offset": {"line": 2241, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { Badge } from '@atlas/components/ui/badge'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { z } from 'zod'\r\n\r\nconst formSchema = z.object({\r\n  maximumBendingMoment: z\r\n    .number()\r\n    .min(0, 'Maximum bending moment must be positive'),\r\n  maximumShearForce: z.number().min(0, 'Maximum shear force must be positive'),\r\n  designBendingStress: z\r\n    .number()\r\n    .min(0, 'Design bending stress must be positive'),\r\n  designBendingStrength: z\r\n    .number()\r\n    .min(0, 'Design bending strength must be positive'),\r\n  designBendingCheck: z\r\n    .number()\r\n    .min(0, 'Design bending check must be positive'),\r\n  designShearStress: z.number().min(0, 'Design shear stress must be positive'),\r\n  designShearStrength: z\r\n    .number()\r\n    .min(0, 'Design shear strength must be positive'),\r\n  designShearCheck: z.number().min(0, 'Design shear check must be positive'),\r\n  permanentLoadPerLinearMeter: z\r\n    .number()\r\n    .min(0, 'Permanent load per linear meter must be positive'),\r\n  imposedLoadPerLinearMeter: z\r\n    .number()\r\n    .min(0, 'Imposed load per linear meter must be positive'),\r\n  instantaneousDeflectionPermanentLoad: z\r\n    .number()\r\n    .min(0, 'Instantaneous deflection permanent load must be positive'),\r\n  instantaneousDeflectionImposedLoad: z\r\n    .number()\r\n    .min(0, 'Instantaneous deflection imposed load must be positive'),\r\n  instantaneousDeflectionTotalLoads: z\r\n    .number()\r\n    .min(0, 'Instantaneous deflection total loads must be positive'),\r\n  instantaneousDeflectionCheck: z\r\n    .number()\r\n    .min(0, 'Instantaneous deflection check must be positive'),\r\n  combinationFactor: z.number().min(0, 'Combination factor must be positive'),\r\n  finalDeflectionTotalLoads: z\r\n    .number()\r\n    .min(0, 'Final deflection total loads must be positive'),\r\n  finalCheckResult: z.number().min(0, 'Final check result must be positive'),\r\n})\r\n\r\ntype FormSchema = z.infer<typeof formSchema>\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FormSchema>\r\n  preInterventionData?: any\r\n  materialProperties?: any\r\n  geometryProperties?: any\r\n  compositeGeometry?: any\r\n  compositeProperties?: any\r\n  onSave?: () => void\r\n  initialDeformation?: any\r\n}\r\n\r\nexport const WoodPostInterventionCheckForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  preInterventionData,\r\n  materialProperties,\r\n  geometryProperties,\r\n  compositeGeometry,\r\n  compositeProperties,\r\n  onSave,\r\n  initialDeformation,\r\n}: Props) => {\r\n  const t = useTranslations(\r\n    'forms.project-params.wood.resultOfPostIntervationCheck',\r\n  )\r\n  const tCommon = useTranslations('actions.common')\r\n\r\n  const form = useForm<FormSchema>({\r\n    resolver: zodResolver(formSchema),\r\n    defaultValues: {\r\n      maximumBendingMoment:\r\n        defaultValues?.maximumBendingMoment ||\r\n        preInterventionData?.maximumBendingMoment ||\r\n        0,\r\n      maximumShearForce:\r\n        defaultValues?.maximumShearForce ||\r\n        preInterventionData?.maximumShearForce ||\r\n        0,\r\n      designBendingStress: defaultValues?.designBendingStress || 0,\r\n      designBendingStrength: defaultValues?.designBendingStrength || 0,\r\n      designBendingCheck: defaultValues?.designBendingCheck || 0,\r\n      designShearStress: defaultValues?.designShearStress || 0,\r\n      designShearStrength: defaultValues?.designShearStrength || 0,\r\n      designShearCheck: defaultValues?.designShearCheck || 0,\r\n      permanentLoadPerLinearMeter:\r\n        defaultValues?.permanentLoadPerLinearMeter ||\r\n        preInterventionData?.permanentLoadPerLinearMeter ||\r\n        0,\r\n      imposedLoadPerLinearMeter:\r\n        defaultValues?.imposedLoadPerLinearMeter ||\r\n        preInterventionData?.imposedLoadPerLinearMeter ||\r\n        0,\r\n      instantaneousDeflectionPermanentLoad:\r\n        defaultValues?.instantaneousDeflectionPermanentLoad || 0,\r\n      instantaneousDeflectionImposedLoad:\r\n        defaultValues?.instantaneousDeflectionImposedLoad || 0,\r\n      instantaneousDeflectionTotalLoads:\r\n        defaultValues?.instantaneousDeflectionTotalLoads || 0,\r\n      instantaneousDeflectionCheck:\r\n        defaultValues?.instantaneousDeflectionCheck || 0,\r\n      combinationFactor:\r\n        defaultValues?.combinationFactor ||\r\n        preInterventionData?.combinationFactor ||\r\n        0,\r\n      finalDeflectionTotalLoads: defaultValues?.finalDeflectionTotalLoads || 0,\r\n      finalCheckResult: defaultValues?.finalCheckResult || 0,\r\n    },\r\n  })\r\n\r\n  // Reset form when defaultValues change (for saved data after refresh)\r\n  useEffect(() => {\r\n    if (\r\n      defaultValues &&\r\n      typeof defaultValues === 'object' &&\r\n      Object.keys(defaultValues).length > 0\r\n    ) {\r\n      // Create a complete form data object with all fields\r\n      const formData = {\r\n        maximumBendingMoment:\r\n          defaultValues.maximumBendingMoment ??\r\n          preInterventionData?.maximumBendingMoment ??\r\n          0,\r\n        maximumShearForce:\r\n          defaultValues.maximumShearForce ??\r\n          preInterventionData?.maximumShearForce ??\r\n          0,\r\n        designBendingStress: defaultValues.designBendingStress ?? 0,\r\n        designBendingStrength: defaultValues.designBendingStrength ?? 0,\r\n        designBendingCheck: defaultValues.designBendingCheck ?? 0,\r\n        designShearStress: defaultValues.designShearStress ?? 0,\r\n        designShearStrength: defaultValues.designShearStrength ?? 0,\r\n        designShearCheck: defaultValues.designShearCheck ?? 0,\r\n        permanentLoadPerLinearMeter:\r\n          defaultValues.permanentLoadPerLinearMeter ??\r\n          preInterventionData?.permanentLoadPerLinearMeter ??\r\n          0,\r\n        imposedLoadPerLinearMeter:\r\n          defaultValues.imposedLoadPerLinearMeter ??\r\n          preInterventionData?.imposedLoadPerLinearMeter ??\r\n          0,\r\n        instantaneousDeflectionPermanentLoad:\r\n          defaultValues.instantaneousDeflectionPermanentLoad ?? 0,\r\n        instantaneousDeflectionImposedLoad:\r\n          defaultValues.instantaneousDeflectionImposedLoad ?? 0,\r\n        instantaneousDeflectionTotalLoads:\r\n          defaultValues.instantaneousDeflectionTotalLoads ?? 0,\r\n        instantaneousDeflectionCheck:\r\n          defaultValues.instantaneousDeflectionCheck ?? 0,\r\n        combinationFactor:\r\n          defaultValues.combinationFactor ??\r\n          preInterventionData?.combinationFactor ??\r\n          0,\r\n        finalDeflectionTotalLoads: defaultValues.finalDeflectionTotalLoads ?? 0,\r\n        finalCheckResult: defaultValues.finalCheckResult ?? 0,\r\n      }\r\n\r\n      form.reset(formData)\r\n    } else {\r\n    }\r\n  }, [defaultValues, preInterventionData, form])\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(session.accessToken)\r\n\r\n  // Helper function to determine check result\r\n  const getCheckResult = (\r\n    value: number,\r\n    threshold: number,\r\n    checkType: 'lessThanOrEqual' | 'greaterThanOrEqual' = 'lessThanOrEqual',\r\n  ) => {\r\n    if (checkType === 'lessThanOrEqual') {\r\n      // For bending and shear checks: <= threshold = \"Satisfy\", > threshold = \"Non-satisfy\"\r\n      return value <= threshold ? 'Satisfy' : 'Non-satisfy'\r\n    }\r\n    // For deflection checks: >= threshold = \"Satisfy\", < threshold = \"Non-satisfy\"\r\n    return value >= threshold ? 'Satisfy' : 'Non-satisfy'\r\n  }\r\n\r\n  // Helper function to get badge variant\r\n  const getBadgeVariant = (result: string) => {\r\n    return result === 'Satisfy' ? 'default' : 'destructive'\r\n  }\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (data: FormSchema) => {\r\n      // Save as part of postIntervationCheck structure with all inherited data\r\n      mutate({\r\n        projectId,\r\n        moduleId,\r\n        body: {\r\n          postIntervationCheck: {\r\n            // Include inherited data from pre-intervention\r\n            initialDeformation: initialDeformation || 0,\r\n            materialProperties: materialProperties || null,\r\n            geometry: geometryProperties || null,\r\n            // Include composite data from props\r\n            compositeGeometry: compositeGeometry || null,\r\n            compositeProperties: compositeProperties || null,\r\n            // Include the current form data (only 3 fields now)\r\n            resultOfPostIntervationCheck: data,\r\n          },\r\n        } as any,\r\n      })\r\n      onSave?.()\r\n    },\r\n    [\r\n      mutate,\r\n      projectId,\r\n      moduleId,\r\n      onSave,\r\n      materialProperties,\r\n      geometryProperties,\r\n      compositeGeometry,\r\n      compositeProperties,\r\n      initialDeformation,\r\n    ],\r\n  )\r\n\r\n  // Calculate derived values when dependencies change\r\n  useEffect(() => {\r\n    // Set values from pre-intervention data first (if available)\r\n    if (preInterventionData) {\r\n      const preIntervention = preInterventionData\r\n\r\n      // Set values from pre-intervention data (only if not already saved)\r\n      if (\r\n        !defaultValues?.maximumBendingMoment &&\r\n        preIntervention.maximumBendingMoment !== undefined\r\n      ) {\r\n        form.setValue(\r\n          'maximumBendingMoment',\r\n          preIntervention.maximumBendingMoment,\r\n        )\r\n      }\r\n      if (\r\n        !defaultValues?.maximumShearForce &&\r\n        preIntervention.maximumShearForce !== undefined\r\n      ) {\r\n        form.setValue('maximumShearForce', preIntervention.maximumShearForce)\r\n      }\r\n      if (\r\n        !defaultValues?.permanentLoadPerLinearMeter &&\r\n        preIntervention.permanentLoadPerLinearMeter !== undefined\r\n      ) {\r\n        form.setValue(\r\n          'permanentLoadPerLinearMeter',\r\n          preIntervention.permanentLoadPerLinearMeter,\r\n        )\r\n      }\r\n      if (\r\n        !defaultValues?.imposedLoadPerLinearMeter &&\r\n        preIntervention.imposedLoadPerLinearMeter !== undefined\r\n      ) {\r\n        form.setValue(\r\n          'imposedLoadPerLinearMeter',\r\n          preIntervention.imposedLoadPerLinearMeter,\r\n        )\r\n      }\r\n      if (\r\n        !defaultValues?.combinationFactor &&\r\n        preIntervention.combinationFactor !== undefined\r\n      ) {\r\n        form.setValue('combinationFactor', preIntervention.combinationFactor)\r\n      }\r\n    }\r\n\r\n    // Calculate design_bending_stress if we have the required data\r\n    if (preInterventionData) {\r\n      const preIntervention = preInterventionData\r\n      const maximumBendingMoment =\r\n        form.getValues('maximumBendingMoment') ||\r\n        preIntervention.maximumBendingMoment ||\r\n        0\r\n\r\n      // Try to get sectionModulus from composite properties (preferred)\r\n      if (\r\n        compositeProperties?.sectionModulus &&\r\n        compositeProperties.sectionModulus > 0\r\n      ) {\r\n        const designBendingStress =\r\n          maximumBendingMoment / (compositeProperties.sectionModulus * 1000)\r\n        form.setValue('designBendingStress', designBendingStress)\r\n      }\r\n      // Fallback: try to get sectionModulus from geometry properties (from pre-intervention forms)\r\n      else if (\r\n        geometryProperties?.sectionModulus &&\r\n        geometryProperties.sectionModulus > 0\r\n      ) {\r\n        const designBendingStress =\r\n          maximumBendingMoment / (geometryProperties.sectionModulus * 1000)\r\n        form.setValue('designBendingStress', designBendingStress)\r\n      } else {\r\n        // Set to 0 if we can't calculate (composite properties not ready yet)\r\n        form.setValue('designBendingStress', 0)\r\n      }\r\n    }\r\n\r\n    // Calculate design_bending_strength = MIN(pre ma \"designBendingStrength\", frp_design_maximum_strain*frp_elasticity_modulus)\r\n    if (materialProperties && compositeProperties) {\r\n      // Get designBendingStrength from previous forms (1,2,3,4)\r\n      const preDesignBendingStrength =\r\n        geometryProperties.designBendingStrength || 0\r\n\r\n      // Get frp_design_maximum_strain and frp_elasticity_modulus from 5th section (Composite Properties)\r\n      const frpDesignMaximumStrain =\r\n        compositeProperties.frpDesignMaximumStrain || 0\r\n      const frpElasticityModulus = compositeProperties.frpElasticityModulus || 0\r\n\r\n      // Calculate the MIN of both values\r\n      const designBendingStrength = Math.min(\r\n        preDesignBendingStrength,\r\n        frpDesignMaximumStrain * frpElasticityModulus,\r\n      )\r\n\r\n      form.setValue('designBendingStrength', designBendingStrength)\r\n\r\n      // Calculate designBendingCheck = design_bending_stress / design_bending_strength\r\n      const designBendingStress = form.getValues('designBendingStress') || 0\r\n      const designBendingCheck =\r\n        designBendingStrength > 0\r\n          ? designBendingStress / designBendingStrength\r\n          : 0\r\n      form.setValue('designBendingCheck', designBendingCheck)\r\n    } else {\r\n      // Set to 0 if we don't have the required data\r\n      form.setValue('designBendingStrength', 0)\r\n      form.setValue('designBendingCheck', 0)\r\n    }\r\n\r\n    // Calculate shear stress and strength\r\n    if (geometryProperties) {\r\n      const maximumShearForce =\r\n        form.getValues('maximumShearForce') ||\r\n        preInterventionData?.maximumShearForce ||\r\n        0\r\n\r\n      // Calculate design_shear_stress = 3*1000*maximum_shear_force/(2*1000*beamSectionHeight*1000*beamSectionWidth)\r\n      const beamSectionHeight = geometryProperties.beamSectionHeight || 0\r\n      const beamSectionWidth = geometryProperties.beamSectionWidth || 0\r\n\r\n      if (beamSectionHeight > 0 && beamSectionWidth > 0) {\r\n        const designShearStress =\r\n          (3 * 1000 * maximumShearForce) /\r\n          (2 * 1000 * beamSectionHeight * 1000 * beamSectionWidth)\r\n        form.setValue('designShearStress', designShearStress)\r\n\r\n        // design_shear_strength = Pre ma \"designShearStrength\"\r\n        const designShearStrength = geometryProperties.designShearStrength || 0\r\n        form.setValue('designShearStrength', designShearStrength)\r\n\r\n        // Calculate designShearCheck = design_shear_stress / design_shear_strength\r\n        const designShearCheck =\r\n          designShearStrength > 0 ? designShearStress / designShearStrength : 0\r\n        form.setValue('designShearCheck', designShearCheck)\r\n      } else {\r\n        form.setValue('designShearStress', 0)\r\n        form.setValue('designShearStrength', 0)\r\n        form.setValue('designShearCheck', 0)\r\n      }\r\n    } else {\r\n      form.setValue('designShearStress', 0)\r\n      form.setValue('designShearStrength', 0)\r\n      form.setValue('designShearCheck', 0)\r\n    }\r\n\r\n    // Calculate deflection values\r\n    if (geometryProperties && compositeProperties) {\r\n      const permanentLoadPerLinearMeter =\r\n        form.getValues('permanentLoadPerLinearMeter') ||\r\n        preInterventionData?.permanentLoadPerLinearMeter ||\r\n        0\r\n      const beamSpan = geometryProperties.beamSpan || 0\r\n      const elasticityInstantaneousModulus =\r\n        geometryProperties.elasticityInstantaneousModulus || 0\r\n\r\n      // Get moment of inertia from composite properties\r\n      const momentOfInertiaAboutY =\r\n        compositeProperties.momentOfInertiaAboutY || 0\r\n\r\n      if (\r\n        beamSpan > 0 &&\r\n        elasticityInstantaneousModulus > 0 &&\r\n        momentOfInertiaAboutY > 0\r\n      ) {\r\n        // instantaneous_deflection_permanent_load = 100*(5*permanent_load_per_linear_meter*beamSpan^4)/(384*1000*designShearStrength*moment_of_inertia_about_y)\r\n        const instantaneousDeflectionPermanentLoad =\r\n          100 *\r\n          ((5 * permanentLoadPerLinearMeter * beamSpan ** 4) /\r\n            (384 *\r\n              1000 *\r\n              elasticityInstantaneousModulus *\r\n              momentOfInertiaAboutY))\r\n        form.setValue(\r\n          'instantaneousDeflectionPermanentLoad',\r\n          instantaneousDeflectionPermanentLoad,\r\n        )\r\n\r\n        // instantaneous_deflection_imposed_load = 100*(5*imposed_load_per_linear_meter*beamSpan^4)/(384*1000*designShearStrength*moment_of_inertia_about_y)\r\n        const imposedLoadPerLinearMeter =\r\n          form.getValues('imposedLoadPerLinearMeter') ||\r\n          preInterventionData?.imposedLoadPerLinearMeter ||\r\n          0\r\n        const instantaneousDeflectionImposedLoad =\r\n          (100 * (5 * imposedLoadPerLinearMeter * beamSpan ** 4)) /\r\n          (384 * 1000 * elasticityInstantaneousModulus * momentOfInertiaAboutY)\r\n        form.setValue(\r\n          'instantaneousDeflectionImposedLoad',\r\n          instantaneousDeflectionImposedLoad,\r\n        )\r\n\r\n        // instantaneous_deflection_total_loads = instantaneous_deflection_permanent_load + instantaneous_deflection_imposed_load + initialDeformation\r\n        const finalInitialDeformation = initialDeformation || 0\r\n        const instantaneousDeflectionTotalLoads =\r\n          instantaneousDeflectionPermanentLoad +\r\n          instantaneousDeflectionImposedLoad +\r\n          finalInitialDeformation\r\n        form.setValue(\r\n          'instantaneousDeflectionTotalLoads',\r\n          instantaneousDeflectionTotalLoads,\r\n        )\r\n\r\n        // instantaneousDeflectionCheck = 100*beamSpan / instantaneous_deflection_total_loads (< 300 = \"Non-satisfy\", >= 300 = \"Satisfy\")\r\n        const instantaneousDeflectionCheck =\r\n          instantaneousDeflectionTotalLoads > 0\r\n            ? (100 * beamSpan) / instantaneousDeflectionTotalLoads\r\n            : 0\r\n        form.setValue(\r\n          'instantaneousDeflectionCheck',\r\n          instantaneousDeflectionCheck,\r\n        )\r\n\r\n        // final_deflection_total_loads = instantaneous_deflection_permanent_load*(1+deformabilityFactor)+instantaneous_deflection_imposed_load*(1+combination_factor*deformabilityFactor)+initialDeformation\r\n        const deformabilityFactor = geometryProperties.deformabilityFactor || 0\r\n        const combinationFactor =\r\n          form.getValues('combinationFactor') ||\r\n          preInterventionData?.combinationFactor ||\r\n          0\r\n        const finalDeflectionTotalLoads =\r\n          instantaneousDeflectionPermanentLoad * (1 + deformabilityFactor) +\r\n          instantaneousDeflectionImposedLoad *\r\n            (1 + combinationFactor * deformabilityFactor) +\r\n          finalInitialDeformation\r\n        form.setValue('finalDeflectionTotalLoads', finalDeflectionTotalLoads)\r\n\r\n        // finalCheckResult = 100*beamSpan/final_deflection_total_loads (>= 200 = \"Satisfy\", < 200 = \"Non-Satisfy\")\r\n        const finalCheckResult =\r\n          finalDeflectionTotalLoads > 0\r\n            ? (100 * beamSpan) / finalDeflectionTotalLoads\r\n            : 0\r\n        form.setValue('finalCheckResult', finalCheckResult)\r\n      } else {\r\n        form.setValue('instantaneousDeflectionPermanentLoad', 0)\r\n        form.setValue('instantaneousDeflectionImposedLoad', 0)\r\n        form.setValue('instantaneousDeflectionTotalLoads', 0)\r\n        form.setValue('instantaneousDeflectionCheck', 0)\r\n        form.setValue('finalDeflectionTotalLoads', 0)\r\n        form.setValue('finalCheckResult', 0)\r\n      }\r\n    } else {\r\n      form.setValue('instantaneousDeflectionPermanentLoad', 0)\r\n      form.setValue('instantaneousDeflectionImposedLoad', 0)\r\n      form.setValue('instantaneousDeflectionTotalLoads', 0)\r\n      form.setValue('instantaneousDeflectionCheck', 0)\r\n      form.setValue('finalDeflectionTotalLoads', 0)\r\n      form.setValue('finalCheckResult', 0)\r\n    }\r\n  }, [\r\n    preInterventionData,\r\n    compositeProperties,\r\n    geometryProperties,\r\n    materialProperties,\r\n    defaultValues,\r\n    form,\r\n    initialDeformation,\r\n  ])\r\n\r\n  return (\r\n    <div className=\"flex flex-col 2xl:flex-row justify-center gap-2\">\r\n      <Form {...form}>\r\n        <form\r\n          className=\"space-y-4 rounded-md border p-4 grow\"\r\n          onSubmit={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {/* 1. maximum_bending_moment = pre ma \"maximumBendingMoment\" */}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"maximumBendingMoment\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          {/* 2. maximum_shear_force = pre ma \"maximumShearForce\" */}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"maximumShearForce\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          {/* 3. design_bending_stress = maximum_bending_moment/(section_modulus*1000) */}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"designBendingStress\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          {/* 4. design_bending_strength = MIN(pre ma \"designBendingStrength\", frp_design_maximum_strain*frp_elasticity_modulus) */}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"designBendingStrength\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          {/* 5. designBendingCheck = design_bending_stress / design_bending_strength <= 1 \"Non-satisfy\" else \"satisfy\" */}\r\n          <div className=\"flex items-start gap-4\">\r\n            <NumberFormInput\r\n              control={form.control}\r\n              name=\"designBendingCheck\"\r\n              t={t}\r\n              disabled={true}\r\n              fieldContainerClassName=\"flex-1\"\r\n              decimalPlaces={3}\r\n            />\r\n            <Badge\r\n              variant={getBadgeVariant(\r\n                getCheckResult(form.watch('designBendingCheck'), 1),\r\n              )}\r\n              className=\"mt-7\"\r\n            >\r\n              {getCheckResult(form.watch('designBendingCheck'), 1)}\r\n            </Badge>\r\n          </div>\r\n\r\n          {/* 6. design_shear_stress = 3*1000*maximum_shear_force/(2*1000*beamSectionHeight*1000*beamSectionWidth) */}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"designShearStress\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          {/* 7. design_shear_strength = Pre ma \"designShearStrength\" */}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"designShearStrength\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          {/* 8. designShearCheck = design_shear_stress / design_shear_strength <= 1 \"satisfy\" else \"Non-satisfy\" */}\r\n          <div className=\"flex items-start gap-4\">\r\n            <NumberFormInput\r\n              control={form.control}\r\n              name=\"designShearCheck\"\r\n              t={t}\r\n              disabled={true}\r\n              fieldContainerClassName=\"flex-1\"\r\n              decimalPlaces={3}\r\n            />\r\n            <Badge\r\n              variant={getBadgeVariant(\r\n                getCheckResult(form.watch('designShearCheck'), 1),\r\n              )}\r\n              className=\"mt-7\"\r\n            >\r\n              {getCheckResult(form.watch('designShearCheck'), 1)}\r\n            </Badge>\r\n          </div>\r\n\r\n          {/* 9. permanent_load_per_linear_meter = Pre ma \"permanentLoadPerLinearMeter\" */}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"permanentLoadPerLinearMeter\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          {/* 10. imposed_load_per_linear_meter = pre ma \"imposedLoadPerLinearMeter\" */}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"imposedLoadPerLinearMeter\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          {/* 11. instantaneous_deflection_permanent_load = 100*(5*permanent_load_per_linear_meter*beamSpan^4)/(384*1000*designShearStrength*moment_of_inertia_about_y) */}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"instantaneousDeflectionPermanentLoad\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          {/* 12. instantaneous_deflection_imposed_load = 100*(5*imposed_load_per_linear_meter*beamSpan^4)/(384*1000*designShearStrength*moment_of_inertia_about_y) */}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"instantaneousDeflectionImposedLoad\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          {/* 13. instantaneous_deflection_total_loads = instantaneous_deflection_permanent_load + instantaneous_deflection_imposed_load + initialDeformation */}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"instantaneousDeflectionTotalLoads\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          {/* 14. instantaneousDeflectionCheck = 100*beamSpan / instantaneous_deflection_total_loads (< 300 = \"Non-satisfy\", >= 300 = \"Satisfy\") */}\r\n          <div className=\"flex items-start gap-4\">\r\n            <NumberFormInput\r\n              control={form.control}\r\n              name=\"instantaneousDeflectionCheck\"\r\n              t={t}\r\n              disabled={true}\r\n              fieldContainerClassName=\"flex-1\"\r\n              decimalPlaces={3}\r\n            />\r\n            <Badge\r\n              variant={getBadgeVariant(\r\n                getCheckResult(\r\n                  form.watch('instantaneousDeflectionCheck'),\r\n                  300,\r\n                  'greaterThanOrEqual',\r\n                ),\r\n              )}\r\n              className=\"mt-7\"\r\n            >\r\n              {getCheckResult(\r\n                form.watch('instantaneousDeflectionCheck'),\r\n                300,\r\n                'greaterThanOrEqual',\r\n              )}\r\n            </Badge>\r\n          </div>\r\n\r\n          {/* 15. combination_factor = pre ma \"combinationFactor\" */}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"combinationFactor\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          {/* 16. final_deflection_total_loads = instantaneous_deflection_permanent_load*(1+deformabilityFactor)+instantaneous_deflection_imposed_load*(1+combination_factor*deformabilityFactor)+initialDeformation */}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"finalDeflectionTotalLoads\"\r\n            t={t}\r\n            disabled={true}\r\n            decimalPlaces={3}\r\n          />\r\n\r\n          {/* 17. finalCheckResult = 100*beamSpan/final_deflection_total_loads (>= 200 = \"Satisfy\", < 200 = \"Non-Satisfy\") */}\r\n          <div className=\"flex items-start gap-4\">\r\n            <NumberFormInput\r\n              control={form.control}\r\n              name=\"finalCheckResult\"\r\n              t={t}\r\n              disabled={true}\r\n              fieldContainerClassName=\"flex-1\"\r\n              decimalPlaces={3}\r\n            />\r\n            <Badge\r\n              variant={getBadgeVariant(\r\n                getCheckResult(\r\n                  form.watch('finalCheckResult'),\r\n                  200,\r\n                  'greaterThanOrEqual',\r\n                ),\r\n              )}\r\n              className=\"mt-7\"\r\n            >\r\n              {getCheckResult(\r\n                form.watch('finalCheckResult'),\r\n                200,\r\n                'greaterThanOrEqual',\r\n              )}\r\n            </Badge>\r\n          </div>\r\n\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full sm:w-auto\"\r\n            disabled={isPending}\r\n          >\r\n            {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n            {tCommon('save')}\r\n          </Button>\r\n        </form>\r\n      </Form>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAGA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;AAEA,MAAM,aAAa,kLAAC,CAAC,MAAM,CAAC;IAC1B,sBAAsB,kLAAC,CACpB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,mBAAmB,kLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACrC,qBAAqB,kLAAC,CACnB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,uBAAuB,kLAAC,CACrB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,oBAAoB,kLAAC,CAClB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,mBAAmB,kLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACrC,qBAAqB,kLAAC,CACnB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,kBAAkB,kLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACpC,6BAA6B,kLAAC,CAC3B,MAAM,GACN,GAAG,CAAC,GAAG;IACV,2BAA2B,kLAAC,CACzB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,sCAAsC,kLAAC,CACpC,MAAM,GACN,GAAG,CAAC,GAAG;IACV,oCAAoC,kLAAC,CAClC,MAAM,GACN,GAAG,CAAC,GAAG;IACV,mCAAmC,kLAAC,CACjC,MAAM,GACN,GAAG,CAAC,GAAG;IACV,8BAA8B,kLAAC,CAC5B,MAAM,GACN,GAAG,CAAC,GAAG;IACV,mBAAmB,kLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACrC,2BAA2B,kLAAC,CACzB,MAAM,GACN,GAAG,CAAC,GAAG;IACV,kBAAkB,kLAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AACtC;AAkBO,MAAM,gCAAgC,CAAC,EAC5C,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,mBAAmB,EACnB,kBAAkB,EAClB,kBAAkB,EAClB,iBAAiB,EACjB,mBAAmB,EACnB,MAAM,EACN,kBAAkB,EACZ;IACN,MAAM,IAAI,IAAA,yNAAe,EACvB;IAEF,MAAM,UAAU,IAAA,yNAAe,EAAC;IAEhC,MAAM,OAAO,IAAA,yKAAO,EAAa;QAC/B,UAAU,IAAA,6KAAW,EAAC;QACtB,eAAe;YACb,sBACE,eAAe,wBACf,qBAAqB,wBACrB;YACF,mBACE,eAAe,qBACf,qBAAqB,qBACrB;YACF,qBAAqB,eAAe,uBAAuB;YAC3D,uBAAuB,eAAe,yBAAyB;YAC/D,oBAAoB,eAAe,sBAAsB;YACzD,mBAAmB,eAAe,qBAAqB;YACvD,qBAAqB,eAAe,uBAAuB;YAC3D,kBAAkB,eAAe,oBAAoB;YACrD,6BACE,eAAe,+BACf,qBAAqB,+BACrB;YACF,2BACE,eAAe,6BACf,qBAAqB,6BACrB;YACF,sCACE,eAAe,wCAAwC;YACzD,oCACE,eAAe,sCAAsC;YACvD,mCACE,eAAe,qCAAqC;YACtD,8BACE,eAAe,gCAAgC;YACjD,mBACE,eAAe,qBACf,qBAAqB,qBACrB;YACF,2BAA2B,eAAe,6BAA6B;YACvE,kBAAkB,eAAe,oBAAoB;QACvD;IACF;IAEA,sEAAsE;IACtE,IAAA,kNAAS,EAAC;QACR,IACE,iBACA,OAAO,kBAAkB,YACzB,OAAO,IAAI,CAAC,eAAe,MAAM,GAAG,GACpC;YACA,qDAAqD;YACrD,MAAM,WAAW;gBACf,sBACE,cAAc,oBAAoB,IAClC,qBAAqB,wBACrB;gBACF,mBACE,cAAc,iBAAiB,IAC/B,qBAAqB,qBACrB;gBACF,qBAAqB,cAAc,mBAAmB,IAAI;gBAC1D,uBAAuB,cAAc,qBAAqB,IAAI;gBAC9D,oBAAoB,cAAc,kBAAkB,IAAI;gBACxD,mBAAmB,cAAc,iBAAiB,IAAI;gBACtD,qBAAqB,cAAc,mBAAmB,IAAI;gBAC1D,kBAAkB,cAAc,gBAAgB,IAAI;gBACpD,6BACE,cAAc,2BAA2B,IACzC,qBAAqB,+BACrB;gBACF,2BACE,cAAc,yBAAyB,IACvC,qBAAqB,6BACrB;gBACF,sCACE,cAAc,oCAAoC,IAAI;gBACxD,oCACE,cAAc,kCAAkC,IAAI;gBACtD,mCACE,cAAc,iCAAiC,IAAI;gBACrD,8BACE,cAAc,4BAA4B,IAAI;gBAChD,mBACE,cAAc,iBAAiB,IAC/B,qBAAqB,qBACrB;gBACF,2BAA2B,cAAc,yBAAyB,IAAI;gBACtE,kBAAkB,cAAc,gBAAgB,IAAI;YACtD;YAEA,KAAK,KAAK,CAAC;QACb,OAAO,CACP;IACF,GAAG;QAAC;QAAe;QAAqB;KAAK;IAE7C,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gNAA2B,EAAC,QAAQ,WAAW;IAE7E,4CAA4C;IAC5C,MAAM,iBAAiB,CACrB,OACA,WACA,YAAsD,iBAAiB;QAEvE,IAAI,cAAc,mBAAmB;YACnC,sFAAsF;YACtF,OAAO,SAAS,YAAY,YAAY;QAC1C;QACA,+EAA+E;QAC/E,OAAO,SAAS,YAAY,YAAY;IAC1C;IAEA,uCAAuC;IACvC,MAAM,kBAAkB,CAAC;QACvB,OAAO,WAAW,YAAY,YAAY;IAC5C;IAEA,MAAM,mBAAmB,IAAA,oNAAW,EAClC,CAAC;QACC,yEAAyE;QACzE,OAAO;YACL;YACA;YACA,MAAM;gBACJ,sBAAsB;oBACpB,+CAA+C;oBAC/C,oBAAoB,sBAAsB;oBAC1C,oBAAoB,sBAAsB;oBAC1C,UAAU,sBAAsB;oBAChC,oCAAoC;oBACpC,mBAAmB,qBAAqB;oBACxC,qBAAqB,uBAAuB;oBAC5C,oDAAoD;oBACpD,8BAA8B;gBAChC;YACF;QACF;QACA;IACF,GACA;QACE;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAGH,oDAAoD;IACpD,IAAA,kNAAS,EAAC;QACR,6DAA6D;QAC7D,IAAI,qBAAqB;YACvB,MAAM,kBAAkB;YAExB,oEAAoE;YACpE,IACE,CAAC,eAAe,wBAChB,gBAAgB,oBAAoB,KAAK,WACzC;gBACA,KAAK,QAAQ,CACX,wBACA,gBAAgB,oBAAoB;YAExC;YACA,IACE,CAAC,eAAe,qBAChB,gBAAgB,iBAAiB,KAAK,WACtC;gBACA,KAAK,QAAQ,CAAC,qBAAqB,gBAAgB,iBAAiB;YACtE;YACA,IACE,CAAC,eAAe,+BAChB,gBAAgB,2BAA2B,KAAK,WAChD;gBACA,KAAK,QAAQ,CACX,+BACA,gBAAgB,2BAA2B;YAE/C;YACA,IACE,CAAC,eAAe,6BAChB,gBAAgB,yBAAyB,KAAK,WAC9C;gBACA,KAAK,QAAQ,CACX,6BACA,gBAAgB,yBAAyB;YAE7C;YACA,IACE,CAAC,eAAe,qBAChB,gBAAgB,iBAAiB,KAAK,WACtC;gBACA,KAAK,QAAQ,CAAC,qBAAqB,gBAAgB,iBAAiB;YACtE;QACF;QAEA,+DAA+D;QAC/D,IAAI,qBAAqB;YACvB,MAAM,kBAAkB;YACxB,MAAM,uBACJ,KAAK,SAAS,CAAC,2BACf,gBAAgB,oBAAoB,IACpC;YAEF,kEAAkE;YAClE,IACE,qBAAqB,kBACrB,oBAAoB,cAAc,GAAG,GACrC;gBACA,MAAM,sBACJ,uBAAuB,CAAC,oBAAoB,cAAc,GAAG,IAAI;gBACnE,KAAK,QAAQ,CAAC,uBAAuB;YACvC,OAEK,IACH,oBAAoB,kBACpB,mBAAmB,cAAc,GAAG,GACpC;gBACA,MAAM,sBACJ,uBAAuB,CAAC,mBAAmB,cAAc,GAAG,IAAI;gBAClE,KAAK,QAAQ,CAAC,uBAAuB;YACvC,OAAO;gBACL,sEAAsE;gBACtE,KAAK,QAAQ,CAAC,uBAAuB;YACvC;QACF;QAEA,4HAA4H;QAC5H,IAAI,sBAAsB,qBAAqB;YAC7C,0DAA0D;YAC1D,MAAM,2BACJ,mBAAmB,qBAAqB,IAAI;YAE9C,mGAAmG;YACnG,MAAM,yBACJ,oBAAoB,sBAAsB,IAAI;YAChD,MAAM,uBAAuB,oBAAoB,oBAAoB,IAAI;YAEzE,mCAAmC;YACnC,MAAM,wBAAwB,KAAK,GAAG,CACpC,0BACA,yBAAyB;YAG3B,KAAK,QAAQ,CAAC,yBAAyB;YAEvC,iFAAiF;YACjF,MAAM,sBAAsB,KAAK,SAAS,CAAC,0BAA0B;YACrE,MAAM,qBACJ,wBAAwB,IACpB,sBAAsB,wBACtB;YACN,KAAK,QAAQ,CAAC,sBAAsB;QACtC,OAAO;YACL,8CAA8C;YAC9C,KAAK,QAAQ,CAAC,yBAAyB;YACvC,KAAK,QAAQ,CAAC,sBAAsB;QACtC;QAEA,sCAAsC;QACtC,IAAI,oBAAoB;YACtB,MAAM,oBACJ,KAAK,SAAS,CAAC,wBACf,qBAAqB,qBACrB;YAEF,8GAA8G;YAC9G,MAAM,oBAAoB,mBAAmB,iBAAiB,IAAI;YAClE,MAAM,mBAAmB,mBAAmB,gBAAgB,IAAI;YAEhE,IAAI,oBAAoB,KAAK,mBAAmB,GAAG;gBACjD,MAAM,oBACJ,AAAC,IAAI,OAAO,oBACZ,CAAC,IAAI,OAAO,oBAAoB,OAAO,gBAAgB;gBACzD,KAAK,QAAQ,CAAC,qBAAqB;gBAEnC,uDAAuD;gBACvD,MAAM,sBAAsB,mBAAmB,mBAAmB,IAAI;gBACtE,KAAK,QAAQ,CAAC,uBAAuB;gBAErC,2EAA2E;gBAC3E,MAAM,mBACJ,sBAAsB,IAAI,oBAAoB,sBAAsB;gBACtE,KAAK,QAAQ,CAAC,oBAAoB;YACpC,OAAO;gBACL,KAAK,QAAQ,CAAC,qBAAqB;gBACnC,KAAK,QAAQ,CAAC,uBAAuB;gBACrC,KAAK,QAAQ,CAAC,oBAAoB;YACpC;QACF,OAAO;YACL,KAAK,QAAQ,CAAC,qBAAqB;YACnC,KAAK,QAAQ,CAAC,uBAAuB;YACrC,KAAK,QAAQ,CAAC,oBAAoB;QACpC;QAEA,8BAA8B;QAC9B,IAAI,sBAAsB,qBAAqB;YAC7C,MAAM,8BACJ,KAAK,SAAS,CAAC,kCACf,qBAAqB,+BACrB;YACF,MAAM,WAAW,mBAAmB,QAAQ,IAAI;YAChD,MAAM,iCACJ,mBAAmB,8BAA8B,IAAI;YAEvD,kDAAkD;YAClD,MAAM,wBACJ,oBAAoB,qBAAqB,IAAI;YAE/C,IACE,WAAW,KACX,iCAAiC,KACjC,wBAAwB,GACxB;gBACA,wJAAwJ;gBACxJ,MAAM,uCACJ,MACA,CAAC,AAAC,IAAI,8BAA8B,YAAY,IAC9C,CAAC,MACC,OACA,iCACA,qBAAqB,CAAC;gBAC5B,KAAK,QAAQ,CACX,wCACA;gBAGF,oJAAoJ;gBACpJ,MAAM,4BACJ,KAAK,SAAS,CAAC,gCACf,qBAAqB,6BACrB;gBACF,MAAM,qCACJ,AAAC,MAAM,CAAC,IAAI,4BAA4B,YAAY,CAAC,IACrD,CAAC,MAAM,OAAO,iCAAiC,qBAAqB;gBACtE,KAAK,QAAQ,CACX,sCACA;gBAGF,8IAA8I;gBAC9I,MAAM,0BAA0B,sBAAsB;gBACtD,MAAM,oCACJ,uCACA,qCACA;gBACF,KAAK,QAAQ,CACX,qCACA;gBAGF,iIAAiI;gBACjI,MAAM,+BACJ,oCAAoC,IAChC,AAAC,MAAM,WAAY,oCACnB;gBACN,KAAK,QAAQ,CACX,gCACA;gBAGF,qMAAqM;gBACrM,MAAM,sBAAsB,mBAAmB,mBAAmB,IAAI;gBACtE,MAAM,oBACJ,KAAK,SAAS,CAAC,wBACf,qBAAqB,qBACrB;gBACF,MAAM,4BACJ,uCAAuC,CAAC,IAAI,mBAAmB,IAC/D,qCACE,CAAC,IAAI,oBAAoB,mBAAmB,IAC9C;gBACF,KAAK,QAAQ,CAAC,6BAA6B;gBAE3C,2GAA2G;gBAC3G,MAAM,mBACJ,4BAA4B,IACxB,AAAC,MAAM,WAAY,4BACnB;gBACN,KAAK,QAAQ,CAAC,oBAAoB;YACpC,OAAO;gBACL,KAAK,QAAQ,CAAC,wCAAwC;gBACtD,KAAK,QAAQ,CAAC,sCAAsC;gBACpD,KAAK,QAAQ,CAAC,qCAAqC;gBACnD,KAAK,QAAQ,CAAC,gCAAgC;gBAC9C,KAAK,QAAQ,CAAC,6BAA6B;gBAC3C,KAAK,QAAQ,CAAC,oBAAoB;YACpC;QACF,OAAO;YACL,KAAK,QAAQ,CAAC,wCAAwC;YACtD,KAAK,QAAQ,CAAC,sCAAsC;YACpD,KAAK,QAAQ,CAAC,qCAAqC;YACnD,KAAK,QAAQ,CAAC,gCAAgC;YAC9C,KAAK,QAAQ,CAAC,6BAA6B;YAC3C,KAAK,QAAQ,CAAC,oBAAoB;QACpC;IACF,GAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,wIAAI;YAAE,GAAG,IAAI;sBACZ,cAAA,8OAAC;gBACC,WAAU;gBACV,UAAU,KAAK,YAAY,CAAC;;kCAG5B,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAIjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAIjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAIjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAIjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kLAAe;gCACd,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,GAAG;gCACH,UAAU;gCACV,yBAAwB;gCACxB,eAAe;;;;;;0CAEjB,8OAAC,0IAAK;gCACJ,SAAS,gBACP,eAAe,KAAK,KAAK,CAAC,uBAAuB;gCAEnD,WAAU;0CAET,eAAe,KAAK,KAAK,CAAC,uBAAuB;;;;;;;;;;;;kCAKtD,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAIjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAIjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kLAAe;gCACd,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,GAAG;gCACH,UAAU;gCACV,yBAAwB;gCACxB,eAAe;;;;;;0CAEjB,8OAAC,0IAAK;gCACJ,SAAS,gBACP,eAAe,KAAK,KAAK,CAAC,qBAAqB;gCAEjD,WAAU;0CAET,eAAe,KAAK,KAAK,CAAC,qBAAqB;;;;;;;;;;;;kCAKpD,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAIjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAIjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAIjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAIjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAIjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kLAAe;gCACd,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,GAAG;gCACH,UAAU;gCACV,yBAAwB;gCACxB,eAAe;;;;;;0CAEjB,8OAAC,0IAAK;gCACJ,SAAS,gBACP,eACE,KAAK,KAAK,CAAC,iCACX,KACA;gCAGJ,WAAU;0CAET,eACC,KAAK,KAAK,CAAC,iCACX,KACA;;;;;;;;;;;;kCAMN,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAIjB,8OAAC,kLAAe;wBACd,SAAS,KAAK,OAAO;wBACrB,MAAK;wBACL,GAAG;wBACH,UAAU;wBACV,eAAe;;;;;;kCAIjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kLAAe;gCACd,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,GAAG;gCACH,UAAU;gCACV,yBAAwB;gCACxB,eAAe;;;;;;0CAEjB,8OAAC,0IAAK;gCACJ,SAAS,gBACP,eACE,KAAK,KAAK,CAAC,qBACX,KACA;gCAGJ,WAAU;0CAET,eACC,KAAK,KAAK,CAAC,qBACX,KACA;;;;;;;;;;;;kCAKN,8OAAC,4IAAM;wBACL,MAAK;wBACL,WAAU;wBACV,UAAU;;4BAET,2BAAa,8OAAC,oOAAW;gCAAC,WAAU;;;;;;4BACpC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;AAMrB", "debugId": null}}, {"offset": {"line": 2836, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form.tsx"], "sourcesContent": ["import { WoodBeamGeometryForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form'\r\nimport { WoodCompositeGeometryForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-geometry-form'\r\nimport { WoodCompositePropertiesForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-composite-properties-form'\r\nimport { WoodGeneralForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-general-form'\r\nimport { WoodGeometryForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-geometry-form'\r\nimport { WoodPostInterventionCheckForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-post-intervention-check-form'\r\n\r\nimport {\r\n  Accordion as AccordionComponent,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from '@atlas/components/ui/accordion'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport type {\r\n  Module,\r\n  ModuleWithParamsWood,\r\n} from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useState } from 'react'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  module: ModuleWithParamsWood\r\n}\r\n\r\nexport const WoodPostInterventionForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  module,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.wood')\r\n  const { params } = module\r\n  const [refreshTrigger, setRefreshTrigger] = useState(0)\r\n\r\n  const [currentCompositeGeometry, setCurrentCompositeGeometry] = useState<any>(\r\n    (params as any)?.postIntervationCheck?.compositeGeometry ||\r\n      (params as any)?.compositeGeometry ||\r\n      null,\r\n  )\r\n\r\n  const [currentCompositeProperties, setCurrentCompositeProperties] =\r\n    useState<any>(\r\n      (params as any)?.postIntervationCheck?.compositeProperties ||\r\n        (params as any)?.compositeProperties ||\r\n        null,\r\n    )\r\n\r\n  // Callback to handle composite geometry updates with change detection\r\n  const handleCompositeGeometryChange = useCallback((newGeometry: any) => {\r\n    // Only update if the geometry data has actually changed\r\n    setCurrentCompositeGeometry((prev: any) => {\r\n      if (\r\n        !prev ||\r\n        prev.productId !== newGeometry.productId ||\r\n        prev.stripWidth !== newGeometry.stripWidth ||\r\n        prev.layersNumber !== newGeometry.layersNumber ||\r\n        prev.equivalentThickness !== newGeometry.equivalentThickness ||\r\n        prev.expositionType !== newGeometry.expositionType ||\r\n        prev.environmentalConversionFactor !==\r\n          newGeometry.environmentalConversionFactor\r\n      ) {\r\n        return newGeometry\r\n      }\r\n      return prev\r\n    })\r\n  }, [])\r\n\r\n  // Callback to handle composite properties updates with change detection\r\n  const handleCompositePropertiesChange = useCallback((newProperties: any) => {\r\n    // Only update if the properties data has actually changed\r\n    setCurrentCompositeProperties((prev: any) => {\r\n      if (\r\n        !prev ||\r\n        prev.sectionModulus !== newProperties.sectionModulus ||\r\n        prev.momentOfInertiaAboutY !== newProperties.momentOfInertiaAboutY ||\r\n        prev.frpElasticityModulus !== newProperties.frpElasticityModulus ||\r\n        prev.frpDesignMaximumStrain !== newProperties.frpDesignMaximumStrain\r\n      ) {\r\n        return newProperties\r\n      }\r\n      return prev\r\n    })\r\n  }, [])\r\n\r\n  // Create a dummy onSave function for read-only forms\r\n  const dummyOnSave = () => {}\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      <AccordionComponent type=\"single\" collapsible className=\"w-full\">\r\n        <AccordionItem value=\"post-intervention\">\r\n          <AccordionTrigger className=\"text-lg font-normal\">\r\n            {t('postForm.Post-Intervention Analysis')}\r\n          </AccordionTrigger>\r\n          <AccordionContent className=\"space-y-6 p-4\">\r\n            {/* Section 1: Initial Deformation (Read-only) */}\r\n            <div>\r\n              <h3 className=\"text-md font-semibold mb-4\">\r\n                {t('postForm.Initial Deformation - Read Only')}\r\n              </h3>\r\n              <div className=\"opacity-60 [&_button[type='submit']]:hidden [&_input]:pointer-events-none [&_input]:cursor-not-allowed [&_[data-slot='select-trigger']]:pointer-events-auto [&_[data-slot='select-trigger']]:opacity-100 [&_[data-slot='select-trigger']]:cursor-pointer\">\r\n                <WoodGeneralForm\r\n                  session={session}\r\n                  projectId={projectId}\r\n                  moduleId={moduleId}\r\n                  defaultValues={{\r\n                    initialDeformation: params?.initialDeformation || 0,\r\n                  }}\r\n                  onSave={dummyOnSave}\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <Separator />\r\n\r\n            {/* Section 2: Material Properties (Read-only) */}\r\n            <div>\r\n              <h3 className=\"text-md font-semibold mb-4\">\r\n                {t('postForm.Material Properties - Read Only')}\r\n              </h3>\r\n              <div className=\"opacity-60 [&_button[type='submit']]:hidden [&_input]:pointer-events-none [&_input]:cursor-not-allowed [&_[data-slot='select-trigger']]:pointer-events-auto [&_[data-slot='select-trigger']]:opacity-100 [&_[data-slot='select-trigger']]:cursor-pointer\">\r\n                <WoodGeometryForm\r\n                  session={session}\r\n                  projectId={projectId}\r\n                  moduleId={moduleId}\r\n                  defaultValues={params?.materialProperties as any}\r\n                  woodName={params?.materialProperties?.woodName}\r\n                  onSave={dummyOnSave}\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <Separator />\r\n\r\n            {/* Section 3: Geometry (Read-only) */}\r\n            <div>\r\n              <h3 className=\"text-md font-semibold mb-4\">\r\n                {t('postForm.Geometry - Read Only')}\r\n              </h3>\r\n              <div className=\"opacity-60 [&_button[type='submit']]:hidden [&_input]:pointer-events-none [&_input]:cursor-not-allowed [&_[data-slot='select-trigger']]:pointer-events-auto [&_[data-slot='select-trigger']]:opacity-100 [&_[data-slot='select-trigger']]:cursor-pointer\">\r\n                <WoodBeamGeometryForm\r\n                  session={session}\r\n                  projectId={projectId}\r\n                  moduleId={moduleId}\r\n                  defaultValues={params?.geometry as any}\r\n                  materialProperties={params?.materialProperties as any}\r\n                  onSave={dummyOnSave}\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <Separator />\r\n\r\n            {/* Section 4: Composite Geometry */}\r\n            <div>\r\n              <h3 className=\"text-md font-semibold mb-4\">\r\n                {t('compositeGeometry.title')}\r\n              </h3>\r\n              <WoodCompositeGeometryForm\r\n                session={session}\r\n                projectId={projectId}\r\n                moduleId={moduleId}\r\n                defaultValues={\r\n                  (params as any)?.postIntervationCheck?.compositeGeometry &&\r\n                  typeof (params as any).postIntervationCheck\r\n                    .compositeGeometry === 'object'\r\n                    ? (params as any).postIntervationCheck.compositeGeometry\r\n                    : (params as any)?.compositeGeometry &&\r\n                        typeof (params as any).compositeGeometry === 'object'\r\n                      ? (params as any).compositeGeometry\r\n                      : undefined\r\n                }\r\n                preInterventionData={params?.preIntervationCheck}\r\n                materialProperties={params?.materialProperties}\r\n                geometryProperties={params?.geometry}\r\n                onSave={() => setRefreshTrigger(prev => prev + 1)}\r\n                onGeometryChange={handleCompositeGeometryChange}\r\n                initialDeformation={params?.initialDeformation}\r\n              />\r\n            </div>\r\n\r\n            <Separator />\r\n\r\n            {/* Section 5: Composite Properties */}\r\n            <div>\r\n              <h3 className=\"text-md font-semibold mb-4\">\r\n                {t('compositeProperties.title')}\r\n              </h3>\r\n              <WoodCompositePropertiesForm\r\n                session={session}\r\n                projectId={projectId}\r\n                moduleId={moduleId}\r\n                defaultValues={\r\n                  (params as any)?.postIntervationCheck?.compositeProperties &&\r\n                  typeof (params as any).postIntervationCheck\r\n                    .compositeProperties === 'object'\r\n                    ? (params as any).postIntervationCheck.compositeProperties\r\n                    : (params as any)?.compositeProperties &&\r\n                        typeof (params as any).compositeProperties === 'object'\r\n                      ? (params as any).compositeProperties\r\n                      : undefined\r\n                }\r\n                compositeGeometry={currentCompositeGeometry}\r\n                preInterventionData={params?.preIntervationCheck}\r\n                materialProperties={params?.materialProperties}\r\n                geometryProperties={params?.geometry}\r\n                onSave={() => setRefreshTrigger(prev => prev + 1)}\r\n                onPropertiesChange={handleCompositePropertiesChange}\r\n                initialDeformation={params?.initialDeformation}\r\n              />\r\n            </div>\r\n\r\n            <Separator />\r\n\r\n            {/* Section 6: Post-Intervention Check Results */}\r\n            <div>\r\n              <h3 className=\"text-md font-semibold mb-4\">\r\n                {t('resultOfPostIntervationCheck.title')}\r\n              </h3>\r\n              <WoodPostInterventionCheckForm\r\n                key={`post-intervention-${refreshTrigger}-${currentCompositeProperties?.sectionModulus || 0}`}\r\n                session={session}\r\n                projectId={projectId}\r\n                moduleId={moduleId}\r\n                defaultValues={\r\n                  (params as any)?.postIntervationCheck\r\n                    ?.resultOfPostIntervationCheck &&\r\n                  typeof (params as any).postIntervationCheck\r\n                    .resultOfPostIntervationCheck === 'object'\r\n                    ? (params as any).postIntervationCheck\r\n                        .resultOfPostIntervationCheck\r\n                    : (params as any)?.resultOfPostIntervationCheck &&\r\n                        typeof (params as any).resultOfPostIntervationCheck ===\r\n                          'object'\r\n                      ? (params as any).resultOfPostIntervationCheck\r\n                      : undefined\r\n                }\r\n                preInterventionData={params?.preIntervationCheck}\r\n                materialProperties={params?.materialProperties}\r\n                geometryProperties={params?.geometry}\r\n                compositeGeometry={currentCompositeGeometry}\r\n                compositeProperties={currentCompositeProperties}\r\n                onSave={() => setRefreshTrigger(prev => prev + 1)}\r\n                initialDeformation={params?.initialDeformation}\r\n              />\r\n            </div>\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n      </AccordionComponent>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAEA;AAMA;AAOA;AACA;;;;;;;;;;;;AASO,MAAM,2BAA2B,CAAC,EACvC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,MAAM,EACA;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,IAAA,iNAAQ,EAAC;IAErD,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,IAAA,iNAAQ,EACtE,AAAC,QAAgB,sBAAsB,qBACpC,QAAgB,qBACjB;IAGJ,MAAM,CAAC,4BAA4B,8BAA8B,GAC/D,IAAA,iNAAQ,EACN,AAAC,QAAgB,sBAAsB,uBACpC,QAAgB,uBACjB;IAGN,sEAAsE;IACtE,MAAM,gCAAgC,IAAA,oNAAW,EAAC,CAAC;QACjD,wDAAwD;QACxD,4BAA4B,CAAC;YAC3B,IACE,CAAC,QACD,KAAK,SAAS,KAAK,YAAY,SAAS,IACxC,KAAK,UAAU,KAAK,YAAY,UAAU,IAC1C,KAAK,YAAY,KAAK,YAAY,YAAY,IAC9C,KAAK,mBAAmB,KAAK,YAAY,mBAAmB,IAC5D,KAAK,cAAc,KAAK,YAAY,cAAc,IAClD,KAAK,6BAA6B,KAChC,YAAY,6BAA6B,EAC3C;gBACA,OAAO;YACT;YACA,OAAO;QACT;IACF,GAAG,EAAE;IAEL,wEAAwE;IACxE,MAAM,kCAAkC,IAAA,oNAAW,EAAC,CAAC;QACnD,0DAA0D;QAC1D,8BAA8B,CAAC;YAC7B,IACE,CAAC,QACD,KAAK,cAAc,KAAK,cAAc,cAAc,IACpD,KAAK,qBAAqB,KAAK,cAAc,qBAAqB,IAClE,KAAK,oBAAoB,KAAK,cAAc,oBAAoB,IAChE,KAAK,sBAAsB,KAAK,cAAc,sBAAsB,EACpE;gBACA,OAAO;YACT;YACA,OAAO;QACT;IACF,GAAG,EAAE;IAEL,qDAAqD;IACrD,MAAM,cAAc,KAAO;IAE3B,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,kJAAkB;YAAC,MAAK;YAAS,WAAW;YAAC,WAAU;sBACtD,cAAA,8OAAC,sJAAa;gBAAC,OAAM;;kCACnB,8OAAC,yJAAgB;wBAAC,WAAU;kCACzB,EAAE;;;;;;kCAEL,8OAAC,yJAAgB;wBAAC,WAAU;;0CAE1B,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAEL,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8OAAe;4CACd,SAAS;4CACT,WAAW;4CACX,UAAU;4CACV,eAAe;gDACb,oBAAoB,QAAQ,sBAAsB;4CACpD;4CACA,QAAQ;;;;;;;;;;;;;;;;;0CAKd,8OAAC,kJAAS;;;;;0CAGV,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAEL,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gPAAgB;4CACf,SAAS;4CACT,WAAW;4CACX,UAAU;4CACV,eAAe,QAAQ;4CACvB,UAAU,QAAQ,oBAAoB;4CACtC,QAAQ;;;;;;;;;;;;;;;;;0CAKd,8OAAC,kJAAS;;;;;0CAGV,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAEL,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,4PAAoB;4CACnB,SAAS;4CACT,WAAW;4CACX,UAAU;4CACV,eAAe,QAAQ;4CACvB,oBAAoB,QAAQ;4CAC5B,QAAQ;;;;;;;;;;;;;;;;;0CAKd,8OAAC,kJAAS;;;;;0CAGV,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAEL,8OAAC,sQAAyB;wCACxB,SAAS;wCACT,WAAW;wCACX,UAAU;wCACV,eACE,AAAC,QAAgB,sBAAsB,qBACvC,OAAO,AAAC,OAAe,oBAAoB,CACxC,iBAAiB,KAAK,WACrB,AAAC,OAAe,oBAAoB,CAAC,iBAAiB,GACtD,AAAC,QAAgB,qBACf,OAAO,AAAC,OAAe,iBAAiB,KAAK,WAC7C,AAAC,OAAe,iBAAiB,GACjC;wCAER,qBAAqB,QAAQ;wCAC7B,oBAAoB,QAAQ;wCAC5B,oBAAoB,QAAQ;wCAC5B,QAAQ,IAAM,kBAAkB,CAAA,OAAQ,OAAO;wCAC/C,kBAAkB;wCAClB,oBAAoB,QAAQ;;;;;;;;;;;;0CAIhC,8OAAC,kJAAS;;;;;0CAGV,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAEL,8OAAC,0QAA2B;wCAC1B,SAAS;wCACT,WAAW;wCACX,UAAU;wCACV,eACE,AAAC,QAAgB,sBAAsB,uBACvC,OAAO,AAAC,OAAe,oBAAoB,CACxC,mBAAmB,KAAK,WACvB,AAAC,OAAe,oBAAoB,CAAC,mBAAmB,GACxD,AAAC,QAAgB,uBACf,OAAO,AAAC,OAAe,mBAAmB,KAAK,WAC/C,AAAC,OAAe,mBAAmB,GACnC;wCAER,mBAAmB;wCACnB,qBAAqB,QAAQ;wCAC7B,oBAAoB,QAAQ;wCAC5B,oBAAoB,QAAQ;wCAC5B,QAAQ,IAAM,kBAAkB,CAAA,OAAQ,OAAO;wCAC/C,oBAAoB;wCACpB,oBAAoB,QAAQ;;;;;;;;;;;;0CAIhC,8OAAC,kJAAS;;;;;0CAGV,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDACX,EAAE;;;;;;kDAEL,8OAAC,kRAA6B;wCAE5B,SAAS;wCACT,WAAW;wCACX,UAAU;wCACV,eACE,AAAC,QAAgB,sBACb,gCACJ,OAAO,AAAC,OAAe,oBAAoB,CACxC,4BAA4B,KAAK,WAChC,AAAC,OAAe,oBAAoB,CACjC,4BAA4B,GAC/B,AAAC,QAAgB,gCACf,OAAO,AAAC,OAAe,4BAA4B,KACjD,WACF,AAAC,OAAe,4BAA4B,GAC5C;wCAER,qBAAqB,QAAQ;wCAC7B,oBAAoB,QAAQ;wCAC5B,oBAAoB,QAAQ;wCAC5B,mBAAmB;wCACnB,qBAAqB;wCACrB,QAAQ,IAAM,kBAAkB,CAAA,OAAQ,OAAO;wCAC/C,oBAAoB,QAAQ;uCAvBvB,CAAC,kBAAkB,EAAE,eAAe,CAAC,EAAE,4BAA4B,kBAAkB,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+B7G", "debugId": null}}, {"offset": {"line": 3166, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/wood/wood-params-form.tsx"], "sourcesContent": ["import { WoodBeamGeometryForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-beam-geometry-form'\r\nimport { WoodGeneralForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-general-form'\r\nimport { WoodGeometryForm as WoodMaterialPropertiesForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-geometry-form'\r\nimport { WoodPreInterventionCheckForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/sections/wood-pre-intervention-check-form'\r\nimport { WoodPostInterventionForm } from '@atlas/components/common/atlas/module-detail/params-forms/wood/wood-post-intervention-form'\r\n\r\nimport {\r\n  Accordion as AccordionComponent,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from '@atlas/components/ui/accordion'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport type {\r\n  Module,\r\n  ModuleWithParamsWood,\r\n} from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { woodParamsCheckSchema, woodCalculationCheck } from '@atlas/types/schemas/wood-form'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useMemo, useState } from 'react'\r\nimport { ModuleReportGenerationSection } from '../../module-report-generation-section'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  module: ModuleWithParamsWood\r\n}\r\n\r\nexport const WoodParamsForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  module,\r\n}: Props) => {\r\n  const { params } = module\r\n  const [openItems, setOpenItems] = useState(['0'])\r\n  const t = useTranslations('forms.project-params.wood')\r\n\r\n  const handleItemSaved = useCallback((id: string) => {\r\n    const nextId = String(Number(id) + 1)\r\n    setOpenItems(old => {\r\n      const temp = old.filter(v => v !== id)\r\n      return old.includes(nextId) ? temp : [...temp, nextId]\r\n    })\r\n  }, [])\r\n\r\n  const { success } = useMemo(\r\n    () => woodParamsCheckSchema.safeParse(params),\r\n    [params],\r\n  )\r\n\r\n  const { success: enableReport } = useMemo(\r\n      () => woodCalculationCheck.safeParse(module),\r\n      [module],\r\n    )\r\n\r\n  return (\r\n    <div>\r\n      <AccordionComponent\r\n        type=\"multiple\"\r\n        value={openItems}\r\n        onValueChange={setOpenItems}\r\n      >\r\n        <AccordionItem value=\"0\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">{t('general.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <WoodGeneralForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={{\r\n                initialDeformation: params?.initialDeformation,\r\n              }}\r\n              onSave={() => handleItemSaved('0')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"1\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('materialProperties.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <WoodMaterialPropertiesForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={\r\n                // Wood material properties are stored in materialProperties field\r\n                // Check if params.materialProperties has wood material properties structure\r\n                params?.materialProperties &&\r\n                typeof params.materialProperties === 'object' &&\r\n                'category' in params.materialProperties\r\n                  ? (params.materialProperties as any) // It's wood material properties\r\n                  : undefined // No wood material properties saved yet, start with empty form\r\n              }\r\n              onSave={() => handleItemSaved('1')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"2\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">{t('geometry.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <WoodBeamGeometryForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={\r\n                // Wood geometry properties are stored in geometry field\r\n                params?.geometry &&\r\n                typeof params.geometry === 'object' &&\r\n                'beamSectionWidth' in params.geometry\r\n                  ? (params.geometry as any) // It's wood geometry properties\r\n                  : undefined // No wood geometry properties saved yet, start with empty form\r\n              }\r\n              materialProperties={\r\n                // Pass material properties for calculations\r\n                params?.materialProperties &&\r\n                typeof params.materialProperties === 'object' &&\r\n                'category' in params.materialProperties\r\n                  ? (params.materialProperties as any) // Wood material properties\r\n                  : undefined // No material properties available yet\r\n              }\r\n              onSave={() => handleItemSaved('2')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"3\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('preIntervationCheck.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <WoodPreInterventionCheckForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              initialDeformation={params?.initialDeformation}\r\n              defaultValues={\r\n                // Wood pre-intervention check properties are stored in preIntervationCheck field\r\n                params?.preIntervationCheck &&\r\n                typeof params.preIntervationCheck === 'object'\r\n                  ? (params.preIntervationCheck as any) // It's wood pre-intervention check properties\r\n                  : undefined // No pre-intervention check properties saved yet, start with empty form\r\n              }\r\n              geometryProperties={\r\n                // Pass geometry properties for calculations\r\n                params?.geometry &&\r\n                typeof params.geometry === 'object' &&\r\n                'beamSectionWidth' in params.geometry\r\n                  ? (params.geometry as any) // Wood geometry properties\r\n                  : undefined // No geometry properties available yet\r\n              }\r\n              onSave={() => handleItemSaved('3')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n      </AccordionComponent>\r\n      {success && (\r\n        <>\r\n          <Separator />\r\n          <WoodPostInterventionForm\r\n            module={module}\r\n            session={session}\r\n            projectId={projectId}\r\n            moduleId={moduleId}\r\n          />\r\n          <div className=\"pt-4\">\r\n                      <ModuleReportGenerationSection\r\n                        moduleId={moduleId}\r\n                        enabled={enableReport}\r\n                        projectId={projectId}\r\n                        session={session}\r\n                      />\r\n                    </div>\r\n        </>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAEA;AAMA;AAMA;AAEA;AACA;AACA;;;;;;;;;;;;;AASO,MAAM,iBAAiB,CAAC,EAC7B,OAAO,EACP,SAAS,EACT,QAAQ,EACR,MAAM,EACA;IACN,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,iNAAQ,EAAC;QAAC;KAAI;IAChD,MAAM,IAAI,IAAA,yNAAe,EAAC;IAE1B,MAAM,kBAAkB,IAAA,oNAAW,EAAC,CAAC;QACnC,MAAM,SAAS,OAAO,OAAO,MAAM;QACnC,aAAa,CAAA;YACX,MAAM,OAAO,IAAI,MAAM,CAAC,CAAA,IAAK,MAAM;YACnC,OAAO,IAAI,QAAQ,CAAC,UAAU,OAAO;mBAAI;gBAAM;aAAO;QACxD;IACF,GAAG,EAAE;IAEL,MAAM,EAAE,OAAO,EAAE,GAAG,IAAA,gNAAO,EACzB,IAAM,gKAAqB,CAAC,SAAS,CAAC,SACtC;QAAC;KAAO;IAGV,MAAM,EAAE,SAAS,YAAY,EAAE,GAAG,IAAA,gNAAO,EACrC,IAAM,+JAAoB,CAAC,SAAS,CAAC,SACrC;QAAC;KAAO;IAGZ,qBACE,8OAAC;;0BACC,8OAAC,kJAAkB;gBACjB,MAAK;gBACL,OAAO;gBACP,eAAe;;kCAEf,8OAAC,sJAAa;wBAAC,OAAM;;0CACnB,8OAAC,yJAAgB;0CACf,cAAA,8OAAC;oCAAG,WAAU;8CAAuB,EAAE;;;;;;;;;;;0CAEzC,8OAAC,yJAAgB;0CACf,cAAA,8OAAC,8OAAe;oCACd,SAAS;oCACT,WAAW;oCACX,UAAU;oCACV,eAAe;wCACb,oBAAoB,QAAQ;oCAC9B;oCACA,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;kCAIpC,8OAAC,sJAAa;wBAAC,OAAM;;0CACnB,8OAAC,yJAAgB;0CACf,cAAA,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;;;;;;0CAGP,8OAAC,yJAAgB;0CACf,cAAA,8OAAC,gPAA0B;oCACzB,SAAS;oCACT,WAAW;oCACX,UAAU;oCACV,eACE,kEAAkE;oCAClE,4EAA4E;oCAC5E,QAAQ,sBACR,OAAO,OAAO,kBAAkB,KAAK,YACrC,cAAc,OAAO,kBAAkB,GAClC,OAAO,kBAAkB,GAC1B,UAAU,+DAA+D;;oCAE/E,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;kCAIpC,8OAAC,sJAAa;wBAAC,OAAM;;0CACnB,8OAAC,yJAAgB;0CACf,cAAA,8OAAC;oCAAG,WAAU;8CAAuB,EAAE;;;;;;;;;;;0CAEzC,8OAAC,yJAAgB;0CACf,cAAA,8OAAC,4PAAoB;oCACnB,SAAS;oCACT,WAAW;oCACX,UAAU;oCACV,eACE,wDAAwD;oCACxD,QAAQ,YACR,OAAO,OAAO,QAAQ,KAAK,YAC3B,sBAAsB,OAAO,QAAQ,GAChC,OAAO,QAAQ,GAChB,UAAU,+DAA+D;;oCAE/E,oBACE,4CAA4C;oCAC5C,QAAQ,sBACR,OAAO,OAAO,kBAAkB,KAAK,YACrC,cAAc,OAAO,kBAAkB,GAClC,OAAO,kBAAkB,GAC1B,UAAU,uCAAuC;;oCAEvD,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;kCAIpC,8OAAC,sJAAa;wBAAC,OAAM;;0CACnB,8OAAC,yJAAgB;0CACf,cAAA,8OAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;;;;;;0CAGP,8OAAC,yJAAgB;0CACf,cAAA,8OAAC,gRAA4B;oCAC3B,SAAS;oCACT,WAAW;oCACX,UAAU;oCACV,oBAAoB,QAAQ;oCAC5B,eACE,iFAAiF;oCACjF,QAAQ,uBACR,OAAO,OAAO,mBAAmB,KAAK,WACjC,OAAO,mBAAmB,GAC3B,UAAU,wEAAwE;;oCAExF,oBACE,4CAA4C;oCAC5C,QAAQ,YACR,OAAO,OAAO,QAAQ,KAAK,YAC3B,sBAAsB,OAAO,QAAQ,GAChC,OAAO,QAAQ,GAChB,UAAU,uCAAuC;;oCAEvD,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;YAKrC,yBACC;;kCACE,8OAAC,kJAAS;;;;;kCACV,8OAAC,wPAAwB;wBACvB,QAAQ;wBACR,SAAS;wBACT,WAAW;wBACX,UAAU;;;;;;kCAEZ,8OAAC;wBAAI,WAAU;kCACH,cAAA,8OAAC,uOAA6B;4BAC5B,UAAU;4BACV,SAAS;4BACT,WAAW;4BACX,SAAS;;;;;;;;;;;;;;;;;;;AAOjC", "debugId": null}}]}