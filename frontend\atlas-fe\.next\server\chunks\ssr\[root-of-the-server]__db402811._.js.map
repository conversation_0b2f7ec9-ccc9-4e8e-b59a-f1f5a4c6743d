{"version": 3, "sources": [], "sections": [{"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/api/auth/doFinalSignOutHandshake.ts"], "sourcesContent": ["import axios, { type AxiosError } from 'axios'\r\nimport type { JWT } from 'next-auth/jwt'\r\n\r\n// this performs the final handshake for the keycloak\r\n// provider, the way it's written could also potentially\r\n// perform the action for other providers as well\r\nexport const doFinalSignOutHandshake = async (jwt: JWT) => {\r\n  const { idToken } = jwt\r\n\r\n  try {\r\n    // Add the id_token_hint to the query string\r\n    const params = new URLSearchParams()\r\n    params.append('id_token_hint', idToken)\r\n    const { status, statusText } = await axios.get(\r\n      `${process.env.AUTH_KEYCLOAK_ISSUER}/protocol/openid-connect/logout?${params.toString()}`,\r\n    )\r\n\r\n    // The response body should contain a confirmation that the user has been logged out\r\n    console.debug('Completed post-logout handshake', status, statusText)\r\n  } catch (e: any) {\r\n    console.error(\r\n      'Unable to perform post-logout handshake',\r\n      (e as AxiosError)?.code || e,\r\n    )\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;;AAMO,MAAM,0BAA0B,OAAO;IAC5C,MAAM,EAAE,OAAO,EAAE,GAAG;IAEpB,IAAI;QACF,4CAA4C;QAC5C,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,CAAC,iBAAiB;QAC/B,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,MAAM,gJAAK,CAAC,GAAG,CAC5C,GAAG,QAAQ,GAAG,CAAC,oBAAoB,CAAC,gCAAgC,EAAE,OAAO,QAAQ,IAAI;QAG3F,oFAAoF;QACpF,QAAQ,KAAK,CAAC,mCAAmC,QAAQ;IAC3D,EAAE,OAAO,GAAQ;QACf,QAAQ,KAAK,CACX,2CACA,AAAC,GAAkB,QAAQ;IAE/B;AACF", "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/lib/api/auth/refreshAccessToken.ts"], "sourcesContent": ["import type { JWT } from 'next-auth/jwt'\r\n\r\nexport const refreshAccessToken = async (token: JWT): Promise<JWT> => {\r\n  try {\r\n    if (\r\n      token.refreshTokenExpires &&\r\n      Date.now() >= token.refreshTokenExpires * 1000\r\n    ) {\r\n      return {\r\n        ...token,\r\n        error: 'RefreshTokenExpired',\r\n        errorDetails: 'The refresh token itself has expired.',\r\n      }\r\n    }\r\n\r\n    if (!token.refreshToken) {\r\n      throw new Error('MissingRefreshToken')\r\n    }\r\n\r\n    const clientId = process.env.AUTH_KEYCLOAK_ID\r\n    const clientSecret = process.env.AUTH_KEYCLOAK_SECRET\r\n    const tokenUrl = `${process.env.AUTH_KEYCLOAK_ISSUER}/protocol/openid-connect/token`\r\n\r\n    if (!(clientId && tokenUrl)) {\r\n      throw new Error('MissingKeycloakConfig')\r\n    }\r\n\r\n    const params = new URLSearchParams()\r\n    params.append('client_id', clientId)\r\n    if (clientSecret) {\r\n      params.append('client_secret', clientSecret)\r\n    }\r\n    params.append('grant_type', 'refresh_token')\r\n    params.append('refresh_token', token.refreshToken)\r\n\r\n    console.log('Try to refresh access token...')\r\n    const response = await fetch(tokenUrl, {\r\n      method: 'POST',\r\n      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },\r\n      body: params.toString(),\r\n    })\r\n\r\n    const refreshedTokens = await response.json()\r\n\r\n    if (!response.ok) {\r\n      console.error('Error during refresh token:', refreshedTokens)\r\n      if (refreshedTokens.error === 'invalid_grant') {\r\n        return {\r\n          ...token,\r\n          error: 'RefreshTokenInvalidated',\r\n          errorDetails:\r\n            refreshedTokens.error_description ||\r\n            'Refresh token validation failed on server.',\r\n        }\r\n      }\r\n      throw new Error(refreshedTokens.error || 'RefreshFailed')\r\n    }\r\n\r\n    const nowInSeconds = Math.floor(Date.now() / 1000)\r\n    const newAccessTokenExpiresAt = nowInSeconds + refreshedTokens.expires_in\r\n    let newRefreshTokenExpiresAt = token.refreshTokenExpires\r\n    if (typeof refreshedTokens.refreshTokenExpires === 'number') {\r\n      newRefreshTokenExpiresAt =\r\n        nowInSeconds + refreshedTokens.refreshTokenExpires\r\n    }\r\n\r\n    return {\r\n      ...token,\r\n      accessToken: refreshedTokens.access_token,\r\n      idToken: refreshedTokens.id_token,\r\n      accessTokenExpires: newAccessTokenExpiresAt,\r\n      refreshToken: refreshedTokens.refresh_token ?? token.refreshToken,\r\n      refreshTokenExpires: newRefreshTokenExpiresAt,\r\n      error: undefined,\r\n      errorDetails: undefined,\r\n    }\r\n  } catch (error: any) {\r\n    console.error('Exception during refresh token:', error)\r\n    return {\r\n      ...token,\r\n      error: 'RefreshAccessTokenError',\r\n      errorDetails: error.message || 'UnknownError',\r\n    }\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAEO,MAAM,qBAAqB,OAAO;IACvC,IAAI;QACF,IACE,MAAM,mBAAmB,IACzB,KAAK,GAAG,MAAM,MAAM,mBAAmB,GAAG,MAC1C;YACA,OAAO;gBACL,GAAG,KAAK;gBACR,OAAO;gBACP,cAAc;YAChB;QACF;QAEA,IAAI,CAAC,MAAM,YAAY,EAAE;YACvB,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,QAAQ,GAAG,CAAC,gBAAgB;QAC7C,MAAM,eAAe,QAAQ,GAAG,CAAC,oBAAoB;QACrD,MAAM,WAAW,GAAG,QAAQ,GAAG,CAAC,oBAAoB,CAAC,8BAA8B,CAAC;QAEpF,IAAI,CAAC,CAAC,YAAY,QAAQ,GAAG;YAC3B,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,SAAS,IAAI;QACnB,OAAO,MAAM,CAAC,aAAa;QAC3B,IAAI,cAAc;YAChB,OAAO,MAAM,CAAC,iBAAiB;QACjC;QACA,OAAO,MAAM,CAAC,cAAc;QAC5B,OAAO,MAAM,CAAC,iBAAiB,MAAM,YAAY;QAEjD,QAAQ,GAAG,CAAC;QACZ,MAAM,WAAW,MAAM,MAAM,UAAU;YACrC,QAAQ;YACR,SAAS;gBAAE,gBAAgB;YAAoC;YAC/D,MAAM,OAAO,QAAQ;QACvB;QAEA,MAAM,kBAAkB,MAAM,SAAS,IAAI;QAE3C,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,IAAI,gBAAgB,KAAK,KAAK,iBAAiB;gBAC7C,OAAO;oBACL,GAAG,KAAK;oBACR,OAAO;oBACP,cACE,gBAAgB,iBAAiB,IACjC;gBACJ;YACF;YACA,MAAM,IAAI,MAAM,gBAAgB,KAAK,IAAI;QAC3C;QAEA,MAAM,eAAe,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;QAC7C,MAAM,0BAA0B,eAAe,gBAAgB,UAAU;QACzE,IAAI,2BAA2B,MAAM,mBAAmB;QACxD,IAAI,OAAO,gBAAgB,mBAAmB,KAAK,UAAU;YAC3D,2BACE,eAAe,gBAAgB,mBAAmB;QACtD;QAEA,OAAO;YACL,GAAG,KAAK;YACR,aAAa,gBAAgB,YAAY;YACzC,SAAS,gBAAgB,QAAQ;YACjC,oBAAoB;YACpB,cAAc,gBAAgB,aAAa,IAAI,MAAM,YAAY;YACjE,qBAAqB;YACrB,OAAO;YACP,cAAc;QAChB;IACF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO;YACL,GAAG,KAAK;YACR,OAAO;YACP,cAAc,MAAM,OAAO,IAAI;QACjC;IACF;AACF", "debugId": null}}, {"offset": {"line": 186, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/auth.ts"], "sourcesContent": ["import { doFinalSignOutHandshake } from '@atlas/lib/api/auth/doFinalSignOutHandshake'\r\nimport { refreshAccessToken } from '@atlas/lib/api/auth/refreshAccessToken'\r\nimport type { AdapterSession } from '@auth/core/adapters'\r\nimport NextAuth, { type Session } from 'next-auth'\r\nimport type { JWT } from 'next-auth/jwt'\r\nimport Keycloak from 'next-auth/providers/keycloak'\r\n\r\nconst ACCESS_TOKEN_BUFFER_SECONDS = 60\r\n\r\nexport const { handlers, signIn, signOut, auth } = NextAuth({\r\n  providers: [Keycloak],\r\n\r\n  callbacks: {\r\n    /**\r\n     * Controlla se l'utente è autorizzato ad accedere.\r\n     * Utile per middleware o protezione server-side.\r\n     *\r\n     */\r\n    authorized({ auth }) {\r\n      return !!auth\r\n    },\r\n\r\n    /**\r\n     * Callback JWT: Gestisce la creazione e l'aggiornamento del token JWT.\r\n     * Eseguito al login iniziale e ad ogni richiesta successiva che usa il JWT.\r\n     */\r\n    async jwt({ token, account, user }) {\r\n      const now = Math.floor(Date.now() / 1000)\r\n\r\n      if (account?.access_token && account?.id_token && user) {\r\n        console.debug('[AUTH] jwt - Initial sign-in.')\r\n\r\n        const accessTokenExpires =\r\n          account.expires_at ?? now + (account.expires_in ?? 300)\r\n\r\n        let refreshTokenExpires: number | undefined\r\n        if (typeof account.refresh_expires_in === 'number') {\r\n          refreshTokenExpires = now + account.refresh_expires_in\r\n          console.debug(\r\n            `[AUTH] jwt - Refresh token initial expiry: ${new Date(refreshTokenExpires * 1000).toISOString()}`,\r\n          )\r\n        }\r\n\r\n        return {\r\n          ...token,\r\n          accessToken: account.access_token,\r\n          idToken: account.id_token,\r\n          refreshToken: account.refresh_token,\r\n          accessTokenExpires: accessTokenExpires,\r\n          refreshTokenExpires: refreshTokenExpires,\r\n          user: user,\r\n          error: undefined,\r\n        }\r\n      }\r\n\r\n      if (token.refreshTokenExpires && now >= token.refreshTokenExpires) {\r\n        console.debug(\r\n          '[AUTH] jwt - Refresh token expired. Invalidating session.',\r\n        )\r\n        return null\r\n      }\r\n\r\n      const isAccessTokenValid =\r\n        token.accessTokenExpires &&\r\n        now < token.accessTokenExpires - ACCESS_TOKEN_BUFFER_SECONDS\r\n\r\n      if (isAccessTokenValid) {\r\n        return token\r\n      }\r\n\r\n      console.debug(\r\n        '[AUTH] jwt - Access token expired or requires refresh. Attempting refresh...',\r\n      )\r\n\r\n      if (!token.refreshToken) {\r\n        console.debug('[AUTH] jwt - Cannot refresh: Missing refresh token.')\r\n        return null\r\n      }\r\n\r\n      try {\r\n        const refreshedToken = await refreshAccessToken(token)\r\n        if (\r\n          refreshedToken.error === 'RefreshTokenExpired' ||\r\n          refreshedToken.error === 'RefreshTokenInvalidated'\r\n        ) {\r\n          console.debug(\r\n            `[AUTH] jwt - Refresh failed (${refreshedToken.error}). Invalidating session.`,\r\n          )\r\n          return null\r\n        }\r\n\r\n        return refreshedToken\r\n      } catch (error) {\r\n        console.error('[AUTH] jwt - Exception during token refresh:', error)\r\n        return null\r\n      }\r\n    },\r\n\r\n    /**\r\n     * Callback Session: Gestisce l'oggetto sessione accessibile lato client.\r\n     * Riceve il token JWT aggiornato dalla callback `jwt`.\r\n     */\r\n    session({ session, token }: { session: Session; token: JWT }): Session {\r\n      session.accessToken = token.accessToken\r\n      session.idToken = token.idToken\r\n      session.user = token.user\r\n      session.error = token.error\r\n      session.accessTokenExpires = token.accessTokenExpires\r\n\r\n      if (session.error) {\r\n        console.warn(\r\n          `[AUTH] session - Propagating token error to client session: ${session.error}`,\r\n        )\r\n      }\r\n\r\n      return session\r\n    },\r\n  },\r\n  session: {\r\n    strategy: 'jwt',\r\n  },\r\n  events: {\r\n    signOut: (\r\n      message:\r\n        | { token: JWT | null }\r\n        // biome-ignore lint/suspicious/noConfusingVoidType: necessary\r\n        | { session: void | AdapterSession | null },\r\n    ) => {\r\n      if ('token' in message && message.token) {\r\n        return doFinalSignOutHandshake(message.token)\r\n      }\r\n      return Promise.resolve()\r\n    },\r\n  },\r\n})\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AAEA;AAEA;AAAA;;;;;AAEA,MAAM,8BAA8B;AAE7B,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,IAAA,gKAAQ,EAAC;IAC1D,WAAW;QAAC,kKAAQ;KAAC;IAErB,WAAW;QACT;;;;KAIC,GACD,YAAW,EAAE,IAAI,EAAE;YACjB,OAAO,CAAC,CAAC;QACX;QAEA;;;KAGC,GACD,MAAM,KAAI,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE;YAChC,MAAM,MAAM,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;YAEpC,IAAI,SAAS,gBAAgB,SAAS,YAAY,MAAM;gBACtD,QAAQ,KAAK,CAAC;gBAEd,MAAM,qBACJ,QAAQ,UAAU,IAAI,MAAM,CAAC,QAAQ,UAAU,IAAI,GAAG;gBAExD,IAAI;gBACJ,IAAI,OAAO,QAAQ,kBAAkB,KAAK,UAAU;oBAClD,sBAAsB,MAAM,QAAQ,kBAAkB;oBACtD,QAAQ,KAAK,CACX,CAAC,2CAA2C,EAAE,IAAI,KAAK,sBAAsB,MAAM,WAAW,IAAI;gBAEtG;gBAEA,OAAO;oBACL,GAAG,KAAK;oBACR,aAAa,QAAQ,YAAY;oBACjC,SAAS,QAAQ,QAAQ;oBACzB,cAAc,QAAQ,aAAa;oBACnC,oBAAoB;oBACpB,qBAAqB;oBACrB,MAAM;oBACN,OAAO;gBACT;YACF;YAEA,IAAI,MAAM,mBAAmB,IAAI,OAAO,MAAM,mBAAmB,EAAE;gBACjE,QAAQ,KAAK,CACX;gBAEF,OAAO;YACT;YAEA,MAAM,qBACJ,MAAM,kBAAkB,IACxB,MAAM,MAAM,kBAAkB,GAAG;YAEnC,IAAI,oBAAoB;gBACtB,OAAO;YACT;YAEA,QAAQ,KAAK,CACX;YAGF,IAAI,CAAC,MAAM,YAAY,EAAE;gBACvB,QAAQ,KAAK,CAAC;gBACd,OAAO;YACT;YAEA,IAAI;gBACF,MAAM,iBAAiB,MAAM,IAAA,qKAAkB,EAAC;gBAChD,IACE,eAAe,KAAK,KAAK,yBACzB,eAAe,KAAK,KAAK,2BACzB;oBACA,QAAQ,KAAK,CACX,CAAC,6BAA6B,EAAE,eAAe,KAAK,CAAC,wBAAwB,CAAC;oBAEhF,OAAO;gBACT;gBAEA,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gDAAgD;gBAC9D,OAAO;YACT;QACF;QAEA;;;KAGC,GACD,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAoC;YAC1D,QAAQ,WAAW,GAAG,MAAM,WAAW;YACvC,QAAQ,OAAO,GAAG,MAAM,OAAO;YAC/B,QAAQ,IAAI,GAAG,MAAM,IAAI;YACzB,QAAQ,KAAK,GAAG,MAAM,KAAK;YAC3B,QAAQ,kBAAkB,GAAG,MAAM,kBAAkB;YAErD,IAAI,QAAQ,KAAK,EAAE;gBACjB,QAAQ,IAAI,CACV,CAAC,4DAA4D,EAAE,QAAQ,KAAK,EAAE;YAElF;YAEA,OAAO;QACT;IACF;IACA,SAAS;QACP,UAAU;IACZ;IACA,QAAQ;QACN,SAAS,CACP;YAKA,IAAI,WAAW,WAAW,QAAQ,KAAK,EAAE;gBACvC,OAAO,IAAA,+KAAuB,EAAC,QAAQ,KAAK;YAC9C;YACA,OAAO,QAAQ,OAAO;QACxB;IACF;AACF", "debugId": null}}, {"offset": {"line": 298, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/sidebar.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Sidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"Sidebar\",\n);\nexport const SidebarContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarContent() from the server but SidebarContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarContent\",\n);\nexport const SidebarFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarFooter() from the server but <PERSON><PERSON><PERSON>ooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarFooter\",\n);\nexport const SidebarGroup = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroup() from the server but SidebarGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarGroup\",\n);\nexport const SidebarGroupAction = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupAction() from the server but SidebarGroupAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarGroupAction\",\n);\nexport const SidebarGroupContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupContent() from the server but SidebarGroupContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarGroupContent\",\n);\nexport const SidebarGroupLabel = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupLabel() from the server but SidebarGroupLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarGroupLabel\",\n);\nexport const SidebarHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarHeader() from the server but SidebarHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarHeader\",\n);\nexport const SidebarInput = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarInput() from the server but SidebarInput is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarInput\",\n);\nexport const SidebarInset = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarInset() from the server but SidebarInset is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarInset\",\n);\nexport const SidebarMenu = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenu() from the server but SidebarMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenu\",\n);\nexport const SidebarMenuAction = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuAction() from the server but SidebarMenuAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuAction\",\n);\nexport const SidebarMenuBadge = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuBadge() from the server but SidebarMenuBadge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuBadge\",\n);\nexport const SidebarMenuButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuButton() from the server but SidebarMenuButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuButton\",\n);\nexport const SidebarMenuItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuItem() from the server but SidebarMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuItem\",\n);\nexport const SidebarMenuSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSkeleton() from the server but SidebarMenuSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuSkeleton\",\n);\nexport const SidebarMenuSub = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSub() from the server but SidebarMenuSub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuSub\",\n);\nexport const SidebarMenuSubButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSubButton() from the server but SidebarMenuSubButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuSubButton\",\n);\nexport const SidebarMenuSubItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSubItem() from the server but SidebarMenuSubItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarMenuSubItem\",\n);\nexport const SidebarProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarProvider\",\n);\nexport const SidebarRail = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarRail() from the server but SidebarRail is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarRail\",\n);\nexport const SidebarSeparator = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarSeparator() from the server but SidebarSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarSeparator\",\n);\nexport const SidebarTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarTrigger() from the server but SidebarTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"SidebarTrigger\",\n);\nexport const useSidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx <module evaluation>\",\n    \"useSidebar\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACvE;;AACO,MAAM,UAAU,IAAA,wQAAuB,EAC1C;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,+DACA;AAEG,MAAM,iBAAiB,IAAA,wQAAuB,EACjD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,+DACA;AAEG,MAAM,gBAAgB,IAAA,wQAAuB,EAChD;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,+DACA;AAEG,MAAM,eAAe,IAAA,wQAAuB,EAC/C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,+DACA;AAEG,MAAM,qBAAqB,IAAA,wQAAuB,EACrD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,+DACA;AAEG,MAAM,sBAAsB,IAAA,wQAAuB,EACtD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,+DACA;AAEG,MAAM,oBAAoB,IAAA,wQAAuB,EACpD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,+DACA;AAEG,MAAM,gBAAgB,IAAA,wQAAuB,EAChD;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,+DACA;AAEG,MAAM,eAAe,IAAA,wQAAuB,EAC/C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,+DACA;AAEG,MAAM,eAAe,IAAA,wQAAuB,EAC/C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,+DACA;AAEG,MAAM,cAAc,IAAA,wQAAuB,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,+DACA;AAEG,MAAM,oBAAoB,IAAA,wQAAuB,EACpD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,+DACA;AAEG,MAAM,mBAAmB,IAAA,wQAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,+DACA;AAEG,MAAM,oBAAoB,IAAA,wQAAuB,EACpD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,+DACA;AAEG,MAAM,kBAAkB,IAAA,wQAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,+DACA;AAEG,MAAM,sBAAsB,IAAA,wQAAuB,EACtD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,+DACA;AAEG,MAAM,iBAAiB,IAAA,wQAAuB,EACjD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,+DACA;AAEG,MAAM,uBAAuB,IAAA,wQAAuB,EACvD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,+DACA;AAEG,MAAM,qBAAqB,IAAA,wQAAuB,EACrD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,+DACA;AAEG,MAAM,kBAAkB,IAAA,wQAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,+DACA;AAEG,MAAM,cAAc,IAAA,wQAAuB,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,+DACA;AAEG,MAAM,mBAAmB,IAAA,wQAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,+DACA;AAEG,MAAM,iBAAiB,IAAA,wQAAuB,EACjD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,+DACA;AAEG,MAAM,aAAa,IAAA,wQAAuB,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,+DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 427, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/sidebar.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Sidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"Sidebar\",\n);\nexport const SidebarContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarContent() from the server but SidebarContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarContent\",\n);\nexport const SidebarFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarFooter() from the server but <PERSON><PERSON>Footer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarFooter\",\n);\nexport const SidebarGroup = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroup() from the server but SidebarGroup is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarGroup\",\n);\nexport const SidebarGroupAction = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupAction() from the server but SidebarGroupAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarGroupAction\",\n);\nexport const SidebarGroupContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupContent() from the server but SidebarGroupContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarGroupContent\",\n);\nexport const SidebarGroupLabel = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarGroupLabel() from the server but SidebarGroupLabel is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarGroupLabel\",\n);\nexport const SidebarHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarHeader() from the server but SidebarHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarHeader\",\n);\nexport const SidebarInput = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarInput() from the server but SidebarInput is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarInput\",\n);\nexport const SidebarInset = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarInset() from the server but SidebarInset is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarInset\",\n);\nexport const SidebarMenu = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenu() from the server but SidebarMenu is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarMenu\",\n);\nexport const SidebarMenuAction = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuAction() from the server but SidebarMenuAction is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarMenuAction\",\n);\nexport const SidebarMenuBadge = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuBadge() from the server but SidebarMenuBadge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarMenuBadge\",\n);\nexport const SidebarMenuButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuButton() from the server but SidebarMenuButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarMenuButton\",\n);\nexport const SidebarMenuItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuItem() from the server but SidebarMenuItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarMenuItem\",\n);\nexport const SidebarMenuSkeleton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSkeleton() from the server but SidebarMenuSkeleton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarMenuSkeleton\",\n);\nexport const SidebarMenuSub = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSub() from the server but SidebarMenuSub is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarMenuSub\",\n);\nexport const SidebarMenuSubButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSubButton() from the server but SidebarMenuSubButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarMenuSubButton\",\n);\nexport const SidebarMenuSubItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarMenuSubItem() from the server but SidebarMenuSubItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarMenuSubItem\",\n);\nexport const SidebarProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarProvider() from the server but SidebarProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarProvider\",\n);\nexport const SidebarRail = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarRail() from the server but SidebarRail is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarRail\",\n);\nexport const SidebarSeparator = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarSeparator() from the server but SidebarSeparator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarSeparator\",\n);\nexport const SidebarTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call SidebarTrigger() from the server but SidebarTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"SidebarTrigger\",\n);\nexport const useSidebar = registerClientReference(\n    function() { throw new Error(\"Attempted to call useSidebar() from the server but useSidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sidebar.tsx\",\n    \"useSidebar\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACvE;;AACO,MAAM,UAAU,IAAA,wQAAuB,EAC1C;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,2CACA;AAEG,MAAM,iBAAiB,IAAA,wQAAuB,EACjD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,2CACA;AAEG,MAAM,gBAAgB,IAAA,wQAAuB,EAChD;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,2CACA;AAEG,MAAM,eAAe,IAAA,wQAAuB,EAC/C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,2CACA;AAEG,MAAM,qBAAqB,IAAA,wQAAuB,EACrD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,2CACA;AAEG,MAAM,sBAAsB,IAAA,wQAAuB,EACtD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,2CACA;AAEG,MAAM,oBAAoB,IAAA,wQAAuB,EACpD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,2CACA;AAEG,MAAM,gBAAgB,IAAA,wQAAuB,EAChD;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,2CACA;AAEG,MAAM,eAAe,IAAA,wQAAuB,EAC/C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,2CACA;AAEG,MAAM,eAAe,IAAA,wQAAuB,EAC/C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,2CACA;AAEG,MAAM,cAAc,IAAA,wQAAuB,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,2CACA;AAEG,MAAM,oBAAoB,IAAA,wQAAuB,EACpD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,2CACA;AAEG,MAAM,mBAAmB,IAAA,wQAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,2CACA;AAEG,MAAM,oBAAoB,IAAA,wQAAuB,EACpD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,2CACA;AAEG,MAAM,kBAAkB,IAAA,wQAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,2CACA;AAEG,MAAM,sBAAsB,IAAA,wQAAuB,EACtD;IAAa,MAAM,IAAI,MAAM;AAAsP,GACnR,2CACA;AAEG,MAAM,iBAAiB,IAAA,wQAAuB,EACjD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,2CACA;AAEG,MAAM,uBAAuB,IAAA,wQAAuB,EACvD;IAAa,MAAM,IAAI,MAAM;AAAwP,GACrR,2CACA;AAEG,MAAM,qBAAqB,IAAA,wQAAuB,EACrD;IAAa,MAAM,IAAI,MAAM;AAAoP,GACjR,2CACA;AAEG,MAAM,kBAAkB,IAAA,wQAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,2CACA;AAEG,MAAM,cAAc,IAAA,wQAAuB,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,2CACA;AAEG,MAAM,mBAAmB,IAAA,wQAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,2CACA;AAEG,MAAM,iBAAiB,IAAA,wQAAuB,EACjD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,2CACA;AAEG,MAAM,aAAa,IAAA,wQAAuB,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,2CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 556, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/app-sidebar/document-store-sidebar-link/index.tsx"], "sourcesContent": ["import {\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n} from '@atlas/components/ui/sidebar'\r\nimport { Book, Folder } from 'lucide-react'\r\nimport { getTranslations } from 'next-intl/server'\r\n\r\nexport const DocumentStoreLinks = async () => {\r\n  const t = await getTranslations('components.document-storage-links')\r\n  const baseDocumentalStorageUri = process.env.NEXT_PUBLIC_NEXTCLOUD_BASE_URL\r\n  const homeUrl = `${baseDocumentalStorageUri}/apps/files/`\r\n  const sharedFolderUrl = `${baseDocumentalStorageUri}/apps/files/?dir=/Documentazione%20tecnica`\r\n\r\n  return (\r\n    <>\r\n      <SidebarMenuItem>\r\n        <a href={sharedFolderUrl} target=\"_blank\" rel=\"noopener noreferrer\">\r\n          <SidebarMenuButton>\r\n            <Book />\r\n            <span>{t('buttons.tecnical-documentation.label')}</span>\r\n          </SidebarMenuButton>\r\n        </a>\r\n      </SidebarMenuItem>\r\n      <SidebarMenuItem>\r\n        <a href={homeUrl} target=\"_blank\" rel=\"noopener noreferrer\">\r\n          <SidebarMenuButton>\r\n            <Folder />\r\n            <span>{t('buttons.home.label')}</span>\r\n          </SidebarMenuButton>\r\n        </a>\r\n      </SidebarMenuItem>\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAIA;AAAA;AACA;;;;;AAEO,MAAM,qBAAqB;IAChC,MAAM,IAAI,MAAM,IAAA,2QAAe,EAAC;IAChC,MAAM,2BAA2B,QAAQ,GAAG,CAAC,8BAA8B;IAC3E,MAAM,UAAU,GAAG,yBAAyB,YAAY,CAAC;IACzD,MAAM,kBAAkB,GAAG,yBAAyB,0CAA0C,CAAC;IAE/F,qBACE;;0BACE,8OAAC,sJAAe;0BACd,cAAA,8OAAC;oBAAE,MAAM;oBAAiB,QAAO;oBAAS,KAAI;8BAC5C,cAAA,8OAAC,wJAAiB;;0CAChB,8OAAC,0MAAI;;;;;0CACL,8OAAC;0CAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;0BAIf,8OAAC,sJAAe;0BACd,cAAA,8OAAC;oBAAE,MAAM;oBAAS,QAAO;oBAAS,KAAI;8BACpC,cAAA,8OAAC,wJAAiB;;0CAChB,8OAAC,gNAAM;;;;;0CACP,8OAAC;0CAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;AAMrB", "debugId": null}}, {"offset": {"line": 661, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/app-sidebar/nav-projects/index.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const NavProjects = registerClientReference(\n    function() { throw new Error(\"Attempted to call NavProjects() from the server but NavProjects is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/common/atlas/app-sidebar/nav-projects/index.tsx <module evaluation>\",\n    \"NavProjects\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,cAAc,IAAA,wQAAuB,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,gGACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 675, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/app-sidebar/nav-projects/index.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const NavProjects = registerClientReference(\n    function() { throw new Error(\"Attempted to call NavProjects() from the server but NavProjects is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/common/atlas/app-sidebar/nav-projects/index.tsx\",\n    \"NavProjects\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,cAAc,IAAA,wQAAuB,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,4EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 689, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 697, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/app-sidebar/nav-user.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const NavUser = registerClientReference(\n    function() { throw new Error(\"Attempted to call NavUser() from the server but NavUser is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/common/atlas/app-sidebar/nav-user.tsx <module evaluation>\",\n    \"NavUser\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,UAAU,IAAA,wQAAuB,EAC1C;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,sFACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 711, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/app-sidebar/nav-user.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const NavUser = registerClientReference(\n    function() { throw new Error(\"Attempted to call NavUser() from the server but NavUser is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/common/atlas/app-sidebar/nav-user.tsx\",\n    \"NavUser\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,UAAU,IAAA,wQAAuB,EAC1C;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,kEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 725, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 733, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/app-sidebar/app-sidebar.tsx"], "sourcesContent": ["import { DocumentStoreLinks } from '@atlas/components/common/atlas/app-sidebar/document-store-sidebar-link'\r\nimport { NavProjects } from '@atlas/components/common/atlas/app-sidebar/nav-projects'\r\nimport { NavUser } from '@atlas/components/common/atlas/app-sidebar/nav-user'\r\nimport {\r\n  Sidebar,\r\n  SidebarContent,\r\n  SidebarFooter,\r\n  SidebarGroup,\r\n  SidebarGroupContent,\r\n  SidebarHeader,\r\n  SidebarMenu,\r\n  SidebarMenuButton,\r\n  SidebarMenuItem,\r\n  SidebarRail,\r\n  SidebarSeparator,\r\n} from '@atlas/components/ui/sidebar'\r\nimport { Link } from '@atlas/i18n/routing'\r\nimport { HomeIcon, PlusCircleIcon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { getLocale, getTranslations } from 'next-intl/server'\r\n\r\ntype Props = React.ComponentProps<typeof Sidebar> & {\r\n  session: Session\r\n}\r\n\r\nexport const AppSidebar = async ({ session, ...props }: Props) => {\r\n  const t = await getTranslations()\r\n  const tAppSidebar = await getTranslations('components.app-sidebar')\r\n  const locale = await getLocale()\r\n\r\n  return (\r\n    <Sidebar collapsible=\"icon\" {...props}>\r\n      <SidebarHeader>\r\n        <SidebarMenu>\r\n          <SidebarMenuItem>\r\n            <SidebarMenuButton\r\n              size=\"lg\"\r\n              className=\"data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground\"\r\n            >\r\n              <div className=\"flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground\">\r\n                <Image\r\n                  src=\"/assets/logo-kimia-icon.png\"\r\n                  alt=\"Kimia Logo\"\r\n                  width={32}\r\n                  height={32}\r\n                  style={{ objectFit: 'contain' }}\r\n                />\r\n              </div>\r\n              <div className=\"grid flex-1 text-left text-sm leading-tight\">\r\n                <span className=\"truncate font-semibold\">\r\n                  {t('application.name')}\r\n                </span>\r\n              </div>\r\n            </SidebarMenuButton>\r\n          </SidebarMenuItem>\r\n        </SidebarMenu>\r\n      </SidebarHeader>\r\n      <SidebarSeparator />\r\n      <SidebarContent>\r\n        <SidebarGroup>\r\n          <SidebarGroupContent className=\"flex flex-col gap-2\">\r\n            <SidebarMenu>\r\n              <SidebarMenuItem>\r\n                <Link href=\"/dashboard/projects/create\" locale={locale}>\r\n                  <SidebarMenuButton\r\n                    tooltip={tAppSidebar('buttons.create-project.label')}\r\n                    className=\"min-w-8 bg-primary text-primary-foreground duration-200 ease-linear hover:bg-primary/90 hover:text-primary-foreground active:bg-primary/90 active:text-primary-foreground\"\r\n                  >\r\n                    <PlusCircleIcon />\r\n                    <span>{tAppSidebar('buttons.create-project.label')}</span>\r\n                  </SidebarMenuButton>\r\n                </Link>\r\n              </SidebarMenuItem>\r\n              <SidebarMenuItem>\r\n                <Link href=\"/dashboard\" locale={locale}>\r\n                  <SidebarMenuButton\r\n                    tooltip={tAppSidebar('buttons.dashboard.label')}\r\n                  >\r\n                    <HomeIcon />\r\n                    <span>{tAppSidebar('buttons.dashboard.label')}</span>\r\n                  </SidebarMenuButton>\r\n                </Link>\r\n              </SidebarMenuItem>\r\n              {process.env.NEXT_PUBLIC_NEXTCLOUD_ENABLE === 'true' &&\r\n                session && <DocumentStoreLinks />}\r\n            </SidebarMenu>\r\n          </SidebarGroupContent>\r\n        </SidebarGroup>\r\n        {session && <NavProjects session={session} />}\r\n      </SidebarContent>\r\n      <SidebarFooter>\r\n        <NavUser user={session.user} />\r\n      </SidebarFooter>\r\n      <SidebarRail />\r\n    </Sidebar>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AAaA;AACA;AAAA;AACA;AAEA;AAAA;;;;;;;;;;AAMO,MAAM,aAAa,OAAO,EAAE,OAAO,EAAE,GAAG,OAAc;IAC3D,MAAM,IAAI,MAAM,IAAA,2QAAe;IAC/B,MAAM,cAAc,MAAM,IAAA,2QAAe,EAAC;IAC1C,MAAM,SAAS,MAAM,IAAA,yPAAS;IAE9B,qBACE,8OAAC,8IAAO;QAAC,aAAY;QAAQ,GAAG,KAAK;;0BACnC,8OAAC,oJAAa;0BACZ,cAAA,8OAAC,kJAAW;8BACV,cAAA,8OAAC,sJAAe;kCACd,cAAA,8OAAC,wJAAiB;4BAChB,MAAK;4BACL,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,wIAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,OAAO;4CAAE,WAAW;wCAAU;;;;;;;;;;;8CAGlC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDACb,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOf,8OAAC,uJAAgB;;;;;0BACjB,8OAAC,qJAAc;;kCACb,8OAAC,mJAAY;kCACX,cAAA,8OAAC,0JAAmB;4BAAC,WAAU;sCAC7B,cAAA,8OAAC,kJAAW;;kDACV,8OAAC,sJAAe;kDACd,cAAA,8OAAC,8HAAI;4CAAC,MAAK;4CAA6B,QAAQ;sDAC9C,cAAA,8OAAC,wJAAiB;gDAChB,SAAS,YAAY;gDACrB,WAAU;;kEAEV,8OAAC,wOAAc;;;;;kEACf,8OAAC;kEAAM,YAAY;;;;;;;;;;;;;;;;;;;;;;kDAIzB,8OAAC,sJAAe;kDACd,cAAA,8OAAC,8HAAI;4CAAC,MAAK;4CAAa,QAAQ;sDAC9B,cAAA,8OAAC,wJAAiB;gDAChB,SAAS,YAAY;;kEAErB,8OAAC,mNAAQ;;;;;kEACT,8OAAC;kEAAM,YAAY;;;;;;;;;;;;;;;;;;;;;;oCAIxB,QAAQ,GAAG,CAAC,4BAA4B,KAAK,UAC5C,yBAAW,8OAAC,8NAAkB;;;;;;;;;;;;;;;;;;;;;oBAIrC,yBAAW,8OAAC,kMAAW;wBAAC,SAAS;;;;;;;;;;;;0BAEpC,8OAAC,oJAAa;0BACZ,cAAA,8OAAC,iLAAO;oBAAC,MAAM,QAAQ,IAAI;;;;;;;;;;;0BAE7B,8OAAC,kJAAW;;;;;;;;;;;AAGlB", "debugId": null}}, {"offset": {"line": 974, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/pages/dashboard/dashboard-layout.tsx"], "sourcesContent": ["import { signIn } from '@atlas/auth'\r\nimport { AppSidebar } from '@atlas/components/common/atlas/app-sidebar/app-sidebar'\r\nimport { SidebarInset, SidebarProvider } from '@atlas/components/ui/sidebar'\r\nimport type { Session } from 'next-auth'\r\n\r\ntype Props = React.PropsWithChildren<{\r\n  session: Session\r\n}>\r\n\r\nexport const DashboardLayoutComponent = async ({\r\n  children,\r\n  session,\r\n}: Props) => {\r\n  if (!session?.user) {\r\n    return await signIn()\r\n  }\r\n\r\n  return (\r\n    <SidebarProvider>\r\n      <AppSidebar session={session} />\r\n      <SidebarInset>{children}</SidebarInset>\r\n    </SidebarProvider>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAOO,MAAM,2BAA2B,OAAO,EAC7C,QAAQ,EACR,OAAO,EACD;IACN,IAAI,CAAC,SAAS,MAAM;QAClB,OAAO,MAAM,IAAA,qHAAM;IACrB;IAEA,qBACE,8OAAC,sJAAe;;0BACd,8OAAC,uLAAU;gBAAC,SAAS;;;;;;0BACrB,8OAAC,mJAAY;0BAAE;;;;;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 1017, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/app/%5Blocale%5D/dashboard/layout.tsx"], "sourcesContent": ["import { auth, signIn } from '@atlas/auth'\r\nimport { DashboardLayoutComponent } from '@atlas/components/pages/dashboard/dashboard-layout'\r\n\r\ntype Props = {\r\n  children: React.ReactNode\r\n}\r\n\r\nconst DashboardLayout = async ({ children }: Props) => {\r\n  const session = await auth()\r\n\r\n  if (!session) {\r\n    return await signIn()\r\n  }\r\n\r\n  return (\r\n    <DashboardLayoutComponent session={session}>\r\n      {children}\r\n    </DashboardLayoutComponent>\r\n  )\r\n}\r\n\r\nexport default DashboardLayout\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;AAMA,MAAM,kBAAkB,OAAO,EAAE,QAAQ,EAAS;IAChD,MAAM,UAAU,MAAM,IAAA,mHAAI;IAE1B,IAAI,CAAC,SAAS;QACZ,OAAO,MAAM,IAAA,qHAAM;IACrB;IAEA,qBACE,8OAAC,2LAAwB;QAAC,SAAS;kBAChC;;;;;;AAGP;uCAEe", "debugId": null}}]}