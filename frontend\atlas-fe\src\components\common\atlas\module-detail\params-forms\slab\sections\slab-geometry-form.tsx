import { NumberFormInput } from '@atlas/components/common/form/number-form-input'
import { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'
import { Button } from '@atlas/components/ui/button'
import { Form } from '@atlas/components/ui/form'
import {
  calculateSlabEffectiveDepth,
  calculateSlabTotalHeight,
} from '@atlas/functions/forms/slab-form-calculations'
import type { Module } from '@atlas/lib/api/modules/schemas/modules'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'
import {
  type SlabGeometryFormInputs as FormSchema,
  slabGeometrySchema,
} from '@atlas/types/schemas/slab-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useQueryClient } from '@tanstack/react-query'
import { Loader2Icon } from 'lucide-react'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useCallback, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'

type Props = {
  session: Session
  projectId: Project['id']
  moduleId: Module['id']
  defaultValues?: Partial<FormSchema>
  onSave: () => void
}

const JOIST_FORMWORK_OPTIONS = ['T_SHAPED', 'RECTANGULAR_WITHOUT_SLAB'] as const
const STRUCTURAL_SCHEME_OPTIONS = ['SIMPLY_SUPPORTED', 'CANTILEVER'] as const

export const SlabGeometryForm = ({
  session,
  projectId,
  moduleId,
  defaultValues,
  onSave,
}: Props) => {
  const t = useTranslations('forms.project-params.slab.geometry')
  const tAction = useTranslations('actions.save-project-params.messages')
  const tCommon = useTranslations('actions.common')
  const queryClient = useQueryClient()

  const form = useForm<FormSchema>({
    resolver: zodResolver(slabGeometrySchema),
    defaultValues: {
      joistFormwork: defaultValues?.joistFormwork ?? 'T_SHAPED',
      joistBase: defaultValues?.joistBase,
      joistWebHeight: defaultValues?.joistWebHeight,
      existingSlabHeight: defaultValues?.existingSlabHeight ?? 0,
      joistSpacing: defaultValues?.joistSpacing,
      bottomRebarCover: defaultValues?.bottomRebarCover,
      topRebarCover: defaultValues?.topRebarCover,
      structuralScheme: defaultValues?.structuralScheme ?? 'SIMPLY_SUPPORTED',
      totalSlabThickness: defaultValues?.totalSlabThickness,
      effectiveDepth: defaultValues?.effectiveDepth,
    },
  })

  const { mutate, isPending } = useSaveModuleParamsMutation(
    session.accessToken,
    {
      onSuccess: async () => {
        toast.success(tAction('edit.success'))
        await queryClient.invalidateQueries({
          queryKey: ['modules', { projectId, moduleId }],
        })
        onSave()
      },
      onError: error => {
        toast.error(tAction('edit.failure', { error: error.message }))
      },
    },
  )

  const handleFormSubmit = useCallback(
    (geometry: FormSchema) => {
      mutate({ projectId, moduleId, body: { geometry } })
    },
    [mutate, projectId, moduleId],
  )

  const [joistFormwork, joistWebHeight, existingSlabHeight, bottomRebarCover] =
    form.watch([
      'joistFormwork',
      'joistWebHeight',
      'existingSlabHeight',
      'bottomRebarCover',
    ])

  // Auto-calculate total slab thickness (D13)
  useEffect(() => {
    if (joistWebHeight && existingSlabHeight !== undefined) {
      const totalHeight = calculateSlabTotalHeight(
        joistWebHeight,
        existingSlabHeight,
      )
      form.setValue('totalSlabThickness', totalHeight, { shouldValidate: true })
    }
  }, [joistWebHeight, existingSlabHeight, form])

  // Auto-calculate effective depth (D16)
  useEffect(() => {
    if (
      joistWebHeight &&
      existingSlabHeight !== undefined &&
      bottomRebarCover
    ) {
      const totalHeight = calculateSlabTotalHeight(
        joistWebHeight,
        existingSlabHeight,
      )
      const effectiveDepth = calculateSlabEffectiveDepth(
        totalHeight,
        bottomRebarCover,
      )
      form.setValue('effectiveDepth', effectiveDepth, { shouldValidate: true })
    }
  }, [joistWebHeight, existingSlabHeight, bottomRebarCover, form])

  // Reset existing slab height to 0 when formwork is rectangular
  useEffect(() => {
    if (joistFormwork === 'RECTANGULAR_WITHOUT_SLAB') {
      form.setValue('existingSlabHeight', 0, { shouldValidate: true })
    }
  }, [joistFormwork, form])

  return (
    <Form {...form}>
      <form
        className="space-y-4 rounded-md border p-4"
        onSubmit={form.handleSubmit(handleFormSubmit)}
      >
        {/* <Image
          src="/assets/slab/slab_geometry.jpg"
          alt="slab geometry"
          height={250}
          width={500}
          className="mx-auto rounded-md object-contain"
          priority
        /> */}

        <SelectFormFixedInput
          control={form.control}
          name="joistFormwork"
          options={JOIST_FORMWORK_OPTIONS}
          optionLabelFn={p => t(`joistFormwork.${p}`)}
          t={t}
        />

        <NumberFormInput control={form.control} name="joistBase" t={t} />

        <NumberFormInput control={form.control} name="joistWebHeight" t={t} />

        <NumberFormInput
          control={form.control}
          name="existingSlabHeight"
          t={t}
          disabled={joistFormwork === 'RECTANGULAR_WITHOUT_SLAB'}
        />

        <NumberFormInput control={form.control} name="joistSpacing" t={t} />

        <NumberFormInput control={form.control} name="bottomRebarCover" t={t} />

        <NumberFormInput control={form.control} name="topRebarCover" t={t} />

        <SelectFormFixedInput
          control={form.control}
          name="structuralScheme"
          options={STRUCTURAL_SCHEME_OPTIONS}
          optionLabelFn={p => t(`structuralScheme.${p}`)}
          t={t}
        />

        <NumberFormInput
          control={form.control}
          name="totalSlabThickness"
          t={t}
          disabled={true}
        />

        <NumberFormInput
          control={form.control}
          name="effectiveDepth"
          t={t}
          disabled={true}
        />

        <Button type="submit" className="w-full sm:w-auto" disabled={isPending}>
          {isPending && <Loader2Icon className="animate-spin" />}
          {tCommon('save')}
        </Button>
      </form>
    </Form>
  )
}
