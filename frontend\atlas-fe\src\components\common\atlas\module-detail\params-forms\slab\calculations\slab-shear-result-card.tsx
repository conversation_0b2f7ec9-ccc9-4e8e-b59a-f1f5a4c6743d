import { Badge } from '@atlas/components/ui/badge'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@atlas/components/ui/card'
import { Separator } from '@atlas/components/ui/separator'
import type { SlabShearCalculationResult } from '@atlas/lib/api/modules/schemas/slab-params'
import { cn } from '@atlas/lib/utils'
import { useLocale, useTranslations } from 'next-intl'

interface SlabShearResultCardProps {
  result: SlabShearCalculationResult
}

export function SlabShearResultCard({ result }: SlabShearResultCardProps) {
  const t = useTranslations('forms.calculations.slab.shear-result')
  const locale = useLocale()

  const isVerified = result?.isShearVerificationSatisfied ?? false

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>{t('title')}</span>
          <Badge
            className={cn(
              'text-base px-3 py-1',
              isVerified ? 'bg-green-600' : 'bg-red-600',
            )}
          >
            {isVerified ? t('verified') : t('not-verified')}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Input Section */}
        <div>
          <h4 className="font-semibold mb-3">
            {t('section-fill-type')}: {result.sectionFillType}
          </h4>
          {result.inputShearForce !== undefined &&
            result.inputShearForce !== null && (
              <div className="mt-2">
                <span className="text-muted-foreground">
                  {t('input-shear-force')}:
                </span>
                <span className="ml-2 font-medium text-lg">
                  {result.inputShearForce.toLocaleString(locale, {
                    maximumFractionDigits: 2,
                  })}{' '}
                  kN
                </span>
              </div>
            )}
        </div>

        <Separator />

        {/* Unreinforced Section */}
        <div>
          <h4 className="font-semibold mb-3">{t('unreinforced-section')}</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            {result.unreinforcedSectionEffectiveDepth !== undefined &&
              result.unreinforcedSectionEffectiveDepth !== null && (
                <div>
                  <span className="text-muted-foreground">
                    {t('effective-depth')}:
                  </span>
                  <span className="ml-2 font-medium">
                    {result.unreinforcedSectionEffectiveDepth.toLocaleString(
                      locale,
                      { maximumFractionDigits: 2 },
                    )}{' '}
                    mm
                  </span>
                </div>
              )}
            {result.unreinforcedTensionAreaMinimumWidth !== undefined &&
              result.unreinforcedTensionAreaMinimumWidth !== null && (
                <div>
                  <span className="text-muted-foreground">
                    {t('minimum-width')}:
                  </span>
                  <span className="ml-2 font-medium">
                    {result.unreinforcedTensionAreaMinimumWidth.toLocaleString(
                      locale,
                      { maximumFractionDigits: 2 },
                    )}{' '}
                    mm
                  </span>
                </div>
              )}
            {result.unreinforcedSizeEffectFactor !== undefined &&
              result.unreinforcedSizeEffectFactor !== null && (
                <div>
                  <span className="text-muted-foreground">
                    {t('size-effect-factor')}:
                  </span>
                  <span className="ml-2 font-medium">
                    {result.unreinforcedSizeEffectFactor.toFixed(3)}
                  </span>
                </div>
              )}
            {result.unreinforcedCoefficient !== undefined &&
              result.unreinforcedCoefficient !== null && (
                <div>
                  <span className="text-muted-foreground">
                    {t('coefficient')}:
                  </span>
                  <span className="ml-2 font-medium">
                    {result.unreinforcedCoefficient.toFixed(4)} MPa
                  </span>
                </div>
              )}
            {result.unreinforcedTensileLongitudinalReinforcementArea !==
              undefined &&
              result.unreinforcedTensileLongitudinalReinforcementArea !==
                null && (
                <div>
                  <span className="text-muted-foreground">
                    {t('reinforcement-area')}:
                  </span>
                  <span className="ml-2 font-medium">
                    {result.unreinforcedTensileLongitudinalReinforcementArea.toLocaleString(
                      locale,
                      { maximumFractionDigits: 2 },
                    )}{' '}
                    mm²
                  </span>
                </div>
              )}
            {result.unreinforcedTensileReinforcementRatio !== undefined &&
              result.unreinforcedTensileReinforcementRatio !== null && (
                <div>
                  <span className="text-muted-foreground">
                    {t('reinforcement-ratio')}:
                  </span>
                  <span className="ml-2 font-medium">
                    {(
                      result.unreinforcedTensileReinforcementRatio * 100
                    ).toFixed(4)}
                    %
                  </span>
                </div>
              )}
            {result.unreinforcedShearCapacity !== undefined &&
              result.unreinforcedShearCapacity !== null && (
                <div className="col-span-2">
                  <span className="text-muted-foreground font-semibold">
                    {t('shear-capacity')}:
                  </span>
                  <span className="ml-2 font-medium text-lg">
                    {result.unreinforcedShearCapacity.toLocaleString(locale, {
                      maximumFractionDigits: 2,
                    })}{' '}
                    kN
                  </span>
                </div>
              )}
          </div>
        </div>

        <Separator />

        {/* Reinforced Section */}
        <div>
          <h4 className="font-semibold mb-3">{t('reinforced-section')}</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            {result.reinforcedSectionEffectiveDepth !== undefined &&
              result.reinforcedSectionEffectiveDepth !== null && (
                <div>
                  <span className="text-muted-foreground">
                    {t('effective-depth')}:
                  </span>
                  <span className="ml-2 font-medium">
                    {result.reinforcedSectionEffectiveDepth.toLocaleString(
                      locale,
                      { maximumFractionDigits: 2 },
                    )}{' '}
                    mm
                  </span>
                </div>
              )}
            {result.reinforcedTensionAreaMinimumWidth !== undefined &&
              result.reinforcedTensionAreaMinimumWidth !== null && (
                <div>
                  <span className="text-muted-foreground">
                    {t('minimum-width')}:
                  </span>
                  <span className="ml-2 font-medium">
                    {result.reinforcedTensionAreaMinimumWidth.toLocaleString(
                      locale,
                      { maximumFractionDigits: 2 },
                    )}{' '}
                    mm
                  </span>
                </div>
              )}
            {result.reinforcedSizeEffectFactor !== undefined &&
              result.reinforcedSizeEffectFactor !== null && (
                <div>
                  <span className="text-muted-foreground">
                    {t('size-effect-factor')}:
                  </span>
                  <span className="ml-2 font-medium">
                    {result.reinforcedSizeEffectFactor.toFixed(3)}
                  </span>
                </div>
              )}
            {result.reinforcedMeanCharacteristicCompressiveStrength !==
              undefined &&
              result.reinforcedMeanCharacteristicCompressiveStrength !==
                null && (
                <div>
                  <span className="text-muted-foreground">
                    {t('mean-compressive-strength')}:
                  </span>
                  <span className="ml-2 font-medium">
                    {result.reinforcedMeanCharacteristicCompressiveStrength.toLocaleString(
                      locale,
                      { maximumFractionDigits: 2 },
                    )}{' '}
                    MPa
                  </span>
                </div>
              )}
            {result.reinforcedCoefficient !== undefined &&
              result.reinforcedCoefficient !== null && (
                <div>
                  <span className="text-muted-foreground">
                    {t('coefficient')}:
                  </span>
                  <span className="ml-2 font-medium">
                    {result.reinforcedCoefficient.toFixed(4)} MPa
                  </span>
                </div>
              )}
            {result.reinforcedTensileLongitudinalReinforcementArea !==
              undefined &&
              result.reinforcedTensileLongitudinalReinforcementArea !==
                null && (
                <div>
                  <span className="text-muted-foreground">
                    {t('reinforcement-area')}:
                  </span>
                  <span className="ml-2 font-medium">
                    {result.reinforcedTensileLongitudinalReinforcementArea.toLocaleString(
                      locale,
                      { maximumFractionDigits: 2 },
                    )}{' '}
                    mm²
                  </span>
                </div>
              )}
            {result.reinforcedTensileReinforcementRatio !== undefined &&
              result.reinforcedTensileReinforcementRatio !== null && (
                <div>
                  <span className="text-muted-foreground">
                    {t('reinforcement-ratio')}:
                  </span>
                  <span className="ml-2 font-medium">
                    {(result.reinforcedTensileReinforcementRatio * 100).toFixed(
                      4,
                    )}
                    %
                  </span>
                </div>
              )}
            {result.reinforcedSectionShearResistance !== undefined &&
              result.reinforcedSectionShearResistance !== null && (
                <div className="col-span-2">
                  <span className="text-muted-foreground font-semibold">
                    {t('shear-resistance')}:
                  </span>
                  <span className="ml-2 font-medium text-lg">
                    {result.reinforcedSectionShearResistance.toLocaleString(
                      locale,
                      { maximumFractionDigits: 2 },
                    )}{' '}
                    kN
                  </span>
                </div>
              )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
