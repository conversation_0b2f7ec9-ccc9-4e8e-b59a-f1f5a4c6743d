// This file is generated automatically by Next.js
// Do not edit this file manually

type AppRoutes = "/[locale]" | "/[locale]/[...rest]" | "/[locale]/dashboard" | "/[locale]/dashboard/projects" | "/[locale]/dashboard/projects/[projectId]" | "/[locale]/dashboard/projects/[projectId]/edit" | "/[locale]/dashboard/projects/[projectId]/modules/[moduleId]" | "/[locale]/dashboard/projects/create" | "/[locale]/sign-up"
type AppRouteHandlerRoutes = "/api/auth/[...nextauth]" | "/api/register"
type PageRoutes = never
type LayoutRoutes = "/[locale]" | "/[locale]/dashboard"
type RedirectRoutes = never
type RewriteRoutes = never
type Routes = AppRoutes | PageRoutes | LayoutRoutes | RedirectRoutes | RewriteRoutes | AppRouteHandlerRoutes


interface ParamMap {
  "/[locale]": { "locale": string; }
  "/[locale]/[...rest]": { "locale": string; "rest": string[]; }
  "/[locale]/dashboard": { "locale": string; }
  "/[locale]/dashboard/projects": { "locale": string; }
  "/[locale]/dashboard/projects/[projectId]": { "locale": string; "projectId": string; }
  "/[locale]/dashboard/projects/[projectId]/edit": { "locale": string; "projectId": string; }
  "/[locale]/dashboard/projects/[projectId]/modules/[moduleId]": { "locale": string; "projectId": string; "moduleId": string; }
  "/[locale]/dashboard/projects/create": { "locale": string; }
  "/[locale]/sign-up": { "locale": string; }
  "/api/auth/[...nextauth]": { "nextauth": string[]; }
  "/api/register": {}
}


export type ParamsOf<Route extends Routes> = ParamMap[Route]

interface LayoutSlotMap {
  "/[locale]": never
  "/[locale]/dashboard": never
}


export type { AppRoutes, PageRoutes, LayoutRoutes, RedirectRoutes, RewriteRoutes, ParamMap, AppRouteHandlerRoutes }

declare global {
  /**
   * Props for Next.js App Router page components
   * @example
   * ```tsx
   * export default function Page(props: PageProps<'/blog/[slug]'>) {
   *   const { slug } = await props.params
   *   return <div>Blog post: {slug}</div>
   * }
   * ```
   */
  interface PageProps<AppRoute extends AppRoutes> {
    params: Promise<ParamMap[AppRoute]>
    searchParams: Promise<Record<string, string | string[] | undefined>>
  }

  /**
   * Props for Next.js App Router layout components
   * @example
   * ```tsx
   * export default function Layout(props: LayoutProps<'/dashboard'>) {
   *   return <div>{props.children}</div>
   * }
   * ```
   */
  type LayoutProps<LayoutRoute extends LayoutRoutes> = {
    params: Promise<ParamMap[LayoutRoute]>
    children: React.ReactNode
  } & {
    [K in LayoutSlotMap[LayoutRoute]]: React.ReactNode
  }

  /**
   * Context for Next.js App Router route handlers
   * @example
   * ```tsx
   * export async function GET(request: NextRequest, context: RouteContext<'/api/users/[id]'>) {
   *   const { id } = await context.params
   *   return Response.json({ id })
   * }
   * ```
   */
  interface RouteContext<AppRouteHandlerRoute extends AppRouteHandlerRoutes> {
    params: Promise<ParamMap[AppRouteHandlerRoute]>
  }
}
