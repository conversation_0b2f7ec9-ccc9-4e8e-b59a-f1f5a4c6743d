import { NumberFormInput } from '@atlas/components/common/form/number-form-input'
import { SelectFormInput } from '@atlas/components/common/form/select-form-input'
import { StringFormInput } from '@atlas/components/common/form/string-form-input'
import { moduleMaterialKnowledgeLevelValues } from '@atlas/constants/module'
import {
  calculateSlabDesignStrengthForBrittleMechanisms,
  calculateSlabDesignStrengthForDuctileMechanisms,
  calculateSlabDesignTensileStrengthForBrittleMechanisms,
} from '@atlas/functions/forms/slab-form-calculations'
import { useReinforcedConcreteMaterials } from '@atlas/lib/query/materials/use-reinforced-concrete-materials'
import type { SlabMaterialFormInputs } from '@atlas/types/schemas/slab-form'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useEffect, useMemo } from 'react'
import { useFormContext } from 'react-hook-form'

type Props = {
  session: Session
}

export function SlabConcreteClassFormSection({ session }: Props) {
  const t = useTranslations('forms.project-params.slab.materialProperties')

  const form = useFormContext<SlabMaterialFormInputs>()

  const {
    data: concreteMaterials,
    isError: concreteMaterialsError,
    isLoading: concreteMaterialsLoading,
  } = useReinforcedConcreteMaterials({ session, body: { size: 100 } })

  const customOption = { value: 'custom', label: t('concreteClass.custom') }

  const concreteMaterialsOptions = [
    ...(concreteMaterials?.content.map(m => ({
      value: m.id,
      label: m.name,
    })) ?? []),
    customOption,
  ]

  const concreteClassKnowledgeLevel = form.watch('concreteClassKnowledgeLevel')
  const concreteClassId = form.watch('concreteClass.id')
  const averageCompressiveStrength = form.watch(
    'concreteClass.averageCompressiveStrength',
  )
  const averageTensileStrength = form.watch(
    'concreteClass.averageTensileStrength',
  )

  const isCustomSelected = concreteClassId === 'custom'

  const selectedConcreteMaterial = useMemo(
    () => concreteMaterials?.content.find(m => m.id === concreteClassId),
    [concreteClassId, concreteMaterials],
  )

  useEffect(() => {
    if (!selectedConcreteMaterial || isCustomSelected) {
      return
    }
    const selected = selectedConcreteMaterial
    if (form.getValues('concreteClass.name') !== selected.name) {
      form.setValue('concreteClass.name', selected.name)
    }
    if (
      form.getValues('concreteClass.cubeCompressiveStrength') !==
      selected.cubeCompressiveStrength
    ) {
      form.setValue(
        'concreteClass.cubeCompressiveStrength',
        selected.cubeCompressiveStrength,
      )
    }
    if (
      form.getValues('concreteClass.cylinderCompressiveStrength') !==
      selected.cylinderCompressiveStrength
    ) {
      form.setValue(
        'concreteClass.cylinderCompressiveStrength',
        selected.cylinderCompressiveStrength,
      )
    }
    if (
      form.getValues('concreteClass.averageCompressiveStrength') !==
      selected.averageCompressiveStrength
    ) {
      form.setValue(
        'concreteClass.averageCompressiveStrength',
        selected.averageCompressiveStrength,
      )
    }
    if (
      form.getValues('concreteClass.averageTensileStrength') !==
      selected.averageTensileStrength
    ) {
      form.setValue(
        'concreteClass.averageTensileStrength',
        selected.averageTensileStrength,
      )
    }
    if (
      form.getValues('concreteClass.elasticModulus') !== selected.elasticModulus
    ) {
      form.setValue('concreteClass.elasticModulus', selected.elasticModulus)
    }
  }, [form, isCustomSelected, selectedConcreteMaterial])

  useEffect(() => {
    if (
      concreteClassKnowledgeLevel &&
      averageCompressiveStrength !== undefined &&
      averageCompressiveStrength !== null
    ) {
      const confidenceFactor =
        moduleMaterialKnowledgeLevelValues[concreteClassKnowledgeLevel]
      form.setValue(
        'concreteClass.designCompressiveStrengthForBrittleMechanisms',
        calculateSlabDesignStrengthForBrittleMechanisms(
          averageCompressiveStrength,
          confidenceFactor,
        ),
      )
      form.setValue(
        'concreteClass.designCompressiveStrengthForDuctileMechanisms',
        calculateSlabDesignStrengthForDuctileMechanisms(
          averageCompressiveStrength,
          confidenceFactor,
        ),
      )
    }
  }, [concreteClassKnowledgeLevel, averageCompressiveStrength, form])

  useEffect(() => {
    if (
      concreteClassKnowledgeLevel &&
      averageTensileStrength !== undefined &&
      averageTensileStrength !== null
    ) {
      const confidenceFactor =
        moduleMaterialKnowledgeLevelValues[concreteClassKnowledgeLevel]
      form.setValue(
        'concreteClass.designTensileStrengthForBrittleMechanisms',
        calculateSlabDesignTensileStrengthForBrittleMechanisms(
          averageTensileStrength,
          confidenceFactor,
        ),
      )
    }
  }, [concreteClassKnowledgeLevel, averageTensileStrength, form])

  return (
    <>
      <SelectFormInput
        control={form.control}
        name="concreteClass.id"
        options={concreteMaterialsOptions}
        t={t}
        loading={concreteMaterialsLoading}
        requestError={concreteMaterialsError}
        errorMessage={t('concreteClass.error')}
      />
      {isCustomSelected && (
        <StringFormInput
          control={form.control}
          name="concreteClass.name"
          t={t}
        />
      )}
      <NumberFormInput
        control={form.control}
        name="concreteClass.cubeCompressiveStrength"
        t={t}
        disabled={!isCustomSelected}
      />
      <NumberFormInput
        control={form.control}
        name="concreteClass.cylinderCompressiveStrength"
        t={t}
        disabled={!isCustomSelected}
      />
      <NumberFormInput
        control={form.control}
        name="concreteClass.averageCompressiveStrength"
        t={t}
        disabled={!isCustomSelected}
      />
      <NumberFormInput
        control={form.control}
        name="concreteClass.averageTensileStrength"
        t={t}
        disabled={!isCustomSelected}
      />
      <NumberFormInput
        control={form.control}
        name="concreteClass.elasticModulus"
        t={t}
        disabled={!isCustomSelected}
      />
      <NumberFormInput
        control={form.control}
        name="concreteClass.designCompressiveStrengthForDuctileMechanisms"
        t={t}
        disabled={true}
      />
      <NumberFormInput
        control={form.control}
        name="concreteClass.designTensileStrengthForBrittleMechanisms"
        t={t}
        disabled={true}
      />
      <NumberFormInput
        control={form.control}
        name="concreteClass.designCompressiveStrengthForBrittleMechanisms"
        t={t}
        disabled={true}
      />
    </>
  )
}
