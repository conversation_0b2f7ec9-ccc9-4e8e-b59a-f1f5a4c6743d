{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/error-alert.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { Alert, AlertDescription, AlertTitle } from '@atlas/components/ui/alert'\r\nimport type { ApiError, ValidationError } from '@atlas/types'\r\nimport { AlertCircle } from 'lucide-react'\r\nimport { signIn } from 'next-auth/react'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  error: ApiError | ValidationError | Error\r\n}\r\n\r\nexport const ErrorAlert = ({ error }: Props) => {\r\n  const t = useTranslations()\r\n\r\n  if (error instanceof Error) {\r\n    return (\r\n      <Alert variant=\"destructive\" className=\"gap-3\">\r\n        <AlertCircle className=\"h-4 w-4\" />\r\n        <AlertTitle>{t('errors.generic.title')}</AlertTitle>\r\n        <AlertDescription>{t('errors.generic.description')}</AlertDescription>\r\n        {process.env.NODE_ENV === 'development' && (\r\n          <AlertDescription>{error.message}</AlertDescription>\r\n        )}\r\n      </Alert>\r\n    )\r\n  }\r\n\r\n  if (error.type === 'ValidationError') {\r\n    return (\r\n      <Alert variant=\"destructive\" className=\"gap-3\">\r\n        <AlertCircle className=\"h-4 w-4\" />\r\n        <AlertTitle>{t(`errors.validation.${error.code}.title`)}</AlertTitle>\r\n        <AlertDescription>\r\n          {t(`errors.validation.${error.code}.description`)}\r\n        </AlertDescription>\r\n        {process.env.NODE_ENV === 'development' && (\r\n          <AlertDescription>{error.message}</AlertDescription>\r\n        )}\r\n      </Alert>\r\n    )\r\n  }\r\n\r\n  if (error.code === '401_UNAUTHORIZED') {\r\n    return signIn()\r\n  }\r\n\r\n  return (\r\n    <Alert variant=\"destructive\" className=\"gap-3\">\r\n      <AlertCircle className=\"h-4 w-4\" />\r\n      <AlertTitle>{t(`errors.api.${error.code}.title`)}</AlertTitle>\r\n      {t.has(`errors.api.${error.code}.description`) && (\r\n        <AlertDescription>\r\n          {t(`errors.api.${error.code}.description`)}\r\n        </AlertDescription>\r\n      )}\r\n    </Alert>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;AAqBS;;AAnBT;AAEA;AACA;AACA;;;AANA;;;;;AAYO,MAAM,aAAa;QAAC,EAAE,KAAK,EAAS;;IACzC,MAAM,IAAI,IAAA,4NAAe;IAEzB,IAAI,iBAAiB,OAAO;QAC1B,qBACE,6LAAC,6IAAK;YAAC,SAAQ;YAAc,WAAU;;8BACrC,6LAAC,sOAAW;oBAAC,WAAU;;;;;;8BACvB,6LAAC,kJAAU;8BAAE,EAAE;;;;;;8BACf,6LAAC,wJAAgB;8BAAE,EAAE;;;;;;gBACpB,oDAAyB,+BACxB,6LAAC,wJAAgB;8BAAE,MAAM,OAAO;;;;;;;;;;;;IAIxC;IAEA,IAAI,MAAM,IAAI,KAAK,mBAAmB;QACpC,qBACE,6LAAC,6IAAK;YAAC,SAAQ;YAAc,WAAU;;8BACrC,6LAAC,sOAAW;oBAAC,WAAU;;;;;;8BACvB,6LAAC,kJAAU;8BAAE,EAAE,AAAC,qBAA+B,OAAX,MAAM,IAAI,EAAC;;;;;;8BAC/C,6LAAC,wJAAgB;8BACd,EAAE,AAAC,qBAA+B,OAAX,MAAM,IAAI,EAAC;;;;;;gBAEpC,oDAAyB,+BACxB,6LAAC,wJAAgB;8BAAE,MAAM,OAAO;;;;;;;;;;;;IAIxC;IAEA,IAAI,MAAM,IAAI,KAAK,oBAAoB;QACrC,OAAO,IAAA,kJAAM;IACf;IAEA,qBACE,6LAAC,6IAAK;QAAC,SAAQ;QAAc,WAAU;;0BACrC,6LAAC,sOAAW;gBAAC,WAAU;;;;;;0BACvB,6LAAC,kJAAU;0BAAE,EAAE,AAAC,cAAwB,OAAX,MAAM,IAAI,EAAC;;;;;;YACvC,EAAE,GAAG,CAAC,AAAC,cAAwB,OAAX,MAAM,IAAI,EAAC,kCAC9B,6LAAC,wJAAgB;0BACd,EAAE,AAAC,cAAwB,OAAX,MAAM,IAAI,EAAC;;;;;;;;;;;;AAKtC;GA9Ca;;QACD,4NAAe;;;KADd", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/header-skeleton.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { Skeleton } from '@atlas/components/ui/skeleton' // For loading state\r\n\r\nexport const HeaderSkeleton = () => (\r\n  <div className=\"mb-6 border-b pb-4\">\r\n    <Skeleton className=\"mb-2 h-8 w-3/4 md:w-1/2\" />\r\n    <div className=\"flex flex-wrap items-center gap-x-4 gap-y-1\">\r\n      <Skeleton className=\"h-5 w-20\" />\r\n      <Skeleton className=\"h-5 w-24\" />\r\n      <Skeleton className=\"h-5 w-32\" />\r\n      <Skeleton className=\"h-5 w-40\" />\r\n      <Skeleton className=\"h-5 w-28\" />\r\n    </div>\r\n  </div>\r\n)\r\n"], "names": [], "mappings": ";;;;;AAEA,6OAAyD,oBAAoB;AAF7E;;;AAIO,MAAM,iBAAiB,kBAC5B,6LAAC;QAAI,WAAU;;0BACb,6LAAC,mJAAQ;gBAAC,WAAU;;;;;;0BACpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,mJAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC,mJAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC,mJAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC,mJAAQ;wBAAC,WAAU;;;;;;kCACpB,6LAAC,mJAAQ;wBAAC,WAAU;;;;;;;;;;;;;;;;;;KARb", "debugId": null}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/product/custom-product-section.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { PRODUCT_FIBER_TYPE } from '@atlas/constants/product'\r\nimport type { RectangularBeamFlexuralCalculationInput } from '@atlas/types/schemas/rectangular-beam-from'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useFormContext } from 'react-hook-form'\r\n\r\nexport const CustomProductSection = () => {\r\n  const form = useFormContext<RectangularBeamFlexuralCalculationInput>()\r\n  const t = useTranslations('forms.calculations.rectangular-beam')\r\n\r\n  return (\r\n    <>\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"input.product.thickness\"\r\n        t={t}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"input.product.tensileStrength\"\r\n        t={t}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"input.product.elasticModulus\"\r\n        t={t}\r\n      />\r\n      <SelectFormFixedInput\r\n        control={form.control}\r\n        name=\"input.product.fiberType\"\r\n        options={PRODUCT_FIBER_TYPE}\r\n        optionLabelFn={p => t(`fiberType.${p}`)}\r\n        t={t}\r\n      />\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;AACA;;;;;;;;AAEO,MAAM,uBAAuB;;IAClC,MAAM,OAAO,IAAA,mLAAc;IAC3B,MAAM,IAAI,IAAA,4NAAe,EAAC;IAE1B,qBACE;;0BACE,6LAAC,qLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;;;;;;0BAEL,6LAAC,qLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;;;;;;0BAEL,6LAAC,qLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;;;;;;0BAEL,6LAAC,mMAAoB;gBACnB,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,SAAS,qJAAkB;gBAC3B,eAAe,CAAA,IAAK,EAAE,AAAC,aAAc,OAAF;gBACnC,GAAG;;;;;;;;AAIX;GA9Ba;;QACE,mLAAc;QACjB,4NAAe;;;KAFd", "debugId": null}}, {"offset": {"line": 314, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/product/product-summary-card.tsx"], "sourcesContent": ["import { FACING_MATERIAL } from '@atlas/constants/module'\r\nimport type { Product } from '@atlas/lib/api/products/schemas/product'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ninterface Props {\r\n  product: Product\r\n  facingMaterial?: FACING_MATERIAL\r\n  isRectangularBeam?: boolean\r\n}\r\n\r\nexport const ProductSummaryCard = ({\r\n  product,\r\n  facingMaterial = undefined,\r\n  isRectangularBeam = true,\r\n}: Props) => {\r\n  const t = useTranslations('components.common.atlas.product')\r\n\r\n  const tForm = useTranslations(\r\n    `forms.calculations${isRectangularBeam ? '' : '.rectangular-beam'}`,\r\n  )\r\n\r\n  return (\r\n    <div className=\"rounded-md border p-2 my-2 bg-gray-50 text-gray-900 dark:bg-gray-800 dark:text-gray-100 dark:border-gray-700 text-sm\">\r\n      <div className=\"font-semibold mb-1\">{t('summary')}</div>\r\n      <ul className=\"list-disc pl-4\">\r\n        {product.fiberType && (\r\n          <li>\r\n            {t('fiberType')}: {tForm(`fiberType.${product.fiberType}`)}\r\n          </li>\r\n        )}\r\n        {product.orientation && (\r\n          <li>\r\n            {t('orientation')}: {tForm(`orientation.${product.orientation}`)}\r\n          </li>\r\n        )}\r\n        {product.thickness && (\r\n          <li>\r\n            {t('thickness')}: {product.thickness}\r\n          </li>\r\n        )}\r\n        {product.availableWidths && (\r\n          <li>\r\n            {t('availableWidths')}: {product.availableWidths.join(', ')}\r\n          </li>\r\n        )}\r\n        {product.elasticModulus && (\r\n          <li>\r\n            {t('elasticModulus')}: {product.elasticModulus}\r\n          </li>\r\n        )}\r\n        {product.cylindricCompressiveStrength && (\r\n          <li>\r\n            {t('cylindricCompressiveStrength')}:{' '}\r\n            {product.cylindricCompressiveStrength}\r\n          </li>\r\n        )}\r\n        {facingMaterial &&\r\n          product.facingPerformance &&\r\n          product.facingPerformance[facingMaterial] && (\r\n            <li>\r\n              {t('facingPerformance')} ({t(`facingMaterial.${facingMaterial}`)}\r\n              ): {product.facingPerformance[facingMaterial]}\r\n            </li>\r\n          )}\r\n        {product.productType && (\r\n          <li>\r\n            {t('productType')}: {product.productType}\r\n          </li>\r\n        )}\r\n        {product.ultimateStrain && (\r\n          <li>\r\n            {t('ultimateStrain')}: {product.ultimateStrain}\r\n          </li>\r\n        )}\r\n        {product.designDeformation && (\r\n          <li>\r\n            {t('designDeformation')}: {product.designDeformation}\r\n          </li>\r\n        )}\r\n        {product.unitStrengthOfTheMesh && (\r\n          <li>\r\n            {t('unitStrengthOfTheMesh')}: {product.unitStrengthOfTheMesh}\r\n          </li>\r\n        )}\r\n        {product.reinforcementTensileStrength && (\r\n          <li>\r\n            {t('reinforcementTensileStrength')}:{' '}\r\n            {product.reinforcementTensileStrength}\r\n          </li>\r\n        )}\r\n        {product.reinforcementUltimateStrain && (\r\n          <li>\r\n            {t('reinforcementUltimateStrain')}:{' '}\r\n            {product.reinforcementUltimateStrain}\r\n          </li>\r\n        )}\r\n        {product.matrixBreachStress && (\r\n          <li>\r\n            {t('matrixBreachStress')}: {product.matrixBreachStress}\r\n          </li>\r\n        )}\r\n        {/* Calculated unitStrengthOfTheMesh */}\r\n        {product.productType === 'MESH' && (\r\n          <li>\r\n            {t('unitStrengthOfTheMesh')}:{' '}\r\n            {(product.name === 'KIMITECH_BS_ST_200' ||\r\n              product.name === 'KIMITECH_BS_ST_400') &&\r\n            facingMaterial\r\n              ? (() => {\r\n                  let value = 0\r\n                  if (product.name === 'KIMITECH_BS_ST_200') {\r\n                    if (facingMaterial === FACING_MATERIAL.BRICK) {\r\n                      value = 1182\r\n                    } else if (facingMaterial === FACING_MATERIAL.TUFF) {\r\n                      value = 1165\r\n                    } else {\r\n                      value = 1126\r\n                    }\r\n                  } else {\r\n                    if (facingMaterial === FACING_MATERIAL.BRICK) {\r\n                      value = 1107\r\n                    } else if (facingMaterial === FACING_MATERIAL.TUFF) {\r\n                      value = 1072\r\n                    } else {\r\n                      value = 1066\r\n                    }\r\n                  }\r\n                  return value\r\n                })()\r\n              : product.tensileStrength}\r\n          </li>\r\n        )}\r\n        {product.width && (\r\n          <li>\r\n            {t('width')}: {product.width}\r\n          </li>\r\n        )}\r\n        {product.density && (\r\n          <li>\r\n            {t('density')}: {product.density}\r\n          </li>\r\n        )}\r\n        {product.maxResistance && (\r\n          <li>\r\n            {t('maxResistance')}: {product.maxResistance}\r\n          </li>\r\n        )}\r\n        {product.weight && (\r\n          <li>\r\n            {t('weight')}: {product.weight}\r\n          </li>\r\n        )}\r\n        {product.crossSectionArea && (\r\n          <li>\r\n            {t('crossSectionArea')}: {product.crossSectionArea}\r\n          </li>\r\n        )}\r\n        {product.diameter && (\r\n          <li>\r\n            {t('diameter')}: {product.diameter}\r\n          </li>\r\n        )}\r\n        {product.pullOutResistance && (\r\n          <li>\r\n            {t('pullOutResistance')}: {product.pullOutResistance}\r\n          </li>\r\n        )}\r\n\r\n        {product.connectorsNumberAlongLength && (\r\n          <li>\r\n            {t('connectorsNumberAlongLength')}:{' '}\r\n            {product.connectorsNumberAlongLength}\r\n          </li>\r\n        )}\r\n        {product.designStrength && (\r\n          <li>\r\n            {t('designStrength')}: {product.designStrength}\r\n          </li>\r\n        )}\r\n        {typeof product.systemDeformation === 'number' &&\r\n          product.systemDeformation > 0 && (\r\n            <li>\r\n              {t('systemDeformation')}: {product.systemDeformation}\r\n            </li>\r\n          )}\r\n        {product.adhesionToConcrete && (\r\n          <li>\r\n            {t('adhesionToConcrete')}: {product.adhesionToConcrete}\r\n          </li>\r\n        )}\r\n        {product.shearStress && (\r\n          <li>\r\n            {t('shearStress')}: {product.shearStress}\r\n          </li>\r\n        )}\r\n      </ul>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;;;AAQO,MAAM,qBAAqB;QAAC,EACjC,OAAO,EACP,iBAAiB,SAAS,EAC1B,oBAAoB,IAAI,EAClB;;IACN,MAAM,IAAI,IAAA,4NAAe,EAAC;IAE1B,MAAM,QAAQ,IAAA,4NAAe,EAC3B,AAAC,qBAAiE,OAA7C,oBAAoB,KAAK;IAGhD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BAAsB,EAAE;;;;;;0BACvC,6LAAC;gBAAG,WAAU;;oBACX,QAAQ,SAAS,kBAChB,6LAAC;;4BACE,EAAE;4BAAa;4BAAG,MAAM,AAAC,aAA8B,OAAlB,QAAQ,SAAS;;;;;;;oBAG1D,QAAQ,WAAW,kBAClB,6LAAC;;4BACE,EAAE;4BAAe;4BAAG,MAAM,AAAC,eAAkC,OAApB,QAAQ,WAAW;;;;;;;oBAGhE,QAAQ,SAAS,kBAChB,6LAAC;;4BACE,EAAE;4BAAa;4BAAG,QAAQ,SAAS;;;;;;;oBAGvC,QAAQ,eAAe,kBACtB,6LAAC;;4BACE,EAAE;4BAAmB;4BAAG,QAAQ,eAAe,CAAC,IAAI,CAAC;;;;;;;oBAGzD,QAAQ,cAAc,kBACrB,6LAAC;;4BACE,EAAE;4BAAkB;4BAAG,QAAQ,cAAc;;;;;;;oBAGjD,QAAQ,4BAA4B,kBACnC,6LAAC;;4BACE,EAAE;4BAAgC;4BAAE;4BACpC,QAAQ,4BAA4B;;;;;;;oBAGxC,kBACC,QAAQ,iBAAiB,IACzB,QAAQ,iBAAiB,CAAC,eAAe,kBACvC,6LAAC;;4BACE,EAAE;4BAAqB;4BAAG,EAAE,AAAC,kBAAgC,OAAf;4BAAkB;4BAC7D,QAAQ,iBAAiB,CAAC,eAAe;;;;;;;oBAGlD,QAAQ,WAAW,kBAClB,6LAAC;;4BACE,EAAE;4BAAe;4BAAG,QAAQ,WAAW;;;;;;;oBAG3C,QAAQ,cAAc,kBACrB,6LAAC;;4BACE,EAAE;4BAAkB;4BAAG,QAAQ,cAAc;;;;;;;oBAGjD,QAAQ,iBAAiB,kBACxB,6LAAC;;4BACE,EAAE;4BAAqB;4BAAG,QAAQ,iBAAiB;;;;;;;oBAGvD,QAAQ,qBAAqB,kBAC5B,6LAAC;;4BACE,EAAE;4BAAyB;4BAAG,QAAQ,qBAAqB;;;;;;;oBAG/D,QAAQ,4BAA4B,kBACnC,6LAAC;;4BACE,EAAE;4BAAgC;4BAAE;4BACpC,QAAQ,4BAA4B;;;;;;;oBAGxC,QAAQ,2BAA2B,kBAClC,6LAAC;;4BACE,EAAE;4BAA+B;4BAAE;4BACnC,QAAQ,2BAA2B;;;;;;;oBAGvC,QAAQ,kBAAkB,kBACzB,6LAAC;;4BACE,EAAE;4BAAsB;4BAAG,QAAQ,kBAAkB;;;;;;;oBAIzD,QAAQ,WAAW,KAAK,wBACvB,6LAAC;;4BACE,EAAE;4BAAyB;4BAAE;4BAC7B,CAAC,QAAQ,IAAI,KAAK,wBACjB,QAAQ,IAAI,KAAK,oBAAoB,KACvC,iBACI,CAAC;gCACC,IAAI,QAAQ;gCACZ,IAAI,QAAQ,IAAI,KAAK,sBAAsB;oCACzC,IAAI,mBAAmB,iJAAe,CAAC,KAAK,EAAE;wCAC5C,QAAQ;oCACV,OAAO,IAAI,mBAAmB,iJAAe,CAAC,IAAI,EAAE;wCAClD,QAAQ;oCACV,OAAO;wCACL,QAAQ;oCACV;gCACF,OAAO;oCACL,IAAI,mBAAmB,iJAAe,CAAC,KAAK,EAAE;wCAC5C,QAAQ;oCACV,OAAO,IAAI,mBAAmB,iJAAe,CAAC,IAAI,EAAE;wCAClD,QAAQ;oCACV,OAAO;wCACL,QAAQ;oCACV;gCACF;gCACA,OAAO;4BACT,CAAC,MACD,QAAQ,eAAe;;;;;;;oBAG9B,QAAQ,KAAK,kBACZ,6LAAC;;4BACE,EAAE;4BAAS;4BAAG,QAAQ,KAAK;;;;;;;oBAG/B,QAAQ,OAAO,kBACd,6LAAC;;4BACE,EAAE;4BAAW;4BAAG,QAAQ,OAAO;;;;;;;oBAGnC,QAAQ,aAAa,kBACpB,6LAAC;;4BACE,EAAE;4BAAiB;4BAAG,QAAQ,aAAa;;;;;;;oBAG/C,QAAQ,MAAM,kBACb,6LAAC;;4BACE,EAAE;4BAAU;4BAAG,QAAQ,MAAM;;;;;;;oBAGjC,QAAQ,gBAAgB,kBACvB,6LAAC;;4BACE,EAAE;4BAAoB;4BAAG,QAAQ,gBAAgB;;;;;;;oBAGrD,QAAQ,QAAQ,kBACf,6LAAC;;4BACE,EAAE;4BAAY;4BAAG,QAAQ,QAAQ;;;;;;;oBAGrC,QAAQ,iBAAiB,kBACxB,6LAAC;;4BACE,EAAE;4BAAqB;4BAAG,QAAQ,iBAAiB;;;;;;;oBAIvD,QAAQ,2BAA2B,kBAClC,6LAAC;;4BACE,EAAE;4BAA+B;4BAAE;4BACnC,QAAQ,2BAA2B;;;;;;;oBAGvC,QAAQ,cAAc,kBACrB,6LAAC;;4BACE,EAAE;4BAAkB;4BAAG,QAAQ,cAAc;;;;;;;oBAGjD,OAAO,QAAQ,iBAAiB,KAAK,YACpC,QAAQ,iBAAiB,GAAG,mBAC1B,6LAAC;;4BACE,EAAE;4BAAqB;4BAAG,QAAQ,iBAAiB;;;;;;;oBAGzD,QAAQ,kBAAkB,kBACzB,6LAAC;;4BACE,EAAE;4BAAsB;4BAAG,QAAQ,kBAAkB;;;;;;;oBAGzD,QAAQ,WAAW,kBAClB,6LAAC;;4BACE,EAAE;4BAAe;4BAAG,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;AAMpD;GA5La;;QAKD,4NAAe;QAEX,4NAAe;;;KAPlB", "debugId": null}}]}