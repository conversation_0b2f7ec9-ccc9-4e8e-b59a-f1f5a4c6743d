import {
  type RectangularBeamForm,
  rectangularBeamFormSchema,
} from '@atlas/types/schemas/rectangular-beam-from'
import { z } from 'zod'
import type { MasonryAntiOverturningParamsFormSchemaInputs } from './masonry/antioverturning-form'
import { masonryAntiOverturningParamsFormSchema } from './masonry/antioverturning-form'
import { type CrmFormSchemaInputs, crmFormSchema } from './masonry/crm-form'
import {
  type FrcmColumnParamsSchemaInput,
  frcmColumnParamsSchema,
} from './masonry/frcm-column-form'
import {
  type MaschiMurariParamsSchemaInputs,
  maschiMurariParamsSchema,
} from './masonry/maschi-murari-form'
import { type SlabForm, slabFormSchema } from './slab-form'
import { type TBeamForm, tBeamFormSchema } from './t-beam-form'

export const moduleParamsFormSchema = z.union([
  rectangularBeamFormSchema,
  masonryAntiOverturningParamsFormSchema,
  tBeamFormSchema,
  crmFormSchema,
  frcmColumnParamsSchema,
  maschiMurariParamsSchema,
  slabFormSchema,
])

export type ModuleParamsForm = z.infer<typeof moduleParamsFormSchema>

export type ModuleParamsForType = {
  T_BEAM: TBeamForm
  RECTANGULAR_BEAM: RectangularBeamForm
  ANTI_OVERTURNING: MasonryAntiOverturningParamsFormSchemaInputs
  CRM: CrmFormSchemaInputs
  FRCM_COLUMN: FrcmColumnParamsSchemaInput
  MASCHI_MURARI: MaschiMurariParamsSchemaInputs
  SLAB: SlabForm
}
