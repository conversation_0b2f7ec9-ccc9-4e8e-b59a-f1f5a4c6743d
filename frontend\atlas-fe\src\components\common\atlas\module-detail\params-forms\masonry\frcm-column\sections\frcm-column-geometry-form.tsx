import { NumberFormInput } from '@atlas/components/common/form/number-form-input'
import { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'
import { Button } from '@atlas/components/ui/button'
import { Form } from '@atlas/components/ui/form'
import {
  FRM_GEOMETRY_TOPOLOGY,
  MODULE_FRM_GEOMETRY_TOPOLOGY,
} from '@atlas/constants/module'
import type { Module } from '@atlas/lib/api/modules/schemas/modules'
import type { Project } from '@atlas/lib/api/projects/schemas/projects'
import { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'
import {
  type FrcmColumnGeometryInput,
  type FrcmColumnParamsSchemaInput,
  frcmColumnGeometrySchema,
} from '@atlas/types/schemas/masonry/frcm-column-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { useQueryClient } from '@tanstack/react-query'
import { Loader2Icon } from 'lucide-react'
import Image from 'next/image'
import type { Session } from 'next-auth'
import { useTranslations } from 'next-intl'
import { useCallback, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'

type Props = {
  session: Session
  projectId: Project['id']
  moduleId: Module['id']
  defaultValues?: Partial<FrcmColumnGeometryInput>
  params: FrcmColumnParamsSchemaInput
  setParams: (newParams: any) => void
  onSave: () => void
}

export const FrcmColumnGeometryForm = ({
  session,
  projectId,
  moduleId,
  defaultValues,
  params,
  setParams,
  onSave,
}: Props) => {
  const t = useTranslations('forms.project-params.frcm-column.geometry')
  const tAction = useTranslations('actions.save-project-params.messages')
  const tCommon = useTranslations('actions.common')
  const queryClient = useQueryClient()

  const form = useForm<FrcmColumnGeometryInput>({
    resolver: zodResolver(frcmColumnGeometrySchema),
    defaultValues: {
      topology: defaultValues?.topology ?? FRM_GEOMETRY_TOPOLOGY.RECTANGULAR,
      largerSizeOrColumnDiameter:
        defaultValues?.largerSizeOrColumnDiameter ?? 0,
      smallerSize: defaultValues?.smallerSize ?? 0,
      crossSectionArea: defaultValues?.crossSectionArea ?? 0,
      crossSectionDiagonal: defaultValues?.crossSectionDiagonal ?? 0,
      cornerRoundingRadius: defaultValues?.cornerRoundingRadius ?? 0,
    },
  })

  const { mutate, isPending } = useSaveModuleParamsMutation(
    session.accessToken,
    {
      onSuccess: async () => {
        toast.success(tAction('edit.success'))
        await queryClient.invalidateQueries({
          queryKey: ['modules', { projectId, moduleId }],
        })
        onSave()
      },
      onError: error => {
        toast.error(tAction('edit.failure', { error: error.message }))
      },
    },
  )

  const handleFormSubmit = useCallback(
    (body: FrcmColumnGeometryInput) => {
      // construct body based on global schema:
      const frcmColumnParams: FrcmColumnParamsSchemaInput = {
        ...params,
        geometry: body,
      }
      mutate({ projectId, moduleId, body: frcmColumnParams })
    },
    [mutate, projectId, moduleId, params],
  )

  const topologyValue = form.watch('topology')
  const smallerSizeValue = form.watch('smallerSize')
  const largerSizeOrColumnDiameterValue = form.watch(
    'largerSizeOrColumnDiameter',
  )

  useEffect(() => {
    const smallerSize =
      topologyValue === FRM_GEOMETRY_TOPOLOGY.CIRCULAR ? 0 : smallerSizeValue
    form.setValue('smallerSize', parseFloat(smallerSize.toFixed(2)))

    const crossSectionArea =
      topologyValue === FRM_GEOMETRY_TOPOLOGY.CIRCULAR
        ? Math.PI * (largerSizeOrColumnDiameterValue / 2) ** 2
        : largerSizeOrColumnDiameterValue * smallerSizeValue
    form.setValue('crossSectionArea', parseFloat(crossSectionArea.toFixed(2)))

    const crossSectionDiagonal =
      topologyValue === FRM_GEOMETRY_TOPOLOGY.CIRCULAR
        ? 0
        : Math.sqrt(
            largerSizeOrColumnDiameterValue ** 2 + smallerSizeValue ** 2,
          )
    form.setValue(
      'crossSectionDiagonal',
      parseFloat(crossSectionDiagonal.toFixed(2)),
    )
  }, [topologyValue, smallerSizeValue, largerSizeOrColumnDiameterValue, form])

  useEffect(() => {
    const subscription = form.watch(values => {
      setParams((prev: any) => ({
        ...prev,
        buildingCharacteristics: values,
      }))
    })
    return () => subscription.unsubscribe()
  }, [form, setParams])

  return (
    <Form {...form}>
      <form
        className="space-y-4 rounded-md border p-4"
        onSubmit={form.handleSubmit(handleFormSubmit)}
      >
        <Image
          src="/assets/masonry_frcm/FRCM_Muratura.jpg"
          alt="site characteristics"
          height={250}
          width={500}
          className="mx-auto rounded-md object-contain"
          priority
        />
        <SelectFormFixedInput
          control={form.control}
          name="topology"
          options={MODULE_FRM_GEOMETRY_TOPOLOGY}
          optionLabelFn={p => t(`topology.${p}`)}
          t={t}
        />
        <NumberFormInput
          control={form.control}
          name="largerSizeOrColumnDiameter"
          t={msg => t(`${topologyValue}.${msg}`)}
          disabled={topologyValue === FRM_GEOMETRY_TOPOLOGY.CIRCULAR}
        />
        <NumberFormInput
          control={form.control}
          name="smallerSize"
          t={msg => t(`${topologyValue}.${msg}`)}
          disabled={topologyValue === FRM_GEOMETRY_TOPOLOGY.CIRCULAR}
        />

        <NumberFormInput
          control={form.control}
          name="crossSectionArea"
          t={t}
          disabled={true}
        />
        <NumberFormInput
          control={form.control}
          name="crossSectionDiagonal"
          t={msg => t(`${topologyValue}.${msg}`)}
          disabled={true}
        />
        <NumberFormInput
          control={form.control}
          name="cornerRoundingRadius"
          t={msg => t(`${topologyValue}.${msg}`)}
        />
        <Button
          type="submit"
          className="w-full sm:w-auto"
          disabled={isPending}
          onClick={form.handleSubmit(handleFormSubmit)}
        >
          {isPending && <Loader2Icon className="animate-spin" />}
          {tCommon('save')}
        </Button>
      </form>
    </Form>
  )
}
