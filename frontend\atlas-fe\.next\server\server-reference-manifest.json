{"node": {"0044f9ee86f66c90fe7b1aeac96677d3571a4ae54e": {"workers": {"app/[locale]/page": {"moduleId": "[project]/.next-internal/server/app/[locale]/page/actions.js { ACTIONS_MODULE0 => \"[project]/src/components/common/atlas/login-form.tsx [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false, "exportedName": "$$RSC_SERVER_ACTION_0", "filename": "src/components/common/atlas/login-form.tsx"}}, "layer": {"app/[locale]/page": "rsc"}, "filename": "src/components/common/atlas/login-form.tsx", "exportedName": "$$RSC_SERVER_ACTION_0"}}, "edge": {}, "encryptionKey": "I6RcvEX/7jg239u70bwr/neXvphypWgYq6I/3D6/niM="}