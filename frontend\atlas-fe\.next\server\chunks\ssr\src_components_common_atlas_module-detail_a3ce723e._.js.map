{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/delete-module-modal.tsx"], "sourcesContent": ["'use client'\r\nimport { moduleToDeleteAtom } from '@atlas/components/common/atlas/module-detail/module-detail-content'\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogAction, // <-- The component we are modifying\r\n  AlertDialogCancel,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogFooter,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@atlas/components/ui/alert-dialog'\r\nimport { buttonVariants } from '@atlas/components/ui/button'\r\nimport { useRouter } from '@atlas/i18n/routing'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useDeleteModuleMutation } from '@atlas/lib/mutation/modules/use-delete-module-mutation'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { useAtom } from 'jotai/index'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  project: Project\r\n}\r\n\r\nexport const DeleteModuleModal = ({ session, project }: Props) => {\r\n  const t = useTranslations('components.modules.delete-modal')\r\n  const tAction = useTranslations('actions.delete-module.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const [module, setModuleToDelete] = useAtom(moduleToDeleteAtom)\r\n  const router = useRouter()\r\n  const queryClient = useQueryClient()\r\n\r\n  const { mutate, isPending } = useDeleteModuleMutation(session.accessToken, {\r\n    onSuccess: async () => {\r\n      toast.success(tAction('delete.success'))\r\n      setModuleToDelete(undefined)\r\n      await queryClient.invalidateQueries({\r\n        queryKey: ['projects', project.id],\r\n      })\r\n      router.push(`/dashboard/projects/${project.id}`)\r\n    },\r\n    onError: error => {\r\n      toast.error(tAction('delete.failure', { error: error.message }))\r\n    },\r\n  })\r\n\r\n  const handleConfirm = () => {\r\n    mutate({ projectId: project.id, moduleId: module!.id })\r\n  }\r\n\r\n  const handleCancel = () => {\r\n    setModuleToDelete(undefined)\r\n  }\r\n\r\n  return (\r\n    <AlertDialog\r\n      open={!!module}\r\n      onOpenChange={() => {\r\n        setModuleToDelete(undefined)\r\n      }}\r\n    >\r\n      <AlertDialogContent>\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>{t('title')}</AlertDialogTitle>\r\n          <AlertDialogDescription>\r\n            {t('description', {\r\n              projectName: project.constructionSiteName ?? '',\r\n              moduleName: module!.name ?? '',\r\n            })}\r\n          </AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <AlertDialogFooter>\r\n          <AlertDialogCancel onClick={handleCancel}>\r\n            {tCommon('cancel')}\r\n          </AlertDialogCancel>\r\n          <AlertDialogAction\r\n            className={buttonVariants({ variant: 'destructive' })}\r\n            onClick={handleConfirm}\r\n          >\r\n            {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n            {tCommon('delete')}\r\n          </AlertDialogAction>\r\n        </AlertDialogFooter>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAUA;AACA;AAEA;AACA;AACA;AACA;AAEA;AACA;AArBA;;;;;;;;;;;;AA4BO,MAAM,oBAAoB,CAAC,EAAE,OAAO,EAAE,OAAO,EAAS;IAC3D,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,CAAC,QAAQ,kBAAkB,GAAG,IAAA,iJAAO,EAAC,8MAAkB;IAC9D,MAAM,SAAS,IAAA,mIAAS;IACxB,MAAM,cAAc,IAAA,wMAAc;IAElC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,oMAAuB,EAAC,QAAQ,WAAW,EAAE;QACzE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,kBAAkB;YAClB,MAAM,YAAY,iBAAiB,CAAC;gBAClC,UAAU;oBAAC;oBAAY,QAAQ,EAAE;iBAAC;YACpC;YACA,OAAO,IAAI,CAAC,CAAC,oBAAoB,EAAE,QAAQ,EAAE,EAAE;QACjD;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,kBAAkB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC/D;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAO;YAAE,WAAW,QAAQ,EAAE;YAAE,UAAU,OAAQ,EAAE;QAAC;IACvD;IAEA,MAAM,eAAe;QACnB,kBAAkB;IACpB;IAEA,qBACE,8OAAC,0JAAW;QACV,MAAM,CAAC,CAAC;QACR,cAAc;YACZ,kBAAkB;QACpB;kBAEA,cAAA,8OAAC,iKAAkB;;8BACjB,8OAAC,gKAAiB;;sCAChB,8OAAC,+JAAgB;sCAAE,EAAE;;;;;;sCACrB,8OAAC,qKAAsB;sCACpB,EAAE,eAAe;gCAChB,aAAa,QAAQ,oBAAoB,IAAI;gCAC7C,YAAY,OAAQ,IAAI,IAAI;4BAC9B;;;;;;;;;;;;8BAGJ,8OAAC,gKAAiB;;sCAChB,8OAAC,gKAAiB;4BAAC,SAAS;sCACzB,QAAQ;;;;;;sCAEX,8OAAC,gKAAiB;4BAChB,WAAW,IAAA,oJAAc,EAAC;gCAAE,SAAS;4BAAc;4BACnD,SAAS;;gCAER,2BAAa,8OAAC,oOAAW;oCAAC,WAAU;;;;;;gCACpC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;AAMrB", "debugId": null}}, {"offset": {"line": 149, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/edit-module-modal.tsx"], "sourcesContent": ["import { editModuleModalOpenAtom } from '@atlas/components/common/atlas/module-detail/module-detail-content'\r\nimport { StringFormInput } from '@atlas/components/common/form/string-form-input'\r\nimport {\r\n  AlertDialog,\r\n  AlertDialogContent,\r\n  AlertDialogDescription,\r\n  AlertDialogHeader,\r\n  AlertDialogTitle,\r\n} from '@atlas/components/ui/alert-dialog'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { onEnterKey } from '@atlas/functions/forms/on-enter-key'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useEditModuleMutation } from '@atlas/lib/mutation/modules/use-edit-module-mutation'\r\nimport {\r\n  type EditModuleForm,\r\n  editModuleFormSchema,\r\n} from '@atlas/types/schemas/module-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useAtom } from 'jotai/index'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  project: Project\r\n  module: Module\r\n}\r\n\r\nexport const EditModuleModal = ({ session, project, module }: Props) => {\r\n  const t = useTranslations('components.modules.edit-modal')\r\n  const tAction = useTranslations('actions.edit-module.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const tFields = useTranslations('forms.create-module.fields')\r\n\r\n  const [open, onOpenChange] = useAtom(editModuleModalOpenAtom)\r\n  const form = useForm<EditModuleForm>({\r\n    resolver: zodResolver(editModuleFormSchema),\r\n    defaultValues: {\r\n      name: module?.name ?? '',\r\n      description: module?.description ?? '',\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useEditModuleMutation(session.accessToken, {\r\n    onSuccess: () => {\r\n      toast.success(tAction('edit.success'))\r\n      onOpenChange(false)\r\n    },\r\n    onError: error => {\r\n      toast.error(tAction('edit.failure', { error: error.message }))\r\n    },\r\n  })\r\n\r\n  const onSubmit = (body: EditModuleForm) => {\r\n    mutate({ projectId: project.id, moduleId: module.id, body })\r\n  }\r\n\r\n  return (\r\n    <AlertDialog open={open} onOpenChange={onOpenChange}>\r\n      <AlertDialogContent>\r\n        <AlertDialogHeader>\r\n          <AlertDialogTitle>{t('title')}</AlertDialogTitle>\r\n          <AlertDialogDescription>\r\n            {t('description', {\r\n              projectName: project.constructionSiteName ?? '',\r\n            })}\r\n          </AlertDialogDescription>\r\n        </AlertDialogHeader>\r\n        <Form {...form}>\r\n          <form\r\n            onSubmit={form.handleSubmit(onSubmit)}\r\n            onKeyDown={onEnterKey(form.handleSubmit(onSubmit))}\r\n            className=\"space-y-6\"\r\n          >\r\n            <StringFormInput\r\n              required\r\n              control={form.control}\r\n              name=\"name\"\r\n              t={tFields}\r\n            />\r\n            <StringFormInput\r\n              control={form.control}\r\n              name=\"description\"\r\n              t={tFields}\r\n            />\r\n            <div className=\"flex justify-end gap-1\">\r\n              <Button\r\n                variant=\"secondary\"\r\n                className=\"w-30\"\r\n                onClick={() => onOpenChange(false)}\r\n              >\r\n                {tCommon('cancel')}\r\n              </Button>\r\n              <Button type=\"submit\" className=\"w-30\" disabled={isPending}>\r\n                {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n                {tCommon('save')}\r\n              </Button>\r\n            </div>\r\n          </form>\r\n        </Form>\r\n      </AlertDialogContent>\r\n    </AlertDialog>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAOA;AACA;AACA;AAGA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;;;;;;;;;;;;;;;;AAQO,MAAM,kBAAkB,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAS;IACjE,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAChC,MAAM,UAAU,IAAA,yNAAe,EAAC;IAEhC,MAAM,CAAC,MAAM,aAAa,GAAG,IAAA,iJAAO,EAAC,mNAAuB;IAC5D,MAAM,OAAO,IAAA,yKAAO,EAAiB;QACnC,UAAU,IAAA,6KAAW,EAAC,iKAAoB;QAC1C,eAAe;YACb,MAAM,QAAQ,QAAQ;YACtB,aAAa,QAAQ,eAAe;QACtC;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,gMAAqB,EAAC,QAAQ,WAAW,EAAE;QACvE,WAAW;YACT,iJAAK,CAAC,OAAO,CAAC,QAAQ;YACtB,aAAa;QACf;QACA,SAAS,CAAA;YACP,iJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC7D;IACF;IAEA,MAAM,WAAW,CAAC;QAChB,OAAO;YAAE,WAAW,QAAQ,EAAE;YAAE,UAAU,OAAO,EAAE;YAAE;QAAK;IAC5D;IAEA,qBACE,8OAAC,0JAAW;QAAC,MAAM;QAAM,cAAc;kBACrC,cAAA,8OAAC,iKAAkB;;8BACjB,8OAAC,gKAAiB;;sCAChB,8OAAC,+JAAgB;sCAAE,EAAE;;;;;;sCACrB,8OAAC,qKAAsB;sCACpB,EAAE,eAAe;gCAChB,aAAa,QAAQ,oBAAoB,IAAI;4BAC/C;;;;;;;;;;;;8BAGJ,8OAAC,wIAAI;oBAAE,GAAG,IAAI;8BACZ,cAAA,8OAAC;wBACC,UAAU,KAAK,YAAY,CAAC;wBAC5B,WAAW,IAAA,6JAAU,EAAC,KAAK,YAAY,CAAC;wBACxC,WAAU;;0CAEV,8OAAC,kLAAe;gCACd,QAAQ;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,GAAG;;;;;;0CAEL,8OAAC,kLAAe;gCACd,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,GAAG;;;;;;0CAEL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4IAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS,IAAM,aAAa;kDAE3B,QAAQ;;;;;;kDAEX,8OAAC,4IAAM;wCAAC,MAAK;wCAAS,WAAU;wCAAO,UAAU;;4CAC9C,2BAAa,8OAAC,oOAAW;gDAAC,WAAU;;;;;;4CACpC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzB", "debugId": null}}, {"offset": {"line": 334, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/module-detail-header.tsx"], "sourcesContent": ["import {\r\n  editModuleModalOpenAtom,\r\n  moduleToDeleteAtom,\r\n} from '@atlas/components/common/atlas/module-detail/module-detail-content'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Card, CardContent, CardHeader } from '@atlas/components/ui/card'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport { useSetAtom } from 'jotai'\r\nimport Image from 'next/image'\r\nimport { useFormatter, useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  project: Project\r\n  module: Module\r\n}\r\n\r\nexport const ModuleDetailHeader = ({ project, module }: Props) => {\r\n  const t = useTranslations('components.module-detail.header')\r\n  const tModuleType = useTranslations('enums.module-type')\r\n  const format = useFormatter()\r\n  const setOpenEditModuleModal = useSetAtom(editModuleModalOpenAtom)\r\n  const setModuleToDelete = useSetAtom(moduleToDeleteAtom)\r\n\r\n  return (\r\n    <Card className=\"top-6 z-10 mb-6\">\r\n      <CardHeader className=\"flex flex-col items-center justify-between pb-2 pt-6 pl-6 pr-6\">\r\n        <Image\r\n          src=\"/assets/logo-kimia-orizzontale.png\"\r\n          alt=\"logo\"\r\n          height={250}\r\n          width={500}\r\n          className=\"mx-auto rounded-md object-contain\"\r\n          priority\r\n        />\r\n        <h1 className=\"text-2xl font-semibold tracking-tight md:text-3xl\">\r\n          {module.name ?? t('unnamed-module')}\r\n        </h1>\r\n        <div className={cn('flex flex-col lg:flex-row gap-2')}>\r\n          <Button size=\"sm\" onClick={() => setOpenEditModuleModal(true)}>\r\n            {t('edit-module')}\r\n          </Button>\r\n          <Button\r\n            variant=\"destructive\"\r\n            size=\"sm\"\r\n            onClick={() => setModuleToDelete(module)}\r\n          >\r\n            {t('delete-module')}\r\n          </Button>\r\n        </div>\r\n      </CardHeader>\r\n      <CardContent className=\" flex flex-col gap-1 pb-6 pl-6 pr-6 pt-0\">\r\n        <div className=\"flex flex-wrap items-center justify-start gap-x-4 gap-y-1 text-sm text-muted-foreground\">\r\n          {project.constructionSiteName && (\r\n            <span>\r\n              {t('project')}: {project.constructionSiteName}\r\n            </span>\r\n          )}\r\n          {module.description && (\r\n            <span>\r\n              {t('description')}: {module.description}\r\n            </span>\r\n          )}\r\n          {module.type && (\r\n            <span>\r\n              {t('type')}: {tModuleType(module.type)}\r\n            </span>\r\n          )}\r\n          {module.createdAt && (\r\n            <span>\r\n              {t('created-label')}:{' '}\r\n              {format.dateTime(module.createdAt, {\r\n                year: 'numeric',\r\n                month: 'long',\r\n                day: 'numeric',\r\n                hour: '2-digit',\r\n                minute: '2-digit',\r\n              })}\r\n            </span>\r\n          )}\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAIA;AACA;AAGA;AACA;AACA;AACA;;;;;;;;;AAOO,MAAM,qBAAqB,CAAC,EAAE,OAAO,EAAE,MAAM,EAAS;IAC3D,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,cAAc,IAAA,yNAAe,EAAC;IACpC,MAAM,SAAS,IAAA,sNAAY;IAC3B,MAAM,yBAAyB,IAAA,oJAAU,EAAC,mNAAuB;IACjE,MAAM,oBAAoB,IAAA,oJAAU,EAAC,8MAAkB;IAEvD,qBACE,8OAAC,wIAAI;QAAC,WAAU;;0BACd,8OAAC,8IAAU;gBAAC,WAAU;;kCACpB,8OAAC,wIAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,QAAQ;wBACR,OAAO;wBACP,WAAU;wBACV,QAAQ;;;;;;kCAEV,8OAAC;wBAAG,WAAU;kCACX,OAAO,IAAI,IAAI,EAAE;;;;;;kCAEpB,8OAAC;wBAAI,WAAW,IAAA,yHAAE,EAAC;;0CACjB,8OAAC,4IAAM;gCAAC,MAAK;gCAAK,SAAS,IAAM,uBAAuB;0CACrD,EAAE;;;;;;0CAEL,8OAAC,4IAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,kBAAkB;0CAEhC,EAAE;;;;;;;;;;;;;;;;;;0BAIT,8OAAC,+IAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,QAAQ,oBAAoB,kBAC3B,8OAAC;;gCACE,EAAE;gCAAW;gCAAG,QAAQ,oBAAoB;;;;;;;wBAGhD,OAAO,WAAW,kBACjB,8OAAC;;gCACE,EAAE;gCAAe;gCAAG,OAAO,WAAW;;;;;;;wBAG1C,OAAO,IAAI,kBACV,8OAAC;;gCACE,EAAE;gCAAQ;gCAAG,YAAY,OAAO,IAAI;;;;;;;wBAGxC,OAAO,SAAS,kBACf,8OAAC;;gCACE,EAAE;gCAAiB;gCAAE;gCACrB,OAAO,QAAQ,CAAC,OAAO,SAAS,EAAE;oCACjC,MAAM;oCACN,OAAO;oCACP,KAAK;oCACL,MAAM;oCACN,QAAQ;gCACV;;;;;;;;;;;;;;;;;;;;;;;;AAOd", "debugId": null}}, {"offset": {"line": 498, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/module-report-generation-section.tsx"], "sourcesContent": ["import { Alert, AlertDescription, AlertTitle } from '@atlas/components/ui/alert'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Card, CardContent, CardHeader } from '@atlas/components/ui/card'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { getModuleReportQuery } from '@atlas/lib/query/modules/get-module-report'\r\nimport { useMutation } from '@tanstack/react-query'\r\nimport { AlertCircleIcon, Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useState } from 'react'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  enabled: boolean\r\n}\r\n\r\nexport const ModuleReportGenerationSection = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  enabled,\r\n}: Props) => {\r\n  const t = useTranslations('components.module-detail.report-generation')\r\n  const [loading, setLoading] = useState<string | number | undefined>(undefined)\r\n  const { mutate } = useMutation({\r\n    mutationFn: () => getModuleReportQuery(session, projectId, moduleId),\r\n    onMutate: () => {\r\n      const toastId = toast.loading(t('executing.loading'))\r\n      setLoading(toastId)\r\n    },\r\n    onError: error => {\r\n      console.log(error)\r\n      toast.dismiss(loading)\r\n      setLoading(undefined)\r\n      toast.error(t('executing.failure', { error: error.message }))\r\n    },\r\n    onSuccess: ({ blob, filename }) => {\r\n      toast.dismiss(loading)\r\n      setLoading(undefined)\r\n      const url = window.URL.createObjectURL(new Blob([blob]))\r\n      const link = document.createElement('a')\r\n      link.href = url\r\n      document.body.appendChild(link)\r\n      link.setAttribute('download', filename)\r\n      link.click()\r\n      link.parentNode?.removeChild(link)\r\n      window.URL.revokeObjectURL(url)\r\n      toast.info(t('executing.success'))\r\n    },\r\n  })\r\n\r\n  return (\r\n    <Card className=\"top-6 z-10 mb-6\">\r\n      <CardHeader className=\"flex flex-col items-center justify-between pb-2 pt-6 pl-6 pr-6\">\r\n        <h1 className=\"text-2xl font-semibold tracking-tight md:text-3xl\">\r\n          {t('header')}\r\n        </h1>\r\n      </CardHeader>\r\n      <CardContent className=\" flex flex-col gap-1 pb-6 pl-6 pr-6 pt-0\">\r\n        {enabled && (\r\n          <Button size=\"sm\" onClick={() => mutate()} disabled={!!loading}>\r\n            {!!loading && <Loader2Icon className=\"animate-spin\" />}\r\n            {t('execute')}\r\n          </Button>\r\n        )}\r\n        {!enabled && (\r\n          <Alert>\r\n            <AlertCircleIcon />\r\n            <AlertTitle>{t('alert.header')}</AlertTitle>\r\n            <AlertDescription>\r\n              <p>{t('alert.description')}</p>\r\n            </AlertDescription>\r\n          </Alert>\r\n        )}\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAGA;AACA;AACA;AAAA;AAEA;AACA;AACA;;;;;;;;;;;AASO,MAAM,gCAAgC,CAAC,EAC5C,OAAO,EACP,SAAS,EACT,QAAQ,EACR,OAAO,EACD;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAA8B;IACpE,MAAM,EAAE,MAAM,EAAE,GAAG,IAAA,6LAAW,EAAC;QAC7B,YAAY,IAAM,IAAA,iLAAoB,EAAC,SAAS,WAAW;QAC3D,UAAU;YACR,MAAM,UAAU,iJAAK,CAAC,OAAO,CAAC,EAAE;YAChC,WAAW;QACb;QACA,SAAS,CAAA;YACP,QAAQ,GAAG,CAAC;YACZ,iJAAK,CAAC,OAAO,CAAC;YACd,WAAW;YACX,iJAAK,CAAC,KAAK,CAAC,EAAE,qBAAqB;gBAAE,OAAO,MAAM,OAAO;YAAC;QAC5D;QACA,WAAW,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE;YAC5B,iJAAK,CAAC,OAAO,CAAC;YACd,WAAW;YACX,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC,IAAI,KAAK;gBAAC;aAAK;YACtD,MAAM,OAAO,SAAS,aAAa,CAAC;YACpC,KAAK,IAAI,GAAG;YACZ,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,KAAK,YAAY,CAAC,YAAY;YAC9B,KAAK,KAAK;YACV,KAAK,UAAU,EAAE,YAAY;YAC7B,OAAO,GAAG,CAAC,eAAe,CAAC;YAC3B,iJAAK,CAAC,IAAI,CAAC,EAAE;QACf;IACF;IAEA,qBACE,8OAAC,wIAAI;QAAC,WAAU;;0BACd,8OAAC,8IAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAG,WAAU;8BACX,EAAE;;;;;;;;;;;0BAGP,8OAAC,+IAAW;gBAAC,WAAU;;oBACpB,yBACC,8OAAC,4IAAM;wBAAC,MAAK;wBAAK,SAAS,IAAM;wBAAU,UAAU,CAAC,CAAC;;4BACpD,CAAC,CAAC,yBAAW,8OAAC,oOAAW;gCAAC,WAAU;;;;;;4BACpC,EAAE;;;;;;;oBAGN,CAAC,yBACA,8OAAC,0IAAK;;0CACJ,8OAAC,2OAAe;;;;;0CAChB,8OAAC,+IAAU;0CAAE,EAAE;;;;;;0CACf,8OAAC,qJAAgB;0CACf,cAAA,8OAAC;8CAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}, {"offset": {"line": 646, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/project-params-section.tsx"], "sourcesContent": ["import { PillarParamsForm } from '@atlas/components/common/atlas/module-detail/params-forms/pillar/pillar-params-form'\r\nimport { RectangularBeamParamsForm } from '@atlas/components/common/atlas/module-detail/params-forms/rectangular-beam/rectangular-beam-params-form'\r\nimport { SlabParamsForm } from '@atlas/components/common/atlas/module-detail/params-forms/slab/slab-params-form'\r\nimport { Alert, AlertDescription, AlertTitle } from '@atlas/components/ui/alert'\r\nimport { Card, CardContent } from '@atlas/components/ui/card'\r\nimport type { ModuleWithParams } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { MasonryAntiOverturningParamsForm } from './params-forms/masonry/anti-overturning/anti-overturning-params-form'\r\nimport { CrmParamsForm } from './params-forms/masonry/crm/crm-params-form'\r\nimport { FrcmColumnParamsForm } from './params-forms/masonry/frcm-column/frcm-column-params-form'\r\nimport { MaschiMurariParamsForm } from './params-forms/masonry/maschi-murari/maschi-murari-params-form'\r\nimport { TBeamParamsForm } from './params-forms/t-beam/t-beam-params-form'\r\nimport { WoodParamsForm } from './params-forms/wood/wood-params-form'\r\n\r\ntype Props = {\r\n  projectId: string\r\n  module: ModuleWithParams\r\n  session: Session\r\n}\r\n\r\nexport const ModuleParamsSection = ({ projectId, module, session }: Props) => {\r\n  const t = useTranslations('components.module-detail')\r\n\r\n  const renderParamsContent = () => {\r\n    switch (module.type) {\r\n      case 'RECTANGULAR_BEAM':\r\n        return (\r\n          <RectangularBeamParamsForm\r\n            projectId={projectId}\r\n            moduleId={module.id}\r\n            session={session}\r\n            module={module}\r\n          />\r\n        )\r\n      case 'T_BEAM':\r\n        return (\r\n          <TBeamParamsForm\r\n            projectId={projectId}\r\n            moduleId={module.id}\r\n            session={session}\r\n            module={module}\r\n          />\r\n        )\r\n      case 'SLAB':\r\n        return (\r\n          <SlabParamsForm\r\n            projectId={projectId}\r\n            moduleId={module.id}\r\n            session={session}\r\n            module={module}\r\n          />\r\n        )\r\n      case 'PILLAR':\r\n        return (\r\n          <PillarParamsForm\r\n            projectId={projectId}\r\n            moduleId={module.id}\r\n            session={session}\r\n            module={module}\r\n          />\r\n        )\r\n      case 'ANTI_OVERTURNING':\r\n        return (\r\n          <MasonryAntiOverturningParamsForm\r\n            projectId={projectId}\r\n            moduleId={module.id}\r\n            session={session}\r\n            module={module}\r\n          />\r\n        )\r\n      case 'CRM':\r\n        return (\r\n          <CrmParamsForm\r\n            projectId={projectId}\r\n            session={session}\r\n            module={module}\r\n          />\r\n        )\r\n      case 'FRCM_COLUMN':\r\n        return (\r\n          <FrcmColumnParamsForm\r\n            projectId={projectId}\r\n            moduleId={module.id}\r\n            session={session}\r\n            module={module}\r\n          />\r\n        )\r\n      case 'WOOD':\r\n        return (\r\n          <WoodParamsForm\r\n            projectId={projectId}\r\n            moduleId={module.id}\r\n            session={session}\r\n            module={module}\r\n          />\r\n        )\r\n      case 'MASCHI_MURARI':\r\n        return (\r\n          <MaschiMurariParamsForm\r\n            projectId={projectId}\r\n            moduleId={module.id}\r\n            session={session}\r\n            module={module}\r\n          />\r\n        )\r\n      default:\r\n        return (\r\n          <Alert variant=\"default\">\r\n            <AlertTitle>{t('params.no-form-available.title')}</AlertTitle>\r\n            <AlertDescription>\r\n              {t('params.no-form-available.description', {\r\n                projectType: module.type!,\r\n              })}\r\n            </AlertDescription>\r\n          </Alert>\r\n        )\r\n    }\r\n  }\r\n\r\n  return (\r\n    <Card className=\"container mx-auto max-w-2xl\">\r\n      <CardContent>{renderParamsContent()}</CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AAQO,MAAM,sBAAsB,CAAC,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAS;IACvE,MAAM,IAAI,IAAA,yNAAe,EAAC;IAE1B,MAAM,sBAAsB;QAC1B,OAAQ,OAAO,IAAI;YACjB,KAAK;gBACH,qBACE,8OAAC,yQAAyB;oBACxB,WAAW;oBACX,UAAU,OAAO,EAAE;oBACnB,SAAS;oBACT,QAAQ;;;;;;YAGd,KAAK;gBACH,qBACE,8OAAC,2OAAe;oBACd,WAAW;oBACX,UAAU,OAAO,EAAE;oBACnB,SAAS;oBACT,QAAQ;;;;;;YAGd,KAAK;gBACH,qBACE,8OAAC,gOAAc;oBACb,WAAW;oBACX,UAAU,OAAO,EAAE;oBACnB,SAAS;oBACT,QAAQ;;;;;;YAGd,KAAK;gBACH,qBACE,8OAAC,sOAAgB;oBACf,WAAW;oBACX,UAAU,OAAO,EAAE;oBACnB,SAAS;oBACT,QAAQ;;;;;;YAGd,KAAK;gBACH,qBACE,8OAAC,2RAAgC;oBAC/B,WAAW;oBACX,UAAU,OAAO,EAAE;oBACnB,SAAS;oBACT,QAAQ;;;;;;YAGd,KAAK;gBACH,qBACE,8OAAC,wOAAa;oBACZ,WAAW;oBACX,SAAS;oBACT,QAAQ;;;;;;YAGd,KAAK;gBACH,qBACE,8OAAC,qQAAoB;oBACnB,WAAW;oBACX,UAAU,OAAO,EAAE;oBACnB,SAAS;oBACT,QAAQ;;;;;;YAGd,KAAK;gBACH,qBACE,8OAAC,gOAAc;oBACb,WAAW;oBACX,UAAU,OAAO,EAAE;oBACnB,SAAS;oBACT,QAAQ;;;;;;YAGd,KAAK;gBACH,qBACE,8OAAC,2QAAsB;oBACrB,WAAW;oBACX,UAAU,OAAO,EAAE;oBACnB,SAAS;oBACT,QAAQ;;;;;;YAGd;gBACE,qBACE,8OAAC,0IAAK;oBAAC,SAAQ;;sCACb,8OAAC,+IAAU;sCAAE,EAAE;;;;;;sCACf,8OAAC,qJAAgB;sCACd,EAAE,wCAAwC;gCACzC,aAAa,OAAO,IAAI;4BAC1B;;;;;;;;;;;;QAIV;IACF;IAEA,qBACE,8OAAC,wIAAI;QAAC,WAAU;kBACd,cAAA,8OAAC,+IAAW;sBAAE;;;;;;;;;;;AAGpB", "debugId": null}}, {"offset": {"line": 825, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/module-detail-content.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { <PERSON><PERSON><PERSON><PERSON><PERSON>t } from '@atlas/components/common/atlas/error-alert'\r\nimport { HeaderSkeleton } from '@atlas/components/common/atlas/header-skeleton'\r\nimport { DeleteModuleModal } from '@atlas/components/common/atlas/module-detail/delete-module-modal'\r\nimport { EditModuleModal } from '@atlas/components/common/atlas/module-detail/edit-module-modal'\r\nimport { ModuleDetailHeader } from '@atlas/components/common/atlas/module-detail/module-detail-header'\r\nimport { ModuleParamsSection } from '@atlas/components/common/atlas/module-detail/project-params-section'\r\nimport { Alert, AlertDescription, AlertTitle } from '@atlas/components/ui/alert'\r\nimport { Skeleton } from '@atlas/components/ui/skeleton'\r\nimport type {\r\n  Module,\r\n  ModuleWithParams,\r\n} from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useModule } from '@atlas/lib/query/modules/use-module'\r\nimport { useProject } from '@atlas/lib/query/projects/use-project'\r\nimport { useAtomValue } from 'jotai'\r\nimport { atom } from 'jotai/index'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: string\r\n  initialDataProject?: Project\r\n  moduleId: string\r\n  initialDataModule?: ModuleWithParams\r\n}\r\n\r\nexport const editModuleModalOpenAtom = atom<boolean>(false)\r\nexport const moduleToDeleteAtom = atom<Module | undefined>(undefined)\r\n\r\nexport const ModuleDetailContent = ({\r\n  session,\r\n  projectId,\r\n  initialDataProject,\r\n  moduleId,\r\n  initialDataModule,\r\n}: Props) => {\r\n  const t = useTranslations('pages.module-detail')\r\n  const editModuleModalOpen = useAtomValue(editModuleModalOpenAtom)\r\n  const moduleToDelete = useAtomValue(moduleToDeleteAtom)\r\n\r\n  const {\r\n    data: project,\r\n    error: projectError,\r\n    isLoading: projectIsLoading,\r\n  } = useProject({\r\n    session,\r\n    projectId,\r\n    initialData: initialDataProject,\r\n  })\r\n\r\n  const {\r\n    data: module,\r\n    error: moduleError,\r\n    isLoading: moduleIsLoading,\r\n  } = useModule({\r\n    session,\r\n    moduleId,\r\n    projectId,\r\n    initialData: initialDataModule,\r\n  })\r\n\r\n  if (projectIsLoading || moduleIsLoading) {\r\n    return (\r\n      <div className=\"p-4 md:p-6\">\r\n        <HeaderSkeleton />\r\n        <Skeleton className=\"mt-4 h-20 w-full\" />\r\n        <Skeleton className=\"mt-4 h-20 w-full\" />\r\n      </div>\r\n    )\r\n  }\r\n\r\n  if (!project) {\r\n    return (\r\n      <div className=\"p-4 md:p-6\">\r\n        <Alert variant=\"destructive\">\r\n          <AlertTitle>{t('project-not-found.title')}</AlertTitle>\r\n          <AlertDescription>\r\n            {t('project-not-found.description')}\r\n          </AlertDescription>\r\n        </Alert>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  if (!module) {\r\n    return (\r\n      <div className=\"p-4 md:p-6\">\r\n        <Alert variant=\"destructive\">\r\n          <AlertTitle>{t('module-not-found.title')}</AlertTitle>\r\n          <AlertDescription>\r\n            {t('module-not-found.description')}\r\n          </AlertDescription>\r\n        </Alert>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <>\r\n      {editModuleModalOpen && (\r\n        <EditModuleModal session={session} project={project} module={module} />\r\n      )}\r\n      {moduleToDelete && (\r\n        <DeleteModuleModal session={session} project={project} />\r\n      )}\r\n      {projectError && <ErrorAlert error={projectError} />}\r\n      {moduleError && <ErrorAlert error={moduleError} />}\r\n      <div className=\"relative flex flex-col-reverse gap-8 p-4 xl:flex-row md:p-6\">\r\n        <ModuleParamsSection\r\n          module={module}\r\n          projectId={projectId}\r\n          session={session}\r\n        />\r\n        <div className=\"xl:sticky xl:top-6 xl:self-start shrink-1\">\r\n          <ModuleDetailHeader project={project} module={module} />\r\n        </div>\r\n      </div>\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AACA;AACA;AAEA;AApBA;;;;;;;;;;;;;;;AA8BO,MAAM,0BAA0B,IAAA,gJAAI,EAAU;AAC9C,MAAM,qBAAqB,IAAA,gJAAI,EAAqB;AAEpD,MAAM,sBAAsB,CAAC,EAClC,OAAO,EACP,SAAS,EACT,kBAAkB,EAClB,QAAQ,EACR,iBAAiB,EACX;IACN,MAAM,IAAI,IAAA,yNAAe,EAAC;IAC1B,MAAM,sBAAsB,IAAA,sJAAY,EAAC;IACzC,MAAM,iBAAiB,IAAA,sJAAY,EAAC;IAEpC,MAAM,EACJ,MAAM,OAAO,EACb,OAAO,YAAY,EACnB,WAAW,gBAAgB,EAC5B,GAAG,IAAA,gKAAU,EAAC;QACb;QACA;QACA,aAAa;IACf;IAEA,MAAM,EACJ,MAAM,MAAM,EACZ,OAAO,WAAW,EAClB,WAAW,eAAe,EAC3B,GAAG,IAAA,6JAAS,EAAC;QACZ;QACA;QACA;QACA,aAAa;IACf;IAEA,IAAI,oBAAoB,iBAAiB;QACvC,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,6KAAc;;;;;8BACf,8OAAC,gJAAQ;oBAAC,WAAU;;;;;;8BACpB,8OAAC,gJAAQ;oBAAC,WAAU;;;;;;;;;;;;IAG1B;IAEA,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0IAAK;gBAAC,SAAQ;;kCACb,8OAAC,+IAAU;kCAAE,EAAE;;;;;;kCACf,8OAAC,qJAAgB;kCACd,EAAE;;;;;;;;;;;;;;;;;IAKb;IAEA,IAAI,CAAC,QAAQ;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0IAAK;gBAAC,SAAQ;;kCACb,8OAAC,+IAAU;kCAAE,EAAE;;;;;;kCACf,8OAAC,qJAAgB;kCACd,EAAE;;;;;;;;;;;;;;;;;IAKb;IAEA,qBACE;;YACG,qCACC,8OAAC,uMAAe;gBAAC,SAAS;gBAAS,SAAS;gBAAS,QAAQ;;;;;;YAE9D,gCACC,8OAAC,2MAAiB;gBAAC,SAAS;gBAAS,SAAS;;;;;;YAE/C,8BAAgB,8OAAC,qKAAU;gBAAC,OAAO;;;;;;YACnC,6BAAe,8OAAC,qKAAU;gBAAC,OAAO;;;;;;0BACnC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,gNAAmB;wBAClB,QAAQ;wBACR,WAAW;wBACX,SAAS;;;;;;kCAEX,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6MAAkB;4BAAC,SAAS;4BAAS,QAAQ;;;;;;;;;;;;;;;;;;;AAKxD", "debugId": null}}]}