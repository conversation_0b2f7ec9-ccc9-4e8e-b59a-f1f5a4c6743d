import { Badge } from '@atlas/components/ui/badge'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@atlas/components/ui/card'
import { cn } from '@atlas/lib/utils'
import type { ReinforcedSectionShearSchema } from '@atlas/types/schemas/masonry/maschi-murari-form'
import { useLocale, useTranslations } from 'next-intl'

type Props = {
  reinforcedSectionShearResults?: ReinforcedSectionShearSchema
}

export function ShearReinforcedSectionResultCard({
  reinforcedSectionShearResults,
}: Props) {
  const {
    shearResistanceReinforcementContribution,
    totalShearResistance,
    firstCheck,
    shearResistanceFromMasonryDiagonalCompression,
    secondCheck,
  } = reinforcedSectionShearResults || {}

  const t = useTranslations(
    'components.calculations.maschi-murari.shearReinforcementCalculationResult.reinforcedSectionResult',
  )

  const locale = useLocale()

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('title')}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <span className="font-medium">
            {t('shearResistanceReinforcementContribution.label')}:
          </span>{' '}
          <span>
            {shearResistanceReinforcementContribution?.toLocaleString(locale, {
              maximumFractionDigits: 3,
            })}{' '}
          </span>
        </div>
        <div>
          <span className="font-medium">
            {t('totalShearResistance.label')}:
          </span>{' '}
          <span>
            {totalShearResistance?.toLocaleString(locale, {
              maximumFractionDigits: 3,
            })}{' '}
          </span>
        </div>
        <span className="font-medium">{t('firstCheck.label')}:</span>{' '}
        <Badge
          className={cn(
            'text-base px-3 py-1',
            firstCheck ? 'bg-green-600' : 'bg-red-600',
          )}
        >
          {firstCheck
            ? t('firstCheck.satisfied')
            : t('firstCheck.notSatisfied')}
        </Badge>
        <div>
          <span className="font-medium">
            {t('shearResistanceFromMasonryDiagonalCompression.label')}:
          </span>{' '}
          <span>
            {shearResistanceFromMasonryDiagonalCompression?.toLocaleString(
              locale,
              {
                maximumFractionDigits: 3,
              },
            )}{' '}
          </span>
        </div>
        <span className="font-medium">{t('secondCheck.label')}:</span>{' '}
        <Badge
          className={cn(
            'text-base px-3 py-1',
            secondCheck ? 'bg-green-600' : 'bg-red-600',
          )}
        >
          {secondCheck
            ? t('secondCheck.satisfied')
            : t('secondCheck.notSatisfied')}
        </Badge>
      </CardContent>
    </Card>
  )
}
