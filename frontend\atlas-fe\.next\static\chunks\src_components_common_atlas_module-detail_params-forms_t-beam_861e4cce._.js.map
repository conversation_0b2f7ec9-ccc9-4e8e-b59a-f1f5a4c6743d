{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-general-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { MODULE_POLARITY, modulePolarity } from '@atlas/constants/module'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type TBeamGeneralFormInputs,\r\n  tBeamGeneralSchema,\r\n} from '@atlas/types/schemas/t-beam-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<TBeamGeneralFormInputs>\r\n  onSave: () => void\r\n}\r\n\r\nexport const TBeamGeneralForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.t-beam.general')\r\n  const tPolarity = useTranslations('forms.common.polarity')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n  const form = useForm<TBeamGeneralFormInputs>({\r\n    resolver: zodResolver(tBeamGeneralSchema),\r\n    defaultValues: {\r\n      initialDeformation: defaultValues?.initialDeformation ?? 0,\r\n      polarity: defaultValues?.polarity ?? modulePolarity.POSITIVE,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (body: TBeamGeneralFormInputs) => {\r\n      mutate({ projectId, moduleId, body })\r\n    },\r\n    [mutate, projectId, moduleId],\r\n  )\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"polarity\"\r\n          options={MODULE_POLARITY}\r\n          optionLabelFn={p => tPolarity(p)}\r\n          t={t}\r\n          description\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"initialDeformation\"\r\n          t={t}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAGA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AAUO,MAAM,mBAAmB;QAAC,EAC/B,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACA;;IACN,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,YAAY,IAAA,4NAAe,EAAC;IAClC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,cAAc,IAAA,2MAAc;QAIV,mCACV;IAJd,MAAM,OAAO,IAAA,4KAAO,EAAyB;QAC3C,UAAU,IAAA,gLAAW,EAAC,qKAAkB;QACxC,eAAe;YACb,oBAAoB,CAAA,oCAAA,0BAAA,oCAAA,cAAe,kBAAkB,cAAjC,+CAAA,oCAAqC;YACzD,UAAU,CAAA,0BAAA,0BAAA,oCAAA,cAAe,QAAQ,cAAvB,qCAAA,0BAA2B,gJAAc,CAAC,QAAQ;QAC9D;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,mNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,SAAS;4DAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;gBACtB,MAAM,YAAY,iBAAiB,CAAC;oBAClC,UAAU;wBAAC;wBAAW;4BAAE;4BAAW;wBAAS;qBAAE;gBAChD;gBACA;YACF;;QACA,OAAO;4DAAE,CAAA;gBACP,oJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAC7D;;IACF;IAGF,MAAM,mBAAmB,IAAA,4KAAW;0DAClC,CAAC;YACC,OAAO;gBAAE;gBAAW;gBAAU;YAAK;QACrC;yDACA;QAAC;QAAQ;QAAW;KAAS;IAG/B,qBACE,6LAAC,2IAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,6LAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,iJAAe;oBACxB,eAAe,CAAA,IAAK,UAAU;oBAC9B,GAAG;oBACH,WAAW;;;;;;8BAEb,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,6LAAC,+IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,6LAAC,uOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB;GA1Ea;;QAOD,4NAAe;QACP,4NAAe;QACjB,4NAAe;QACf,4NAAe;QACX,2MAAc;QACrB,4KAAO;QAQU,mNAA2B;;;KApB9C", "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-geometry-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { PolarityAwareNumberFormInput } from '@atlas/components/common/form/polarity-aware-number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport {\r\n  MODULE_GEOMETRY_EXPOSURE,\r\n  type ModulePolarity,\r\n  moduleGeometryExposure,\r\n} from '@atlas/constants/module'\r\nimport {\r\n  calculateTBeamEffectiveDepth,\r\n  calculateTBeamTotalHeight,\r\n} from '@atlas/functions/forms/t-beam-form-calculations'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type TBeamGeometryFormInputs as FormSchema,\r\n  tBeamGeometrySchema,\r\n} from '@atlas/types/schemas/t-beam-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FormSchema>\r\n  polarity?: ModulePolarity\r\n  onSave: () => void\r\n}\r\n\r\nexport const TBeamGeometryForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  polarity,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.t-beam.geometry')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n  const form = useForm<FormSchema>({\r\n    resolver: zodResolver(tBeamGeometrySchema),\r\n    defaultValues: {\r\n      primaryHeight: defaultValues?.primaryHeight,\r\n      primaryWidth: defaultValues?.primaryWidth,\r\n      secondaryWidth: defaultValues?.secondaryWidth,\r\n      secondaryHeight: defaultValues?.secondaryHeight,\r\n      concreteCover1: defaultValues?.concreteCover1,\r\n      concreteCover2: defaultValues?.concreteCover2,\r\n      totalHeight: defaultValues?.totalHeight,\r\n      effectiveDepth: defaultValues?.effectiveDepth,\r\n      exposure: defaultValues?.exposure ?? moduleGeometryExposure.INTERNAL,\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (geometry: FormSchema) => {\r\n      mutate({ projectId, moduleId, body: { geometry } })\r\n    },\r\n    [mutate, projectId, moduleId],\r\n  )\r\n\r\n  const [\r\n    _primaryWidth,\r\n    primaryHeight,\r\n    secondaryHeight,\r\n    _concreteCover2,\r\n    concreteCover1,\r\n  ] = form.watch([\r\n    'primaryWidth',\r\n    'primaryHeight',\r\n    'secondaryHeight',\r\n    'concreteCover2',\r\n    'concreteCover1',\r\n  ])\r\n\r\n  // Calculate total height when web height and flange thickness change\r\n  useEffect(() => {\r\n    if (primaryHeight && secondaryHeight) {\r\n      form.setValue(\r\n        'totalHeight',\r\n        calculateTBeamTotalHeight(primaryHeight, secondaryHeight),\r\n      )\r\n    }\r\n  }, [primaryHeight, secondaryHeight, form])\r\n\r\n  // Calculate effective depth based on polarity\r\n  useEffect(() => {\r\n    const totalHeight = calculateTBeamTotalHeight(\r\n      primaryHeight || 0,\r\n      secondaryHeight || 0,\r\n    )\r\n    if (totalHeight && concreteCover1) {\r\n      form.setValue(\r\n        'effectiveDepth',\r\n        calculateTBeamEffectiveDepth(totalHeight, concreteCover1),\r\n      )\r\n    }\r\n  }, [primaryHeight, secondaryHeight, concreteCover1, form])\r\n\r\n  return (\r\n    <div className=\"flex flex-col justify-center gap-2\">\r\n      <Image\r\n        src={\r\n          polarity === 'NEGATIVE'\r\n            ? '/assets/t_beam/T_beam_geometry_M-.png'\r\n            : '/assets/t_beam/T_beam_geometry_M+.png'\r\n        }\r\n        alt=\"T-beam geometry\"\r\n        height={500}\r\n        width={500}\r\n        className=\"mx-auto rounded-md object-contain\"\r\n        priority\r\n      />\r\n      <Form {...form}>\r\n        <form\r\n          className=\"space-y-4 rounded-md border p-4 grow\"\r\n          onSubmit={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          <PolarityAwareNumberFormInput\r\n            control={form.control}\r\n            name=\"primaryHeight\"\r\n            t={t}\r\n            polarity={polarity}\r\n          />\r\n          <PolarityAwareNumberFormInput\r\n            control={form.control}\r\n            name=\"primaryWidth\"\r\n            t={t}\r\n            polarity={polarity}\r\n          />\r\n          <PolarityAwareNumberFormInput\r\n            control={form.control}\r\n            name=\"secondaryWidth\"\r\n            t={t}\r\n            polarity={polarity}\r\n          />\r\n          <PolarityAwareNumberFormInput\r\n            control={form.control}\r\n            name=\"secondaryHeight\"\r\n            t={t}\r\n            polarity={polarity}\r\n          />\r\n          <PolarityAwareNumberFormInput\r\n            control={form.control}\r\n            name=\"concreteCover1\"\r\n            t={t}\r\n            polarity={polarity}\r\n          />\r\n          <PolarityAwareNumberFormInput\r\n            control={form.control}\r\n            name=\"concreteCover2\"\r\n            t={t}\r\n            polarity={polarity}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"totalHeight\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"effectiveDepth\"\r\n            t={t}\r\n            disabled={true}\r\n          />\r\n          <SelectFormFixedInput\r\n            control={form.control}\r\n            name=\"exposure\"\r\n            options={MODULE_GEOMETRY_EXPOSURE}\r\n            optionLabelFn={p => t(`exposure.${p}`)}\r\n            t={t}\r\n          />\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full sm:w-auto\"\r\n            disabled={isPending}\r\n            onClick={form.handleSubmit(handleFormSubmit)}\r\n          >\r\n            {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n            {tCommon('save')}\r\n          </Button>\r\n        </form>\r\n      </Form>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAKA;AAMA;AACA;AAIA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;AAWO,MAAM,oBAAoB;QAAC,EAChC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,QAAQ,EACR,MAAM,EACA;;IACN,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,cAAc,IAAA,2MAAc;QAYpB;IAXd,MAAM,OAAO,IAAA,4KAAO,EAAa;QAC/B,UAAU,IAAA,gLAAW,EAAC,sKAAmB;QACzC,eAAe;YACb,aAAa,EAAE,0BAAA,oCAAA,cAAe,aAAa;YAC3C,YAAY,EAAE,0BAAA,oCAAA,cAAe,YAAY;YACzC,cAAc,EAAE,0BAAA,oCAAA,cAAe,cAAc;YAC7C,eAAe,EAAE,0BAAA,oCAAA,cAAe,eAAe;YAC/C,cAAc,EAAE,0BAAA,oCAAA,cAAe,cAAc;YAC7C,cAAc,EAAE,0BAAA,oCAAA,cAAe,cAAc;YAC7C,WAAW,EAAE,0BAAA,oCAAA,cAAe,WAAW;YACvC,cAAc,EAAE,0BAAA,oCAAA,cAAe,cAAc;YAC7C,UAAU,CAAA,0BAAA,0BAAA,oCAAA,cAAe,QAAQ,cAAvB,qCAAA,0BAA2B,wJAAsB,CAAC,QAAQ;QACtE;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,mNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,SAAS;6DAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;gBACtB,MAAM,YAAY,iBAAiB,CAAC;oBAClC,UAAU;wBAAC;wBAAW;4BAAE;4BAAW;wBAAS;qBAAE;gBAChD;gBACA;YACF;;QACA,OAAO;6DAAE,CAAA;gBACP,oJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAC7D;;IACF;IAGF,MAAM,mBAAmB,IAAA,4KAAW;2DAClC,CAAC;YACC,OAAO;gBAAE;gBAAW;gBAAU,MAAM;oBAAE;gBAAS;YAAE;QACnD;0DACA;QAAC;QAAQ;QAAW;KAAS;IAG/B,MAAM,CACJ,eACA,eACA,iBACA,iBACA,eACD,GAAG,KAAK,KAAK,CAAC;QACb;QACA;QACA;QACA;QACA;KACD;IAED,qEAAqE;IACrE,IAAA,0KAAS;uCAAC;YACR,IAAI,iBAAiB,iBAAiB;gBACpC,KAAK,QAAQ,CACX,eACA,IAAA,8LAAyB,EAAC,eAAe;YAE7C;QACF;sCAAG;QAAC;QAAe;QAAiB;KAAK;IAEzC,8CAA8C;IAC9C,IAAA,0KAAS;uCAAC;YACR,MAAM,cAAc,IAAA,8LAAyB,EAC3C,iBAAiB,GACjB,mBAAmB;YAErB,IAAI,eAAe,gBAAgB;gBACjC,KAAK,QAAQ,CACX,kBACA,IAAA,iMAA4B,EAAC,aAAa;YAE9C;QACF;sCAAG;QAAC;QAAe;QAAiB;QAAgB;KAAK;IAEzD,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,2IAAK;gBACJ,KACE,aAAa,aACT,0CACA;gBAEN,KAAI;gBACJ,QAAQ;gBACR,OAAO;gBACP,WAAU;gBACV,QAAQ;;;;;;0BAEV,6LAAC,2IAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,6LAAC;oBACC,WAAU;oBACV,UAAU,KAAK,YAAY,CAAC;;sCAE5B,6LAAC,uNAA4B;4BAC3B,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,uNAA4B;4BAC3B,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,uNAA4B;4BAC3B,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,uNAA4B;4BAC3B,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,uNAA4B;4BAC3B,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,uNAA4B;4BAC3B,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;4BACH,UAAU;;;;;;sCAEZ,6LAAC,mMAAoB;4BACnB,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS,0JAAwB;4BACjC,eAAe,CAAA,IAAK,EAAE,AAAC,YAAa,OAAF;4BAClC,GAAG;;;;;;sCAEL,6LAAC,+IAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU;4BACV,SAAS,KAAK,YAAY,CAAC;;gCAE1B,2BAAa,6LAAC,uOAAW;oCAAC,WAAU;;;;;;gCACpC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;AAMrB;GA/Ka;;QAQD,4NAAe;QACT,4NAAe;QACf,4NAAe;QACX,2MAAc;QACrB,4KAAO;QAeU,mNAA2B;;;KA3B9C", "debugId": null}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-concrete-class-form-section.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { StringFormInput } from '@atlas/components/common/form/string-form-input'\r\nimport { moduleMaterialKnowledgeLevelValues } from '@atlas/constants/module'\r\nimport {\r\n  calculateTBeamDesignStrengthForBrittleMechanisms,\r\n  calculateTBeamDesignStrengthForDuctileMechanisms,\r\n} from '@atlas/functions/forms/t-beam-form-calculations'\r\nimport { useReinforcedConcreteMaterials } from '@atlas/lib/query/materials/use-reinforced-concrete-materials'\r\nimport type { TBeamMaterialFormInputs } from '@atlas/types/schemas/t-beam-form'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useMemo } from 'react'\r\nimport { useFormContext } from 'react-hook-form'\r\n\r\ntype Props = {\r\n  session: Session\r\n}\r\n\r\nexport function TBeamConcreteClassFormSection({ session }: Props) {\r\n  const t = useTranslations('forms.project-params.t-beam.materialProperties')\r\n\r\n  const form = useFormContext<TBeamMaterialFormInputs>()\r\n\r\n  const {\r\n    data: concreteMaterials,\r\n    isError: concreteMaterialsError,\r\n    isLoading: concreteMaterialsLoading,\r\n  } = useReinforcedConcreteMaterials({ session, body: { size: 100 } })\r\n\r\n  const customOption = { value: 'custom', label: t('concreteClass.custom') }\r\n\r\n  const concreteMaterialsOptions = [\r\n    ...(concreteMaterials?.content.map(m => ({\r\n      value: m.id,\r\n      label: m.name,\r\n    })) ?? []),\r\n    customOption,\r\n  ]\r\n\r\n  const knowledgeLevel = form.watch('knowledgeLevel')\r\n  const concreteClassId = form.watch('concreteClass.id')\r\n  const averageCompressiveStrength = form.watch(\r\n    'concreteClass.averageCompressiveStrength',\r\n  )\r\n\r\n  const isCustomSelected = concreteClassId === 'custom'\r\n\r\n  const selectedConcreteMaterial = useMemo(\r\n    () => concreteMaterials?.content.find(m => m.id === concreteClassId),\r\n    [concreteClassId, concreteMaterials],\r\n  )\r\n\r\n  useEffect(() => {\r\n    if (!selectedConcreteMaterial || isCustomSelected) {\r\n      return\r\n    }\r\n    const selected = selectedConcreteMaterial\r\n    if (form.getValues('concreteClass.name') !== selected.name) {\r\n      form.setValue('concreteClass.name', selected.name)\r\n    }\r\n    if (\r\n      form.getValues('concreteClass.cubeCompressiveStrength') !==\r\n      selected.cubeCompressiveStrength\r\n    ) {\r\n      form.setValue(\r\n        'concreteClass.cubeCompressiveStrength',\r\n        selected.cubeCompressiveStrength,\r\n      )\r\n    }\r\n    if (\r\n      form.getValues('concreteClass.cylinderCompressiveStrength') !==\r\n      selected.cylinderCompressiveStrength\r\n    ) {\r\n      form.setValue(\r\n        'concreteClass.cylinderCompressiveStrength',\r\n        selected.cylinderCompressiveStrength,\r\n      )\r\n    }\r\n    if (\r\n      form.getValues('concreteClass.averageCompressiveStrength') !==\r\n      selected.averageCompressiveStrength\r\n    ) {\r\n      form.setValue(\r\n        'concreteClass.averageCompressiveStrength',\r\n        selected.averageCompressiveStrength,\r\n      )\r\n    }\r\n    if (\r\n      form.getValues('concreteClass.averageTensileStrength') !==\r\n      selected.averageTensileStrength\r\n    ) {\r\n      form.setValue(\r\n        'concreteClass.averageTensileStrength',\r\n        selected.averageTensileStrength,\r\n      )\r\n    }\r\n    if (\r\n      form.getValues('concreteClass.elasticModulus') !== selected.elasticModulus\r\n    ) {\r\n      form.setValue('concreteClass.elasticModulus', selected.elasticModulus)\r\n    }\r\n  }, [form, isCustomSelected, selectedConcreteMaterial])\r\n\r\n  useEffect(() => {\r\n    if (\r\n      knowledgeLevel &&\r\n      averageCompressiveStrength !== undefined &&\r\n      averageCompressiveStrength !== null\r\n    ) {\r\n      const confidenceFactor =\r\n        moduleMaterialKnowledgeLevelValues[knowledgeLevel]\r\n      form.setValue(\r\n        'concreteClass.designCompressiveStrengthForBrittleMechanisms',\r\n        calculateTBeamDesignStrengthForBrittleMechanisms(\r\n          averageCompressiveStrength,\r\n          confidenceFactor,\r\n        ),\r\n      )\r\n      form.setValue(\r\n        'concreteClass.designCompressiveStrengthForDuctileMechanisms',\r\n        calculateTBeamDesignStrengthForDuctileMechanisms(\r\n          averageCompressiveStrength,\r\n          confidenceFactor,\r\n        ),\r\n      )\r\n    }\r\n  }, [knowledgeLevel, averageCompressiveStrength, form])\r\n\r\n  return (\r\n    <>\r\n      <SelectFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.id\"\r\n        options={concreteMaterialsOptions}\r\n        t={t}\r\n        loading={concreteMaterialsLoading}\r\n        requestError={concreteMaterialsError}\r\n        errorMessage={t('concreteClass.error')}\r\n      />\r\n      {isCustomSelected && (\r\n        <StringFormInput\r\n          control={form.control}\r\n          name=\"concreteClass.name\"\r\n          t={t}\r\n        />\r\n      )}\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.cubeCompressiveStrength\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.cylinderCompressiveStrength\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.averageCompressiveStrength\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.averageTensileStrength\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.elasticModulus\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.designCompressiveStrengthForDuctileMechanisms\"\r\n        t={t}\r\n        disabled={true}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"concreteClass.designCompressiveStrengthForBrittleMechanisms\"\r\n        t={t}\r\n        disabled={true}\r\n      />\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAIA;AAGA;AACA;AACA;;;;;;;;;;;;AAMO,SAAS,8BAA8B,KAAkB;QAAlB,EAAE,OAAO,EAAS,GAAlB;;IAC5C,MAAM,IAAI,IAAA,4NAAe,EAAC;IAE1B,MAAM,OAAO,IAAA,mLAAc;IAE3B,MAAM,EACJ,MAAM,iBAAiB,EACvB,SAAS,sBAAsB,EAC/B,WAAW,wBAAwB,EACpC,GAAG,IAAA,mNAA8B,EAAC;QAAE;QAAS,MAAM;YAAE,MAAM;QAAI;IAAE;IAElE,MAAM,eAAe;QAAE,OAAO;QAAU,OAAO,EAAE;IAAwB;QAGnE;IADN,MAAM,2BAA2B;WAC3B,CAAA,iCAAA,8BAAA,wCAAA,kBAAmB,OAAO,CAAC,GAAG,CAAC,CAAA,IAAK,CAAC;gBACvC,OAAO,EAAE,EAAE;gBACX,OAAO,EAAE,IAAI;YACf,CAAC,gBAHG,4CAAA,iCAGG,EAAE;QACT;KACD;IAED,MAAM,iBAAiB,KAAK,KAAK,CAAC;IAClC,MAAM,kBAAkB,KAAK,KAAK,CAAC;IACnC,MAAM,6BAA6B,KAAK,KAAK,CAC3C;IAGF,MAAM,mBAAmB,oBAAoB;IAE7C,MAAM,2BAA2B,IAAA,wKAAO;2EACtC,IAAM,8BAAA,wCAAA,kBAAmB,OAAO,CAAC,IAAI;mFAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;0EACpD;QAAC;QAAiB;KAAkB;IAGtC,IAAA,0KAAS;mDAAC;YACR,IAAI,CAAC,4BAA4B,kBAAkB;gBACjD;YACF;YACA,MAAM,WAAW;YACjB,IAAI,KAAK,SAAS,CAAC,0BAA0B,SAAS,IAAI,EAAE;gBAC1D,KAAK,QAAQ,CAAC,sBAAsB,SAAS,IAAI;YACnD;YACA,IACE,KAAK,SAAS,CAAC,6CACf,SAAS,uBAAuB,EAChC;gBACA,KAAK,QAAQ,CACX,yCACA,SAAS,uBAAuB;YAEpC;YACA,IACE,KAAK,SAAS,CAAC,iDACf,SAAS,2BAA2B,EACpC;gBACA,KAAK,QAAQ,CACX,6CACA,SAAS,2BAA2B;YAExC;YACA,IACE,KAAK,SAAS,CAAC,gDACf,SAAS,0BAA0B,EACnC;gBACA,KAAK,QAAQ,CACX,4CACA,SAAS,0BAA0B;YAEvC;YACA,IACE,KAAK,SAAS,CAAC,4CACf,SAAS,sBAAsB,EAC/B;gBACA,KAAK,QAAQ,CACX,wCACA,SAAS,sBAAsB;YAEnC;YACA,IACE,KAAK,SAAS,CAAC,oCAAoC,SAAS,cAAc,EAC1E;gBACA,KAAK,QAAQ,CAAC,gCAAgC,SAAS,cAAc;YACvE;QACF;kDAAG;QAAC;QAAM;QAAkB;KAAyB;IAErD,IAAA,0KAAS;mDAAC;YACR,IACE,kBACA,+BAA+B,aAC/B,+BAA+B,MAC/B;gBACA,MAAM,mBACJ,oKAAkC,CAAC,eAAe;gBACpD,KAAK,QAAQ,CACX,+DACA,IAAA,qNAAgD,EAC9C,4BACA;gBAGJ,KAAK,QAAQ,CACX,+DACA,IAAA,qNAAgD,EAC9C,4BACA;YAGN;QACF;kDAAG;QAAC;QAAgB;QAA4B;KAAK;IAErD,qBACE;;0BACE,6LAAC,qLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,SAAS;gBACT,GAAG;gBACH,SAAS;gBACT,cAAc;gBACd,cAAc,EAAE;;;;;;YAEjB,kCACC,6LAAC,qLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;;;;;;0BAGP,6LAAC,qLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,6LAAC,qLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,6LAAC,qLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,6LAAC,qLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,6LAAC,qLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,6LAAC,qLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU;;;;;;0BAEZ,6LAAC,qLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU;;;;;;;;AAIlB;GA5KgB;;QACJ,4NAAe;QAEZ,mLAAc;QAMvB,mNAA8B;;;KATpB", "debugId": null}}, {"offset": {"line": 694, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-steel-grade-form-section.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { StringFormInput } from '@atlas/components/common/form/string-form-input'\r\nimport { moduleMaterialKnowledgeLevelValues } from '@atlas/constants/module'\r\nimport {\r\n  calculateTBeamDesignYieldStrengthForBrittleMechanisms,\r\n  calculateTBeamDesignYieldStrengthForDuctileMechanisms,\r\n} from '@atlas/functions/forms/t-beam-form-calculations'\r\nimport { useSteelGradesMaterials } from '@atlas/lib/query/materials/use-steel-grades-materials'\r\nimport type { TBeamMaterialFormInputs } from '@atlas/types/schemas/t-beam-form'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useMemo } from 'react'\r\nimport { useFormContext } from 'react-hook-form'\r\n\r\nexport function TBeamSteelGradeFormSection({ session }: { session: Session }) {\r\n  const t = useTranslations('forms.project-params.t-beam.materialProperties')\r\n  const form = useFormContext<TBeamMaterialFormInputs>()\r\n\r\n  const {\r\n    data: steelMaterials,\r\n    isError: steelMaterialsError,\r\n    isLoading: steelMaterialsLoading,\r\n  } = useSteelGradesMaterials({ session, body: { size: 100 } })\r\n\r\n  const customOption = { value: 'custom', label: t('steelGrade.custom') }\r\n  const steelMaterialsOptions = [\r\n    ...(steelMaterials?.content.map(m => ({ value: m.id, label: m.name })) ??\r\n      []),\r\n    customOption,\r\n  ]\r\n\r\n  const knowledgeLevel = form.watch('knowledgeLevel')\r\n  const steelGradeId = form.watch('steelGrade.id')\r\n  const yieldStrength = form.watch('steelGrade.yieldStrength')\r\n\r\n  const isCustomSelected = steelGradeId === 'custom'\r\n\r\n  const selectedSteelMaterial = useMemo(\r\n    () => steelMaterials?.content.find(m => m.id === steelGradeId),\r\n    [steelGradeId, steelMaterials],\r\n  )\r\n\r\n  useEffect(() => {\r\n    if (!selectedSteelMaterial || isCustomSelected) {\r\n      return\r\n    }\r\n    const selected = selectedSteelMaterial\r\n    if (form.getValues('steelGrade.name') !== selected.name) {\r\n      form.setValue('steelGrade.name', selected.name)\r\n    }\r\n    if (form.getValues('steelGrade.yieldStrength') !== selected.yieldStrength) {\r\n      form.setValue('steelGrade.yieldStrength', selected.yieldStrength)\r\n    }\r\n    if (\r\n      form.getValues('steelGrade.tensileStrength') !== selected.tensileStrength\r\n    ) {\r\n      form.setValue('steelGrade.tensileStrength', selected.tensileStrength)\r\n    }\r\n    if (\r\n      form.getValues('steelGrade.elongationPercentage') !==\r\n      selected.elongationPercentage\r\n    ) {\r\n      form.setValue(\r\n        'steelGrade.elongationPercentage',\r\n        selected.elongationPercentage,\r\n      )\r\n    }\r\n    if (\r\n      form.getValues('steelGrade.elasticModulus') !== selected.elasticModulus\r\n    ) {\r\n      form.setValue('steelGrade.elasticModulus', selected.elasticModulus)\r\n    }\r\n  }, [form, isCustomSelected, selectedSteelMaterial])\r\n\r\n  useEffect(() => {\r\n    if (\r\n      knowledgeLevel &&\r\n      yieldStrength !== undefined &&\r\n      yieldStrength !== null\r\n    ) {\r\n      const confidenceFactor =\r\n        moduleMaterialKnowledgeLevelValues[knowledgeLevel]\r\n      form.setValue(\r\n        'steelGrade.designYieldStrengthForDuctileMechanisms',\r\n        calculateTBeamDesignYieldStrengthForDuctileMechanisms(\r\n          yieldStrength,\r\n          confidenceFactor,\r\n        ),\r\n      )\r\n      form.setValue(\r\n        'steelGrade.designYieldStrengthForBrittleMechanisms',\r\n        calculateTBeamDesignYieldStrengthForBrittleMechanisms(\r\n          yieldStrength,\r\n          confidenceFactor,\r\n        ),\r\n      )\r\n    }\r\n  }, [knowledgeLevel, yieldStrength, form])\r\n\r\n  return (\r\n    <>\r\n      <SelectFormInput\r\n        control={form.control}\r\n        name=\"steelGrade.id\"\r\n        options={steelMaterialsOptions}\r\n        t={t}\r\n        loading={steelMaterialsLoading}\r\n        requestError={steelMaterialsError}\r\n        errorMessage={t('steelGrade.error')}\r\n      />\r\n      {isCustomSelected && (\r\n        <StringFormInput control={form.control} name=\"steelGrade.name\" t={t} />\r\n      )}\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"steelGrade.yieldStrength\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"steelGrade.elasticModulus\"\r\n        t={t}\r\n        disabled={!isCustomSelected}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"steelGrade.designYieldStrengthForDuctileMechanisms\"\r\n        t={t}\r\n        disabled={true}\r\n      />\r\n      <NumberFormInput\r\n        control={form.control}\r\n        name=\"steelGrade.designYieldStrengthForBrittleMechanisms\"\r\n        t={t}\r\n        disabled={true}\r\n      />\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAIA;AAGA;AACA;AACA;;;;;;;;;;;;AAEO,SAAS,2BAA2B,KAAiC;QAAjC,EAAE,OAAO,EAAwB,GAAjC;;IACzC,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,OAAO,IAAA,mLAAc;IAE3B,MAAM,EACJ,MAAM,cAAc,EACpB,SAAS,mBAAmB,EAC5B,WAAW,qBAAqB,EACjC,GAAG,IAAA,qMAAuB,EAAC;QAAE;QAAS,MAAM;YAAE,MAAM;QAAI;IAAE;IAE3D,MAAM,eAAe;QAAE,OAAO;QAAU,OAAO,EAAE;IAAqB;QAEhE;IADN,MAAM,wBAAwB;WACxB,CAAA,8BAAA,2BAAA,qCAAA,eAAgB,OAAO,CAAC,GAAG,CAAC,CAAA,IAAK,CAAC;gBAAE,OAAO,EAAE,EAAE;gBAAE,OAAO,EAAE,IAAI;YAAC,CAAC,gBAAhE,yCAAA,8BACF,EAAE;QACJ;KACD;IAED,MAAM,iBAAiB,KAAK,KAAK,CAAC;IAClC,MAAM,eAAe,KAAK,KAAK,CAAC;IAChC,MAAM,gBAAgB,KAAK,KAAK,CAAC;IAEjC,MAAM,mBAAmB,iBAAiB;IAE1C,MAAM,wBAAwB,IAAA,wKAAO;qEACnC,IAAM,2BAAA,qCAAA,eAAgB,OAAO,CAAC,IAAI;6EAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;oEACjD;QAAC;QAAc;KAAe;IAGhC,IAAA,0KAAS;gDAAC;YACR,IAAI,CAAC,yBAAyB,kBAAkB;gBAC9C;YACF;YACA,MAAM,WAAW;YACjB,IAAI,KAAK,SAAS,CAAC,uBAAuB,SAAS,IAAI,EAAE;gBACvD,KAAK,QAAQ,CAAC,mBAAmB,SAAS,IAAI;YAChD;YACA,IAAI,KAAK,SAAS,CAAC,gCAAgC,SAAS,aAAa,EAAE;gBACzE,KAAK,QAAQ,CAAC,4BAA4B,SAAS,aAAa;YAClE;YACA,IACE,KAAK,SAAS,CAAC,kCAAkC,SAAS,eAAe,EACzE;gBACA,KAAK,QAAQ,CAAC,8BAA8B,SAAS,eAAe;YACtE;YACA,IACE,KAAK,SAAS,CAAC,uCACf,SAAS,oBAAoB,EAC7B;gBACA,KAAK,QAAQ,CACX,mCACA,SAAS,oBAAoB;YAEjC;YACA,IACE,KAAK,SAAS,CAAC,iCAAiC,SAAS,cAAc,EACvE;gBACA,KAAK,QAAQ,CAAC,6BAA6B,SAAS,cAAc;YACpE;QACF;+CAAG;QAAC;QAAM;QAAkB;KAAsB;IAElD,IAAA,0KAAS;gDAAC;YACR,IACE,kBACA,kBAAkB,aAClB,kBAAkB,MAClB;gBACA,MAAM,mBACJ,oKAAkC,CAAC,eAAe;gBACpD,KAAK,QAAQ,CACX,sDACA,IAAA,0NAAqD,EACnD,eACA;gBAGJ,KAAK,QAAQ,CACX,sDACA,IAAA,0NAAqD,EACnD,eACA;YAGN;QACF;+CAAG;QAAC;QAAgB;QAAe;KAAK;IAExC,qBACE;;0BACE,6LAAC,qLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,SAAS;gBACT,GAAG;gBACH,SAAS;gBACT,cAAc;gBACd,cAAc,EAAE;;;;;;YAEjB,kCACC,6LAAC,qLAAe;gBAAC,SAAS,KAAK,OAAO;gBAAE,MAAK;gBAAkB,GAAG;;;;;;0BAEpE,6LAAC,qLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,6LAAC,qLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU,CAAC;;;;;;0BAEb,6LAAC,qLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU;;;;;;0BAEZ,6LAAC,qLAAe;gBACd,SAAS,KAAK,OAAO;gBACrB,MAAK;gBACL,GAAG;gBACH,UAAU;;;;;;;;AAIlB;GA7HgB;;QACJ,4NAAe;QACZ,mLAAc;QAMvB,qMAAuB;;;KARb", "debugId": null}}, {"offset": {"line": 878, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-material-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormFixedInput } from '@atlas/components/common/form/select-form-fixed-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport {\r\n  MODULE_MATERIAL_KNOWLEDGE_LEVEL,\r\n  moduleMaterialKnowledgeLevel,\r\n  moduleMaterialKnowledgeLevelValues,\r\n} from '@atlas/constants/module'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport {\r\n  type TBeamMaterialFormInputs as FormSchema,\r\n  tBeamMaterialSchema,\r\n} from '@atlas/types/schemas/t-beam-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\nimport { TBeamConcreteClassFormSection } from './t-beam-concrete-class-form-section'\r\nimport { TBeamSteelGradeFormSection } from './t-beam-steel-grade-form-section'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FormSchema>\r\n  onSave: () => void\r\n}\r\n\r\nexport const TBeamMaterialForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  onSave,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.t-beam.materialProperties')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n  const form = useForm<FormSchema>({\r\n    resolver: zodResolver(tBeamMaterialSchema),\r\n    defaultValues: {\r\n      knowledgeLevel:\r\n        defaultValues?.knowledgeLevel ?? moduleMaterialKnowledgeLevel.LC1,\r\n      confidenceFactor: defaultValues?.knowledgeLevel\r\n        ? moduleMaterialKnowledgeLevelValues[defaultValues?.knowledgeLevel]\r\n        : moduleMaterialKnowledgeLevelValues[moduleMaterialKnowledgeLevel.LC1],\r\n      concreteClass: {\r\n        id: defaultValues?.concreteClass?.id,\r\n        name: defaultValues?.concreteClass?.name,\r\n        cubeCompressiveStrength:\r\n          defaultValues?.concreteClass?.cubeCompressiveStrength,\r\n        cylinderCompressiveStrength:\r\n          defaultValues?.concreteClass?.cylinderCompressiveStrength,\r\n        averageCompressiveStrength:\r\n          defaultValues?.concreteClass?.averageCompressiveStrength,\r\n        averageTensileStrength:\r\n          defaultValues?.concreteClass?.averageTensileStrength,\r\n        elasticModulus: defaultValues?.concreteClass?.elasticModulus,\r\n        designCompressiveStrengthForBrittleMechanisms:\r\n          defaultValues?.concreteClass\r\n            ?.designCompressiveStrengthForBrittleMechanisms,\r\n        designCompressiveStrengthForDuctileMechanisms:\r\n          defaultValues?.concreteClass\r\n            ?.designCompressiveStrengthForDuctileMechanisms,\r\n      },\r\n      steelGrade: {\r\n        id: defaultValues?.steelGrade?.id,\r\n        name: defaultValues?.steelGrade?.name,\r\n        yieldStrength: defaultValues?.steelGrade?.yieldStrength,\r\n        tensileStrength: defaultValues?.steelGrade?.tensileStrength,\r\n        elongationPercentage: defaultValues?.steelGrade?.elongationPercentage,\r\n        elasticModulus: defaultValues?.steelGrade?.elasticModulus,\r\n        designYieldStrengthForBrittleMechanisms:\r\n          defaultValues?.steelGrade?.designYieldStrengthForBrittleMechanisms,\r\n        designYieldStrengthForDuctileMechanisms:\r\n          defaultValues?.steelGrade?.designYieldStrengthForDuctileMechanisms,\r\n      },\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (materialProperties: FormSchema) => {\r\n      mutate({ projectId, moduleId, body: { materialProperties } })\r\n    },\r\n    [mutate, projectId, moduleId],\r\n  )\r\n\r\n  const knowledgeLevel = form.watch('knowledgeLevel')\r\n\r\n  useEffect(() => {\r\n    if (knowledgeLevel) {\r\n      form.setValue(\r\n        'confidenceFactor',\r\n        moduleMaterialKnowledgeLevelValues[knowledgeLevel],\r\n      )\r\n    }\r\n  }, [knowledgeLevel, form])\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <SelectFormFixedInput\r\n          control={form.control}\r\n          name=\"knowledgeLevel\"\r\n          options={MODULE_MATERIAL_KNOWLEDGE_LEVEL}\r\n          optionLabelFn={p => t(`knowledgeLevel.${p}`)}\r\n          t={t}\r\n          description\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"confidenceFactor\"\r\n          t={t}\r\n          disabled={true}\r\n        />\r\n        <Separator />\r\n        <TBeamConcreteClassFormSection session={session} />\r\n        <Separator />\r\n        <TBeamSteelGradeFormSection session={session} />\r\n        <Button type=\"submit\" className=\"w-full sm:w-auto\" disabled={isPending}>\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AAOA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;AAUO,MAAM,oBAAoB;QAAC,EAChC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACA;QAcI,8BACE,+BAEJ,+BAEA,+BAEA,+BAEA,+BACc,+BAEd,+BAGA,+BAIE,2BACE,4BACS,4BACE,4BACK,4BACN,4BAEd,4BAEA;;IAzCR,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,cAAc,IAAA,2MAAc;QAK5B;IAJN,MAAM,OAAO,IAAA,4KAAO,EAAa;QAC/B,UAAU,IAAA,gLAAW,EAAC,sKAAmB;QACzC,eAAe;YACb,gBACE,CAAA,gCAAA,0BAAA,oCAAA,cAAe,cAAc,cAA7B,2CAAA,gCAAiC,8JAA4B,CAAC,GAAG;YACnE,kBAAkB,CAAA,0BAAA,oCAAA,cAAe,cAAc,IAC3C,oKAAkC,CAAC,0BAAA,oCAAA,cAAe,cAAc,CAAC,GACjE,oKAAkC,CAAC,8JAA4B,CAAC,GAAG,CAAC;YACxE,eAAe;gBACb,EAAE,EAAE,0BAAA,qCAAA,+BAAA,cAAe,aAAa,cAA5B,mDAAA,6BAA8B,EAAE;gBACpC,IAAI,EAAE,0BAAA,qCAAA,gCAAA,cAAe,aAAa,cAA5B,oDAAA,8BAA8B,IAAI;gBACxC,uBAAuB,EACrB,0BAAA,qCAAA,gCAAA,cAAe,aAAa,cAA5B,oDAAA,8BAA8B,uBAAuB;gBACvD,2BAA2B,EACzB,0BAAA,qCAAA,gCAAA,cAAe,aAAa,cAA5B,oDAAA,8BAA8B,2BAA2B;gBAC3D,0BAA0B,EACxB,0BAAA,qCAAA,gCAAA,cAAe,aAAa,cAA5B,oDAAA,8BAA8B,0BAA0B;gBAC1D,sBAAsB,EACpB,0BAAA,qCAAA,gCAAA,cAAe,aAAa,cAA5B,oDAAA,8BAA8B,sBAAsB;gBACtD,cAAc,EAAE,0BAAA,qCAAA,gCAAA,cAAe,aAAa,cAA5B,oDAAA,8BAA8B,cAAc;gBAC5D,6CAA6C,EAC3C,0BAAA,qCAAA,gCAAA,cAAe,aAAa,cAA5B,oDAAA,8BACI,6CAA6C;gBACnD,6CAA6C,EAC3C,0BAAA,qCAAA,gCAAA,cAAe,aAAa,cAA5B,oDAAA,8BACI,6CAA6C;YACrD;YACA,YAAY;gBACV,EAAE,EAAE,0BAAA,qCAAA,4BAAA,cAAe,UAAU,cAAzB,gDAAA,0BAA2B,EAAE;gBACjC,IAAI,EAAE,0BAAA,qCAAA,6BAAA,cAAe,UAAU,cAAzB,iDAAA,2BAA2B,IAAI;gBACrC,aAAa,EAAE,0BAAA,qCAAA,6BAAA,cAAe,UAAU,cAAzB,iDAAA,2BAA2B,aAAa;gBACvD,eAAe,EAAE,0BAAA,qCAAA,6BAAA,cAAe,UAAU,cAAzB,iDAAA,2BAA2B,eAAe;gBAC3D,oBAAoB,EAAE,0BAAA,qCAAA,6BAAA,cAAe,UAAU,cAAzB,iDAAA,2BAA2B,oBAAoB;gBACrE,cAAc,EAAE,0BAAA,qCAAA,6BAAA,cAAe,UAAU,cAAzB,iDAAA,2BAA2B,cAAc;gBACzD,uCAAuC,EACrC,0BAAA,qCAAA,6BAAA,cAAe,UAAU,cAAzB,iDAAA,2BAA2B,uCAAuC;gBACpE,uCAAuC,EACrC,0BAAA,qCAAA,6BAAA,cAAe,UAAU,cAAzB,iDAAA,2BAA2B,uCAAuC;YACtE;QACF;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,mNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,SAAS;6DAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;gBACtB,MAAM,YAAY,iBAAiB,CAAC;oBAClC,UAAU;wBAAC;wBAAW;4BAAE;4BAAW;wBAAS;qBAAE;gBAChD;gBACA;YACF;;QACA,OAAO;6DAAE,CAAA;gBACP,oJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAC7D;;IACF;IAGF,MAAM,mBAAmB,IAAA,4KAAW;2DAClC,CAAC;YACC,OAAO;gBAAE;gBAAW;gBAAU,MAAM;oBAAE;gBAAmB;YAAE;QAC7D;0DACA;QAAC;QAAQ;QAAW;KAAS;IAG/B,MAAM,iBAAiB,KAAK,KAAK,CAAC;IAElC,IAAA,0KAAS;uCAAC;YACR,IAAI,gBAAgB;gBAClB,KAAK,QAAQ,CACX,oBACA,oKAAkC,CAAC,eAAe;YAEtD;QACF;sCAAG;QAAC;QAAgB;KAAK;IAEzB,qBACE,6LAAC,2IAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,6LAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,6LAAC,mMAAoB;oBACnB,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,SAAS,iKAA+B;oBACxC,eAAe,CAAA,IAAK,EAAE,AAAC,kBAAmB,OAAF;oBACxC,GAAG;oBACH,WAAW;;;;;;8BAEb,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;;;;;;8BAEZ,6LAAC,qJAAS;;;;;8BACV,6LAAC,8RAA6B;oBAAC,SAAS;;;;;;8BACxC,6LAAC,qJAAS;;;;;8BACV,6LAAC,wRAA0B;oBAAC,SAAS;;;;;;8BACrC,6LAAC,+IAAM;oBAAC,MAAK;oBAAS,WAAU;oBAAmB,UAAU;;wBAC1D,2BAAa,6LAAC,uOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB;GAtHa;;QAOD,4NAAe;QACT,4NAAe;QACf,4NAAe;QACX,2MAAc;QACrB,4KAAO;QA0CU,mNAA2B;;;KArD9C", "debugId": null}}, {"offset": {"line": 1109, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-reinforcement-form.tsx"], "sourcesContent": ["import { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { PolarityAwareNumberFormInput } from '@atlas/components/common/form/polarity-aware-number-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport type { ModulePolarity } from '@atlas/constants/module'\r\nimport { calculateTBeamArmorArea } from '@atlas/functions/forms/t-beam-form-calculations'\r\nimport type { Module } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useSaveModuleParamsMutation } from '@atlas/lib/mutation/modules/use-save-module-params-mutation'\r\nimport { cn } from '@atlas/lib/utils'\r\nimport {\r\n  type TBeamRebarFormInputs as FormSchema,\r\n  tBeamRebarSchema,\r\n} from '@atlas/types/schemas/t-beam-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { useQueryClient } from '@tanstack/react-query'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useEffect } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  defaultValues?: Partial<FormSchema>\r\n  onSave: () => void\r\n  polarity: ModulePolarity\r\n}\r\n\r\nexport const TBeamReinforcementForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  defaultValues,\r\n  onSave,\r\n  polarity,\r\n}: Props) => {\r\n  const t = useTranslations('forms.project-params.t-beam.tBeamRebar')\r\n  const tAction = useTranslations('actions.save-project-params.messages')\r\n  const tCommon = useTranslations('actions.common')\r\n  const queryClient = useQueryClient()\r\n  const form = useForm<FormSchema>({\r\n    resolver: zodResolver(tBeamRebarSchema),\r\n    defaultValues: {\r\n      compressionRebars: {\r\n        diameter: defaultValues?.compressionRebars?.diameter,\r\n        quantity: defaultValues?.compressionRebars?.quantity,\r\n        area: defaultValues?.compressionRebars?.area,\r\n      },\r\n      tensionRebars: {\r\n        diameter: defaultValues?.tensionRebars?.diameter,\r\n        quantity: defaultValues?.tensionRebars?.quantity,\r\n        area: defaultValues?.tensionRebars?.area,\r\n      },\r\n    },\r\n  })\r\n\r\n  const [compressionRebars, tensionRebars] = form.watch([\r\n    'compressionRebars',\r\n    'tensionRebars',\r\n  ])\r\n\r\n  useEffect(() => {\r\n    if (compressionRebars.diameter && compressionRebars.quantity) {\r\n      form.setValue(\r\n        'compressionRebars.area',\r\n        calculateTBeamArmorArea(\r\n          compressionRebars.diameter,\r\n          compressionRebars.quantity,\r\n        ),\r\n      )\r\n    }\r\n  }, [compressionRebars.diameter, compressionRebars.quantity, form])\r\n\r\n  useEffect(() => {\r\n    if (tensionRebars.diameter && tensionRebars.quantity) {\r\n      form.setValue(\r\n        'tensionRebars.area',\r\n        calculateTBeamArmorArea(tensionRebars.diameter, tensionRebars.quantity),\r\n      )\r\n    }\r\n  }, [tensionRebars.diameter, tensionRebars.quantity, form])\r\n\r\n  const { mutate, isPending } = useSaveModuleParamsMutation(\r\n    session.accessToken,\r\n    {\r\n      onSuccess: async () => {\r\n        toast.success(tAction('edit.success'))\r\n        await queryClient.invalidateQueries({\r\n          queryKey: ['modules', { projectId, moduleId }],\r\n        })\r\n        onSave()\r\n      },\r\n      onError: error => {\r\n        toast.error(tAction('edit.failure', { error: error.message }))\r\n      },\r\n    },\r\n  )\r\n\r\n  const handleFormSubmit = useCallback(\r\n    (tBeamRebar: FormSchema) => {\r\n      mutate({ projectId, moduleId, body: { tBeamRebar } })\r\n    },\r\n    [mutate, projectId, moduleId],\r\n  )\r\n\r\n  return (\r\n    <Form {...form}>\r\n      <form\r\n        className=\"space-y-4 rounded-md border p-4\"\r\n        onSubmit={form.handleSubmit(handleFormSubmit)}\r\n      >\r\n        <h1 className=\"text-md font-bold\">\r\n          {t(`compressionRebars.subtitlePolarity.${polarity}`)}\r\n        </h1>\r\n        <p className={cn('text-muted-foreground text-sm')}>\r\n          {t('compressionRebars.description')}\r\n        </p>\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"compressionRebars.diameter\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"compressionRebars.quantity\"\r\n          t={t}\r\n        />\r\n        <PolarityAwareNumberFormInput\r\n          control={form.control}\r\n          name=\"compressionRebars.area\"\r\n          t={t}\r\n          polarity={polarity}\r\n          disabled={true}\r\n        />\r\n        <Separator />\r\n        <h1 className=\"text-md font-bold\">\r\n          {t(`tensionRebars.subtitlePolarity.${polarity}`)}\r\n        </h1>\r\n        <p className={cn('text-muted-foreground text-sm')}>\r\n          {t('tensionRebars.description')}\r\n        </p>\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"tensionRebars.diameter\"\r\n          t={t}\r\n        />\r\n        <NumberFormInput\r\n          control={form.control}\r\n          name=\"tensionRebars.quantity\"\r\n          t={t}\r\n        />\r\n        <PolarityAwareNumberFormInput\r\n          control={form.control}\r\n          name=\"tensionRebars.area\"\r\n          t={t}\r\n          polarity={polarity}\r\n          disabled={true}\r\n        />\r\n        <Button\r\n          type=\"submit\"\r\n          className=\"w-full sm:w-auto\"\r\n          disabled={isPending}\r\n          onClick={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n          {tCommon('save')}\r\n        </Button>\r\n      </form>\r\n    </Form>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AAEA;AAGA;AACA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AAWO,MAAM,yBAAyB;QAAC,EACrC,OAAO,EACP,SAAS,EACT,QAAQ,EACR,aAAa,EACb,MAAM,EACN,QAAQ,EACF;QASU,kCACA,mCACJ,mCAGI,8BACA,+BACJ;;IAfZ,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,cAAc,IAAA,2MAAc;IAClC,MAAM,OAAO,IAAA,4KAAO,EAAa;QAC/B,UAAU,IAAA,gLAAW,EAAC,mKAAgB;QACtC,eAAe;YACb,mBAAmB;gBACjB,QAAQ,EAAE,0BAAA,qCAAA,mCAAA,cAAe,iBAAiB,cAAhC,uDAAA,iCAAkC,QAAQ;gBACpD,QAAQ,EAAE,0BAAA,qCAAA,oCAAA,cAAe,iBAAiB,cAAhC,wDAAA,kCAAkC,QAAQ;gBACpD,IAAI,EAAE,0BAAA,qCAAA,oCAAA,cAAe,iBAAiB,cAAhC,wDAAA,kCAAkC,IAAI;YAC9C;YACA,eAAe;gBACb,QAAQ,EAAE,0BAAA,qCAAA,+BAAA,cAAe,aAAa,cAA5B,mDAAA,6BAA8B,QAAQ;gBAChD,QAAQ,EAAE,0BAAA,qCAAA,gCAAA,cAAe,aAAa,cAA5B,oDAAA,8BAA8B,QAAQ;gBAChD,IAAI,EAAE,0BAAA,qCAAA,gCAAA,cAAe,aAAa,cAA5B,oDAAA,8BAA8B,IAAI;YAC1C;QACF;IACF;IAEA,MAAM,CAAC,mBAAmB,cAAc,GAAG,KAAK,KAAK,CAAC;QACpD;QACA;KACD;IAED,IAAA,0KAAS;4CAAC;YACR,IAAI,kBAAkB,QAAQ,IAAI,kBAAkB,QAAQ,EAAE;gBAC5D,KAAK,QAAQ,CACX,0BACA,IAAA,4LAAuB,EACrB,kBAAkB,QAAQ,EAC1B,kBAAkB,QAAQ;YAGhC;QACF;2CAAG;QAAC,kBAAkB,QAAQ;QAAE,kBAAkB,QAAQ;QAAE;KAAK;IAEjE,IAAA,0KAAS;4CAAC;YACR,IAAI,cAAc,QAAQ,IAAI,cAAc,QAAQ,EAAE;gBACpD,KAAK,QAAQ,CACX,sBACA,IAAA,4LAAuB,EAAC,cAAc,QAAQ,EAAE,cAAc,QAAQ;YAE1E;QACF;2CAAG;QAAC,cAAc,QAAQ;QAAE,cAAc,QAAQ;QAAE;KAAK;IAEzD,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,mNAA2B,EACvD,QAAQ,WAAW,EACnB;QACE,SAAS;kEAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;gBACtB,MAAM,YAAY,iBAAiB,CAAC;oBAClC,UAAU;wBAAC;wBAAW;4BAAE;4BAAW;wBAAS;qBAAE;gBAChD;gBACA;YACF;;QACA,OAAO;kEAAE,CAAA;gBACP,oJAAK,CAAC,KAAK,CAAC,QAAQ,gBAAgB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAC7D;;IACF;IAGF,MAAM,mBAAmB,IAAA,4KAAW;gEAClC,CAAC;YACC,OAAO;gBAAE;gBAAW;gBAAU,MAAM;oBAAE;gBAAW;YAAE;QACrD;+DACA;QAAC;QAAQ;QAAW;KAAS;IAG/B,qBACE,6LAAC,2IAAI;QAAE,GAAG,IAAI;kBACZ,cAAA,6LAAC;YACC,WAAU;YACV,UAAU,KAAK,YAAY,CAAC;;8BAE5B,6LAAC;oBAAG,WAAU;8BACX,EAAE,AAAC,sCAA8C,OAAT;;;;;;8BAE3C,6LAAC;oBAAE,WAAW,IAAA,4HAAE,EAAC;8BACd,EAAE;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,6LAAC,uNAA4B;oBAC3B,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;oBACV,UAAU;;;;;;8BAEZ,6LAAC,qJAAS;;;;;8BACV,6LAAC;oBAAG,WAAU;8BACX,EAAE,AAAC,kCAA0C,OAAT;;;;;;8BAEvC,6LAAC;oBAAE,WAAW,IAAA,4HAAE,EAAC;8BACd,EAAE;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,6LAAC,qLAAe;oBACd,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;;;;;;8BAEL,6LAAC,uNAA4B;oBAC3B,SAAS,KAAK,OAAO;oBACrB,MAAK;oBACL,GAAG;oBACH,UAAU;oBACV,UAAU;;;;;;8BAEZ,6LAAC,+IAAM;oBACL,MAAK;oBACL,WAAU;oBACV,UAAU;oBACV,SAAS,KAAK,YAAY,CAAC;;wBAE1B,2BAAa,6LAAC,uOAAW;4BAAC,WAAU;;;;;;wBACpC,QAAQ;;;;;;;;;;;;;;;;;;AAKnB;GA9Ia;;QAQD,4NAAe;QACT,4NAAe;QACf,4NAAe;QACX,2MAAc;QACrB,4KAAO;QA0CU,mNAA2B;;;KAtD9C", "debugId": null}}, {"offset": {"line": 1389, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-result-card.tsx"], "sourcesContent": ["import {\r\n  <PERSON>,\r\n  Card<PERSON>ontent,\r\n  <PERSON><PERSON><PERSON>er,\r\n  <PERSON>Title,\r\n} from '@atlas/components/ui/card'\r\nimport type { TBeamFlexuralCalculationResult } from '@atlas/lib/api/modules/schemas/t-beam-params'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  results: TBeamFlexuralCalculationResult\r\n}\r\n\r\nexport const TBeamFlexuralResultCard = ({ results }: Props) => {\r\n  const t = useTranslations('forms.calculations.t-beam.flexural.results')\r\n\r\n  const resultItems = [\r\n    { key: 'momentCapacity', value: results.momentCapacity },\r\n    { key: 'equilibrium', value: results.equilibrium },\r\n    { key: 'checkResult', value: results.checkResult ? t('pass') : t('fail') },\r\n  ]\r\n\r\n  return (\r\n    <Card>\r\n      <CardHeader>\r\n        <CardTitle>{t('title')}</CardTitle>\r\n      </CardHeader>\r\n      <CardContent className=\"space-y-4\">\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\r\n          {resultItems.map(item => (\r\n            <div\r\n              key={item.key}\r\n              className=\"flex flex-col space-y-1 p-3 bg-muted rounded-md\"\r\n            >\r\n              <span className=\"text-sm font-medium text-muted-foreground\">\r\n                {t(`${item.key}.label`)}\r\n              </span>\r\n              <span className=\"text-lg font-semibold\">\r\n                {typeof item.value === 'number'\r\n                  ? item.value.toFixed(4)\r\n                  : item.value}\r\n                {typeof item.value === 'number' && (\r\n                  <span className=\"text-sm text-muted-foreground ml-1\">\r\n                    {t(`${item.key}.unit`)}\r\n                  </span>\r\n                )}\r\n              </span>\r\n            </div>\r\n          ))}\r\n        </div>\r\n        <div\r\n          className={`p-4 rounded-md border-2 ${\r\n            results.checkResult\r\n              ? 'border-green-500 bg-green-50 text-green-800'\r\n              : 'border-red-500 bg-red-50 text-red-800'\r\n          }`}\r\n        >\r\n          <p className=\"font-semibold\">\r\n            {results.checkResult\r\n              ? t('verification.pass')\r\n              : t('verification.fail')}\r\n          </p>\r\n        </div>\r\n      </CardContent>\r\n    </Card>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AAOA;;;;;AAMO,MAAM,0BAA0B;QAAC,EAAE,OAAO,EAAS;;IACxD,MAAM,IAAI,IAAA,4NAAe,EAAC;IAE1B,MAAM,cAAc;QAClB;YAAE,KAAK;YAAkB,OAAO,QAAQ,cAAc;QAAC;QACvD;YAAE,KAAK;YAAe,OAAO,QAAQ,WAAW;QAAC;QACjD;YAAE,KAAK;YAAe,OAAO,QAAQ,WAAW,GAAG,EAAE,UAAU,EAAE;QAAQ;KAC1E;IAED,qBACE,6LAAC,2IAAI;;0BACH,6LAAC,iJAAU;0BACT,cAAA,6LAAC,gJAAS;8BAAE,EAAE;;;;;;;;;;;0BAEhB,6LAAC,kJAAW;gBAAC,WAAU;;kCACrB,6LAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAA,qBACf,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;wCAAK,WAAU;kDACb,EAAE,AAAC,GAAW,OAAT,KAAK,GAAG,EAAC;;;;;;kDAEjB,6LAAC;wCAAK,WAAU;;4CACb,OAAO,KAAK,KAAK,KAAK,WACnB,KAAK,KAAK,CAAC,OAAO,CAAC,KACnB,KAAK,KAAK;4CACb,OAAO,KAAK,KAAK,KAAK,0BACrB,6LAAC;gDAAK,WAAU;0DACb,EAAE,AAAC,GAAW,OAAT,KAAK,GAAG,EAAC;;;;;;;;;;;;;+BAZhB,KAAK,GAAG;;;;;;;;;;kCAmBnB,6LAAC;wBACC,WAAW,AAAC,2BAIX,OAHC,QAAQ,WAAW,GACf,gDACA;kCAGN,cAAA,6LAAC;4BAAE,WAAU;sCACV,QAAQ,WAAW,GAChB,EAAE,uBACF,EAAE;;;;;;;;;;;;;;;;;;;;;;;AAMlB;GArDa;;QACD,4NAAe;;;KADd", "debugId": null}}, {"offset": {"line": 1521, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-calculation.tsx"], "sourcesContent": ["import { TBeamFlexuralResultCard } from '@atlas/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-result-card'\r\nimport { CustomProductSection } from '@atlas/components/common/atlas/product/custom-product-section'\r\nimport { ProductSummaryCard } from '@atlas/components/common/atlas/product/product-summary-card'\r\nimport { NumberFormInput } from '@atlas/components/common/form/number-form-input'\r\nimport { SelectFormInput } from '@atlas/components/common/form/select-form-input'\r\nimport { Button } from '@atlas/components/ui/button'\r\nimport { Form } from '@atlas/components/ui/form'\r\nimport { productFiberType } from '@atlas/constants/product'\r\nimport type { ModuleWithParamsTBeam } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { useModuleCalculation } from '@atlas/lib/mutation/modules/use-module-calculation'\r\nimport { useProductsByCategory } from '@atlas/lib/query/products/use-products-by-category'\r\nimport {\r\n  type TBeamFlexuralCalculationInput,\r\n  tBeamFlexuralCalculationSchema,\r\n} from '@atlas/types/schemas/t-beam-form'\r\nimport { zodResolver } from '@hookform/resolvers/zod'\r\nimport { Loader2Icon } from 'lucide-react'\r\nimport Image from 'next/image'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useEffect, useMemo } from 'react'\r\nimport { useForm } from 'react-hook-form'\r\nimport { toast } from 'sonner'\r\n\r\ntype Props = {\r\n  session: Session\r\n  module: ModuleWithParamsTBeam\r\n  projectId: Project['id']\r\n}\r\n\r\nexport const TBeamFlexuralCalculation = ({\r\n  session,\r\n  module,\r\n  projectId,\r\n}: Props) => {\r\n  const t = useTranslations('forms.calculations.t-beam.flexural')\r\n  const tAction = useTranslations('actions.calculations.t-beam.flexural')\r\n  const tCommon = useTranslations('actions.common')\r\n  const { flexuralVerifyExecutionInput, flexuralVerifyExecutionResult } = module\r\n  const form = useForm<TBeamFlexuralCalculationInput>({\r\n    resolver: zodResolver(tBeamFlexuralCalculationSchema),\r\n    defaultValues: {\r\n      calculationType: 'FLEXURAL_VERIFY',\r\n      input: {\r\n        product: {\r\n          id: flexuralVerifyExecutionInput?.product.id,\r\n          name: flexuralVerifyExecutionInput?.product.name,\r\n          sourceType:\r\n            flexuralVerifyExecutionInput?.product.id === 'custom'\r\n              ? 'CUSTOM'\r\n              : 'DATABASE',\r\n          thickness: flexuralVerifyExecutionInput?.product.thickness,\r\n          tensileStrength:\r\n            flexuralVerifyExecutionInput?.product.tensileStrength,\r\n          elasticModulus: flexuralVerifyExecutionInput?.product.elasticModulus,\r\n          fiberType:\r\n            flexuralVerifyExecutionInput?.product.fiberType ??\r\n            productFiberType.CARBON,\r\n        },\r\n        stripWidth: flexuralVerifyExecutionInput?.stripWidth ?? 1,\r\n        layersNumber: flexuralVerifyExecutionInput?.layersNumber ?? 2,\r\n        bendingMoment: flexuralVerifyExecutionInput?.bendingMoment ?? 10,\r\n      },\r\n    },\r\n  })\r\n\r\n  const { mutate, isPending } = useModuleCalculation(session.accessToken, {\r\n    onSuccess: () => {\r\n      toast.success(tAction('calculate.success'))\r\n    },\r\n    onError: error => {\r\n      toast.error(tAction('calculate.failure', { error: error.message }))\r\n    },\r\n  })\r\n\r\n  const handleFormSubmit = (body: TBeamFlexuralCalculationInput) => {\r\n    const res = mutate({ projectId, moduleId: module.id, body })\r\n    console.log(res)\r\n  }\r\n\r\n  const {\r\n    data: products,\r\n    isError: errorGettingProducts,\r\n    isLoading: isLoadingProducts,\r\n  } = useProductsByCategory(session, 'T_BEAM', 0, 100)\r\n\r\n  const productsOptions = [\r\n    ...(products?.content.map(m => ({\r\n      value: m.id,\r\n      label: m.name ?? t('product.unnamed'),\r\n    })) ?? []),\r\n    { value: 'custom', label: t('product.custom') },\r\n  ]\r\n\r\n  const [productId] = form.watch(['input.product.id'])\r\n  const selectedProduct = useMemo(\r\n    () => products?.content.find(p => p.id === productId),\r\n    [productId, products],\r\n  )\r\n\r\n  useEffect(() => {\r\n    if (!productId) {\r\n      return\r\n    }\r\n    if (productId === 'custom') {\r\n      form.setValue('input.product.sourceType', 'CUSTOM')\r\n    }\r\n\r\n    if (selectedProduct) {\r\n      form.setValue('input.product', {\r\n        ...selectedProduct,\r\n        sourceType: 'DATABASE',\r\n      })\r\n    }\r\n  }, [form, productId, selectedProduct])\r\n\r\n  return (\r\n    <div className=\"flex flex-col justify-center gap-4\">\r\n      <Form {...form}>\r\n        <form\r\n          className=\"space-y-4 rounded-md border p-4\"\r\n          onSubmit={form.handleSubmit(handleFormSubmit)}\r\n        >\r\n          <h1 className=\"text-3xl font-bold\">{t('heading')}</h1>\r\n          <Image\r\n            src=\"/assets/t_beam/T_beam_flexural_verify.jpg\"\r\n            alt=\"flexural verify\"\r\n            height={250}\r\n            width={500}\r\n            className=\"mx-auto rounded-md object-contain\"\r\n            priority\r\n          />\r\n          <h1 className=\"text-xl font-bold\">{t('sub-heading')}</h1>\r\n          <SelectFormInput\r\n            control={form.control}\r\n            name=\"input.product.id\"\r\n            options={productsOptions}\r\n            t={t}\r\n            loading={isLoadingProducts}\r\n            requestError={errorGettingProducts}\r\n            errorMessage={t('products.error')}\r\n          />\r\n          {productId === 'custom' && <CustomProductSection />}\r\n          {selectedProduct && <ProductSummaryCard product={selectedProduct} />}\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.stripWidth\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.layersNumber\"\r\n            t={t}\r\n          />\r\n          <NumberFormInput\r\n            control={form.control}\r\n            name=\"input.bendingMoment\"\r\n            t={t}\r\n          />\r\n          <Button\r\n            type=\"submit\"\r\n            className=\"w-full sm:w-auto\"\r\n            disabled={isPending}\r\n          >\r\n            {isPending && <Loader2Icon className=\"animate-spin\" />}\r\n            {tCommon('calculate')}\r\n          </Button>\r\n        </form>\r\n      </Form>\r\n      {flexuralVerifyExecutionResult && (\r\n        <TBeamFlexuralResultCard results={flexuralVerifyExecutionResult} />\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAGA;AACA;AACA;AAIA;AACA;AACA;AAEA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;AAQO,MAAM,2BAA2B;QAAC,EACvC,OAAO,EACP,MAAM,EACN,SAAS,EACH;;IACN,MAAM,IAAI,IAAA,4NAAe,EAAC;IAC1B,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,UAAU,IAAA,4NAAe,EAAC;IAChC,MAAM,EAAE,4BAA4B,EAAE,6BAA6B,EAAE,GAAG;QAkB9D,iDAGQ,0CACE,4CACC;IAtBrB,MAAM,OAAO,IAAA,4KAAO,EAAgC;QAClD,UAAU,IAAA,gLAAW,EAAC,iLAA8B;QACpD,eAAe;YACb,iBAAiB;YACjB,OAAO;gBACL,SAAS;oBACP,EAAE,EAAE,yCAAA,mDAAA,6BAA8B,OAAO,CAAC,EAAE;oBAC5C,IAAI,EAAE,yCAAA,mDAAA,6BAA8B,OAAO,CAAC,IAAI;oBAChD,YACE,CAAA,yCAAA,mDAAA,6BAA8B,OAAO,CAAC,EAAE,MAAK,WACzC,WACA;oBACN,SAAS,EAAE,yCAAA,mDAAA,6BAA8B,OAAO,CAAC,SAAS;oBAC1D,eAAe,EACb,yCAAA,mDAAA,6BAA8B,OAAO,CAAC,eAAe;oBACvD,cAAc,EAAE,yCAAA,mDAAA,6BAA8B,OAAO,CAAC,cAAc;oBACpE,WACE,CAAA,kDAAA,yCAAA,mDAAA,6BAA8B,OAAO,CAAC,SAAS,cAA/C,6DAAA,kDACA,mJAAgB,CAAC,MAAM;gBAC3B;gBACA,YAAY,CAAA,2CAAA,yCAAA,mDAAA,6BAA8B,UAAU,cAAxC,sDAAA,2CAA4C;gBACxD,cAAc,CAAA,6CAAA,yCAAA,mDAAA,6BAA8B,YAAY,cAA1C,wDAAA,6CAA8C;gBAC5D,eAAe,CAAA,8CAAA,yCAAA,mDAAA,6BAA8B,aAAa,cAA3C,yDAAA,8CAA+C;YAChE;QACF;IACF;IAEA,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,IAAA,6LAAoB,EAAC,QAAQ,WAAW,EAAE;QACtE,SAAS;6DAAE;gBACT,oJAAK,CAAC,OAAO,CAAC,QAAQ;YACxB;;QACA,OAAO;6DAAE,CAAA;gBACP,oJAAK,CAAC,KAAK,CAAC,QAAQ,qBAAqB;oBAAE,OAAO,MAAM,OAAO;gBAAC;YAClE;;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,MAAM,OAAO;YAAE;YAAW,UAAU,OAAO,EAAE;YAAE;QAAK;QAC1D,QAAQ,GAAG,CAAC;IACd;IAEA,MAAM,EACJ,MAAM,QAAQ,EACd,SAAS,oBAAoB,EAC7B,WAAW,iBAAiB,EAC7B,GAAG,IAAA,iMAAqB,EAAC,SAAS,UAAU,GAAG;QAG1C;IADN,MAAM,kBAAkB;WAClB,CAAA,wBAAA,qBAAA,+BAAA,SAAU,OAAO,CAAC,GAAG,CAAC,CAAA;gBAEjB;mBAFuB;gBAC9B,OAAO,EAAE,EAAE;gBACX,OAAO,CAAA,UAAA,EAAE,IAAI,cAAN,qBAAA,UAAU,EAAE;YACrB;wBAHI,mCAAA,wBAGG,EAAE;QACT;YAAE,OAAO;YAAU,OAAO,EAAE;QAAkB;KAC/C;IAED,MAAM,CAAC,UAAU,GAAG,KAAK,KAAK,CAAC;QAAC;KAAmB;IACnD,MAAM,kBAAkB,IAAA,wKAAO;6DAC7B,IAAM,qBAAA,+BAAA,SAAU,OAAO,CAAC,IAAI;qEAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;4DAC3C;QAAC;QAAW;KAAS;IAGvB,IAAA,0KAAS;8CAAC;YACR,IAAI,CAAC,WAAW;gBACd;YACF;YACA,IAAI,cAAc,UAAU;gBAC1B,KAAK,QAAQ,CAAC,4BAA4B;YAC5C;YAEA,IAAI,iBAAiB;gBACnB,KAAK,QAAQ,CAAC,iBAAiB;oBAC7B,GAAG,eAAe;oBAClB,YAAY;gBACd;YACF;QACF;6CAAG;QAAC;QAAM;QAAW;KAAgB;IAErC,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,2IAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,6LAAC;oBACC,WAAU;oBACV,UAAU,KAAK,YAAY,CAAC;;sCAE5B,6LAAC;4BAAG,WAAU;sCAAsB,EAAE;;;;;;sCACtC,6LAAC,2IAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,QAAQ;4BACR,OAAO;4BACP,WAAU;4BACV,QAAQ;;;;;;sCAEV,6LAAC;4BAAG,WAAU;sCAAqB,EAAE;;;;;;sCACrC,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,SAAS;4BACT,GAAG;4BACH,SAAS;4BACT,cAAc;4BACd,cAAc,EAAE;;;;;;wBAEjB,cAAc,0BAAY,6LAAC,2MAAoB;;;;;wBAC/C,iCAAmB,6LAAC,uMAAkB;4BAAC,SAAS;;;;;;sCACjD,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,6LAAC,qLAAe;4BACd,SAAS,KAAK,OAAO;4BACrB,MAAK;4BACL,GAAG;;;;;;sCAEL,6LAAC,+IAAM;4BACL,MAAK;4BACL,WAAU;4BACV,UAAU;;gCAET,2BAAa,6LAAC,uOAAW;oCAAC,WAAU;;;;;;gCACpC,QAAQ;;;;;;;;;;;;;;;;;;YAId,+CACC,6LAAC,kRAAuB;gBAAC,SAAS;;;;;;;;;;;;AAI1C;GAhJa;;QAKD,4NAAe;QACT,4NAAe;QACf,4NAAe;QAElB,4KAAO;QA2BU,6LAAoB;QAkB9C,iMAAqB;;;KAtDd", "debugId": null}}, {"offset": {"line": 1813, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-calculations.tsx"], "sourcesContent": ["import { TBeamFlexuralCalculation } from '@atlas/components/common/atlas/module-detail/params-forms/t-beam/calculations/t-beam-flexural-calculation'\r\nimport {\r\n  Ta<PERSON>,\r\n  Ta<PERSON>Content,\r\n  Ta<PERSON>List,\r\n  TabsTrigger,\r\n} from '@atlas/components/ui/tabs'\r\nimport type { ModuleWithParamsTBeam } from '@atlas/lib/api/modules/schemas/modules'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\n\r\ntype Props = {\r\n  session: Session\r\n  module: ModuleWithParamsTBeam\r\n  projectId: Project['id']\r\n}\r\n\r\nexport const TBeamCalculations = ({ session, module, projectId }: Props) => {\r\n  const t = useTranslations('forms.calculations.t-beam')\r\n\r\n  return (\r\n    <>\r\n      <h3 className=\"text-lg font-medium py-4\">{t('title')}</h3>\r\n      <Tabs defaultValue=\"flexural\">\r\n        <TabsList>\r\n          <TabsTrigger value=\"flexural\">{t('flexural.label')}</TabsTrigger>\r\n        </TabsList>\r\n        <TabsContent value=\"flexural\">\r\n          <TBeamFlexuralCalculation\r\n            session={session}\r\n            module={module}\r\n            projectId={projectId}\r\n          />\r\n        </TabsContent>\r\n      </Tabs>\r\n    </>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AASA;;;;;;AAQO,MAAM,oBAAoB;QAAC,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAS;;IACrE,MAAM,IAAI,IAAA,4NAAe,EAAC;IAE1B,qBACE;;0BACE,6LAAC;gBAAG,WAAU;0BAA4B,EAAE;;;;;;0BAC5C,6LAAC,2IAAI;gBAAC,cAAa;;kCACjB,6LAAC,+IAAQ;kCACP,cAAA,6LAAC,kJAAW;4BAAC,OAAM;sCAAY,EAAE;;;;;;;;;;;kCAEnC,6LAAC,kJAAW;wBAAC,OAAM;kCACjB,cAAA,6LAAC,gRAAwB;4BACvB,SAAS;4BACT,QAAQ;4BACR,WAAW;;;;;;;;;;;;;;;;;;;AAMvB;GApBa;;QACD,4NAAe;;;KADd", "debugId": null}}, {"offset": {"line": 1897, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/common/atlas/module-detail/params-forms/t-beam/t-beam-params-form.tsx"], "sourcesContent": ["import { ModuleReportGenerationSection } from '@atlas/components/common/atlas/module-detail/module-report-generation-section'\r\nimport { TBeamGeneralForm } from '@atlas/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-general-form'\r\nimport { TBeamGeometryForm } from '@atlas/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-geometry-form'\r\nimport { TBeamMaterialForm } from '@atlas/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-material-form'\r\nimport { TBeamReinforcementForm } from '@atlas/components/common/atlas/module-detail/params-forms/t-beam/sections/t-beam-reinforcement-form'\r\nimport { TBeamCalculations } from '@atlas/components/common/atlas/module-detail/params-forms/t-beam/t-beam-calculations'\r\nimport {\r\n  Accordion as AccordionComponent,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from '@atlas/components/ui/accordion'\r\nimport { Separator } from '@atlas/components/ui/separator'\r\nimport { modulePolarity } from '@atlas/constants/module'\r\nimport type {\r\n  Module,\r\n  ModuleWithParamsTBeam,\r\n} from '@atlas/lib/api/modules/schemas/modules'\r\nimport { tBeamCalculationCheck } from '@atlas/lib/api/modules/schemas/t-beam-params'\r\nimport type { Project } from '@atlas/lib/api/projects/schemas/projects'\r\nimport { tBeamParamsCheckSchema } from '@atlas/types/schemas/t-beam-form'\r\nimport type { Session } from 'next-auth'\r\nimport { useTranslations } from 'next-intl'\r\nimport { useCallback, useMemo, useState } from 'react'\r\n\r\ntype Props = {\r\n  session: Session\r\n  projectId: Project['id']\r\n  moduleId: Module['id']\r\n  module: ModuleWithParamsTBeam\r\n}\r\n\r\nexport const TBeamParamsForm = ({\r\n  session,\r\n  projectId,\r\n  moduleId,\r\n  module,\r\n}: Props) => {\r\n  const { params } = module\r\n  const [openItems, setOpenItems] = useState(['0'])\r\n  const t = useTranslations('forms.project-params.t-beam')\r\n\r\n  const handleItemSaved = useCallback((id: string) => {\r\n    const nextId = String(Number(id) + 1)\r\n    setOpenItems(old => {\r\n      const temp = old.filter(v => v !== id)\r\n      return old.includes(nextId) ? temp : [...temp, nextId]\r\n    })\r\n  }, [])\r\n\r\n  const { success } = useMemo(\r\n    () => tBeamParamsCheckSchema.safeParse(params),\r\n    [params],\r\n  )\r\n\r\n  const enableReport = useMemo(() => {\r\n    const result = tBeamCalculationCheck.safeParse(module)\r\n    return result.success\r\n  }, [module])\r\n\r\n  return (\r\n    <div>\r\n      <AccordionComponent\r\n        type=\"multiple\"\r\n        value={openItems}\r\n        onValueChange={setOpenItems}\r\n      >\r\n        <AccordionItem value=\"0\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">{t('general.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <TBeamGeneralForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={{\r\n                initialDeformation: params?.initialDeformation,\r\n                polarity: params?.polarity,\r\n              }}\r\n              onSave={() => handleItemSaved('0')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"1\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">{t('geometry.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <TBeamGeometryForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={params?.geometry}\r\n              polarity={params.polarity ?? modulePolarity.POSITIVE}\r\n              onSave={() => handleItemSaved('1')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"2\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">{t('tBeamRebar.title')}</h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <TBeamReinforcementForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={params?.tBeamRebar}\r\n              polarity={params.polarity ?? modulePolarity.POSITIVE}\r\n              onSave={() => handleItemSaved('2')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n        <AccordionItem value=\"3\">\r\n          <AccordionTrigger>\r\n            <h3 className=\"text-lg font-medium\">\r\n              {t('materialProperties.title')}\r\n            </h3>\r\n          </AccordionTrigger>\r\n          <AccordionContent>\r\n            <TBeamMaterialForm\r\n              session={session}\r\n              projectId={projectId}\r\n              moduleId={moduleId}\r\n              defaultValues={params?.materialProperties}\r\n              onSave={() => handleItemSaved('3')}\r\n            />\r\n          </AccordionContent>\r\n        </AccordionItem>\r\n      </AccordionComponent>\r\n\r\n      {success && (\r\n        <>\r\n          <Separator />\r\n          <TBeamCalculations\r\n            module={module}\r\n            session={session}\r\n            projectId={projectId}\r\n          />\r\n        </>\r\n      )}\r\n\r\n      {enableReport && (\r\n        <>\r\n          <Separator />\r\n          <ModuleReportGenerationSection\r\n            session={session}\r\n            projectId={projectId}\r\n            moduleId={moduleId}\r\n            enabled={enableReport}\r\n          />\r\n        </>\r\n      )}\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AACA;AAKA;AAEA;AAEA;AACA;;;;;;;;;;;;;;;;AASO,MAAM,kBAAkB;QAAC,EAC9B,OAAO,EACP,SAAS,EACT,QAAQ,EACR,MAAM,EACA;;IACN,MAAM,EAAE,MAAM,EAAE,GAAG;IACnB,MAAM,CAAC,WAAW,aAAa,GAAG,IAAA,yKAAQ,EAAC;QAAC;KAAI;IAChD,MAAM,IAAI,IAAA,4NAAe,EAAC;IAE1B,MAAM,kBAAkB,IAAA,4KAAW;wDAAC,CAAC;YACnC,MAAM,SAAS,OAAO,OAAO,MAAM;YACnC;gEAAa,CAAA;oBACX,MAAM,OAAO,IAAI,MAAM;6EAAC,CAAA,IAAK,MAAM;;oBACnC,OAAO,IAAI,QAAQ,CAAC,UAAU,OAAO;2BAAI;wBAAM;qBAAO;gBACxD;;QACF;uDAAG,EAAE;IAEL,MAAM,EAAE,OAAO,EAAE,GAAG,IAAA,wKAAO;mCACzB,IAAM,yKAAsB,CAAC,SAAS,CAAC;kCACvC;QAAC;KAAO;IAGV,MAAM,eAAe,IAAA,wKAAO;iDAAC;YAC3B,MAAM,SAAS,0LAAqB,CAAC,SAAS,CAAC;YAC/C,OAAO,OAAO,OAAO;QACvB;gDAAG;QAAC;KAAO;QAoCW,kBAeA;IAjDtB,qBACE,6LAAC;;0BACC,6LAAC,qJAAkB;gBACjB,MAAK;gBACL,OAAO;gBACP,eAAe;;kCAEf,6LAAC,yJAAa;wBAAC,OAAM;;0CACnB,6LAAC,4JAAgB;0CACf,cAAA,6LAAC;oCAAG,WAAU;8CAAuB,EAAE;;;;;;;;;;;0CAEzC,6LAAC,4JAAgB;0CACf,cAAA,6LAAC,4PAAgB;oCACf,SAAS;oCACT,WAAW;oCACX,UAAU;oCACV,eAAe;wCACb,kBAAkB,EAAE,mBAAA,6BAAA,OAAQ,kBAAkB;wCAC9C,QAAQ,EAAE,mBAAA,6BAAA,OAAQ,QAAQ;oCAC5B;oCACA,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;kCAIpC,6LAAC,yJAAa;wBAAC,OAAM;;0CACnB,6LAAC,4JAAgB;0CACf,cAAA,6LAAC;oCAAG,WAAU;8CAAuB,EAAE;;;;;;;;;;;0CAEzC,6LAAC,4JAAgB;0CACf,cAAA,6LAAC,8PAAiB;oCAChB,SAAS;oCACT,WAAW;oCACX,UAAU;oCACV,aAAa,EAAE,mBAAA,6BAAA,OAAQ,QAAQ;oCAC/B,UAAU,CAAA,mBAAA,OAAO,QAAQ,cAAf,8BAAA,mBAAmB,gJAAc,CAAC,QAAQ;oCACpD,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;kCAIpC,6LAAC,yJAAa;wBAAC,OAAM;;0CACnB,6LAAC,4JAAgB;0CACf,cAAA,6LAAC;oCAAG,WAAU;8CAAuB,EAAE;;;;;;;;;;;0CAEzC,6LAAC,4JAAgB;0CACf,cAAA,6LAAC,wQAAsB;oCACrB,SAAS;oCACT,WAAW;oCACX,UAAU;oCACV,aAAa,EAAE,mBAAA,6BAAA,OAAQ,UAAU;oCACjC,UAAU,CAAA,oBAAA,OAAO,QAAQ,cAAf,+BAAA,oBAAmB,gJAAc,CAAC,QAAQ;oCACpD,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;kCAIpC,6LAAC,yJAAa;wBAAC,OAAM;;0CACnB,6LAAC,4JAAgB;0CACf,cAAA,6LAAC;oCAAG,WAAU;8CACX,EAAE;;;;;;;;;;;0CAGP,6LAAC,4JAAgB;0CACf,cAAA,6LAAC,8PAAiB;oCAChB,SAAS;oCACT,WAAW;oCACX,UAAU;oCACV,aAAa,EAAE,mBAAA,6BAAA,OAAQ,kBAAkB;oCACzC,QAAQ,IAAM,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;YAMrC,yBACC;;kCACE,6LAAC,qJAAS;;;;;kCACV,6LAAC,8OAAiB;wBAChB,QAAQ;wBACR,SAAS;wBACT,WAAW;;;;;;;;YAKhB,8BACC;;kCACE,6LAAC,qJAAS;;;;;kCACV,6LAAC,0OAA6B;wBAC5B,SAAS;wBACT,WAAW;wBACX,UAAU;wBACV,SAAS;;;;;;;;;;;;;;AAMrB;GA5Ha;;QAQD,4NAAe;;;KARd", "debugId": null}}]}