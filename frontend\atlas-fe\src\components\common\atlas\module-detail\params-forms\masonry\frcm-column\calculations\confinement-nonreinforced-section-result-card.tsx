import { Badge } from '@atlas/components/ui/badge'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@atlas/components/ui/card'
import { cn } from '@atlas/lib/utils'
import type { NonReinforcedSectionSchema } from '@atlas/types/schemas/masonry/frcm-column-form'
import { useLocale, useTranslations } from 'next-intl'

type Props = {
  nonReinforcedSectionResults: NonReinforcedSectionSchema
}

export function ConfinementNonReinforcedSectionResultCard({
  nonReinforcedSectionResults,
}: Props) {
  const { normalStressStrength, designAxialResistance, check } =
    nonReinforcedSectionResults

  const t = useTranslations(
    'components.calculations.frcm-column.confinementReinforcementCalculationResult.confinementNonReinforcedSectionResult',
  )
  const locale = useLocale()

  return (
    <Card>
      <CardHeader>
        <CardTitle>{t('title')}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <span className="font-medium">
            {t('normalStressStrength.label')}:
          </span>{' '}
          <span>
            {normalStressStrength?.toLocaleString(locale, {
              maximumFractionDigits: 3,
            })}{' '}
          </span>
        </div>
        <div>
          <span className="font-medium">
            {t('designAxialResistance.label')}:
          </span>{' '}
          <span>
            {designAxialResistance?.toLocaleString(locale, {
              maximumFractionDigits: 3,
            })}{' '}
          </span>
        </div>
        <span className="font-medium">{t('check.label')}:</span>{' '}
        <Badge
          className={cn(
            'text-base px-3 py-1',
            check ? 'bg-green-600' : 'bg-red-600',
          )}
        >
          {check ? t('check.satisfied') : t('check.notSatisfied')}
        </Badge>
      </CardContent>
    </Card>
  )
}
