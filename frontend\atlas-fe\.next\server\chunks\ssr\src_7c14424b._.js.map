{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/sonner.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sonner.tsx <module evaluation>\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,UAAU,IAAA,wQAAuB,EAC1C;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,8DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/components/ui/sonner.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/sonner.tsx\",\n    \"Toaster\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,UAAU,IAAA,wQAAuB,EAC1C;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,0CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/i18n/request.ts"], "sourcesContent": ["import { getRequestConfig } from 'next-intl/server'\r\nimport { routing } from './routing'\r\n\r\nexport default getRequestConfig(async ({ requestLocale }) => {\r\n  // This typically corresponds to the `[locale]` segment\r\n  let locale = await requestLocale\r\n\r\n  // Ensure that a valid locale is used\r\n  if (!(locale && routing.locales.includes(locale as any))) {\r\n    locale = routing.defaultLocale\r\n  }\r\n\r\n  return {\r\n    locale,\r\n    messages: (await import(`../../messages/${locale}.json`)).default,\r\n  }\r\n})\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;uCAEe,IAAA,8QAAgB,EAAC,OAAO,EAAE,aAAa,EAAE;IACtD,uDAAuD;IACvD,IAAI,SAAS,MAAM;IAEnB,qCAAqC;IACrC,IAAI,CAAC,CAAC,UAAU,iIAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAc,GAAG;QACxD,SAAS,iIAAO,CAAC,aAAa;IAChC;IAEA,OAAO;QACL;QACA,UAAU,CAAC;;;;;;;;;kBAAa,CAAC,eAAe,EAAE,OAAO,KAAK,CAAC,CAAC,EAAE,OAAO;IACnE;AACF", "debugId": null}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/i18n/routing.ts"], "sourcesContent": ["import { createNavigation } from 'next-intl/navigation'\r\nimport { defineRouting } from 'next-intl/routing'\r\n\r\nexport const locales = ['en', 'it'] as const\r\n\r\nexport type Locales = (typeof locales)[number]\r\n\r\nexport const routing = defineRouting({\r\n  locales,\r\n\r\n  defaultLocale: 'it',\r\n})\r\n\r\nexport const { Link, redirect, usePathname, useRouter, getPathname } =\r\n  createNavigation(routing)\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,MAAM,UAAU;IAAC;IAAM;CAAK;AAI5B,MAAM,UAAU,IAAA,mPAAa,EAAC;IACnC;IAEA,eAAe;AACjB;AAEO,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,GAClE,IAAA,kRAAgB,EAAC", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/providers/locale-layout-provider.tsx"], "sourcesContent": ["import { routing } from '@atlas/i18n/routing'\r\nimport { notFound } from 'next/navigation'\r\nimport { NextIntlClientProvider } from 'next-intl'\r\nimport { getMessages } from 'next-intl/server'\r\nimport type { PropsWithChildren } from 'react'\r\n\r\ntype Props = PropsWithChildren<{\r\n  params: Promise<{ locale: string }>\r\n}>\r\n\r\nexport const LocaleLayoutProvider = async ({ children, params }: Props) => {\r\n  const { locale } = await params\r\n  if (!routing.locales.includes(locale as any)) {\r\n    notFound()\r\n  }\r\n\r\n  const messages = await getMessages()\r\n\r\n  return (\r\n    <NextIntlClientProvider messages={messages}>\r\n      {children}\r\n    </NextIntlClientProvider>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AACA;AACA;;;;;;AAOO,MAAM,uBAAuB,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAS;IACpE,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IACzB,IAAI,CAAC,iIAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,SAAgB;QAC5C,IAAA,iMAAQ;IACV;IAEA,MAAM,WAAW,MAAM,IAAA,+PAAW;IAElC,qBACE,8OAAC,4RAAsB;QAAC,UAAU;kBAC/B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/providers/query-provider.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/providers/query-provider.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/query-provider.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/providers/query-provider.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/providers/query-provider.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/query-provider.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/providers/app-theme-provider.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AppThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppThemeProvider() from the server but AppThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/app-theme-provider.tsx <module evaluation>\",\n    \"AppThemeProvider\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,mBAAmB,IAAA,wQAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,sEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 190, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/providers/app-theme-provider.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AppThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call AppThemeProvider() from the server but AppThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/app-theme-provider.tsx\",\n    \"AppThemeProvider\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;AACO,MAAM,mBAAmB,IAAA,wQAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,kDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 212, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/providers/root-layout-provider.tsx"], "sourcesContent": ["import { LocaleLayoutProvider } from '@atlas/providers/locale-layout-provider'\r\nimport QueryProvider from '@atlas/providers/query-provider'\r\nimport type { PropsWithChildren } from 'react'\r\nimport { AppThemeProvider } from './app-theme-provider'\r\n\r\ntype Props = PropsWithChildren<{\r\n  params: Promise<{ locale: string }>\r\n}>\r\n\r\nexport const RootLayoutProvider = ({ children, ...props }: Props) => {\r\n  return (\r\n    <AppThemeProvider>\r\n      <LocaleLayoutProvider {...props}>\r\n        <QueryProvider>{children}</QueryProvider>\r\n      </LocaleLayoutProvider>\r\n    </AppThemeProvider>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAEA;;;;;AAMO,MAAM,qBAAqB,CAAC,EAAE,QAAQ,EAAE,GAAG,OAAc;IAC9D,qBACE,8OAAC,iKAAgB;kBACf,cAAA,8OAAC,yKAAoB;YAAE,GAAG,KAAK;sBAC7B,cAAA,8OAAC,iJAAa;0BAAE;;;;;;;;;;;;;;;;AAIxB", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file:///E:/work_25/atlas/frontend/atlas-fe/src/app/%5Blocale%5D/layout.tsx"], "sourcesContent": ["import './globals.css'\r\nimport { Toaster } from '@atlas/components/ui/sonner'\r\nimport { RootLayoutProvider } from '@atlas/providers/root-layout-provider'\r\n\r\ntype RootLayoutProps = {\r\n  children: React.ReactNode\r\n  params: Promise<{ locale: string }>\r\n}\r\n\r\nconst RootLayout = async ({ children, params }: RootLayoutProps) => {\r\n  const { locale } = await params\r\n\r\n  return (\r\n    <html lang={locale} suppressHydrationWarning>\r\n      <body>\r\n        <RootLayoutProvider params={params}>{children}</RootLayoutProvider>\r\n        <Toaster />\r\n      </body>\r\n    </html>\r\n  )\r\n}\r\n\r\nexport default RootLayout\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;;AAOA,MAAM,aAAa,OAAO,EAAE,QAAQ,EAAE,MAAM,EAAmB;IAC7D,MAAM,EAAE,MAAM,EAAE,GAAG,MAAM;IAEzB,qBACE,8OAAC;QAAK,MAAM;QAAQ,wBAAwB;kBAC1C,cAAA,8OAAC;;8BACC,8OAAC,qKAAkB;oBAAC,QAAQ;8BAAS;;;;;;8BACrC,8OAAC,6IAAO;;;;;;;;;;;;;;;;AAIhB;uCAEe", "debugId": null}}]}